<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ContentFilters</key>
	<dict/>
	<key>auto_connect</key>
	<true/>
	<key>data</key>
	<dict>
		<key>connection</key>
		<dict>
			<key>database</key>
			<string>wordpress</string>
			<key>host</key>
			<string>127.0.0.1</string>
			<key>kcid</key>
			<string>-4248604222124855395</string>
			<key>name</key>
			<string>127.0.0.1</string>
			<key>rdbms_type</key>
			<string>mysql</string>
			<key>sslCACertFileLocation</key>
			<string></string>
			<key>sslCACertFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslCertificateFileLocation</key>
			<string></string>
			<key>sslCertificateFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslKeyFileLocation</key>
			<string></string>
			<key>sslKeyFileLocationEnabled</key>
			<integer>0</integer>
			<key>type</key>
			<string>SPTCPIPConnection</string>
			<key>useSSL</key>
			<integer>0</integer>
			<key>user</key>
			<string>root</string>
		</dict>
		<key>session</key>
		<dict>
			<key>connectionEncoding</key>
			<string>utf8</string>
			<key>contentPageNumber</key>
			<integer>1</integer>
			<key>contentSelection</key>
			<data>
			YnBsaXN0MDDUAQIDBAUGJCVYJHZlcnNpb25YJG9iamVjdHNZJGFy
			Y2hpdmVyVCR0b3ASAAGGoKgHCBMUFRYaIVUkbnVsbNMJCgsMDxJX
			TlMua2V5c1pOUy5vYmplY3RzViRjbGFzc6INDoACgAOiEBGABIAF
			gAdUdHlwZVRyb3dzXxAdU2VsZWN0aW9uRGV0YWlsVHlwZU5TSW5k
			ZXhTZXTSFwsYGVxOU1JhbmdlQ291bnQQAIAG0hscHR5aJGNsYXNz
			bmFtZVgkY2xhc3Nlc1pOU0luZGV4U2V0oh8gWk5TSW5kZXhTZXRY
			TlNPYmplY3TSGxwiI1xOU0RpY3Rpb25hcnmiIiBfEA9OU0tleWVk
			QXJjaGl2ZXLRJidUZGF0YYABAAgAEQAaACMALQAyADcAQABGAE0A
			VQBgAGcAagBsAG4AcQBzAHUAdwB8AIEAoQCmALMAtQC3ALwAxwDQ
			ANsA3gDpAPIA9wEEAQcBGQEcASEAAAAAAAACAQAAAAAAAAAoAAAA
			AAAAAAAAAAAAAAABIw==
			</data>
			<key>contentSortColIsAsc</key>
			<true/>
			<key>contentViewport</key>
			<string>{{0, 0}, {693, 456}}</string>
			<key>isToolbarVisible</key>
			<true/>
			<key>queries</key>
			<string>return;

create database wordpress;
drop database wordpress;
grant all privileges on wordpress.* to 'wordpress'@'localhost' identified by'wordpress' with grant option;

select * from users;
select * from options;
select * from options where option_name like '%cit%';
select * from options where option_name like '%profoundmls%';
select * from wp_options where option_name like '%pfmls%';
select * from options where option_value like '%cit%';

select * from options where option_value = 'neighborhood';
select * from wp_posts limit 5;
update posts set post_name = 'neighborhood' where ID = 5;

select * from wp_pfmls_saved_listings;
show tables like '%pfmls%';
show tables like 'wp_%';
drop table wp_pfmls_saved_listings;
drop table wp_pfmls_saved_searches;
truncate table wp_pfmls_saved_listings;
truncate table wp_pfmls_saved_searches;

alter table wp_pfmls_mls add unique key saved_listing_per_user (user_id, mls_id); 

CREATE TABLE wp_pfmls_mls 
(
id SERIAL,
 user_id BIGINT(20) UNSIGNED NOT NULL,
mls_id VARCHAR(11) NOT NULL,
created TIMESTAMP NOT NULL,
FOREIGN KEY (user_id) REFERENCES wp_users (ID)
) ENGINE=InnoDB CHARSET UTF8;

select * from wp_options where option_name like '%pfmls%';
delete from wp_options where option_name = 'pfmls_user_login_db_version';

select exists (select 'x' from wp_pfmls_mls) as it_EXISTS;

select * from wp_users;

select * from wp_options order by option_name;
select * from wp_options where option_name = 'widget_pfmls_smallmap-';
</string>
			<key>view</key>
			<string>SP_VIEW_CUSTOMQUERY</string>
			<key>windowVerticalDividerPosition</key>
			<real>202</real>
		</dict>
	</dict>
	<key>encrypted</key>
	<false/>
	<key>format</key>
	<string>connection</string>
	<key>queryFavorites</key>
	<array/>
	<key>queryHistory</key>
	<array>
		<string>select * from wp_options where option_name = 'widget_pfmls_smallmap'</string>
		<string>select * from wp_options order by option_name</string>
		<string>select LIST_105, LIST_22 from property order by LIST_22 desc limit 10</string>
		<string>select * from property order by LIST_22 desc limit 10</string>
		<string>select * from field_mapping order by MapName</string>
		<string>select * from field_mapping order by armls</string>
		<string>select * from field_mapping</string>
		<string>select * from property_a_fields</string>
		<string>select * from property_a_fields limit 1</string>
		<string>ALTER TABLE `access_log` ADD COLUMN `NumResults` INTEGER UNSIGNED NOT NULL</string>
		<string>CREATE TABLE `access_log` (
  `LogID` int(11) NOT NULL AUTO_INCREMENT,
  `access_id` int(11) NOT NULL,
  `ClientIP` varchar(15) NOT NULL,
  `BrowserIP` varchar(15) NOT NULL,
  `QueryType` enum('FeatList','Search','Details','Options','RSS') NOT NULL,
  `PropID` int(11) NOT NULL,
  `UserAgent` varchar(255) NOT NULL,
  `Referer` varchar(255) NOT NULL,
  `QueryTime` double NOT NULL,
  `NumQueries` int(11) NOT NULL,
  `TotalTime` double NOT NULL,
  `Created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Features` varchar(255) NOT NULL,
  `Words` varchar(255) NOT NULL,
  `Hostname` varchar(255) NOT NULL,
  `Category` varchar(255) NOT NULL,
  `City` varchar(255) NOT NULL,
  `RequestURL` varchar(255) NOT NULL,
  `Page` smallint(5) unsigned NOT NULL,
  `SortOrder` varchar(255) NOT NULL,
  `Price` varchar(20) NOT NULL,
  `Sqft` varchar(20) NOT NULL,
  `Bedroom` varchar(20) NOT NULL,
  `Bathroom` varchar(20) NOT NULL,
  `ZipCode` varchar(20) NOT NULL,
  `PropType` varchar(20) NOT NULL,
  `NumResults` int(10) unsigned NOT NULL,
  PRIMARY KEY (`LogID`),
  KEY `IX_Customer` (`access_id`),
  KEY `IX_ClientIP` (`ClientIP`),
  KEY `IX_BrowserIP` (`BrowserIP`),
  KEY `IX_Type` (`QueryType`),
  KEY `IX_Created` (`Created`)
) ENGINE=MyISAM AUTO_INCREMENT=448</string>
		<string>CREATE TABLE `access_log` (
  `LogID` int(11) NOT NULL AUTO_INCREMENT,
  `access_id` int(11) NOT NULL,
  `ClientIP` varchar(15) NOT NULL,
  `BrowserIP` varchar(15) NOT NULL,
  `QueryType` enum('FeatList','Search','Details','Options','RSS') NOT NULL,
  `PropID` int(11) NOT NULL,
  `UserAgent` varchar(255) NOT NULL,
  `Referer` varchar(255) NOT NULL,
  `QueryTime` double NOT NULL,
  `NumQueries` int(11) NOT NULL,
  `TotalTime` double NOT NULL,
  `Created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Features` varchar(255) NOT NULL,
  `Words` varchar(255) NOT NULL,
  `Hostname` varchar(255) NOT NULL,
  `Category` varchar(255) NOT NULL,
  `City` varchar(255) NOT NULL,
  `RequestURL` varchar(255) NOT NULL,
  `Page` smallint(5) unsigned NOT NULL,
  `SortOrder` varchar(255) NOT NULL,
  `Price` varchar(20) NOT NULL,
  `Sqft` varchar(20) NOT NULL,
  `Bedroom` varchar(20) NOT NULL,
  `Bathroom` varchar(20) NOT NULL,
  `ZipCode` varchar(20) NOT NULL,
  `PropType` varchar(20) NOT NULL,
  `NumResults` int(10) unsigned NOT NULL,
  PRIMARY KEY (`LogID`),
  KEY `IX_Customer` (`access_id`),
  KEY `IX_ClientIP` (`ClientIP`),
  KEY `IX_BrowserIP` (`BrowserIP`),
  KEY `IX_Type` (`QueryType`),
  KEY `IX_Created` (`Created`)
) ENGINE=MyISAM AUTO_INCREMENT=448 DEFAULT CHARSET=latin1$$</string>
		<string>alter table access change column hide_awc hide_ucb tinyint(1) not null default 0</string>
		<string>describe access</string>
		<string>alter table access change column hide_aws hide_ucb tinyint(1) not null default 0</string>
		<string>alter table access change column hide_aws hide_ucb</string>
		<string>alter table access rename column hide_aws hide_ucb</string>
		<string>describe property</string>
		<string>describe profound</string>
		<string>select * from wp_pfmls_saved_listings</string>
	</array>
	<key>rdbms_type</key>
	<string>mysql</string>
	<key>rdbms_version</key>
	<string>5.5.29</string>
	<key>version</key>
	<integer>1</integer>
</dict>
</plist>
