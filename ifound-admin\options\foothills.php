<?
$genesis_settings = array(
	'genesis-vestige' 			    => array(
		'nav_superfish' 			  => 1,
		'nav_home' 					    => 1,
		'subnav_superfish'		  => 1,
		'subnav_home'				    => 1
	),
    'genesis-settings'          => get_option( 'genesis-settings' ),
	'theme_mods_' . $this->website['stylesheet'] 	=> array( 
		'nav_menu_locations' 		=> array(
		    'primary'					  => $this->menu_ids['main']
		),
        'custom_css_post_id'    => -1
	)
);


$sidebars_widgets = array(
    'sidebars_widgets' => array(
        'wp_inactive_widgets' => array(),
        'sidebar' => array(
            0 => 'ifound_quick_search-3',
            1 => 'recent-posts-2',
            2 => 'categories-2',
        ),
        'before-header-1' => array(
            0 => 'custom_html-5',
        ),
        'before-header-2' => array(
            0 => 'ifound_social_media-2',
        ),
        'before-header-3' => array(
            0 => 'custom_html-6',
        ),
        'header-mid' => array(
            0 => 'custom_html-2',
        ),
        'header_right' => array(
            0 => 'ifound_broker_logo-2',
        ),
        'front-page-1' => array(
            0 => 'ifound_slider_widget-2',
        ),
        'front-page-2' => array(
            0 => 'ifound_quick_search-2',
        ),
        'front-page-5' => array(
            0 => 'text-6',
            1 => 'custom_html-3',
            2 => 'text-7',
        ),
        'front-page-6' => array(
            0 => 'featured-post-2',
        ),
        'front-page-7' => array(),
        'lead-capture' => array(),
        'footer-1' => array(
            0 => 'custom_html-4',
            1 => 'ifound_social_media-3',
        ),
        'after-entry' => array(),
        'details-before-slider' => array(
            0 => 'ifound_whats_my_payment-2',
        ),
        'details-after-slider' => array(
            0 => 'ifound_save_this_property-2',
        ),
        'search-results-before-criteria' => array(),
        'search-results-after-criteria' => array(),
        'search-results-after-map' => array(
            0 => 'ifound_save_this_search-2',
        ),
        'search-results-after-results' => array(),
        'array_version' => 3,
    )
);


$widgets = array(
    'widget_archives' => array(
        '_multiwidget' => 1,
    ),
    'widget_autotext' => array(
        '_multiwidget' => 1,
    ),
    'widget_calendar' => array(
        '_multiwidget' => 1,
    ),
    'widget_categories' => array(
        2 => array(
          	'title' => '',
          	'count' => 0,
          	'hierarchical' => 0,
          	'dropdown' => 0,
        ),
        '_multiwidget' => 1,
    ),
    'widget_custom_html' => array(
      	3 => array(
         	 'title' => 'Do You Know What Your Home is Worth?',
          	'content' => '<p>Home Valuation</p><p><a href="' . $this->home_worth_slug() . '" class="button taglinebutton">Find Out</a></p>',
      	),
      	4 => array(
          	'title' => 'Contact',
          	'content' => $this->contact_info(),
      	),
      	5 => array(
          	'title' => '',
          	'content' => $this->header_phone(),
      	),
      	6 => array(
          	'title' => '',
          	'content' => $this->my_account_link(),
      	),
      	'_multiwidget' => 1,
    ),
    'widget_featured-page' => array(
        '_multiwidget' => 1,
    ),
    'widget_featured-post' => array(
        2 => array(
            'title' => 'Real Estate News',
            'posts_cat' => '0',
            'posts_num' => 3,
            'posts_offset' => '0',
            'orderby' => 'date',
            'order' => 'DESC',
            'gravatar_size' => '45',
            'gravatar_alignment' => 'alignnone',
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'post_info' => '[post_date] By [post_author_posts_link] [post_comments]',
            'show_content' => 'content-limit',
            'content_limit' => '170',
            'more_text' => 'Read More',
            'extra_title' => '',
            'extra_num' => '',
            'more_from_category_text' => 'More Posts from this Category',
        ),
        '_multiwidget' => 1,
    ),
    'widget_gform_widget' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_broker_logo' => array(
          2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_cmc_form' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_featured_listings' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_quick_search' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_save_this_property' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_save_this_search' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_search_nearby' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_social_media' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_whats_my_payment' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_media_audio' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_gallery' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_image' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_video' => array(
        '_multiwidget' => 1,
    ),
    'widget_meta' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_slider_widget' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_nav_menu' => array(
        '_multiwidget' => 1,
    ),
    'widget_pages' => array(
        '_multiwidget' => 1,
    ),
    'widget_recent-comments' => array(
        '_multiwidget' => 1,
    ),
    'widget_recent-posts' => array(
        2 => array(
            'title' => '',
            'number' => 5,
        ) ,
        3 => array(
            'title' => 'Real Estate News',
            'number' => 5,
            'show_date' => false,
        ),
        '_multiwidget' => 1,
    ),
    'widget_rss' => array(
        '_multiwidget' => 1,
    ),
    'widget_search' => array(
        '_multiwidget' => 1,
    ),
    'widget_strong-testimonials-view-widget' => array(
        '_multiwidget' => 1,
    ),
    'widget_tag_cloud' => array(
        '_multiwidget' => 1,
    ),
    'widget_text' => array(
        1 => array(),
        6 => array(
            'title' => '',
            'text' => '',
            'filter' => true,
            'visual' => true,
        ),
        7 => array(
            'title' => 'Get in Touch',
            'text' => $this->call_or_text(),
            'filter' => true,
            'visual' => true,
        ),
        '_multiwidget' => 1,
    ),
    'widget_user-profile' => array(
        '_multiwidget' => 1,
    ),
);


$theme_settings = array_merge( $genesis_settings, $sidebars_widgets, $widgets );