<?php

namespace Profound\MLS;

abstract class ImageHandler {
	private $db;
	private $propAPI;
	private $mls;

	function __construct($options = array()) {
		$this->options = $options;
	}

	abstract function getImagePathsList($listing_id);

	// We could create methods here called getDb(), which could then forward
	// forward to the same method on our MLS object. This is better than
	// exposing the MLS object and having the user call e.g. 
	// $my_image_handler_object->getMls()->getDb(). That's brittle.
	// For our convenience, we'll basically have a liason by default
	// by overriding __call, kind of like the pattern in Ruby with 
	// method_missing.
	// The reason this isn't flawless is that our interface changes if the MLS
	// interface changes. For example, if the MLS object offers a method such
	// as getDb(), we automatically offer getDb(). If they change, our exposed
	// method changes as well.
	function __call($name, array $args)
    {
        // User called a non-existing method. Forward to the MLS.
        return $this->getMls()->$name($args);
    }


	protected function getThumbnailImagePath($listingId, $imageNumber) {
		return $this->getImagePath($listingId, $imageNumber, "-t");
	}
    
	protected function getHighResImagePath($listingId, $imageNumber) {
		return $this->getImagePath($listingId, $imageNumber, "-o");
	}

    function getImagePathFor($photo, $resolution) {
        switch ($resolution) {
        case "normal":
            return $this->getImagePath($photo["Content-ID"], $photo["Object-ID"]);
        case "thumbnail":
            return $this->getThumbnailImagePath($photo["Content-ID"], $photo["Object-ID"]);
        case "highres":
            return $this->getHighResImagePath($photo["Content-ID"], $photo["Object-ID"]);
        }
    }


    protected function getMls() {
    	if (!$this->mls) {
			$this->mls = $this->options['mls'];
			if (!$this->mls) {
				throw new \Exception("No MLS was specified for loading");
			}
		}
		return $this->mls;
    }
}
