import { __ } from '@wordpress/i18n';
import { Button } from '@wordpress/components';
import './editor.scss';

export default function Edit({ attributes, className, setAttributes }) {
	function launchMapMaker() {
		jQuery(window).trigger('ifound:show-map-maker', [attributes, function(newAttributes) {
			setAttributes(newAttributes);
		}]);
	}

	return (
		<div className={ className }>
			<div style={ {'text-align': 'center'} }>
				<img src="/wp-content/plugins/ifound/idx/admin/images/polygon-drawing-icon.png" style={{ width: '24px' }} />
				{' '}
				<strong>
					iFound Map Maker
				</strong>
			</div>
			<Button isPrimary onClick={launchMapMaker}>Launch Map Maker</Button>
		</div>
	);
}
