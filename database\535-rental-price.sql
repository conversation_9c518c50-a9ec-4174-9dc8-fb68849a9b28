alter table armls_property_B add column EffectiveListPrice decimal(14,0) NOT NULL default 0;

update armls_property_B set EffectiveListPrice = case when LIST_22 = 0 then LIST_24 else LIST_22 end;

drop trigger if exists effective_list_price_calculation;

delimiter //
CREATE TRIGGER effective_list_price_calculation
BEFORE INSERT ON armls_property_B
FOR EACH ROW
BEGIN
  SET NEW.EffectiveListPrice = case when NEW.LIST_22 = 0 then NEW.LIST_24 else NEW.LIST_22 end;
END;//
delimiter ;
