<?php

class Auth {
    public $db;

    public function __construct() {
        //Config file that Zend_Db_Adapter will use
        $config = parse_ini_file('../config.ini', true);

        $this->db = new Zend_Db_Adapter_Pdo_Mysql($config['database']);
    }

    public function authenticate($query_info) {

        // Check to see if they are authenticated in session or query contains user and pass
        $sql = "SELECT
                    *
                FROM
                    access
                    LEFT JOIN access_meta USING (access_id)

                WHERE
                    access_apikey = ? AND access_status = '1'
        ";

        if (isset($query_info['apikey'])) {
            $result = $this->db->fetchRow($sql, $query_info['apikey']);
        }

        if (isset($result['access_id'])) {
			// Replace the template fields of the user account with the MLS system. We used to let the account's values
			// be customized, but we no longer do, so might as well just overwrite them here.
			$mlsSystemSql = "SELECT * FROM mls_systems WHERE name = ?";
			$mlsSystemResult = $this->db->fetchRow($mlsSystemSql, [$result['mls']]);
			if ($mlsSystemResult) {
				foreach ($mlsSystemResult as $fieldName => $value) {
					if (strpos($fieldName, 'meta_') === 0) {
						$result[$fieldName] = $value;
					}
				}
			}

            // Case 562. Not sure why, but feature_gui, which is a tinyint(1),
            // comes to us as a string. We cast to a boolean here.
            $result['feature_gui'] = filter_var($result['feature_gui'], FILTER_VALIDATE_BOOLEAN);

            // Ideally we'd be checking the 'primary' flag on the client_crms record, but we haven't been setting it so far.
            // For now, we'll just assume a client has at most one CRM.
            $crmSql = "SELECT count(*) from client_crms where access_id = ? and display_name = 'LionDesk' and enabled = 1 order by id asc limit 1";
            $crmResult = $this->db->fetchRow($crmSql, [$result['access_id']]);
            $result['crm_liondesk'] = $crmResult['count(*)'] ? true : false;

	        /*
            if ($result['access_ip'] && !$this->testIP($result['access_ip'])) {
                die('Please authenticate from a known IP');
            }
            if ($result['access_domain'] && !$this->testDomain($result['access_domain'])) {
                die('Please authenticate from a known domain');
            }
	        */
            return $result;
        } else {
            echo json_encode(array('authorized' => 0));
	        exit;
        }
    }

    /**
     * @param $ip
     * @return bool
     */
    private function testIP($ip) {
        //$ipregex = preg_replace("/./", "\.", $ip);
        //$ipregex = preg_replace("/*/", ".*", $ipregex);
        $ipregex = preg_replace("/\./", "\.", $ip);
        $ipregex = preg_replace("/\*/", ".*", $ipregex);

        if(preg_match('/^'.$ipregex.'/', $_SERVER['REMOTE_ADDR'])){
            return true;
        }
        return false;
    }




    /**
     * @param $domain
     * @return bool
     */
    private function testDomain($domain){

        if( $domain == $_SERVER['HTTP_HOST']){
            return true;
        }
        return false;
    }





}
