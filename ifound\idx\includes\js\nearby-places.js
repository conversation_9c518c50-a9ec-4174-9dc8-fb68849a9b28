
/**
 * @link https://developers.google.com/maps/documentation/javascript/places
 * @link https://developers.google.com/places/web-service/supported_types
 * @link https://developers.google.com/maps/documentation/javascript/examples/place-details
 * @link https://stackoverflow.com/questions/15096461/resize-google-maps-marker-icon-image/
 */
jQuery( document ).ready( function( $ ) {

    var map;
    var infowindow;
	  var place = ['school'];

	  function initMap() {
	      var lat = parseFloat( nearby.lat );
		    var lng = parseFloat( nearby.lng );
     	  var center = {lat: lat, lng: lng};

        map = new google.maps.Map(document.getElementById('nearby-places-map'), {
            center: center,
            zoom: 13,
            ...window.iFoundGlobal.sharedGoogleMapBaseOptions,
        });

        infowindow = new google.maps.InfoWindow();
        var service = new google.maps.places.PlacesService(map);
        service.nearbySearch({
         	  location: center,
			      radius: 5000,
          	rankBy: google.maps.places.RankBy.PROMINENCE,
          	type: place
        }, callback);

        var subject = new google.maps.Marker({
            position: center,
            map: map,
            icon: nearby_places.subject_icon
        })

 	  }

 	  function callback(results, status) {
        if (status === google.maps.places.PlacesServiceStatus.OK) {
         	  for (var i = 0; i < results.length; i++) {
           		 createMarker(results[i]);
            }
        }
 	  }

    function createMarker(place) {
        var infowindow = new google.maps.InfoWindow();
        var service = new google.maps.places.PlacesService(map);
		    /** @link https://stackoverflow.com/questions/15096461/resize-google-maps-marker-icon-image */
		    var Gicons = {
    		    url: place.icon,
    		    scaledSize: new google.maps.Size(30, 30), // scaled size
    		    origin: new google.maps.Point(0,0), // origin
    		    anchor: new google.maps.Point(0, 0) // anchor
		    };
        service.getDetails({
            placeId: place.place_id
            }, function(place, status) {
                if (status === google.maps.places.PlacesServiceStatus.OK) {
                    var marker = new google.maps.Marker({
                    map: map,
                    position: place.geometry.location,
  				          icon: Gicons
                });
                google.maps.event.addListener(marker, 'click', function() {
                    infowindow.setContent('<div><strong>' + place.name + '</strong><br>' +
                    place.formatted_address + '</div>');
                    infowindow.open(map, this);
                });
            }
        });
	  }

    $( '.nearby-places-radio' ).checkboxradio({
        icon: false
    });

	  $('.nearby-places-radio').change(function() {
		    places = new Array();
 		    $( '.nearby-places-radio' ).each(function() {
  			    if( $(this).is(':checked') ) {
				        place = $(this).val();
			      }
		    });
		    initMap();
  	});

	window.iFoundGlobal.loadGoogleMaps(['places']).then(function() {
		initMap();
	});
});
