<?php

namespace Profound\MLS;

class GlvarnvPropertyLimiter extends PropertyLimiter {
	public function limitProperties(&$sql, $options = array()) {
		parent::limitProperties($sql, $options);

		// For showing only show listings that have images, add:
        // AND field_2238 <> ''
        // bellow
		// GLVAR doesn't provide geocoding, so we have RetsCloud do it.
		// So don't show listings without geocode information.
		$sql .= " AND GEO_Lat <> '' AND GEO_Long <> ''";

	}

	public function shouldGeocode() {
		return true;
	}
}
