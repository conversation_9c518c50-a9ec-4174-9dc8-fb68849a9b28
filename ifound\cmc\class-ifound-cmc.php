<?
/**
 * Current Market Comparison
 *
 * @since 1.0.0
 * @since 1.1.8 Remove iFOUND_cmc::cmc_request(), iFOUND_cmc::price_stats(), CMC version and clean up comments related to versions.
 */
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

/**
 * iFound CMC Class
 *
 * @since 1.0.0
 */

class iFoundCmc extends iFoundIdx {
	use UtilTrait;

	private $address;
	private $lat;
	private $lng;
	private $ptype;
	private $beds;
	private $baths;
	private $sqft;
	private $built;
	private $stories;
	/** @since 2.0.1 $map_stuff Replaced $map_data when merged with iFound Plugin. */
	private $map_stuff;
	private	$closed;
	private $active;
	protected $max_radius;
	protected $google_key;

	/**
	 * init CMC class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 * @since 2.1.3 Add gform_after_submission_ action hook to log CMC address capture.
	 */

	public function __construct() {

		$this->max_radius = $this->max_radius();

		/*
		 * Check for a custom google key
		 */

		if( $this->google_key == NULL ) $this->google_key = $this->get_config()['ifa_google_maps_api_key'];
		/**
		 * CMC Process.
		 *
		 * @since 1.0.0
		 * @since 1.2.14 Process as cmc_process
		 */

		add_action( 'prep_cmc_process_from_GET', array( $this, 'prep_cmc_process_from_GET' ) );
		add_action( 'prep_cmc_process_from_listing', array( $this, 'prep_cmc_process_from_listing' ), 10, 1 );

		 /**
		 * CMC Form.
		 *
		 * @since 1.0.0
		 */

		add_shortcode( 'cmc', array( $this, 'cmc_form' ) );

		 /**
		 * CMC Report.
		 *
		 * @since 1.0.0
		 * @since 1.2.14 Process as action.
		 */

		add_action( 'cmc_report', array( $this, 'cmc_report' ) );

		 /**
		 * CMC Scripts.
		 *
		 * @since 1.0.0
		 */

		add_action( 'wp_enqueue_scripts', array( $this, 'cmc_scripts' ) );

		 /**
		 * CMC Pop.
		 *
		 * @since 1.0.0
		 */

		add_action( 'wp_footer', array( $this, 'cmc_pop' ) );

		 /**
		 * CMC Widget.
		 *
		 * @since 1.0.0
		 */

		add_action( 'widgets_init', array( $this, 'init_widgets' ) );

		/**
		 * Gform Confirmaation
		 *
		 * Remove the G Form confirmation for the incomplete submission form.
		 *
		 * @since 1.0.0
		 * @see https://www.gravityhelp.com/documentation/article/gform_confirmation_anchor/
		 */

		$address_form_id = get_option( 'cmc_ifound_address_capture_form_id' );
		add_filter( 'gform_confirmation_anchor_' . $address_form_id, '__return_false' );
		add_action( 'gform_after_submission_' . $address_form_id, array( $this, 'log_activity_cmc_address' ), 10, 2 );

		add_action( 'template_redirect', array( $this, 'cmc_template_redirect' ) );

		add_filter( 'body_class', array( $this, 'body_class' ) );

		$cmc_form_id = get_option('cmc_ifound_form_id');
		// This allows us to customize the field. We use multiple hooks, as recommended by this page (looks in the
		// section titled The Hooks):
		// https://docs.gravityforms.com/dynamically-populating-drop-down-or-radio-buttons-fields/
		add_filter('gform_pre_render_' . $cmc_form_id, [$this, 'gform_pop_render']);
		add_filter('gform_pre_validation_' . $cmc_form_id, [$this, 'gform_pop_render']);
		add_filter('gform_pre_submission_filter_' . $cmc_form_id, [$this, 'gform_pop_render']);
		add_filter('gform_admin_pre_render_' . $cmc_form_id, [$this, 'gform_pop_render']);

        // Gravity form's {embed_url} trick won't work for concatenating URL query string bits if the embed_url already
        // has a query string. We fix the situation here.
        add_filter('gform_pre_send_email', [$this, 'gform_pre_send_email'], 10, 4);
	}

    public function gform_pre_send_email($email, $message_format, $notification, $entry) {
	    $url = site_url( $_SERVER['REQUEST_URI'] );
        // The following regex is similar to $this->util()->get_link_regex(), except I took out the comma and \s in the
        // first [^] block, because we need to allow them because we are allowing an address input by the user.
        $link_regex = '#https?://[^()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|/))#';
	    $email['message'] = preg_replace_callback($link_regex, function($matches) {
		    $url = $matches[0];
            // Only do this for the CMC report, not all gravity form submissions.
		    $index_of_cmc_string = strpos($url, $this->cmc_report);
		    if ($index_of_cmc_string === false) {
			    return $url;
		    }
		    $url_parts = parse_url($url);
		    if ($url_parts === false) {
			    return $url;
		    }
		    if ($url_parts['host'] !== $this->util()->get_host()) {
			    return $url;
		    }
		    $embed_url = substr($url, 0, $index_of_cmc_string);
		    // Did the embed_url already have a query string? If not, there's nothing that we need to do.
		    $first_question_mark_pos = strpos($embed_url, '?');
		    if ($first_question_mark_pos !== false) {
			    $embed_url_parts = parse_url($embed_url);
			    parse_str($embed_url_parts['query'], $embed_url_query_array);
			    $second_question_mark_pos = strpos($url, '?', $first_question_mark_pos + 1);
			    $cmc_query_string = substr($url, $second_question_mark_pos + 1);
			    parse_str($cmc_query_string, $cmc_query_array);
			    $url = $embed_url_parts['scheme']
				    . '://' . $embed_url_parts['host']
				    . $embed_url_parts['path']
				    . $this->cmc_report
				    . '/'
			    ;
			    $url = add_query_arg($cmc_query_array, $url);
			    $url = add_query_arg($embed_url_query_array, $url);
		    }
		    return $url;
	    }, $email['message']);
	    return $email;
    }

	public function gform_pop_render($form) {
		$choices = $this->get_prop_type_mapped();
		if ($choices) {
			foreach ($form['fields'] as &$field) {
				if ($field->label === 'Property Type') {
					$filtered_choices = $this->get_agent_filtered_prop_type_mapped_labels($choices);
					$field->choices = $this->util()->array_map_vk(function($value, $key) {
						return ['text' => $value, 'value' => $key];
					}, $filtered_choices);
				}
			}
		}
		return $form;
	}

	/**
	 * CMC Scripts
	 *
	 * @since 1.0.0
	 */

	public function cmc_scripts(){

		if( ! get_option( 'cmc_activate' ) ) return;

		 /**
		 * Enqueue CMC Styles.
		 *
		 * @since 1.0.0
		 */

		wp_register_style( 'cmc_styles', plugins_url( 'css/cmc.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		/**
		 * Register CMC Form/Pop Script.
		 *
		 * @since 1.0.0
		 */

		wp_register_script( 'cmc_js', plugins_url( 'js/cmc.js', __FILE__ ), array( 'jquery', 'load_map_js' ), iFOUND_PLUGIN_VERSION );

		/**
		 * Localize CMC Script Data.
		 *
		 * @since 1.0.0
		 */

		wp_localize_script( 'cmc_js', 'cmc_data', array(
			'settings'	=> (object) get_option( 'cmc_admin_map_settings' ),
			'icon'		=> plugins_url( '/images/form-marker.png', __FILE__ ),
			'form_id'	=> get_option( 'cmc_ifound_form_id' ),
			'form2_id'	=> get_option( 'cmc_ifound_address_capture_form_id' ),
		));

		/**
		 * Enqueue CMC Map Script.
		 *
		 * @since 1.0.0
		 */

		wp_register_script(
			'cmc_map_js',
			plugins_url( 'js/cmc_map.js', __FILE__ ),
			array( 'jquery', 'jquery-ui-core', 'load_map_js' ),
			iFOUND_PLUGIN_VERSION
		);

		/**
		  * Localize CMC Script Map Data.
		  *
		    * @since 1.0.0
		    */

		$show_map_by_default = false;
		$map_settings = get_option('ifound_map_settings');
		if (is_array($map_settings)) {
			$show_map_by_default = !!($map_settings['show_map_by_default'] ?? false);
		}
		wp_localize_script( 'cmc_map_js', 'cmc_map', array(
			'image_url'           => plugins_url('/images/', __FILE__),
			'map_stuff'           => $this->map_stuff,
			'max_radius'          => $this->max_radius,
			'show_map_by_default' => $show_map_by_default,
		));


	}

	public function body_class($classes) {
		global $wp_query;
		if ( isset( $wp_query->query_vars[$this->cmc_report] ) ) {
			$classes[] = 'ifa-dynamic';
		}
		return $classes;
    }

	/**
	 * CMC Template Redirect
	 *
	 * @since 1.1.0
	 */

	public function cmc_template_redirect() {

		global $wp_query;

		if ( isset( $wp_query->query_vars[$this->cmc_report] ) ) {
			include( plugin_dir_path( __FILE__ ) . 'templates/cmc-report.php' );
			do_action( 'ifound_activity_log', iFoundContacts::get_contact_id(), 'View Current Market Comparison', $this->address );

			exit;

		}

	}

	/**
	 * Max Radius
	 *
	 * The max radius to serach from subject property.
	 *
	 * @since 5.0.0
	 *
	 * @return float $max_radius The max radius.
	 */

	public function max_radius() {
		$max_radius = get_option( 'cmc_map_radius', 2 );
		return floatval( $max_radius );
	}

	/**
	 * Init Widgets
	 *
	 * @since 1.0.0
	 *
	 * @see Class CMC_widget
	 */

	public function init_widgets(){

		if( ! get_option( 'cmc_activate' ) ) return;

		register_widget( 'iFoundCmcWidget' );

	}

	/**
	 * CMC Process
	 *
	 * Process GET and build API query string
	 *
	 * @since 1.0.0
	 * @since 1.1.8 Use iFOUND::process_input() to process IDX requests.
	 * @since 1.2.11 Add Google API support
	 * @since 1.2.11 Fully process the CMC data here.
	 * @since 2.1.3  Add ifound_activity_log action hook to log CMC view.
	 * @since 4.1.12 Update query params to work with the server ProperySearch.
	 */

	public function prep_cmc_process_from_GET() {

		if (!get_option('cmc_activate')) return;

		/** Clean up GET query string values */
		$this->address = sanitize_text_field($_GET['Address']);

		if (empty($this->address)) return;

		/** If Lat or Lng are not set end script */
		if (empty($_GET['Latitude']) || empty($_GET['Longitude'])) {

			$this->google_address();

		} else {

			$this->lat = floatval($_GET['Latitude']);
			$this->lng = floatval($_GET['Longitude']);

		}

		if (empty($this->lat) || empty($this->lng)) return;

		$this->ptype = sanitize_text_field($_GET['PropType']);
		$this->beds = intval($_GET['Beds']);
		$this->baths = intval($_GET['Bathrooms']);
		$this->sqft = sanitize_text_field($_GET['SquareFeet']);
		$this->built = intval($_GET['YearBuilt']);
		$this->stories = intval($_GET['IntStories']);

		$this->do_cmc_process();
	}

	public function prep_cmc_process_from_listing($listing) {
		if ($this->util()->is_user_agent_bot()) {
			return;
		}
		if (!get_option('cmc_activate')) return;

		$this->address = $this->util()->format_listing_address($listing);

		if (empty($this->address)) return;

		/** If Lat or Lng are not set end script */
		if (empty($listing['Latitude']) || empty($listing['Longitude'])) {

			$this->google_address();

		} else {

			$this->lat = floatval($listing['Latitude']);
			$this->lng = floatval($listing['Longitude']);

		}

		if (empty($this->lat) || empty($this->lng)) return;

		$this->ptype = $listing['PropType'];
		$this->beds = intval($listing['Beds']);
		$this->baths = intval($listing['Bathrooms']);
		$this->sqft = intval(str_replace(',', '', $listing['SquareFeet']));
		$this->built = intval($listing['YearBuilt']);
		$this->stories = intval($listing['IntStories']);

		$this->do_cmc_process();
	}

	private function do_cmc_process() {
		global $mls_associations;

		list( $sqft_low, $sqft_high ) = explode( '-', $this->sqft );
		if (is_numeric($this->ptype)) {
			$ptype_num = intval($this->ptype);
			$ptypes = $this->get_prop_type_mapped()[$ptype_num]['property_sub_types'];
			$criteria = $this->lookups_to_criteria()->prop_type->values;
			// Only use property types the agent has checked in their search settings.
			$prop_type = array_values(array_filter($ptypes, function($x) use ($criteria) {
				return in_array($x, $criteria);
			}));
		} else {
			$prop_type = empty( $this->ptype ) ? '' : $this->mls_associations_value( 'prop_type', $this->ptype );
			$prop_type = [$prop_type];
		}

		$input = array(
			'nearby'		=> array(
				'radius' 		=> $this->max_radius,
				'lat'			=> $this->lat,
				'lng'			=> $this->lng
			),
			'bedrooms' 		=> array(
				'min' 			=> ( $this->beds > 0 )  ? $this->beds  - 1 : 1,
				'max' 			=> ( $this->beds > 0 )  ? $this->beds  + 1 : 99
			),
			'bathrooms' 	=> array(
				'min' 			=> ( $this->baths > 0 ) ? $this->baths - 1 : 1,
				'max' 			=> ( $this->baths > 0 ) ? $this->baths + 1 : 99
			),
			'living_sqft' 	=> array(
				'min' 			=> is_numeric( $sqft_low )  ? round( intval( $sqft_low ) * .85 ) : 0,
				'max' 			=> is_numeric( $sqft_high ) ? round( intval( $sqft_high ) * 1.15 ) : 999999
			),
			'stats' 			=> [
				'show' => 'choose',
				'fields' => [
					'list_price' => [
						'min' => true,
						'max' => true,
						'median' => true,
					],
				],
			],
			'sort'				=> isset( $mls_associations->cmc_sort_values->active ) ? $mls_associations->cmc_sort_values->active : '',
			'int_stories' 		=> empty( $this->stories ) 	? false : $this->stories,
			'max_results'		=> 24
		);
		// Property Type is special basically just because of Bright which treats things differently in Solr. Rather
		// than re-index everything, it's simpler (and maybe makes more sense / is more intuitive anyway) to not
		// send the property type if we don't have it.
		if (count($prop_type) > 0 && $prop_type[0]) {
			$input['prop_type'] = $prop_type;
		}

		$this->params = array_filter( $input );

		$this->active = $this->request();

		$closed_input = array_merge(
			$input,
			array(
				'days_back' 	=> 90,
				'list_status' 	=> array( 'Closed', 'Sold' ),
				'sort'			=> isset( $mls_associations->cmc_sort_values->closed ) ? $mls_associations->cmc_sort_values->closed : '',
				'stats' 			=> [
					'show' => 'choose',
					'fields' => [
						'close_price' => [
							'min' => true,
							'max' => true,
							'median' => true,
						],
					],
				],
			)
		);

		$this->params = array_filter( $closed_input );

		$this->closed = $this->request();

		/** Subject Map Data */
		$subject_data = array(
			0 => array(
				$this->address,
				$this->lat,
				$this->lng,
				'subject',
				'subject-data'
			)
		);

		/** Active Map Data */
		$active_data = (array) $this->map_stuff( $this->active->listings, 'active' );

		/** Closed Map Data */
		$closed_data = (array) $this->map_stuff( $this->closed->listings, 'closed' );

		/** Create the Map Data Object */
		$this->map_stuff = array_merge_recursive( $subject_data, $active_data, $closed_data );
	}

	/**
	 * Google Address
	 *
	 * Use Google Address API to retrieve coordinates.
	 *
	 * @since 1.2.11
	 */

	public function google_address() {
		$geocoding = apply_filters('ifound_geocode', $this->address);

		$this->lat = $geocoding['lat'];
		$this->lng = $geocoding['lng'];

	}

	/**
	 * CMC Report
	 *
	 * Built the CMC report display.
	 *
	 * @since 1.0.0
	 *
	 * @return object $cmc_report The display for the CMC report.
	 */

	public function cmc_report() {
		if( ! get_option( 'cmc_activate' ) ) return;

		wp_enqueue_style( 'cmc_styles' );

		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'cmc_map_js' ); ?>

		<div class="cmc-report" id="cmc">

			<div class="wrap"><?

				$this->legend();
				$this->subject();
				$this->body( $this->closed, 'Closed' );
				$this->body( $this->active, 'Active' ); ?>

			</div>

		</div><?

	}

	/**
	 * CMC Form
	 *
	 * Display the CMC Form.
	 *
	 * @since 1.0.0
	 *
	 * @return object $cmc_form The form for the CMC input.
	 */

	public static function cmc_form() {

		if( ! get_option( 'cmc_activate' ) ) return;

		wp_enqueue_style( 'cmc_styles' );

		wp_enqueue_script( 'cmc_js' );

		ob_start(); ?>

		<div id="cmc">

			<div class="cmc" id="cmc-auto-input">

				<div class="wrap">

					<input id="cmc-autocomplete" placeholder="Type Your Address Here"/>


					<div class="button-wrap">
						<button id="cmc-button" class="button"><? _e( 'View', 'ifound' ); ?></button>
					</div>

					<? /** @see https://www.gravityhelp.com/documentation/article/embedding-a-form/ */ ?>
					<? gravity_form( get_option( 'cmc_ifound_address_capture_form_id' ), false, false, false, null, true, 99 ); ?>

					<div class="clear"></div>

				</div>

			</div>

		</div><?

		return ob_get_clean();

	}

	/**
	 * CMC Pop
	 *
	 * Display the CMC popup with Gravity Form.
	 *
	 * @since 1.0.0
	 */

	public function cmc_pop() {

		if( ! get_option( 'cmc_activate' ) ) return;

		/**
		 * If CMC Script is enqueued then print CMC Pop to the footer
		 *
		 * @see iFoundCmc::cmc_form()
		 */

		if ( wp_script_is( 'cmc_js', 'enqueued' ) ) { ?>

			<div id="cmc">

				<div id="pop-backdrop" class="pop-backdrop"></div>

				<div id="pop-close" class="pop-close" title="Close">
					<i class="fal fa-window-close fa-2x" aria-hidden="true"></i>
				</div>

				<div id="CMC_form" class="pop-form">

					<div class="wrap">

						<h3 id="form_address" class="center"></h3>

						<div class="one-third first">

							<div class="pop-info">
								<? echo stripslashes( get_option( 'cmc_pop_up_text' ) ); ?>
							</div>

							<div class="cmc-map-container">

								<div class="wrap">

									<div id="pop-map" class="pop-map"></div>

								</div>

							</div>

						</div>

						<div class="two-thirds"><?

							/**
							 * Print the CMC Gravity Form to the CMC pop. Allow GForm Ajax is true.
							 *
							 * @link https://www.gravityhelp.com/documentation/article/embedding-a-form/
							 */

							if( $form_id = get_option( 'cmc_ifound_form_id' ) ) {

								gravity_form( $form_id, false, false, false, null, true, 99 );

							} ?>

						</div>

						<div class="clear"></div>

					</div>

				</div>

			</div><?

		}

	}

	/**
	 * Ledgend
	 *
	 * Diplay the CMC ledgend.
	 *
	 * @since 1.0.0
	 */

	public function legend() { ?>

		<div id="cmc-map" class="cool-wrap" style="display: none;"></div>
		<div class="button toggle-show-map" style="display: none;">Show map</div>

		<div class="cmc-type legend center">

			<div class="wrap">

				<div class="three-fourths first">

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->subject_map_icon() ?>" alt="Subject" width="20" title="The subject property for this report." class="cmc_tip"/>

						<div class="legend-heading"><? _e( 'Subject', 'ifound' ); ?></div>

					</div>

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->active_map_icon() ?>" alt="Active" width="20" title="Properties that are current active listings." class="cmc_tip"/>

						<div class="legend-heading"><? _e( 'Active', 'ifound' ); ?></div>

					</div>

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->closed_map_icon() ?>" alt="closed" width="20" title="Properties that have sold in the last 90 days." class="cmc_tip"/>

						<div class="legend-heading"><? _e( 'Closed', 'ifound' ); ?></div>

					</div>

				</div>

				<div class="one-fourth">

					<div class="distance green-line">
						<? _e( 'Best', 'ifound' ); ?><span class="right"><? _e( round( $this->max_radius * .25, 2 ) . ' Miles', 'ifound' ); ?></span>
					</div>

					<div class="distance yellow-line">
						<? _e( 'Better', 'ifound' ); ?><span class="right"><? _e( round( $this->max_radius * .5, 2 ) . ' Miles', 'ifound' ); ?></span>
					</div>

					<div class="distance orange-line">
						<? _e( 'Good', 'ifound' ); ?><span class="right"><? _e( round( $this->max_radius * .75, 2 ) . ' Miles', 'ifound' ); ?></span>
					</div>

					<div class="distance red-line">
						<? _e( 'Fair', 'ifound' ); ?><span class="right"><? _e( round( $this->max_radius, 2 ) . ' Miles', 'ifound' ); ?></span>
					</div>

				</div>

			</div>

		</div><?

	}

	/**
	 * Subject
	 *
	 * Diplay the CMC subject property data.
	 *
	 * @since 1.0.0
	 */

	public function subject() {
		$ptype_string = $this->ptype;
		if (is_numeric($this->ptype)) {
			$choices = $this->get_prop_type_mapped();
			$choice = $choices[$this->ptype];
			$ptype_string = $choice['label'];
		}
		?>

		<div class="subject">

			<div class="wrap">

				<h2><? _e( 'Subject Property', 'ifound' ); ?></h2>
				<h3><? if( $this->address ) _e( $this->address, 'ifound' ); ?></h3>

				<div class="one-half first">

					<div class="cmc-heading ptype">
						<? _e( 'Property Type:', 'ifound' ); ?>
					</div>

					<div class="cmc-value ptype">
						<?= $ptype_string ?>
					</div>

					<div class="cmc-heading">
						<? _e( 'Bedrooms:', 'ifound' ); ?>
					</div>

					<div class="cmc-value">
						<? if( $this->beds ) _e( $this->beds, 'ifound' ); ?>
					</div>

					<div class="cmc-heading">
						<? _e( 'Bathrooms:', 'ifound' ); ?>
					</div>

					<div class="cmc-value">
						<? if( $this->baths ) _e( $this->baths, 'ifound' ); ?>
					</div>


				</div>

				<div class="one-half">

					<div class="cmc-heading">
						<? _e( 'Interior SqFt:', 'ifound' ); ?>
					</div>

					<div class="cmc-value">
						<? if( $this->sqft ) _e( $this->sqft, 'ifound' ); ?>
					</div>

					<div class="cmc-heading">
						<? _e( 'Interior Stories:', 'ifound' ); ?>
					</div>

					<div class="cmc-value">
						<? if( $this->stories ) _e( $this->stories, 'ifound' ); ?>
					</div>

					<div class="cmc-heading">
						<? _e( 'Year Built:', 'ifound' ); ?>
					</div>

					<div class="cmc-value">
						<? if( $this->built ) _e( $this->built, 'ifound' ); ?>
					</div>

				</div>

			</div>

		</div><?
	}

	/**
	 * Body
	 *
	 * Diplay the CMC active/closed listings data.
	 *
	 * @since 1.0.0
	 */

	public function body( $listings, $title ) {

		if( empty( $listings->listings[0] ) ) { ?>

			<div class="listing">

				<div class="wrap">
					<? _e( 'There are no comparable ' . strtolower( $title ) . ' properties available at this time.', 'ifound' ); ?>
				</div>

			</div><?

			return;
		}

		$stats = $listings->stats;

		$stat = isset( $stats->fields->close_price ) ? $stats->fields->close_price : $stats->fields->list_price; ?>

		<div class="listing">

			<div class="wrap">

				<h2><? _e(  $stat->count . ' ' . $title . ' Properties', 'ifound' ); ?></h2>

				<div class="compare-wrap">

					<div class="one-third first">
						<? _e( 'Lowest Price: $' . number_format( $stat->min ), 'ifound' ); ?>
					</div>

					<div class="one-third">
						<? _e( 'Median Price: $' . number_format( round( $stat->median ) ), 'ifound' ); ?>
					</div>

					<div class="one-third">
						<? _e( 'Highest Price: $' . number_format( $stat->max ), 'ifound' ); ?>
					</div>

					<div class="clear"></div>

				</div>

				<div class="listing-wrap cmc-headings">

					<div class="ifound-wrap">

						<div class="one-half first">
							<? _e( 'Address', 'ifound' ); ?>
						</div>

						<div class="one-sixth center">
							<? _e( 'Distance/Miles', 'ifound' ); ?>
						</div>

						<div class="one-sixth center">
							<? _e(  $title . ' Price', 'ifound' ); ?>
						</div>

						<div class="one-sixth center">
							<? _e( 'Details', 'ifound' ); ?>
						</div>

					</div>

				</div><?

				foreach( $listings->listings as $listing ) {

					$image = apply_filters( 'ifound_check_image', $listing->img_url );

					$distance = $this->distance( $listing->Latitude, $listing->Longitude );

					$address = apply_filters( 'ifound_address', $listing );

					$href_str = '';
					if (!$this->util()->is_user_agent_facebook()) {
						$href = apply_filters( 'ifound_detail_url', '{DetailsURL}', $listing );
						$href_str = "href=\"$href\"";
					}
					?>

					<div id="<? echo $listing->ListingID; ?>" class="listing-wrap">

						<div class="ifound-wrap">

							<div class="one-half first">

								<a rel="nofollow" <? echo $href_str; ?> target="_blank">
									<? _e( $address, 'ifound' ); ?>
								</a>

							</div>

							<div class="one-sixth cmc-data">
								<span class="miles-total"><? _e( $distance, 'ifound' ); ?> </span>
								<span class="miles"><? _e( 'Miles', 'ifound' ); ?></span>
							</div>

							<div class="one-sixth cmc-data"><?

								if( ! empty( $listing->ClosePrice ) && $listing->ClosePrice != '$0' ) {

								 	_e( $listing->ClosePrice, 'ifound' );

								 } else {

								 	_e( $listing->ListPrice, 'ifound' );

								 } ?>

							</div>

							<div class="one-sixth cmc-data open cmc-more">
								<a><? _e( 'More', 'ifound' ); ?></a>
							</div>

							<span class="open-box hide">

								<div class="one-third first">
									<img src='<? echo $image; ?>'/>
								</div>


								<div class="one-third">

									<div class="cmc-listing-details"><?

										if ( isset( $listing->Beds ) ) { ?>

											<div class="cmc-detail beds">
												<? _e( 'Beds: ' . $listing->Beds, 'ifound' ); ?>
											</div><?

										}

										if ( isset( $listing->Bathrooms ) ) { ?>

											<div class="cmc-detail baths">
												<? _e( 'Baths: ' . $listing->Bathrooms, 'ifound' ); ?>
											</div><?

										}

										if ( isset( $listing->IntStories ) ) { ?>

											<div class="cmc-detail stories">
												<? _e( 'Stories: ' . $listing->IntStories, 'ifound' ); ?>
											</div><?

										}

										if ( isset( $listing->SquareFeet ) ) { ?>

											<div class="cmc-detail sqft">
												<? _e( 'SqFt: ' . $listing->SquareFeet, 'ifound' ); ?>
											</div><?

										}

										if ( isset( $listing->PriceSqFt ) ) { ?>

											<div class="cmc-detail price-per">
												<? _e( 'Price/SqFt: $' . $listing->PriceSqFt, 'ifound' ); ?>
											</div><?

										} ?>

									</div>

								</div>

								<div class="one-third"><?

									if( $listing->CloseDate ) {?>

										<div class="close-date">
											<? _e( 'Closed Date: ' . date( 'm d, Y', strtotime( $listing->CloseDate ) ), 'ifound' ); ?>
										</div><?
									} ?>

									<div class="cmc-detail-button">

										<a rel="nofollow" <? echo $href_str; ?> class="button" target="_blank">
											<? _e( 'Full Details', 'ifound' ); ?>
										</a>

									</div>

								</div>

							</span>

							<div class="cmc-data open cmc-mobile-more">
								<a><? _e( 'More', 'ifound' ); ?></a>
							</div>

						</div>

					</div><?

				} ?>

				</div>

			</div><?

	}

	/**
	 * Map Data
	 *
	 * Create the json map data oblect
	 *
	 * @since 1.0.0
	 *
	 * @return object $map_stuff The map data used to create the CMC map.
	 */

	public function map_stuff( $listings, $image ) {

		if( ! get_option( 'cmc_activate' ) ) return;

		if( empty ( $listings ) ) return array();

		foreach( $listings as $listing ) {

			$address = apply_filters( 'ifound_address', $listing );

			$map_stuff[] = array(
				$address,
				$listing->Latitude,
				$listing->Longitude,
				$image,
				$listing->ListingID
			);

		}

		return $map_stuff;
	}

	/**
	 * Get Distance
	 *
	 * Get the distance of listing from subject property.
	 *
	 * @since 1.0.0
	 *
	 * @params array $listings Listings for the CMC active/closed properties.
	 * @return array $listings Listings for the CMC active/closed properties with distance.
	 */

	public function get_distance( $listings ) {

		$new_listings = array();

		foreach( $listings as $key => $value ) {

			$listings[$key]['distance'] = $this->distance( $value['Latitude'], $value['Longitude'] );

		}

		return $listings;

	}

	/**
	 * Distance
	 *
	 * Calculates the great-circle distance between two points, with
	 * the Vincenty formula.
	 *
	 * @param float $latitudeTo Latitude of target point in [deg decimal]
	 * @param float $longitudeTo Longitude of target point in [deg decimal]
	 * @return float Distance between points in [m] (same as earthRadius)
	 *
	 * @link http://stackoverflow.com/questions/10053358/measuring-the-distance-between-two-coordinates-in-php
	 */

	public function distance( $latitudeTo, $longitudeTo ) {

	  	// convert from degrees to radians
	 	$latFrom	= deg2rad( $this->lat );
	  	$lonFrom 	= deg2rad( $this->lng );
	  	$latTo 		= deg2rad( $latitudeTo );
	  	$lonTo 		= deg2rad( $longitudeTo );

	  	$lonDelta = $lonTo - $lonFrom;
	  	$a = pow( cos( $latTo ) * sin( $lonDelta ), 2 ) + pow( cos( $latFrom ) * sin( $latTo ) - sin( $latFrom ) * cos( $latTo ) * cos( $lonDelta ), 2 );
	  	$b = sin( $latFrom ) * sin( $latTo ) + cos( $latFrom ) * cos( $latTo ) * cos( $lonDelta );

	  	$angle = atan2( sqrt( $a ), $b );
	  	return round( $angle * 3959, 2 ); // 3959 Earths Radius returns Miles
	}

	/**
	 * Error
	 *
	 * @since 1.0.0
	 *
	 * @return string $error The error message if Lat/Lng are not provided.
	 */

	public function error() { ?>

		<div class="error-message"><? _e( 'There was an error processing your request.', 'ifound' ); ?></div><?

	}

	/**
	 * CMC Update Form PropTypes
	 *
	 * Update property types for MLS in CMC Gravity Form.
	 * Default Property types are for ARMLS.
	 *
	 * @since 1.0.0
	 */

	public static function cmc_update_form_ptypes() {

		global $mls_associations;

		if( $form_id = get_option( 'cmc_ifound_form_id' ) ) {

			$form = GFAPI::get_form( $form_id );

			foreach( $mls_associations->prop_type as $key => $value ) {

				$ptypes_array[] = array(
					'text' 			=> $key,
					'value'			=> $key,
					'isSelected'	=> false,
					'price'			=> ''
				);

			}

			$select_one[] = array(
				'text' 			=> 'Select One',
				'value'			=> '',
				'isSelected'	=> true,
				'price'			=> ""
			);

			$form['fields'][3]['choices'] = array_merge_recursive( $select_one, $ptypes_array );

			GFAPI::update_form( $form, $form_id );

		}

	}

	/**
	 * Log Activity CMC Address
	 *
	 * Log address capture in contacts activity log. This is called by GFORMS.
	 *
	 * @since 2.1.3
	 *
	 * @link https://docs.gravityforms.com/gform_after_submission/
	 */

	public function log_activity_cmc_address( $entry, $form ) {
		do_action( 'ifound_activity_log', iFoundContacts::get_contact_id(), 'Capture Current Market Comparison Address', rgar( $entry, '1' ) );
	}

}

$cmc_directories = array(
	'admin',
	'widgets'
);

foreach( $cmc_directories as $directory ) {

	foreach ( glob( plugin_dir_path( __FILE__ ) . $directory . '/*.php' ) as $file ) {
	    require_once $file;
	}

}
