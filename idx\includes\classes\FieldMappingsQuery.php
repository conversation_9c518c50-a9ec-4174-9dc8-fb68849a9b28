<?php

require_once '../vendor/autoload.php';
require_once 'IDX.php';

class FieldMappingsQuery extends IDX {
	public function fetchFieldMappings($mls, $mls_class) {
		// Set the query type for logging purposes. See case 559.
		$this->query_type = "FieldMappings";

		$data = $this->fetchData($mls, $mls_class);
		$response = $this->mapRelationalDataToObject($data);

		return $response;
	}

	protected function fetchData($mls, $mls_class) {
		$genericMlsClassName = Profound\MLS\MlsClass::getGenericMlsClassName($mls, $mls_class);
		$sql = "SELECT DisplayName, EasyName, Type, $mls as SystemName FROM field_mapping where mls_class = ? and $mls <> '' and DisplayName <> '' order by DisplayName";
		$results = $this->db->fetchAll($sql, array($genericMlsClassName));
		return $results;
	}

	protected function mapRelationalDataToObject($data) {
		$fieldMappings = array();
		foreach ($data as $row) {
			$easyName = $row['EasyName'];
			$fieldMappings[$easyName] = array(
				'display_name' => $row['DisplayName'],
				'easy_name' => $row['EasyName'],
				'type' => $row['Type'],
				'system_name' => $row['SystemName']
			);
		}
		$output = array('field_mappings' => $fieldMappings);
		return $output;
	}
}
