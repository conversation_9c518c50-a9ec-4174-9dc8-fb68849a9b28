<?php
/**
 * iFoundQuickSearchAdmin class
 *
 * @since 5.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundQuickSearchAdmin {
	use NewHooklessTrait;

	/**
	 * init iFoundQuickSearchAdmin class.
	 *
	 * @since 5.0.0
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 5.0.0
	 */
	 
	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_menu', array($this, 'settings_menu'));
		}
	}

	/**
	 * Settings Menu
	 *
	 * Show the iFound Tab in the WP Admin.
	 *
	 * @since 5.0.0
	 */
	
	public function settings_menu() {
		
		add_submenu_page(
        	'ifound_agent',
        	'Quick Search',
        	'Quick Search',
        	'edit_crm_settings',
			'quick_search',
        	array( $this, 'quick_search_criteria' ) 
		);

	}

	public function get_fields() {
		global $mls_associations;
		$fields = $mls_associations->quick_search_fields;
		$fields->open_house = 'checkbox';
		if (iFoundIdx::new_hookless()->get_prop_type_mapped()) {
			$fields->prop_type_mapped = 'select';
		}
		return $fields;
	}

	/**
	 * Quick Search Criteria
	 *
	 * @since 5.0.0
	 */
	
	public function quick_search_criteria() {

		if( ! current_user_can( 'edit_crm_settings' ) ) return;

		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'jquery-ui-sortable' );
		wp_enqueue_script( 'search_criteria_js' );
		
		global $mls_associations;

		$page 		 = 'quick_search_criteria';
		$option_name = $page . iFoundCrm::new_hookless()->crm_id();
		$fields 	 = $mls_associations->quick_search_fields;
		$choices 	 = get_option( 'ifound_' . $option_name, array() ); ?>

		<h1 class="ifound-admin-h1"><? _e( 'Quick Filters', 'found' ); ?></h1><?

		do_action( 'ifound_help_button', 'quick_search' ); ?>

		<div id="quick-search-criteria" class="ifound-wrap"><?
		
			do_action( 'ifound_search_criteria_options', $fields, $choices );
			do_action( 'ifound_search_criteria_form', $choices, 'sortable-criteria', 'Quick Filters', 'criteria-main' ); ?>

		</div><? 

		do_action( 'ifound_save_choices_button', $option_name );
		
	}
	
}
