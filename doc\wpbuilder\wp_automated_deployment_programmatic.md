[Home](Home)

[TOC]

# Creating and Cloning Sites Programmatically Using WordPress Core Functions

## Goal

Create a script file to be run by [WP-CL<PERSON>](http://wp-cli.org/) that will create a new site, then clone an existing site into the new site.  Once this is working, create a plugin that adds a WP-CLI command that allows relevant information to be passed in.  This could also potentially be done through an API, though that requires exposing the interface used to do it to the world.

## Important Functions

- [`wpmu_create_blog( $domain, $path, $blog_title, $current_user->ID[, $meta][, $wpdb->siteid] );`](http://codex.wordpress.org/WPMU_Functions/wpmu_create_blog)
    
    Used to create a new blog.  Returns the ID of the blog that was created.  See `validate_another_blog_signup` in `wp-signup.php` for a real-world example of its use.

- [`update_blog_option($blog_id, $key, $value[, $refresh])`](https://codex.wordpress.org/WPMU_Functions/update_blog_option)
    
    Updates a setting on the specified blog.  This basically just switches to the specified blog, runs `update_option()`, and switches back.  See the codex for [`update_option()`](https://codex.wordpress.org/Function_Reference/update_option) for more information.  Note that if you attempt to update an option that doesn't exist, it will be added.

## Steps taken

### Automating the Site Deployment and Cloning Process

1. Created a throwaway experimental script file to execute with WP-CLI using `wp eval-file testscript.php`.  Using this file, I verified that no include statements are necessary to access WP core functions when executed with WP-CLI; it appears to take care of completely loading the WP environment before executing the code.

2. I was able to successfully create blogs using `wpmu_create_blog()` as described above.  This returns the ID of the blog it creates.  **Note**:  The `$domain` argument for `wpmu_create_blog()` needs to include the subdomain AND the main domain.  In my case, this was, e.g., `site6.wpmulti`

3. I next attempted to use the cloning script from [this blog](http://buddydev.com/wordpress-multisite/cloning-blogs-on-wordpress-multisite-programmatically/), which didn't throw any errors at the time of execution, but it did present this error message when accessing the network admin panel under the list of sites:

        Warning: array_keys() expects parameter 1 to be array, string given in /home/<USER>/sites/profound/wp_automation/wordpress/wp-includes/capabilities.php on line 141 Warning: Invalid argument supplied for foreach() in /home/<USER>/sites/profound/wp_automation/wordpress/wp-includes/capabilities.php on line 141 Warning: array_keys() expects parameter 1 to be array, string given in /home/<USER>/sites/profound/wp_automation/wordpress/wp-includes/capabilities.php on line 141 Warning: Invalid argument supplied for foreach() in /home/<USER>/sites/profound/wp_automation/wordpress/wp-includes/capabilities.php on line 141 

    Line 141 in `capabilities.php` is a `foreach` statement in the `reinit()` function that loops through user roles.  This error showed up on every page in the administrative backend, not just the site listing, so I assume the script did some damage to the user roles.  I re-imported a database dump I had performed immediately after adding the first multisite blog through the UI, which addressed the error in WordPress, but apparently left the tables behind for the other blogs.  Attempting to run my site creation/cloning script at that point returned an error stating:

        <h1>Already Installed</h1><p>You appear to have already installed WordPress. To reinstall please clear your old database tables first.</p></body></html>

    I next dropped the database completely, re-created, and then re-imported the snapshot.  The script ran with no issues at that point.

4. In an attempt to address the user roles issue, I removed the section of my script that was intended to preserve user roles for new blogs.  This attempt was successful; re-running the script produced a new blog and did not produce the errors.

        $excluded_options = array(
            'siteurl',
            'blogname',
            'blogdescription',
            'home',
            'admin_email',
            'upload_path',
            'upload_url_path',
            // The line below appears to cause the foreach errors
            // $new_table_prefix.'user_roles' //preserve the roles
        );

5. At this point I still hadn't logged in to any of the multisites for any reason.  Now, wanting to test to make sure posts, settings, etc. were properly cloned, I tried to, and got this error on the login screen after submitting the admin credentials:

    > ERROR: Cookies are blocked or not supported by your browser. You must enable cookies to use WordPress.

    Cookies were properly enabled.  Clearing the cache did not resolve the issue.  After some google, I added the following to `wp-config.php`, which resolved the issue:

            /* Cookie settings to prevent weird error message on multisite login */
            define('ADMIN_COOKIE_PATH', '/');
            define('COOKIE_DOMAIN', '');
            define('COOKIEPATH', '');
            define('SITECOOKIEPATH', ''); 

6. I whipped up a simple makefile to reset the database to a known-good point between experiments.

        DB=mywordpressdb
        DBUSER=user
        DBPW=password
        SNAPSHOT_DIR=db-snapshots/
        SNAPSHOT=2014-04-20_post_added_to_site1.sql

        reset-db: 
            mysql --user=$(DBUSER) --password=$(DBPW) -e 'drop database $(DB);create database $(DB);' && mysql --user=$(DBUSER) --password=$(DBPW) $(DB) < $(SNAPSHOT_DIR)/$(SNAPSHOT)

    At this point in my progression, this snapshot is just the default root site, with one blog added that has been unaltered except to add a single test post.

7. After resetting the DB, I cloned my test site to a new site, and IT WORKED.  The test post I had created was properly duplicated in the new blog.

### Creating the Plugin to Add A Custom WP-CLI Command

1. Next step: create the plugin.  I followed the instructions in the [Command Cookbook](https://github.com/wp-cli/wp-cli/wiki/Commands-Cookbook) and declared a class extending `WP_CLI_Command`.  Initially, this was a basic function to echo the command line inputs, just to verify that arguments were being received and processed properly from the command line:

        class IDX_Commands extends WP_CLI_Command {

            function clone_to_new_site( $args, $assoc_args ) {
                $domain = $assoc_args['domain'];
                $from_id = $assoc_args['from_blog_id'];
                WP_CLI::success( "\nYou have requested to copy blog id $from_id to domain $domain.");
            }
        }

        WP_CLI::add_command( 'idx', 'IDX_Commands' );

    Attempting to activate the plugin returns the following error:

        Fatal error: Class 'WP_CLI_Command' not found in /home/<USER>/sites/profound/wp_automation/wordpress/wp-content/plugins/deploy-o-matic.php on line 15

    There are luckily other plugins that define custom commands for WP-CLI, such as [wp-composer](http://wordpress.org/plugins/composer/).  I have downloaded and installed this plugin to pick through its code and see what they're doing differently.  In the main plugin file, `run.php`, a class is instantiated that extends `WP_CLI_Command`.  They're pulling it off somehow.  I'm chasing the various `require`s and `include`s in the plugin and coming up with dead ends.  There's nothing tying it back to the `WP_CLI_Command` class, and searching within the project does not show that class being defined anywhere.

2. Clue: running `wp` with no arguments from the command line now fails.  There appear to be missing dependencies.

        PHP Fatal error:  require_once(): Failed opening required '/home/<USER>/sites/profound/wp_automation/wordpress/wp-content/plugins/composer/wp-cli-composer-command.php'

    There's a `composer.json` file in the `wp-composer` plugin directory. On a hunch, I installed composer globally:

        $ curl -sS https://getcomposer.org/installer | php
        $ sudo mv composer.phar /usr/local/bin/composer
        $ composer install
        $ composer update

    ...which changed nothing.  Running `wp` at the command line throws the same error.  I'm questioning my choice of example at this stage.

3. I uninstalled wp-composer to allow WP-CLI to function properly, then installed [blog-duplicator](https://github.com/trepmal/blog-duplicator/blob/master/blog-duplicator-cli.php) instead.  It's a much simpler plugin - 2 files and a readme - and it both activates smoothly within WordPress and allows proper functionality within WP-CLI.  Oddly, however, there are no `include`s or `require`s linking it to a definition of the `WP_CLI_Command` class.  It still shows its additional commands properly from within the WP-CLI help file, however.  Testing shows that its code is running as expected, though I did not step all the way through a duplication.

4.  Here's the trick.  Two files are necessary; one contains the plugin heads for WP, and the following code:

        if ( defined('WP_CLI') && WP_CLI ) {
            include plugin_dir_path( __FILE__ ) . '/deploy-o-matic-cli.php';
        }

    It appears there's a `WP_CLI` function that is loading the necessary classes, etc. for WP-CLI.  The file `deploy-o-matic-cli.php` actually contains the class definition listed above.  After making this change, the plugin activated successfully and is running properly through the command line.  We now have a workable interface to the outside world.  An API may end up being the smarter way to do this, if it's possible without too many compromises in terms of security, etc.

### Expanding Plugin Functionality

1. Time to actually add useful functionality to WP-CLI.  For starters, I've defined the commands `wp idx create --domain=mydomain --title='My Title!'` and `wp idx clone --from_id=2 --to_id=6`.

2. Now that I've accomplished a basic scaffold for how this idea is going to take shape, I'm shifting my focus toward finding an acceptable automated test solution.











