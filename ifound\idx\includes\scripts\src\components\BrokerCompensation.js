import React, {useState, useEffect, Component} from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import {makeRandomId} from "../lib/utils";
import axios from 'axios';
import useFetcher from '../hooks/useFetcher';
import useIfoundToasts from '../hooks/useIfoundToasts';
import classnames from 'classnames';

import './BrokerCompensation.css';
import {getDateTimeNowAsMysqlStr} from '../lib/datetime';

function BrokerCompensation(props) {
	const [legalDisclaimerHistory, setLegalDisclaimerHistory] = useState(props.legal_disclaimer_history);
	const [compensations, setCompensations] = useState(props.compensations);
	const [allowedSeeHowItLooksMlsIds, setAllowedSeeHowItLooksMlsIds] = useState(() => {
		return new Set(props.compensations.map(x => x.mls_id));
	});
	const [showCompensations, setShowCompensations] = useState(computeMostRecentShowCompensations);
	const {addSuccessToast, showToastForFetchError} = useIfoundToasts();

	function computeMostRecentShowCompensations() {
		if (!props.legal_disclaimer_history.length) {
			return false;
		}
		const mostRecentHistory = props.legal_disclaimer_history[props.legal_disclaimer_history.length - 1];
		return mostRecentHistory.status === 'checked';
	}

	const {
		isLoading,
		isSuccess,
		run,
	} = useFetcher({
		fetchFn: () => axios({
			url: props.endpoint,
			method: 'POST',
			data: ({
				legal_disclaimer_history: (() => {
					let history = props.legal_disclaimer_history;
					if (showCompensations !== computeMostRecentShowCompensations()) {
						history.push({
							status: showCompensations ? 'checked' : 'unchecked',
							date_gmt: getDateTimeNowAsMysqlStr(),
						})
					}
					return history;
				})(),
				compensations: compensations,
			}),
		})
			.then(() => {
				setAllowedSeeHowItLooksMlsIds(new Set(compensations.map(x => x.mls_id)));
				addSuccessToast('Saved!')
			})
			.catch(showToastForFetchError)
	})

	function onAdd() {
		// We need the row_id for the sake of React, so it can have a key for each item in the table.
		setCompensations(compensations.concat({
			row_id: makeRandomId(),
			mls_id: '',
			text: '',
		}));
	}

	function remove(rowId) {
		setCompensations(compensations.filter(x => x.row_id !== rowId));
	}

	function setValue(rowId, fieldName, val) {
		setCompensations(produce(compensations, draftState => {
			draftState.find(x => x.row_id === rowId)[fieldName] = val;
			return draftState;
		}));
	}

	function onSave(event) {
		event.preventDefault();
		run();
	}

	// You can only see how it looks if we know it's been saved.
	function maySeeHowItLooks(mlsId) {
		return allowedSeeHowItLooksMlsIds.has(mlsId);
	}

	function onChangeCheckbox() {
		setShowCompensations(!showCompensations);
	}

	return <div className="ifound-broker-compensation">
		<form onSubmit={onSave}>
			<div>
				<div>
					<label htmlFor="legal_disclaimer" onClick={onChangeCheckbox}>
						<input type="checkbox"
							   onChange={onChangeCheckbox}
							   checked={showCompensations}
							   value="checked"/> I agree to the following legal conditions
					</label>
				</div>

				<div>By checking this box, you as the agent who owns this website agree to the following legal
					disclaimer
					associated with using the Broker Compensation feature. Additionally, when you check this box,
					iFoundAgent will be emailed a copy of this disclaimer showing that you agreed to it for our records.
					If you should uncheck this box in the future, the broker compensation feature will stop
					functioning.
				</div>
			</div>

			<div className="disclaimer">
				<h2><strong>Legal Disclaimer</strong></h2>

				<div>You, {props.agentName}, real estate agent ("Agent"), owner of this website,
					acknowledge and agree that Pro-Found Marketing, LLC (aka "iFoundAgent" & "iFoundAgent.com" & "iFound
					CRM" & "iFound CRM"), the provider of the real estate website, CRM, and IDX display services that
					you use, shall be held harmless from any and all claims, liabilities, damages, or legal actions that
					may arise as a result of the Agent's use of the Broker Compensation feature associated with your
					iFoundAgent website, CRM, and IDX.
				</div>

				<div>The Agent agrees to assume full responsibility for any and all consequences, legal or otherwise,
					resulting from the use of the broker compensation feature, including but not limited to claims of
					misrepresentation, non-disclosure, or other disputes related to the listing information displayed on
					the Agent's website.
				</div>

				<div><strong>Broker Compensation Information</strong></div>

				<div>The Agent understands and agrees that they may only provide broker compensation information on
					properties where they are the listing agent, and on listings from their brokerage, provided they
					have the explicit permission of their brokerage to do so. The Agent is responsible for ensuring that
					the disclosure of broker compensation is accurate and compliant with all applicable rules,
					regulations, and brokerage policies.
				</div>

				<div><strong>Indemnification</strong></div>

				<div>By using the iFoundAgent services, the Agent expressly releases iFoundAgent from any liability and
					agrees to indemnify and defend iFoundAgent against any lawsuits, claims, or actions brought against
					it in connection with the use of the broker compensation feature. This indemnification extends to
					all costs, including but not limited to legal fees, damages, and settlements.
				</div>

				<div><strong>Compliance and Responsibility</strong></div>

				<div>The Agent acknowledges that they are solely responsible for compliance with all applicable laws,
					regulations, and real estate practices related to the use of the iFoundAgent services and the
					information displayed on their website.
				</div>

				<div><strong>Governing Law</strong></div>

				<div>This disclaimer shall be binding upon the Agent and their heirs, legal representatives, and
					assigns.
					The Agent agrees that this disclaimer shall be governed by the laws of the jurisdiction in which the
					Agent operates.
				</div>
			</div>
			{showCompensations &&
				<div>
					<table className="widefat striped">
						<thead>
						<tr>
							<th>MLS ID</th>
							<th>Compensation Text</th>
							<th>See how it looks</th>
							<th>Actions</th>
						</tr>
						</thead>
						<tbody>
						{compensations.map(x => {
							const url = new URL(window.location.href);
							for (const key of url.searchParams.keys()) {
								url.searchParams.delete(key);
							}
							// The listing could be closed or some non active status, so make sure we check all statuses.
							const listingStatusesStr = props.allListingStatuses.map(x => {
								return `list_status-${x}`
							})
								.join('/');
							url.pathname = `/listing-search/${listingStatusesStr}/mls_id-${x.mls_id}/`;
							const link = url.toString();
							return <tr key={x.row_id}>
								<td>
									<input type="text"
										   onChange={event => setValue(x.row_id, 'mls_id', event.target.value)}
										   value={x.mls_id} placeholder="MLS ID" className="regular-text" required/>
								</td>
								<td>
									<input type="text"
										   onChange={event => setValue(x.row_id, 'text', event.target.value)}
										   value={x.text} placeholder="Compensation text, e.g. X%"
										   className="regular-text"
										   required/>
								</td>
								<td>
									{maySeeHowItLooks(x.mls_id) &&
										<a target="_blank" href={link}>
											<i className="fa fa-external-link" aria-hidden="true"></i>
											{' '}
											See how it looks
										</a>
									}
								</td>
								<td>
									<i onClick={() => remove(x.row_id)} className="far fa-trash" title="Remove"
									   style={{cursor: 'pointer', color: 'red'}}></i>
								</td>
							</tr>;
						})}
						</tbody>
					</table>
					<div style={{marginTop: '10px'}}>
						<button type="button" className="button" onClick={onAdd}>
							<i className="fal fa-plus-square"></i>
							{' '}
							Add
						</button>
					</div>
				</div>
			}
			<div style={{marginTop: '10px'}}>
				<button type="submit" className="button button-primary">
					<i className={classnames('fal', {
						'fa-paper-plane': !isLoading,
						'fa-spinner': isLoading,
						'fa-spin': isLoading,
					})}></i>
					{' '}
					Save
				</button>
			</div>
		</form>
	</div>
}

BrokerCompensation.propTypes = {
	endpoint: PropTypes.string,
	agentName: PropTypes.string.isRequired,
	allListingStatuses: PropTypes.arrayOf(PropTypes.string).isRequired,
	legal_disclaimer_history: PropTypes.arrayOf(PropTypes.shape({
		status: PropTypes.string.isRequired,
		date_gmt: PropTypes.string.isRequired,
	})),
	compensations: PropTypes.arrayOf(PropTypes.shape({
		row_id: PropTypes.number.isRequired,
		mls_id: PropTypes.string.isRequired,
		text: PropTypes.string.isRequired,
	})),
};

export default BrokerCompensation;
