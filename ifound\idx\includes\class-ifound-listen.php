<?
/**
 * iFoundListen class
 *
 * Listen for HTTP requests.
 *
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundListen extends iFoundIdx {
	use UtilTrait;

	/**
	 * init iFoundListen class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct() {

		/**
		 * Endpoints
		 *
		 * @since 1.0.0
		 */

		add_action( 'init', array( $this, 'endpoints' ) );

		/**
		 * Endpoint Callback
		 *
		 * @since 1.0.0
		 */

		add_action( 'template_redirect', array( $this, 'endpoint_callback' ) );
		add_action( 'template_redirect', array( $this, 'search_results' ) );
		add_action( 'template_redirect', array( $this, 'search_nearby' ) );
		add_action( 'template_redirect', array( $this, 'polygon_search' ) );

		/**
		 * Init REST API
		 *
		 * @since 1.0.4
		 */

		add_action( 'rest_api_init', array( $this, 'register_route' ) );

	}

	/**
	 * Rewrite Endpoints
	 *
	 * Let's get our endpoint registered.
	 *
	 * @since 1.0.0
	 * @since 2.5.39 Add subdivisions endpoint.
	 */

	public function endpoints() {

		add_rewrite_endpoint( $this->detail, 			EP_ALL );
		add_rewrite_endpoint( $this->search, 			EP_ALL );
		add_rewrite_endpoint( $this->advanced, 			EP_ALL );
		add_rewrite_endpoint( $this->save_this, 		EP_ALL );
		add_rewrite_endpoint( $this->unsubscribe, 		EP_ALL );
		add_rewrite_endpoint( $this->client_profile,	EP_ALL );
		add_rewrite_endpoint( $this->polygon_search,	EP_ALL );
		add_rewrite_endpoint( $this->subdivisions,		EP_ALL );
		add_rewrite_endpoint( $this->cmc_report,		EP_ALL );
		add_rewrite_endpoint( $this->search_nearby,		EP_ALL );

	}

	/**
	 * Search Results
	 *
	 * @since 1.0.0
	 * @since 2.5.8 Add $results param to ifound_seo hook.
	 */

	public function  search_results() {

		global $wp_query;

		if ( isset( $wp_query->query_vars[$this->search] ) ) {

			define( 'DOING_RESULTS', true );

			wp_enqueue_script( 'save_this_js' );

			$uri_string = $wp_query->get( $this->search );

			if ( ! empty( $uri_string ) ) {

				$parts = explode( '/', $uri_string );

				if( is_array( $parts) ) {

					$params = array();

					$param_type = $this->param_type();

					foreach(  $parts as $part ) {

						list( $key, $value ) = explode( '-', $part, 2 );

						if ($key === 'polygons') {
							$json_string = urldecode($value);
							$json_data = json_decode($json_string, true);
							// We expect all polygons in one value, therefore we don't append to the array spot, but
							// merely set it.
							$params[$key] = $json_data;
						} else if (
							$key === 'prop_type_mapped'
							|| in_array($key, $param_type['text'] ?? [])
							|| in_array($key, $param_type['boolean'] ?? [])
						) {

							$params[$key][] = urldecode( $value );

						} else {

							$remove 	= array( '_min', '_max' );
							$easy_name 	= str_replace( $remove, '', $key );
							$is_days_ago_field = $this->util()->is_easy_name_a_days_ago_field($easy_name);

							if(in_array($easy_name, $param_type['number'])
								|| in_array($easy_name, $param_type['date'])
								|| $is_days_ago_field
							) {

								$mm = ( strpos( $key, '_max' ) !== false ) ? 'max' : 'min';

								$params[$easy_name][$mm] = urldecode( $value );

							} else {

								// This is for our other params
								$params[$key] = urldecode( $value );

							}

						}

					}

				}

			}

			if($params !== NULL) {
				$params['mls_class'] = $this->mls_class( $params['mls_class'], false );
				$results = $this->process_input( $params );
				$results->input_obj = $params;
				do_action( 'ifound_external_crm_search_entry', apply_filters( 'ifound_obj', $params ) );
				do_action( 'ifound_seo', 'results', $results->listings[0] );
			} else $results = NULL;

			/** Let's find the page template. */
			$this->template( $results );

			die();

		}

	}

	/**
	 * Search Nearby
	 *
	 * Search using your current loaction and nearby radius.
	 *
	 * @since 1.0.0
	 * @since 4.1.11 Move to this method from iFoundListen::endpoint_callback()
	 */

	public function search_nearby() {

		global $wp_query;

		if ( isset( $wp_query->query_vars[$this->search_nearby] ) ) {

			define( 'DOING_RESULTS', true );

			wp_enqueue_script( 'save_this_js' );

			$input = array();

			if( $uri_string = $wp_query->get( $this->search_nearby ) ) {

				list( $lat, $lng ) = explode( '/', $uri_string );

				$input['nearby'] = array(
					'radius' 	=> 1,
					'lat'	 	=> $lat,
					'lng'		=> $lng
				);

			}

			$input['mls_class'] = 'res';
			$params['stats'] 	= array( 'list_price' );

			$params['stats'] 	= array( 'list_price' );

			$this->params = $input;

			$results = $this->request();

			$results->input_obj = $input;

			do_action( 'ifound_external_crm_search_entry', $results->input_obj );

			do_action( 'ifound_seo', 'results', $results->listings[0] );

			/** Let's find the page template. */
			$this->template( $results );

			die();

		}

	}

	/**
	 * Process polygon_search
	 *
	 * @since 1.2.0
	 * @since 2.5.8 Add ifound_seo hook.
	 */

	public function polygon_search() {

		global $wp_query;

		if ( isset( $wp_query->query_vars[$this->polygon_search] ) ) {

			define( 'DOING_POLYGONS', true );

			$uri_string = $wp_query->get( $this->polygon_search );

			list( $slug, $polygon_id ) = explode( '/', $uri_string );

			$polygon_id = intval( $polygon_id );

			$input['polygons'][] = iFoundShortcode::new_hookless()->request_polygon_by_id( $polygon_id );

			$results = $this->process_input( $input );

			$results->input_obj = $input;

			do_action( 'ifound_seo', 'results', $results->listings[0] );

			/** Let's find the page template. */
			include( plugin_dir_path( __FILE__ ) . 'templates/advanced-search.php' );

			exit();

		}

	}

	/**
	 * Endpoint Callback
	 *
	 * Proccess the query_var and react accordindly.
	 *
	 * @since 1.0.0
	 */

	public function endpoint_callback() {

		global $wp_query;

		/**
	 	 * Process listing-details
	 	 *
	 	 * @since 1.0.0
	 	 * @since 1.2.36 Add 301 redirect for empty PDP.
	 	 * @since 2.5.8 Add $results param to ifound_seo hook.
	 	 * @since 2.5.59 Check if ListingID is set rather than expired.
	 	 * @since 3.6.2  Make request inside the details object.
	 	 */

		if ( isset( $wp_query->query_vars[$this->detail] ) ) {

			$uri_string = $wp_query->get( $this->detail );

			/** If no input, let's do something about it here. */
			if ( ! $uri_string ) do_action( 'ifound_no_data', 'results' );

			list( $address, $mls_id ) = explode( '/', $uri_string );

			$details = new iFoundDetails( $mls_id );

			wp_enqueue_script( 'pdp_js' );
			do_action( 'ifound_seo','detail', $details->details );

			$mls_class = $details->details->mls_class;
			$extra = ['mls_class' => $mls_class];
			/** Let's find the page template. */
			$this->template( $details->details, $extra);

			die();



		} elseif ( isset( $wp_query->query_vars[$this->advanced] ) ) {

			define( 'DOING_ADVANCED', true );

			wp_enqueue_script( 'save_this_js' );

			/** Let's find the page template. */
			$this->template();

			die();

		/**
	 	 * Process unsubscribe
	 	 *
	 	 * @since 1.0.0
	 	 * @since 1.2.34 Add ifound_activity_log action hook.
	 	 */

		} elseif ( isset( $wp_query->query_vars[$this->unsubscribe] ) ) {
			define( 'DOING_UNSUBSCRIBE', true );

			$contact_id = intval( $wp_query->get( 'unsubscribe' ) );

			if( is_int( $contact_id ) ) {

				// We used to use save_this_id, and must keep it around (at least for a while) to handle links in old
				// emails.
				$post_id = $_GET['post_id'] ?: $_GET['save_this_id'];
				$email_address = $_GET['email'];
				if ($post_id && $email_address) {
					// This is a spouse unsubscribing. We don't want to unsubscribe the contact. Just unsubscribe the
					// spouse from the campaign.
					do_action('ifound_remove_email_address_from_campaign', $post_id, $email_address, $contact_id);
					do_action( 'ifound_activity_log', $post_id, 'Unsubscribed', 'Unsubscribed from email list: ' . $email_address );
				} else {
					wp_set_object_terms( $contact_id, 'Unsubscribed', 'contacts_status', true );

					do_action( 'ifound_activity_log', $contact_id, 'Unsubscribed', 'Unsubscribed from email list' );
					iFoundContacts::new_hookless()->disable_all_campaigns($contact_id);
				}
			}

			/** Let's find the page template. */
			$this->template();

			die();

		/**
	 	 * Process save-this
	 	 *
	 	 * @since 1.0.0
	 	 * @since 1.2.34 Add ifound_activity_log action hook.
	 	 */

		} elseif ( isset( $wp_query->query_vars[$this->save_this] ) ) {

			define( 'DOING_SAVED_SEARCH', true );

			$save_this_id = intval( $wp_query->get( $this->save_this ) );

			if( is_int( $save_this_id ) ) {

				$post = get_post($save_this_id);
				if ($post->post_type !== 'save_this') {
					$this->show_wp_template_page_and_die(404);
				}
				$input = $post->params ?: [];
				if (isset($input['nearby']) && !isset($input['nearby']['lat'])) {
					$input = $this->util()->maybe_geocode_campaign($save_this_id);
				}
				$options = [];
				if ($post->stats) {
					$options['stats'] = $post->stats;
				}
				if ($post->extra_map_data) {
					$options['extra_map_data'] = $post->extra_map_data;
				}

				/** We need to find what type tihs is search/property??*/
				$results = $this->process_input( $input, $options );

				$results->input_obj = $input;

				//This is the id for the update button
				$results->save_this_id = $save_this_id;
			}

			/** Let's find the page template. */
			$this->template( $results, [
				'mls_class' => $input['mls_class'],
				'stats'     => $options['stats'],
				'extra_map_data'  => $options['extra_map_data'],
			] );

			die();

		/**
	 	 * Process subdivisions
	 	 *
	 	 * @since 2.5.39
	 	 */

		} elseif ( isset( $wp_query->query_vars[$this->subdivisions] ) ) {

			define( 'DOING_SUBDIVISIONS', true );

			$uri_string = $wp_query->get( $this->subdivisions );

			list( $slug, $search ) = explode( '/', $uri_string );

			$polygons = apply_filters( 'ifound_map_api', 'polygons/', array( 'all', 'subdivision', 'master', $search ) );

			// TODO: Return this title with the API response
			$extension 			= ( count( $polygons ) > 1 ) ? 'Subdivisions' : 'Subdivision';
			$page_title 		= str_replace( '_', ' ', $search );
			$this->page_title	= ucwords( $page_title ) . ' ' . $extension;

			$this->template( $polygons );

			die();

		/**
	 	 * Process client_profile
	 	 *
	 	 * @since 1.1.0
	 	 */

		} elseif ( isset( $wp_query->query_vars[$this->client_profile] ) ) {

			define( 'DOING_CLIENT', true );

			/** Let's find the page template. */
			$this->template();

			die();

		}

	}


	/**
	 * Register Routes
	 *
	 * Register the REST API Route. We handle all validation here. If any param is not valid. The whole thing dies.
	 *
	 * @since 1.0.0
	 *
	 * @see https://developer.wordpress.org/rest-api/extending-the-rest-api/adding-custom-endpoints/
	 */

	public function register_route() {

		/** SEARCH ROUTE */
		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/search/(?P<nonce>\S+)/?',
			array(
				'methods'  => WP_REST_Server::READABLE,
				'callback' => array( $this, 'process_search' ),
				'args' => array(
					'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'search_secure_me', $param, false );
						}
					)
      			)
			)
		);

		/** Cookie to Contact ID ROUTE */
		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/contact_id/(?P<nonce>\S+)',
			array(
				'methods'  => WP_REST_Server::READABLE,
				'callback' => function() {
					$contact_id = iFoundCrm::new_hookless()->get_contact_id();
					$is_from_lead_gen = false;
					// TODO: Seems wrong to register the taxonomy here. I guess the iFoundContacts->taxonomy() method is
					//   not run when it's a REST route?
					// Update: I found this link
					//   https://stackoverflow.com/questions/42462187/wordpress-rest-api-v2-how-to-list-taxonomy-terms
					// where multiple answers say it's important to use 'show_in_rest' when registering the taxonomy.
					// But that doesn't seem to make a difference for me. So registering here is the only way I know to
					// do it, but it still feels yucky. However, I've spent too much time so I'm going to live with it
					// for now.
					iFoundContacts::new_hookless()->register_contact_tag_taxonomy(iFoundContacts::$generic_contact_tag_taxonomy);
					$is_from_lead_gen = has_term('PPC Buyer CF', iFoundContacts::$generic_contact_tag_taxonomy,
						$contact_id);
					$terms = wp_get_post_terms($contact_id, iFoundContacts::$generic_contact_tag_taxonomy);
					$lead_data = null;
					if ($is_from_lead_gen) {
						$campaign_url = null;
						// Get the search campaign, if we can. We assume there's exactly one.
						$posts = get_posts([
							'post_author' => $contact_id,
							'post_type' => iFoundSaveThis::$the_post_type,
							'meta_query' => [
								[
									'key'   => 'contact_id',
									'value' => $contact_id,
								],
							],
							'fields' => 'ids',
						]);
						if (count($posts) === 1) {
							$campaign_id = $posts[0];
							$campaign_url = $this->util()->build_post_href( $campaign_id, 'view', [
								'ensure_href_is_url' => true,
							]);
						}
						$lead_data = [
							'campaign_url' => $campaign_url,
						];
					}
					return [
						'contact_id'       => $contact_id,
						'is_from_lead_gen' => $is_from_lead_gen,
						'lead_gen_data'        => $lead_data,
					];
				},
				'args' => array(
					'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'cookie_to_contact_id_secure_me', $param, false );
						}
					)
      			)
			)
		);

		/** ADMIN ROUTE */
		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/email/(?P<nonce>\S+)/(?P<input>[\d]+)',
			array(
				'methods'  				=> WP_REST_Server::READABLE,
				'callback' 				=> array( $this, 'get_email_template' ),
				'permission_callback' 	=> function () {
      				return true;//current_user_can( 'edit_posts' ); //TODO: Find out why this does not work. @see emeil-editor.js https://developer.wordpress.org/rest-api/using-the-rest-api/authentication/
    			},
				'args' => array(
      				'input' => array(
						'sanitize_callback' => 'absint',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_numeric( $param );
						}
					),
					'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'admin_secure_me', $param, false );
						}
					)
      			)
			)
		);

		/** SHORTCODE ROUTE */
		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/shortcode/(?P<nonce>\S+)/(?P<meta_id>[\d]+)/(?P<type>\S+)',
			array(
				'methods'  				=> WP_REST_Server::READABLE,
				'callback' 				=> array( $this, 'shortcode_tasks' ),
				'permission_callback' 	=> function () {
      				return true;//current_user_can( 'edit_posts' ); //TODO: Find out why this does not work. @see emeil-editor.js https://developer.wordpress.org/rest-api/using-the-rest-api/authentication/
    			},
				'args' => array(
      				'meta_id' => array(
						'sanitize_callback' => 'absint',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_numeric( $param );
						}
					),
					'type' => array(
						'sanitize_callback' => 'sanitize_text_field',
        				'validate_callback' => function( $param, $request, $key ) {
          					$allowed_array = array( 'dynamic', 'display', 'backup', 'stats' );
							return in_array( $param, $allowed_array );
						}
					),
					'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'shortcode_secure_me', $param, false );
						}
					)
      			)
			)
		);

		/** MLS CLASS ROUTE */
		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/criteria/(?P<nonce>\S+)/(?P<id>\S+)',
			array(
				'methods'  => WP_REST_Server::READABLE,
				'callback' => array( $this, 'criteria_on_mls_class_change' ),
				'args' => array(
					'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'dynamic_secure_me', $param, false );
						}
					),
					'id' => array(
						'sanitize_callback' => 'sanitize_text_field',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_string( $param );
						}
					),
      			)
			)
		);

		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/trends',
			[
				'methods' => WP_REST_Server::READABLE,
				'callback' => [$this, 'process_trends'],
				'permission_callback' => function($request) {
					$original_signature = $request->get_param('signature');
					$recomputed_signature = hash_hmac('sha256', $request->get_param('inputs'), SECURE_AUTH_KEY);
					$valid = $original_signature === $recomputed_signature;
					if (!$valid) {
						return new WP_Error('unauthorized', 'Signature does not match', ['status' => 403]);
					}
					return true;
				}
			]
		);
	}

	public function process_trends($request) {
		$inputs_json = $request->get_param('inputs');
		$inputs = json_decode($inputs_json, true);
		$query = $inputs['query'];
		$stats = $inputs['stats'];
		$results = $this->process_input($query, [
			'stats' => $stats,
			'trends' => [
				'compute_trends' => true,
			],
		]);
		return $results->stats->trends;
	}


	/**
	 * Process Search
	 *
	 * @since 1.0.0
	 * @since 2.3.0 Move hooks to appropriare methods. Rename from input_output.
	 */

	public function process_search( $data ) {

		$input = $data->get_query_params();

		if( isset( $input['doing_campaign'] ) )
			define( 'DOING_CAMPAIGN', true );

		do_action( 'ifound_external_crm_search_entry', $input );

		$results = $this->process_input( $input );

		$results->input_obj = $input;

		ob_start();

		do_action( 'ifound_price_stats', $results );
		do_action( 'ifound_display_results', $results, ['mls_class' => $input['mls_class']] );
		do_action( 'ifound_display_pagination', $results );

		$body = ob_get_clean();

		return array(
			'body'  	=> $body,
			'map_data'	=> apply_filters( 'ifound_map_data', $results )
		);

	}

	/**
	 * Get Email Template
	 *
	 * This gets the email template for the email editor. It is requested  by: @see email-editor.js
	 *
	 * @since 1.0.0
	 *
	 * @param int $data An int value for the email template, a custom post ID.
	 */

	public function get_email_template( $data ) {

		$input = $data['input'];

		$post_id = intval( $input );

		if( is_int( $post_id ) ) {

			$the_post = get_post( $post_id );
			$subject  = get_post_meta( $post_id, 'subject', true );

			$array = array(
				'content'  	=> wpautop( html_entity_decode( $the_post->post_content, ENT_QUOTES ), true ),
				'subject'	=> html_entity_decode( $subject, ENT_QUOTES )
			);

			return $array;

		}

	}

	/**
	 * Shortcode Tasks
	 *
	 * This repopulates the shortcoe dashboard when a shortcode is highlighted and the form is popped open.
	 *
	 * @since 1.0.0
	 *
	 * @param int $data An int value for the meta data of a highlighted shortcode.
	 */

	public function shortcode_tasks( $data ) {

		$input = $this->get_meta_by_id( $data['meta_id'] );

		define( 'iFOUND_ADMIN_AJAX', true );

		if ($data['type'] === 'stats') {
			return apply_filters('ifound_search_stats_form', $input);
		}
		$extra = [];

		ob_start();

		if( $data['type'] == 'dynamic' ) {

			do_action( 'ifound_dynamic_form', $input['query'] );
			$extra['extra_map_data'] = $input['extra_map_data'];

		} elseif( $data['type'] == 'display' ) {

			do_action( 'ifound_display_options_form', $input );

		} elseif( $data['type'] == 'backup' ) {

			if( ! empty( $input['backup_query'] ) ) {

				do_action( 'ifound_dynamic_form', $input['backup_query'] );

			}

		} elseif ($data['type'] === 'stats') {
			do_action('ifound_display_stats_form', $input);
		}

		$body = ob_get_clean();

		return array(
			'body'  	=> $body,
			'map_data'	=> apply_filters( 'ifound_map_data', $input['query'], $extra )
		);

	}

	/**
	 * Criteria on MLS Class Change
	 *
	 * Get the mappings for a new mls class.
	 *
	 * @since 1.2.17
	 */

	public function criteria_on_mls_class_change( $data ) {

		$type = false;

		if ($data['id'] == 'mls_class_select_campaign-builder') {
			$type = 'ifound_more_campaign_bulder_criteria';
		} else if (in_array($data['id'], ['mls_class_select', 'mls_class_select_admin'], true)) {
			$type = 'ifound_more_adv_search_criteria';
		}

		$css_class = null;
		if (isset($_GET['more_criteria_class'])) {
			$css_class = $_GET['more_criteria_class'];
		}
		else if ($data['id'] === 'mls_class_select') {
			$css_class = 'more-filters-';
		}

		ob_start();

		do_action( 'ifound_search_criteria', $type, $css_class );

		return ob_get_clean();

	}

}
