/*
 * Display the prompt if Wise Agent API key isn't present
 * But a user hovered on the Sync Wise Agent button
 */

jQuery(document).ready(function($) {
	var currUrl = window.location.href.split('/').pop();

	if(currUrl !== 'edit.php?post_type=contacts' && currUrl !== 'admin.php?page=campaign-builder') {
		$('#wa-sync').remove();
		return;
	} else if(currUrl.includes('admin.php?page=campaign-builder')) {
		var allButtons = $('.wa-sync-button');
		if(allButtons.length > 1) $(allButtons).first().remove();
	}

	$('#wa-sync').attr('disabled', true);
	$('#wa-sync').hover(showPopup, killPopup);

	function showPopup() {
		if($('.wa-info-popup').length > 0) return;
		var popup = '<div class="wa-info-popup" style="padding: 10px 5px;border:1px solid #25a102;color:3a5795;">'
			 + 'Want this feature?<br />Please register an account with ' 
			 + '<a href="https://wiseagent.com">Wiseagent CRM</a>.<br />'
			 + 'Or just paste your Wiseagent API key in "Contact Manager=>CRM Integration"<br />'
			 + 'if you already have an account</div>';
		$('#wa-sync').after(popup);
	}

	function killPopup() {
		setTimeout(function() {
			$('.wa-info-popup').remove();
		}, 2500);
	}
});

