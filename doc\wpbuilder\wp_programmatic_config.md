[Home](Home)

[TOC]

# **Programmatically Configuring WordPress**


## **Domain Mapping**

The Domain Mapping plugin stores its settings in the root site's `_domain_mapping` table (generally `wp_domain_mapping`).  This database has a very simple structure, and includes only columns for `id`, `blog_id`, `domain`, and `active` (either 0 or 1).  The easiest way to handle adding/changing these values is probably directly through sql statements.


## **Genesis SEO**

Genesis SEO settings are defined in the blog-specific `_options` table in the row with `option_name = genesis-seo-settings`.  Data in the `option_value` field is a serialized array.

- The checkbox labeled 'add site description (tagline) to `<title>` on home page?' corresponds to the `append_description_home` key, and has a value of 0 or 1.
- The 'Homepage Document Title' text field is associated with the `home_doctitle` key.

These options can be easily updated by retrieving the array using the WP core function `get_option()` which returns an unserialized associative array, updating this array as desired, and re-writing to the database using `update_option()`.


## **Menus**

Names of menus are stored in the `_terms` table for each blog.  Menu items themselves are actually a type of post with `post_type = nav_menu_item`.

Useful functions:

- [`wp_update_nav_menu_item()`](http://wpseek.com/wp_update_nav_menu_item/)
- [`wp_get_nav_menu_items()`](http://codex.wordpress.org/Function_Reference/wp_get_nav_menu_items)

Several useful examples can be found [in this generally informative post](http://wordpress.stackexchange.com/questions/44736/programmatically-add-a-navigation-menu-and-menu-items).  Here's an example of adding a page-type nav menu item, modified from the linked thread:

		$menu_item = array(
			'menu-item-title' => 'About',
			'menu-item-object' => 'page',
			'menu-item-object-id' => get_page_by_path('about')->ID,
			'menu-item-type' => 'post_type',
			'menu-item-status' => 'publish'));
		);

		wp_update_nav_menu_item($menu_id, 0, $menu_item);

Note that the second argument to `wp_update_nav_menu_item()` above - is the database ID of the nav menu item in the `_posts` table.  If 0 is given, a new item is created; if a number is provided, the given menu item is updated.

Here's an example from the same thread of creating a new nav menu and adding an item to it:

		if (!$menu_exists) {
			$menu_id = wp_create_nav_menu($menuname);

			wp_update_nav_menu_item($menu_id, 0, array(
				'menu-item-title' =>  __('Home'),
				'menu-item-classes' => 'home',
				'menu-item-url' => home_url( '/' ), 
				'menu-item-status' => 'publish'));
		}


## **Pages and Posts**

Pages and Posts are both stored in the `_posts` table.  Pages have `post_type = page`.  Both can be retrieved with [`get_post()`](http://codex.wordpress.org/Function_Reference/get_post).  This function can return a [`WP_Post`](http://codex.wordpress.org/Class_Reference/WP_Post) object or an array.  Post/page contents can be accessed through the `post_content` property / index.

A full list of post-related functions can be found [here](http://codex.wordpress.org/Function_Reference#Post.2C_Custom_Post_Type.2C_Page.2C_Attachment_and_Bookmarks_Functions).  Posts can be updated using [`wp_update_post()`](http://codex.wordpress.org/Function_Reference/wp_update_post)

**NOTE:** If you look at the posts in the database, there will be several revisions of each post stored.  For example, you might have post IDs 83, 1053, 1064, and 1073, all of which represent different revisions of a particular post.  To programmatically edit a post in this situation, edit the FIRST post - post ID 83 in this case.  Editing any of the others will not show any effect when viewing the site.


## **Site Name and Tagline / Description**

These are stored as options in a site's `_options` table, under `blogname` and `blogdescription`.  Like any other option, they can be read with `get_option()` and set with `update_option()`.


## **Users**

A full list of user functions can be found [in the codex](http://codex.wordpress.org/Function_Reference#User_and_Author_Functions).  The following are the ones most relevant to our needs:

- [`wp_create_user()`](http://codex.wordpress.org/Function_Reference/wp_create_user)
- [`add_role()`](http://codex.wordpress.org/Function_Reference/add_role)

Here's an example of the creation of a new admin user, from a [stackoverflow question](http://stackoverflow.com/questions/17308808/create-an-admin-user-programmatically-in-wordpress):

		function add_user() {
				$username = 'username123';
				$password = 'pasword123';
				$email = '<EMAIL>';

				// Create the new user
				$user_id = wp_create_user( $username, $password, $email );

				// Get current user object
				$user = get_user_by( 'id', $user_id );

				// Remove role
				$user->remove_role( 'subscriber' );

				// Add role
				$user->add_role( 'administrator' );
			}


## **Widgets**

Widgets in WordPress are an extension of the `WP_Widget` class, defined in `wp-includes/widget.php`.  The widget handling code appears to be housed in the same file.  

Widget settings may be updated programmatically using the `WP_Widget->update($new_instance, $old_instance)` method.

Widget instances are stored in the site-specific `_options` tables, e.g., `wp_13_options`, as serialized data.  Rows containing widget data appear to start with `widget_`.  This data can be accessed and unserialized in php using the `unserialize()` global function, or can be accessed using the core WP function `get_option`, e.g., `get_option('widget_social-widget')`.  The resulting indexed array contains widget instances, but note that their indeces in the array don't necessarily appear to be contiguous.

Just as `get_option()` can be used to retrieve an array of widget instances, [`update_option()`](http://codex.wordpress.org/Function_Reference/update_option) or [`add_option()`](http://codex.wordpress.org/Function_Reference/add_option) can be used to store an array of widget instances in the database.  (Note: the difference between these two functions is that `add_option()` performs a check to see if an option already exists, and will do nothing if it does; `update_option()` does not perform this check, and will overwrite any existing rows in the database.)

### Community Widget

In the iFoundAgent.com sites, the community thumbnails shown on the homepage are all part of a single text widget, which is basically used as a custom HTML container.  This widget has the title `'Communities'`.  The `text` key of the instance is a series of `div` html elements:

		<div class="communities-boxes"><a href="/ahwatukee-homes-for-sale/"><img src="/wp-content/uploads/sites/13/2014/01/ahwatukee.jpg" height="80" width="125" alt="Ahwatukee" /><strong>Ahwatukee</strong></a></div>

