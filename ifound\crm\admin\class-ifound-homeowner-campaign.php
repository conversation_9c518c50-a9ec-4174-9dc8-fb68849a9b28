<?php

defined('ABSPATH') or die('You do not have access!');

// This is the class for Bulk Campaigns. We changed the name after this class and its surrounding functionality existed.
class iFoundHomeownerCampaign {
	use UtilTrait;
	use NewHooklessTrait;

	public static $tool_name = 'Bulk Polygon Automator';

	public static $draft_bulk_campaigns_page_slug = 'ifound_draft_bulk_campaigns_page';
	// There is the feature called bulk campaigns, which is controlled by the ifoundadmin website . Then there is this
	// additional level of access. Its name is not great; it really means if the user can access the Bulk Campaign
	// Automator tool.
	public static $manage_bulk_campaigns_cap_name = 'ifound_manage_bulk_campaigns';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			if (is_admin()) {
				add_action('admin_notices', array($this, 'my_bulk_action_admin_notice'));
				add_action('admin_menu', [$this, 'admin_menu_hook']);

				foreach (iFoundJointContact::$all_contact_post_types as $type) {
					add_filter("handle_bulk_actions-edit-$type", array($this, 'handle_bulk_actions_edit_contacts'), 10, 3);
				}
				// This is my attempt to highlight this page's parent menu item, even though this page is hidden from the menu.
				// It does highlight the 'Contacts' submenu, but the Contact Manager menu doesn't show its submenus, so it's kind of worthless.
				// I got it from https://wordpress.stackexchange.com/a/203156/27896
				add_filter('submenu_file', function ($submenu_file) {
					$screen = get_current_screen();
					if ($screen->id === 'admin_page_create_homeowner_campaign') {
						$submenu_file = apply_filters('ifound_crm_menu', null);
					}
					return $submenu_file;
				});
				add_action('admin_post_ifound_create_homeowner_campaign', array($this, 'admin_post_ifound_create_homeowner_campaign'));
				add_action('ifound_create_homeowner_campaign_page', array($this, 'create_homeowner_campaign_page'));
			}

		}

		// Reminder: originally this was inside the is_admin() check above, and also in class-ifound-features.php, it
		// was only loading this file if is_admin() was true. But the problem was that action scheduler was failing with
		// this message: "action failed via WP Cron: Scheduled action for ifound_run_homeowner_campaign will not be
		// executed as no callbacks are registered."
		add_action('ifound_run_homeowner_campaign', [$this, 'run_campaign_now']);

		add_action('ifound_geocode_campaigns', [$this, 'geocode_campaigns']);
	}

	public function admin_menu_hook() {
		add_submenu_page(
			iFoundCrm::new_hookless()->crm_menu(),
			__(iFoundHomeownerCampaign::$tool_name, 'ifound'),
			__(iFoundHomeownerCampaign::$tool_name, 'ifound'),
			'crm_import',
			static::$draft_bulk_campaigns_page_slug,
			[$this, 'draft_bulk_campaigns_page']
		);
	}

	public function draft_bulk_campaigns_page() {
		if (!apply_filters('ifound_has_feature', 'bulk-campaigns')) {
			do_action('ifound_warn_feature', 'bulk-campaigns');
			return;
		}
		$campaigns = get_posts([
			'author' => iFoundadmin::new_hookless()->get_this_user_id_or_primary_admin_id(),
			'post_type' => iFoundSaveThis::$the_post_type,
			'post_status' => 'draft',
			'numberposts' => -1,
			'order' => 'ASC',
		]);
		$campaigns = array_map(function($x) {
			$val = (array) $this->util()->pick($x, ['ID']);
			$val['custom_subject'] = get_post_meta($x->ID, 'custom_subject', true);
			$val['view_link'] = $this->util()->build_post_href($x->ID, 'view');
			$val['edit_link'] = $this->util()->build_post_href($x->ID, 'edit');
			$contact_id = get_post_meta($x->ID, 'contact_id', true);
			$val['contact'] = $this->util()->get_single_metas($contact_id, ['fname', 'lname']);
			$val['contact']['link'] = $this->util()->build_post_href($contact_id, 'edit');
			return $val;
		}, $campaigns);
		?>
		<h1><?= static::$tool_name ?></h1>
		<div class="ifound ifound-bulk-campaign-automator"></div>
		<script>
			jQuery(document).ready(function() {
				var target = document.querySelector('.ifound-bulk-campaign-automator');
				var campaigns = <?= json_encode($campaigns); ?>;
				var props = {
					googleMapsApiKey: '<?= iFound::new_hookless()->get_config()['ifa_google_maps_api_key'] ?>',
					campaigns: campaigns,
					subjectMapIcon: '<?= iFoundMap::new_hookless()->subject_map_icon() ?>',
					endpoint: '<?= rest_url(iFoundHomeownerCampaignsController::$endpoint_namespace
						. iFoundHomeownerCampaignsController::$endpoint_base) ?>',
				};
				window.ifound_spa.render_app('bulk_campaign_automator', target, props);
			});
		</script>
		<?php
	}

	public function geocode_campaigns($campaign_ids) {
		foreach ($campaign_ids as $save_this_id) {
			$this->util()->maybe_geocode_campaign($save_this_id);
		}
	}

	public function handle_bulk_actions_edit_contacts($redirect, $doaction, $object_ids) {
		if ($doaction === 'ifound_create_homeowner_campaign') {
			$redirect = menu_page_url('create_homeowner_campaign');
			$redirect = add_query_arg('contact_ids', $object_ids, $redirect);
		}
		return $redirect;
	}

	public static function automate_campaign($save_this_id, $options) {
		$common_vals = [
			'recurring' => 'yes',
			'how_often' => $options['how_often'],
		];
		$email_vals = [];
		$sms_vals = [];
		if ($options[iFoundSharedCampaign::$DO_EMAIL_KEY]) {
			$crm = iFoundCrm::new_hookless()->crm();
			$email_vals = [
				'from_email' => $crm->from_email,
				'from_name'  => $crm->from_name,

				iFoundSharedCampaign::$DO_EMAIL_KEY => iFoundSharedCampaign::$DO_EMAIL_YES,
				iFoundSharedCampaign::$TO_EMAIL_KEY => $options[iFoundSharedCampaign::$TO_EMAIL_KEY],
				'custom_content'           => $options['custom_content'],
				'custom_subject'           => $options['custom_subject'],
				'header'                   => $options['header_template_id'],
				'signature'                => $options['signature_template_id'],
				'footer'                   => $options['footer_template_id'],
			];
		}
		if ($options[iFoundSharedCampaign::$DO_SMS_KEY]) {
			$sms_vals = [
				iFoundSharedCampaign::$DO_SMS_KEY => iFoundSharedCampaign::$DO_SMS_YES,
				iFoundSharedCampaign::$TO_SMS_KEY => $options[iFoundSharedCampaign::$TO_SMS_KEY],
				'sms_template_id'      => $options['sms_template_id'],
			];
		}
		$vals = array_merge($common_vals, $email_vals, $sms_vals);
		$pre_set_fields_for_campaign = apply_filters('ifound_sanitize', $vals);
		foreach ($pre_set_fields_for_campaign as $key => $value) {
			update_post_meta($save_this_id, $key, $value);
		}
	}

	public static function start_date($data) {

		$start_date = isset($data->start_date) ? sanitize_text_field($data->start_date) : 0;

		return (strtotime($start_date) <= self::current_time()) ? '' : $start_date;

	}

	public static function current_time($time = false) {
		$time = $time ? $time : time();
		return $time + iFound::gmt();
	}


	public function create_homeowner_campaign($contact_array, $options) {
		$contact_id = $contact_array['id'];
		$homeowner_campaign_template = get_posts([
			'post_type' => 'ifound_email',
			'name' => 'homeowner-campaign',
		])[0];
		$homeowner_campaign_template_subject = get_post_meta($homeowner_campaign_template->ID, 'subject', true);

		$options = wp_parse_args($options, [
			'campaign_title'                    => 'Monthly neighborhood market update',
			'list_status'                       => [],
			'days_back'                         => '90',
			'how_often'                         => 'Monthly',
			'subdivision'                       => [],
			iFoundEmail::$INSTANT_UPDATE        => true,
			'custom_subject'                    => $homeowner_campaign_template_subject,
			'custom_content'                    => $homeowner_campaign_template->post_content,
			iFoundSharedCampaign::$TO_EMAIL_KEY => $contact_array['email'],
			iFoundSharedCampaign::$TO_SMS_KEY   => $contact_array['mphone'],
		]);
		define('iFOUND_CONTACT_ID', $contact_id);

		$save_this_post = array(
			'post_title' => $options['campaign_title'],
			'post_status' => $options['post_status'],
			'post_type' => iFoundSaveThis::$the_post_type,
			'post_author' => iFoundadmin::new_hookless()->get_this_user_id_or_primary_admin_id(),
		);

		$save_this_id = wp_insert_post($save_this_post);

		$input = new stdClass();
		$input->params = [
			'mls_class' => 'Residential',
			'sort' => 'latest_listing',
		];
		$includes_closed_status = (function() use ($options) {
			$lowercase_statuses_selected = array_map(function($x) {
				return strtolower($x);
			}, $options['list_status'] ?? []);
			return $this->util()->in_array_any(['closed', 'sold'], $lowercase_statuses_selected);
		})();
		if ($includes_closed_status) {
			$input->params['days_back'] = $options['days_back'];
		}
		if (isset($options['radius'])) {
			$input->params['nearby'] = [
				'radius' => $options['radius'],
			];
		}
		$limiters = ['list_status', 'subdivision'];
		foreach ($limiters as $limiter) {
			if ($options[$limiter]) {
				$input->params[$limiter] = $options[$limiter];
			}
		}

		$params = $input->params;

		add_post_meta($save_this_id, 'params', $params, true);
		add_post_meta($save_this_id, 'contact_id', $contact_id, true);
		// This meta value doesn't do anything yet. But I want to keep track of which campaigns are bulk campaigns, so
		// we can watch their behavior over time and see if it differs from non-bulk campaigns.
		update_post_meta($save_this_id, 'ifound_is_bulk_campaign', 'yes');

		$terms = ['Homeowner Campaign', 'Market Update'];
		if ($options[iFoundEmail::$INSTANT_UPDATE]) {
			$terms[] = 'Instant Update';
		}
		if ($options[iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED]) {
			$terms[] = 'Instant Update Recently Closed';
		}
		wp_set_object_terms($save_this_id, $terms, iFoundSaveThis::$the_taxonomy, true);

		self::automate_campaign($save_this_id, $options);

		return $save_this_id;
	}

	private function create_homeowner_campaign_page_done() {
		$crm_page = iFoundCrm::new_hookless()->crm_menu();
		?>
		<div>Done creating bulk search campaigns.</div>
		<div>
			Return to the <a href="<?= $crm_page ?>">contacts</a> page.
		</div>
		<?php
	}

	private function create_homeowner_campaign_page_start() {
		if (!apply_filters('ifound_has_feature', 'bulk-campaigns')) {
			do_action('ifound_warn_feature', 'bulk-campaigns');
			return;
		}

		wp_register_script('ifound_create_homeowner_campaign_js', plugins_url('js/create-homeowner-campaign.js', __FILE__), array(), iFOUND_PLUGIN_VERSION);

		$homeowner_campaign_template = get_posts([
			'post_type' => 'ifound_email',
			'name'      => 'homeowner-campaign',
		])[0];
		$sms_templates = iFoundSms::new_hookless()->get_sample_campaign_templates();
		wp_localize_script('ifound_create_homeowner_campaign_js', 'ifound_create_homeowner_campaign_js', [
			'homeownerCampaignTemplateId' => $homeowner_campaign_template->ID,
			'sms_campaign_templates'      => $sms_templates,
			'has_sms_feature'             => apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY),
		]);
		wp_enqueue_script('ifound_create_homeowner_campaign_js');
		wp_enqueue_script( 'email_editor_js' );
		$lookups = apply_filters('ifound_lookups_to_criteria', null);
		$contacts = apply_filters('ifound_get_contacts_from_request', $_GET['contact_ids']);
		$contacts = array_map(function($contact) {
			return array_merge($contact, [
				'may_sms' => iFoundSms::new_hookless()->may_send_bulk_sms($contact['id']),
			]);
		}, $contacts);
		$num_contacts_missing_address = array_reduce($contacts, function($carry, $contact) {
			$has_address = strlen(trim($contact['address'])) === 0;
			if ($has_address) {
				$carry++;
			}
			return $carry;
		}, 0);
		$search_settings_link = menu_page_url('ifound_search_settings', false);
		$will_include_contingent_phrase = get_option('ifound_search_settings')['hide_contingent']
			? 'would not be'
			: 'would be';
		$crm = iFoundCrm::new_hookless()->crm();
		?>
		<form id="create-homeowner-campaign-form" action="<?= admin_url('admin-post.php') ?>" method="POST">
			<div>You are creating homeowner campaigns for the following <?= count($contacts) ?> contacts.</div>
			<div>There are <?= $num_contacts_missing_address ?> contacts without an address.</div>
			<div class="contacts">
				<table class="striped" style="border: 1px silver solid;">
					<thead>
					<tr>
						<th class="sticky-th"><input type="checkbox" class="contacts_check_uncheck_all" checked></th>
						<th class="sticky-th">Name</th>
						<th class="sticky-th">Email</th>
						<th class="sticky-th">Address</th>
						<th class="sticky-th">May send text</th>
					</tr>
					</thead>
					<tbody>
					<?php foreach ($contacts as $contact) : ?>
						<tr>
							<td style="text-align:center;">
								<input type="checkbox" class="include_contact_check_uncheck" name="contact_ids[]" value="<?= $contact['id']?>" checked>
							</td>
							<td><a href="<?= $this->util()->build_post_href($contact['id'], 'edit') ?>"><?= $contact['name'] ?></a></td>
							<td><?= $contact['email'] ?></td>
							<td><?= $contact['address'] ?></td>
							<td style="text-align:center;">
								<? if ($contact['may_sms']): ?>
									<i class="fa fa-check"></i>
								<? else: ?>
									<span style="color:red;"><i class="fa fa-ban"></i></span>
								<? endif ?>
							</td>
						</tr>
					<?php endforeach ?>
					</tbody>
				</table>
			</div>
			<script>
				jQuery(document).ready($ => {
					window.ifound_checkUncheckAll($('.contacts_check_uncheck_all'), $('.include_contact_check_uncheck'));
				});
			</script>

			<input type="hidden" name="action" value="ifound_create_homeowner_campaign">
			<? wp_nonce_field('crm_action', 'crm_nonce_field'); ?>
			<table class="form-table">
				<tbody>
				<tr>
					<td class="label">How/when to send</td>
					<td>
						<table>
							<tbody>
							<tr>
								<td style="padding-right: 50px; vertical-align: top;">
									<input id="post_status_draft" type="radio" name="post_status" value="draft" checked>
								</td>
								<td>
									<div><label for="post_status_draft"><em>Using the polygon automator, send
												later</em></label></div>
									You'll use the polygon automator in the next step
								</td>
							</tr>
							<tr>
								<td style="padding-right: 50px; vertical-align: top;">
									<input id="post_status_publish" type="radio" name="post_status" value="publish">
								</td>
								<td>
									<div class="owl">
										<div><label for="post_status_publish"><em>Using radius and/or subdivision, send
													now</em></label></div>
										<div style="margin-top: 20px;">
											<div>
												<label><input type="checkbox" name="include_radius"> Perform radius
													search</label>
											</div>
										</div>
										<div>
											<? do_action('ifound_radius_select', ['disabled' => true]) ?>
										</div>
										<div style="margin-top: 20px;">
											<div>Subdivision</div>
											<div><input type="text" name="subdivision" size="50"></div>
											<div class="help-text">Separate multiple subdivisions with a comma.
												Leave blank for none.</div>
										</div>
									</div>
								</td>
							</tr>
							</tbody>
						</table>
						<div>
						</div>
						<div>

						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Listing status</td>
					<td>
						<?php foreach ($lookups->list_status->values as $status) : ?>
							<div>
								<label>
									<input type="checkbox" name="list_status[]" value="<?= $status ?>"
										   checked="checked"> <?= $status ?>
								</label>
							</div>
						<?php endforeach ?>
						<div class="help-text">
							Selecting zero options is the same as selecting Active. Based on your
							<a href="<?= $search_settings_link?>">search settings</a>, contingent listings
							<?= $will_include_contingent_phrase ?> included.
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Days back closed</td>
					<td>
						<input type="number" name="days_back" value="90" min="0">
					</td>
				</tr>
				<tr>
					<td class="label">Frequency</td>
					<td>
						<?php do_action('ifound_how_often_select', 'Monthly') ?>
					</td>
				</tr>
				<tr>
					<td class="label">Instant update</td>
					<td>
						<div>
							<? _e( 'Send an email update instantly when:', 'ifound' ); ?>
						</div>
						<div>
							<input type="checkbox" checked="checked" name="<?= iFoundEmail::$INSTANT_UPDATE ?>"
								   id="<?= iFoundEmail::$INSTANT_UPDATE ?>" value="checked"/>
							<label for="<?= iFoundEmail::$INSTANT_UPDATE ?>">
								<? _e( 'new listings become available', 'ifound' ); ?>
							</label>
						</div>
						<div>
							<input type="checkbox" name="<?= iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED ?>"
								   id="<?= iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED ?>" value="checked" checked/>
							<label for="<?= iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED ?>">
								<? _e( 'existing listings become closed', 'ifound' ); ?>
							</label>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Campaign title</td>
					<td>
						<input type="text" required="required" name="campaign_title"
							   value="Monthly neighborhood market update near your home"
							   size="70">
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<div class="title-heading default-criteria-heading"><i class="fal fa-envelope" aria-hidden="true"></i> <? _e( 'Email', 'ifound' ); ?></div>
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" id="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" value="<?= iFoundSharedCampaign::$DO_EMAIL_YES ?>" checked>
						<label for="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>"><? _e( 'Include email', 'ifound' ); ?></label>
					</td>
				</tr>
				<tr>
					<td class="label">Email header template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'header',
								false, [], 'header_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email subject/body template</td>
					<td>
						<div class="edit-email-wrapper">
							<? do_action('ifound_email_dropdown', '', 'search-campaign', false, []); ?>
						</div>
						<div>Pick a <em>Search Campaign</em> template to default the email subject and body below, or
							write your own</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email subject</td>
					<td>
						<input type="text" id="email-subject" name="custom_subject" placeholder="Email Subject"
							   size="70" class="email-validate"/>
					</td>
				</tr>
				<tr>
					<td class="label">Email body</td>
					<td>
						<div class="ifound-wp-editor-wrapper">
						<?php
						$settings = ['textarea_name' => 'custom_content', 'wpautop' => false];
						wp_editor('', 'ifound_email_editor', $settings);
						?>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email signature template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'signature',
								$crm->signature, [], 'signature_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email footer template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'footer',
								$crm->footer, [], 'footer_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				<? if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)): ?>
					<tr>
						<td colspan="2">
							<div class="title-heading default-criteria-heading"><i class="fal fa-comments" aria-hidden="true"></i> <? _e( 'Text Messages', 'ifound' ); ?></div>
						</td>
					</tr>
					<tr>
						<td>
							<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" id="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" value="<?= iFoundSharedCampaign::$DO_SMS_YES ?>">
							<label for="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>"><? _e( 'Include text message(s)', 'ifound' ); ?></label>
						</td>
						<td>We'll use the contact's mobile phone number (not spouse)</td>
					</tr>
					<tr>
						<td class="label">Template</td>
						<td>
							<select name="sms_template_id" id="sms_template_id" class="mphone-validate">
								<? foreach ($sms_templates as $sms_template): ?>
									<option value="<?= $sms_template['id'] ?>"><?= $sms_template['label'] ?></option>
								<? endforeach ?>
							</select>
							<div class="sms_template_body_wrapper mphone-validate">
								Message: <span class="sms_template_body">
												<?= $sms_templates[0]['body'] ?>
											</span>
							</div>
						</td>
					</tr>
				<? endif ?>
				<tr>
					<td colspan="2">
						<div class="title-heading default-criteria-heading"><i class="fal fa-paper-plane" aria-hidden="true"></i> <? _e( 'Send', 'ifound' ); ?></div>
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<button type="submit" class="button button-primary" value="create_homeowner_campaign">
							Send campaigns now
						</button>
					</td>
				</tr>
				</tbody>
			</table>
		</form>
		<?php
	}

	public function create_homeowner_campaign_page() {
		wp_register_style('ifound_create_homeowner_campaign_css', plugins_url('css/create-homeowner-campaign.css', __FILE__), array(), iFOUND_PLUGIN_VERSION);
		wp_enqueue_style('ifound_create_homeowner_campaign_css');
		?>
		<div class="ifound_wrap create-homeowner-campaign-page">
			<h1>Create Bulk Search Campaigns</h1>
			<?
			if ($_GET['page'] === 'create_homeowner_campaign' && $_GET['done'] === 'true') {
				$this->create_homeowner_campaign_page_done();
			} else {
				$this->create_homeowner_campaign_page_start();
			} ?>
		</div>
		<?php
	}

	// This is a hook of type admin_post_{$action}, https://developer.wordpress.org/reference/hooks/admin_post_action/
	public function admin_post_ifound_create_homeowner_campaign() {
		$contacts = apply_filters('ifound_get_contacts_from_request', $_POST['contact_ids']);
		$campaign_ids = [];
		$post_status = $_POST['post_status'];
		foreach ($contacts as $contact) {
			$subdivision = $this->util()->split_on_comma($_POST['subdivision']);
			$options = [
				'campaign_title'                             => $_POST['campaign_title'],
				'list_status'                                => $_POST['list_status'],
				'how_often'                                  => $_POST['how_often'],
				'subdivision'                                => $subdivision,
				iFoundEmail::$INSTANT_UPDATE                 =>
					$_POST[iFoundEmail::$INSTANT_UPDATE] === 'checked' ?? false,
				iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED =>
					$_POST[iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED] === 'checked' ?? false,
				'custom_subject'                             => $_POST['custom_subject'],
				'custom_content'                             => $_POST['custom_content'],
				'header_template_id'                         => $_POST['header_template_id'],
				'signature_template_id'                      => $_POST['signature_template_id'],
				'footer_template_id'                         => $_POST['footer_template_id'],
				iFoundSharedCampaign::$DO_EMAIL_KEY          => $_POST[iFoundSharedCampaign::$DO_EMAIL_KEY],
				iFoundSharedCampaign::$DO_SMS_KEY            => $_POST[iFoundSharedCampaign::$DO_SMS_KEY],
				'sms_template_id'                            => $_POST['sms_template_id'],
				'post_status'                                => $post_status,
			];
			if ($_POST['include_radius'] === 'on') {
				$options['radius'] = $_POST['radius'];
			}
			if ($_POST['days_back']) {
				$options['days_back'] = $_POST['days_back'];
			}
			$campaign_ids[] = $this->create_homeowner_campaign($contact, $options);
		}
		$redirect_url = admin_url('admin.php?page=create_homeowner_campaign');
		$redirect_url = add_query_arg('done', 'true', $redirect_url);
		if ($post_status === 'publish') {
			foreach ($campaign_ids as $campaign_id) {
				$this->run_campaign_now($campaign_id);
			}
		} else {
			$crm_page = iFoundCrm::new_hookless()->crm_menu();
			$redirect_url = admin_url($crm_page . '&page=' . static::$draft_bulk_campaigns_page_slug);
		}

		// Use the timestamp of 1 to have it happen asap.
		// Note how we wrap the campaign_ids array in an array, because we want them all passed to the callback at once.
		wp_schedule_single_event(time() + 1, 'ifound_geocode_campaigns', [$campaign_ids]);
		wp_redirect($redirect_url);
	}

	public function my_bulk_action_admin_notice() {
		if (!($_GET['page'] === 'create_homeowner_campaign' && $_GET['done'] === 'true')) {
			return;
		}

		?>
		<div class="notice notice-success is-dismissible pad-me">Successfully created homeowner campaigns</div>
		<?php
	}

	// Converts the campaign from what we consider "draft state" to "published".
	public function publish($post_id, $data = []) {
		// These aren't optional/defaults. I just figured it's an easy way to show it in an organized way to the
		// developer, and allow it to be changed/expanded more easily in the future.
		$data = wp_parse_args($data, [
			'is_publish_scheduled' => true,
			// Ok, technically start_date and time_of_day are optional if is_publish_scheduled is false.
			'start_date' => '',
			'time_of_day' => '',
			'polygons' => [],
		]);
		$contact_id = get_post_meta($post_id, 'contact_id', true );

		$params = get_post_meta($post_id, 'params', true );
		$params['polygons'] = $data['polygons'];
		update_post_meta($post_id, 'params', $params);
		$ifound_geo = get_post_meta($contact_id, 'ifound_geo', true);
		// Add a 'dropped pin' if we have the contact location.
		if (iFoundGeo::new_hookless()->has_lat_lng($ifound_geo)) {
			$lat_lng = iFoundGeo::new_hookless()->get_lat_lng($ifound_geo);
			$extra_map_data = [
				'dropped_pins' => [
					[
						'position' => $lat_lng,
					],
				],
			];
		}
		update_post_meta($post_id, 'extra_map_data', $extra_map_data);
		$post = get_post($post_id);
		$post->post_status = 'publish';
		wp_update_post($post);
		if ($data['is_publish_scheduled']) {
			update_post_meta($post_id, 'start_date', $data['start_date']);
			update_post_meta($post_id, 'time_of_day', $data['time_of_day']);
			$next_time = $data['start_date'] . ' ' . $data['time_of_day'];
			update_post_meta($post_id, 'next_time', $next_time);
			do_action('ifound_update_campaign_status', $post_id, 'active');
			do_action('ifound_activity_log', $contact_id, 'Save Campaign', $post->post_title);
		} else {
			do_action('ifound_activity_log', $contact_id, 'Save Campaign', $post->post_title);
			as_enqueue_async_action('ifound_run_homeowner_campaign', [$post_id], 'ifound');
		}
	}

	public function run_campaign_now($post_id) {
		// Send now and set next_date for the next one.

		iFoundSaveThis::new_hookless()->send_campaign_now($post_id);

		$next_time_obj = new stdClass();
		$next_time_obj->how_often = get_post_meta($post_id, 'how_often', true);
		$next_time_obj->start_date = (new DateTime('now'))->format('Y-m-d');
		$next_time_obj->time_of_day = 'now';
		update_post_meta($post_id, 'start_date', $next_time_obj->start_date);
		update_post_meta($post_id, 'time_of_day', $next_time_obj->time_of_day);
		$next_time = apply_filters('ifound_next_time', $next_time_obj);
		update_post_meta($post_id, 'next_time', $next_time);
		do_action('ifound_update_campaign_status', $post_id, 'active');
	}
}
