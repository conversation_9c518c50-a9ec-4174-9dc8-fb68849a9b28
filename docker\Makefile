.PHONY: start setup php base jessie wordpress profoundwp images idx newidx solr build idx_db ../idx/config/local.ini
.PHONY: sync-% crmls-sync pull-% create-db pull-% pull-db reset-db-container

SRV = <EMAIL>
RSYNC = rsync -avz
ADDHOST = grep -s hostname /etc/hosts || echo 127.0.0.1 hostname | sudo tee -a /etc/hosts
USER_ID = $(shell id -u)
PROFOUNDIDX_DIR = $(shell cd .. ; pwd)

DEVTLD = test

# profoundwp files
SITE = profoundwp
INSTALL_FILES = php/wordpress/$(SITE)/install_files

MAKEFLAGS=--no-print-directory

# run this every time you want to start docker
# remove .pid files in case previous rails container was
# shut down incorrectly
start: 
	find ../idx2 -name "*.pid" | xargs -r rm && docker-compose up -d

# Start these up so they'll be ready for the solr and WordPress container setups
start-db:
	docker-compose up -d idx_db
	docker-compose up -d wpdb
	docker-compose up -d railsadmin

restart: stop start

# run this the first time you start docker
setup: config download pull-images build-clean

#build: images rails-admin-setup start-db idx-load props-load idx-setup new-idx-setup solr-setup pull-property_lookup_opts start pf-setup if-setup compile
build: images rails-admin-setup start-db idx-load props-load idx-setup new-idx-setup solr-setup pull-property_lookup_opts start compile

build-clean:
	make --no-print-directory build NOCACHE=--no-cache

#=======================================================================================================
# Create files for /etc/passwd and /etc/group, so that file and directory ownership matches the local user

# Get the UserID of the current user, and use that for the `ubuntu` user when building the base Docker image
UID = $(shell id -u)
GID = $(shell id -g)

ETCFILES = base/files/passwd base/files/group

etc-files: $(ETCFILES)

# We really only need the root, ubuntu, and www-data users
base/files/passwd:
	printf "root:x:0:0:root:/root:/bin/bash\nnobody:x:65534:65534:nobody:/nonexistent:/usr/sbin/nologin\nubuntu:x:$(UID):$(GID):ubuntu:/home/<USER>/bin/bash\nwww-data:x:33:33:www-data:/var/www:/usr/sbin/nologin" > $@
	
# Need group for root, staff for font-config, ngroup for `ssh`
base/files/group:
	printf "root:x:0:\nstaff:x:50:\nubuntu:x:$(GID):\nnogroup:x:65534:\nwww-data:x:33:" > $@

rm-etc-files:
	-rm $(ETCFILES)

#===============================================================================================================
# Networking / Hosts file

# add missing hostnames to /etc/hosts file
hosts:
	@echo "adding missing hostnames to end of /etc/hosts file"
	$(ADDHOST:hostname=wordpress.$(DEVTLD))
	#$(ADDHOST:hostname=profoundwp.$(DEVTLD))
	$(ADDHOST:hostname=ifoundwp.$(DEVTLD))
	$(ADDHOST:hostname=idx.$(DEVTLD))
	$(ADDHOST:hostname=idx-server.$(DEVTLD))
	$(ADDHOST:hostname=solr.$(DEVTLD))
	$(ADDHOST:hostname=idx-admin.$(DEVTLD))
	$(ADDHOST:hostname=phpmyadmin.$(DEVTLD))

#===============================================================================================================
# compile coffeescript and stylus files

compile:
	mkdir -p ../plugin/js/built
	docker-compose run --rm  -v $(PWD)/../plugin:/www/plugin -v $(PWD)/../Makefile:/www/Makefile -w /www newidx make trans-compile

#===============================================================================================================
# Config files

config: ../plugin/config.ini ../idx2/db/seeds/passwords.yml ../idx2/config/database.yml idx-config

idx-config: ../idx/includes/classes/dbconfig.ini ../idx/config/local.ini ../ifound/config.ini

../plugin/config.ini:
	cp config/plugin-config.ini ../plugin/config.ini

../ifound/config.ini:
	cp config/plugin-config.ini ../ifound/config.ini

../idx2/db/seeds/passwords.yml:
	cp config/passwords.yml ../idx2/db/seeds/passwords.yml

../idx2/config/database.yml:
	cp config/database.yml ../idx2/config/database.yml

../idx/includes/classes/dbconfig.ini:
	cp config/dbconfig.ini ../idx/includes/classes/dbconfig.ini

../idx/config/local.ini:
	scp $(SRV):config/local.ini ../idx/config/

#===============================================================================================================
# Remote DB dumps & download

REMOTEDB = ifoundid_wrdp1
BLOGID = 2

YAML = ../server/config/default.yaml

MLS = armls
RETS_CLASS = $(shell cat $(YAML) | yq -r .mls.$(MLS).classes.res )
LASTMOD = $(shell cat $(YAML) | yq -r .mls.$(MLS).fields.lastmod )
LISTINGID = $(shell cat $(YAML) | yq -r .mls.$(MLS).fields.listing_id )

PROP_TABLE = $(MLS)_property_$(RETS_CLASS)
DB = pfndidx_azdb

GMAPS_API_KEY = AIzaSyAObiUBSbbAzkmEFRxwXUQ8VvSH9awkyx8



mls-dump:
	mysql $(DB) -e " \
		DROP TABLE IF EXISTS $(PROP_TABLE)_dump; \
		CREATE TABLE $(PROP_TABLE)_dump LIKE $(PROP_TABLE); \
		INSERT INTO $(PROP_TABLE)_dump SELECT * FROM $(PROP_TABLE) ORDER BY $(LASTMOD) DESC LIMIT 100; \
		DROP TABLE IF EXISTS $(MLS)_images_dump; \
		CREATE TABLE $(MLS)_images_dump LIKE $(MLS)_images; \
		INSERT INTO $(MLS)_images_dump SELECT * FROM $(MLS)_images WHERE \`Content-ID\` IN (SELECT $(LISTINGID) from $(PROP_TABLE)_dump);"
	mysqldump $(DB) $(PROP_TABLE)_dump $(MLS)_images_dump $(MLS)_fields_$(RETS_CLASS) | gzip -c > ../database/dumps/$(MLS).sql.gz

# dump needed tables for idx database
idx-dump:
	ssh <EMAIL> "mysqldump $(DB) field_mapping mls_associations_2 access access_meta users users_clients videos registrations reseller_pricings resellers mls_systems client_crms communities active_admin_comments | gzip > ~/dev_database_dumps/dev-idx-db.sql.gz" 

# TODO: Add support for dumping other property classes: land, rentals, etc.?
rets-dump:
	ssh <EMAIL> "$(MAKE) -C idx-prod/docker mls-dump MLS=$(MLS)"

# FIXME: Split & refactor so we only pull settings for the correct setup.  
# FIXME: For the options, and anything else that is stored just in the DB, switch to using WP CLI to set these up
# dump needed tables for wordpress database
wp-dump:
	ssh <EMAIL> "mysqldump --skip-extended-insert $(REMOTEDB) wp_$(BLOGID)_options | egrep -i \"'(widget|sidebar)_|'(revslider|gse-settings|nav_menu_options|genesis-settings|ml-slider_children|template|sidebars_widgets|theme_mods|nav_menu|show_|pfmls_.+page_id|ifound|rev-slider)\" | perl -pi -e 's/VALUES \(\d+,/(option_name,option_value,autoload) VALUES (/' 2>/dev/null | sed \"s/wp_$(BLOGID)_/wp_/; s/INSERT/REPLACE/\" | gzip > ~/dev_database_dumps/$(REMOTEDB)-options.sql.gz && \
		mysqldump --skip-extended-insert $(REMOTEDB) wp_$(BLOGID)_options | egrep \"'lwa_\" | perl -pi -e 's/VALUES \(\d+,/(option_name,option_value,autoload) VALUES (/' 2>/dev/null | sed \"s/wp_$(BLOGID)_/wp_/g; s/INSERT/\nREPLACE/g; s/\. /.  /g; s/needs\./needs. /\" | gzip > ~/dev_database_dumps/dev-login-ajax.sql.gz && \
		mysqldump --skip-extended-insert $(REMOTEDB) \$$(mysql -B -A $(REMOTEDB) -e \"SHOW TABLES LIKE 'wp_$(BLOGID)_%'\" | grep -P '_(rg|gf|revslider|responsive|strong|term|post)') | sed \"s/wp_$(BLOGID)_/wp_/g\" | gzip > ~/dev_database_dumps/$(REMOTEDB)-wp.sql.gz"

dump: idx-dump wp-dump

# download necessary files for IDX database setup
idx-download:
	scp <EMAIL>:~/dev_database_dumps/dev-idx-db.sql.gz idx_db/downloads/

rets-download:
	scp <EMAIL>:~/idx-prod/database/dumps/$(MLS).sql.gz idx_db/downloads/

# geocode mls
geocode: 
	$(DCRUN) -v $(DIR_PATH)/../tools:/www/tools idx php tools/geocode.php $(MLS) $(RETS_CLASS) $(GMAPS_API_KEY)
#===============================================================================================================
# File sync / downloads

SITEDIR = /home/<USER>/sites/ifoundidx.com/

# FIXME: Should not be pulling these plugins from our server.  Use WP-CLI, or put them on S3 somewhere ...
genesis:
	$(RSYNC) $(SRV):~/sites/getprofoundidx.com/www/wp-content/themes/genesis php/wordpress/base/downloads/

gravityforms:
	$(RSYNC) $(SRV):~/sites/getprofoundidx.com/www/wp-content/plugins/gravityforms php/wordpress/base/downloads/

# ifound test site plugins 
revslider:
	$(RSYNC) $(SRV):~/sites/getprofoundidx.com/www/wp-content/plugins/revslider php/wordpress/ifoundwp/downloads/

strong-testimonials:
	$(RSYNC) $(SRV):~/sites/getprofoundidx.com/www/wp-content/plugins/strong-testimonials php/wordpress/ifoundwp/downloads/

jquery-download:
	$(RSYNC) $(SRV):$(SITEDIR)/www/jquery-ui-1.12.1 $(INSTALL_FILES)

# download necessary files for wordpress setup
wp-download:
	$(RSYNC) --delete $(SRV):~/dev_database_dumps $(INSTALL_FILES)
	# sync from houseschandleraz
	$(RSYNC) --exclude 'revslider/templates' $(SRV):$(SITEDIR)/www/wp-content/uploads/sites/$(BLOGID)/ $(INSTALL_FILES)/uploads/

	# ProFoundMLSAdminOptions from housesscottsdalearizona.com/
	ssh $(SRV) "wp --path=/home/<USER>/sites/ifoundidx.com/www --url=housesscottsdalearizona.com/ option get ProFoundMLSAdminOptions --format=json" > $(INSTALL_FILES)/dev_database_dumps/profoundmlsadminoption

plugin-download:
	$(RSYNC) -r --files-from=php/wordpress/profoundwp/plugin-files.txt $(SRV):$(SITEDIR)/www/wp-content/plugins/ $(INSTALL_FILES)/

solr-download: solr/solr-dist.tar.gz

solr/solr-dist.tar.gz:
	wget https://s3-us-west-1.amazonaws.com/pf-idx-dev/solr-dist.tar.gz -O $@

# Dump and download the settings from scottsdalelatestlistings.com, if the ifoundwp site 
if-download:
	make --no-print-directory wp-download strong-testimonials revslider SITE=ifoundwp REMOTEDB=ifoundsi_wpdb2 BLOGID=3 SITEDIR=/home/<USER>/sites/ifoundsites.com/

if-dump:
	make --no-print-directory wp-dump SITE=ifoundwp REMOTEDB=ifoundsi_wpdb2 BLOGID=3

# all downloads
download: idx-download wp-dump wp-download if-dump if-download genesis gravityforms jquery-download plugin-download solr-download

#===============================================================================================================
# Docker container setups

pfmls-idx-update:
	curl -s "http://wordpress.$(DEVTLD)/?pfmls_idx_update" > /dev/null

# generate solr cores and preform data import
solr-setup: solr-create solr-configs solr-start solr-import

solr-create:
	docker-compose up --no-start solr

solr-start:
	docker-compose up -d solr

solr-stop:
	docker-compose stop solr

# FIXME: This is really ugly, with all the volume mounts, and using the "newidx" container - use a node.js base container for this?
solr-configs:
	docker-compose run --rm -v $(PWD)/../solr/tools:/var/lib/solr/tools -v $(PWD)/../solr/lib:/var/lib/solr/lib -v $(PWD)/../solr/templates:/var/lib/solr/templates -v $(PWD)/../volumes/solr:/var/lib/solr/dist/server/solr -v $(PWD)/../node_modules:/var/lib/node_modules -v $(PWD)/../solr/configs:/var/lib/solr/configs -v $(PWD)/../server:/var/lib/server -v $(PWD)/../server/package.json:/var/lib/package.json -w /var/lib/solr newidx tools/gen-solr-configs

# Needs IDX database up & running
solr-import:
	docker-compose exec --user ubuntu solr make full-import CORE=$(MLS)_res

# FIXME: Switch to a Docker image based on ifoundagent:base, so that we can run make, reduce initial image download time, etc.
idx-load:
	docker-compose exec idx_db make load-dump

rets-load:
	docker-compose exec idx_db make load-data MLS=$(MLS)

rets-summary:
	docker-compose exec idx_db make rets-summary

props-load:
	docker-compose exec idx_db bash import-props.sh

DCRUN = docker-compose run --rm 

../node_modules:
	[ -d $@ ] || mkdir $@
	
idx-setup:
	$(DCRUN) idx php /composer.phar install

new-idx-setup: ../node_modules
	$(DCRUN) --user ubuntu newidx npm install

reso-setup: ../node_modules
	$(DCRUN) --user ubuntu reso npm install

RAILSDIRS = $(PWD)/../volumes/rubygems $(PWD)/../volumes/binstubs

$(RAILSDIRS):
	mkdir $@

rails-dirs: $(RAILSDIRS)

rails-admin-setup: rails-dirs
	$(DCRUN) --user ubuntu railsadmin make docker-setup

pf-setup:
	$(DCRUN) --user ubuntu profoundwp make pf-setup 

if-setup:
	$(DCRUN) --user ubuntu ifoundwp make if-setup 

#===============================================================================================================

# run this every time you want to stop docker
stop: 
	- docker-compose down

# stops docker, and resets everything to how it was before `download` was run for the first time
#reset: reset-volumes reset-downloads
reset: reset-volumes remove-base-images

reset-volumes:
	docker-compose down --volumes --rmi local
	rm -f rails_admin/.dbsetup-marker

reset-downloads:
	#rm -rf php/wordpress/profoundwp/install_files
	rm -f rails_admin/dev-idx-db.sql.gz

remove-base-images:
	- docker image rm ifoundagent:base ifoundagent:jessie ifoundagent:php ifoundagent:wordpress

remove-vendor-images:
	- docker image rm debian:jessie nginx:1.13.0-alpine mysql:5.7.18 phpmyadmin/phpmyadmin:latest

prune:
	docker system prune -f

clean: reset remove-vendor-images prune

#============================================================================
# Manual build of Docker images

REGISTRY = ifoundagent

# NOTE: 2020-05-11 The php image isn't building due to solr errors.
# Remove it from this list and get the pre-built image from another dev on the team.
BASEIMAGES = base jessie php wordpress idx newidx reso
NOCACHE = 

pull-images:
	docker pull debian:jessie
	docker pull nginx:1.13.0-alpine
	docker pull mysql:5.6.34
	docker pull phpmyadmin/phpmyadmin:latest

images: $(BASEIMAGES)

base: etc-files
	docker build $(NOCACHE) -t $(REGISTRY):base -f base/stretch/Dockerfile base/

jessie: etc-files
	docker build $(NOCACHE) -t $(REGISTRY):jessie -f base/jessie/Dockerfile base/

php:
	docker build $(NOCACHE) -t $(REGISTRY):php -f php/base/Dockerfile php/base

idx:
	docker build $(NOCACHE) -t $(REGISTRY):idx -f php/idx/Dockerfile php/idx

newidx:
	docker build $(NOCACHE) -t $(REGISTRY):newidx -f newidx/Dockerfile newidx

reso-s:
	docker build $(NOCACHE) -t $(REGISTRY):reso -f reso/Dockerfile reso

railsadmin:
	docker build $(NOCACHE) -t $(REGISTRY):railsadmin -f rails_admin/Dockerfile rails_admin

solr:
	docker build $(NOCACHE) -t $(REGISTRY):solr -f solr/Dockerfile solr

#build:
#	docker build -t $(REGISTRY):build build

wordpress:
	docker build $(NOCACHE) -t $(REGISTRY):wordpress -f php/wordpress/base/Dockerfile php/wordpress

#============================================================================
# Container / Database setup

# TODO: These targets aren't very DRY
DOCKER_CMD := bash
IDB_MYSQL := mysql -A -u root -proot pfndidx_azdb
shell-%:
	docker-compose exec $(SH_OPTS) $* $(DOCKER_CMD)

user-shell-%:
	make shell-$* SH_OPTS="--user ubuntu"

pf-shell:
	$(MAKE) user-shell-profoundwp

if-shell:
	$(MAKE) user-shell-ifoundwp

ng-shell:
	$(MAKE) shell-nginx DOCKER_CMD=sh

db-shell:
	$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL)"

idx-shell:
	$(MAKE) user-shell-idx

rails-shell:
	$(MAKE) user-shell-railsadmin

idb-shell: shell-idx_db
solr-shell: shell-solr
newidx-shell: shell-newidx

#============================================================================
# Logging help

if-tail:
	tail -f nginx/logs/ifoundwp*.log

idx-tail:
	tail -f nginx/logs/idx*log

idx-log:
	docker-compose logs --tail=100 -f newidx

#===============================================================================================================
# RETS sync (testing)

RCMD = sync --delete
RETSCMD = tools/rets $(MLS) $(MLS_CLASS) $(RCMD)
MLS_CLASS = res
RMAKE = make MLS=$(MLS) MLS_CLASS=$(MLS_CLASS) 
RETSUSER = ubuntu
DOPTS = 
RANDSTR = $(shell head /dev/urandom | tr -dc a-f0-9 | head -c 8)
DOCKERNET = ifoundagent_ifa

# FIXME: DNS stopped working for some reason ... switching to docker
rets-docker: 
	docker-compose -f docker-compose.yml -f rets.yml run $(DCOPTS) -u $(RETSUSER) --no-deps --rm -e NODE_ENV=$(NODE_ENV) -v $(PWD)/../lib:/www/lib2 -v $(PWD)/../tools:/www/tools -v $(PWD)/../tmp:/tmp -v $(PWD)/Makefile:/www/Makefile newidx $(DCCMD)
#	docker run --name rets-$(MLS)-$(RANDSTR) $(DOPTS) -u $(RETSUSER) --rm -e NODE_ENV=$(NODE_ENV) --net $(DOCKERNET) -v $(PWD)/../lib:/www/lib2 -v $(PWD)/../tools:/www/tools -v $(PWD)/../tmp:/tmp -v $(PWD)/Makefile:/www/Makefile -v $(PWD)/../server:/www -v $(PWD)/../node_modules:/www/node_modules docker_newidx $(DCCMD)

rets-shell:
	$(RMAKE) rets-docker DCCMD=bash DOPTS='-it'

rets-run:
	$(RMAKE) rets-docker DCCMD="$(RMAKE) rets-cmd 'RCMD=$(RCMD)'"

rets-cmd:
	$(RETSCMD) 

rets-sync:
	tools/rets $(MLS) $(MLS_CLASS) sync --incr >> log/$(MLS)-$(MLS_CLASS)-incr.log 2>&1

reso-sync:
	cd reso && node index.js -m $(MLS) -c $(MLS_CLASS) -t online --rapid --download >> logs/$(MLS)-$(MLS_CLASS)-sync.log 2>&1

reso-purge:
	cd reso && node index.js -m $(MLS) -c $(MLS_CLASS) -t online --purge --download >> logs/$(MLS)-$(MLS_CLASS)-purge.log 2>&1

# Until it is easier to run services on the server as I want, I have hijacked this.
# Yes, the name is rets, but I'm using it for RESO. Only 'REColorado - Res - Daily' uses it for now.
rets-full: reso-purge
# 	tools/rets $(MLS) $(MLS_CLASS) ids --cache >> log/$(MLS)-$(MLS_CLASS)-daily.log 2>&1
# 	tools/rets $(MLS) $(MLS_CLASS) sync --delete >> log/$(MLS)-$(MLS_CLASS)-daily.log 2>&1

#RESO sync

PROFILER = --prof
RESOCMD = cd sync && node index.js $(MLS) $(MLS_CLASS) $(RCMD)

reso-cmd:
	$(RESOCMD)

reso-docker: 
	docker-compose -f docker-compose.yml run $(DCOPTS) -u $(RETSUSER) --no-deps --rm -e NODE_ENV=$(NODE_ENV) -v /etc/hosts:/etc/hosts -v $(PWD)/../reso:/www/sync -v $(PWD)/../tmp:/tmp -v $(PWD)/Makefile:/www/Makefile reso $(DCCMD)

reso-test: 
	docker-compose -f docker-compose.yml run $(DCOPTS) -u $(RETSUSER) --no-deps --rm -e NODE_ENV=$(NODE_ENV) -v /etc/hosts:/etc/hosts -v $(PWD)/../reso:/www/sync -v $(PWD)/../tmp:/tmp -v $(PWD)/Makefile:/www/Makefile reso 
#.tests/test $(TEST_ARGS)

reso-full:
	$(RMAKE) reso-docker DCCMD="$(RMAKE) reso-cmd 'RCMD=purge'"

reso-incr:
	$(RMAKE) reso-docker DCCMD="$(RMAKE) reso-cmd 'RCMD=incr'"

reso-tunnel-host-full:
	$(RMAKE) reso-docker DCCMD="$(RMAKE) reso-cmd 'RCMD=sync purge test' RETSUSER=root"

reso-tunnel-host-incr:
	$(RMAKE) reso-docker DCCMD="$(RMAKE) reso-cmd 'RCMD=sync incr test' RETSUSER=root"

#----------------------------------------------------------------------------------------------------------

brightmls-sync:
	@$(MAKE) rets-run MLS=brightmls MLS_CLASS=res RCMD='sync --incr'
	
brightmls-sync-dryrun:
	@$(MAKE) rets-run MLS=brightmls MLS_CLASS=res RCMD='sync --dry-run'

brightmls-full:
	@$(MAKE) rets-run MLS=brightmls MLS_CLASS=res

brightmls-cache:
	@$(MAKE) rets-run MLS=brightmls MLS_CLASS=res RCMD='ids --cache'

brightmls-rentals-cache:
	@$(MAKE) rets-run MLS=brightmls MLS_CLASS=rentals RCMD='ids --cache'

brightmls-daily: brightmls-cache brightmls-full

crmls-sync:
	@$(MAKE) sync-crmls MLS_CLASS=Residential

armls-sync: sync-armls

trendmls-sync:
	@$(MAKE) sync-trendmls MLS_CLASS=RES

trendmls-sync-lots:
	@$(MAKE) sync-trendmls MLS_CLASS=LOT

RETS_OPTS:=
ifdef LIMIT
RETS_OPTS += LIMIT=$(LIMIT)
endif

ifdef CACHE
RETS_OPTS += CACHE=$(CACHE)
endif

ifdef MLS_CLASS
RETS_OPTS += MLS_CLASS=$(MLS_CLASS)
endif

ifdef SCHEMA_ONLY
RETS_OPTS += SCHEMA_ONLY=$(SCHEMA_ONLY)
endif

ifdef LOOKUPS
RETS_OPTS += LOOKUPS=$(LOOKUPS)
endif

ifdef DAYSAGO
RETS_OPTS += DAYSAGO=$(DAYSAGO)
endif

sync-%:
	@docker-compose exec idx make rets-sync-$* $(RETS_OPTS)

rets-setup-%:
	@docker-compose exec idx make rets-setup-$* $(RETS_OPTS)

solr-load-config:
	@$(MAKE) solr-create solr-configs solr-stop solr-start

drop-mls-tables:
	@$(MAKE) drop-mls-tables-$(MLS)

drop-mls-tables-trendmls:
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'drop table if exists trendmls_fields_RES; drop table if exists trendmls_fields_RNT; drop table if exists trendmls_fields_LOT;  drop table if exists trendmls_property_RES; drop table if exists trendmls_property_RNT; drop table if exists trendmls_property_LOT; drop table if exists trendmls_images;'"

drop-mls-tables-crmls:
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'drop table if exists crmls_fields_Residential; drop table if exists crmls_fields_ResidentialLease; drop table if exists crmls_fields_Land;  drop table if exists crmls_property_Residential; drop table if exists crmls_property_ResidentialLease; drop table if exists crmls_property_Land; drop table if exists crmls_images;'"

drop-mls-tables-recolorado:
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'drop table if exists recolorado_fields_RESI; drop table if exists recolorado_fields_INCOME; drop table if exists recolorado_fields_LAND;  drop table if exists recolorado_property_RESI; drop table if exists recolorado_property_INCOME; drop table if exists recolorado_property_LAND; drop table if exists recolorado_images;'"

drop-mls-tables-brightmls:
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'drop table if exists brightmls_fields_RESI; drop table if exists brightmls_fields_RLSE; drop table if exists brightmls_fields_LAND;  drop table if exists brightmls_property_RESI; drop table if exists brightmls_property_RLSE; drop table if exists brightmls_property_LAND; drop table if exists brightmls_images;'"

crmls-setup: drop-mls-tables-crmls
	@$(MAKE) rets-setup-crmls MLS_CLASS=Residential
	@$(MAKE) rets-setup-crmls MLS_CLASS=ResidentialLease LOOKUPS=0
	@$(MAKE) rets-setup-crmls MLS_CLASS=Land LOOKUPS=0
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'alter table crmls_property_Residential add index IX_Timestamps (ListingKeyNumeric, ModificationTimestamp, PhotosChangeTimestamp); alter table crmls_property_ResidentialLease add index IX_Timestamps (ListingKeyNumeric, ModificationTimestamp, PhotosChangeTimestamp); alter table crmls_property_Land add index IX_Timestamps (ListingKeyNumeric, ModificationTimestamp, PhotosChangeTimestamp); alter table crmls_property_Residential add index MLS_ID (ListingID); alter table crmls_property_ResidentialLease add index MLS_ID (ListingID); alter table crmls_property_Land add index MLS_ID (ListingID); alter table crmls_images add index IX_Image (\`Content-ID\`, \`Object-ID\`); alter table crmls_property_Residential add index IX_LastMod (ModificationTimestamp); alter table crmls_property_ResidentialLease add index IX_LastMod (ModificationTimestamp); alter table crmls_property_Land add index IX_LastMod (ModificationTimestamp); alter table crmls_property_Residential add column Latitude decimal(17,12)  DEFAULT 0; alter table crmls_property_Residential add column Longitude decimal(17,12)  DEFAULT 0; alter table crmls_property_ResidentialLease add column Latitude decimal(17,12)  DEFAULT 0; alter table crmls_property_ResidentialLease add column Longitude decimal(17,12)  DEFAULT 0; alter table crmls_property_Land add column Latitude decimal(17,12)  DEFAULT 0; alter table crmls_property_Land add column Longitude decimal(17,12)  DEFAULT 0; create index IX_Geocode ON crmls_property_Residential (Latitude, Longitude); create index IX_Geocode ON crmls_property_ResidentialLease (Latitude, Longitude); create index IX_Geocode ON crmls_property_Land (Latitude, Longitude);'"
	@$(MAKE) solr-load-config
	@$(MAKE) crmls-sync LIMIT=10

trendmls-setup: drop-mls-tables-trendmls
	@$(MAKE) rets-setup-trendmls MLS_CLASS=RES
	@$(MAKE) rets-setup-trendmls MLS_CLASS=RNT LOOKUPS=0
	@$(MAKE) rets-setup-trendmls MLS_CLASS=LOT LOOKUPS=0
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'alter table trendmls_images add index IX_Image (\`Content-ID\`, \`Object-ID\`);  alter table trendmls_property_RES add index IX_Timestamps (ListingKey, ModificationTimestamp, MaxPhotoTms);  alter table trendmls_property_RES add index MLS_ID (ListingID);  alter table trendmls_property_RES add index IX_LastMod (ModificationTimestamp);  alter table trendmls_property_RNT add index MLS_ID (ListingID); alter table trendmls_property_RNT add index IX_Timestamps (ListingKey, ModificationTimestamp, MaxPhotoTms); alter table trendmls_property_RNT add index IX_LastMod (ModificationTimestamp);  alter table trendmls_property_LOT add index IX_Timestamps (ListingKey, ModificationTimestamp, MaxPhotoTms); alter table trendmls_property_LOT add index MLS_ID (ListingID); alter table trendmls_property_LOT add index IX_LastMod (ModificationTimestamp);  '"
	@$(MAKE) solr-load-config
	@$(MAKE) trendmls-sync LIMIT=10


recolorado-setup: drop-mls-tables-recolorado
	@$(MAKE) rets-setup-recolorado MLS_CLASS=RESI
	@$(MAKE) rets-setup-recolorado MLS_CLASS=INCOME LOOKUPS=0
	@$(MAKE) rets-setup-recolorado MLS_CLASS=LAND LOOKUPS=0
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'alter table recolorado_property_RESI add index IX_Timestamps (Matrix_Unique_ID, StatusChangeTimestamp, PhotoModificationTimestamp); alter table recolorado_property_INCOME add index IX_Timestamps (Matrix_Unique_ID, StatusChangeTimestamp, PhotoModificationTimestamp); alter table recolorado_property_LAND add index IX_Timestamps (Matrix_Unique_ID, StatusChangeTimestamp, PhotoModificationTimestamp); alter table recolorado_property_RESI add index MLS_ID (MLSNumber); alter table recolorado_property_INCOME add index MLS_ID (MLSNumber); alter table recolorado_property_LAND add index MLS_ID (MLSNumber); alter table recolorado_images add index IX_Image (\`Content-ID\`, \`Object-ID\`); alter table recolorado_property_RESI add index IX_LastMod (StatusChangeTimestamp); alter table recolorado_property_INCOME add index IX_LastMod (StatusChangeTimestamp); alter table recolorado_property_LAND add index IX_LastMod (StatusChangeTimestamp); create index IX_Geocode ON recolorado_property_RESI (Latitude, Longitude); create index IX_Geocode ON recolorado_property_INCOME (Latitude, Longitude); create index IX_Geocode ON recolorado_property_LAND (Latitude, Longitude);'"
	@$(MAKE) solr-load-config
	@$(MAKE) recolorado-sync LIMIT=10

brightmls-setup: drop-mls-tables-brightmls
	@$(MAKE) rets-setup-brightmls MLS_CLASS=RESI
	@$(MAKE) rets-setup-brightmls MLS_CLASS=RLSE LOOKUPS=0
	@$(MAKE) rets-setup-brightmls MLS_CLASS=LAND LOOKUPS=0
	@$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'alter table brightmls_property_RESI add index IX_Timestamps (StatusChangeTimestamp); alter table brightmls_property_RLSE add index IX_Timestamps (StatusChangeTimestamp); alter table brightmls_property_LAND add index IX_Timestamps (StatusChangeTimestamp); alter table brightmls_property_RESI add index MLS_ID (ListingId); alter table brightmls_property_RLSE add index MLS_ID (ListingId); alter table brightmls_property_LAND add index MLS_ID (ListingId); alter table brightmls_images add index IX_Image (\`Content-ID\`, \`Object-ID\`); alter table brightmls_property_RESI add index IX_LastMod (StatusChangeTimestamp); alter table brightmls_property_RLSE add index IX_LastMod (StatusChangeTimestamp); alter table brightmls_property_LAND add index IX_LastMod (StatusChangeTimestamp); create index IX_Geocode ON brightmls_property_RESI (Latitude, Longitude); create index IX_Geocode ON brightmls_property_RLSE (Latitude, Longitude); create index IX_Geocode ON brightmls_property_LAND (Latitude, Longitude);'"
	@$(MAKE) solr-load-config
	@$(MAKE) brightmls-sync LIMIT=10

# Copied from the root Makefile
TABLE =
SQLFNAME = $(DB)-$(TABLE).sql
FNAME = $(SQLFNAME).gz

DBOPTS = --user=$(DBUSER) --password='$(DBPASS)' --host=$(DBHOST)

pull-db:
	ssh $(SRV) "mysqldump $(DB) $(TABLE) | gzip -c > ~/dev_database_dumps/$(FNAME)"
	scp $(SRV):~/dev_database_dumps/$(FNAME) idx_db/downloads
	$(MAKE) shell-idx_db DOCKER_CMD="gunzip /tmp/downloads/$(FNAME)"
	$(MAKE) shell-idx_db DOCKER_CMD="$(IDB_MYSQL) -e 'source /tmp/downloads/$(SQLFNAME)'"
	$(MAKE) shell-idx_db DOCKER_CMD="gzip /tmp/downloads/$(SQLFNAME)"

pull-%: create-db
	@$(MAKE) TABLE=$* pull-db

#pull-mapping: pull-field_mapping

#reset-db-container:
#	docker-compose rm -f idx_db
#	docker volume rm docker_idx_data
#	docker-compose up idx_db
