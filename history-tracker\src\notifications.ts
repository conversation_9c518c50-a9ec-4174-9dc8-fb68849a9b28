import sgMail from '@sendgrid/mail'
import Twi<PERSON> from 'twilio'
import config from './config.js'

export interface NotificationError {
	message: string
	stack?: string
	filename?: string
	details?: any
}

let twilioClient: Twilio | null = null
let isDevMode = false

export function setDevMode(devMode: boolean): void {
	isDevMode = devMode
}

function getTwilioClient(): Twilio {
	if (!twilioClient) {
		twilioClient = new Twilio(config.twilio.apiKey, config.twilio.apiSecret, { 
			accountSid: config.twilio.accountSid 
		})
	}
	return twilioClient
}

export async function sendErrorEmail(error: NotificationError): Promise<void> {
	if (isDevMode) {
		console.log('\n📧 [DEV MODE] Would send error email:')
		console.log(`Subject: History Tracker Error${error.filename ? ` - ${error.filename}` : ''}`)
		console.log(`To: ${config.adminEmails?.join(', ') || 'admin emails'}`)
		console.log(`Message: ${error.message}`)
		if (error.filename) console.log(`File: ${error.filename}`)
		if (error.details) console.log(`Details: ${JSON.stringify(error.details, null, 2)}`)
		if (error.stack) console.log(`Stack: ${error.stack}`)
		console.log(`Timestamp: ${new Date().toISOString()}\n`)
		return
	}

	try {
		sgMail.setApiKey(config.sendgrid.apiKey)

		const subject = `History Tracker Error${error.filename ? ` - ${error.filename}` : ''}`
		const htmlContent = `
      <h2>History Tracker Error</h2>
      <p><strong>Error Message:</strong> ${error.message}</p>
      ${error.filename ? `<p><strong>File:</strong> ${error.filename}</p>` : ''}
      ${error.details ? `<p><strong>Details:</strong> <pre>${JSON.stringify(error.details, null, 2)}</pre></p>` : ''}
      ${error.stack ? `<p><strong>Stack Trace:</strong> <pre>${error.stack}</pre></p>` : ''}
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    `

		const msg = {
			to: config.adminEmails,
			from: {
				email: config.sendgrid.fromEmail,
				name: config.sendgrid.fromName,
			},
			subject,
			html: htmlContent,
		}

		await sgMail.sendMultiple(msg)
	} catch (emailError) {
		console.error('Failed to send error email:', emailError)
		throw emailError
	}
}

export async function sendErrorSMS(error: NotificationError): Promise<void> {
	// Create shortened SMS format: "History Tracker Error - {mlsSystem} {mlsClass} {filename}"
	let message = 'History Tracker Error'
	
	// Add MLS system and class if available from details
	if (error.details?.mlsSystem && error.details?.mlsClass) {
		message += ` - ${error.details.mlsSystem} ${error.details.mlsClass}`
		
		// Add filename if available
		let filename = error.filename
		if (!filename && error.message) {
			// Try to extract filename from error message pattern "Failed to process file {filename}: ..."
			const fileMatch = error.message.match(/Failed to process file (.+?):/)
			if (fileMatch) {
				filename = fileMatch[1]
			}
		}
		
		if (filename) {
			message += ` ${filename}`
		}
	}
	// For startup errors or other cases without MLS context, just use base message
	
	if (isDevMode) {
		console.log('\n📱 [DEV MODE] Would send SMS:')
		console.log(`To: ${config.twilio?.adminSmsNumber || 'admin SMS number'}`)
		console.log(`From: ${config.twilio?.fromNumber || 'Twilio number'}`)
		console.log(`Message: ${message}\n`)
		return
	}

	try {
		const client = getTwilioClient()

		await client.messages.create({
			body: message,
			from: config.twilio.fromNumber,
			to: config.twilio.adminSmsNumber,
		})
	} catch (smsError) {
		console.error('Failed to send error SMS:', smsError)
		throw smsError
	}
}

export async function notifyError(error: NotificationError): Promise<void> {
	const errors: Error[] = []

	try {
		await sendErrorEmail(error)
	} catch (emailError) {
		errors.push(emailError as Error)
	}

	try {
		await sendErrorSMS(error)
	} catch (smsError) {
		errors.push(smsError as Error)
	}

	if (errors.length > 0) {
		console.error('Failed to send some notifications:', errors)
		// Don't throw here - we don't want notification failures to crash the main process
	}
}
