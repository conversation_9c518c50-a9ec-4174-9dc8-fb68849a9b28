<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class MlsAssocuation extends iFoundAdmin {

	private $post_type		= 'mls_association';
	private $query_var		= 'mls_association';
	private $label_name 	= 'MLS Association';
	private	$label_names	= 'MLS Associations';
	protected	$blog_id	= 8;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	public function __construct() {
		add_action( 'init', array( $this, 'mls_association_post_type' ) );	
		add_action( 'rest_api_init', array( $this, 'mls_association_route' ) );	
		add_action( 'post_updated', array( $this, 'update_server_associations' ), 10, 3 );	
	}

	public function mls_association_post_type() {
		
		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'query_var'				=> $this->query_var,
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title', 'editor' ),
				'register_meta_box_cb'	=> array( $this, 'add_metaboxes' ),
			)
	  	);
		
	}

	// https://mls-associations.ifoundadmin.com/wp-json/ifound-admin/1.0.0/mls-association/armls/
	public function mls_association_route() {

		register_rest_route( 
			'ifound-admin/' . $this->api_version,
			'/mls-association/(?P<mls_name>\S+)/', 
			array(
				'methods'  				=> WP_REST_Server::READABLE,
				'callback' 			  	=> array( $this, 'get_mls_associations' ),
				'args' => array(
      				'mls_name' => array(
						'sanitize_callback' => 'sanitize_text_field',
        				'validate_callback' => function( $param, $request, $key ) {
        					$this->mls_name = sanitize_text_field( $param );
          					return is_string( $param );
						}
					)
      			)
			)
		);

	}

	public function add_metaboxes($post) {
		$terms = get_the_terms($post, 'content_type');
		$term_slugs = array_map(function($x) { return $x->slug; }, $terms ?: []);
		$terms_that_need_reminders = [
			'list_status',
			'prop_type',
		];
		if (!!count(array_intersect($term_slugs, $terms_that_need_reminders))) {
			add_meta_box(
				'reminder_metabox',
				__('Reminder', 'ifound'),
				function() use ($post) {
					$this->reminder_metabox($post);
				},
				$this->post_type,
				'ifound_context',
				'high'
			);
		}
	}

	public function reminder_metabox($post) {
		$terms = get_the_terms($post, 'content_type');
		$term_slugs = array_map(function($x) { return $x->slug; }, $terms);
		$content_types_with_custom_pipe_syntax = [
			'list_status',
			'prop_type',
		];
		$formatted_list = join(', ', $content_types_with_custom_pipe_syntax);
		if (!!count(array_intersect($term_slugs, $content_types_with_custom_pipe_syntax))) {
			?>
			<ul>
				<li>
					For <code><?= $formatted_list ?></code>, the format is "[how you want it worded on the website]|[actual value in the MLS]"
				</li>
			</ul>
			<?php
		}
	}

	public function get_mls_associations( $data ) {

		return array(
			'city'					=> $this->get_mls_association( 'city' ),
			'contingent'			=> $this->get_mls_association( 'contingent' ),
			'list_status'			=> $this->get_mls_association( 'list_status' ),
			'prop_type'				=> $this->get_mls_association( 'prop_type' ),
			'mls_class'				=> $this->get_mls_association( 'mls_class' ),
			'school_district'			=> $this->get_mls_association( 'school_district' ),
			'geo'					=> $this->get_mls_association( 'geo' ),
			'field_list'	=> array(
			 	'res' 				=> $this->get_mls_association( 'field_list_res' ),
				'rentals'			=> $this->get_mls_association( 'field_list_rental' ),
				'land'				=> $this->get_mls_association( 'field_list_land' )
			),
			'quick_search_fields'	=> $this->get_mls_association( 'quick_search_fields' ),
			'cmc_sort_values'		=> $this->get_mls_association( 'cmc_sort_values' ),
			'sort_options'			=> $this->get_mls_association( 'sort_options' ),
			'disclosure'			=> $this->get_mls_association( 'disclosure', false ),
			'zip'				=> $this->get_mls_association( 'zip' ),
			'aerial_sphere_cities'          => $this->get_mls_association( 'aerial_sphere_cities', false )
		);

	}

	public function get_mls_association( $content_type, $explode = true ) {

		$args = array( 
			'post_type' 		=> $this->post_type, 
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'compare'		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( $content_type )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		if ( empty( $posts[0] ) ) {

			return array();
		}

		$post = $posts[0];
		
		return $explode ? $this->explode( $post->post_content ) : preg_replace('/(\>)\s*(\<)/m', '$1$2', $post->post_content );
	}

	public function update_server_associations( $object_id, $post_after, $post_before ) {

		if( get_post_type( $object_id ) == $this->post_type ) {

			$args = array( 'fields' => 'names' );
			
			$mls_names = wp_get_object_terms( $object_id, 'mls_name', $args );

			foreach ( $mls_names as $mls_name ) {

				$this->mls_name = $mls_name;

				$associations = $this->compile();

				$associations = json_encode( $associations );

				$this->server( $associations );

			}

		}

	}

	public function compile() {

		return array(
			'contingent'		=> $this->get_mls_association( 'contingent' ),
			'list_status'		=> $this->get_mls_association( 'list_status' ),
			'prop_type'			=> $this->get_mls_association( 'prop_type' ),
			'mls_class'			=> $this->get_mls_association( 'mls_class' ),
			'field_list'	=> array(
			 	'res' 			=> $this->get_mls_association( 'field_list_res' ),
				'rentals'		=> $this->get_mls_association( 'field_list_rental' ),
				'land'			=> $this->get_mls_association( 'field_list_land' )
			)
		);

	}

	public function server( $associations ) {

		$this->conn = $this->make_conn_to_railsadmin();

		$rows = $this->conn->query(
			"
			SELECT count(*) FROM mls_associations_2 
			WHERE mls =  '" . $this->mls_name . "'
			"
		)->fetchColumn();
			    
		( $rows > 0 ) ? $this->update_server( $associations ) : $this->insert_server( $associations );

		$conn = null;

	}

	public function update_server( $associations ) {

		$sql = "
			UPDATE mls_associations_2 
			SET associations = '" . $associations . "'
			WHERE mls = '" . $this->mls_name . "'
		";

		$this->conn->query( $sql );

	}

	public function insert_server( $associations ) {

		$sql = "
			INSERT INTO mls_associations_2 
			( mls, associations )
			valueS 
			('" . $this->mls_name . "', '" . $associations . "')
		";
			    
		$this->conn->exec( $sql );

	}
	
}
