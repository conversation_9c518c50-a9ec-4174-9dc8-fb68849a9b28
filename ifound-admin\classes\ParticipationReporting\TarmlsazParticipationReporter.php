<?php

require_once(__DIR__ . '/EmailParticipationReporter.php');

class TarmlsazParticipationReporter extends EmailParticipationReporter {
	protected static $internal_mls_display_name = 'TARMLSAZ/MLSSAZ';
	protected static $to_email = '<EMAIL>';
	protected static $email_subject = 'MLSSAZ Subscriber Report from iFoundAgent';
	protected static $email_message = <<<EOT
Please see the attached for the MLSSAZ Vendor Information Request report from iFoundAgent.

This report was autogenerated.
EOT;

	protected function generate_participation_report() {
		$posts = $this->get_active_clients();
		$sort_field_name = 'Client Name';
		$items = array_map(function($post) use ($sort_field_name) {
			$client_info = get_post_meta($post->ID, 'client_info', true);
			$website = get_post_meta($post->ID, 'website', true);
			$full_name = $client_info['fname'] . ' ' . $client_info['lname'];
			return [
				$sort_field_name            => $full_name,
				'Brokerage Name'            => $client_info['broker_name'],
				// TODO: How to get this field?
				'Client Site URL Address'   => $website['domain'],
				'Data Feed Activation Date' => '',
				'Client Email Address'      => $client_info['email'],
				'Data Feed Type'            => 'IDX',
			];
		}, $posts);
		// Sort the items to make it easier to compare to the wp-admin where we can filter by mls and visually prove
		// that we have the right clients in the list.
		usort($items, function($a, $b) use ($sort_field_name) {
			return $a[$sort_field_name] <=> $b[$sort_field_name];
		});

		return $items;
	}
}
