jQuery( document ).ready( function( $ ) {

	function initMap() {
		
		function ZoomControl(controlDiv, map) {

			controlDiv.style.padding = '15px';

			var controlWrapper = document.createElement('div');
			controlWrapper.style.cursor = 'pointer';
			controlWrapper.style.textAlign = 'center';
			controlWrapper.style.width = '50px'; 
			controlWrapper.style.height = '108px';
			controlDiv.appendChild(controlWrapper);

			var zoomInButton = document.createElement('div');
			zoomInButton.style.width = '50px'; 
			zoomInButton.style.height = '50px';
			zoomInButton.style.border = "thin solid #000000";
			zoomInButton.style.backgroundColor = '#000000';
			zoomInButton.style.borderRadius = "7px";
			zoomInButton.style.marginBottom = "8px";
			zoomInButton.style.boxShadow = "0 .188em .625em #494949";
			zoomInButton.style.backgroundImage = 'url("' + cmc_admin_data.image_url + 'plus.png")';
			controlWrapper.appendChild(zoomInButton);

			var zoomOutButton = document.createElement('div');
			zoomOutButton.style.width = '50px'; 
			zoomOutButton.style.height = '50px';
			zoomOutButton.style.backgroundColor = '#000000';
			zoomOutButton.style.border = "thin solid #000000";
			zoomOutButton.style.borderRadius = "7px";
			zoomOutButton.style.padding = "4px";
			zoomOutButton.style.boxShadow = "0 .188em .625em #494949";
			zoomOutButton.style.backgroundImage = 'url("' + cmc_admin_data.image_url + 'minus.png")';
			controlWrapper.appendChild(zoomOutButton);

			google.maps.event.addDomListener(zoomInButton, 'click', function() {
				map.setZoom(map.getZoom() + 1);
			});	

			google.maps.event.addDomListener(zoomOutButton, 'click', function() {
				map.setZoom(map.getZoom() - 1);
			});  

		}
		
		function changeInputs(event) {
        	var ne = rectangle.getBounds().getNorthEast();
        	var sw = rectangle.getBounds().getSouthWest();

			$('#cmc_north').val(ne.lat());
			$('#cmc_south').val(sw.lat());
			$('#cmc_east').val(ne.lng());
			$('#cmc_west').val(sw.lng());
      }
				
				
		var styles = [
			{
				featureType: "all",
				stylers: [
					{ saturation: -80 }
				]
			},{
				featureType: "road.arterial",
				elementType: "geometry",
				stylers: [
					{ hue: "#ff9009" },
					{ saturation: 20 }
				]
			}
		];
		
		map = new google.maps.Map(document.getElementById('admin-map'), {
			center: {lat: map_settings.center_lat, lng: map_settings.center_lng},
			zoom: map_settings.zoom,
			scrollwheel: false,
			styles: styles,
			disableDefaultUI: true
		});
		
		var zoomControlDiv = document.createElement('div');
		var zoomControl = new ZoomControl(zoomControlDiv, map);
	
		zoomControlDiv.index = 1;
		map.controls[google.maps.ControlPosition.RIGHT_CENTER].push(zoomControlDiv);


		// Start Rectangle
		var rectangleBounds = {
			north: map_settings.north,
			south: map_settings.south,
			east: map_settings.east,
			west: map_settings.west
		}

		var rectangle = new google.maps.Rectangle({
			strokeColor: '#000000',
			strokeOpacity: 0.8,
			strokeWeight: 2,
			fillColor: '#000000',
			fillOpacity: 0.35,
			map: map,
			draggable: true,
			editable: true,
			bounds: rectangleBounds
		});
		
		google.maps.event.addListener(map, 'zoom_changed', function() {
			var zoom = map.getZoom();
			$('#cmc_zoom').val(zoom);
		});
		
		google.maps.event.addListener(map, 'center_changed', function() {
			var center = map.getCenter();
			$('#cmc_center_lat').val(center.lat());
			$('#cmc_center_lng').val(center.lng());
		});

		google.maps.event.addListener(rectangle, 'bounds_changed', function() {
			changeInputs(event);
		});
		
	}
	
	$.getScript( cmc_admin_data.url + cmc_admin_data.key )
		.done(function( script, textStatus ) {
			initMap();
	});
	
});