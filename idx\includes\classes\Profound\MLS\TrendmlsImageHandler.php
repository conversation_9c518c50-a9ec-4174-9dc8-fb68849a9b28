<?php

namespace Profound\MLS;

class TrendmlsImageHandler extends ImageHandler {
	static $mlsname = "trendmls";

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$img_array = array();

		$sql = "SELECT PropMediaURL, PropMediaURLThumb, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Preferred` DESC, `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		foreach ($results as $key => $result) {
			$loc = preg_replace('/^http:/', 'https:', $result["PropMediaURL"]);
			$thumb = preg_replace('/^http:/', 'https:', $result["PropMediaURLThumb"]);

			$img_array[$key]['normal_url'] = $loc;
			$img_array[$key]['thumbnail_url'] = $thumb;
			$img_array[$key]['highres_url'] = $loc;
			$img_array[$key]['description'] = $result['Content-Description'];
		}

		return $img_array;
	}
}
