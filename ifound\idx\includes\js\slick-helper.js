// This puts an overlay like "1 of 35" in the top left corner of the large image, and it updates the count as the user
// changes which slide they are on.
function addImageCountOverlayOnTopOfLargeImage() {
	function getNeededSlides(e) {
		let t = [];
		for (let n of e) !n.classList.contains("slick-cloned") && n.getAttribute("data-slick-index") && t.push(n);
		return t
	}

	function changeDisplayCount(e, t, n) {
		let l = parseInt(e.getElementsByClassName("slick-current")[0].getAttribute("data-slick-index"));
		t.innerHTML = l + 1 + " of " + n
	}

	let e = document.getElementsByClassName("details-slider");
	for (let t of e) {
		t.style.position = "relative";
		let e = getNeededSlides(t.getElementsByClassName("slick-track")[0].getElementsByClassName("slick-slide")),
			n = document.createElement("div");
		n.setAttribute("style", "position:absolute;top:1rem;left:2rem;background:rgba(40,40,40,.3);padding:3px 7px; font-size:1.5rem;color:#fff"), n.innerHTML = "1 of " + e.length;
		let l = t.querySelectorAll(".slick-prev, .slick-next, .slick-slide");
		for (let i of l) i.addEventListener("click", function () {
			changeDisplayCount(t, n, e.length)
		});
		document.addEventListener("keydown", function (l) {
			37 !== l.which && 39 !== l.which || changeDisplayCount(t, n, e.length)
		}), document.addEventListener("touchend", function (l) {
			changeDisplayCount(t, n, e.length)
		}), t.appendChild(n)
	}
}

// Slick's 'init' event will fire for each carousel. We will use the init event of the navigation carousel, so that
// the divs with class name 'slick-track' will exist for both the 'ifound-details-slider' and
// 'ifound-details-slider-nav' divs, meaning our event listeners will listen to both.
jQuery('.ifound-details-slider-nav').on('init', function(event, slick) {
	addImageCountOverlayOnTopOfLargeImage();
});
