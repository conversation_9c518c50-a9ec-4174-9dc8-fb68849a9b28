<?php

defined('ABSPATH') or die('You do not have access!');

// This is another class related to bulk campaigns. Its name is HomeownerCampaigns... for the sake of consistency with
// other, older files, because our original name for the concept was homeowner campaigns. We have since started calling
// the concept bulk campaigns.
class iFoundSmsController {
	use UtilTrait;

	public static $endpoint_namespace = 'ifound';
	public static $endpoint_base = '/sms';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('rest_api_init', [$this, 'rest_api_init_hook']);
	}

	public function rest_api_init_hook() {
		$this->register_routes();
	}

	private function register_routes() {
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/opt-out', [
			[
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => [$this, 'opt_out'],
				'permission_callback' => [$this, 'permissions_check'],
			],
		]);
	}

	public function permissions_check() {
		if (!apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)) {
			return false;
		}
		$api_settings = get_option('ifound_api_settings');
		$auth_header = $_SERVER['HTTP_AUTHENTICATION'];
		return $api_settings['api_secret'] === $auth_header;
	}

	public function opt_out(WP_REST_Request $request) {
		$params = $request->get_json_params();
		$phone_number = $params['phone_number'];
		$opt_out_type = $params['opt_out_type'];
		iFoundSms::new_hookless()->opt_out_number($phone_number, $opt_out_type);
		$data = [
			'success' => true,
		];
		return new WP_REST_Response($data, 200);
	}
}
