-- getprofo_wp1

CREATE TABLE IF NOT EXISTS wp_98_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_110_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_126_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_150_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_158_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_159_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_160_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_167_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_168_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_169_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_171_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_176_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_177_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_178_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_179_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_180_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_181_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_182_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_183_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_184_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_189_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_201_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_202_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_206_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_211_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_212_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_214_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_217_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_219_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_221_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_225_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_232_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_234_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_235_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_236_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_237_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_238_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_239_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_243_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_244_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_245_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_249_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_250_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_251_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_254_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;












CREATE TABLE IF NOT EXISTS wp_98_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_110_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_126_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_150_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_158_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_159_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_160_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_167_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_168_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_169_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_171_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_176_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_177_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_178_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_179_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_180_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_181_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_182_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_183_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_184_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_189_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_201_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_202_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_206_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_207_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_211_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_213_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_215_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_217_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_219_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_221_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_224_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_225_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_234_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_236_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_237_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_239_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_245_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_246_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_250_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_251_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_253_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_254_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;







-- natseo


CREATE TABLE IF NOT EXISTS wp_27_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_33_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_34_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_39_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_40_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_53_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_59_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_60_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_69_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_70_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_71_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_72_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;














CREATE TABLE IF NOT EXISTS wp_27_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_33_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_34_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_39_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_40_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_53_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_59_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_60_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_69_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_70_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_71_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_72_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;





-- realdeal_wrdp1


CREATE TABLE IF NOT EXISTS wp_2_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_3_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_6_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_36_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_42_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_47_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_155_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_160_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_232_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;



CREATE TABLE IF NOT EXISTS wp_2_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_3_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_6_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_36_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_42_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_47_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_155_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_160_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_232_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;




-- tamsms

CREATE TABLE IF NOT EXISTS wp_12_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_18_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_19_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_21_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_22_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_23_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_25_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_27_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_28_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_29_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_30_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_32_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_33_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;



CREATE TABLE IF NOT EXISTS wp_12_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_18_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_19_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_20_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_21_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_24_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_25_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_26_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_28_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_29_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_30_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_32_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_33_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;