<?php

namespace Profound\MLS;

class MlsGridImageHandlerStrategy {
	public function getImagePathsList($listing_id, $mlsname, $db) {
		$imgtable = $mlsname . "_images";
		$img_array = array();
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Object-ID` ASC";
		$results = $db->fetchAll($sql);
		$img_array = array();
		// Here's what we're doing here. We can use Cloudfront for the original. For anything that needs Cloudinary to
		// do transformations, we'll use Cloudinary THROUGH Cloudfront.
		$cloudfront_prefix_for_original = 'https://d3qxmr0ipxcgvq.cloudfront.net/';
		$cloudfront_prefix_for_transformations = 'https://d3m7ihe4pz156o.cloudfront.net/';
		$cloudinary_path_prefix = 'image/fetch/';
		$cdn_original_prefix = $cloudfront_prefix_for_transformations . $cloudinary_path_prefix;
		$cdn_thumbnail_prefix = $cdn_original_prefix . '/w_600/';
		$mlg_prefix_to_remove = 'https://s3.amazonaws.com/mlsgrid/';
		foreach ($results as $key => $result) {
			// This replacement should leave us with a partial path like this:
			// images/REC2030736971/77a3da2b-98f2-4fb0-8e30-170036ea5903.jpeg
			$mlg_partial_path = str_replace($mlg_prefix_to_remove, '', $result['Location']);
			$high_res_loc = $cloudfront_prefix_for_original . $mlg_partial_path;
			$thumbnail_loc = $cdn_thumbnail_prefix . $result['Location'];

			$img_array[$key]['normal_url'] = $high_res_loc;
			$img_array[$key]['thumbnail_url'] = $thumbnail_loc;
			$img_array[$key]['highres_url'] = $high_res_loc;
			$img_array[$key]['description'] = $result['Content-Description'];
		}
		return $img_array;
	}
}
