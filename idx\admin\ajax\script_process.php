<?php

// Initialize variables
$file = $file = $_REQUEST['f']; //'05_prop_images.php';
$found = 0;
//$file = basename(__FILE__);
$commands = array();



// Get running processes.
exec("ps aux", $commands);

//print "<pre>"; print_r($commands); print "</pre>";

// If processes are found
if(count($commands) > 0) {
	
	foreach($commands as $command) {
		
		if(strpos($command, $file) === false) {
			// Do nothin'
		}else{
			// Let's count how many times the file is found.
			$found++;
		}
	}
}

// If the instance of the file is found more than once.
if ($found != 0) {
	echo "Currently Processing.";
	die();
}else{
	echo 'Process Complete. <a href="/admin/">Refresh Page</a> to see results.';
	die();
}

?>