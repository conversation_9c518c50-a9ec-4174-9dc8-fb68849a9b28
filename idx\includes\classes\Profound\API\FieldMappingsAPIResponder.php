<?php

// This API is responsible for returning RETS lookups and options.

namespace Profound\API;

use \FieldMappingsQuery;

class FieldMappingsAPIResponder extends APIResponder {
	protected function getDefaultAPI() {
		return API::explicit("field_mappings", "v1.01", "json");
	}

	public function computeResponseData() {
		$mls = $_REQUEST['mls'];
		$mls_class = $_REQUEST['mls_class'];
		if (!in_array($mls, \Profound\MLS\Mls::getMlsNameAbbrevs())) {
			throw new \Exception("Invalid MLS name: ($mls)");
		}
		$idx = new FieldMappingsQuery('../config.ini', array('field_mappings' => $this->api));
		return $idx->fetchFieldMappings($mls, $mls_class);
	}
}
