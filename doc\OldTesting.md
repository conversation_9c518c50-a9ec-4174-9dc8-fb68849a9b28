# Testing (Old)

There are two projects in this repository, under the directories `idx` and `plugin`. Currently, each has a separate testing sweet. In the future it would be ideal to put all tests in one place, but for now, it's fine to have them be separate, and it allows us to be sure to test them in isolation.

### PHPUnit

We use the de-facto standard [PHPUnit](http://en.wikipedia.org/wiki/PHPUnit) testing framework. You'll need to install [Composer](https://github.com/composer/composer). After that, you should only need to run `composer install --dev` in the sub-project's directory (`idx` or `plugin`) to get everything set up. Then you just run `phpunit` to run the entire suite.

Some of the tests require a test database and a configuration file. Look at e.g. `idx/tests/phpunit-example.ini` for an example configuration of connecting to a test database. You can run phpunit with the XML configuration file like this (this example's working directory is `idx`):

    $ phpunit --configuration tests/phpunit.xml

### Guard
[guard-phpunit](https://github.com/Maher4Ever/guard-phpunit) allows for even more automated watching of test / source file changes and re-running of the tests.

Its setup is more involved. Since it's a ruby gem, you'll first need ruby. I recommend using [rbenv](https://github.com/sstephenson/rbenv/) to install ruby, which will install the `gem` command-line program. Once that's done, you can run `gem install bundler` to install bundler. From there, you can run `bundle` which will install the gems listed in `Gemfile`.

After all that's done, you can run guard by typing `guard`, which will read configuration information from `Guardfile`.

### Integration testing

We don't currently have integration testing. To allow for integration testing, we'd ideally have a test web server, as well as test data, both of which would be spun up locally upon a test run.

### Plugin testing

As of this writing, plugin testing has just been looked into so it's not ready, but we're using instructions from [Unit Testing WordPress Plugins: The Right Way | CatN](http://catn.com/wordpress/unit-testing-wordpress-plugins-the-right-way).

That link describes how to set up testing and how to bootstrap php and wordpress properly to allow you to do so. You'll need to add some config, so see the `wordpress-tests_bootstrap_path` and `wordpress_plugin_dir_name` keys mentioned in the example config file `plugin/config-example.ini`.

As mentioned in the instructions from [wordpress-tests](https://github.com/nb/wordpress-tests), you'll need to set the `ABSPATH` constant in the `./unittests-config.php` file to point to your wordpress test installation.