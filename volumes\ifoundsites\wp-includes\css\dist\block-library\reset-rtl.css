html :where(.editor-styles-wrapper){
  background:#fff;
  color:initial;
  font-family:serif;
  font-size:medium;
  line-height:normal;
}
:where(.editor-styles-wrapper) .wp-align-wrapper{
  max-width:840px;
}
:where(.editor-styles-wrapper) .wp-align-wrapper.wp-align-full,:where(.editor-styles-wrapper) .wp-align-wrapper>.wp-block{
  max-width:none;
}
:where(.editor-styles-wrapper) .wp-align-wrapper.wp-align-wide{
  max-width:840px;
}
:where(.editor-styles-wrapper) a{
  transition:none;
}
:where(.editor-styles-wrapper) code,:where(.editor-styles-wrapper) kbd{
  background:inherit;
  font-family:monospace;
  font-size:inherit;
  margin:0;
  padding:0;
}
:where(.editor-styles-wrapper) p{
  font-size:revert;
  line-height:revert;
  margin:revert;
}
:where(.editor-styles-wrapper) ol,:where(.editor-styles-wrapper) ul{
  box-sizing:border-box;
  list-style-type:revert;
  margin:revert;
  padding:revert;
}
:where(.editor-styles-wrapper) ol ol,:where(.editor-styles-wrapper) ol ul,:where(.editor-styles-wrapper) ul ol,:where(.editor-styles-wrapper) ul ul{
  margin:revert;
}
:where(.editor-styles-wrapper) ol li,:where(.editor-styles-wrapper) ul li{
  margin:revert;
}
:where(.editor-styles-wrapper) ol ul,:where(.editor-styles-wrapper) ul ul{
  list-style-type:revert;
}
:where(.editor-styles-wrapper) h1,:where(.editor-styles-wrapper) h2,:where(.editor-styles-wrapper) h3,:where(.editor-styles-wrapper) h4,:where(.editor-styles-wrapper) h5,:where(.editor-styles-wrapper) h6{
  color:revert;
  font-size:revert;
  font-weight:revert;
  line-height:revert;
  margin:revert;
}
:where(.editor-styles-wrapper) select{
  -webkit-appearance:revert;
  background:revert;
  border:revert;
  border-radius:revert;
  box-shadow:revert;
  color:revert;
  cursor:revert;
  font-family:system-ui;
  font-size:revert;
  font-weight:revert;
  line-height:revert;
  margin:revert;
  max-width:revert;
  min-height:revert;
  outline:revert;
  padding:revert;
  text-shadow:revert;
  transform:revert;
  vertical-align:revert;
}
:where(.editor-styles-wrapper) select:disabled,:where(.editor-styles-wrapper) select:focus{
  background-color:revert;
  background-image:revert;
  border-color:revert;
  box-shadow:revert;
  color:revert;
  cursor:revert;
  text-shadow:revert;
  transform:revert;
}