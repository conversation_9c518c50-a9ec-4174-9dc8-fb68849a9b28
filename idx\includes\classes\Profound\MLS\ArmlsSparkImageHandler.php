<?php

namespace Profound\MLS;

class ArmlsSparkImageHandler extends ImageHandler {
	static $mlsname = "armls_spark";

	// I got this from https://www.php.net/manual/en/function.str-ends-with.php#126551
	// I took out the type declarations because I found an example where the MediaURL was null, not string.
	private function str_ends_with($haystack, $needle) {
		$needle_len = strlen($needle);
		return ($needle_len === 0 || 0 === substr_compare($haystack, $needle, - $needle_len));
	}

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Preferred` DESC, `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		$img_array = array();
		$count = 0;
		foreach ($results as $result) {
			$loc = $result["Location"];

			// In Spark, there are potentially documents in the Media, such as PDF files. Their Web API doesn't accept
			// arguments to $expand, like doing this doesn't actually filter:
			//   $expand=Media($filter=MediaCategory eq 'Photo')
			// So, filter them here until there's a built-in solution in OpenRESync.
			if (!($this->str_ends_with($loc, '.jpg') || $this->str_ends_with($loc, '.jpeg'))) {
				continue;
			}

			// All images used to be on cdn.photos.sparkplatform.com, and they have a trick where you can get different
			// sizes using -t for a thumbnail and -o for the original. But recently some images are hosted on a
			// different CDN which doesn't allow this trick. So don't use the trick in that case.
			$canUseModifierTrick = strpos($loc, 'sparkplatform') !== false;

			if ($canUseModifierTrick) {
				// In Spark, the MediaURL already ends in -o, so take it off.
				$loc = preg_replace("/-o(\.jpe?g)$/", '$1', $loc);
			}

			$img_array[$count]['normal_url'] = $loc;
			$img_array[$count]['thumbnail_url'] = $loc;
			$img_array[$count]['highres_url'] = $loc;
			if ($canUseModifierTrick) {
				$img_array[$count]['highres_url'] = preg_replace("/\.(jpe?g)$/", "-o.$1", $loc);
			}
			$img_array[$count]['description'] = $result['Content-Description'];

			$count++;
		}
		return $img_array;
	}
}
