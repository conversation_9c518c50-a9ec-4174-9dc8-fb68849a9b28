jQuery(document).ready(function($) {
	
	$( '.note-pop' ).on( 'click', function() {
		var mls_id = $( this ).attr( 'mls_id' );
		$( '#popup_' + mls_id ).slideDown( 'slow' );
	});

	$( '.notes-close' ).on( 'click', function() {
		$( this ).closest( '.ifound-note-popup' ).slideUp( 'slow' );
	});

	$( '.add-notes' ).on( 'click', function() {
		
		var wrapper = $( this ).closest( '.ifound-notes-wrapper' );

		input = new Object;
		input.text = $( wrapper ).find( '.new-note-text' ).val();
		input.post_id = $( this ).attr( 'post_id' );
		input.mls_id = $( this ).attr( 'mls_id' );

		$.ajax ( {
			url : notes.endpoint,
			type : 'post', 
			data : {
				action : 'new_note_ajax',
				input : input,
				notes_nonce : notes.nonce,
			},
			beforeSend: function() {
				$( wrapper ).find( '.new-note-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
				$( wrapper ).find('.new-note').removeClass('new-note');
			},
			success: function( response ) {
				$( wrapper ).find( '.new-note-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response.class );
				if( response.success ) {
					$( wrapper ).find( '.new-notes-catch' ).prepend( response.note );
					$( wrapper ).find( '.new-note-text' ).val( '' ).focus();
				}
			},
			dataType:'json'
		});
	})
	
});