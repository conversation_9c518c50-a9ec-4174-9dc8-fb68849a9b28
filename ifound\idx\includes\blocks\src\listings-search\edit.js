import { __ } from '@wordpress/i18n';
import { Button } from '@wordpress/components';
import './editor.scss';

export default function Edit({ attributes, className, setAttributes }) {
	function launchShortcodeCreator() {
		jQuery(window).trigger('ifound:show-shortcode-creator', [attributes.id, function(id) {
			setAttributes({ id: id.toString() });
		}]);
	}

	return (
		<div className={ className }>
			<div style={ {'text-align': 'center'} }>
				<img src="/wp-content/plugins/ifound/idx/admin/images/ifound-shortcode.png" style={{ width: '24px' }} />
				{' '}
				<strong>
					iFound Listings Search
				</strong>
			</div>
			<div>
				<Button isPrimary onClick={launchShortcodeCreator}>{attributes.id ? 'Edit' : 'Create'} Listings Search</Button>
			</div>
			{!attributes.id && <div style={{ color: '#aaa', fontSize: 'smaller' }}>Until the search is set up, this block will render blank.</div>}
		</div>
	);
}
