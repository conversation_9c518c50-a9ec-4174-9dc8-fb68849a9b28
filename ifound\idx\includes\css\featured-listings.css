@charset "UTF-8";
/* CSS Document */

/* Featured Listings */
.featured-listing{
	width: calc(33% - 10px); 
	max-width: 100%;
	min-height:360px;
	padding: 0 0 4px 0;
    margin: 10px 5px;
    display: inline-block;
    float: none;
	border:#ccc thin solid;
}

.ifound-featured-slider .featured-listing:hover{
	background:white;
}

.ifound-featured-slider h2 {
	font-size: 22px;
	padding: 10px;
	margin-bottom: 0;
	text-align: center;
	z-index:10;
}

.ifound-featured-slider h2 a {
    font-size: 22px;
}

.ifound-featured-data .price {
    font-size: 30px;
    text-align: center;
	color:#b40101;
	margin-bottom: 10px;
}

.ifound-featured-data {
	text-align: center;
}

.ifound-featured-data .beds, 
.ifound-featured-data .baths,
.ifound-featured-data .size,
.ifound-featured-data .lot {
	float: left;
    margin: 0 auto;
    padding: 5px 0;
    width: 50%;
}

.ifound-featured-data .beds:before,
.ifound-featured-data .baths:before {
	content: "\f236";
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    padding-right: 3px;
}

.ifound-featured-data .baths:before {
	content: "\f2cd";
}

.recolorado .ifound-featured-data .broker-name{
    font-size: 18px;
    font-weight: bold;
}

.ifound-featured-image a {
	display: block;
    width: 100%;
    position: relative;
    /* Why was this here? I'm removing it because it's making the virtual tour link about half height */
    /*height: 0;*/
    padding: 56.25% 0 0 0;
    overflow: hidden;
    background: #000;
}

.ifound-featured-image img {
	position: absolute;
    display: block;
    max-width: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
}

.ifound-featured-data .broker-name {
    font-size: 16px;
}

.ifound-virtual-tour {
    position: absolute;
    bottom: 0;
    left: 0;
    line-height: normal;
}

.ifound-virtual-tour .button.vt-link {
    border: none;
    border-radius: 0;
    display: block;
    letter-spacing: normal;
    font-size: 1rem;
    padding: 7px;
    color: #fff;
}

.ifound-open-house-datetime {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: #0006;
    color: #fff;
    padding: 2px 5px;
    font-size: 14px;
    font-weight: 700;
}
