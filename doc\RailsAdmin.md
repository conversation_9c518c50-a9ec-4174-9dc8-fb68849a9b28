# IDX Admin (Ruby-on-Rails version)

- Get GPG keys for RVM: `gpg --keyserver hkp://keys.gnupg.net --recv-keys 409B6B1796C275462A1703113804BB82D39DC0E3`
- Install RVM: `\curl -sSL https://get.rvm.io | bash`
- Install Ruby for the project: `rvm install ruby-2.0.0-p0`
- For any Debian based system since mid 2015, you have to use this patch:
```
wget https://github.com/ruby/ruby/commit/801e1fe46d83c856844ba18ae4751478c59af0d1.diff -O openssl.patch
rvm install --patch ./openssl.patch ruby-2.0.0-p0
```
- Instal dependencies for Qt5-dev: `sudo apt-get install qt5-default libqt5webkit5-dev gstreamer1.0-plugins-base gstreamer1.0-tools gstreamer1.0-x`. For Debian based systems before mid 2015 (like Ubuntu 14.04) it would be: `sudo apt-get install libqt4-dev libqtwebkit-dev`
- Install MySQL client dev package: `sudo apt-get install libmysqlclient-dev`
- Instal bundler: `gem install bundler`
- Run: `bundle install` or `rvm do rails-2.0.0 bundle install` if rvm is not properly configured
- Configure DB and test passwords
- Run rake db:setup
- You should either use MySQL client up to 5.6 or to upgrade mysql2 gem to 0.3.17 by editing the Gemfile.lock. Using MySQL client up to 5.6 is preferred.
- Install any Foreman implementation, like the original Foreman itself, node-foreman or shoreman.
- Run Foreman. For node-foreman, it is: `nf start`
- Since I am not using unicorn, because I want to use the development mode, I had to edit the Nginx file to proxy to the port I am using on the Rails server and to erase the `/assets` section
