import mysql from 'mysql2/promise'
import config from './config.js'

export interface Property {
	ListingId: string
	StandardStatus: string
	Price: number
	ModificationTimestamp: Date
}

export interface PropertyChanges {
	ListingId: string
	StandardStatus: string
	StandardStatus_ModificationTimestamp: Date | null
	StandardStatus_updated_at: Date | null
	Price: number
	Price_ModificationTimestamp: Date | null
	Price_updated_at: Date | null
	created_at: Date
	updated_at: Date
}

export interface PropertyHistory {
	id?: number
	ListingId: string
	StandardStatus: string
	Price: number
	ModificationTimestamp: Date
	created_at: Date
}

let connection: mysql.Connection | null = null

export async function getConnection(): Promise<mysql.Connection> {
	if (!connection) {
		// Use test database if we're in test mode
		const isTestMode = process.env.NODE_ENV === 'test' || process.env.TEST_DATABASE === 'true'
		const dbConfig = isTestMode && config.testDatabase ? config.testDatabase : config.database

		connection = await mysql.createConnection({
			host: dbConfig.host,
			port: dbConfig.port,
			user: dbConfig.user,
			password: dbConfig.password,
			database: dbConfig.database,
			dateStrings: false,
			timezone: '+00:00',
		})
	}
	return connection
}

export async function closeConnection(): Promise<void> {
	if (connection) {
		await connection.end()
		connection = null
	}
}

export async function withTransaction<T>(
	operation: (conn: mysql.Connection) => Promise<T>
): Promise<T> {
	const conn = await getConnection()
	
	await conn.beginTransaction()
	try {
		const result = await operation(conn)
		await conn.commit()
		return result
	} catch (error) {
		await conn.rollback()
		throw error
	}
}


export async function getLatestPropertyHistory(
	listingId: string,
	tableName: string,
	conn?: mysql.Connection
): Promise<PropertyHistory | null> {
	const connection = conn || await getConnection()
	const [rows] = await connection.execute(
		`SELECT * FROM ${tableName} WHERE ListingId = ? ORDER BY created_at DESC LIMIT 1`,
		[listingId]
	)

	const results = rows as any[]
	if (results.length === 0) {
		return null
	}

	const row = results[0]
	return {
		id: row.id,
		ListingId: row.ListingId,
		StandardStatus: row.StandardStatus,
		Price: parseFloat(row.Price),
		ModificationTimestamp: row.ModificationTimestamp,
		created_at: row.created_at,
	}
}

export async function insertPropertyHistory(
	data: Omit<PropertyHistory, 'id' | 'created_at'>,
	tableName: string,
	conn?: mysql.Connection
): Promise<void> {
	const connection = conn || await getConnection()
	await connection.execute(
		`INSERT INTO ${tableName} (ListingId, StandardStatus, Price, ModificationTimestamp) VALUES (?, ?, ?, ?)`,
		[data.ListingId, data.StandardStatus, data.Price, data.ModificationTimestamp]
	)
}

export async function upsertPropertyChanges(
	data: {
		ListingId: string
		StandardStatus: string
		Price: number
		StandardStatus_ModificationTimestamp?: Date | null
		StandardStatus_updated_at?: Date | null
		Price_ModificationTimestamp?: Date | null
		Price_updated_at?: Date | null
	},
	tableName: string,
	conn?: mysql.Connection
): Promise<void> {
	const connection = conn || await getConnection()

	const fields = [
		'ListingId',
		'StandardStatus',
		'Price',
		'StandardStatus_ModificationTimestamp',
		'StandardStatus_updated_at',
		'Price_ModificationTimestamp',
		'Price_updated_at',
	]

	const updateFields = fields
		.slice(1)
		.map(field => `${field} = VALUES(${field})`)
		.join(', ')

	await connection.execute(
		`INSERT INTO ${tableName} (${fields.join(', ')})
     VALUES (?, ?, ?, ?, ?, ?, ?)
     ON DUPLICATE KEY UPDATE ${updateFields}`,
		[
			data.ListingId,
			data.StandardStatus,
			data.Price,
			data.StandardStatus_ModificationTimestamp || null,
			data.StandardStatus_updated_at || null,
			data.Price_ModificationTimestamp || null,
			data.Price_updated_at || null,
		]
	)
}
