<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );
	
/**
 * iMax Class
 *
 * @since 2.6.9
 */

/**
	HTTP Headers:
		Authorization - Your API key
		Referer: The URL of the site sending the request
		User-Agent: iFoundAgent 1.0; https://ifoundagent.com

	Body:
	    New Contact: Submission
	        action: NewSubmission
			fname: string
			lname: string
			comments: string
			phone: string
			email: string
			title: string
			contact_id: int

	    Website Visit:
	        action: WebsiteVisit
	        url: string
	        type: string - page_view || detail_view || results_view
	        contact_id: int

	    Property Details View
	        action: PropertyView
	        mls_id: string
			url: string
			street_address: string
			city: string
			state: string
			contact_id: int

		Save This Search|Property|Campaign
			action: SaveSearch|SaveProperty|SaveCampaign
			data: mixed array|object
			contact_id: int
*/
 
class iFoundImax {

	/**
	 * init iFOUND_imax class.
	 *
	 * @since 2.6.9
	 */
	 
	public static function init() {
        if( get_option( 'imax_api_key' ) ) {
        	$class = __CLASS__;
        	new $class;
        }
    }
	
	/**
	 * Constructor
	 *
	 * @since 2.6.9
	 */
	 
	public function __construct() {

		// Disable for now. We don't have anyone on iMax now and aren't sure if this works anymore.
		// add_action( 'ifound_external_crm_new_submission', array( $this, 'new_submission' ), 10, 1 );
		// add_action( 'ifound_external_crm_property_view', array( $this, 'property_view' ), 10, 1 );
		// add_action( 'ifound_external_crm_page_view', array( $this, 'page_view' ), 10, 1 );
		// add_action( 'ifound_external_crm_save_this', array( $this, 'save_this' ), 10, 2 );
		
	}
	
	/**
	 * New Submission
	 *
	 * Push the contatct info to CRM. 
	 *
	 * @since 2.6.9
	 *
	 * @param object $entry The contact data from form submit.
	 */
	
	public function new_submission( $entry ) {
		
		$body = array(
			'action' 		=> 'NewSubmission',
			'fname' 		=> $entry->fname,
			'lname' 		=> $entry->lname,
			'comments' 		=> $entry->comments,
			'phone' 		=> $entry->hphone,
			'email' 		=> $entry->email,
			'tag' 			=> 'Contact from your iFoundAgent website: ' . site_url( '/' ),
			'title'			=> $entry->title,
			'contact_id'	=> $entry->contact_id
		);
	
		$this->request( $body );
		
	}
	
	/**
	 * Property View
	 *
	 * Push the property data info to CRM. 
	 *
	 * @since 2.6.9
	 *
	 * @param object $results The data for the property viewed
	 */
	
	public function property_view( $results ) {
		
		/** Check if $contact_id set. */
		if( ! defined( 'iFOUND_CONTACT_ID' ) ) return;

		$address = apply_filters( 'ifound_address', $results );
		
		$body = array(
			'action' 			=> 'PropertyView',
			'mls' 				=> $results->ListingID,
			'url' 				=> site_url( '/' . $_SERVER['REQUEST_URI'] ),
			'street_address'	=> $address,
			'city' 				=> $results->City,
			'state' 			=> $results->State,
			'contact_id'		=> iFOUND_CONTACT_ID
		);
		
		$this->request( $body );
		
	}

	/**
	 * Page View
	 *
	 * Push the property data info to CRM. 
	 *
	 * @since 2.6.9
	 *
	 * @param string $type The type of page viewed.
	 */
	
	public function page_view( $type ) {
		
		/** Check if $contact_id set. */
		if( ! defined( 'iFOUND_CONTACT_ID' ) ) return;
		
		$body = array(
			'action'		=> 'WebsiteVisit',
			'url' 			=> site_url( '/' . $_SERVER['REQUEST_URI'] ),
			'type'			=> $type,
			'contact_id'	=> iFOUND_CONTACT_ID
		);
		
		$this->request( $body );
		
	}

	/**
	 * Save This Property/Search/Campaign
	 *
	 * Push the saved property/search data to CRM. 
	 *
	 * @since 3.1.0
	 *
	 * @param object $data The data for the property viewed
	 */
	
	public function save_this( $type, $data ) {
		
		/** Check if $contact_id set. */
		if( ! defined( 'iFOUND_CONTACT_ID' ) ) return;
		
		$body = array(
			'action'		=> $type,
			'data' 			=> $data,
			'contact_id'	=> iFOUND_CONTACT_ID
		);
		
		$this->request( $body );
		
	}
	
	/**
	 * Request
	 *
	 * Make the API request to CRM. 
	 *
	 * @since 2.6.9
	 *
	 * @param  array  $body     The data for a the resuest.
	 * @return string $response A json sting including the error code and contact ID.
	 */
	
	private function request( $body ) {
		
		if( $api_key = get_option( 'imax_api_key' ) ) {

			$api_email 	= get_option( 'imax_api_email' );
			
			$url_parts = array( 
				'https://api.imaxcrm.com',
				'analytics',
				'backend.php',
				'IfoundAgent',
				'leadActivity',
				$api_key,
				$api_email
			);
			
			$args = array(
				'blocking'			=> false,
				'user-agent' 		=> 'iFoundAgent 1.0; https://ifoundagent.com',
				'body' 				=> $body,
				'headers' 			=> array(
					'Authorization' => $api_key,
					'Referer'		=> site_url()
				)
			);
	
			wp_remote_post( join( '/', $url_parts ), $args );

		}
		
	}

}
