#!/usr/bin/env node

import path from 'path'
import { closeConnection } from './database.js'
import { processJsonFiles } from './fileProcessor.js'
import { processListingRecords } from './historyTracker.js'
import { notifyError, setDevMode } from './notifications.js'
import config from './config.js'

const DATA_DIR = path.join(process.cwd(), 'data')

function validateAndGetNodeEnv(): 'dev' | 'prod' {
	const nodeEnv = process.env.NODE_ENV
	
	if (!nodeEnv) {
		console.error('ERROR: NODE_ENV environment variable is required')
		console.error('Set NODE_ENV to either "dev" or "prod"')
		console.error('Example: NODE_ENV=dev npm run dev armls_spark res')
		process.exit(1)
	}
	
	if (nodeEnv !== 'dev' && nodeEnv !== 'prod') {
		console.error(`ERROR: NODE_ENV must be either "dev" or "prod", got "${nodeEnv}"`)
		process.exit(1)
	}
	
	return nodeEnv as 'dev' | 'prod'
}

async function main(): Promise<void> {
	// Validate NODE_ENV and set dev mode
	const nodeEnv = validateAndGetNodeEnv()
	const devMode = nodeEnv === 'dev'
	setDevMode(devMode)
	
	if (devMode) {
		console.log('🔧 Running in DEV MODE - notifications will only log to stdout')
	} else {
		console.log('🚀 Running in PRODUCTION MODE - notifications will be sent via email/SMS')
	}

	// Get MLS system and class from command line arguments
	const mlsSystemName = process.argv[2]
	const mlsClassName = process.argv[3]

	if (!mlsSystemName || !mlsClassName) {
		console.error('Usage: node dist/index.js <mls-system-name> <mls-class-name>')
		console.error('Example: node dist/index.js mls1 residential')
		console.error('Example: node dist/index.js mls2 commercial')
		process.exit(1)
	}
	try {
		console.log(`History Tracker starting for MLS system: ${mlsSystemName}, class: ${mlsClassName}`)

		// Load configuration
		await config.load()
		console.log('Configuration loaded successfully')

		// Validate MLS class configuration exists
		const mlsClass = config.getMlsClass(mlsSystemName, mlsClassName)
		console.log(
			`Using tables: ${mlsClass.property_changes_table_name}, ${mlsClass.property_history_table_name}`
		)

		// Construct MLS class-specific data directory
		const mlsDataDir = path.join(DATA_DIR, mlsClass.data_directory)
		console.log(`Processing files from: ${mlsDataDir}`)

		// Process JSON files with MLS class-specific configuration
		// This will throw immediately upon first error
		const results = await processJsonFiles(mlsDataDir, records =>
			processListingRecords(records, mlsSystemName, mlsClassName)
		)

		// Report results (only reached if no errors occurred)
		console.log(`Successfully processed ${results.length} files`)

		for (const result of results) {
			console.log(`✓ ${result.filename}: ${result.records.length} listings processed`)
		}

		if (results.length === 0) {
			console.log('No JSON files found to process')
		}

		console.log('History Tracker completed successfully')
	} catch (error) {
		console.error('History Tracker failed:', error)
		
		// Send notification for the error before exiting
		await notifyError({
			message: error instanceof Error ? error.message : 'Unknown error occurred',
			stack: error instanceof Error ? error.stack : undefined,
			details: {
				type: 'Processing error',
				mlsSystem: mlsSystemName,
				mlsClass: mlsClassName,
			},
		})
		
		// Close database connection before exiting
		try {
			await closeConnection()
		} catch (closeError) {
			console.error('Error closing database connection:', closeError)
		}
		
		// Exit immediately with error code
		process.exit(1)
	}

	// Close database connection on successful completion
	try {
		await closeConnection()
	} catch (closeError) {
		console.error('Error closing database connection:', closeError)
		process.exit(1)
	}
}

// Handle unhandled rejections and exceptions
process.on('unhandledRejection', async (reason, promise) => {
	console.error('Unhandled Rejection at:', promise, 'reason:', reason)

	await notifyError({
		message: 'Unhandled Promise Rejection',
		stack: reason instanceof Error ? reason.stack : undefined,
		details: { reason: String(reason) },
	})

	process.exit(1)
})

process.on('uncaughtException', async error => {
	console.error('Uncaught Exception:', error)

	await notifyError({
		message: 'Uncaught Exception',
		stack: error.stack,
		details: { error: error.message },
	})

	process.exit(1)
})

// Start the application
main().catch(async error => {
	console.error('Failed to start History Tracker:', error)

	await notifyError({
		message: 'Failed to start History Tracker',
		stack: error instanceof Error ? error.stack : undefined,
		details: { error: String(error) },
	})

	process.exit(1)
})
