<?php
/**
 *
 * This script answers all prop queries from WP plugins
 *
 */

namespace Profound\API;

use \PropQuery;

class PropAPIResponder extends APIResponder {
	private $idx;

	protected function getDefaultAPI() {
		return API::explicit("prop", "v1.00", "json");
	}

	public function computeResponseData() {
		$this->idx = new PropQuery('../config.ini', array('prop' => $this->api));
		return $this->idx->propDetails();
	}
}