# Property Details
rewrite "^/app/detail/([^/]*)-(\d{7})/?$" /app/detail.php?q=$2 last;
rewrite "^/app2/detail/([^/]*)-(\d{7})/?$" /app2/detail.php?q=$2 last;
rewrite "^/app3/detail/([^/]*)-(\d{7})/?$" /app3/detail.php?q=$2 last;

# Property Search
rewrite ^/q/([^/]+)/?$ /query/$1.php last;
rewrite ^/q/([^/]+)/([^/]+)/?$ /query/$1.php?q=$2 last;

# Pass PHP scripts to Fastcgi listening on Unix socket.  Do not process them if inside WP uploads directory.
location ~ \.php$ {

    # SECURITY : Zero day Exploit Protection. See: http://wiki.nginx.org/Pitfalls
    try_files $uri =404;

    #===============================================================================================
    # Nginx microcache
    #
    # See: http://reviewsignal.com/blog/2014/06/25/40-million-hits-a-day-on-wordpress-using-a-10-vps/

    #fastcgi_cache  microcache;
    #fastcgi_cache_key $scheme$host$request_uri$request_method;
    #fastcgi_cache_valid 200 301 302 600s;
    #fastcgi_cache_use_stale updating error timeout invalid_header http_500;

    fastcgi_pass_header Set-Cookie;
    fastcgi_pass_header Cookie;
    fastcgi_ignore_headers Cache-Control Expires Set-Cookie;

    #===============================================================================================
    # FastCGI basics

    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_pass unix:/var/run/php5-fpm-idx.sock;
    fastcgi_index index.php;
    fastcgi_param PATH_INFO               $fastcgi_script_name;

    # TODO: Remove if using/upgrading to fastcgi 0.8.30?
    fastcgi_param  SCRIPT_FILENAME    $document_root$fastcgi_script_name;

    fastcgi_param PHP_VALUE "newrelic.appname=IDX Server";

    # Custom headers sent by ProFoundIDX.php
    fastcgi_param X_IDX_CLIENT_IP $http_x_idx_client_ip;
    fastcgi_param X_IDX_CLIENT_REFERER $http_x_idx_client_referer;
    fastcgi_param X_IDX_CLIENT_USER_AGENT $http_x_idx_client_user_agent;

    include fastcgi_params;

    #===============================================================================================
    # FastCGI tweaks
    #
    # See: http://calendar.perfplanet.com/2012/using-nginx-php-fpmapc-and-varnish-to-make-wordpress-websites-fly/

    fastcgi_intercept_errors on;
    fastcgi_ignore_client_abort off;
    fastcgi_connect_timeout 60;
    fastcgi_send_timeout 180;
    fastcgi_read_timeout 180;
    fastcgi_buffer_size 128k;
    fastcgi_buffers 4 256k;
    fastcgi_busy_buffers_size 256k;
    fastcgi_temp_file_write_size 256k;

    # See: http://premium.wpmudev.org/blog/wordpress-multisite-wordpress-nginx/
    fastcgi_keep_conn on;
}

