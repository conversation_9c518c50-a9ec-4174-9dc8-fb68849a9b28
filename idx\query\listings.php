<?php

// The difference between this file and prop.php is that this one is meant to handle
// property details for multiple properties, not just a single one. Furthermore,
// it might not return all details but just a subset for different purposes. E.g.
// returning just the titles of a set of listings.

require_once('../vendor/autoload.php');

$listingsAPI = new Profound\API\ListingsAPIResponder();
$listingsAPI->handleRequest();