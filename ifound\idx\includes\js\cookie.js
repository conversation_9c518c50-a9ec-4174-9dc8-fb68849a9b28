function createCookie(name, value, seconds) {
    if( seconds != 0 ) {
        var expires;
        if (seconds) {
            var date = new Date();
            date.setTime(date.getTime() + (seconds * 1000));
            expires = "; expires=" + date.toGMTString();
        } else {
            expires = "";
        }
        document.cookie = name + "=" + value + expires + "; path=/";
    }
}

function readCookie(name) {
	var cookies = document.cookie.split(';');
	for(var cookie of cookies) {
		if(cookie.includes(name)) {
			return cookie.split('=')[1];
		}
	}
	return null;
}

function deleteCookie(name) {
	if (readCookie(name)) {
		var path = '/';
		document.cookie = name + "=" +
			((path) ? ";path="+path:"")+
			";expires=Thu, 01 Jan 1970 00:00:01 GMT";
	}
}

function readLocalStorage() {
    return JSON.parse(localStorage.getItem('pfmls')) || {};
}

function setLocalStorageValue(key, value) {
    var data = readLocalStorage();
    data[key] = value;
    var newValue = JSON.stringify(data);
    localStorage.setItem('pfmls', newValue);
}
