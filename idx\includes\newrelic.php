<?php

if (extension_loaded('newrelic')) {

	if (isset($_SERVER['HTTP_X_IDX_CLIENT_IP'])) {
		# Remote IP - this is what needs to be used for iptables blocking
		$ip = $_SERVER['HTTP_X_IDX_CLIENT_IP'];

		# ISP & Geo IP    
		$ipdata = geoip_record_by_name($ip);

		# ASN
		$asnstr = geoip_asnum_by_name($ip);
		preg_match('/^AS(\d+)\s+(.*)/', $asnstr, $match);
		if ($match) {     
			list ($junk, $asn, $asn_name) = $match;
			$ipdata['asn_number'] = $asn;
			$ipdata['asn_name'] = $asn_name;
		}

		# ISP
		$ipdata['isp_name'] = geoip_isp_by_name($ip);
		$ipdata['org_name'] = geoip_org_by_name($ip);

		# Hostname
		#$ipdata['source_host'] = gethostbyaddr($ip);
		$ipdata['source_ip'] = $ip;

		# ISP & IP data
		foreach ($ipdata as $key => $val) {
			newrelic_add_custom_parameter($key, $val);
		}
	}

	# HTTP Referer - WordPress page URL that made the IDX request
	if (isset($_SERVER['HTTP_REFERER'])) {
		$page_url = strtolower($_SERVER['HTTP_REFERER']);

		$url = parse_url($page_url);
		$base_domain = preg_replace('#^www\.#', '', $url['host']);

		newrelic_add_custom_parameter("base_domain", $base_domain);
		newrelic_add_custom_parameter("page_url", $page_url);
		if (isset($url['query'])) {
			newrelic_add_custom_parameter("query_string", $url['query']);
		}
		if (isset($url['path'])) {
			newrelic_add_custom_parameter("url_path", $url['path']);
		}	
	}

	# Client Referer
	if (isset($_SERVER['HTTP_X_IDX_CLIENT_REFERER'])) {

		$referer = strtolower($_SERVER['HTTP_X_IDX_CLIENT_REFERER']);

		$refer_domain = preg_replace('#^https?://#', '', $referer);
		$refer_domain = preg_replace('#[/?].*#', '', $refer_domain);

		# Referer tracking
		newrelic_add_custom_parameter("referer_url", $referer);
		newrelic_add_custom_parameter("referer_domain", $refer_domain);
	}

	if (isset($_SERVER['QUERY_STRING'])) {
		newrelic_add_custom_parameter("idx_query", $_SERVER['QUERY_STRING']);
	}

	newrelic_add_custom_parameter("server_name", $_SERVER['SERVER_NAME']);

	newrelic_add_custom_parameter("http_method", $_SERVER['REQUEST_METHOD']);
	newrelic_add_custom_parameter("http_protocol", str_replace('HTTP/', '', $_SERVER['SERVER_PROTOCOL']));
	newrelic_add_custom_parameter("idx_url", $_SERVER['REQUEST_URI']);
	newrelic_add_custom_parameter("full_url", $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);

	newrelic_add_custom_parameter("remote_ip", $_SERVER['REMOTE_ADDR']);
}
