// Reminder: I think most of these address formats are technically configurable per site. For example, go to
// http://ifoundwp.test/wp-admin/admin.php?page=ifound_seo_settings and you'll see "Property Detail URL" that allows
// even the URL to be chosen. But my understanding is we don't teach this to clients and they never change it. So
// because it's extra effort to get these values from PHP to JS, I'm going to just hard-code it for now.

import slugify from 'slugify';

export function makeAddress(listing) {
	const fields = ['StreetNumber', 'StreetDirPrefix', 'StreetName', 'StreetSuffix', 'UnitNumber', 'City', 'PostalCode'];
	const fullAddress = fields.map(fname => listing[fname]).filter(x => x).join(' ');
	return fullAddress;
}

export function makeAddressKebab(listing) {
	const fields = ['StreetNumber', 'StreetDirPrefix', 'StreetName', 'StreetSuffix', 'UnitNumber', 'City', 'PostalCode'];
	let fullAddress = fields.map(fname => listing[fname]).filter(x => x).map(x => x.toLowerCase()).join('-');
	fullAddress = slugify(fullAddress);
	return fullAddress;
}

export function milesToMeters(miles) {
	return miles * 1609.34;
}
