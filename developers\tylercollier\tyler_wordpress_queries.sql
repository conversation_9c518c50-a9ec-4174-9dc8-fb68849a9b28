return;

create database wordpress;
drop database wordpress;
grant all privileges on wordpress.* to 'wordpress'@'localhost' identified by'wordpress' with grant option;

select * from users;
select * from options;
select * from options where option_name like '%cit%';
select * from options where option_name like '%profoundmls%';
select * from wp_options where option_name like '%pfmls%';
select * from options where option_value like '%cit%';

select * from wp_options where option_value = 'neighborhood';
select * from wp_posts limit 5;
update posts set post_name = 'neighborhood' where ID = 5;

select * from wp_pfmls_saved_listings;
show tables like '%pfmls%';
show tables like 'wp_%';
drop table wp_pfmls_saved_listings;
drop table wp_pfmls_saved_searches;
truncate table wp_pfmls_saved_listings;
truncate table wp_pfmls_saved_searches;

alter table wp_pfmls_mls add unique key saved_listing_per_user (user_id, mls_id); 

CREATE TABLE wp_pfmls_mls 
(
id SERIAL,
 user_id BIGINT(20) UNSIGNED NOT NULL,
mls_id VARCHAR(11) NOT NULL,
created TIMESTAMP NOT NULL,
FOREIGN KEY (user_id) REFERENCES wp_users (ID)
) ENGINE=InnoDB CHARSET UTF8;

select * from wp_options where option_name like '%pfmls%';
delete from wp_options where option_name = 'pfmls_user_login_db_version';

select exists (select 'x' from wp_pfmls_mls) as it_EXISTS;

select * from wp_users;

select * from wp_options where option_name like '%neigh%';

select * from wp_options where option_name like '%ProFoundMLSAdminOptions%';