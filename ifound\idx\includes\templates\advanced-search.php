<?
/**
 * The template for displaying Advanced Search
 *
 * @since 1.0
 * @version 1.0
 */

get_header(); ?>

<div id="primary" class="content-area advanced">
		
		<main id="main" class="site-main" role="main">

			<article id="advanced-search" class="advanced-search">

				<header class="entry-header">

					<h1 class="entry-title"><?
						do_action( 'ifound_page_h1', $results ); ?>
					</h1>

				</header><!-- .entry-header -->

				<div class="entry-content">

					<!-- Start Advanced Section --><?
					do_action( 'ifound_before_advanced' );
					do_action( 'ifound_advanced_scripts', $results, $extra ); ?>
					<div class="advanced-criteria-section">

						<div class="ifound-wrap"><?
							do_action( 'ifound_advanced_search_bar' );
							do_action( 'ifound_dynamic_form_wrapper', $results->input_obj ); ?>
						</div>

					</div>

					<div class="button-wrapper">

						<div class="ifound-wrap"><?

							do_action( 'ifound_advanced_button' );

							if( defined( 'DOING_SAVED_SEARCH' ) ) {

								do_action( 'ifound_update_this_button', $results->save_this_id );

							} else {

								do_action( 'ifound_save_this_button', 'search-update' );

							}
							do_action( 'ifound_show_map_button' ); ?>

						</div>

					</div>

					<div class="aerial-sphere-advanced-search" style="visibility: hidden;"><?

					if($results !== NULL) {
						do_action( 'aerial_sphere_search', $results);
					} else {
						do_action( 'aerial_sphere_search_init' );
					}
					?></div><?

					do_action( 'ifound_shapes_map' );
					if ($results->save_this_id) {
						do_action('ifound_subject_info', $results->save_this_id);
					}
					do_action( 'ifound_advanced_search_results', $results, $extra );
					?>
					</div><!-- End Details Section -->

				</div><!-- .entry-content -->

		</article><!-- #post-## -->

	</main><!-- #main -->

</div><!-- #primary --><?

get_footer();

/** Required Hooks **DO NOT REMOVE** */
do_action( 'ifound_registration', $results );
$view_type = defined( 'DOING_SAVED_SEARCH' ) ? 'results_view' : 'advanced_view';
do_action( 'ifound_tracking', $results, $view_type );
apply_filters( 'ifound_footer', $results );
?>
