@charset "UTF-8";
/* CSS Document */

/* This line could be removed if strong testimonials updates to fontawesome 5.0. */
.fa-facebook-f:before{
	content: "\f39e" !important;
}

.social-network-icon-wrapper{
	width: 32px;
	height: 32px;
	margin: 4px 2px;
	text-align: center;
	display: inline-block;
}

.pre-header .ifound-social-media-wrapper {
	float: right;
}

.pre-header-2 {
	padding: 0;
}

.social-network-icon{
	width: 100%;
	height: 100%;
	font-size: 24px;
	line-height:32px;
	color:#999;
}

.ifound-social-radius .social-network-icon-wrapper{
	border: thin solid #ccc;
	border-radius: 7px;
	background: #f5f5f5;
	padding: 2px;
}

.ifound-social-floating .social-network-icon-wrapper{
	padding: 2px;
	margin: 4px 0;
}

.ifound-social-square .social-network-icon-wrapper{
	border: thin solid #ccc;
	background: #f5f5f5;
	padding: 2px;
}

.ifound-social-round .social-network-icon-wrapper{
	border: thin solid #ccc;
	-moz-border-radius: 16px;
	-webkit-border-radius: 16px;
	border-radius: 50%;
	background: #f5f5f5;
	padding: 4px;
}

.ifound-social-oval .social-network-icon-wrapper{
	border: thin solid #ccc;
	background: #f5f5f5;
	-moz-border-radius: 32px / 16px;
	-webkit-border-radius: 32px / 16px;
	border-radius: 32px / 16px;
	padding: 2px;
}

.social-network-icon-wrapper {
	box-sizing: initial;
}

.fa-tiktok:before {
	content: "\e07b"
}

/*
 Keep this synced with other places in code using this string: khjfdgaft677d6r

 I got this from https://www.briancoords.com/twitter-icon-font-awesome-4/
 I changed it a bit.
*/
.fab.fa-twitter, .fab.fa-twitter-square {
	font-family: sans-serif;
	font-weight: bold;
}
.fab.fa-twitter::before, .fab.fa-twitter-square::before {
	content: "𝕏";
}
.fab.fa-twitter::before {
	font-size: 1.2em;
}
