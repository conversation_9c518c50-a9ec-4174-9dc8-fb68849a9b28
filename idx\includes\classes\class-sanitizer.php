<?php
/*******************************************************************
**********       Pinion Media :: Sanitizer Class      **************
**********        Updated 110115 | Version 1.7.4      **************
**********    (c) Copyright 2011 Pinion Media, llc    **************
**********           http://pinionmedia.com           **************
********************************************************************/


class sanitizer {


// Set default parameters
/*
These parameters can be overwritten by using the object and the variable name
Example:
$sanitizer_obj->hacker_xss=array('active'=>false);
OR
$sanitizer_obj->alphanumeric_only = array('active'=>true, 'include'=>array('username', 'password'));
OR
$sanitizer_obj->badword['badwords'][] = "cockblock";

To Exclude the field variable from ALL cleaning:
$sanitizer_obj->exclude = array('var_field');
*/
var $exclude = array();
var $hacker_xss = array('active'=>true);
var $hacker_all_tags = array('active'=>true);
var $spamer = array('active'=>true, 'exclude'=>array('emailaddress'));
var $badword = array('active'=>true);
var $phone = array('active'=>true, 'include'=>array('phone', 'phone2', 'fax'));
var $alphanumeric_only = array('active'=>true, 'include'=>array('password'));
var $lowercase = array('active'=>true, 'include'=>array('emailaddress'));
var $remove_slashes = array('active'=>false);
var $add_slashes = array('active'=>false);
var $price_strg = array('active'=>true, 'include'=>array('price'));
var $strip_total = array('active'=>true, 'include'=>array('grand_total'));
var $strip_cc = array('active'=>true, 'include'=>array('cc'));
//var $strip_http = array('active'=>true, 'include'=>array('webaddress'));

// Phone number formatting default
var $phone_format = '(*************';



function cleandata( $process_info ){

	/*
	Although this sanitizer can accept all types of data and clean it, for the specfic data to be cleaned (with default settings)
	the array of variable default must match the following structure. (all other variables need to be specified - examples above)

	$process_info['country'] // Used for the Phone Formatting... If not US, United States, Canada, or CA then do not format
	$process_info['username']
	$process_info['password']
	$process_info['emailaddress']
	$process_info['phone']
	$process_info['phone2']
	$process_info['fax']
	$process_info['grand_total']
	$process_info['cc']



	New Vars that will come out of this class
	$process_info['strg_total'] // creates a total for viewing (exp: 45 -> 45.00)
	$process_info['stripped_total'] // creates a total for trust commerce (exp: 45 -> 4500)
	$process_info['cut_cc'] // Creates a last 4 of cc
	$process_info['data_changed'] // Array of changed fields, containing the 'new' and 'prev' data in that array
	*/

	// Assign a temp process_info of all incoming data (to compair against at the end)
	$temp_process_info = $process_info;



	// Remove hacker XSS code from form
	if($this->hacker_xss['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->hacker_xss);

				if($cleanit){
					$process_info["$id"] = (!is_array($result))? $this->remove_xss($result) : $process_info["$id"]; // Get rid of the xss hacker attempts
				}

			}
		}

	} // End Hacker




	// Remove hacker all tag code from form
	if($this->hacker_all_tags['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->hacker_all_tags);

				if($cleanit){
					$process_info["$id"] = (!is_array($result))? strip_tags($result) : $process_info["$id"]; // Clean values of any tag code
				}

			}
		}

	} // End Hacker




	// Remove values of any nonalphanumeric data
	if($this->alphanumeric_only['active']){
		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->alphanumeric_only);

				if($cleanit){
					$process_info["$id"] = ereg_replace("[^A-Za-z0-9_-]", "", $process_info["$id"]);
				}

			}
		}

	} // End nonalphanumeric




	// Set the value to lowercase data (used mainly for emailaddress)
	if($this->lowercase['active']){
		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->lowercase);

				if($cleanit){
					$process_info["$id"] = strtolower($process_info["$id"]);
				}

			}
		}

	} // End lowercase




	// Remove slashes from data
	if($this->remove_slashes['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->remove_slashes);

				if($cleanit){
					$process_info["$id"] = (!is_array($result))? stripslashes($result) : $process_info["$id"]; // Clean values of any slash escapes
				}

			}
		}

	} // End remove_slashes




	// Add slashes to data
	if($this->add_slashes['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->add_slashes);

				if($cleanit){
					// Add slashes for database use (needed for get_magic_quotes_gpc()=Off and excess slashes from bad coding)
					$process_info["$id"] = (!is_array($result))? addslashes($result) : $process_info["$id"];
				}

			}
		}

	} // End add_slashes




	// Clean data from spamers
	if($this->spamer['active']){
		$spamer_parts = array('@', '.');
		$spamer_swap = array('(at)', '(dot)');

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->spamer);

				if($cleanit){
					$process_info["$id"] = (strstr($result,'@') != "" && strstr($result,'.') != "")? str_replace($spamer_parts, $spamer_swap, $process_info["$id"]) : $process_info["$id"];
				}

			}
		}

	} // End Spamer




	// Clean bad words from data
	if($this->badword['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->badword);

				if($cleanit){
					$process_info["$id"] = $this->word_filter($process_info["$id"]);
				}

			}
		}

	} // End Bad Words




	// Cleanup the price of non characters & create the price_strg formatted
	if($this->price_strg['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->price_strg);

				if($cleanit){
					$process_info["$id"] = ereg_replace("[^[:digit:]]", "", $process_info["$id"]);
					$process_info['price_strg'] = number_format($process_info["$id"]);
				}

			}
		}

	} // End Price String




	// Create total formated
	if($this->strip_total['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->strip_total);

				if($cleanit){
					$process_info['strg_total'] = number_format($process_info["$id"], 2);
					$process_info['stripped_total'] = str_replace (".", "", $process_info['strg_total']);
				}

			}
		}

	} // End Total Formatting




	// Cleanup the CC of non characters & Create the cut cc
	if($this->strip_cc['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->strip_cc);

				if($cleanit){
					$process_info["$id"] = ereg_replace("[^[:digit:]]", "", $process_info["$id"]);
					$process_info['cut_cc'] = substr_replace($process_info["$id"], '', 0, -4);
				}

			}
		}

	} // End CC Cleaning




	// Clean up Phone, phone2 and fax
	if($this->phone['active']){

		if( !isset($process_info['country']) || ( in_array($process_info['country'],array('United States','US','Canada','CA')) ) ){

			if(is_array($process_info)){
				foreach ($process_info as $id => $result){

					$cleanit = $this->cleanit_toggle($id, $this->phone);

					if($cleanit && (strlen($process_info["$id"]) > 6)){
						$process_info["$id"] = $this->clean_phone($process_info["$id"]);
					}

				}
			}

		}else{
			// The number is international
			// There are just too many formatting issues with international
			// numbers and we're not able to get a consistant formatting to work.
			// So, don't process the phone for non US and CA
		}

	} // End Phone




	// Clean up http:// from webaddresses
	if(isset($this->strip_http) && $this->strip_http['active']){

		if(is_array($process_info)){
			foreach ($process_info as $id => $result){

				$cleanit = $this->cleanit_toggle($id, $this->strip_http);

				if($cleanit){
					$process_info["$id"] = str_replace('http://', '', strtolower($process_info["$id"]));
				}

			}
		}

	} // End Phone




	// Test to see what data has changed.
	// Then assign the fields to the changed array
	if(is_array($process_info)){
		foreach ($process_info as $id => $result){

			if($process_info["$id"] != $temp_process_info["$id"]){
				$process_info['data_changed'][$id]['new'] = $process_info["$id"];
				$process_info['data_changed'][$id]['prev'] = $temp_process_info["$id"];
			}

		}
	}




	return $process_info;

} // End Clean Data Function -------------------------------------------------------------













// Cleanit function for all above items	------------------------------------------------------
function cleanit_toggle($field, $elements){

	$cleanit = true;

	// process the include items
	if(array_key_exists('include', $elements) && is_array($elements['include'])){
		$cleanit = (in_array($field, $elements['include']))? true : false;
	}
	// process the exclude items
	if(array_key_exists('exclude', $elements) && is_array($elements['exclude'])){
		$cleanit = (in_array($field, $elements['exclude']))? false : true;
	}
	// process the global exclude items
	if(in_array($field, $this->exclude)){
		$cleanit = false;
	}

	return $cleanit;

} // End Cleanit toggle function -------------------------------------------------------------

// -- PHONE CLEANING AND FORMATTING -------------------------------------------------------------
function clean_phone($phone){ // Must have PHONE_FORMAT from constant file to work correctly
	if ($phone == ''){ return $phone; }

	if(substr($phone, 0, 1) == '1'){ $phone = substr($phone, 1); }
	if(substr($phone, 0, 1) == '0'){ $phone = substr($phone, 0); }

	$phone = ereg_replace("[^[:digit:]]", "", $phone); // Get rid of any non-digits

	$result = '';
	$format_pos = 0;
	$string_pos = 0;
	while((strlen($this->phone_format) - 1) >= $format_pos){
       //If its a number => stores it
       if (is_numeric(substr($this->phone_format, $format_pos, 1))){
           $result .= substr($phone, $string_pos, 1); //$format_pos
           $string_pos++;
       //If it is not a number => stores the caracter
       }else{
           $result .= substr($this->phone_format, $format_pos, 1);
       }
       //Next caracter at the mask.
       $format_pos++;
	}

	return $result;
}
// ---------------------------------------------------------------------------------------------
// -- BAD WORD CLEANING ------------------------------------------------------------------------
function word_filter($content) {

	//$this->badword['badwords'][] = "ass";
	$this->badword['badwords'][] = "asshole";
	//$this->badword['badwords'][] = "ballsack";
	$this->badword['badwords'][] = "bitch";
	//$this->badword['badwords'][] = "bastard";
	$this->badword['badwords'][] = "clit";
	//$this->badword['badwords'][] = "cock";
	//$this->badword['badwords'][] = "cum";
	//$this->badword['badwords'][] = "cunt";
	//$this->badword['badwords'][] = "dick";
	//$this->badword['badwords'][] = "dike";
	//$this->badword['badwords'][] = "dildo";
	$this->badword['badwords'][] = "fuck";
	$this->badword['badwords'][] = "fucker";
	$this->badword['badwords'][] = "fuckers";
	$this->badword['badwords'][] = "fuckin";
	$this->badword['badwords'][] = "fucking";
	$this->badword['badwords'][] = "fucken";
	//$this->badword['badwords'][] = "gay";
	//$this->badword['badwords'][] = "god damn";
	//$this->badword['badwords'][] = "goddamn";
	//$this->badword['badwords'][] = "hoe";
	$this->badword['badwords'][] = "nigger";
	//$this->badword['badwords'][] = "jackoff";
	//$this->badword['badwords'][] = "jack-off";
	$this->badword['badwords'][] = "jism";
	$this->badword['badwords'][] = "jizm";
	$this->badword['badwords'][] = "pussy";
	$this->badword['badwords'][] = "shit";
	$this->badword['badwords'][] = "shitting";
	$this->badword['badwords'][] = "slut";
	$this->badword['badwords'][] = "twat";
	$this->badword['badwords'][] = "vagina";
	//$this->badword['badwords'][] = "wack";
	//$this->badword['badwords'][] = "whore";

	$wordreplace = array ( "#" ); //"!", "#", "%", "^", "&", "*" );

	$count = count($this->badword['badwords']);
	$countfilter = count($wordreplace);

	// Loop through the badwords array
	for ($n = 0; $n < $count; ++$n, next ($this->badword['badwords'])) {
		//Create random replace characters
		$x = 2;
		$y = rand(4,7);
		$filter = "";
		while ($x<="$y") {
			$f = rand(0, $countfilter - 1);
			$filter .="$wordreplace[$f]";
			$x++;
		}
		//Search for badwords in content
		$search = $this->badword['badwords'][$n];
		//$content = preg_replace("'$search'i","$filter",$content);
		//$content = preg_replace("/(\w+)\s+$search\b/i","$filter",$content);
		$content = preg_replace("/$search\b/i","$filter",$content);
	}

	return $content;
}
// ---------------------------------------------------------------------------------------------

// -- CROSS SITE SCRIPTING CLEANER -------------------------------------------------------------
function remove_xss($val) {
   // remove all non-printable characters. CR(0a) and LF(0b) and TAB(9) are allowed
   // this prevents some character re-spacing such as <java\0script>
   // note that you have to handle splits with \n, \r, and \t later since they *are* allowed in some inputs
   $val = preg_replace('/([\x00-\x08][\x0b-\x0c][\x0e-\x20])/', '', $val);

   // straight replacements, the user should never need these since they're normal characters
   // this prevents like <IMG SRC=&#X40&#X61&#X76&#X61&#X73&#X63&#X72&#X69&#X70&#X74&#X3A&#X61&#X6C&#X65&#X72&#X74&#X28&#X27&#X58&#X53&#X53&#X27&#X29>
   $search = 'abcdefghijklmnopqrstuvwxyz';
   $search .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
   $search .= '1234567890!@#$%^&*()';
   $search .= '~`";:?+/={}[]-_|\'\\';
   for ($i = 0; $i < strlen($search); $i++) {
      // ;? matches the ;, which is optional
      // 0{0,7} matches any padded zeros, which are optional and go up to 8 chars

      // &#x0040 @ search for the hex values
      $val = preg_replace('/(&#[x|X]0{0,8}'.dechex(ord($search[$i])).';?)/i', $search[$i], $val); // with a ;
      // &#00064 @ 0{0,7} matches '0' zero to seven times
      $val = preg_replace('/(&#0{0,8}'.ord($search[$i]).';?)/', $search[$i], $val); // with a ;
   }

   // now the only remaining whitespace attacks are \t, \n, and \r
   $ra1 = Array('javascript',
   				'vbscript',
				'expression',
				'applet',
				'meta',
				'xml',
				'blink',
				//'link',
				//'style',
				'script',
				'embed',
				'object',
				'iframe',
				'frame',
				'frameset',
				'ilayer',
				'layer',
				'bgsound',
				'title',
				'base');
   $ra2 = Array('onabort', 'onactivate', 'onafterprint', 'onafterupdate', 'onbeforeactivate', 'onbeforecopy', 'onbeforecut', 'onbeforedeactivate', 'onbeforeeditfocus', 'onbeforepaste', 'onbeforeprint', 'onbeforeunload', 'onbeforeupdate', 'onblur', 'onbounce', 'oncellchange', 'onchange', 'onclick', 'oncontextmenu', 'oncontrolselect', 'oncopy', 'oncut', 'ondataavailable', 'ondatasetchanged', 'ondatasetcomplete', 'ondblclick', 'ondeactivate', 'ondrag', 'ondragend', 'ondragenter', 'ondragleave', 'ondragover', 'ondragstart', 'ondrop', 'onerror', 'onerrorupdate', 'onfilterchange', 'onfinish', 'onfocus', 'onfocusin', 'onfocusout', 'onhelp', 'onkeydown', 'onkeypress', 'onkeyup', 'onlayoutcomplete', 'onload', 'onlosecapture', 'onmousedown', 'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout', 'onmouseover', 'onmouseup', 'onmousewheel', 'onmove', 'onmoveend', 'onmovestart', 'onpaste', 'onpropertychange', 'onreadystatechange', 'onreset', 'onresize', 'onresizeend', 'onresizestart', 'onrowenter', 'onrowexit', 'onrowsdelete', 'onrowsinserted', 'onscroll', 'onselect', 'onselectionchange', 'onselectstart', 'onstart', 'onstop', 'onsubmit', 'onunload');
   $ra = array_merge($ra1, $ra2);
      // Since Scan allert is only concerned about the use of '<' and '>',
      // and the following code adds a bogus tags (<x>) inside the middle of any tags
	  // outlined in the above array, this script makes things worse to get PCI compliant with Scan Alert.
   $found = true; // keep replacing as long as the previous round replaced something
   while ($found == true) {
      $val_before = $val;
      for ($i = 0; $i < sizeof($ra); $i++) {
         $pattern = '/';
         for ($j = 0; $j < strlen($ra[$i]); $j++) {
            if ($j > 0) {
               $pattern .= '(';
               $pattern .= '(&#[x|X]0{0,8}([9][a][b]);?)?';
               $pattern .= '|(&#0{0,8}([9][10][13]);?)?';
               $pattern .= ')?';
            }
            $pattern .= $ra[$i][$j];
         }
         $pattern .= '/i';
         $replacement = substr($ra[$i], 0, 2).'<x>'.substr($ra[$i], 2); // add in <> to nerf the tag
         $val = preg_replace($pattern, $replacement, $val); // filter out the hex tags
         if ($val_before == $val) {
            // no replacements were made, so exit the loop
            $found = false;
         }
      }
   }

   // These items are to restrict SQL injection hacks
   // ... but it is very limiting for form processing,
   // so I will disable some for now.
   $val = str_replace (";", "", $val); //"&#59;", $val);
   $val = str_replace ("%", "", $val); //"&#37;", $val);
   $val = str_replace (">", "", $val); //"&gt;", $val);
   $val = str_replace ("<", "", $val); //"&lt;", $val);
   //$val = str_replace (")", "&#41;", $val);
   //$val = str_replace ("(", "&#40;", $val);
   //$val = str_replace ("'", "", $val); //"&#39;", $val);
   //$val = str_replace ('"', "", $val); //"&#34;", $val);
   //$val = str_replace ('-', "&#45;", $val);
   //$val = str_replace ('+', "&#45;", $val);
   //$val = str_replace ('=', "&#45;", $val);
   $val = str_replace ('|', "", $val);

   return $val;
}
// ---------------------------------------------------------------------------------------------


} // End formcleaner Class
?>