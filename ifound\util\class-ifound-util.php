<?php

defined('ABSPATH') or die(' You do not have access! ');

require_once(__DIR__ . '/../traits/NewHooklessTrait.php');
/*
 *		Utility Class
 *
 */

$error_log_for_host_count = 0;

class iFoundUtil {
	use NewHooklessTrait;

	public static $mysql_date_format = 'Y-m-d H:i:s';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_filter('ifound_get_contacts_from_request', array($this, 'get_contacts_from_request'));
			add_filter('ifound_format_listing_address', array($this, 'format_listing_address'));
			add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
			add_action('wp_enqueue_scripts', array($this, 'non_admin_enqueue_scripts'));
		}
	}

	private function enqueue_shared_scripts() {
		wp_register_script('ifound_shared_js', plugins_url('includes/js/ifound-shared.js', __FILE__), ['jquery'],
			iFOUND_PLUGIN_VERSION);
		wp_enqueue_script('ifound_shared_js');
	}

	public function admin_enqueue_scripts() {
		$this->enqueue_shared_scripts();

		wp_register_script('ifound_shared_admin_js', plugins_url('includes/js/ifound-shared-admin.js', __FILE__),
			['ifound_shared_js'], iFOUND_PLUGIN_VERSION);
		wp_localize_script( 'ifound_shared_admin_js', 'ifound_shared_admin', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'wp_rest' ),
		));
		wp_enqueue_script('ifound_shared_admin_js');
	}

	public function non_admin_enqueue_scripts() {
		$this->enqueue_shared_scripts();
	}

	// The array returned is our own custom construction, not a WP_Post object.
	public function get_contacts_from_request($contact_ids) {
		// Let's be safe and check for no contact IDs, because if it's an empty array, our query below will actually
		// pull all contacts, which we don't want.
		if (!$contact_ids) {
			wp_die('Error 879697654: No contact IDs passed');
		}
		$query_args = array(
			'post_type' => iFoundJointContact::$all_contact_post_types,
			'post__in' => $contact_ids,
			'numberposts' => -1,
			'post_status' => 'publish',
		);
		$contacts = [];
		$posts = get_posts($query_args);
		foreach ($posts as $post) {
			$contact_meta = $this->get_single_metas($post->ID, [
				'fname',
				'lname',
				'email',
				'address',
				'address_2',
				'city',
				'state',
				'zip',
				'mphone',
			]);
			$contacts[] = [
				'id' => $post->ID,
				'name' => $contact_meta['fname'] . ' ' . $contact_meta['lname'],
				'email' => $contact_meta['email'],
				'address' => implode(' ', [
					$contact_meta['address'],
					$contact_meta['address_2'],
					$contact_meta['city'],
					$contact_meta['state'],
					$contact_meta['zip'],
				]),
				'mphone' => $contact_meta['mphone'],
			];
		}
		return $contacts;
	}

	// Grab multiple post_meta (or user_meta) values at once. The name 'single' specifies that there is expected to be a
	// single record for that key.
	public function get_single_metas($record_id, $meta_keys, $meta_type = 'post') {
		$fn_name = $meta_type === 'user' ? 'get_user_meta' : 'get_post_meta';
		$a = [];
		foreach ($meta_keys as $meta_key) {
			$v = call_user_func($fn_name, $record_id, $meta_key, 'true');
			$a[$meta_key] = $v;
		}
		return $a;
	}

	/*		 save_contact_meta
	 *
	 * Used to store and update
	 * contact's meta data.
	 * @param  contact_info: Array with all of contact's information (first, last, email, etc.)
	 * @variable cInfo_copy:	A copy of contact_info array
	 * @variable fInfo_copy:  A copy of the field's info
	 */
	public static function save_contact_meta( $contact_id, $contact_info ) {

		$cInfo_copy = array();

		do_action( 'ifound_external_crm_save_contact_data', $contact_id, $contact_info );

		foreach( $contact_info as $field => $field_info ) {

			if ( ! empty ( $contact_info[$field] ) ) {

				$fInfo_copy = apply_filters( 'ifound_sanitize', $contact_info[$field] );

				update_post_meta( $contact_id, $field, $fInfo_copy );
				$cInfo_copy[$field] = $fInfo_copy;
			}
		}

		$cInfo_copy['Contact Id'] = $contact_id;

		return $cInfo_copy;
	}

	// I got this from https://stackoverflow.com/a/17808971/135101
	// It allows you to move associative array items.
	public function move_item(&$ref_arr, $key1, $move, $key2 = null) {
		$arr = $ref_arr;
		if ($key2 == null) $key2 = $key1;
		if (!isset($arr[$key1]) || !isset($arr[$key2])) return false;

		$i = 0;
		foreach ($arr as &$val) $val = array('sort' => (++$i * 10), 'val' => $val);

		if (is_numeric($move)) {
			if ($move == 0 && $key1 == $key2) return true;
			elseif ($move == 0) {
				$tmp = $arr[$key1]['sort'];
				$arr[$key1]['sort'] = $arr[$key2]['sort'];
				$arr[$key2]['sort'] = $tmp;
			} else $arr[$key1]['sort'] = $arr[$key2]['sort'] + ($move * 10 + ($key1 == $key2 ? ($move < 0 ? -5 : 5) : 0));
		} else {
			switch ($move) {
				case 'up':
					$arr[$key1]['sort'] = $arr[$key2]['sort'] - ($key1 == $key2 ? 15 : 5);
					break;
				case 'down':
					$arr[$key1]['sort'] = $arr[$key2]['sort'] + ($key1 == $key2 ? 15 : 5);
					break;
				case 'top':
					$arr[$key1]['sort'] = 5;
					break;
				case 'bottom':
					$arr[$key1]['sort'] = $i * 10 + 5;
					break;
				default:
					return false;
			}
		}
		uasort($arr, function ($a, $b) {
			return $a['sort'] > $b['sort'];
		});
		foreach ($arr as &$val) $val = $val['val'];
		$ref_arr = $arr;
		return true;
	}

	public function format_listing_address($listing) {
		return implode(' ', [
			$listing['StreetNumber'],
			$listing['StreetDirPrefix'],
			$listing['StreetName'],
			$listing['StreetSuffix'],
			$listing['City'],
			$listing['State'],
			$listing['PostalCode']
		]);
	}

	public function make_address_slug($listing, $crm_id_options = []) {
		$fields = array(
			$listing->StreetNumber,
			$listing->StreetDirPrefix,
			$listing->StreetName,
			$listing->StreetSuffix,
			$listing->City,
			$listing->PostalCode
		);

		$address = [];
		foreach($fields as $field) {
			if($field !== '')
				$address[] = str_replace(' ', '-', $field);
		}

		$joined_address = join('-', $address);
		$address_slug = sanitize_title($joined_address);
		$path_parts = [iFoundIdx::new_hookless()->detail, $address_slug, $listing->ListingID];
		$path = '/' . join('/', $path_parts) . '/';
		$url = site_url($path);
		$url = $this->add_id_ext_to_url($url, $crm_id_options);
		return $url;
	}

	public function add_id_ext_to_url($url, $crm_id_options) {
		if (isset($crm_id_options['contact_id'])) {
			$url = add_query_arg('contact_id', $crm_id_options['contact_id'], $url);
			$crm_id = iFoundCrm::new_hookless()->crm_id($crm_id_options);
			if ($crm_id) {
				$url = add_query_arg('aid', $crm_id, $url);
			}
		}
		return $url;
	}

	public function make_street_address($listing) {
		$field_names = ['StreetNumber', 'StreetDirPrefix', 'StreetName', 'StreetSuffix'];
		$values = array_map(function($field_name) use ($listing) {
			return $listing->$field_name;
		}, $field_names);
		$filtered_values = array_filter($values);
		$street_address = join(' ', $filtered_values);
		return $street_address;
	}

	public function maybe_geocode_campaign($save_this_id) {
		$post = get_post($save_this_id);
		if ($post->post_type !== iFoundSaveThis::$the_post_type) {
			return null;
		}
		$input = $post->params ?: [];
		if (isset($input['nearby']) && !isset($input['nearby']['lat'])) {
			$contact_id = $post->contact_id;
			$address_fields = iFoundJointContact::new_hookless()->get_address_fields($contact_id);
			if ($address_fields['address']) {
				$ifound_geo = iFoundJointContact::new_hookless()->maybe_geocode_contact($contact_id);
				$address = iFoundAddress::new_hookless()->build_address($address_fields, ['for_geocoding' => true]);
				$lat_lng = iFoundGeo::new_hookless()->get_lat_lng($ifound_geo);
				$input['nearby']['lat'] = $lat_lng['lat'];
				$input['nearby']['lng'] = $lat_lng['lng'];
				$input['nearby']['address'] = $address;
				update_post_meta($save_this_id, 'params', $input);
			}
		}
		return $input;
	}

	public function user_has_role($user_id, $role_name) {
		if (!$user_id) {
			return false;
		}
		$user = get_userdata($user_id);
		// This shouldn't happen, but could due to a bug, like one time where I forced the post_author of a post to an
		// ID of a user that didn't exist.
		if (!$user) {
			return false;
		}
		$roles = $user->roles;
		if ($roles && in_array($role_name, $roles)) {
			return true;
		}
		return false;
	}

	public function user_has_admin_role($user_id) {
		return $this->user_has_role($user_id, 'administrator');
	}

	public function user_has_admin_or_super_role($user_id) {
		return $this->user_has_admin_role($user_id) || is_super_admin($user_id);
	}

	public function get_html( $dir, $name ) {
		return file_get_contents( plugin_dir_path( __FILE__ ) . '../includes/startup/' . $dir .$name . '.html' );
	}

	// Get the host this code is running on.
	public function get_host() {
		$home_url = home_url();
		$parsed_url = parse_url($home_url, PHP_URL_HOST);
		if ($parsed_url) {
			return $parsed_url;
		}

		// The above should always work, even when run from the command line (e.g. for cron). But we can keep this as a
		// backup.
		// From https://stackoverflow.com/a/8909559/135101
		$possibleHostSources = array('HTTP_X_FORWARDED_HOST', 'HTTP_HOST', 'SERVER_NAME', 'SERVER_ADDR');
		$sourceTransformations = array(
			"HTTP_X_FORWARDED_HOST" => function($value) {
				$elements = explode(',', $value);
				return trim(end($elements));
			}
		);
		$host = '';
		foreach ($possibleHostSources as $source)
		{
			if (!empty($host)) break;
			if (empty($_SERVER[$source])) continue;
			$host = $_SERVER[$source];
			if (array_key_exists($source, $sourceTransformations))
			{
				$host = $sourceTransformations[$source]($host);
			}
		}

		// Remove port number from host
		$host = preg_replace('/:\d+$/', '', $host);

		return trim($host);
	}

	public function is_monetization_project() {
		return iFound::new_hookless()->does_config_array_val_include('monetization_domains', $this->get_host());
	}

	// I often want to debug in production by using error_log. However, with our plugin used on multisites with hundreds
	// of sites, I don't want the code to run for all sites. Also, at least currently on our web host PHP error log
	// page, the time resolution is one minute, and error_log statements issued within that minute seem to appear in a
	// random order. By writing out the timestamp with microsecond resolution, we can determine the real order.
	// So here is a method that can aid. It is expected that the $host variable will be changed manually during debug
	// time in production.
	public function error_log_for_host($message, $show_count = true) {
		global $error_log_for_host_count;

		$error_log_for_host_count++;

		$host = '';
		if ($this->get_host() === $host) {
			$datetime_utc = new \DateTime('now', new \DateTimeZone('UTC'));
			$datetime_string = $datetime_utc->format('Y-m-d H:i:s.u') . 'Z';
			$count_string = $show_count ? $error_log_for_host_count . ':' : '';
			$m = "$datetime_string: $count_string $message";
			error_log($m);
		}
	}

	// Turn absolute hrefs (links that start with a slash) into full blown URLs with scheme and host by grabbing them
	// from $_SERVER if necessary.
	// Assumes $href is not null or the empty string.
	public function ensure_href_is_url($href) {
		// If href starts with a single slash, not followed by a second, we consider it an absolute href and must turn
		// it into a URL. Otherwise, we consider it already a URL.
		if (substr($href, 0, 1) === '/') {
			if (strlen($href) >= 2) {
				if (substr($href, 1, 1) === '/') {
					// This is a scheme relative URL. Return as is.
					return $href;
				}
			}
			// Prepend $href with scheme and host
			return untrailingslashit(site_url()) . $href;
		}
		// At this point, $href doesn't start with /, e.g. it starts with http, mailto:, etc.
		return $href;
	}

	/**
	 * Is Site Admin
	 *
	 * Check to see if curent user is a site admin.
	 *
	 * @since 3.0.0
	 * @since 3.0.2 Check is_super_admin, the super admin role does not have our special crm capabilities assigned.
	 *
	 * @return bool $is_site_admin True is current user is an admin. False for all other roles.
	 */
	public function is_site_admin($user_id = null) {
		if ($user_id !== null) {
			$user = get_userdata($user_id);
			$is_administrator = in_array('administrator',  $user->roles);
			return $is_administrator || is_super_admin($user_id);
		}
		$is_administrator = in_array('administrator',  wp_get_current_user()->roles);
		// Reminder: we used to check is_admin() here. I don't think that makes sense, because that function merely
		// checks if the page route is under /wp-admin, not if the user is an admin. I'm leaving this comment in the
		// chance that it was necessary.
		return $is_administrator || is_super_admin();
	}

	// This handles the problem in PHP where, if you add 1 month to 2010-10-31, you get 2010-12-01, when of course you
	// want 2010-11-30. PHP calls it overflowing dates.
	// Discussed at https://www.php.net/manual/en/datetime.examples-arithmetic.php in the section titled
	// "Example #3 Adding or subtracting times can over- or underflow dates".
	// We will finesse it for only the scenario of adding months. Other than that, we just forward the request to
	// strtotime().
	// Limitations:
	// * Only handles addition, not subtraction
	// * Only handles adding 11 months or fewer
	public function strtotime_improved($datetime, $base_timestamp = null) {
		$orig_answer = strtotime($datetime, $base_timestamp);
		$matches = null;
		if (preg_match('/^\+?(?P<num>\d+) months?$/i', $datetime, $matches)) {
			$months_to_add = intval($matches['num']);
			$base_orig_month_num = intval(date('m', $base_timestamp));
			$month_num_should_be = $base_orig_month_num + $months_to_add;
			if ($month_num_should_be > 12) {
				$month_num_should_be -= 12;
			}
			$new_month_num_from_orig_answer = intval(date('m', $orig_answer));
			if ($month_num_should_be !== $new_month_num_from_orig_answer) {
				$improved_datetime_string = "last day of +{$months_to_add} months";
				return strtotime($improved_datetime_string, $base_timestamp);
			}
		}
		return $orig_answer;
	}

	public function array_find($array, $callback) {
		foreach ($array as $value) {
			$found = call_user_func($callback, $value);
			if ($found) {
				return $value;
			}
		}
		return null;
	}

	// This is like lodash's pick.
	// $obj can be an array or an object.
	public function pick($obj, $fields_list) {
		$val = null;
		$type = gettype($obj);
		if ($type === 'object') {
			$val = new stdClass();
			foreach ($fields_list as $field) {
				$val->$field = $obj->$field ?? null;
			}
		} else if ($type === 'array') {
			$val = [];
			foreach ($fields_list as $field) {
				$val[$field] = $obj[$field] ?? null;
			}
		} else {
			throw new \InvalidArgumentException('Object of type ' . $type . ' is not valid');
		}
		return $val;
	}

	public function build_post_href($post_id, $link_type, $options = []) {
		$options = wp_parse_args($options, [
			'ensure_href_is_url' => false,
		]);
		$link = '';
		if ($link_type === 'view') {
			$link = "/save-this/{$post_id}/";
		} else if ($link_type === 'edit') {
			$link = admin_url("post.php?post={$post_id}&action=edit");
		} else {
			return new WP_Error("{$link_type} is not a supported link type");
		}
		if ($options['ensure_href_is_url']) {
			$link = $this->ensure_href_is_url($link);
		}
		return $link;
	}

	public function get_datetime_mysql_format($datetime) {
		return DateTime::createFromFormat(static::$mysql_date_format, $datetime);
	}

	public function is_easy_name_a_days_ago_field($easy_name) {
		$days_ago_field_names = array('days_back', 'days_market', 'days_updated' );
		$is_days_ago_field = in_array($easy_name, $days_ago_field_names, true);
		return $is_days_ago_field;
	}

	public function split_on_comma($val) {
		$split_val = preg_split('/,\s*/', $val, -1, PREG_SPLIT_NO_EMPTY);
		return $split_val;
	}

	// Is at least one item in both arrays?
	public function in_both_arrays($array1, $array2) {
		$intersection = array_intersect($array1, $array2);
		return !!count($intersection);
	}

	// This is array_map() but allows both value and key in the callback.
	public function array_map_vk($fn, $array) {
		$mapped = [];
		foreach ($array as $key => $value) {
			$mapped[] = $fn($value, $key);
		}
		return $mapped;
	}

	// This is array_map() for associative arrays, allowing you to modify the key and the value. The callback should
	// return an array (tuple) of [new key, new value].
	public function array_map_modify_both($fn, $array) {
		$mapped = [];
		foreach ($array as $key => $value) {
			[$new_key, $new_value] = $fn($key, $value);
			$mapped[$new_key] = $new_value;
		}
		return $mapped;
	}

	public function get_link_regex() {
		// This is based on the regex I used in tests/ifound/crm/includes/ClassIfoundEmailTest.php
		$url_regex = '#https?://[^,\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|/))#';
		return $url_regex;
	}

	public function fetch_json($url, $args = [], $http_method = 'GET') {
		$body = $this->fetch($url, $args, $http_method);
		$json = json_decode($body, true);
		return $json;
	}

	// Use wp_remote_request(), but throw exceptions. You should only use this if you wrap with try/catch, or you know
	// there is an appropriate try/catch in the call chain, otherwise Wordpress might swallow it.
	public function fetch($url, $args = [], $http_method = 'GET') {
		$args['method'] = $http_method;
		$response = wp_remote_request($url, $args);
		$this->throw_if_wp_error($response);
		$code = wp_remote_retrieve_response_code($response);
		// I'm considering a 300-level response an error because it should have been followed, and I'm not going to
		// handle following here.
		if ($code >= 300) {
			$message = wp_remote_retrieve_response_message($response);
			$body = wp_remote_retrieve_body($response);
			$inner_exception = new Exception($body, $code);
			throw new Exception("Error (HTTP {$code}): {$message}", $code, $inner_exception);
		}
		$body = wp_remote_retrieve_body($response);
		return $body;
	}

	public function quote_elements($arr) {
		return array_map(function($x) { return "'$x'"; }, $arr);
	}

	// I got this initially from https://stackoverflow.com/a/53882337/135101
	public function array2csv($data, $headers = [], $delimiter = ',', $enclosure = '"', $escape_char = "\\") {
		$f = fopen('php://output', 'w');
		if ($headers) {
			fputcsv($f, $headers, $delimiter, $enclosure, $escape_char);
		}
		foreach ($data as $item) {
			fputcsv($f, $item, $delimiter, $enclosure, $escape_char);
		}
		return stream_get_contents($f);
	}

	// Note: it's static.
	// Copied from the WP 6.1 source code. It was only added to WP as of 5.3.
	public static function wp_timezone_string() {
		$timezone_string = get_option( 'timezone_string' );

		if ( $timezone_string ) {
			return $timezone_string;
		}

		$offset  = (float) get_option( 'gmt_offset' );
		$hours   = (int) $offset;
		$minutes = ( $offset - $hours );

		$sign      = ( $offset < 0 ) ? '-' : '+';
		$abs_hour  = abs( $hours );
		$abs_mins  = abs( $minutes * 60 );
		$tz_offset = sprintf( '%s%02d:%02d', $sign, $abs_hour, $abs_mins );

		return $tz_offset;
	}

	// Note: it's static.
	// Copied from the WP 6.1 source code. It was only added to WP as of 5.3.
	public static function wp_timezone() {
		return new DateTimeZone( static::wp_timezone_string() );
	}

	// Note: it's static.
	// Copied from the WP 6.1 source code. It was only added to WP as of 5.3.
	public static function current_datetime() {
		return new DateTimeImmutable( 'now', static::wp_timezone() );
	}

	// From: https://stackoverflow.com/a/25149337/135101
	public function date_interval_to_seconds($dateInterval) {
		$reference = new DateTimeImmutable;
		$endTime = $reference->add($dateInterval);

		return $endTime->getTimestamp() - $reference->getTimestamp();
	}

	public function throw_if_wp_error($maybe_wp_error) {
		if (is_wp_error($maybe_wp_error)) {
			$error_code = $maybe_wp_error->get_error_code();
			$error_message = $maybe_wp_error->get_error_message();
			throw new Exception("Error ({$error_code}): {$error_message}");
		}
	}

	// I got this from: https://stackoverflow.com/a/11040612
	public function in_array_any(array $needles, array $haystack): bool {
		return array_intersect($needles, $haystack) !== [];
	}

	/**
	 * Get Cron Time
	 *
	 * Get the next cron PHP time with GMT offset applied.
	 *
	 * @since 2.5.2
	 * @since 2.5.3 Move to iFOUND_activity class
	 *
	 * @param  string $time_of_day The time of day in PHP date format H:i:s
	 * @return int    $cron_time   The PHP time for the next cron run.
	 */

	public static function get_cron_time( $time_of_day ) {

		$timestamp   	= current_time( 'Y-m-d' ) . ' ' . $time_of_day;
		$c_timestamp 	= current_time( 'Y-m-d' ) . ' ' . current_time( 'H:i:s' );

		$time        	= strtotime( $timestamp );
		$current_time 	= strtotime( $c_timestamp );

		$next_time 		= ( $time < $current_time ) ? ( $time + 86400 ) : $time;
		$cron_time		= ( $next_time - ( get_option( 'gmt_offset' ) * 3600 ) );

		return $cron_time;

	}

	public function respond_with_error($msg) {
		wp_send_json_error(['message' => $msg], 400);
		die();
	}

	// This is the opposite of parse_url(). It builds a URL from parts, i.e. from the output of parse_url().
	// I got it from https://stackoverflow.com/a/31691249/135101
	public function unparse_url(array $parsed): string {
		$pass      = $parsed['pass'] ?? null;
		$user      = $parsed['user'] ?? null;
		$userinfo  = $pass !== null ? "$user:$pass" : $user;
		$port      = $parsed['port'] ?? 0;
		$scheme    = $parsed['scheme'] ?? "";
		$query     = $parsed['query'] ?? "";
		$fragment  = $parsed['fragment'] ?? "";
		$authority = (
			($userinfo !== null ? "$userinfo@" : "") .
			($parsed['host'] ?? "") .
			($port ? ":$port" : "")
		);
		return (
			(strlen($scheme) > 0 ? "$scheme:" : "") .
			(strlen($authority) > 0 ? "//$authority" : "") .
			($parsed['path'] ?? "") .
			(strlen($query) > 0 ? "?$query" : "") .
			(strlen($fragment) > 0 ? "#$fragment" : "")
		);
	}

	public function get_table_name($table_name) {
		global $wpdb;

		return $wpdb->prefix . $table_name;
	}

	public function sanitize_text($text) {
		return wp_kses(trim($text), []);
	}

	public function sanitize_textarea($text) {
		return wp_kses($text, []);
	}

	public function get_percent_str($numerator, $denominator) {
		if (!$denominator) {
			return 'N/A';
		}
		$num = round(($numerator / $denominator) * 100);
		return $num . '%';
	}

	public function get_option_name_for_user_id($user_id, $option_name) {
		return $option_name . '_user:' . $user_id;
	}

	public function get_transient_for_user_id($user_id, $transient_name) {
		$option_name = $this->get_option_name_for_user_id($user_id, $transient_name);
		return get_transient($option_name);
	}

	public function set_transient_for_user_id($user_id, $transient_name, $value, $expiration = 0) {
		$option_name = $this->get_option_name_for_user_id($user_id, $transient_name);
		return set_transient($option_name, $value, $expiration);
	}

	public function delete_transient_for_user_id($user_id, $transient_name) {
		$option_name = $this->get_option_name_for_user_id($user_id, $transient_name);
		delete_transient($option_name);
	}

	public function is_advanced_search_page() {
		$regex_string = '#^/listing-search/#';
		$did_match = preg_match($regex_string, $_SERVER['REQUEST_URI']);
		return $did_match;
	}

	public function remove_leading_whitespace($str) {
		$new_str = preg_replace('/^\s+/m', '', $str);
		return $new_str;
	}

	public function is_user_agent_bot($options = []) {
		$options = wp_parse_args($options, [
			'include_google' => true,
		]);
		$bot_user_agents_array = [
			'spider',
			'crawler',
			'bot',
			'facebook',
		];
		if ($options['include_google']) {
			$bot_user_agents_array[] = 'googleother';
		}
		$bot_user_agents_str = join('|', $bot_user_agents_array);
		$user_agent_lowercase = strtolower($_SERVER['HTTP_USER_AGENT'] ?? '');
		$is_bot = preg_match("/{$bot_user_agents_str}/", $user_agent_lowercase);
		return $is_bot;
	}

	// We are having trouble with Facebook bot hitting our servers too hard. This is how we'll prevent them
	// from crawling our PDPs.
	public function is_user_agent_facebook() {
		$user_agent_lowercase = strtolower($_SERVER['HTTP_USER_AGENT'] ?? '');
		$is_bot = preg_match("/facebook/", $user_agent_lowercase);
		return $is_bot;
	}

	// We define "visiting hours" as between 8 am and 8 pm, website local time.
	public function is_during_visiting_hours() {
		$start_time_as_interval = new DateInterval('PT8H');
		$end_time_as_interval = new DateInterval('PT20H');
		$current_datetime = iFoundUtil::current_datetime();
		$start_datetime = $current_datetime->modify('today')->add($start_time_as_interval);
		$end_datetime = $current_datetime->modify('today')->add($end_time_as_interval);
		$is_between_visiting_hours = $current_datetime > $start_datetime && $current_datetime < $end_datetime;
		return $is_between_visiting_hours;
	}

	// Return the next start of visiting hours, which is today at 8 am or if it's past that, tomorrow 8 am.
	public function get_next_visiting_hours_start() {
		$start_time_as_interval = new DateInterval('PT8H');
		$current_datetime = iFoundUtil::current_datetime();
		$start_datetime = $current_datetime->modify('today')->add($start_time_as_interval);
		if ($current_datetime > $start_datetime) {
			return $start_datetime->modify('tomorrow')->add($start_time_as_interval);
		}
		return $start_datetime;
	}

	// Originally from https://www.php.net/manual/en/function.array-change-key-case.php#124285
	// I modified it to make recursive calls using $this.
	public function array_change_key_case_recursive($arr, $case = CASE_LOWER) {
		return array_map(function($item) use($case) {
			if(is_array($item))
				$item = $this->array_change_key_case_recursive($item, $case);
			return $item;
		},array_change_key_case($arr, $case));
	}
}
