
#====================================================================================================================
# Core WordPress config

URL = wordpress.test 
TITLE = iFoundAgent Development Site
DBNAME = wordpress

wait-db:
	@ /utils/wait-for-it.sh wpdb:3306 -t 0

#setup: download config install plugins-cleanup themes-cleanup permalinks denali-activate
setup: db-create db-grant install permalinks 

activate: denali-activate gf-activate

download:
	wp core download

config:
	wp core config --dbhost=wpdb --dbuser=wordpress --dbpass=wordpress --dbname=$(DBNAME) 

db-create:
	mysql -h wpdb -u root -pwordpress -e "CREATE DATABASE IF NOT EXISTS $(DBNAME)"

db-grant:
	mysql -h wpdb -u root -pwordpress -e "GRANT ALL PRIVILEGES ON $(DBNAME).* TO 'wordpress'@'%'"

# Remove extra themes
themes-cleanup:
	rm -r wp-content/themes/twenty*

# Remove extra plugins
plugins-cleanup:
	rm -r wp-content/plugins/hello.php wp-content/plugins/akismet

install:
	wp core install --url=$(URL) --title="$(TITLE)" --admin_user=admin --admin_password=admin --admin_email=<EMAIL> --skip-email

# permalink structure
permalinks:
	wp rewrite structure /%postname%/

fix-https:
	wp search-replace https://$(URL) http://$(URL)

#====================================================================================================================
# Shared

# Activate theme
denali-activate:
	wp theme activate denali

gf-activate:
	wp plugin activate gravityforms

#====================================================================================================================
# TODO: Move these to profoundwp directory
#
# We expect certain mounts from the `docker-compose.yml` file for the `pf-setup` service/container

REMOTEDB = ifoundid_wrdp1
BLOGID = 2



# Activate plugins
pf-plugins:
	wp plugin activate login-with-ajax
	wp plugin activate widget-shortcode
	wp plugin activate use-shortcodes-in-sidebar-widgets
	wp plugin activate profoundmls

# database dumps
pf-db-setup:
	@ echo "Starting database imports. This will take a while."
	gunzip -c /tmp/dbdumps/$(REMOTEDB)-options.sql.gz | mysql -u root -pwordpress -h wpdb $(DBNAME)
	gunzip -c /tmp/dbdumps/dev-login-ajax.sql.gz | mysql -u root -pwordpress -h wpdb $(DBNAME)
	gunzip -c /tmp/dbdumps/$(REMOTEDB)-wp.sql.gz | mysql -u root -pwordpress -h wpdb $(DBNAME)
	@ echo "Finished database imports!"

pfmls-opts:
	wp --allow-root option set ProFoundMLSAdminOptions --format=json < /tmp/dbdumps/profoundmlsadminoption

# Update custom link home button to point to docker hostname
pf-menu:
	wp menu item update 786 --link=http://$(URL)

# Delete testimonial section from front page
pf-widget-delete:
	wp widget delete text-14

# Change featured listings widget to show more properties
pf-feat-listings:
	wp widget update text-11 --title="Featured Listings" --text="[ProFoundMLSSearchListings pfmls_version=\"5\" mls_class=\"res\" query=\"city=Chandler;list_price&gt;=100,000;list_status IN Active\" items_per_page=\"6\" show_map=\"false\" show_paging=\"false\" show_sorting=\"false\" show_save_search=\"false\" zero_results_message=\"Sorry, the specified listing(s) category you're looking for isn't available.\"]"

# Add full size image in place of responsive slider on houseschandleraz...
pf-fix-image:
	WP_CLI_STRICT_ARGS_MODE=1 wp widget add media_image front-page-1 --url="http://$(URL)/wp-content/uploads/revslider/home-slider/3.jpg" --size=full

pf-fix-uploads:
	wp search-replace 'sites/$(BLOGID)' ''

OLDDOMAIN = houseschandleraz.com 

pf-fix-header:
	wp search-replace $(OLDDOMAIN) $(URL)

#====================================================================================================================
# ifound plugin setup

pf-setup: wait-db
	make URL=profoundwp.test DBNAME=profoundwp setup activate pf-plugins pf-db-setup pf-menu pf-widget-delete pfmls-opts pf-feat-listings pf-fix-image pf-fix-uploads pf-fix-header fix-https

if-setup: wait-db
	make URL=ifoundwp.test DBNAME=ifoundwp REMOTEDB=ifoundsi_wpdb2 BLOGID=3 OLDDOMAIN=scottsdalelatestlistings.com setup pf-db-setup activate ifound-activate pf-fix-uploads pf-fix-header
	# This is a hack to run WP-CLI again, because the site in production is using URLs from the base domain ... :-(
	make URL=ifoundwp.test OLDDOMAIN=ifoundsites.com pf-fix-header 
	make URL=ifoundwp.test fix-https

ifound-activate:
	wp plugin activate ifound
	wp plugin activate revslider
	wp plugin activate strong-testimonials
