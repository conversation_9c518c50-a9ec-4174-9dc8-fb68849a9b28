# Deployment

## Old IDX Server (PHP)

To push the latest changes & pull them into the `~/prod` directory on the server, run:

    make release

## IDX Server (node.js)

To deploy the latest changes, and restart the node.js upstart service on the server, run:

    make fullrel

## WordPress Plugin

To deploy the latest WordPress plugin code, run:

	make wpplugin-deploy

### Details

The build step uses `git archive` to build a raw zip file of the code for a particular commit (defaults to `HEAD`), unzips that file, compiled the coffeescript and stylus files in to JavaScript and CSS, removes the `stylus` and `coffee` directories from the folder, and re-zips the folder in to the deployment zip file.

This then logs into the server via SSH and runs the `mls_deploy` target in `/home/<USER>/Makefile`, which copies the plugin files to all diretories where it is installed, via `rsync`.
