.main_area {	padding: 0;	margin: 0;}#yp_hotel {	margin: 0 0 0 0;	padding: 0 0 0 20px;	background: url('../images/yp/yp_hotelbar.jpg') repeat-x;	overflow: hidden;	text-align: left;	border-left: 1px #e1ddd1 solid;	border-right: 1px #e1ddd1 solid;	height: 35px;}* html #yp_hotel {	height: 1%;	overflow: visible;}#yp_hotel li {	list-style: none;	float: left;	margin: 0;	padding-right: 20px;	min-height: 35px;	font-size: 11px;	color: #757575;	line-height: 35px;}* html #yp_hotel li {	height: 35px;}#yp_hotel li h1 {	margin: 0;	padding: 0;	font-size: 18px;	color: #676662;}#yp_hotel li .yp_fld {	border: 1px #ccc solid;	background: #fff;	font-size: 11px;	padding: 2px;	color: #757575;}#yp_hotel li .yp_sel {	border: 1px #ccc solid;	background: #fff;	font-size: 11px;	color: #757575;}#yp_hotel li.yp_date {	padding-right: 5px;}#yp_business {	margin: 1px 0 0 0;	padding: 0 0 0 20px;	overflow: hidden;	text-align: left;	height: 33px;}* html #yp_business {	height: 33px;	overflow: visible;}#yp_business li {	list-style: none;	float: left;	margin: 0;	padding-right: 20px;	min-height: 33px;	font-size: 11px;	color: #757575;}* html #yp_business li {	height: 33px;}#yp_business li h1 {	margin: 0;	padding: 0;	font-size: 18px;	color: #fff;	line-height: 33px;}#yp_business li h1 a {	color: #fff;	text-decoration: underline;}#yp_business li .yp_fld {	border: 1px #ccc solid;	background: #fff;	font-size: 11px;	padding: 2px;	color: #757575;	margin-top: 7px;}#yp_business li.yp_power {	padding: 0 0 0 30px;}#yp_featured {	min-height: 50px;	overflow: hidden;	background: #feed95;	border: 3px #ffc000 solid;	margin: 2px 0 17px 0;	clear: both;}* html #yp_featured {	height: 50px;	overflow: visible;}#yp_700banner {	margin: 2px 0 17px 0;	clear: both;	text-align: center;}h2 {	font-size: 14px;	color: #565656;	margin: 7px 0 20px 0;	padding: 0;}#yp_results {	float: left;	margin-left: 14px;	width: 628px;}#yp_results .result_header {	height: 25px;	background: #ccc;	font-weight: bold;	font-size: 11px;	line-height: 25px;	padding: 0 6px 0 6px;}#yp_results .result_entry {	border: 1px #ccc solid;	vertical-align: top;}#yp_results .result_entry img {	border: 0px solid;}#yp_results .result_entry td {	height: 85px;	border-top: 1px #ccc solid;	padding: 10px;	vertical-align: top;}#yp_related {	margin-left: 654px;}#yp_related #related_categories {	margin: 0 0 15px 12px;	padding: 0;	font-size: 11px;}#yp_related #related_categories li.top {	list-style: none;	margin-bottom: 10px;	font-weight: bold;}#yp_related #related_categories li {	list-style: none;	line-height: 16px;}#yp_related #related_categories li a {	color: #565656;	text-decoration: underline;}#yp_results p {	margin: 0;	padding: 0;	line-height: 16px;	font-size: 11px;}#yp_results a {	color: #ff542c;}#yp_results .no_underline {	text-decoration: none;}#yp_results .title {	font-size: 12px;}#yp_results .redtitle {	font-size: 14px;	font-weight: bold;	color: #b31e3f;}#yp_featured table {	padding: 5px 10px 5px 10px;}#yp_featured table img {	float: left;	margin-right: 10px;	border: 0px solid;}.yp_rating img {	vertical-align: text-bottom;}#yp_tiles {	border: 1px #ccc solid;	border-bottom: 0px solid;	margin: 0 0 15px 0;	padding: 0;}#yp_tiles li {	margin: 0;	padding: 0;	list-style: none;	background: #efefef;	min-height: 50px;	overflow: hidden;	border-bottom: 1px #ccc solid;	padding: 5px 3px 5px 3px;	font-size: 11px;}* hml .yp_tiles li {	height: 50px;	overflow: visible;}#yp_tiles li a {	color: #565656;}#yp_tiles li .tile_title {	font-size: 15px;	font-weight: bold;}#yp_tiles li .redtitle {	color: #b31e3f;}#yp_tiles li p {	margin: 0;	padding: 0;	line-height: 18px;}#yp_tiles li p.black {	color: #000;}form {	padding: 0;	margin: 0;}#yp_browse {	margin: 0 0 20px 0;	padding: 0;}#yp_browse li.first {	font-weight: bold;	margin-bottom: 3px;}#yp_browse li {	line-height: 16px;	list-style: none;	font-size: 11px;}#yp_browse li a {	color: #565656;}.browse_title {	font-size: 16px;	margin: 0 0 8px 0;	padding: 0;	font-weight: bold;	display: block;}.browse_image {	float: left;	margin: 0 15px 18px 0;}.browse_row {	margin-bottom: 20px;}#yp_search_box {	margin: 20px 0 30px 0px;	padding: 5px 8px 5px 8px;	background: url('../images/yp/yp_search.jpg') no-repeat top left;	width: 603px;	height: 109px;}#yp_search_box .search1 {	float: left;	width: 185px;}#yp_search_box .search2 {	float: left;	width: 185px;}#yp_search_box .search3 {	float: left;	width: 100px;	margin-top: 16px;}#yp_search_box .popular {	clear: both;	font-size: 11px;	padding: 8px 0 0 0;}.paginate {	font-family: Arial, Helvetica, sans-serif;	font-size: 11px;}a.paginate {	border: 1px solid #ccc;	padding: 2px 6px 2px 6px;	text-decoration: none;	color: #000080;}a.paginate:hover {	background-color: #fff;	color: #000;	text-decoration: underline;}a.current {	border: 1px solid #ccc;	font: bold 11px Arial, Helvetica, sans-serif;	padding: 2px 6px 2px 6px;	cursor: default;	background: #efefef;	color: #FFF;	text-decoration: none;}span.inactive {	border: 1px solid #999;	font-family: Arial, Helvetica, sans-serif;	font-size: 11px;	padding: 2px 6px 2px 6px;	color: #999;	cursor: default;}#listing_details {	margin: 10px 5px 10px 5px;	font-size: 11px;}.listing_type {	font-size: 11px;	margin: 0 0 15px 0;	padding: 0;}.listing_type span {	color: #ff472b;}.listing_left {	float: left;	width: 470px;	margin-right: 15px;}.listing_left img {	border: 0px solid;}.listing_left .logo {	margin-bottom: 10px;}.listing_left h1 {	font-size: 16px;	margin: 0 0 2px 0;	padding: 0;}.listing_left p {	margin: 0 0 5px 0;	padding: 0;	line-height: 14px;}.listing_left .business {	padding-bottom: 5px;	margin-bottom: 5px;	border-bottom: 1px #f9f9f9 solid;}.listing_left .sub1 {	float: left;	width: 120px;}.listing_left .sub2 {	float: left;	width: 470px;}.listing_middle {	float: left;	width: 315px;}.listing_right {	float: right;	width: 128px;}#listing_icons {	padding: 0;	margin: 0;	float: left;	background: #f9f9f9;	height: 22px;}#listing_icons li {	float: left;	padding: 0;	list-style: none;	margin: 0 15px 0 0;}#listing_icons li.site {	padding: 0 5px 0 0;	margin: 0;}#listing_icons li.directions {	padding: 0 0 0 5px;	line-height: 22px;	margin: 0;	background: #fff;}#listing_icons li.directions a {	color: #565656;}.listing_info {	clear: both;	padding: 20px 0 20px 0;}.listing_info .title {	color: #000;	font-size: 12px;	display: block;	margin-bottom: 3px;}.listing_info .info_block {	margin: 0 0 40px 0;}.listing_info .info_block_small {	margin: 0 0 15px 0;}.get_directions {	margin: 5px 0 5px 0;}.get_directions img {	float: left;	margin-right: 5px;}.get_directions a {	color: #565656;}#reviews {	margin: -33px 0 0 0;	padding: 0;	color: #000;}#reviews li {	margin: 0 0 15px 0;	padding: 0;	list-style: none;}.listing_rate {	float: right;}.listing_rate p {	line-height: 14px;}.listing_rate a {	color: #565656;}#map_canvas {	width: 315px;	height: 300px;	border: 1px solid #6C5E4D;}.sub_par1 {	float: left;	width: 320px;}#custom_links {	margin: 3px 0 0 0;	padding: 0;	float: left;	clear: both;}#custom_links li {	list-style: none;	margin-right: 5px;}#custom_links li a {	color: #565656;}#yp_channel {	}#yp_channel .result_header {	height: 25px;	background: #ccc;	font-weight: bold;	font-size: 11px;	line-height: 25px;	padding: 0 6px 0 6px;}#yp_channel .result_entry {	border: 1px #ccc solid;	vertical-align: top;}#yp_channel .result_entry img {	border: 0px solid;}#yp_channel .result_entry td {	height: 85px;	border-top: 1px #ccc solid;	padding: 10px;	vertical-align: top;}#yp_channel p {	margin: 0;	padding: 0;	line-height: 16px;	font-size: 11px;}#yp_channel a {	color: #ff542c;}#yp_channel .no_underline {	text-decoration: none;}#yp_channel .title {	font-size: 12px;}#yp_channel .redtitle {	font-size: 14px;	font-weight: bold;	color: #b31e3f;}.imgcode {	position: absolute;	width: 0;	height: 0;	border-width: 0;}