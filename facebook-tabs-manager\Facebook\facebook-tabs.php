<?
/**
 * 	Facebook App Tabs
 *
 *
 * 	URL FOR SEARCH APP TAB				
 * 	http://www.facebook.com/dialog/pagetab?app_id=371250183235723&next=https://ifoundagent.com/wp-content/Facebook/facebook-tabs.php?search=true
 *
 *
 * 	URL FOR HOME VALUE APP TAB				
 * 	http://www.facebook.com/dialog/pagetab?app_id=1631030900533241&next=https://ifoundagent.com/wp-content/Facebook/facebook-tabs.php?home_value=true
 *
 */
 
session_start();

require_once dirname( __FILE__ ) . '/autoload.php';

/** For Search Tabs */
if(	$_GET['search']	){
	
	$fb = new Facebook\Facebook([
	  	'app_id' 				=> '371250183235723',
	  	'app_secret' 			=> '6d58d5efff50dd42503d3684c7b8720a',
	  	'default_graph_version'	=> 'v2.8',
	]);
	
	/** For Search Tabs */
	$location = array(
		/**  Page ID => https Destination Page URL */
		'1642099072734529' => 'https://azhteam.com/property-search/', //AZ Home Team
		'745152005598270' => 'https://westphxhomes.com/property-search/', //Steve Bauman 
		'336139249836205' => 'https://realtorslavica.com/property-search/',
		'234586323302135' => 'https://brianmagpantay.com/property-search/',
		'332884910251402' => 'https://propertiesofarizona.com/property-search/', //Carolyne Baker
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url'
	);

/** For Home Value Tabs */
} elseif( $_GET['home_value'] ){
	
	$fb = new Facebook\Facebook([
	  	'app_id' 				=> '1631030900533241',
	  	'app_secret' 			=> '88b9efa87fde34275902bee9ffd4dcfe',
	  	'default_graph_version'	=> 'v2.8',
	]);
	
	/** For Home Value Tabs */
	$location = array(
		/**  Page ID => https Destination Page URL */
		'1642099072734529' => 'https://azhteam.com/do-you-know-the-value-of-your-home/', //AZ Home Team
		'745152005598270' => 'https://westphxhomes.com/home-values/', //Steve Bauman 
		'336139249836205' => 'https://realtorslavica.com/do-you-know-the-value-of-your-home/',
		'234586323302135' => 'https://brianmagpantay.com/do-you-know-the-value-of-your-home/',
		'332884910251402' => 'https://propertiesofarizona.com/property-search/', // Carolyne Baker
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url',
		'page_id' => 'page_url'
	);

}

if( isset( $location ) ) {
	
	// Obtain a signed request entity from the page tab
	$helper = $fb -> getPageTabHelper();
	$page_id = $helper -> getPageId();

	if( isset( $page_id ) ) {
		
		header( "Location: " . $location[ $page_id ] );
		
	} else die( 'No page is available at this time.' );
	
} else die( 'No page is available at this time.' );

die();
?>

