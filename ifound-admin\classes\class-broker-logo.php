<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

 
class <PERSON><PERSON><PERSON><PERSON><PERSON> extends iFoundAdmin {

	private $post_type		= 'broker_logo';
	private $label_name 	= 'Broker Logo';
	private	$label_names	= 'Broker Logos';
	protected $blog_id		= 7;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	 
	public function __construct() {
		
		add_action( 'init', array( $this, 'broker_logo_post_type' ) );

		add_filter( 'manage_broker_logo_posts_columns' , array( $this, 'add_broker_logo_columns' ) );
		add_action( 'manage_broker_logo_posts_custom_column' , array( $this, 'broker_logo_column'), 10, 2 );
			
	}

	public function broker_logo_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title', 'thumbnail' )
			)
	  	);
		
	}

	public function add_broker_logo_columns( $columns ) {
		
		return array_merge( 
			array( 'cb' 					=> __( 'checkall', 'ifound' ) ),
			array( 'title' 					=> __( 'Title', 'ifound' ) ),
			array( 'broker_logo' 			=> __( 'Broker Logo', 'ifound' ) ),
			array( 'taxonomy-stylesheet' 	=> __( 'Style Sheet', 'ifound' ) )
		);
	
	}

	public function broker_logo_column( $column, $id ) { 

		if ( $column == 'broker_logo' ) {
	
			ob_start(); ?>

			<div><? 

				$featured_img_url = get_the_post_thumbnail_url( get_the_ID(),'full' ); ?>

				<div><image src="<? echo esc_url( $featured_img_url ); ?>" class="broker-logo"/></div>

			</div><?

			echo ob_get_clean();

		}
		
	}

}
