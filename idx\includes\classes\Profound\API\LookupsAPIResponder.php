<?php

// This API is responsible for returning RETS lookups and options.

namespace Profound\API;

use \LookupsQuery;

class LookupsAPIResponder extends APIResponder {
	protected function getDefaultAPI() {
		return API::explicit("lookups", "v1.01", "json");
	}

	public function computeResponseData() {
		$mls = $_REQUEST['mls'];
		$mls_class = $_REQUEST['mls_class'];
		if (!in_array($mls, \Profound\MLS\Mls::getMlsNameAbbrevs())) {
			throw new \Exception("Invalid MLS name: ($mls)");
		}
		$idx = new LookupsQuery('../config.ini', array('lookups' => $this->api));
		return $idx->fetchLookups($mls, $mls_class);
	}
}
