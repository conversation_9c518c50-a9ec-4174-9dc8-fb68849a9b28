/*! radio-buttons-for-taxonomies 1.7.5 */
!function(a){a("#the-list").on("click","a.editinline",function(){inlineEditPost.revert();var b=inlineEditPost.getId(this);rowData=a("#inline_"+b),a(".post_category",rowData).each(function(){var c,d=a(this).text();d=""!==d.trim()?d.trim():"0";var e=d.split(",");e=e?e[0]:"0",c=a(this).attr("id").replace("_"+b,""),a("li#"+c+"-"+e).find("input:radio").first().prop("checked",!0)})}),a("#doaction, #doaction2").click(function(b){var c=a(this).attr("id").substr(2);"edit"===a('select[name="'+c+'"]').val()&&(b.preventDefault(),a(".cat-checklist").each(function(){a(this).find('input[type="radio"]').length&&(a(this).find('input[type="radio"]').prop("checked",!1),a(this).prev("input").remove())}))}),a(".radio-buttons-for-taxonomies").each(function(){var b,c,d=a(this).attr("id");b=d.split("-"),b.shift(),c=b.join("-"),a("#"+c+"-all li :radio, #"+c+"-pop li :radio").on("click",function(){var b=a(this),d=b.is(":checked"),e=b.val();a("#"+c+"-all li :radio, #"+c+"-pop li :radio").prop("checked",!1),a("#"+c+'-all li :radio[value="'+e+'"], #'+c+'-pop li :radio[value="'+e+'"]').prop("checked",d)})})}(jQuery);