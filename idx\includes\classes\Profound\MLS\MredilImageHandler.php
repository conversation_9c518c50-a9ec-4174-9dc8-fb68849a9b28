<?php

namespace Profound\MLS;

class MredilImageHandler extends ImageHandler {
	static $mlsname = "mredil";

	protected function getDbTable() {
		return "mredil_property_ResidentialProperty";
	}

	protected function getImageCountField() {
		return "PHOTOCOUNT";
	}

	protected function getListingKey() {
		return "LN";
	}

	protected function getImagePath($listingId, $imageNumber, $suffix = '') {
		return "http://mredil-images2.s3.amazonaws.com/" . $this->getImageFilename($listingId, $imageNumber, $suffix);
	}

	function getImageFilename($listingId, $imageNumber, $suffix = '') {
		// NOTICE: We are padding to 8, assuming <PERSON><PERSON><PERSON> does the same
		// We must double check when it reaches 1,000,000
		$id = str_pad($listingId, 8, '0', STR_PAD_LEFT);
		return "{$id}/IMG_{$imageNumber}{$suffix}.jpg";
	}

	protected function lookAllTables($imageCountField, $listingKey, $listing_id) {
		return "SELECT $imageCountField FROM mredil_property_ResidentialProperty WHERE $listingKey = \"$listing_id\" UNION SELECT $imageCountField FROM mredil_property_RentalHome WHERE $listingKey = \"$listing_id\" UNION SELECT $imageCountField FROM mredil_property_LotsAndLand WHERE $listingKey = \"$listing_id\"";
	}

	public function getImagePathsList($listing_id, $dbTable = '') {
		$imageCountField = $this->getImageCountField();
		$listingKey = $this->getListingKey();

		if(empty($dbTable))
			$sql = $this->lookAllTables($imageCountField, $listingKey, $listing_id);
		else
			$sql = "SELECT $imageCountField FROM $dbTable WHERE $listingKey = \"$listing_id\"";

		$result = $this->getDb()->fetchAll($sql);
		$count = $result[0][$imageCountField];
		$imagesArray = array();
		for ($i = 1; $i < $count; $i++) {
			$imgValues = array();
			$imgValues['img_url'] = $this->getImagePath($listing_id, $i);
			$imgValues['normal_url'] = $this->getImagePath($listing_id, $i);
			$imgValues['thumbnail_url'] = $this->getThumbnailImagePath($listing_id, $i);
			$imgValues['highres_url'] = $this->getHighResImagePath($listing_id, $i);
			$imgValues['description'] = "";
			$imagesArray[] = $imgValues;
		}
		return $imagesArray;
	}
}
