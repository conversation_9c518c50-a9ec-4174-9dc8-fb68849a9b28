jQuery( document ).ready( function( $ ) {
	var lat = false;
	var lng = false;
	$( '.search-nearby-button' ).on( 'click', function() {
		var startPos;
		var geoSuccess = function(position) {
			startPos = position;
			lat = startPos.coords.latitude;
			lng = startPos.coords.longitude;
			if(lat && lng){
				window.location = ifound_search_nearby.endpoint + lat + '/' + lng + '/';
			} else {
				alert( 'We were not able to find your current location. Location information is required to use this feature.' );
			}
		};
		// Reminder: To use this locally during dev, in Chrome at least, you need to tell Chrome to "unsafely treat
		// insecure origins as secure".
		// See the Testing Powerful Features section of:
		//   https://www.chromium.org/Home/chromium-security/deprecating-powerful-features-on-insecure-origins
		// which points to:
		//   chrome://flags/#unsafely-treat-insecure-origin-as-secure
		var geoError = function(e) {
			console.log('geo position error', e)
			alert('There was an error getting the geo position. Did you block access? See error console for more.')
		};
		navigator.geolocation.getCurrentPosition(geoSuccess, geoError);
	});
});
