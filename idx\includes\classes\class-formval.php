<?php
/*************************************************************************
**********       Pinion Media :: Form Validation Class      **************
**********           Updated 100828 | Version 1.5.4         **************
**********       (c) Copyright 2010 Pinion Media, llc       **************
**********              http://pinionmedia.com              **************
**************************************************************************/

require_once(dirname(__FILE__) . '/../constants.php'); //Only needed for the NOW date variable

class formval {
	

var $token_min = 3; // 3 seconds (if the form is submitted quicker than 3 seconds, probably a spider)
var $token_max = 15; // 15 minutes (if they can't complete the form in that time, the session will end anyway)


function validate($type,$required,$form_var,$cc_type='n/a'){
	
	$form_var = (!is_array($form_var))? trim($form_var) : $form_var;
	$check = false;
	if (($required =="no") && ($form_var =="")){
		$check = true;
	}
	
	switch($type){
		case 'email':
			if ((strstr($form_var,'@') != "") && (strstr($form_var,'.') != "") && ($required == "no")){
				$check = true;
			}
			if ((strstr($form_var,'@') != "") && (strstr($form_var,'.') != "") && ($required == "yes") && ($form_var != "")){
				$check = true;
			}
		break;
		
		case 'text':
			if (($required == "yes") && ($form_var != "") && isset($form_var) && !is_null($form_var)){
				$check = true;
			}
			if ($required == "no"){
				$check = true;
			}
		break;
		
		case 'number':
			if (($required == "yes") && ($form_var != "") && isset($form_var) && !is_null($form_var) && is_numeric($form_var)){
				$check = true;
			}
			if ($required == "no"){
				$check = true;
			}
		break;
		
		case 'zipcode':
			if (($required == "yes") && ((preg_match("/^[0-9]{5}-[0-9]{4}$/",$form_var,$dummy) != "0") || (preg_match("/^[0-9]{5}$/",$form_var,$dummy) != "0"))){
				$check = true;
			}
			if (($required == "no") && ((preg_match("/^[0-9]{5}-[0-9]{4}$/",$form_var,$dummy)) || (preg_match("/^[0-9]{5}$/",$form_var,$dummy)))){
				$check = true;
			}
		break;
		
		case 'credit':
			if (($required == "yes") && ($this->CCVal($form_var, $cc_type))) {
				$check = true;
			}
			if (($required == "no") && ($this->CCVal($form_var, $cc_type))){
				$check = true;
			}
		break;
		
		case 'ip':
			if (($required == "yes") && (preg_match("/^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/",$form_var,$dummy) != "0") && (strstr($form_var,'-') == "")){
				$check = true;
			}
			if (($required == "no") && (preg_match("/^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/",$form_var,$dummy) != "0") && (strstr($form_var,'-') == "")){
				$check = true;
			}
		break;
		
		case 'phone':
			$numbers = ereg_replace("[^[:digit:]]", "", $form_var); // Get rid of any non-digits
			
			if (($required == "yes") && (preg_match("/[0-9]{10,}/",$numbers,$dummy) != "0")){
				$check = true;
			}
			if (($required == "no") && (preg_match("/[0-9]{10,}/",$numbers,$dummy) != "0")){
				$check = true;
			}
			break;
			
			case 'not_email':
			if (strstr($form_var,'@') == ""){
				$check = true;
			}
		break;
		
		case 'token':
			// check the token for info matching and data
			if ($required == "yes"){
				if(is_array($form_var)){
					if($form_var[0] == $form_var[1]){ // check to see that the token from session matches the token from the form
						$token_info = unserialize(base64_decode($form_var[0]));
						$time_issue_check = (($token_info['ti']+$token_info['tm']) < NOW)? true : false; // check to see that the token_issue_date (tid) + token_minimum_date (tmd) seconds is less than NOW 
						$time_exp_check = ($token_info['te'] > NOW)? true : false; // check to see that the token_exp_date (ted) is greater than NOW
					}
				}
				if($time_exp_check && $time_issue_check){
					$check = true;
				}
			}
			if ($required == "no"){
				$check = true;
			}
		break;
		
	}
		
	return ($check);

}

function CCVal($Num, $Name = 'n/a') {

	$GoodCard = true; // Innocent until proven guilty

    $Num = ereg_replace("[^[:digit:]]", "", $Num); // Get rid of any non-digits

	// Perform card-specific checks, if applicable
    switch ($Name) {
	
    case "mcd" :
      $GoodCard = ereg("^5[1-5].{14}$", $Num);
      break;

    case "vis" :
      $GoodCard = ereg("^4.{15}$|^4.{12}$", $Num);
      break;

    case "amx" :
      $GoodCard = ereg("^3[47].{13}$", $Num);
      break;

    case "dsc" :
      $GoodCard = ereg("^6011.{12}$", $Num);
      break;

    case "dnc" :
      $GoodCard = ereg("^30[0-5].{11}$|^3[68].{12}$", $Num);
      break;

    case "jcb" :
      $GoodCard = ereg("^3.{15}$|^2131|1800.{11}$", $Num);
      break;
    }

	//  The Luhn formula works right to left, so reverse the number.
    $Num = strrev($Num);

    $Total = 0;

	for ($x=0; $x<strlen($Num); $x++) {
		$digit = substr($Num,$x,1); 
		// If it's an odd digit, double it
		if ($x/2 != floor($x/2)) {
			$digit *= 2;

	  		// If the result is two digits, add them
        	if (strlen($digit) == 2) {
         		$digit = substr($digit,0,1) + substr($digit,1,1);
			}
      	}
		// Add the current digit, doubled and added if applicable, to the Total
		$Total += $digit;
	}

	//  If it passed (or bypassed) the card-specific check and the Total is
	//  evenly divisible by 10, it's cool!
	if ($GoodCard && $Total % 10 == 0) {
	
		return true; 
		
	}else{ 
	
		return false;
		
	}
}
  
function issue_token(){
	// Create Token for Honey Pot (session)
	$token_strg = 't0k3n';
	//$token_rand = rand(0, 1000);
	$token_issue_date = NOW;
	$token_exp_date = NOW + ($this->token_max * 60); // add token_max*60sec to now date (if they can't process the form in that time, the session will end anyway)
	$token_info = array( 't' => $token_strg, 'ti' => $token_issue_date, 'te' => $token_exp_date, 'tm' => $this->token_min );
	$form_token = base64_encode(serialize($token_info));
	
	return $form_token;
}
  
} // End of Class
?>