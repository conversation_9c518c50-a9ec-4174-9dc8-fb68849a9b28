/*
 * This is a file responsible for listening to the "Show Results" button
 * And getting updated criteria and directing to the new search
 */

jQuery(function($) {
	let currUrl = window.location.href;
	if(!currUrl.includes('/listing-search/') ) return;

	$( '.map-button' ).css( 'display', 'inline-block' );
	$( '#quick-search-button' ).css( 'display', 'block' ).click(createNewSearch);

	/*
	 * Getting a new url and redirecting to it
	 */
	function createNewSearch() {
		let targetUrl = getUrl();
		window.location.href = targetUrl;
	}

	/*
	 * Assembling the new url by getting all the criteria
	 * from the search panel
	 */
	function getUrl() {
		let url = '/';
		let criteria = $( 'input.dynamic-value' );

		for(criterion of criteria) {
			criterion.name = criterion.name.replace( '[]', '' ).replace( '[min]', '_min' ).replace( '[max]', '_max' );
			url += criterion.name + '-' + criterion.value + '/';
		}
		
        const href = window.location.href;
        if(href.includes("?")){
            let urlAddress = href.split('?');
            return (urlAddress[0].replace(window.location.pathname, '') + '/listing-search' + url + '?'+urlAddress[1]);
        }

		return (window.location.href.replace(window.location.pathname, '') + '/listing-search' + url );
	}

	function getCityState(cityState) {
		let parts = cityState.split(',');
		if(parts.length !== 2) return null;
		return {
			city: parts[0],
			state: parts[1].replace(/ /g, ''), 
		};
	}
});
