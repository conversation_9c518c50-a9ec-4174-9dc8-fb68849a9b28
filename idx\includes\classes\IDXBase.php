<?php

require_once dirname(__FILE__) . '/../../vendor/autoload.php';

use \Profound\API\API;
use \Profound\Client\Access;
use \Profound\MLS\FieldMappings;
use \Profound\MLS\Mls;
use Noodlehaus\Config;

class IDXBase
{

	/**
	 * @var Instance of Zend_Db_Adapter_Pdo_Mysql used for data access
	 */
	public $db;

	/**
	 * @var Array to store authentication & access information pulled from the DB
	 */
	public $access_array;

	/**
	 * @var array Configuration parameters for the IDX
	 */
	public $config = array();

	/**
	 * @var array Mapping of standard field names to MLS-specific field names
	 */
	protected $field_map;

	/**
	 * @var string Key name of the MLS that is being used for the request
	 */
	public $mlsname;

	protected $searchAPI;
	protected $propAPI;
	protected $default_format = "json";

	/**
	 * @var Instance of Mls. Note that it's different than the 'mls' member
	 * variable that's instantiated in the IDX class.
	 */
	private $mls_obj;


	private function getConfigPath($filename = 'default.ini', $basedir = false, $optional = false) {
		return ($optional ? '?' : '') . $basedir . $filename;
	}


	private function getConfigFiles($configPath = "../", $env) {
		$basedir = dirname($configPath) . "/config/";
		if (!$env) {
			$env = getenv("PHP_ENV");
			if (empty($env))
				$env = "production";
		}

		$filenames = ['default.ini', $env . '.ini', 'local.ini', $env . '.local.ini'];
		$files = array();
		foreach ($filenames as $filename) {
			$files[] = $this->getConfigPath($filename, $basedir, strcmp('default.ini', $filename));
		}
		return $files;
	}

	/**
	 * Read config from multiple files.
	 *
	 * @var $configPath Path to the folder with the config files.
	 *      For backwards compatibility, we accept a file as well, from which
	 *      we extract the folder.
	 * @var environment which we are running. If `false`, we get from value the env.
	 *      Defaults to `production`.
	 */
	protected function readConfigs($configPath, $environment = false) {
		$conf = new Config($this->getConfigFiles($configPath, $environment));
		return $conf;
	}


	/**
	 * Read the config file and save the information; set up the DB connection
	 * @param $configfile
	 */
	public function __construct($configfile = false, $apis = array()) {
		if ($configfile) {
			$this->config = $this->readConfigs($configfile);
			$this->initDB($this->config['database']);
		}
		$this->loadBaseAPIs($apis);
	}

	protected function loadBaseAPIs($apis) {
		$base_apis = array(
			'search' => API::explicit('search', 'v1.00', $this->default_format),
			'prop' => API::explicit('prop', 'v1.00', $this->default_format),
			'lookups' => API::explicit('lookups', 'v1.00', "javascript"),
		);
		$used_apis = array_merge($base_apis, $apis);
		$this->searchAPI = $used_apis['search'];
		$this->propAPI = $used_apis['prop'];
		$this->lookupsAPI = $used_apis['lookups'];
	}

	protected function loadMLS() {
		$this->mls_obj = Mls::create(
			$this->access_array['mls'],
			array(
				'access' => new Access($this->access_array),
				'db' => $this->db,
				'propAPI' => $this->propAPI,
				'searchAPI' => $this->searchAPI,
				'lookupsAPI' => $this->lookupsAPI,
				'mls_class' => $this->mls->mls_class,
			)
		);
	}

	protected function getMls() {
		if (!$this->mls_obj) {
			$this->loadMLS();
		}
		return $this->mls_obj;
	}

	/**
	 * Initialize the DB access adapter
	 * @param $config Database connection parameters
	 */
	public function initDB($config) {
		$this->db = new Zend_Db_Adapter_Pdo_Mysql($config);
	}

	/**
	 * Re-map the DB field names for a property or list of properties
	 *
	 * @param $prop_array array List of properties or single property
	 * @param bool $list
	 * @return array List of properties or single property, with re-mapped field names
	 */
	public function reverseMap($prop_array, $list = false) {
		$mappings = $this->getFieldMappings($this->mls->mls_class);
		$props = $list ? $prop_array : array($prop_array);

		$propsCopy = array();
		foreach ($props as $i => $prop) {
			// Skip extra fields, like those included in search results
			if (!is_array($prop)) {
				$propsCopy[$i] = $prop;
				continue;
			}

			// Case 564. The original problem was that Sandicor-Paragon only has one one listing key, and the way we do this mapping here removes one of them (ListingID). The following puts it back for all MLS's that have the same 'problem'.
			if ($mappings->getByMapName('ListingKey')->mlsFieldName == $mappings->getByMapName('ListingID')->mlsFieldName) {
				$propsCopy[$i]['ListingID'] = $props[$i][$mappings->getByMapName('ListingID')->mlsFieldName];
			}

			foreach ($prop as $key => $val) {
				// Keep the images
				if ($key == 'images')
					$propsCopy[$i][$key] = $val;
				else {
					$fieldMapping = $mappings->getByMlsFieldName($key);
					if ($fieldMapping) {
						// Turns out people put all sorts of garbage for the VirtualTour field.
						// If we take their value as in, this could cause problems on our site like
						//   1. Looking bad
						//   2. Functionality breaking
						//   3. Massive security violation
						// Therefore, let's only allow values that start with 'http'. We make it https if it's not.
						// If it starts with www., we change it to https://www.
						// Note: I only investigated ARMLS. Perhaps other MLSs sanitize what's entered by agents.
						if ($fieldMapping->mapName === 'VirtualTour') {
							if (strpos($val, 'https') === 0) {
								$propsCopy[$i][$fieldMapping->mapName] = $val;
							} else {
								if (strpos($val, 'http') === 0) {
									$propsCopy[$i][$fieldMapping->mapName] = 'https' . substr($val, 4);
								} else if (strpos($val, 'www.') === 0) {
									$propsCopy[$i][$fieldMapping->mapName] = 'https://' . $val;
								} else {
									$propsCopy[$i][$fieldMapping->mapName] = '';
								}
							}
						} else {
							$propsCopy[$i][$fieldMapping->mapName] = $val;
						}
					}
				}
			}

			// Here's another situation where the field mapping duplicate hoses us. In the field mapping database right
			// now, both ListAgentID and ListingMemberSold are mapped to listing_member_shortid. It just so happens that
			// the order that the below mapping runs means that the listing_member_shortid ends up on the
			// ListingMemberSold field, and thus not ListAgentID. So I'll hard-code it to get mapped here.
			$listAgentIdFieldName = $mappings->getByMapName('ListAgentID')->mlsFieldName;
			if ($listAgentIdFieldName) {
				$propsCopy[$i]['ListAgentID'] = $prop[$listAgentIdFieldName];
			}
		}

		return $list ? $propsCopy : $propsCopy[0];
	}

	protected function getListingIDFieldName($mls_class = 'res') {
		$mls = $this->access_array['mls'];
		$sql = "SELECT $mls FROM field_mapping where MapName = 'ListingID' and mls_class = '$mls_class'";
		$field_name = $this->db->fetchOne($sql);
		return $field_name;
	}

	protected function getFieldMapping($mls_class = 'res') {
		if ($this->field_map) {
			return $this->field_map;
		}
		$mls = $this->access_array['mls'];
		$sql = "SELECT MapName, $mls FROM field_mapping where mls_class = '$mls_class'";
		$res = $this->db->fetchAll($sql);
		$map = array();
		foreach ($res as $data) {
			$map[$data['MapName']] = $data[$mls] ? $data[$mls] : '';
		}
		$this->field_map = (object) $map;
		return $this->field_map;
	}

	public function getFieldMappings($mls_class = 'res') {
		$fieldMappings = new FieldMappings(array(
			'mls' => $this->access_array['mls'],
			'mls_class' => $mls_class,
			'db' => $this->db,
		));
		return $fieldMappings;
	}

	/**
	 * Map a list of fields to the MLS specific database field names
	 *
	 * Any field names that do not have a mapping will be dropped.
	 *
	 * @param $fnames List of field names to map
	 * @return array Mapped field names
	 */
	protected function mapFieldNames($fnames) {
		$results = array();
		$map = (array) $this->getFieldMapping();
		foreach ($fnames as $fname) {
			if (array_key_exists($fname, $map)) {
				$mapname = $map[$fname];
				if ($mapname) $results[] = $mapname;
			}
		}
		return $results;
	}

	protected function getImagePath($img, $web = false) {

		$key = $img['Content-ID'];
		$id = $img['Object-ID'];

		$imgdir = $this->getImageDir($key, $web);

		# TODO: Assuming type of jpg - should look at Content-Type
		return "$imgdir/$key-$id.jpg";
	}

	protected function getImageDir($key, $web = false) {
		# TODO: a hack for ARML which has keys that end with six zeros
		if ($this->mlsname == 'armls')
			$key = substr(preg_replace('/000000$/', '', $key), 10);

		$subdir = $this->mlsname . '/' . ((int) $key % 100);
		$host = $_SERVER['HTTP_HOST'];
		$prefix = $web ? 'http://' . $host : dirname(__FILE__) . "/../..";

		return "$prefix/propimgs/$subdir/$key";
	}

	protected function getImagePathsList($listing_id, $dbTable = '') {
		$options = array('idx' => $this);
		return $this->getMls()->getImageHandler($options)->getImagePathsList($listing_id, $dbTable);
	}

	// TODO: Improve this. It's quick to write but dirty.
	protected function getPreferredImage($listing_id) {
		$image_paths_list = $this->getImagePathsList($listing_id);
		// Some listings have no images.
		if (count($image_paths_list) == 0) {
			return null;
		}
		// TODO: This method is only called by search functionality so far.
		// However, the ImageHandler classes we've written so far make
		// inquiries about used prop API. Thus we need to repeat that here.
		// We need to make all places aware of the APIs actually being used.
		return $image_paths_list[0]['normal_url'];
	}

	# TODO: this is copied from the ProFoundMLS.php plugin code - and modified a bit.
	/**
	 * Construct an address using the fields from the IDX for the given array
	 *
	 * @param array $result Array of data
	 * @return string Complete address
	 */
	protected function getAddress($result) {
		// Construct the address
		// TODO: This is a hack for now. Later when we merge our code
		// that splits out implementation per MLS, we should split this
		// out there too. Even worse, note that, currently, $result passed
		// into this method has data as determined by the IDX client meta
		// data (where you set H1, title, other 'SEO' stuff), which doesn't
		// make sense for passing around internally where we need certain
		// pieces of data and assume they exist. As of this writing, we don't
		// put the State into that SEO stuff because no one looking at e.g.
		// the ARMLS search results wonders what state the results are for.
		// Right now, PHP just issues a warning here when we try to use 'State'
		// as a key. And right now we're only using this function to call
		// out to google to get lat/long from google based on address, and
		// that works with the postal code even if state is missing. But this
		// is a huge FIXME situation: we should not allow the client's particular
		// setup to matter in internal data passing.
		// Case 598: I did an 'isset' here for state to quiet the logs of
		// warnings.
		// Update 2014-09-10: We no longer need to split out the following code
		// to specially handle SNDMLS (we don't use them anymore). SDCRCA doesn't
		// have street suffix but I just used isset() to deal with that. However,
		// I've left the warnings above as a reminder for some future system
		// overhaul.
		return implode(' ', array(
			$this->getStreetAddress($result),
			@$result['City'],
			@$result['State'],
			$result['PostalCode']));
	}

	private function getStreetAddress($result) {
		// Construct the address

        // Hack for TrendMLS, where AddressExportAllowed
        // prevents mapping by address when set to "N"
		if (@$result['AddressExportAllowed'] == "N")
			return "";
		return implode(' ', array(
			@$result['StreetNumber'],
			@$result['StreetDirPrefix'],
			@$result['StreetName'],
			@$result['StreetSuffix']));
	}


	protected function shouldGeocode() {
		return $this->getMls()->getPropertyLimiter()->shouldGeocode();
	}

	// Disabling Geocoding for now ... new IDX code doesn't have it
	protected function maybeGeocode($props) {
		// if ($this->shouldGeocode()) {
		// 	return $this->geocode($props);
		// }
		return $props;
	}

	/**
	 * Use the Google Geocoder API web server to fetch missing latitude & longitude
	 * @param $props
	 */
	public function geocode($props, $should_update = true) {
		$base_url = "https://maps.googleapis.com/maps/api/geocode/json?key={$this->apikey}&address=";
		$map = $this->getFieldMapping();

		$parray = (isset($props['ListingKey']) || isset($props['ListingID'])) ? array($props) : $props;

		$ret = array();
		foreach ($parray as &$prop) {
			//check the property_coordinates table
				try {
					$sql = "SELECT
						*
						FROM
							property_coordinates
	
						WHERE
							ListingKey = ?
					";
	
					$result = $this->db->fetchRow($sql, $prop['ListingKey']);
					
				} catch (\Exception $e) {
					echo "ERROR fetching from property coordinates table: " . $prop['ListingKey'] . " : " . $e->getMessage() . "\n";
				}

				if (isset($result['Latitude']) && isset($result['Longitude'])) {
					echo "Geocoding ". $prop['ListingKey'] . " from property_coordinates table\n";

					$prop['Latitude'] = $result['Latitude'];
					$prop['Longitude'] = $result['Longitude'];

					if ($should_update) {
						$newvals = array(
							$map->Latitude	=> $result['Latitude'],
							$map->Longitude => $result['Longitude'],
						);
						$this->saveLocation($prop, $newvals, $map);
					}

				}
				else {
						$address = $this->getAddress($prop);
						$url = $base_url . urlencode($address);
		
						if (empty($address)) {
							echo "-- The address was empty for listing MLS ID {$prop['ListingID']}\n";
							continue;
						}
		
						try {
							$contents = file_get_contents($url);
							$geo = json_decode($contents);
							$loc = null;
							if ($geo->status == 'OK') {
								$loc = $geo->results[0]->geometry->location;
							} else if ($geo->status == 'ZERO_RESULTS') {
								// Google maps said there's no result. Thus, we might as well set the lat/lon to something that's basically non-zero so it's not run again, but still looks wrong on a globe. -1/-1 should work.
								$loc = (object) array('lat' => -1, 'lng' => -1);
							} else {
								echo "FAIL: " . $address . " - " . $geo->status . "\n";
								print_r($geo);
							}
		
							if ($loc) {
								$prop['Latitude'] = $loc->lat;
								$prop['Longitude'] = $loc->lng;
								if ($should_update) {
									$newvals = array(
										$map->Latitude	=> $loc->lat,
										$map->Longitude => $loc->lng,
									);

									$this->saveLocation($prop, $newvals, $map);
								}

								$propcoords = array(
									'address' => $address,
									'ListingKey' => $prop['ListingKey'],
									'Latitude' => $loc->lat,
									'Longitude' => $loc->lng
								);

								$this->savePropertyCoordinates($propcoords);
								// Dump out the address & coordinates
								echo $prop['ListingKey'] . " : " . $address . " : " . $loc->lat . " " . $loc->lng . "\n";
							}
						} catch (\Exception $e) {
							echo "ERROR: " . $address . " : " . $e->getMessage() . "\n";
						}
					

				}
			#}
			$ret[] = $prop;
		}
		return (isset($props['ListingKey'])) ? $ret[0] : $ret;
	}

	/**
	 * save location to idx database
	 */
	public function saveLocation($prop, $newvals, $map) {
		// Case 598. Based on Zen documentation, "If you provide an
		// array of arrays as the third argument, the values will be
		// automatically quoted into the keys".
		// See: http://framework.zend.com/manual/1.12/en/zend.db.adapter.html

		$where["`{$map->ListingKey}` = ?"] = $prop['ListingKey'];
		$this->db->update($this->mls->db_table, $newvals, $where);
	}

	public function savePropertyCoordinates($propcoords) {
		echo "Saving property to property coordinates table: " . $propcoords['ListingKey'] . "\n";
		$this->db->insert("property_coordinates", $propcoords);
		
	}
}