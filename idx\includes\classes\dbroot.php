<?php
/*******************************************************************
**********        Pinion Media :: DB Root Class       **************
**********        Updated 100930 | Version 1.5.1      **************
**********    (c) Copyright 2010 Pinion Media, llc    **************
**********           http://pinionmedia.com           **************
********************************************************************/


require_once("PEAR.php");
require_once("DB.php");
define("DB_CONNECT_ERROR","A DATABASE CONNECTION COULD NOT BE ESTABLISHED");
define("DB_CONNECT_ERROR_NUM",1);

//start db connect class
class dbroot extends PEAR {



function dbroot(){
	
	// Get the DB connection settings from the config file
	$config = parse_ini_file(dirname(__FILE__) . '/dbconfig.ini');
	extract($config);
	 
	//$dsn = "mysql://UserName:Password@localhost/DBName";
	$this->dsn = $protocol . '://' . $username . ':' . $password . '@' . $host . '/' . $dbname;

	$this->PEAR();
	$this->connect = DB::connect($this->dsn);
	if (DB::isError($this->connect)) {    
		//header("location:/error.php?db_error=true");
		return  $this->raiseError(DB_CONNECT_ERROR, DB_CONNECT_ERROR_NUM);
	}
	
}




function _dbroot(){
	$this->_PEAR();
}




function process($action, $sql, $result_id=NULL){
	
	$results = $this->connect->query($sql);
	
	switch($action){
		case 'get': // ---------------------------------------------------------------------------
		// Usage: return $this->process('get', $sql);             // returns the record values in a multi-dimensional numbered array (the record id will the array keys)
		//        return $this->process('get', $sql, $result_id); // Option: returns a single level numbered array of a single records values (the record id will be missing, but it's known via the $result_id)
			
			if (!DB::isError($results)){
				if (DB::isError($num = $results->numRows())){
					return $this->raiseError("couldn't get number of records $sql", $num->getMessage());
				}
				if ($num){
					if(DB::isError($return_array = $this->connect->getAssoc($sql))) {
						return $this->raiseError("couldn't build assoc array $sql", $return_array->getMessage());
					}
					//print_r($return_array);
					$results->free();
					if($result_id>0){
						$return_array[$result_id]['id'] = $result_id;
						return $return_array[$result_id];
					}else{
						return $return_array;
					}
				}else{ 
					return False;
				}
			}else{
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}
		
		break;
		
		
		case 'get_assoc': // ---------------------------------------------------------------------------
		// Usage: return $this->process('get_assoc', $sql);             // returns the record values in a multi-dimensional associative array (the record id will the array keys)
		//        return $this->process('get_assoc', $sql, $result_id); // Option: returns a single level associative array of a single records values (the record id will be missing, but it's known via the $result_id)
			
			if (!DB::isError($results)){
				if (DB::isError($num = $results->numRows())){
					return $this->raiseError("couldn't get number of records $sql", $num->getMessage());
				}
				if ($num){
					if(DB::isError($return_array = $this->connect->getAssoc($sql, true, array(), DB_FETCHMODE_ASSOC))) {
						return $this->raiseError("couldn't build assoc array $sql", $return_array->getMessage());
					}
					//print_r($return_array);
					$results->free();
					if($result_id>0){
						$return_array[$result_id]['id'] = $result_id;
						return $return_array[$result_id];
					}else{
						return $return_array;
					}
				}else{
					return False;
				}
				
			}else {
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}
		
		break;
		
		
		case 'get_count': // -------------------------------------------------------------------------
		// Usage: return $this->process('get_count', $sql); // returns the number of records from a query. However, this does not 
		//                                                     account for left joins and possible duplicate records returned
			
			if (!DB::isError($results)){
				if (DB::isError($num = $results->numRows())){
					return $this->raiseError("couldn't get number of records $sql", $num->getMessage());
				}
				if ($num){
					$results->free();
					return ($num > 0)? $num :  0; //$num; //$return_array;
				}else{ 
					return 0;
				}
			}else{
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}
		
		break;
		
		
		case 'get_count_unique': // ---------------------------------------------------------------------------
		// Usage: return $this->process('get_count_unique', $sql); // same as 'get_count', but returns the number of records from a query 
		//                                                            accounting for left joins in possible duplicate records returned
		
			if (!DB::isError($results)){
				if (DB::isError($num = $results->numRows())){
					return $this->raiseError("couldn't get number of records $sql", $num->getMessage());
				}
				if ($num){
					if(DB::isError($return_array = $this->connect->getAssoc($sql))) {
						return $this->raiseError("couldn't build assoc array $sql", $return_array->getMessage());
					}
					//print_r($return_array);
					$results->free();
					return (is_array($return_array))? count($return_array) : 0; //$num; //$return_array;
				}else{ 
					return 0;
				}
			}else{
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}
		
		break;
		
		
		case 'put': // ---------------------------------------------------------------------------
		// Usage: return $this->process('put', $sql);             // put the query into the database 
		//        return $this->process('put', $sql, $result_id); // Option: pass this the $result_id in so that it can be in the success return
			
			if (DB::isError($results)){
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}else{
				if( ($this->connect->affectedRows() > 0) && (is_numeric($result_id))){ // Pass through the $result_id
					return $result_id;
				}elseif($this->connect->affectedRows() > 0){
					return true;
				}else{
					return false;
				}
			}
		
		break;
		
		
		case 'update': // ---------------------------------------------------------------------------
		case 'delete': // ---------------------------------------------------------------------------
		// Usage: return $this->process('update', $sql); // update the query in the database 
		//        return $this->process('delete', $sql); // delete the query from the database 
			
			if (!DB::isError($results)){
				if($this->connect->affectedRows() > 0){;
					//return $this->connect->affectedRows();
					return true;
				}else{
					return false;
				}
			}else{
				return $this->raiseError("couldn't execute qurey $sql", $results->getMessage());
			}
		
		break;
		
		
	}
	
} // End process function


}// end class

?>