<?php



class serial {
	
	
var $pair_array = array();
	
	
	
public function set_var($var, $val){
	$this->pair_array[$var] = $val;
}
	
	
	
public function set_array($var_array=NULL){
	$this->pair_array = (is_array($var_array))? $this->pair_array + $var_array : $this->pair_array;
}




public function get_serialized($array_vars){
	return base64_encode(serialize($array_vars));
}




public function get_unserialized($serialized){
	return unserialize(base64_decode($serialized));
}
	
	
	
public function get_serialized_array($var_array=NULL){
	
	$this->pair_array_out = (is_array($var_array))? $this->pair_array + $var_array : $this->pair_array;
	
	return $this->get_serialized($this->pair_array_out);
}



public function get_unserialized_array($var, $in_array){
	if($in_array[$var]){
		$serial_array = $this->get_unserialized($in_array[$var]);
		return (is_array($serial_array))? $in_array+$serial_array : $in_array;
		//print "<pre>"; print_r($action_info); print "</pre>";
	}else{
		return $in_array;
	}
}
	
	
	
	
	
} // end get_cleanup class

?>