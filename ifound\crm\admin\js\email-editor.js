jQuery(document).ready(function($) {

	var is_tinymce_active;

	$( '.edit-email-wrapper .template_id' ).on( 'change', function() {
		$.ajax( {
			url: email_editor.endpoint + $( this ).val(),
			headers: {
				accept: 'application/json',
			},
			beforeSend: function ( xhr ) {
				xhr.setRequestHeader( 'X-WP-Nonce', email_editor.nonce );
			},
		} ).done( function ( response ) {
			setItem( response.content );
			$( '#email-subject' ).val( response.subject );
		} );
	});

	function setItem(x){
		// @link stackoverflow.com/questions/1179136/way-to-check-whether-tinymce-is-active-in-wordpress
		is_tinymce_active = false;
		is_tinymce_active = (typeof tinyMCE != "undefined") && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden();
		if ( is_tinymce_active === true ) {
			tinyMCE.activeEditor.setContent(x);
		} else {
			$('#ifound_email_editor').val( x );
		}
	}

    $( '.merge-tag-select' ).on( 'change', function() {
        var merge_tag = $( this ).val();
        if( $( this ).val().length )
            appendItem( merge_tag );
    });

	function makeTrackableLink(contactId, link, text, isCustom) {
		var url = window.location.origin + '/ifoundcampaign?c=' + contactId + '&d=' + (isCustom ? 1 : 0) + '&f=' + encodeURIComponent(link);
		return '<a href="' + url + '">' + text + '</a>';
	}

	$( '.activity-tracking-link-select' ).on( 'change', function() {
		// Use setTimeout so the dropdown rolls back up before we show alerts or prompts.
		setTimeout((function() {
			function insertLink(contactId, link, text, isCustom) {
				if (!link || !link.match('^https?://')) {
					alert('Links must start with http:// or https://');
					return;
				}
				var trackableLink = makeTrackableLink(contactId, link, text, isCustom);
				appendItem(trackableLink);
			}

			var contactId = $('#contact_id').val();
			if (contactId) {
				var val = $( this ).val();
				if (val === 'ifound_activity_tracking_link') {
					var link = prompt('Paste your link');
					if (link !== null) {
						insertLink(contactId, link, link, true);
					}
				} else {
					var data = JSON.parse(decodeURIComponent(val));
					insertLink(contactId, data.link, data.title, false);
				}
			} else {
				alert('You must first select a contact from the dropdown list');
			}
			$(this).val('');
		}).bind(this), 0)
	});

	function appendItem(x){
		is_tinymce_active = false;
		is_tinymce_active = (typeof tinyMCE != "undefined") && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden();
		if ( is_tinymce_active === true ) {
    		tinyMCE.activeEditor.execCommand( 'mceInsertContent', false, x );
		} else {
			$('#ifound_email_editor').val( $( '#ifound_email_editor' ).val() + x );
		}
	}


	$(window).on('ifound:chose-contact-address', function() {
		var contactId = $('#contact_id').val();
		if (!contactId) {
			return;
		}

		var spinner_div = $('.to_emails_spinner');
		$.ajax({
			url: lookup_contact.endpoint,
			type: 'get',
			data: {
				action: 'lookup_contact',
				lookup_contact_nonce: lookup_contact.nonce,
				contact_id: contactId,
			},
			beforeSend: function() {
				spinner_div.removeClass('hidden');
			},
			success: function(response) {
				var vals = JSON.parse(response);
				$('.no_contact_selected_warning').hide();
				window.ifound_populateEmailsTo(vals.emails);
				window.ifound_populateToSmss(vals.mphones);
				window.ifound_handleMayText(vals.mayTextVals);
			},
			complete: function() {
				spinner_div.addClass('hidden');
			},
		});
	});
});
