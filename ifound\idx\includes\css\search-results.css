@charset "UTF-8";
/* CSS Document */

/* Search Results */

.search-results-before-criteria,
.search-results-after-criteria,
.search-results-after-map,
.search-results-after-results {
	margin: 40px 0;
}

.search-results-before-criteria {
	margin-top: 0;
}

.search-results-after-results {
	margin-bottom: 0;
}

.price-stats {
	background: #f5f5f5;
	margin-bottom: 40px;
	padding: 10px 20px;
	border: 1px #ddd solid;
}

.price-stats .price {
	font-size: 1.4em;
}

.price-stats .total span {
	font-weight: normal;
	color: #d92228;
}

.price-stats .items {
	display: flex;
	flex-wrap: wrap;
}

.price-stats .stats-data {
	flex: 1 1 150px;
}

.price-stats .stats-data .hint {
	font-size: small;
}

.price-stats .stats-data .heading {
	font-weight: bold;
	float: initial;
	white-space: nowrap;
}

.price-stats .stats-data .facets .heading {
	font-size: smaller;
}

.stats-data {
	background: #fff;
	margin: 2px;
	padding: 0 8px;
	color: #000;
	border: 1px solid #ccc;
}

.stats-data .heading {
	border-bottom: 1px solid #ccc;
	margin-bottom: 5px;
}
.facets .heading {
	line-height: 1;
	border-bottom: none;
	margin-bottom: 0;
}
.stats-data .facet {
	line-height: 1;
	margin-bottom: 15px;
}

.ifound_trends .ifound_trends_chart {
	margin-top: 20px;
}

.price-stats button.hide-show {
	background-color: transparent;
	color: black;
	border: 1px black solid;
	padding: 5px 10px;
}

.price-stats button.hide_show_stats {
	display: none;
}

.ifound-results {
	border-bottom: #ccc 1px solid;
	font-size: 18px;
	padding: 10px 0;
}

.ifound-results h2,
.ifound-results h2 span {
	font-size: 20px;
}

.ifound-results h2 a {
	text-decoration: none;
}

.ifound-results h2 span {
	font-size: 18px;
}

.ifound-results .results-image {
	float: left;
	width: 25%;
}

.ifound-results .results-image a {
	display: block;
    width: 100%;
    position: relative;
	/* Why was this here? I'm removing it because it's making the virtual tour link about half height */
    /*height: 0;*/
    padding: 56.25% 0 0 0;
    background: #000;
    overflow: hidden;
}

.ifound-results .results-image img {
	position: absolute;
    display: block;
    max-width: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
}

.ifound-results .results-data {
	float: left;
	padding: 0 10px;
	margin-bottom: 10px;
	width: 51%;
}

.ifound-results .results-buttons {
	float: right;
	width: 20%;
}

.ifound-results .results-buttons .button {
	box-sizing: border-box;
	display: block;
	font-size: 18px;
	letter-spacing: normal;
	padding: 12px;
	text-align: center;
	width: 100%;
}

.ifound-results .save-this {
	margin-top: 10px;
}

.broker-name {
	font-size: 16px;
	max-width: 290px;
	overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ifound-results .beds:before,
.ifound-results .baths:before,
.ifound-results .sqft:before {
	font-family: 'Font Awesome 5 Pro';
    content: "\f236";
    margin-right: 8px;
    color: #565656;
    vertical-align: middle;
}

.ifound-results .baths:before {
    content: "\f2cd";
}

.ifound-results .sqft:before {
    content: "\f047";
}

.ifound-results .beds span,
.ifound-results .baths span {
	display: none;
}

.ifound-results .list_price {
	color: #d92228;
	font-size: 30px;
}

.ifound-results .beds,
.ifound-results .baths,
.ifound-results.zoom-marker .beds,
.ifound-results.zoom-marker .baths {
    float: left;
    text-align: center;
    margin-right: 5px;
    border-right: 1px solid #777;
    padding-right: 5px;
    width: auto;
}

.ifound-results.zoom-marker .beds,
.ifound-results.zoom-marker .baths {
	width: 25%;
}

.ifound-results .listing-id {
	font-size: 16px;
}

/* Results Map */

.results-map {
	display: none;
	width: 100%;
	height: 100%;
}

.black-button {
	background-color: #000;
}

.button-invisible,
.map-and-palette-wrapper {
        display: none;
}

.ifound-results-map {
	width: 100%;
	height: 500px;
	max-height: 100%;
}

/* Property Popup */

.ifound-results.zoom-marker .results-image,
.ifound-results.zoom-marker .results-data,
.ifound-results.zoom-marker .results-buttons {
	float: none;
	width: 100%;
}

.ifound-results.zoom-marker h2,
.ifound-results.zoom-marker h2 span {
	margin: 0 0 10px;
	text-align: center;
}

.ifound-results.zoom-marker .results-data {
	text-align: center;
}

.results-section .ifound-results.zoom-marker {
	display: block;
	position: fixed;
	top: 10%;
	width: 300px;
	max-width: 300px;
	max-height: 90%;
	margin: 0 auto;
	padding: 4px;
	overflow: scroll;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 0 10px rgba(0,0,0,.5);
	z-index: 9999;
}

.zoom-marker-close {
	display: none;
	position: relative;
	cursor: pointer;
	text-align: right;
}

.zoom-marker .zoom-marker-close {
	display: block;
}

/* Pagination */

.ifound-pagination {
	background: #f5f5f5;
	padding: 10px;
	margin-top: 40px;
}

.ifound-pagination .button.current {
	background-color: #000;
}

.ifound-pagination .button {
	margin-bottom: 3px;
}

@media all and (max-width: 960px) {
  
	.ifound-results .results-buttons {
		clear: both;
		float: none;
		width: 100%;
	}
	
	.results-buttons .ifound-wrap {
		display: inline-block;
	}
	
	.ifound-results .results-buttons .button {
		display: inline-block;
		width: auto;
	}
	
	.ifound-results .results-image {
		max-width: 300px;
		width: 50%;
	}

	.ifound-results .results-data {
		width: 50%;
	}
  
 }
 
@media all and (max-width: 700px) {
  
	.ifound-results .results-image {
		float: none;
		max-width: 100%;
		width: 100%;
	}

	.ifound-results .results-data {
		float: none;
		padding: 0;
		width: 100%;
	}
  
}
 
@media all and (max-width: 480px) {
	  
	.ifound-results .results-buttons .button {
		width: 100%;
	}
	
	.results-buttons .ifound-wrap {
		display: block;
	}

	.ifound-grid .ifound-results.zoom-marker h2 {
    	margin-top: 0;
    }

    .results-section .ifound-results.zoom-marker {
        max-width:none;
        width: auto;
        top: 2%;
        bottom: 2%;
        left: 2%;
        right: 2%;
        overflow: scroll;
    }

	.price-stats .items {
		display: none;
	}
	.price-stats button.hide_show_stats {
		display: initial;
	}
}

/* Grid Results */
.ifound-grid .ifound-results {
	box-shadow: 0 1px 1px rgba(0,0,0,.1);
	border: 1px solid #e5e5e5;
    display: inline-block;
    margin: 5px 2px;
    padding: 4px;
    vertical-align: top;
    width: calc(33% - 7px);
}

.ifound-grid .ifound-results .results-image,
.ifound-grid .ifound-results .results-data,
.ifound-grid .ifound-results .results-buttons {
	float: none;
	width: 100%;
}

.ifound-grid .ifound-results .results-image {
	max-width: 100%;
	position: relative;
}

.ifound-grid .ifound-results h2,
.ifound-grid .ifound-results h2 span {
	margin: 10px 0;
	text-align: center;
}

.ifound-grid .ifound-results .results-data {
	padding: 5px 0;
	text-align: center;
}

.ifound-grid .broker-name {
	margin: 0 auto;
}

.ifound-grid .ifound-results .beds,
.ifound-grid .ifound-results .baths,
.ifound-results .sqft,
.ifound-results .listing-id,
.ifound-featured-data .sqft,
.ifound-featured-data .listing-id {
	background: #f8f8f8;
    border: 1px solid #fff;
	float: left;
	text-align: center;
    margin-right: 0;
    padding-right: 0;
    width: 50%;
}

.ifound-grid .ifound-results .baths {
	border-right: none;
}

.ifound-grid .ifound-results .sqft {
	clear: both;
}

.ifound-results .broker-name {
	clear: both;
}

.ifound-open-house-datetime {
	position: absolute;
	bottom: 0;
	right: 0;
	background-color: #0006;
	color: #fff;
	padding: 2px 5px;
	font-size: 12px;
	font-weight: 700;
}

.ifound-virtual-tour {
	position: absolute;
	bottom: 0;
	left: 0;
	line-height: normal;
}

.ifound-virtual-tour .button.vt-link {
	border: none;
	border-radius: 0;
	display: block;
	letter-spacing: normal;
	font-size: 1rem;
	padding: 7px;
	color: #fff;
}

.ifound-grid .ifound-results {
	margin: 15px 2px;
}

@media only screen and (max-width: 960px) {

	.ifound-grid .results-buttons .ifound-wrap,
	.ifound-grid .ifound-results .results-buttons .button {
    	display: block;
	}

	.ifound-grid .ifound-results {
	    width: calc(50% - 8px);
	}

	.ifound-grid .ifound-results .results-image {
		max-width: 100%;
		width: 100%;
	}

}

@media only screen and (max-width: 600px) {

	.ifound-grid .ifound-results {
		margin: 10px 0;
	    width: 100%;
	}

	.stats-data .heading,
	.stats-data .price {
		float: left;
		font-size: 20px;
	}

	.stats-data .heading {
		margin-right: 10px;
	}

	.total h2 {
		font-size: 22px;
	}

	.subject-info h2 {
		font-size: 22px;
	}

}

/* End Grid Results */

