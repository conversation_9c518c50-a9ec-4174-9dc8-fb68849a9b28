window.iFoundGlobal = window.iFoundGlobal || {};

var $ = window.jQuery;

iFoundGlobal.syncStepsWithSelectedDripTemplate = function() {
	var $start_date = $('.start-date');
	$start_date.datepicker({
		dateFormat: 'yy-mm-dd',
		minDate: 0,
	});
	$start_date.datepicker().datepicker('setDate', new Date());

	$('.drip_template_id').on('change', function(event) {
		var id = event.target.value;
		$upcoming_step_index = $('.upcoming_step_index');
		// Clear previous choices
		$upcoming_step_index.empty();
		// Add choices
		var steps = ifound_drip_campaign.drip_template_steps_by_template_id[id];
		for (var i = 0; i < steps.length; i++) {
			$upcoming_step_index.append($('<option>', {
				value: i,
				text: i + 1,
			}));
		}
		$upcoming_step_index.val('0');

		var $possible_steps = $('.possible_steps > table > tbody');
		$possible_steps.empty();
		for (var i = 0; i < steps.length; i++) {
			var $tr = $('<tr>');
			$tr.append('<td>' + (i + 1) + '</td>');
			var customer_template =
				ifound_drip_campaign.email_template_titles_by_id[steps[i].customer_template_id] || '-';
			$tr.append('<td>' + customer_template + '</td>');
			var reminder_template =
				ifound_drip_campaign.email_template_titles_by_id[steps[i].reminder_template_id] || '-';
			$tr.append('<td>' + reminder_template + '</td>');
			var interval = steps[i].interval;
			$tr.append('<td>' + interval + '</td>');
			$possible_steps.append($tr);
		}
	});

	var $show_hide_possible_steps_button = $('.show_hide_possible_steps')
	$show_hide_possible_steps_button.on('click', function() {
		$('.possible_steps').slideToggle('slow');
		$show_hide_possible_steps_button.text(
			$show_hide_possible_steps_button.text() === 'Show possible steps'
				? 'Hide possible steps'
				: 'Show possible steps'
		);
	});
}

