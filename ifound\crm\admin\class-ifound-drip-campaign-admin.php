<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundDripCampaignAdmin {
	use UtilTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			if (is_admin()) {
				add_filter('manage_' . iFoundDripCampaign::$the_post_type . '_posts_columns', array($this, 'manage_posts_columns'));
				$shared = iFoundSharedCampaign::new_hookless();
				add_action('manage_' . iFoundDripCampaign::$the_post_type . '_posts_custom_column', array($shared, 'contact_info_column'), 10, 2);
				add_filter('manage_edit-' . iFoundDripCampaign::$the_post_type . '_sortable_columns', array($this, 'sortable_columns'));
			}
		}
	}

	public function manage_posts_columns($columns) {
		$columns['contact'] = 'Contact';
		$this->util()->move_item($columns, 'contact', 'down', 'title');
		return $columns;
	}

	public function sortable_columns($columns) {
		$columns['contact'] = 'contact';
		return $columns;
	}
}
