<?
/**
 * iFound_text_me_now class
 *
 * Display text_me_now in widget areas.
 *
 * @package iFOUND
 * @since 1.0.0
 * @since 2.4.9 Add body message to sms texts.
 *
 * @link https://stackoverflow.com/questions/6480462/how-to-pre-populate-the-sms-body-text-via-an-html-link
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


class iFound_text_me_now extends WP_Widget {
	
	private $settings;
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Mobile - Text Me Now', 
			array(
			'description' => 'Add Mobile Text Me Now to any widget area. This will only display on mobile devices.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {
		
		if ( wp_is_mobile() ) {
			
			$ifound_agent = get_option( 'ifound_agent' );

			if( empty( $ifound_agent['text_me_phone'] ) ) return;

			$button_text = empty( $ifound_agent['text_me_button'] ) ? 'Text Me' : $ifound_agent['text_me_button'];

			$phone = $ifound_agent['text_me_phone'];
			/** Using both ?& works on IOS and Android */
			$msg  = '?&body=' . rawurlencode( 'I am texting you from this page: ' . iFound::current_url() );

			echo $args['before_widget']; ?>

			<div class="ifound-text-me-now">

				<div class="widgets-wrap">

					<div class="text-me-now">

						<a class="phone-me-now-button sms button" href="sms:<? echo $phone; ?><? echo $msg; ?>">

							<i class="far fa-mobile text-me-now-icon"></i><? echo _e( $button_text, 'ifound' ); ?>
								
						</a>

					</div>

				</div>

			</div><?

			echo $args['after_widget'];
			
		}
		
	}
	
}
