jQuery( document ).ready( function( $ ) {

	$( '.update-campaign' ).on( 'click', function( e ) {

		var ID = e.target.id;
		var parent_div = $( this ).closest( '.save-this-section' );
		var existingIconClass = parent_div.find('.update-campaign').attr('class').split(/\s+/)
			.filter(function(x) {
				return x.startsWith('fa-toggle-');
			})
			[0]
		;
		var $updateCampaignHint = parent_div.find('.update-campaign-hint');

		jQuery.ajax ( {
			url : update_campaign.endpoint,
			type : 'post',
			data : {
				action : 'update_campaign',
				id : ID,
				update_campaign_nonce : update_campaign.nonce,
			},
			beforeSend: function() {
				parent_div.find('.update-campaign').removeClass('fa-toggle-on fa-toggle-off').addClass('fa-spinner fa-spin');
			},
			success: function( response ) {
				parent_div.find('.update-campaign').addClass(response.class);
				parent_div.find('.next-time').html(response.next_time);
				parent_div.find('.campaign-status').html(response.status);
				var newVisibility = existingIconClass === 'fa-toggle-off' ? 'hidden' : 'visible';
				$updateCampaignHint.css('visibility', newVisibility);
			},
			error: function(response) {
				parent_div.find('.update-campaign').addClass(existingIconClass);
				if (response && response.responseJSON && response.responseJSON.data
					&& response.responseJSON.data.message) {
					alert(response.responseJSON.data.message);
				}
			},
			complete: function(response) {
				parent_div.find('.update-campaign').removeClass('fa-spinner fa-spin');
			},
			dataType:'json'
		});
	});

})
