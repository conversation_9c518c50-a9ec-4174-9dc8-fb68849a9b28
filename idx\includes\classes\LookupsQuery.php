<?php

require_once '../vendor/autoload.php';
require_once 'IDX.php';

use Profound\MLS\Mls;
use Profound\MLS\MlsClass;

class LookupsQuery extends IDX {
	public function fetchLookups($mls, $mls_class) {
		// Set the query type for logging purposes. See case 559.
		$this->query_type = "Lookups";
		$data = $this->fetchData($mls, $mls_class);
		$response = $this->mapRelationalDataToObject($data);

		return $response;
	}

	// I named this with "Obj" to distinguish it from the $mls string we're passing around.
	protected function getMlsObj($mls) {
		$mlsObj = Mls::create($mls);
		return $mlsObj;
	}

	// Normally the mapped field name is the name of the mls. However, for
	// datasources synced by RetsCloud, all fields are prefixed with "field_".
	// However, our lookups fetching from the RETS system does not prepend. A
	// simple solution is just to cut off the 'field_' part.
	protected function getMappedFieldName($mls) {
		$prefix = $this->getMlsObj($mls)->getFieldPrefix();
		if ($prefix) {
			return "replace(fm.$mls, '$prefix', '')";
		}
		return "fm.$mls";
	}

	protected function fetchData($mls, $mls_class) {
		$sysname = $this->getMappedFieldName($mls);

		// Translate the local property class to the generic name:  Ex: A -> res
		$generic = MlsClass::getGenericMlsClassName($mls, $mls_class);
		$mls_class = $this->getLocalMlsClassFromGeneric($mls, $mls_class);

		// We order by "Value" (field on the table) to get the forced sort order
		// from the MLS. For example, this gets Seasonal Rent lookup options
		// in the intended order. They're month names, which wouldn't look right
		// in alphabetical order.
		// Hmm. This isn't perfect. For "Street type", the order seems to be
		// random, and if you really wanted to find something in the list, that
		// one is too long to not be properly alphabetized. I'm not sure which
		// is the preferred approach. I'll make it sort by LongValue
		// for now and wait for someone to give me a good business case reason
		// to change it.
		$sql = "
			SELECT
				t1.EasyName, t1.Interpretation, plo.LongValue
			FROM
				(
					SELECT DISTINCT
						fm.EasyName, ft.Interpretation, ft.LookupName
					FROM
						{$mls}_fields_{$mls_class} ft
						JOIN field_mapping fm ON fm.$mls <> '' AND fm.EasyName <> '' AND $sysname = ft.SystemName AND fm.mls_class = '$generic'
				) t1
				JOIN property_lookup_opts plo ON plo.mls = '$mls' AND plo.LookupName = t1.LookupName
			ORDER BY
				EasyName, LongValue
		";
		$results = $this->db->fetchAll($sql, array($mls));
		return $results;
	}

	// We map the relational table data to an associative so that it
	// can be turned into JSON.
	protected function mapRelationalDataToObject($data) {
		$previous_name = '';
		$previous_interpretation = '';
		$lookups = array();
		$output = array('lookups' => &$lookups);
		$current_lookup_set = null;
		$easy_name = null;
		foreach ($data as $result) {
			$easy_name = $result['EasyName'];
			$interpretation = $result['Interpretation'];
			if ($easy_name != $previous_name) {
				if ($current_lookup_set != null) {
					$lookups[$previous_name] = array(
						'values' => $current_lookup_set,
						'interpretation' => $previous_interpretation,
					);
				}
				$previous_name = $easy_name;
				$previous_interpretation = $interpretation;
				$current_lookup_set = array();
			}
			$short_value = $result['LongValue'];
			$current_lookup_set[] = $short_value;
		}
		if ($current_lookup_set != null) {
			$lookups[$previous_name] = array(
				'values' => $current_lookup_set,
				'interpretation' => $previous_interpretation,
			);
		}
		return $output;
	}
}
