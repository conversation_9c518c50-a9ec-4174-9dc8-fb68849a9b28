import React, { useState, useEffect } from 'react';
import merge from 'lodash/merge';
import axios from 'axios';
import classnames from 'classnames';
import { formatDateTime } from '../lib/datetime';
import produce from 'immer';
import useFetcher from '../hooks/useFetcher';
import urljoin from 'url-join';
import useIfoundToasts from '../hooks/useIfoundToasts';
import { makeAddress} from '../lib/address';
import { makePdpLink } from '../lib/url';
import AddOwnedListing from "./AddOwnedListing";

function OwnedListings(props) {
	const initial = merge({}, {
		owned_listings: {},
	}, props);
	const [ownedListings, setOwnedListings] = useState(initial.owned_listings);
	const [listingIdToDelete, setListingIdToDelete] = useState('');
	const [listingIdToRefresh, setListingIdToRefresh] = useState('');
	const { addErrorToast } = useIfoundToasts();

	const {
		isLoading: isFetchingDelete,
		errorMessage: errorMessageForDelete,
		isSuccess: isSuccessForDelete,
		run: runFetchDelete,
	} = useFetcher({
		fetchFn: () => axios({
			url: urljoin(props.endpoint, listingIdToDelete.toString()),
			method: 'DELETE',
			params: {
				_wpnonce: props.nonce,
			},
		})
			.then(response => {
				setOwnedListings(produce(ownedListings, draftState => {
					delete draftState[listingIdToDelete];
				}));
			})
			.finally(() => {
				setListingIdToDelete(null);
			}),
	})
	useEffect(() => {
		if (errorMessageForDelete) {
			addErrorToast(errorMessageForDelete);
		}
	}, [errorMessageForDelete])

	const {
		isLoading: isFetchingRefresh,
		errorMessage: errorMessageForRefresh,
		isSuccess: isSuccessForRefresh,
		run: runFetchRefresh,
	} = useFetcher({
		fetchFn: () => axios({
			url: urljoin(props.endpoint, listingIdToRefresh.toString(), 'refresh'),
			method: 'POST',
			params: {
				_wpnonce: props.nonce,
			},
		})
			.then(response => {
				const data = response.data;
				const ownedListing = data.owned_listing;
				setOwnedListings(produce(ownedListings, draftState => {
					draftState[listingIdToRefresh] = ownedListing;
				}));
			})
			.finally(() => {
				setListingIdToRefresh(null);
			}),
	})
	useEffect(() => {
		if (errorMessageForRefresh) {
			addErrorToast(errorMessageForRefresh);
		}
	}, [errorMessageForRefresh])

	function del(listingId) {
		if (window.confirm(`Are you sure you wish to delete MLS ID ${listingId}?`)) {
			setListingIdToDelete(listingId);
			runFetchDelete();
		}
	}

	function refreshListing(listingId) {
		setListingIdToRefresh(listingId);
		runFetchRefresh();
	}

	function displayListings(listings) {
		return (
			<table className="striped">
				<thead>
				<tr>
					<th>MLS ID</th>
					<th>Status</th>
					<th>Address</th>
					<th>Image</th>
					<th>Date Added (here)</th>
					<th>Date Updated (here)</th>
					<th>List Price</th>
					<th>Close Price</th>
					<th>Close Date</th>
					<th>Living Sqft</th>
					<th>Garage Spaces</th>
					<th>Interior Levels</th>
					<th>Subdivision</th>
					<th>Beds</th>
					<th>Baths</th>
					<th>Year Built</th>
					<th>Actions</th>
				</tr>
				</thead>
				<tbody>
				{Object.values(listings).map(x => <tr key={x.data.ListingID}>
					<td><a href={makePdpLink(x.data)} target="_blank">{x.data.ListingID}</a></td>
					<td>{x.data.ListStatus}</td>
					<td>{makeAddress(x.data)}</td>
					<td><img src={x.data.img_url} style={{ height: '67px' }} /></td>
					<td>{formatDateTime(x.created_at)}</td>
					<td>{formatDateTime(x.updated_at)}</td>
					<td>{x.data.ListPrice}</td>
					<td>{x.data.ClosePrice}</td>
					<td>{x.data.CloseDate ? formatDateTime(x.data.CloseDate) : ''}</td>
					<td>{x.data.SquareFeet}</td>
					<td>{x.data.GarageSpaces}</td>
					<td>{x.data.IntStories}</td>
					<td>{x.data.Subdivision}</td>
					<td>{x.data.Beds}</td>
					<td>{x.data.Bathrooms}</td>
					<td>{x.data.YearBuilt}</td>
					<td>
						<div style={{ display: 'flex' }}>
							<button
								type="button"
								className="button"
								onClick={() => refreshListing(x.data.ListingID)}
								disabled={isFetchingRefresh}
							>
								<i className={classnames('fas', {
									'fa-refresh': !isFetchingRefresh || x.data.ListingID !== listingIdToRefresh,
									'fa-spinner': isFetchingRefresh && x.data.ListingID === listingIdToRefresh,
									'fa-spin': isFetchingRefresh && x.data.ListingID === listingIdToRefresh,
								})}></i>
								{' '}
								Refresh
							</button>
							<button
								type="button"
								className="button"
								style={{ borderColor: 'red', color: 'red', marginLeft: '5px' }}
								onClick={() => del(x.data.ListingID)} disabled={isFetchingDelete}
							>
								<i className={classnames('fal', {
									'fa-trash-alt': !isFetchingDelete || x.data.ListingID !== listingIdToDelete,
									'fa-spinner': isFetchingDelete && x.data.ListingID === listingIdToDelete,
									'fa-spin': isFetchingDelete && x.data.ListingID === listingIdToDelete,
								})}></i>
								{' '}
								Delete
							</button>
						</div>
					</td>
				</tr>)}
				</tbody>
			</table>
		)
	}

	return <div className="ifound owned-listings">
		<div style={{ overflowX: 'auto' }}>
			{Object.keys(ownedListings).length
				? displayListings(ownedListings)
				: <div style={{ fontStyle: 'italic' }}>No listings</div>
			}
		</div>
		<hr />
		<AddOwnedListing
			endpoint={props.endpoint}
			nonce={props.nonce}
			onAdd={ownedListing => {
				setOwnedListings(produce(ownedListings, draftState => {
					draftState[ownedListing.data.ListingID] = ownedListing;
				}));
			}}
		/>
	</div>;
}

export default OwnedListings;
