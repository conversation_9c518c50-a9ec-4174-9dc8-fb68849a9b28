/* Javascript Functions written for the MPSC Patrol Log Site */



function confirm_action(messssage){
	confirm_box=confirm(messssage);
	if(confirm_box){
		return true;
	}else{
		return false;
	}
}



// Rollover Functions -----------------------------------------------------------------------------

function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}

function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
	var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
	if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
	d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}


// -------------------------------------------------------------------------------------------------

// Antispam Email Function -------------------------------------------------------------------------

function antispam(fname,lname,name,domain,tld,subject) {
	if(subject){
		link_strg="mailto:"+fname+" "+lname+" <"+name+"@"+domain+"."+tld+">?subject="+subject;
	}else{
		link_strg="mailto:"+fname+" "+lname+" <"+name+"@"+domain+"."+tld+">";
	}
	document.location=link_strg;
}


// -------------------------------------------------------------------------------------------------



function newWindow(theURL,winName,winWidth,winHeight,features) { //v2.0
	//winWidth = 460;
	//winHeight = 500;
	winName = 'popwindow';
	winOffX = (screen.width - winWidth - 22)/2;
	winOffY = (screen.height - winHeight)/2 - 50;
	features = "width=" + winWidth + ",height=" + winHeight + ",toolbar=0,menubar=0,scrollbars=0,resizable=0,location=0,directories=0,status=0,left=" + winOffX + ",top=" + winOffY;
	window.open(theURL,winName,features);
}


// -------------------------------------------------------------------------------------------------



function init_map(){
	google.maps.event.trigger(map, 'resize');
}


// -------------------------------------------------------------------------------------------------

// Antispam Email Function -------------------------------------------------------------------------
/***********************************************
* Drop Down/ Overlapping Content- � Dynamic Drive (www.dynamicdrive.com)
* This notice must stay intact for legal use.
* Visit http://www.dynamicdrive.com/ for full source code
***********************************************/

function getposOffset(overlay, offsettype){
var totaloffset=(offsettype=="left")? overlay.offsetLeft : overlay.offsetTop;
var parentEl=overlay.offsetParent;
while (parentEl!=null){
totaloffset=(offsettype=="left")? totaloffset+parentEl.offsetLeft : totaloffset+parentEl.offsetTop;
parentEl=parentEl.offsetParent;
}
return totaloffset;
}

function overlay(curobj, subobjstr, opt_position){
if (document.getElementById){
var subobj=document.getElementById(subobjstr)
subobj.style.display=(subobj.style.display!="block")? "block" : "none"
var xpos=getposOffset(curobj, "left")+((typeof opt_position!="undefined" && opt_position.indexOf("right")!=-1)? -(subobj.offsetWidth-curobj.offsetWidth) : 0)
var ypos=getposOffset(curobj, "top")+((typeof opt_position!="undefined" && opt_position.indexOf("bottom")!=-1)? curobj.offsetHeight : 0)
subobj.style.left=xpos+"px"
subobj.style.top=ypos+"px"
return false
}
else
return true
}

function overlayclose(subobj){
document.getElementById(subobj).style.display="none"
}


// -------------------------------------------------------------------------------------------------

// Auto Checkbox Filler Function -------------------------------------------------------------------------


function setboxes(the_form, the_var, do_check) {
	var elts      = (typeof(document.forms[the_form].elements[the_var]) != 'undefined')
				  ? document.forms[the_form].elements[the_var]
				  : (typeof(document.forms[the_form].elements[the_var]) != 'undefined')
		  ? document.forms[the_form].elements[the_var]
		  : document.forms[the_form].elements[the_var];
	var elts_cnt  = (typeof(elts.length) != 'undefined')
				  ? elts.length
				  : 0;

	if (elts_cnt) {
		for (var i = 0; i < elts_cnt; i++) {
			elts[i].checked = do_check;
		} // end for
	} else {
		elts.checked        = do_check;
	} // end if... else

	return true;
} // end of the 'sethidehoticeboxes()' function


// -------------------------------------------------------------------------------------------------


// Video Tab Related Functions ---------------------------------------------------------------------

/**
 * This will either hold false if nothing is being edited right now, or it will hold true if there is.
 */
var currently_editing_video = false;
/**
 * Code to simplify $.ajax calls.
 * @param params - Expects multi-dim array.
 */
function videoAction(params) {
	var args = $.extend({}, {
		url: "video-ajax.php",
		context: "document.body"
	}, params);
	$.ajax(args);
}

/**
 * If the access_id provided is null, pull it from the form on the page.
 * @param access_id
 */
function accessIdCheck(access_id) {
	if (access_id == null) {
		access_id = document.getElementById('video-form-companies').value;
	}
	return access_id;
}

/**
 * Retrieves list of property videos from server and displays them.
 * @param access_id
 */
function getvideos(access_id) {
	access_id = accessIdCheck(access_id);
	videoAction({
		data: {
			action: "fetchAll",
			id: access_id
		},
		success: function(html) {
			document.getElementById('video_content').innerHTML = html;
			getglobalvideo(access_id);
		}
	})
	return false;
}

/**
 * Retrieves the agent's global video from server and displays it.
 * @param access_id
 */
function getglobalvideo(access_id) {
	access_id = accessIdCheck(access_id);
	videoAction({
		data: {
			action: "fetchGlobalVideo",
			id: access_id
		},
		success: function(html) {
			document.getElementById('global_video_content').innerHTML = html;
		}
	})
}

/**
 * Takes form data from the page requests server to update a new property video listing with it.
 */
function addNewVideo() {
	/**
	 * TODO: validate data, sanitize data.
	 * TODO: use array for $.ajax({ data: ... })
	 */
	var video_message = document.getElementById('video_message');
	var video_form = document.getElementById('videos');
	var access_id = document.getElementById('video-form-companies').value;

	videoAction({
		data: {
			action: "add",
			id: access_id,
			MLS_id: video_form['MLS_id'].value,
			youtube_id: video_form['youtube_id'].value
		},
		success: function(html) {
			video_message.innerHTML = html;
			getvideos(access_id);
		}
	});
}

/**
 * Sends delete request to server using MLS_id.
 * @param MLS_id
 */
function removeVideo(MLS_id) {
	var access_id = document.getElementById('video-form-companies').value;
	videoAction({
		data: {
			action: "delete",
			id: access_id,
			MLS_id: MLS_id
		},
		success: function(html) {
			document.getElementById('video_message').innerHTML = html;
			getvideos(access_id);
		}
	})
}

/**
 * Asks server to remove an agent's global video.
 * @param access_id
 */
function removeGlobalVideo(access_id) {
	access_id = accessIdCheck(access_id);
	videoAction({
		data:{
			action: "removeGlobalVideo",
			id: access_id
		},
		success: function(html) {
			document.getElementById('video_message').innerHTML = html;
			getglobalvideo(access_id);
		}
	})
}

/**
 * Changes interface so that a user can edit the data.
 * @param MLS_id
 * @param youtube_id
 */
function editVideo(MLS_id, youtube_id) {
	var table_row = document.getElementById(MLS_id.toString() + youtube_id.toString());
	if (!currently_editing_video) {
		currently_editing_video = true;
		table_row.innerHTML = "<td><input type=\"text\" id=\"new_MLS_id\" value=\"" + MLS_id + "\" /></td>" +
			"<td><input type=\"text\" id=\"new_youtube_id\" value=\"" + youtube_id + "\" /></td>" +
			"<td><button type=\"button\" onclick=\"updateVideo('" + MLS_id + "','" + youtube_id + "')\">Edit</button></td>";
	} else {
		// If we're editing something refetch video stuff, repopulate screen, set flag to false, and then run again.
		/**
		 * TODO: fix this so that it will show the edit form after the AJAX is called when you click to
		 * edit another item without finishing the request... i don't feel this is super critical right
		 * now, so i'm going to leave it be.
		 */
		getvideos();
		currently_editing_video = false;
		editVideo(MLS_id, youtube_id);
	}
}

/**
 * Sends new youtube_id to server to update an agent's global video.
 * @param youtube_id
 * @param access_id
 */
function replaceGlobalVideo(youtube_id, access_id) {
	access_id = accessIdCheck(access_id);
	videoAction({
		data:{
			action: "replaceGlobalVideo",
			id: access_id,
			youtube_id: youtube_id
		},
		success: function(html) {
			document.getElementById('video_message').innerHTML = html;
			getglobalvideo(access_id);
		}
	})
}

/**
 * Sends update request to server via ajax.
 * @param old_MLS_id
 * @param old_youtube_id
 */
function updateVideo(old_MLS_id, old_youtube_id) {
	var access_id = document.getElementById('video-form-companies').value;
	var new_MLS_id = document.getElementById('new_MLS_id').value;
	var new_youtube_id = document.getElementById('new_youtube_id').value;

	currently_editing_video = false;

	videoAction({
		data:{
			action: "update",
			id: access_id,
			old_MLS_id: old_MLS_id,
			new_MLS_id: new_MLS_id,
			youtube_id: new_youtube_id
		},
		success: function(html) {
			document.getElementById('video_message').innerHTML = html;
			getvideos(access_id);
		}
	})
}

// -------------------------------------------------------------------------------------------------

/**
 * Deploy the plugin to all of the domains selected using AJAX requests to the deployment script
 */
function deployPlugin() {
	$('#deploy-btn').attr('disabled', true);
	
	var pending = 0;
	
	function checkDone() {
		if (pending === 0) {
			$('#deploy-btn').attr('disabled', false);
		}	
	}
	
	// Loop over the checked boxes and get the IDs, and deploy them with an AJAX request
	var cbs = $('#deploy_form').find('input');	
	for (i = 0; i < cbs.length; i++) {
		var cb = cbs[i];
		var id = $(cb).attr('access_id');
		if (!id) continue;		
		
		var msg = cb.checked ? '<img src="../images/process_ani.gif">' : 'Skipped';
					
		// Clear old message
		setDeployStatus({id: id, msg: msg});
		
		if (!cb.checked) continue;

		var a = $(cb).parent().next().next().find("a");
		var idx_update_url = a.attr("href") + "/?pfmls_idx_update";
		// For debug
		// idx_update_url = "http://wordpress/?pfmls_idx_update"

		pending++;
		$.post('deploy.php?id=' + id, function(data) {
				var res = JSON.parse(data);	
				setDeployStatus(res);	

				// Run our pfmls_idx_update functionality:

				$.get(idx_update_url)
					.done(function(data, textStatus, jqXHR) {
						addDeployMsg({id: id, msg: "<p>pfmls_idx_update OK</p>"});
					})
					.fail(function(data, textStatus, jqXHR) {
						addDeployMsg({id: id, msg: "<p>The ?pfmls_idx_update call for " + idx_update_url + " failed</p>"});
					})
					.always(function() {
						pending--;
						checkDone();
					})
				;				
			}
		);		

	}
}

function setDeployStatus(res) {
	var el = $('#dmsg-' + res.id);
	el.html(res.msg.toString());
}

function addDeployMsg(res) {
	var el = $('#dmsg-' + res.id);
	el.append(res.msg.toString());
}

//========================================================================================================
// IDX Admin form functions

// FIXME: remove use of "global" variable

$(function(){

	var names = ['ftp', 'access', 'cat', 'result', 'prop'];

	for (var i = 0; i < names.length; i++) {
		var name = names[i];
	
		// Add the click handler for the icon to open the settings dialog
		var el = $('a.poplight_' + name + '[href^=#]');
		el.click((function() {
			
			var fname = name;
			
			return function() {
				var accessID = $(this).attr('rel'); 
				var popID = fname + '_pop_' + accessID;
		
				// If the element doesn't exist, then create the action containers
				// And then load in the content
				if( $('#' + popID).length == 0) {
					$('body').append('<div id="' + popID + '" class="popup_block"><div id="load_container_' + popID + '"></div></div>');
				}
				$('#load_container_' + popID).empty().html('<img src="/images/process_ani.gif" />');
				$('#load_container_' + popID).load("/admin/ajax/get_form.php?form=" + fname + "&aid=" + accessID, function() {
					showDialog(popID);
					$('.button').button();
				});
			}
		})());

	}
	
	//Close Popups and Fade Layer
	$('a.close, #fade').live('click', function() { //When clicking on the close or fade layer...
		$('#fade , .popup_block').fadeOut('10', function() {
			$('#fade, a.close').remove();  //fade them both out
		});
	});
});

/**
 * Display the poplight dialog
 * 
 * Fade in the background and the dialog.  Center the dialog as soon as the fade begins.
 * @param popID DOM ID for the dialog
 */
function showDialog(popID) {

	//Fade in the Popup and add close button
	$('#' + popID).fadeIn('50').css({ width: 500 }).prepend('<a href="#" class="close"><img src="/images/icon_delete.gif" class="btn_close" title="Close Window" alt="Close" /></a>');

	//Fade in Background
	$('body').append('<div id="fade"></div>');
	$('#fade').css({'filter' : 'alpha(opacity=80)'}).fadeIn(); // 'alpha(opacity=80)'}) is used to fix IE Bug

	// Center the popup dialog
	var dlg = $('#' + popID);
	var popMargTop = dlg.height() / 2;
	var popMargLeft = dlg.width() / 2;
	dlg.css({
		'margin-top' : -popMargTop,
		'margin-left' : -popMargLeft
	});

	dlg.draggable();
	
	$("select#field_prop_opts").change(function () { 
		var field_id = $('.prop_opt_to:checked').val();
		var str = $("textarea#"+field_id).val(); 
		$("select#field_prop_opts option:selected").each(function () { 
			str += $(this).val() + " "; 
		}); 
		$("textarea#"+field_id).val(str); 
	});
	
}


function saveChanges(form) {
	// Get the form data
	var data = $(form).serialize();
	
	// Show the loading spinner
	$('#save-button').attr('disabled', true);
	$('#form-buttons').append('<img id="spinner" src="/images/process_ani.gif" />'); 

	// Submit to the server in the background
	$.post('ajax/save_form.php', data, function(resp, statusText, xhr) {
		var res = JSON.parse(resp);

		$('#spinner').remove();
		$('#save-button').attr('disabled', false);
		
		if (res.success) {
			$('.alert_msg').text("Changes saved successfully");
			
			$('#fade , .popup_block').fadeOut(function() {
				$('#fade, a.close').remove();  //fade them both out
			});
			
			// TODO: if a new customer was added, reload the page, or add a row in the table
			// TODO: if the Company Name was changed, update the table
		} else {
			alert('An Error occurred trying to save');
		}
	});
	
	return false;
}

/**
 * Check or un-check all of the boxes in the list of sites
 *
 * @param val True/false to check/un-check
 */
function setChecks(val) {
	$('#deploy_tab').find('input[type=checkbox]').attr('checked', val);
}
