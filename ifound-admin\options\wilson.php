<?
$genesis_settings = array(
	'genesis-vestige'                            => array(
		'nav_superfish'    => 1,
		'nav_home'         => 1,
		'subnav_superfish' => 1,
		'subnav_home'      => 1,
	),
	'genesis-settings'                           => get_option('genesis-settings'),
	'theme_mods_' . $this->website['stylesheet'] => array(
		'nav_menu_locations' => array(
			'primary' => $this->menu_ids['main'],
		),
		'custom_css_post_id' => -1,
	),
);


$sidebars_widgets = array(
	'sidebars_widgets' => array(
		'wp_inactive_widgets'            => array(),
		'sidebar'                        => array(
			'ifound_quick_search-2',
			'categories-3',
		),
		'before-header-1'                => array(
			'custom_html-9',
		),
		'before-header-2'                => array(
			'ifound_social_media-2',
		),
		'before-header-3'                => array(
			'custom_html-11',
		),
		'front-page-1'                   => array(
			'rev-slider-widget-2',
		),
		'front-page-2'                   => array(
			'ifound_quick_search-3',
		),
		'front-page-3'                   => array(
			'ifound_featured_listings-2',
		),
		'front-page-4'                   => array(
			'media_image-2',
			'featured-page-4',
		),
		'front-page-5'                   => array(
			'custom_html-6',
			'featured-page-5',
			'featured-page-6',
			'featured-page-7',
			'featured-page-8',
			'featured-page-9',
			'featured-page-10',
		),
		'front-page-6'                   => array(),
		'front-page-7'                   => array(
			'strong-testimonials-view-widget-2',
		),
		'front-page-8'                   => array(),
		'front-page-9'                   => array(),
		'front-page-10'                  => array(
			'custom_html-4',
		),
		'lead-capture'                   => array(),
		'footer-1'                       => array(
			'ifound_social_media-3',
		),
		'footer-2'                       => array(
			'custom_html-10',
		),
		'footer-3'                       => array(
			'media_image-3',
		),
		'after-entry'                    => array(),
		'details-before-slider'          => array(
			'ifound_whats_my_payment-2',
		),
		'details-after-slider'           => array(
			'ifound_save_this_property-2',
		),
		'after-details'                  => array(
			'ifound_text_me_now-3',
			'ifound_call_me_now-2',
		),
		'search-results-before-criteria' => array(),
		'search-results-after-criteria'  => array(),
		'search-results-after-map'       => array(
			'ifound_save_this_search-2',
		),
		'search-results-after-results'   => array(),
		'mobile-phone-me-now'            => array(
			'ifound_call_me_now-3',
			'ifound_text_me_now-3',
		),
		'array_version'                  => 3,
	),
);


$widgets = array(
	'widget_archives'                        => array(
		2              => array(
			'title'    => '',
			'count'    => 0,
			'dropdown' => 0,
		),
		'_multiwidget' => 1,
	),
	'widget_autotext'                        => array(
		'_multiwidget' => 1,
	),
	'widget_block'                           => [
		[
			'2'            => [
				'content' => '<!-- wp:search /-->',
			],
			'3'            => [
				'content' => '<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Recent Posts</h2><!-- /wp:heading --><!-- wp:latest-posts /--></div><!-- /wp:group -->',
			],
			'4'            => [
				'content' => '<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Recent Comments</h2><!-- /wp:heading --><!-- wp:latest-comments {"displayAvatar":false,"displayDate":false,"displayExcerpt":false} /--></div><!-- /wp:group -->',
			],
			'5'            => [
				'content' => '<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Archives</h2><!-- /wp:heading --><!-- wp:archives /--></div><!-- /wp:group -->',
			],
			'6'            => [
				'content' => '<!-- wp:group --><div class="wp-block-group"><!-- wp:heading --><h2>Categories</h2><!-- /wp:heading --><!-- wp:categories /--></div><!-- /wp:group -->',
			],
			'_multiwidget' => 1,
		],
	],
	'widget_calendar'                        => array(
		'_multiwidget' => 1,
	),
	'widget_categories'                      => array(
		'2'            => array(
			'title'        => '',
			'count'        => 0,
			'hierarchical' => 0,
			'dropdown'     => 0,
		),
		'3'            => array(
			'title'        => 'Recent Search Blogs',
			'count'        => 0,
			'hierarchical' => 0,
			'dropdown'     => 0,
		),
		'_multiwidget' => 1,
	),
	'widget_featured-page'                   => array(
		2              => array(
			'title'           => '',
			'page_id'         => $this->post_ids['listing-search'],
			'show_image'      => '1',
			'image_size'      => 'medium',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		3              => array(
			'title'           => '',
			'page_id'         => $this->post_ids['whats-my-home-worth'],
			'show_image'      => '1',
			'image_size'      => 'medium',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		4              => array(
			'title'           => $this->get_agent_name(),
			'page_id'         => $this->post_ids['about'],
			'image_size'      => 'thumbnail',
			'image_alignment' => 'alignnone',
			'show_content'    => '1',
			'content_limit'   => '390',
			'more_text'       => 'More about ' . $this->get_agent_name(),
		),
		5              => array(
			'title'           => $this->location[1],
			'page_id'         => $this->post_ids[$this->page_slug[1]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		6              => array(
			'title'           => $this->location[2],
			'page_id'         => $this->post_ids[$this->page_slug[2]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		7              => array(
			'title'           => $this->location[3],
			'page_id'         => $this->post_ids[$this->page_slug[3]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		8              => array(
			'title'           => $this->location[4],
			'page_id'         => $this->post_ids[$this->page_slug[4]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		9              => array(
			'title'           => $this->location[5],
			'page_id'         => $this->post_ids[$this->page_slug[5]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		10             => array(
			'title'           => $this->location[6],
			'page_id'         => $this->post_ids[$this->page_slug[6]],
			'show_image'      => '1',
			'image_size'      => 'communities',
			'image_alignment' => 'alignnone',
			'show_title'      => '1',
			'content_limit'   => '',
			'more_text'       => '',
		),
		'_multiwidget' => 1,
	),
	'widget_featured-post'                   => array(
		'_multiwidget' => 1,
	),
	'widget_gform_widget'                    => array(
		'_multiwidget' => 1,
	),
	'widget_ifound_broker_logo'              => array(
		'2'            => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_cmc_form'                 => array(
		'_multiwidget' => 1,
	),
	'widget_ifound_featured_listings'        => array(
		2              => array(''),
		3              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_property_video'           => array(
		'_multiwidget' => 1,
	),
	'widget_ifound_quick_search'             => array(
		2              => array(''),
		3              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_save_this_property'       => array(
		2              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_save_this_search'         => array(
		2              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_search_nearby'            => array(
		2              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_text_me_now'              => array(
		2              => array(''),
		3              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_call_me_now'              => array(
		2              => array(''),
		3              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_whats_my_payment'         => array(
		2              => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_social_media'             => array(
		2              => array(''),
		3              => array(''),
		'_multiwidget' => 1,
	),
	'widget_media_gallery'                   => array(
		'_multiwidget' => 1,
	),
	'widget_media_image'                     => array(
		'2'            => array(
			'size'                 => 'full',
			'width'                => 1000,
			'height'               => 1000,
			'caption'              => '',
			'alt'                  => '',
			'link_type'            => 'none',
			'link_url'             => '',
			'image_classes'        => '',
			'link_classes'         => '',
			'link_rel'             => '',
			'link_target_blank'    => false,
			'image_title'          => '',
			'attachment_id'        => 108,
			'url'                  => 'https://palisades.ifoundsites.com/wp-content/uploads/sites/1141/2022/07/agent-female.jpg',
			'title'                => '',
			'column-classes'       => 'one-half',
			'column-classes-first' => true,
		),
		'3'            => array(
			'size'              => 'full',
			'width'             => 150,
			'height'            => 76,
			'caption'           => '',
			'alt'               => '',
			'link_type'         => 'none',
			'link_url'          => '',
			'image_classes'     => '',
			'link_classes'      => '',
			'link_rel'          => '',
			'link_target_blank' => false,
			'image_title'       => '',
			'attachment_id'     => 104,
			'url'               => 'https://palisades.ifoundsites.com/wp-content/uploads/sites/1141/2021/07/broker-logo.png',
			'title'             => '',
		),
		'_multiwidget' => 1,
	),
	'widget_media_video'                     => array(
		'_multiwidget' => 1,
	),
	'widget_meta'                            => array(
		2              => array(
			'title' => '',
		),
		'_multiwidget' => 1,
	),
	'widget_metaslider_widget'               => array(
		'_multiwidget' => 1,
	),
	'widget_nav_menu'                        => array(
		2              => array(
			'title'    => 'Communities',
			'nav_menu' => $this->menu_ids['communities'],
		),
		'_multiwidget' => 1,
	),
	'widget_pages'                           => array(
		'_multiwidget' => 1,
	),
	'widget_recent-comments'                 => array(
		2              => array(
			'title'  => '',
			'number' => 5,
		),
		'_multiwidget' => 1,
	),
	'widget_recent-posts'                    => array(
		2              => array(
			'title'  => '',
			'number' => 5,
		),
		'_multiwidget' => 1,
	),
	'widget_rev-slider-widget'               => array(
		2              => array(
			"rev_slider_title" => "",
			"rev_slider"       => "1",
			"rev_slider_pages" => "",
		),
		'_multiwidget' => 1,
	),
	'widget_rss'                             => array(
		'_multiwidget' => 1,
	),
	'widget_search'                          => array(
		'_multiwidget' => 1,
	),
	'widget_strong-testimonials-view-widget' => array(
		2              => array(
			'title'  => 'Testimonials',
			'text'   => '',
			'filter' => false,
			'view'   => 1,
		),
		'_multiwidget' => 1,
	),
	'widget_tag_cloud'                       => array(
		'_multiwidget' => 1,
	),
	'widget_custom_html'                     => array(
		4  => array(
			'title'   => 'Do you know what your home is worth?',
			'content' => '<a href="/' . $this->home_worth_slug() . '/" class="button taglinebutton">Find Out</a>',
		),
		5  => array(
			'title'   => '',
			'content' => '',
		),
		6  => array(
			'title'   => 'Featured Communities',
			'content' => '',
		),
		8  => array(
			'title'   => '',
			'content' => '<h5 style="margin-top: 40px;">"' . stripcslashes($this->dev['tagline']) . '"</h5>',
		),
		9  => array(
			'title'   => '',
			'content' => $this->header_phone(),
		),
		10 => array(
			'title'   => '',
			'content' => $this->contact_info(),
		),
		11 => array(
			'title'   => '',
			'content' => $this->my_account_link(),
		),
	),
	'widget_text'                            => array(
		'1'            => array(),
		'_multiwidget' => 1,
	),
	'widget_user-profile'                    => array(
		'_multiwidget' => 1,
	),
);


$theme_settings = array_merge($genesis_settings, $sidebars_widgets, $widgets);
