<?
/**
 * iFoundAdmin Class
 *
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class iFoundAdmin extends iFoundIdx {
	use UtilTrait;
	use NewHooklessTrait;
	/**
	 * Settings.
	 *
	 * @since 1.0.0
	 * @access private
	 * @var object $settings The settings for the plugin.
	 */

	private $settings;

	// 1 is the ID of the super admin. Email templates (among other things) are assigned to the super admin, except for
	// team members. Rather than hard-code 1 in our code, we'll just point to here, so it's easier to understand what
	// the 1 refers to, as well as find places to update if the requirements change.
	public static $shared_owner_ID = 1;
	private static $primary_admin_id_option_name = 'ifound_primary_admin_id';
	public static $manage_ifound_staff_settings_capability_name = 'manage_ifound_staff_settings';
	private static $ifound_staff_settings_option_name = 'ifound_staff_settings';
	public static $ifound_broker_compensations_option_name = 'ifound_broker_compensations';

	/**
	 * init iFoundAdmin class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			if ( is_admin() ) {
				$this->mappings = $this->mappings();
				add_action( 'init', array( $this, 'settings' ), 1 );
			}
			add_action( 'admin_menu', array( $this, 'settings_menu' ) );
			add_action( 'admin_init', array( $this, 'register_settings' ) );
			add_action( 'admin_init', array( $this, 'create_bulk_pages' ) );
			add_action( 'admin_init', array( $this, 'save_property_video' ) );
			add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ) );
			add_action( 'wp_ajax_search_criteria', array( $this, 'search_criteria' ) );
			add_action( 'update_meta_box_order', array( $this, 'update_meta_box_order' ) );
			add_action( 'edit_form_top', array( $this, 'help_button_on_edit_pages' ), 10 , 1 );
			add_action( 'ifound_choices_button', array( $this, 'choices_button' ), 10 , 1 );
			add_action( 'ifound_help_button', array( $this, 'help_button' ), 10 , 1 );
			add_action( 'ifound_save_choices_button', array( $this, 'save_choices_button' ), 10 , 1 );
			add_action( 'ifound_search_criteria_options', array( $this, 'search_criteria_options' ), 10 , 2 );
			add_action( 'ifound_search_criteria_form', array( $this, 'search_criteria_form' ), 10 , 4 );
		}
	}

	/**
	 * Admin Scripts
	 *
	 * @since 1.0.8
	 */

	public function admin_scripts() {
		wp_register_script('ifound_checkbox_dropdown_js', plugins_url('js/checkbox-dropdown.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
		wp_enqueue_style( 'ifound_checkbox_dropdown_css', plugins_url( 'css/checkbox-dropdown.css', __FILE__ ), [], iFOUND_PLUGIN_VERSION );

		wp_enqueue_style( 'ifound_admin_css', plugins_url( 'css/ifound-admin.css', __FILE__ ), ['ifound_checkbox_dropdown_css'], iFOUND_PLUGIN_VERSION );

		wp_register_style( 'shortcode_dashboard_css', plugins_url( 'css/shortcode-dashboard.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'campaign_builder_css', plugins_url( 'css/campaign-builder.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		wp_register_script( 'search_criteria_js', plugins_url( 'js/search-criteria.js', __FILE__ ), array( 'jquery', 'jquery-ui-sortable' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'search_criteria_js', 'search_criteria', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'search_criteria_secure_me' )
		));
		wp_register_script( 'help_info_js', plugins_url( 'js/help-info.js', __FILE__ ), array( 'jquery', 'cookie_js' ), iFOUND_PLUGIN_VERSION );
		wp_enqueue_script('help_info_js');
		wp_enqueue_script('disable_show_default_js');
	}

	/**
	 * Settings
	 *
	 * All the settings used in the admin.
	 *
	 * @uses iFOUND::obj()
	 *
	 * @since 1.0.0
	 * @since 1.0.38 Add grid_layout to settings. This is an array( 1 => 'checked' ) if checked.
	 * @since 2.6.0 Add settings to hide contingent.
	 *
	 * @return object $settings {
	 *
 	 *     @type array $name {
	 *			The option name for the setting to store in database under.
 	 *
	 *     		@type string $title Title of options page..
	 *     		@type array $fields {
	 *				The fileds to be displayed on the options page.
	 *
	 *     			@type array $order {
	 *					The display order of the options.
	 *
	 *     					@type string $key     The key name for the setting.
	 *     					@type string $heading The heading label for the setting.
	 *     					@type string $type    The input type for the setting.
	 *     					@type int    $rows    The number of rows to show if textarea.
	 *     					@type string $script  The type of script to include for this set of fields (i.e. checkall ).
	 *     					@type bool   $values  The input has a list of values to be displayed for it.
	 *				}
	 *			}
	 * 		}
 	 * }
	 */

	public function settings() {
		$primary_admin_id = $this->get_primary_admin_id_without_override();

		$array = array(
			'ifound_agent' => array(
				'title'				=> 'Agent Info',
				'fields'			=> array(
					1 => array(
						'notes'		=> 'Data provided here is used for system features. To take full advantage of this. Please keep your information up to date.'
					),
					2 => array(
						'key'		=> 'agent_name',
						'heading'	=> 'Agent Name',
						'type'		=> 'text'
					),
					3 => array(
						'key'		=> 'team_name',
						'heading'	=> 'Business/Team Name*',
						'type'		=> 'text'
					),
					4 => array(
						'key'		=> 'broker_name',
						'heading'	=> 'Broker Name',
						'type'		=> 'text'
					),
					5 => array(
						'key'		=> 'street_address',
						'heading'	=> 'Street Address*',
						'type'		=> 'text'
					),
					6 => array(
						'key'		=> 'city',
						'heading'	=> 'City*',
						'type'		=> 'text'
					),
					7 => array(
						'key'		=> 'state',
						'heading'	=> 'State*',
						'type'		=> 'text'
					),
					8 => array(
						'key'		=> 'zip',
						'heading'	=> 'Zip*',
						'type'		=> 'text'
					),
					9 => array(
						'key'		=> 'office_phone',
						'heading'	=> 'Office Phone',
						'type'		=> 'text'
					),
					10 => array(
						'key'		=> 'mobile_phone',
						'heading'	=> 'Mobile Phone*',
						'type'		=> 'text'
					),
					31 => array(
						'key'		=> 'fax',
						'heading'	=> 'Fax*',
						'type'		=> 'text'
					),
					42 => array(
						'key'		=> 'email',
						'heading'	=> 'Email',
						'type'		=> 'text'
					),
					53 => array(
						'key'		=> 'bcc_email',
						'heading'	=> 'Bcc Email',
						'type'		=> 'text',
						'notes'		=> 'Bcc email will receive a copy of all client emails sent.'
					),
					60 => array(
						'key'			=> 'sms_phone',
						'heading'		=> 'SMS Phone',
						'type'			=> 'text',
						'placeholder' 	=> '5555555555'
					),
					80 => array(
						'key'		=> 'agent_id',
						'heading'	=> 'Agent MLS ID',
						'type'		=> 'text'
					),
					85 => array(
						'key'		=> 'broker_id',
						'heading'	=> 'Broker MLS ID',
						'type'		=> 'text'
					),
					87 => array(
						'key' 		=> 'broker_logo',
						'heading'	=> 'Broker Logo',
						'type'		=> 'text'
					),
					90 => array(
						'key' 		=> 'business_logo',
						'heading'	=> 'Business Logo*',
						'type'		=> 'text'
					),
					100 => array(
						'key' 		=> 'business_photo',
						'heading'	=> 'Business Photo*',
						'type'		=> 'text'
					),
					110 => array(
						'key' 			=> 'price_range',
						'heading'		=> 'Market Price Range*',
						'type'			=> 'text',
						'placeholder' 	=> '$100,000-$20,000,000'
					),
					120 => array(
						'notes'			=> '* Used for Google Structured Data for Local Business. <a href="https://search.google.com/structured-data/testing-tool/u/0/" target="_blank">Test Data</a>'
					),
					130 => array(
						'notes'			=> '<h3>Phone Me Now Settings</h3>',
					),
					140 => array(
						'key'			=> 'call_me_button',
						'heading'		=> 'Call Me Now Button Text',
						'type'			=> 'text',
						'placeholder' 	=> 'Type custom button text or phone # here.'
					),
					150 => array(
						'key'			=> 'call_me_phone',
						'heading'		=> 'Call Me Now Mobile Phone',
						'type'			=> 'text',
						'placeholder' 	=> '************ or ************'
					),
					160 => array(
						'key'			=> 'text_me_button',
						'heading'		=> 'Text Me Now Button Text',
						'type'			=> 'text',
						'placeholder' 	=> 'Type custom button text or phone # here.'
					),
					170 => array(
						'key'			=> 'text_me_phone',
						'heading'		=> 'Text Me Now Mobile Phone',
						'type'			=> 'text',
						'placeholder' 	=> '************ or ************'
					),
					200 => array(
						'key' 			=> 'broker_disclosure',
						'heading'		=> 'Broker Disclosure',
						'type'			=> 'textarea',
						'rows'			=> 10,
						'msg'			=> 'This will be displayed in the footer on every page. Use {Year} for dynamic copyright year.'
					)
				)

			),
			'ifound_api_settings' => array(
				'title'				=> 'API Settings',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'api_secret',
						'heading'	=> 'API Secret Key',
						'type'		=> 'text'
					),
					1 => array(
						'key' 		=> 'mls_name',
						'heading'	=> 'MLS Name',
						'type'		=> 'text'
					),
					2 => array(
						'key'		=> 'google_custom_api',
						'heading'	=> 'Google API',
						'type'		=> 'text',
					),
					3 => array(
						'key'		=> 'aerial_sphere_api',
						'heading'	=> 'AerialSphere API',
						'type'		=> 'text',
					),
				)
			),
			'ifound_map_settings' => array(
				'title'				=> 'Map Settings',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'active_map_icon',
						'heading'	=> 'Custom Active Map Icon',
						'type'		=> 'text'
					),
					1 => array(
						'key' 		=> 'closed_map_icon',
						'heading'	=> 'Custom Closed Map Icon',
						'type'		=> 'text'
					),
					2 => array(
						'key' 		=> 'subject_map_icon',
						'heading'	=> 'Custom Subject Map Icon',
						'type'		=> 'text'
					),
					3 => array(
						'key'		=> 'show_map_by_default',
						'heading'	=> 'Show Map By Default',
						'type'		=> 'checkbox',
					),
					20 => array(
						'key' 		=> 'city',
						'heading'	=> 'Area Map Cities',
						'type'		=> 'checkbox',
						'script'	=> 'checkall',
						'values' 	=> true,
						'msg'		=> 'Please check the cities to include on the area map.'
					)
				)
			),
			'ifound_search_settings' => array(
				'title'				=> 'Search Settings',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'hide_contingent',
						'heading'	=> 'Hide Contingent Listings',
						'type'		=> 'checkbox'
					),
					15 => array(
						'key' 		=> 'prop_type',
						'heading'	=> 'Property Types',
						'type'		=> 'checkbox',
						'script'	=> 'checkall',
						'values' 	=> true,
						'msg'		=> 'Please check the criteria to include as search options.'
					),
					16 => array(
						'key' 		=> 'mls_class',
						'heading'	=> 'MLS Class',
						'type'		=> 'checkbox',
						'script'	=> 'checkall',
						'values' 	=> true,
						'msg'		=> 'Please check the criteria to include as search options.'
					),
					18 => array(
						'key' 		=> 'list_status',
						'heading'	=> 'List Status',
						'type'		=> 'checkbox',
						'script'	=> 'checkall',
						'values' 	=> true,
						'msg'		=> 'Please check the criteria to include as search options.'
					),
					20 => array(
						'key' 		=> 'city',
						'heading'	=> 'Cities',
						'type'		=> 'checkbox',
						'script'	=> 'checkall',
						'values' 	=> true,
						'msg'		=> 'Please check the criteria to include as search options.'
					)
				)
			),
			'ifound_results_settings' => array(
				'title'				=> 'Search Results',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'grid_layout',
						'heading'	=> 'Grid Layout',
						'type'		=> 'checkbox'
					),
					1 => array(
						'key' 		=> 'results_class',
						'heading'	=> 'Custom Results Class',
						'type'		=> 'text'
					),
					2 => array(
						'key' 		=> 'custom_template',
						'heading'	=> 'Custom Results Template',
						'type'		=> 'text'
					),
				)
			),
			'ifound_seo_settings' => array(
				'title'				=> 'SEO Settings',
				'fields' 			=> array(
					10 => array(
						'key' 		=> 'results_h1',
						'heading'	=> 'Search Results H1',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					20 => array(
						'key' 		=> 'results_h2',
						'heading'	=> 'Search Results H2',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					30 => array(
						'key' 		=> 'results_meta_title',
						'heading'	=> 'Search Results Meta Title',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					40 => array(
						'key' 		=> 'results_meta_description',
						'heading'	=> 'Search Results Meta Deseiption',
						'type'		=> 'textarea',
						'rows'		=> 3
					),
					50 => array(
						'key' 		=> 'address',
						'heading'	=> 'Property Address',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					55 => array(
						'key' 		=> 'detail_h1',
						'heading'	=> 'Property Detail H1',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					60 => array(
						'key' 		=> 'detail_url',
						'heading'	=> 'Property Detail URL',
						'type'		=> 'detail_url'
					),
					70 => array(
						'key' 		=> 'detail_meta_title',
						'heading'	=> 'Property Detail Meta Title',
						'type'		=> 'textarea',
						'rows'		=> 1
					),
					80 => array(
						'key' 		=> 'detail_meta_description',
						'heading'	=> 'Property Detail Meta Description',
						'type'		=> 'textarea',
						'rows'		=> 3
					),
					90 => array(
						'notes'		=> '<b>* All fields are Required.</b>',
					),
				)
			),
			'ifound_featured_settings' => array(
				'title'				=> 'Featured Listings',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'featured_widget_title',
						'heading'	=> 'Featured Widget Title',
						'type'		=> 'text'
					),
					1 => array(
						'key' 		=> 'featured_class',
						'heading'	=> 'Custom Featured Class',
						'type'		=> 'text'
					),
					2 => array(
						'key' 		=> 'featured_query',
						'heading'	=> 'Featured Query',
						'type'		=> 'text'
					),
					3 => array(
						'key' 		=> 'featured_bu_query',
						'heading'	=> 'Featured Back Up Query',
						'type'		=> 'text'
					),
					4 => array(
						'key' 		=> 'featured_qty',
						'heading'	=> 'Featured Qty to Display',
						'type'		=> 'text'
					),
					10 => array(
						'key' 		=> 'hide_contingent',
						'heading'	=> 'Hide Contingent Listings',
						'type'		=> 'checkbox'
					),
					20 => array(
						'key' 		=> 'featured_slider_script',
						'heading'	=> 'Featured Slider Script',
						'type'		=> 'textarea',
						'rows'		=> 15
					),
				)
			),
			'ifound_registration_settings' => array(
				'title'				=> 'User Registration',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'make_offer_form_id',
						'heading'	=> 'Make Offer Form ID',
						'type'		=> 'text'
					),
					1 => array(
						'key' 		=> 'schedule_showing_form_id',
						'heading'	=> 'Schedule Showing Form ID',
						'type'		=> 'text'
					),
					4 => array(
						'key' 		=> 'prequal_form_id',
						'heading'	=> 'PreQualify Form ID',
						'type'		=> 'text'
					),
					7 => array(
						'key' 		=> 'registration_form_id',
						'heading'	=> 'Registration Form ID',
						'type'		=> 'text'
					),
					10 => array(
						'key' 		=> 'registration_limit_all',
						'heading'	=> 'Views Before Show - All Pages',
						'type'		=> 'text'
					),
					20 => array(
						'key' 		=> 'registration_limit_results',
						'heading'	=> 'Views Before Show - Search Results',
						'type'		=> 'text'
					),
					22 => array(
						'key' 		=> 'registration_limit_advanced',
						'heading'	=> 'Views Before Show - Advanced Search',
						'type'		=> 'text'
					),
					30 => array(
						'key' 		=> 'registration_limit_details',
						'heading'	=> 'Views Before Show - Property Details',
						'type'		=> 'text'
					),
					35 => array(
						'key' 		=> 'registration_delay_before',
						'heading'	=> 'Seconds to Delay Before Show',
						'type'		=> 'text'
					),
					36 => array(
						'key' 		=> 'registration_delay_between',
						'heading'	=> 'Minutes to Delay Between Shows',
						'type'		=> 'text'
					),
					40 => array(
						'key' 		=> 'registration_force',
						'heading'	=> 'Force User Registration',
						'type'		=> 'checkbox',
						'script'	=> 'none'
					),
					45 => array(
						'key' 		=> 'registration_disabled',
						'heading'	=> 'Disable User Registration',
						'type'		=> 'checkbox',
						'script'	=> 'none'
					),
					50 => array(
						'key' 		=> 'registration_body',
						'heading'	=> 'Registration Body',
						'type'		=> 'textarea',
						'rows'		=> 30
					)
				)
			),
			'ifound_unsubscribe_settings' => array(
				'title'				=> 'Unsubscribe Page',
				'fields' 			=> array(
					30 => array(
						'key' 		=> 'unsubscribe_body',
						'heading'	=> 'Unsubscribe Body',
						'type'		=> 'textarea',
						'rows'		=> 20
					)
				)
			),
			'ifound_detalis_settings' => array(
				'title'				=> 'Property Details',
				'fields' 			=> array(
					0 => array(
						'key' 		=> 'details_class',
						'heading'	=> 'Custom Details Class',
						'type'		=> 'text'
					),
					1 => array(
						'key' 		=> 'custom_template',
						'heading'	=> 'Custom Details Template',
						'type'		=> 'text'
					),
					5 => array(
						'key' 		=> 'details_slider_script',
						'heading'	=> 'Details Slider Script',
						'type'		=> 'textarea',
						'rows'		=> 15
					),
				)
			),
			static::$ifound_staff_settings_option_name => array(
				'capability' => static::$manage_ifound_staff_settings_capability_name,
				'title' => 'iFound Staff Settings',
				'fields' => [
					0 => [
						'key' => static::$primary_admin_id_option_name,
						'heading' => 'Primary Admin ID',
						'type' => 'text',
						'hint' => "We normally assume the primary admin is the (non-super) admin with the lowest ID"
							. " (on this site that's $primary_admin_id), so only set this if it's different",
					],
				],
			),
		);
		if (isset($this->field_mappings()->contingent)) {
			$array['ifound_search_settings']['fields'][17] = array(
				'key' 		=> 'contingent',
				'heading'	=> 'Contingent',
				'type'		=> 'checkbox',
				'script'	=> 'checkall',
				'values' 	=> true,
				'msg'		=> 'Please check the criteria to include as search options.'
			);
			// Put it between MLS Class and List Status (where we used to always put it, even if there was no contingent
			// field).
			$this->util()->move_item($array['ifound_search_settings']['fields'], 17, 'up', 18);
		}
		// Don't show Area Map State if it's a multiple state MLS because the state will be ignored.
		if (!$this->is_multiple_state_mls()) {
			$array['ifound_map_settings']['fields'][19] = [
				'key' 		=> 'state',
				'heading'	=> 'Area Map State',
				'type'		=> 'text'
			];
			// Put it after Show Map By Default in the Map Settings section. Otherwise it will show up after the Area
			// Map Cities section, which would be confusing.
			$this->util()->move_item($array['ifound_map_settings']['fields'], 19, 'down', 3);
		}
		if (apply_filters('ifound_has_feature', 'teams')) {
		   $array['ifound_teams_settings'] = [
				'title'  => 'Multi-User',
				'fields' => [
					0 => [
						'key'     => 'show_bios',
						'heading' => 'Show bios for agents',
						'type'    => 'checkbox',
					],
					2 => [
						'key'     => 'home_link_to_bio',
						'heading' => 'Home link should go to agent bio',
						'type'    => 'checkbox',
						'hint'   => '<em>(Only takes effect when "Show bios for agents" is checked)</em>',
					],
					1 => [
						'key'     => 'allow_override_broker_logo',
						'heading' => 'Allow agents to override broker logo',
						'type'    => 'checkbox',
					],
				],
			];
		}

		/** Convert our array into an object. */
		$this->settings = $this->obj( $array );

	}



	/**
	 * Register Settings
	 *
	 * Let WP Admin know we have settings and plan to use them.
	 *
	 * @since 1.0.0
	 * @since 3.3.0 Update the sanitize callback functionality.
	 */

	public function register_settings() {

		$args = array(
			'sanitize_callback' => function($input) {
			    // If the input is null, which would happen, say, if there is only a single checkbox option and it was
				// not checked, then we want to return an array, rather than use our sanitize function.
				if ($input === null) {
					return [];
				}
				return call_user_func(['iFound', 'sanitize'], $input);
			}
		);

		foreach( $this->settings as $setting => $value ) {

			register_setting( $setting, $setting, $args );

		}

	}


	/**
	 * Settings Menu
	 *
	 * Show the iFound Tab in the WP Admin. Assign settings_page as our callback.
	 *
	 * @since 1.0.0
	 */

	public function settings_menu() {

		add_menu_page(
			__( 'iFound IDX', 'ifound' ),
			__( 'iFound IDX', 'ifound' ),
			'edit_crm_settings',
			'ifound_agent',
			array( $this, 'settings_page' ),
			'dashicons-location',
			2
		);

		foreach( $this->settings as $setting => $value ) {
			// For team members, where we don't show the Agent Info submenu, the 'iFound Settings' menu gets duplicated
			// as a submenu. This hides it, but still allows the top level menu of iFound Settings (same name), so we
			// show a simple message just to make sure the page is not blank which might confuse the user.
			if ($setting === 'ifound_agent' && !current_user_can('manage_options')) {
				add_submenu_page(
						'ifound_agent',
					'',
					'',
					'edit_crm_settings',
					'ifound_agent',
					function() {
						echo "<h1>iFound IDX</h1><div>Use the submenus to change settings</div>";
					}
				);
				continue;
			}

			add_submenu_page(
        		'ifound_agent',
        		$value->title,
        		$value->title,
        		$value->capability ?: 'manage_options',
				$setting,
        		array( $this, 'settings_page' )
			);

		}

		add_submenu_page(
        	'ifound_agent',
        	'Property Videos',
        	'Property Videos',
        	'manage_options',
			'property_videos',
        	array( $this, 'property_videos_page' )
		);

		if (apply_filters('ifound_has_feature', 'broker-compensation')) {
			add_submenu_page(
				'ifound_agent',
				'Broker Compensation',
				'Broker Compensation',
				'manage_options',
				'broker_compensation',
				array($this, 'broker_compensation_page')
			);
		}

		$teams = iFoundTeams::new_hookless();
		if ($this->util()->is_site_admin() || ($teams->user_has_team_member_role() && $teams->has_crm_enabled())) {
			add_submenu_page(
				'ifound_agent',
				'Campaign Filters',
				'Campaign Filters',
				'edit_crm_settings',
				'campaign_bulder_criteria',
				array($this, 'sortable_criteria')
			);
		}

		add_submenu_page(
        	'ifound_agent',
        	'Search Filters',
        	'Search Filters',
        	'edit_crm_settings',
			'adv_search_criteria',
			array( $this, 'sortable_criteria' )
		);

		add_submenu_page(
        	'ifound_agent',
        	'Bulk Pages',
        	'Bulk Pages',
        	'manage_options',
			'bulk_pages',
        	array( $this, 'bulk_pages' )
		);

		// This is to make it easier when we inevitably have to fix something in production. Now we have a dedicated
		// page to visit. We intentionally make it not visible in the menu by using admin.php.
		// Visit /wp-admin/admin.php?page=ifound_prod_fix
		add_submenu_page(
			'admin.php',
			'Production Fix',
			'Production Fix',
			'exist',
			'ifound_prod_fix',
			array( $this, 'prod_fix' )
		);
	}

	/**
	 * Settings Page
	 *
	 * Display the settings inputs on this page.
	 *
	 * @since 1.0.0
	 *
	 * @link https://codex.wordpress.org/Creating_Options_Pages
	 */

	public function settings_page() {

		if ( ! current_user_can( 'edit_posts' ) ) return;

		global $mls_associations;

		$settings_key = sanitize_key( $_GET['page'] );
		$page_title = get_admin_page_title();
		$option = get_option( $settings_key ); ?>

		<div class="ifound-wrap">

			<h1 class="ifound-admin-h1"><? _e( $page_title, 'ifound' ); ?></h1><?

			do_action( 'ifound_help_button', $settings_key ); ?>

			<form method="post" action="options.php">

				<? settings_fields( $settings_key ); ?>
    			<? do_settings_sections( $settings_key ); ?>

				<table class="search-settings form-table">

					<tbody><?

						foreach( $this->settings as $setting => $value ) {

							if( $setting == $settings_key ) {

								if( $value->title ) { ?>

									<tr><td colspan="2"><h2><? echo $value->title; ?></h2></td></tr><?

								}

								foreach( $value->fields as $field ) {

									$name = $settings_key . '[' . $field->key . ']';

									$value = empty( $option[$field->key] ) ? '' : $option[$field->key];

									if( $field->type == 'text' ) { ?>

										<tr>

											<th scope="row"><? _e( $field->heading, 'ifound' ); ?></th>

											<td>

												<input type="text" name="<? echo $name; ?>" id="<? echo $name; ?>" value="<? echo $value; ?>" class="regular-text" placeholder="<? echo $field->placeholder?>">
                                                <? $this->maybe_show_hint($field->hint) ?>

											</td>

										</tr><?

									} elseif( $field->type == 'detail_url' ) { ?>

										<tr>

											<th scope="row" colspan="2">
												<h3><? _e( $field->heading, 'ifound' ); ?></h3>
											</th>

										</tr>

										<tr>

											<td>

												<b class="site-url"><? echo site_url( '/listing-details/' ); ?></b>

												<input type="text" name="<? echo $name; ?>" id="<? echo $name; ?>" value="<? echo $value; ?>" size="75" placeholder="<? echo $field->placeholder?>">

												<b class="url_ext"><? _e( '/{ListingID}/', 'ifound' ); ?></b>

											</td>

										</tr><?

									} elseif( $field->type == 'textarea' ) { ?>

										<tr>

											<th scope="row" colspan="2">
												<h3><? _e( $field->heading, 'ifound' ); ?></h3>
											</th>

										</tr><?

										if( $field->msg ) {?>

											<tr>

												<td class="admin-msg" colspan="2"><? _e( $field->msg, 'ifound' ); ?></td>

											</tr><?

										} ?>

										<tr>

											<td scope="row" colspan="2">
												<textarea name="<? echo $name; ?>" id="<? echo $name; ?>" cols="160" rows="<? echo $field->rows; ?>" class="large-text code"><? echo $value; ?></textarea>
											</td>

										</tr><?

									} elseif( $field->type == 'checkbox' ) {

										$i = 1;

										if( $field->script == 'checkall' ) {

											$this->checkall( $field->heading, $field->key );

										}

										if( $field->msg ) {?>

											<tr>

												<td class="admin-msg" colspan="2"><? _e( $field->msg, 'ifound' ); ?></td>

											</tr><?

										}

										/** We do cities and prop_types here. */
										if( isset( $mls_associations ) && $field->values ) {

											$easyname = $field->key;

											foreach( $mls_associations->$easyname as $key => $value ) {

												$this->checkbox( $key, $option, $key, $field->key, $name, $key, $i, null );

												$i++;

											}

										} else {

											$checked = $option[$field->key] ? 'checked' : '';
											$this->checkbox( 'checked', $option, $field->heading, $field->key, $name, 'checked', $i, $field->hint );

										}

									}

									if( $field->notes ) {?>

										<tr>

											<td class="admin-msg" colspan="2"><? _e( $field->notes, 'ifound' ); ?></td>

										</tr><?

									}

								}

							}

						} ?>

					</tbody>

				</table>

				<? submit_button(); ?>

			</form>

		</div><?

	}

	private function maybe_show_hint($hint) {
		if ($hint) {
			?>
			<div><?= $hint ?></div>
			<?php
		}
	}

	public function checkbox( $compare, $option, $heading, $key, $name, $value, $i, $hint ) {

		$checked = in_array( $compare, $option[$key] ?: array() ) ? 'checked' : ''; ?>

		<tr>

			<th scope="row"><? _e( $heading, 'ifound' ); ?></th>

			<td>
				<input type="checkbox" name="<? echo $name; ?>[<? echo $i; ?>]" id="<? echo $name; ?>" class="<? echo $key; ?>_checkme" value="<? echo $value; ?>" <? echo $checked; ?>/>
				<? $this->maybe_show_hint($hint) ?>
			</td>

		</tr><?

	}

	public function checkall( $heading, $key ) { ?>

		<tr>

			<th colspan="2">
				<h3><? _e( $heading, 'ifound' ); ?></h3>
			</th>

		</tr>

		<script>
			jQuery(document).ready(function ($) {
				$('.<? echo $key; ?>_checkall').on('click', function () {
					var shouldCheck = $(this).data('shouldCheck');
					$('.<? echo $key; ?>_checkme').prop('checked', shouldCheck);
					$(this).data('shouldCheck', !shouldCheck);
				});
			});
		</script>

		<tr><td colspan="2"><a href="javascript:void(0);" class="<? echo $key; ?>_checkall" data-should-check="true"><? _e( 'Check/Uncheck All', 'ifound' ); ?></a></td></tr><?

	}

	/**
	 * Sortable Criteria
	 *
	 * The admin display for sortable search criteria pages.
	 *
	 * EXAMPLE PAGES:
	 * @link /wp-admin/admin.php?page=adv_search_criteria
	 * @link /wp-admin/admin.php?page=search_bar_criteria
	 *
	 * @since 1.0.16
	 */

	public function sortable_criteria() {

		if( ! current_user_can( 'edit_crm_settings' ) ) return;

		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'jquery-ui-sortable' );
		wp_enqueue_script( 'search_criteria_js' );

		$page 			  = sanitize_key( $_GET['page'] );
		$type_string      = $page === 'campaign_bulder_criteria' ? 'Campaign' : 'Search';
		$crm_id           = iFoundCrm::new_hookless()->crm_id();
		$page_plus_crm_id = $page . $crm_id;
		$choices      	  = get_option( 'ifound_' . $page_plus_crm_id, array() );
		$more_choices 	  = get_option( 'ifound_more_' . $page_plus_crm_id, array() ); ?>

		<h1 class="ifound-admin-h1"><? _e( $type_string . ' Filters', 'found' ); ?></h1><?

		do_action( 'ifound_help_button', $page ); ?>

		<div class="ifound-wrap"><?

			do_action( 'ifound_search_criteria_options', $this->mappings, array_merge( $choices, $more_choices ) );
			do_action( 'ifound_search_criteria_form', $choices, 'sortable-criteria', 'Main Filters', 'criteria-main' );
			do_action( 'ifound_search_criteria_form', $more_choices, 'more-sortable-criteria', 'More Filters', 'criteria-additional' ); ?>

		</div><?

		do_action( 'ifound_save_choices_button', $page_plus_crm_id );

	}

	public function search_criteria_options( $easy_names, $choices ) {
		$fields = $easy_names;

		// For Campaign Criteria and Search Criteria, sort the easy names by display name because the list is long.
		$easy_names_as_array = array_values((array)$easy_names);
		if (isset($easy_names_as_array[0]->display_name)) {
			$sorted_easy_names = $easy_names_as_array;
			usort($sorted_easy_names, function ($a, $b) {
				return $a->display_name <=> $b->display_name;
			});
			$sorted_easy_names = (object)$sorted_easy_names;
			$fields = [];
			foreach ($sorted_easy_names as $easy_name) {
				$fields[$easy_name->easy_name] = $easy_name;
			}
		}
		?>

		<div class="criteria-options">

			<div class="ifound-wrap">

				<h2><? _e( 'Filter Options', 'found' ); ?></h2>

				<ul id="criteria-choices" class="connectedSortable choices-wrapper"><?

					foreach( $fields as $easy_name => $value ) {

						if( in_array( $easy_name, $choices ) ) continue;

						$this->choices_button( $easy_name );

					} ?>

				</ul>

			</div>

		</div><?

	}

	/**
	 * Choices Button
	 *
	 * A drag/drop button for sortable search criteria.
	 *
	 * @since 1.0.16
	 *
	 * @param object $value An object with the button attributes.
	 */

	public function choices_button( $easy_name ) {
		$mapping = $this->mappings->$easy_name;
		if($mapping) {
			$label = $mapping->admin_display_name ?? $mapping->display_name;
			?>
			<li class="ui-state-default">
				<? _e( $label, 'ifound' ); ?>
				<input type="hidden" name="<? echo $easy_name; ?>" value="<? echo $easy_name; ?>">
			</li>
			<?
		}
	}

	/**
	 * Save Choices Button
	 *
	 * A button for sortable search criteria.
	 *
	 * @since 5.0.0
	 *
	 * @param string $page The name of the choices option..
	 */

	public function save_choices_button( $page ) { ?>

		<div class="save-criteria-wrapper">

			<div class="ifound-wrap">

				<div class="button button-primary save-criteria" page="<? echo $page; ?>">
					<i class="fal fa-plus-square" id="save-spinner"></i>
					<? _e( 'Save Filters', 'ifound' ); ?>
				</div>

			</div>

		</div><?

	}

	public function search_criteria_form( $choices, $form_id, $h2, $criteria_id ) { ?>

		<form method="post" id="ifound-<? echo $form_id; ?>-form">

			<div class="decisions-wrapper criteria-wrapper">

				<div class="ifound-wrap">

					<h2><? _e( $h2, 'ifound' ); ?></h2>

					<ul id="<? echo $criteria_id; ?>" class="connectedSortable"><?

						foreach( $choices as $choice ) {

							$this->choices_button( $choice );

						} ?>

					</ul>

				</div>

			</div>

		</form><?

	}

	/**
	 * "Save" Search Criteria
	 *
	 * This is a function to process an ajax POST of the sortable search criteria and save it to options table.
	 *
	 * @since 1.0.16
	 * @since 2.5.63 Do action to refresh transients
	 *
	 * @paran  string $page  The page name for the type of search criteria being saved.
	 * @param  string $main  The serialized form data of the main search criteria. A list of chosen search criteria.
	 * @param  string $more  The serialized form data of the more filters search criteria. A list of chosen search criteria.
	 * @return string $class The css class to display the appropriate response on the submit button.
	 */

	public function search_criteria() {

		/** Check the nonce to ensure it is valid. */
		check_ajax_referer( 'search_criteria_secure_me', 'search_criteria_nonce' );

		$main = false;
		$more = false;

		$input = $_REQUEST['input'];

		/** Check to see if out key will work.*/
		if( $page = sanitize_key( $input['page'] ) ) {

			if( ! empty( $input['main'] ) ) {

				parse_str( $input['main'], $input_main );
				$main_criteria = apply_filters( 'ifound_sanitize', $input_main );
				update_option( 'ifound_' . $page, $main_criteria );

				$main = true;

			}

			if( ! empty( $input['more'] ) ) {

				parse_str( $input['more'], $input_more );
				$more_criteria = apply_filters( 'ifound_sanitize', $input_more );
				update_option( 'ifound_more_' . $page, $more_criteria );

				$more = true;

			}

		}

		if( $main || $more ) {

			/** Both saved */
			$response = 'fa-plus-square';

		} else {

			/** Something went wrong */
			$response = 'fa-exclamation-triangle';

		}

		echo json_encode( $response );

		do_action( 'ifound_refresh_transients', true );

		die();

	}

	public function bulk_pages() { ?>

		<div class="ifound-wrap">

			<h1 class="ifound-admin-h1"><? _e( 'Bulk Pages', 'ifound' ); ?></h1><?

			do_action( 'ifound_help_button', 'bulk_pages' ); ?>

			<form method="post">

				<? wp_nonce_field( 'create_pages_secure_me', 'create_pages_nonce_field' ); ?>

				<input type="hidden" value="true" name="create_pages">

				<table class="bulk-pages form-table">

					<tbody>

						<tr>

							<th scope="row"><label for="template"><? _e( 'Parent Page', 'ifound' ); ?></label></th>

							<td><?

								$args = array(
								    'name'                  => 'parent',
								    'id'                    => 'parent',
								    'class'                 => 'parent',
								    'show_option_none'      => '(No Parent Page)',
								    'option_none_value'     => '0'
								);

								wp_dropdown_pages( $args ); ?>

							</td>

						</tr>

						<tr>

							<th scope="row"><label for="template"><? _e( 'Author', 'ifound' ); ?></label></th>

							<td><?

								$args = array(
								    'name'                  => 'author',
								    'id'                    => 'author',
								    'class'                 => 'author'
								);

								wp_dropdown_users( $args ); ?>

							</td>

						</tr>

						<tr>

							<th scope="row"><label for="template"><? _e( 'Title Template', 'ifound' ); ?></label></th>

							<td>

								<input type="text" name="template" id="template" value="{Title} Homes for Sale" class="regular-text">

							</td>

						</tr>

						<tr>

							<th scope="row"><label for="search_type"><? _e( 'Search Type', 'ifound' ); ?></label></th>

							<td>

								<select name="search_type" id="search_type">
									<option value="city"><? _e( 'City', 'ifound' ); ?></option>
									<option value="zip"><? _e( 'Zipcode', 'ifound' ); ?></option>
									<option value="subdivision"><? _e( 'Subdivision', 'ifound' ); ?></option>
									<option value="marketing_name"><? _e( 'Marketing Name', 'ifound' ); ?></option>
									<option value="mls_area"><? _e( 'MLS Area', 'ifound' ); ?></option>
									<option value="school_district"><? _e( 'School District', 'ifound' ); ?></option>
									<option value="township"><? _e( 'Township', 'ifound' ); ?></option>
								</select>

							</td>

						</tr>

						<tr>

							<th scope="row"><label for="title_list"><? _e( 'Locations List', 'ifound' ); ?></label></th>

						</tr>

						<tr>

							<td colspan="4">

								<textarea name="title_list" id="title_list" cols="160" rows="10" class="large-text code"></textarea>
								<? _e( 'Use a comma separated list of locations.', 'ifound' ); ?>

							</td>

						</tr>

						<tr>

							<td><input type="submit" value="Create Pages" class="button button-primary"></td>

						</tr>

					</tbody>

				</table>

			</form>

		</div><?

	}

	public function create_bulk_pages() {

		if( $_POST['create_pages'] == 'true' && check_admin_referer( 'create_pages_secure_me', 'create_pages_nonce_field' ) ) {

   			$parent = intval( $_POST['parent'] );
   			$author = intval( $_POST['author'] );

			$titles = htmlspecialchars( $_POST['title_list'], ENT_QUOTES );

   			$titles = explode( ',', $titles );

			foreach( $titles as $title ) {

   				$c = array(
   					'title' 	=> str_replace( '{Title}', trim( sanitize_text_field( $title ) ), $_POST['template'] ),
   					'name'  	=> trim( sanitize_text_field( $title ) ),
   					'type'		=> sanitize_key( $_POST['search_type'] ),
   					'parent'	=> $parent,
   					'author'	=> $author
   				);

   				if( post_exists( $c['title'] ) ) continue;

   				$this->community_pages( $c );

   			}

		}

	}

	private function community_pages( $c ) {

		if( post_exists( $c['title'] ) ) return;

		$page_id = wp_insert_post( array(
			'post_title'    => $c['title'],
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_parent'	=> $c['parent'],
			'post_author'	=> $c['author']
		));

		$save_this_shortcode = array( 'query' => array( $c['type'] => $c['name'] ) );
		$meta_id = add_post_meta( $page_id, 'save_this_shortcode', $save_this_shortcode );

		wp_update_post( array(
      		'ID'           => $page_id,
      		'post_content' => '[ifound id=' . $meta_id . ']',
  		));

	}

	/**
	 * Update Meta Box Order
	 *
	 * Sets the default metabox order for our custom post types.
	 *
	 * @since 2.4.6
	 * @since 3.0.0 Move this from install to admin.
	 */

	public function update_meta_box_order() {

		$meta_boxes = array(
			'contacts' 			=> array(
				'side' 		=> 'submitdiv,contacts_statusdiv,assign_team_members_metaboxdiv',
				'normal' 	=> 'contact_meta,contacts-default-update',
				'advanced' 	=> 'notes_meta,activity_log_meta,save_this_campaign_meta,save_this_property_meta,save_this_search_meta,detail_views_meta,results_views_meta,views_meta'
			),
			'private_contact' 	=> array(
				'side' 		=> 'submitdiv,contacts_statusdiv',
				'normal' 	=> 'contact_meta,contacts-default-update',
				'advanced' 	=> 'notes_meta,activity_log_meta,save_this_campaign_meta,save_this_property_meta,save_this_search_meta,detail_views_meta,results_views_meta,views_meta'
			),
			'save_this' 		=> array(
				'side'		=> 'submitdiv,save_typediv,campaign_typediv,assign_team_members_metaboxdiv',
				'normal'	=> 'save_this-default-publish,save_this-default-update',
				'advanced'	=> 'saved_canpaigns_meta,activity_log_meta'
			),
			'ifound_email' 		=> array(
				'side'		=> 'submitdiv,ifound_email_typediv',
				'normal'	=> 'slugdiv,ifound_email-default-publish,ifound_email-default-update',
				'advanced'	=> ''
			),
		);

		$meta_boxes = apply_filters( 'ifound_meta_box_order', $meta_boxes );

		$user_ids = get_users( array( 'fields' => 'ID' ) );

		if( ! in_array( 1, $user_ids ) )

			$user_ids = array_merge( array( 1 ), $user_ids );

		foreach( $user_ids as $user_id ) {

			foreach( $meta_boxes as $key => $value )

				update_user_meta( intval( $user_id ), 'meta-box-order_' . $key, $value );

		}

	}

	/**
	 * Help Button
	 *
	 * Add a help button to admin pages.
	 *
	 * @since 3.2.1
	 *
	 * @param string $slug_id The ID of the slug.
	 */

	public function help_button( $slug_id ) {

		$slug = $this->help_button_slug( $slug_id ); ?>

		<div class="help-button-wrapper">

			<div class="ifound-wrap">

				<a href="https://ifoundagent.com/<? echo $slug; ?>" class="button button-primary help-button" target="_blank">
					<i class="far fa-question-circle"></i> <? _e( 'Help', 'ifound' ); ?>
				</a>

			</div>

		</div><?

	}

	/**
	 * Help Button Slig
	 *
	 * The URI slug for help button.
	 *
	 * @since 3.2.1
	 *
	 * @param  string $slug_id The ID of the slug.
	 * @return string $slug    The URI slug.
	 */

	public function help_button_slug( $slug_id ) {

		$slugs = array(
			'drip_campaigns' 				=> 'knowledgebase/',
			'teams'							=> 'knowledgebase/',
			'private_contact'				=> 'knowledgebase/',
			'save_this' 					=> 'knowledgebase/edit-current-campaigns/',
			'ifound_email' 					=> 'knowledgebase/add-edit-email-templates/',
			'users' 						=> 'knowledgebase/user-management/',
			'attachment' 					=> 'knowledgebase/media-library/',
			'gf_entries' 					=> 'knowledgebase/edit-form-create-form-review-entries/',
			'gf_edit_forms' 				=> 'knowledgebase/edit-form-create-form-review-entries/',
			'wpm-testimonial' 				=> 'knowledgebase/add-edit-testimonials/',
			'page' 							=> 'knowledgebase/add-page-wordpress/',
			'post' 							=> 'knowledgebase/add-search-blog-post-page/',
			'contacts' 						=> 'knowledgebase/learn-contact-dashboard/',
			'crm_export' 					=> 'knowledgebase/',
			'crm_import' 					=> 'knowledgebase/how-to-import-contacts/',
			'crm_integration' 				=> 'knowledgebase/integrate-ifoundagent-liondesk/ ',
			'crm_settings' 					=> 'knowledgebase/ifound-crm-settings-overview/',
			'campaign_builder'				=> 'knowledgebase_category/campaign-builder/',
			'ifound_agent' 					=> 'knowledgebase/agent-info-settings-overview/',
			'ifound_map_settings' 			=> 'knowledgebase/map-settings-overview/',
			'ifound_results_settings' 		=> 'knowledgebase/search-results-settings-overview/',
			'ifound_api_settings' 			=> 'knowledgebase/',
			'ifound_search_settings' 		=> 'knowledgebase/search-settings-website/',
			'ifound_detalis_settings' 		=> 'knowledgebase/',
			'ifound_featured_settings' 		=> 'knowledgebase/featured-listings-settings-overview/',
			'ifound_registration_settings' 	=> 'knowledgebase/registration-pop-settings/',
			'ifound_unsubscribe_settings' 	=> 'knowledgebase/unsubscribe-page-settings/',
			'ifound_teams_settings' 	    => 'knowledgebase/team-settings/',
			'campaign_bulder_criteria' 		=> 'knowledgebase/customize-campaign-builder-search-criteria/',
			'adv_search_criteria' 			=> 'knowledgebase/customize-search-criteria-site-users-see/',
			'bulk_pages' 					=> 'knowledgebase/use-bulk-page-creator/',
			'social_media' 					=> 'knowledgebase/social-media-settings-overview/',
			'cmc' 							=> 'knowledgebase/current-market-comparison-cmc-settings-overview/',
			'ifound_slider'					=> 'knowledgebase/',
			'quick_search'					=> 'knowledgebase/',
			'property_videos'				=> 'knowledgebase/property_videos/'
		);

		$slugs = apply_filters( 'ifound_help_button_slug', $slugs );

		return empty( $slugs[$slug_id] ) ? 'knowledgebase/' : $slugs[$slug_id];

	}

	/**
	 * Help Button on Edit Pages
	 *
	 * Add a help button to admin edit pages.
	 *
	 * @global $typenow The post type for the current page.
	 *
	 * @since 3.2.1
	 *
	 * @param object $post The post object for the current post.
	 */

	public function help_button_on_edit_pages( $post ) {

		global $typenow;

		do_action( 'ifound_help_button', $typenow );

	}

	/**
	 * Property Video Page
	 *
	 * The admin page fpr Property Video.
	 *
	 * @since 3.6.2
	 */

	public function property_videos_page() {

		$default = get_option( 'ifound_property_video_default', '' ); ?>

		<div class="ifound-wrap">

			<h1 class="ifound-admin-h1"><? _e( 'Property Videos', 'ifound' ); ?></h1><?

			do_action( 'ifound_help_button', 'property_video' ); ?>

			<div class="ifound-wrap"></div><?

			$this->property_video_instructions(); ?>

			<form method="post">

				<? wp_nonce_field( 'property_video_secure_me', 'property_video_nonce_field' ); ?>

				<input type="hidden" value="true" name="save_property_video">

				<table class="bulk-pages form-table">

					<tbody>

						<tr>

							<th scope="row"><label for="property_video_default"><? _e( 'Default Video Code', 'ifound' ); ?></label></th>

							<td>

								<input type="text" name="property_video_default" id="property_video_default" value="<? echo $default; ?>" class="regular-text" placeholder="YouTube Video Code">

							</td>

						</tr>

						<tr>

							<td colspan="3"><? _e( 'Paste the code from any YouTube Video to display as a default video if no video is assigned to the property.', 'ifound' ); ?></td>

						</tr>

						<tr>

							<th colspan="3"><label for="property_video"><? _e( 'Property Videos', 'ifound' ); ?></label></th>

						</tr><?

						$this->property_video_inputs(); ?>

						<tr>

							<td><input type="submit" value="Save Changes" class="button button-primary"></td>

						</tr>

					</tbody>

				</table>

			</form>

		</div><?

	}

	/**
	 * Property Video Input
	 *
	 * Prints all the Property Video Inputs.
	 *
	 * @since 3.6.2
	 */

	public function property_video_inputs() {

		$videos = get_option( 'ifound_property_videos' );

		$empty = array( 'mls_id' => '', 'code' => '' );

		$videos = $videos ? array_merge( $empty, $videos ) : $empty;

		$i = 0;

		foreach ( $videos as $video ) {

			if ( $i > 0 && ( empty( $video['mls_id'] ) || empty( $video['code'] ) ) ) continue; ?>

			<tr>

				<td>

					<input type="text" name="videos[<? echo $i; ?>][mls_id]" id="property_video_mls_id" value="<? echo $video['mls_id'] ?? ''; ?>" class="regular-text" placeholder="MLS ID">

				</td>

				<td>

					<i class="far fa-angle-double-left"></i>

					<input type="text" name="videos[<? echo $i; ?>][code]" id="property_video_code" value="<? echo $video['code'] ?? ''; ?>" class="regular-text" placeholder="YouTube Video Code">

				</td>

			</tr><?

			$i++;

		}

	}

	/**
	 * Property Video Instructions
	 *
	 * The instructions on how to add a YouTube code.
	 *
	 * @since 3.6.2
	 */

	public function property_video_instructions() { ?>

		<div class="property-video-instructions">

			<div class="ifound-wrap">

				<table class="form-table">

					<tbody>

						<tr>

							<th colspan="3"><i class="far fa-info-circle"></i> <? _e( 'What is a YouTube Video Code?', 'ifound' ); ?></th>

						</tr>

						<tr>

							<th><label for="sample"><? _e( 'Sample YouTube URL', 'ifound' ); ?></label></th>

							<td id="sample"><? _e( 'https://youtu.be/nNSUlncWtZs', 'ifound' ); ?></td>

						</tr>

						<tr>

							<th><label for="sample"><? _e( 'What part do you need?', 'ifound' ); ?></label></th>

							<td id="sample"><? _e( '<strike>https://youtu.be/</strike><b><u>nNSUlncWtZs</u></b>', 'ifound' ); ?></td>

						</tr>

						<tr>

							<th><label for="sample"><? _e( 'Paste this part into the input.', 'ifound' ); ?></label></th>

							<td id="sample"><? _e( '<b><u>nNSUlncWtZs</u></b>', 'ifound' ); ?></td>

						</tr>

					</tbody>

				</table>

			</div>

		</div><?

	}

	/**
	 * Save Property Video
	 *
	 * Save the data from the Property Video form.
	 *
	 * @since 3.6.2
	 */

	public function save_property_video() {

		if( $_POST['save_property_video'] == 'true' && check_admin_referer( 'property_video_secure_me', 'property_video_nonce_field' ) ) {

   			$default = sanitize_text_field( $_POST['property_video_default'] );

   			if ( ! empty( $default ) ) {

   				update_option( 'ifound_property_video_default', $default );

   			} else {

   				delete_option( 'ifound_property_video_default' );

   			}

   			$clean_videos = apply_filters( 'ifound_sanitize', $_POST['videos'] );

   			$videos = array_filter( $clean_videos, function( $x ) {
   				return ! empty( $x['mls_id'] ) && ! empty( $x['code'] );
   			} );

   			update_option( 'ifound_property_videos', $videos );


		}

	}

	private function maybe_run_shortcode_conversion() {
		if (isset($_GET['profound_to_ifound'])) {
			[$fixed_post_ids, $not_fixed_post_ids] =
				iFoundProfound::new_hookless()->run_profound_to_ifound_shortcode_conversion($_GET['post_id']);
			$this->show_process_results_for_posts('Profound to iFound shortcode conversion',
				$fixed_post_ids, $not_fixed_post_ids);
		}
	}

	private function show_process_results_for_posts($message, $fixed_post_ids, $not_fixed_post_ids) {
		?>
		<h2>Did <?= $message ?> for these post IDs</h2>
		<ul>
			<? foreach ($fixed_post_ids as $post_id) {
				?><li><?= $post_id ?></li>
			<? } ?>
		</ul>
		<h2>Did NOT do <?= $message ?> for these post IDs</h2>
		<ul>
			<? foreach ($not_fixed_post_ids as $post_id) {
				?><li><?= $post_id ?></li>
			<? } ?>
		</ul>
		<?php
	}

	private function maybe_run_profound_shortcode_rollback() {
	    if (isset($_GET['profound_shortcode_rollback'])) {
			$revision_datetime_gmt_before = $_GET['revision_datetime_gmt_before'];
			if (!$revision_datetime_gmt_before) {
				?>
					<h2>Did not find <code>revision_datetime</code> in <code>$_GET</code></h2>
				<?php
				return;
			}
			[$fixed_post_ids, $not_fixed_post_ids] = iFoundProfound::new_hookless()
				->run_profound_shortcode_rollback($revision_datetime_gmt_before, $_GET['post_id']);
			$this->show_process_results_for_posts('Profound shortcode rollback',
			    $fixed_post_ids, $not_fixed_post_ids);
	    }
	}

	private function maybe_do_cron_stuff() {
		// Makes it easier for us to remove a single cron event. There's no other way to do so. Using the WP CLI, for
		// example, can only remove all cron events based on event name. It ignores the args.

		// Fill these out when needed.
		$host = 'example.com';
		$user_id = 0;

		if ($this->util()->get_host() === $host) {
			$cron_event_name = iFoundActivity::$activity_report_hook_name;
			$cron_args = [$user_id];
			$next_scheduled_timestamp = wp_next_scheduled($cron_event_name, $cron_args);
			if ($next_scheduled_timestamp) {
				wp_unschedule_event($next_scheduled_timestamp, $cron_event_name, $cron_args);
			}
		}
	}

	private function tag_contacts() {
		// Of course this is just an example and needs to be tailored each time.
		$query_args = array(
			'post_type' => iFoundContacts::$the_post_type,
			'numberposts' => -1,
			'post_status' => 'publish',
			'meta_query' => array(
				array(
					'key'     => 'acquired_from',
					'compare' => 'EXISTS',
				),
			),
			'orderby' => 'ID',
			// The default order is DESC so we force ASC
			'order' => 'ASC',
		);
		$posts = get_posts($query_args);
		foreach ($posts as $post) {
			$term = get_post_meta($post->ID, 'acquired_from', true);
			// ADD tag with append=true
			wp_set_object_terms($post->ID, $term, iFoundContacts::$generic_contact_tag_taxonomy, true);
			?>
			<div>Tagged <?= $post->ID ?> with <?= $term ?></div>
			<?php
		}
	}

	// This is to make it easier when we inevitably have to fix something in production. Now we have a dedicated page
	// to visit.
	public function prod_fix() {
		$was_page_posted = false;
		$page_posted_time = null;
		$output_buffer = null;
		if (isset($_POST['trigger'])) {
			ob_start();
			$this->maybe_run_shortcode_conversion();
			$this->maybe_run_profound_shortcode_rollback();
			$this->maybe_do_cron_stuff();
			// $this->tag_contacts();
			$output_buffer = ob_get_clean();

			$was_page_posted = true;
			$page_posted_time = new \DateTime(null, new \DateTimeZone('UTC'));

			// Here's where we would actually do the stuff. If I find myself doing things often, I'll leave them here,
			// commented out.
			//
			// iFoundProcessAlerts::new_hookless()->process_property_alerts();
			// iFoundProcessAlerts::new_hookless()->process_campaign_alert(496, true);
			// iFoundProcessAlerts::new_hookless()->do_instant_update(527,
			// 	iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED, '2023-12-15 00:00:00');
			//
			// $role = get_role(iFoundTeams::$role_name);
			// $role->remove_cap('manage_contact_tags');
			// $role = get_role( 'administrator' );
			// $role->remove_cap('manage_contact_tags');
		}
		?>

		<div class="ifound_wrap ifound_prod_fix">
			<h1>Production Fix</h1>
			<div>
				<div>Was page posted: <?= $was_page_posted ? 'yes' : 'no' ?></div>
				<div>
					Time posted: <?= $page_posted_time ? $page_posted_time->format('Y-m-d H:i:s') . ' UTC' : 'N/A' ?>
				</div>
				<form method="POST">
					<button type="submit" name="trigger">Click me to trigger prod fix</button>
				</form>
			</div>
			<p>
				This is to make it easier when we inevitably have to fix something in production. Now we have a
				dedicated page to visit.
			</p>
			<p>
				There basically shouldn't be any functionality as far as what's checked into source control. But when
				needed, this space can be modified in production to do a task and print a result.
			</p>
		</div>

		<?php
		if ($output_buffer) {
			?>
			<h2>Output</h2>
			<div class="prod_fix_output">
				<?= $output_buffer ?>
			</div>
			<?php
		}
	}

	private function get_primary_admin_id_without_override() {
		$wp_user_search = new WP_User_Query([
			'role'   => 'administrator',
			'fields' => 'ID',
			'orderby' => 'ID',
			'number' => 2,
		]);
		$results = $wp_user_search->get_results();
		$index_to_use = 0;
		$admin_id = intval($results[$index_to_use]);
		// Sometimes, the super admin is also a "regular" admin on some of our sites. Ignore that user.
		if (is_multisite() && is_super_admin($admin_id) && count($results) > 1) {
			$index_to_use++;
		}
		$admin_id = intval($results[$index_to_use]);
		return $admin_id;
	}

	// We have a concept of a shared admin, where multiple admin accounts control one set of admin data. Sometimes we
	// need to do things as one admin on behalf of all such admins. But which one? We'll just pick the one with the
	// lowest ID, considering them the "primary" (non-super) admin. The reason we use the lowest is it's most likely the
	// one that was being used by "the" agent owning the website.
	// Here's a reminder of the situation. Originally we'd record all customer/visitor activity against the logged in
	// user (agent), and then for activity reports, we'd just grab all activity because we hadn't yet built our multi-
	// user system (aka teams). When we built multi-user, we decided all admins should share data, and the way we
	// accomplished that was to make new data (e.g. creating a contact) act as if it was created by user with ID 1. Now,
	// when we do activity reports, we need to handle it in a special way for admins. We need to not only get the shared
	// data attached to user with ID 1, but also data from the past. How to know which user it was attached to in the
	// past? There's not. If there were two admins in the past, they could have had user IDs 2 and 3, or perhaps 75 and
	// 113, who knows. But most likely I'm thinking, the "main" agent of the site was the one with the lowest ID. I'm
	// thinking this could be wrong for a few random one-off situations, and we'll fix those as they come for those
	// individual sites.
	public function get_primary_admin_id() {
		$staff_settings = get_option(static::$ifound_staff_settings_option_name);
		$admin_id = intval($staff_settings[static::$primary_admin_id_option_name] ?? null);
		// Note: if the Primary Admin ID is set to blank, it will end up as a decimal 0, and we'll jump pass this 'if'.
		if ($admin_id) {
			return $admin_id;
		}
		return $this->get_primary_admin_id_without_override();
	}

	public function get_this_user_id_or_primary_admin_id($user_id = null) {
		if (!$user_id) {
			$user_id = get_current_user_id();
		}
		if ($this->util()->user_has_admin_or_super_role($user_id)) {
			$primary_admin_id = $this->get_primary_admin_id();
			return $primary_admin_id;
		}
		return $user_id;
	}

	// The term "user_ids" (plural) is just a hint/reminder that the result will always be an array.
	public function get_this_user_ids_or_primary_admin_ids($user_id = null) {
		if (!$user_id) {
			$user_id = get_current_user_id();
		}
		if ($this->util()->user_has_admin_or_super_role($user_id)) {
			$primary_admin_id = $this->get_primary_admin_id();
			if (iFoundAdmin::$shared_owner_ID === $primary_admin_id) {
				return [iFoundAdmin::$shared_owner_ID];
			}
			return [iFoundAdmin::$shared_owner_ID, $primary_admin_id];
		}
		return [$user_id];
	}

	public function get_this_user_ids_or_primary_admin_ids_str($user_id = null): string {
        $ids = $this->get_this_user_ids_or_primary_admin_ids($user_id);
        return join(',', $ids);
	}

    // "Staff" here means working for the company that makes this plugin.
    public function is_user_staff() {
        return current_user_can(static::$manage_ifound_staff_settings_capability_name);
    }

    public function get_staff_name() {
        return 'iFoundAgent';
    }

	public function broker_compensation_page() {
		global $mls_associations;

		$data = iFoundIdx::new_hookless()->get_broker_compensations_for_agent();
		$all_listing_statuses = array_keys((array)$mls_associations->list_status);
		$endpoint = rest_url(iFoundIdxAdminController::$endpoint_namespace
			. iFoundIdxAdminController::$broker_compensation_endpoint_base);
		$endpoint = wp_nonce_url($endpoint, 'wp_rest');
		$agent_name = get_option('ifound_agent', [])['agent_name'];
		?>
		<h1>Broker Compensation</h1>
		<div class="ifound-broker-compensation-spa"></div>

		<script>
		jQuery(document).ready(function() {
			var target = document.querySelector('.ifound-broker-compensation-spa');
			var props = {
				endpoint: '<?= $endpoint ?>',
				agentName: '<?= $agent_name ?>',
				allListingStatuses: <?= json_encode($all_listing_statuses) ?>,
				legal_disclaimer_history: <?= json_encode($data['legal_disclaimer_history']) ?>,
				compensations: <?= json_encode($data['compensations']) ?>,
			}
			window.ifound_spa.render_app('broker_compensation', target, props);
		});
		</script>
		<?php
	}
}
