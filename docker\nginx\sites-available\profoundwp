server {
   listen 80;
   server_name wordpress.test;

   root /var/www/profoundwp;
   index index.php;

   access_log /var/log/nginx/profoundwp.log;
   error_log /var/log/nginx/profound-error.log;

   location / {
     try_files $uri $uri/ /index.php?$args;
   }

   location ~ \.php$ {
     set $upstream profoundwp:9000; 
     fastcgi_pass $upstream;
     fastcgi_index index.php;
     include fastcgi_params;
     fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
   }

   include /etc/nginx/global/idx.conf;

}
