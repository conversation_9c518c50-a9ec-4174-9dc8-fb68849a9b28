jQuery( document ).ready( function( $ ) {
	
	$( '.save-criteria' ).on( 'click', function( e ) {	
		var input = new Object();
			input.page = $( this ).attr( 'page' );
			input.main = $( '#ifound-sortable-criteria-form' ).serialize();
			input.more = $( '#ifound-more-sortable-criteria-form' ).serialize();	
		
		$.ajax ( {
			url : search_criteria.endpoint,
			type : 'post', 
			data : {
				action : 'search_criteria',
				input : input,
				search_criteria_nonce : search_criteria.nonce,
			},
			beforeSend: function() {
				$( '#save-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
			},
			success: function( response ) {
				$( '#save-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response );
			},
			dataType:'json'
		});
		
	});
	
	$( "#criteria-choices, #criteria-main" ).sortable({
		connectWith: ".connectedSortable"
	}).disableSelection();
				
	$( "#criteria-choices, #criteria-additional" ).sortable({
		connectWith: ".connectedSortable"
	}).disableSelection();
});
