<?php

defined('ABSPATH') or die(' You do not have access! ');

require_once(__DIR__ . '/../traits/NewHooklessTrait.php');

class iFoundGravityForms {
	use NewHooklessTrait;
	use UtilTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_filter('gform_pre_send_email', [$this, 'gform_pre_send_email'], 10, 4);
		}
	}

	// Due to Google/Yahoo changes that will be enforced starting Feb 1, 2024, we want to make sure emails being sent
	// out that were initiated by Gravity Forms use an email address that will not be considered spoofed. In other
	// words, send it from our domain.
	public function gform_pre_send_email($email, $message_format, $notification, $entry) {
		try {
			if (!isset($email['headers'])) {
				$email['headers'] = [];
			}
			$has_crm_feature = apply_filters('ifound_has_feature', 'crm');
			$site_agent_info = get_option('ifound_agent');
			$from_name = $site_agent_info['agent_name'];
			$agent_email = $site_agent_info['email'];
			$team_member_agent = null;
			// If the form was filled out and the visitor is 'bound' to a team member, we should send the email from
			// their email address.
			$agent_id = apply_filters('ifound_get_agent_id', null);
			if ($agent_id && iFoundTeams::new_hookless()->user_has_team_member_role(['id' => $agent_id])) {
				$team_member_agent = get_userdata($agent_id);
				$agent_email = $team_member_agent->user_email;
				$from_name = $team_member_agent->display_name;
			} else if ($has_crm_feature) {
				$crm = iFoundCrm::new_hookless()->crm();
				$from_name = $crm->from_name;
				$agent_email = $crm->from_email;
			}

			$from_domain = iFoundCrm::$from_email_domain;
			// TODO: Theoretically an agent could have their own Email Service Provider set up to properly send email,
			// and in such case, I was thinking we should respect how their gravity forms are set to send. We'll see if
			// we ever get there, but for now, no one is set up properly. So, we'll just always override the from email.
			// if ($this->has_separate_email_service_provider()) {
			// 	$from_domain = parse_url('mailto://' . $crm->from_email)['host'];
			// }
			$is_from_email_from_acceptable_domain = false;
			// $acceptable_from_domains = array_unique([$from_domain, $this->util()->get_host()]);
			// foreach ($acceptable_from_domains as $domain_to_check) {
			// 	if (strpos($from_header, '@' . $domain_to_check) !== false) {
			// 		$is_from_email_from_acceptable_domain = true;
			// 		break;
			// 	}
			// }
			if (!$is_from_email_from_acceptable_domain) {
				$from_email = iFoundEmail::new_hookless()->make_from_email_address($from_name, $from_domain);
				$from_header = "From: {$from_name} <{$from_email}>";

				$reply_to_header = "Reply-To: {$from_name} <{$agent_email}>";
			}
			// Remove existing from and reply-to headers
			$email['headers'] = array_values(array_filter($email['headers'], function($x) {
				return !((strpos($x, 'From: ') === 0) || (strpos($x, 'Reply-To: ') === 0));
			}));
			$email['headers'][] = $from_header;
			$email['headers'][] = $reply_to_header;

			// Never send this to the admin. Send it to the agent.
			$admin_email = get_bloginfo('admin_email');
			$email['to'] = str_replace($admin_email, $agent_email, $email['to']);
		} catch (\Exception $exception) {
			// Oh well. Better to send as is then not send at all.
		}
		return $email;
	}
}
