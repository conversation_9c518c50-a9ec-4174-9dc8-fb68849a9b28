<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundDripCampaignShared {
	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_enqueue_scripts', [$this, 'admin_enqueue_scripts']);
		}
	}

	public function admin_enqueue_scripts() {
		wp_register_script('ifound_drip_campaign_shared_js',
			plugins_url( 'js/drip-campaign-shared.js', __FILE__ ),
			['jquery'], iFOUND_PLUGIN_VERSION);
	}
}
