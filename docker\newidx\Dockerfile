FROM node:18.12.1-bullseye-slim

# See:
#   https://stackoverflow.com/a/63830970/135101
#   and what it links to.
#   https://github.com/nodejs/docker-node/blob/master/docs/BestPractices.md
# Add Tini
# See: https://github.com/krallin/tini#using-tini
ENV TINI_VERSION v0.19.0
ADD https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini /tini
RUN chmod +x /tini
ENTRYPOINT ["/tini", "--"]

RUN apt-get update

RUN apt-get install -y --no-install-recommends --fix-missing \
    build-essential \
    # Install dig, host, nslookup, etc.
    dnsutils \
    less \
    wget \
    vim \
    procps \
    lsof

RUN npm install -g iced-coffee-script coffeescript stylus supervisor

# Set the working directory
WORKDIR /www

# Run the main target in `server/Makefile`, which runs `supervisor` to run `iced server.iced`
CMD make