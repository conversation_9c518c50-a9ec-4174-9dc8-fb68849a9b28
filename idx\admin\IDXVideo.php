<?php
/**
 * A class that interacts with an ajax file and the database on the admin side for agent videos.
 * Author: <PERSON> <<EMAIL>>
 * Date: 11/23/11
 */

require_once dirname(__FILE__) . "/../includes/classes/IDXBase.php";

class IDXVideos extends IDXBase
{
	/**
	 * Holds the access_id of the agent this class is interfacing with.
	 * @var null | int
	 */
	public $access_id;

	/**
	 * TODO: sanitize data
	 * @param bool $configfile expects path to config.ini file holding database connection info
	 * @param null $access_id corresponds to access table in profoundidx DB
	 */
	public function __construct($configfile = false, $access_id = null) {
		if ($configfile == false) {
			$configfile = dirname(__FILE__) . "/../config.ini";
		}
		parent::__construct($configfile);

		if ($access_id != null) {
			(is_int($access_id))? $this->access_id = $access_id : $this->access_id = null;
		}
	}

	/**
	 * Performs requested operation and returns success string.
	 *
	 * @param $requestData array Holds the array of the $_REQUEST[] data from the ajax request
	 * @return string Success or failure message from corresponding method.
	 */
	public function processReq($requestData) {
		if (isset($requestData['id'])) {
			$this->access_id = (int)$requestData['id'];
		}

		switch ($requestData['action']) {
			case "fetchAll":
				//give video listings
				return $this->displayAllVideos();
				break;
			case "fetchGlobalVideo":
				//give a particular agent's global video
				return $this->displayGlobalVideo();
				break;
			case "add":
				//add new listing to db
				return $this->addPropertyVideo($requestData['MLS_id'], $requestData['youtube_id']);
				break;
			case "delete":
				//remove listing from db
				return $this->removeVideo($requestData['MLS_id']);
				break;
			case "removeGlobalVideo":
				//remove a particular agent's global video
				return $this->replaceGlobalVideo(null);
				break;
			case "update":
				//update a listing in the db
				return $this->updateVideo($requestData['old_MLS_id'], $requestData['new_MLS_id'], $requestData['youtube_id']);
				break;
			case "replaceGlobalVideo":
				//edit/update a particular agent's global video
				return $this->replaceGlobalVideo($requestData['youtube_id']);
				break;
			default:
				return "No value for action: " . $requestData['action'];
		}
	}
	
	/**
	 * Gets an agent's videos out of the database and returns them as an array.
	 *
	 * @return array
	 */
	public function getAgentVideos() {
		$sql = $this->db->quoteInto("SELECT MLS_id, youtube_id FROM videos WHERE access_id = ?", $this->access_id);
		$arrayVideoList = $this->db->fetchAll($sql);
		return $arrayVideoList;
	}

	/**
	 * Returns an agent's global video (youtube).
	 *
	 * @return string
	 */
	public function getGlobalVideo() {
		$sql = $this->db->quoteInto("SELECT access_youtube_id FROM access WHERE access_id = ?", $this->access_id);
		$video = $this->db->fetchOne($sql);
		return $video;
	}

	/**
	 * Returns HTML output displaying all of an agent's property videos.
	 *
	 * @return string
	 */
	public function displayAllVideos() {
		if ($this->access_id != null) {
		?>
			<table>
				<tr>
					<td>MLS ID</td>
					<td>Youtube ID</td>
					<td> </td>
				</tr>
				<tr>
					<td><input type="text" name="MLS_id" /></td>
					<td><input type="text" name="youtube_id" /></td>
					<td><button type="button" onclick="addNewVideo()">Add New Video</button></td>
				</tr>
			<?php
			$video_array = $this->getAgentVideos();
			if ($video_array != null) {
				foreach ($video_array as $video_entry) {
					?>
					<tr id="<?=$video_entry['MLS_id'] . $video_entry['youtube_id'];?>">
						<td><?=$video_entry['MLS_id'];?></td>
						<td><?=$video_entry['youtube_id'];?></td>
						<td><a href="#" onclick="editVideo('<?=$video_entry['MLS_id'] . "','" . $video_entry['youtube_id'];?>')">Edit</a> | <a href="#" onclick="removeVideo(<?=$video_entry['MLS_id'];?>)">Remove</a></td>
					</tr>
					<?php
				}
			}
			?>
				</table>
			<?php
		} else {
			return "Must have an access ID set.  This is an unusual error to get through the interface.";
		}
	}

	/**
	 * Returns HTML output that displays an agent's global video.
	 *
	 * @return string Success or failure message.
	 */
	public function displayGlobalVideo() {
		if ($this->access_id != null) {
		?>
			<table>
				<tr>
					<td>Youtube ID</td>
					<td> </td>
				</tr>
				<tr>
					<td><input type="text" id="form_global_youtube_id" /></td>
					<td><button type="button" onclick="replaceGlobalVideo(document.getElementById('form_global_youtube_id').value)">Replace Global Video</button></td>
				</tr>
				<?php
					$global_video = $this->getGlobalVideo();
					if ($global_video != null) {
						?>
						<tr id="global_video_listing">
							<td><?=$global_video;?></td>
							<td><a href="#" onclick="removeGlobalVideo();">Remove</a></td>
						</tr>
					<?php
					} else {
					?>
						<tr>
							<td colspan="2">There is no global video set for this agent.</td>
						</tr>
					<?php
					}
					?>
			</table>
			<?php
		} else {
			return "Must have an access ID set.  This is an unusual error to get through the interface.";
		}
	}

	/**
	 * Adds a new property video to the database.
	 *
	 * @param null $MLS_id The MLS id that corresponds to the property.
	 * @param null $youtube_id The youtube id for the video that will correspond to the property.
	 * @return string Success or failure message.
	 */
	public function addPropertyVideo($MLS_id = null, $youtube_id = null) {
		if ($this->access_id == null || $MLS_id == null || $youtube_id == null || !is_numeric($MLS_id)) {
			if(!is_numeric($MLS_id)) {
				return "MLS ID must be an integer.";
			} else {
				return "Cannot add video when there is missing data.";
			}
		} else {
			$new_video_data = array(
				'access_id'     => $this->access_id,
				'MLS_id'        => $MLS_id,
				'youtube_id'    => $youtube_id
			);
			try {
				$this->db->insert("videos", $new_video_data);
			} catch (Exception $e) {
				return $e->getMessage();
			}
			return "Successfully added video.";
		}
	}

	/**
	 * Removes a specific property video from the database.
	 *
	 * @param null $MLS_id
	 * @return string Success or failure message.
	 */
	public function removeVideo($MLS_id = null) {
		if ($MLS_id == null || $this->access_id == null || !is_numeric($MLS_id)) {
			if (!is_numeric($MLS_id)) {
				return "Cannot remove video when MLS ID is not an integer.";
			} else {
				return "Cannot remove video when there is missing data.";
			}
		} else {
			$delete_keys = array(
				'MLS_id = ?'    => (int)$MLS_id,
				'access_id = ?' => (int)$this->access_id
			);
			try {
				$rows_affected = $this->db->delete('videos', $delete_keys);
			} catch (Exception $e) {
				return $e->getMessage();
			}
			if ($rows_affected == 1) {
				return "Successfully removed video.";
			} else {
				return "Removed " . $rows_affected . " rows."; //this is probably not a success
			}
		}
	}

	/**
	 * Edits/updates an existing property video in the database with new information.
	 *
	 * @param null $old_MLS_id MLS id that corresponds with the database listing we are changing.
	 * @param null $new_MLS_id MLS id that will replace the old MLS id in the database.
	 * @param null $youtube_id New youtube video id that will replace the old one.
	 * @return string Success or failure message.
	 */
	public function updateVideo($old_MLS_id = null, $new_MLS_id = null, $youtube_id = null) {
		if ($new_MLS_id == null || $this->access_id == null || $youtube_id == null || $old_MLS_id == null ||
			!is_numeric($new_MLS_id) || !is_numeric($old_MLS_id)) {
			if (!is_numeric($new_MLS_id) || !is_numeric($old_MLS_id)) {
				return "Cannot edit video when non-integer data is passed to key value.";
			} else {
				return "Cannot edit video when there is missing data.";
			}
		} else {
			$data = array(
				'MLS_id'        => $new_MLS_id,
				'youtube_id'    => $youtube_id
			);
			$where = array(
				'MLS_id = ?'    => $old_MLS_id,
				'access_id = ?' => $this->access_id
			);
			try {
				$rows_affected = $this->db->update('videos', $data, $where);
			} catch (Exception $e) {
				return $e->getMessage();
			}
			if ($rows_affected == 1) {
				return "Successfully edited video.";
			} else {
				return "Editted " . $rows_affected . " records."; // This is probably not a success.
			}
		}
	}

	/**
	 * Writes over the existing global video for an agent in the database--pass this null to delete it.
	 * 
	 * @param $youtube_id
	 * @return string Success or failure message.
	 */
	public function replaceGlobalVideo($youtube_id) {
		if ($this->access_id == null) {
			return "Cannot replace global video when there is missing data.";
		}

		$data = array('access_youtube_id' => $youtube_id);
		$where = array('access_id = ?' => $this->access_id);

		try {
			$rows_affected = $this->db->update('access', $data, $where);
		} catch (Exception $e) {
			return $e->getMessage();
		}
		if ($rows_affected == 1) {
			return "Successfully updated global video.";
		} else {
			return "Updated " . $rows_affected . " records."; // This is probably not a success.
		}
	}
}
