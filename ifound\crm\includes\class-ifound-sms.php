<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

// I'm justifying extending iFoundCrm because a client can't have an SMS sent without the CRM feature.
class iFoundSms extends iFoundCrm {
	use NewHooklessTrait;

	private static $SEND_SMS_LATER_HOOK = 'ifound_send_sms_later';

	public static $PURPOSE_AGENT_VISIT_ALERT = 'agent:visit_alert';
	public static $PURPOSE_AGENT_INTRO = 'agent:intro';
	public static $PURPOSE_AGENT_DRIP_REMINDER = 'agent:drip_reminder';
	public static $PURPOSE_CONTACT_INTRO = 'contact:intro';
	public static $PURPOSE_CONTACT_CAMPAIGN = 'contact:campaign';
	public static $PURPOSE_CONTACT_INSTANT_UPDATE = 'contact:instant_update';
	public static $PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED = 'contact:instant_update_recently_closed';
	public static $PURPOSES_FOR_CONTACT_OVERUSE = null;

	private static $OPT_INS_TABLE_NAME = 'ifound_sms_opt_ins';
	private static $SMS_INTROS_TABLE_NAME = 'ifound_sms_intros';
	private static $SMS_MESSAGES_TABLE_NAME = 'ifound_sms_messages';

	private static $SMS_TEMPLATE_ID_SELLER = 'seller';
	private static $SMS_TEMPLATE_ID_BUYER = 'buyer';

	public static $OPT_OUT_TYPE_HELP = 'HELP';
	public static $OPT_OUT_TYPE_START = 'START';
	public static $OPT_OUT_TYPE_STOP = 'STOP';
	public static $CONTACT_SMS_OPT_OUT_YES = 'yes';
	public static $CONTACT_SMS_OPT_OUT_NO = 'no';

	public static $IDX_SERVER_SMS_MESSAGE_ID_QUERY_PARAM_NAME = 'ifaidxsmsmid';

	public static function static_init() {
		static::$PURPOSES_FOR_CONTACT_OVERUSE = [
			static::$PURPOSE_CONTACT_CAMPAIGN,
			static::$PURPOSE_CONTACT_INSTANT_UPDATE,
			static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED,
		];
	}

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', [$this, 'create_tables']);

			add_action(static::$SEND_SMS_LATER_HOOK, [$this, 'send_sms_later']);
		}
	}

	public function create_tables() {
		$option_name = 'ifound_create_sms_tables_status';
		if (in_array(get_option($option_name, 'none'), ['done', 'pending'])) {
			return;
		}
		// Immediately mark it as pending so only one 'thread' (PHP request) does this work.
		update_option($option_name, 'pending');
		iFoundSms::new_hookless()->create_opt_ins_table();
		iFoundSms::new_hookless()->create_sms_intros_table();
		iFoundSms::new_hookless()->create_sms_messages_table();
		update_option($option_name, 'done');
	}

	public function send($to_phone_number, $body, $purpose, $options = []) {
		$to_phone_number = $this->get_digits_only($to_phone_number);
		$to_phone_number = $this->ensure_e164($to_phone_number);
		$options = wp_parse_args($options, [
			'user_id' => null,
			'post_id' => null,
			'meta_id' => null,
		]);
		$referer = iFoundCrm::new_hookless()->get_crm_menu_admin_url();
		if ($options['post_id']) {
			$referer = admin_url("post.php?post={$options['post_id']}&action=edit");
		}
		$args = [
			'body' => [
				'apikey'     => $this->api_secret(),
				'to'         => $to_phone_number,
				'body'       => $body,
				'wp_user_id' => $options['user_id'],
				'wp_post_id' => $options['post_id'],
				'referer'    => $referer,
				'purpose'    => $purpose,
			],
		];
		$url = iFoundIdx::new_hookless()->base_url(true) . '/sms/send';
		$response = wp_remote_post($url, $args);
		$record = [
			'user_id'   => $options['user_id'],
			'post_id'   => $options['post_id'],
			'meta_id'   => $options['meta_id'],
			'purpose'   => $purpose,
			'to_number' => $to_phone_number,
			'body'      => $body,
		];
		$this->record_sms_send($record, $response);
		return $response;
	}

	private function record_sms_send($vals, $response) {
		$record = [
			'user_id'   => $vals['user_id'],
			'post_id'   => $vals['post_id'],
			'meta_id'   => $vals['meta_id'],
			'to_number' => $vals['to_number'],
			'purpose'   => $vals['purpose'],
			'body'      => $vals['body'],
		];
		$error_message = $this->get_error_message_from_response($response);
		if ($error_message) {
			$record = array_merge($record, [
				'success'       => false,
				'error_message' => $error_message,
			]);
		} else {
			$response_body = wp_remote_retrieve_body($response);
			$body = json_decode($response_body, true);
			$record = array_merge($record, [
				'success'       => true,
				'idx_server_id' => $body['ifound_sms_message_id'],
				'from_number'   => $body['from_number'],
				'num_segments'  => $body['num_segments'],
				'body'          => $body['message'],
			]);
		}
		return $this->insert_sms_messages_record($record);
	}

	// This is a helper function that handles looking at the response from sending a text when using send().
	// The $default_log_message is what will be written if there was no error. If there was an error, it will be
	// appended to $default_log_message.
	public function write_to_activity_log($post_id, $action, $default_log_message, $response) {
		$log_message = $default_log_message;
		$error_message = $this->get_error_message_from_response($response);
		if ($error_message) {
			$log_message .= "(Error: $error_message)";
		}
		do_action('ifound_activity_log', $post_id, $action, $log_message);
	}

	private function get_error_message_from_response($response) {
		if (is_wp_error($response)) {
			return join("\n", $response->get_error_messages());
		} else if (wp_remote_retrieve_response_code($response) !== 200) {
			$body = $response['body'];
			// We send back plain text for 401 and 403's, but JSON for 500's on this endpoint.
			$json = json_decode($body, true);
			$error_key = 'message';
			if (!json_last_error() && isset($json[$error_key])) {
				return $json[$error_key];
			} else {
				return $body;
			}
		}
		return null;
	}

	private function get_opt_ins_table_name() {
		return $this->util()->get_table_name(static::$OPT_INS_TABLE_NAME);
	}

	public function insert_opt_ins_record($record) {
		global $wpdb;

		$record['created_date_gmt'] = current_time('mysql', true);
		return $wpdb->insert($this->get_opt_ins_table_name(), $record);
	}

	// The idea is we'll track opt-ins, so that even if the contact is deleted, we'll record when the agent marked them
	// as having opted in.
	public function create_opt_ins_table() {
		global $wpdb;
		$table_name = $this->get_opt_ins_table_name();
		// I'm not creating any keys, because I don't currently have any plans to actually use this table. It's a CYA
		// (just in case) thing.
		$sql = <<<EOT
			CREATE TABLE IF NOT EXISTS $table_name (
				id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
				type VARCHAR(30) NOT NULL,
				user_id BIGINT(20) UNSIGNED NOT NULL,
				contact_id BIGINT(20) UNSIGNED NOT NULL,
				first_name VARCHAR(30) NOT NULL,
				last_name VARCHAR(30) NOT NULL,
				mobile_phone VARCHAR(30) NOT NULL,
				email VARCHAR(255) NOT NULL,
				created_date_gmt datetime NOT NULL,
				primary key (id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		EOT;
		return $wpdb->query($sql);
	}

	// These are not the "real" templates. These are the example templates that we'll show the agents so they can have
	// an idea of what will be sent. The thing to understand is we don't actually replace any of the merge tags in
	// these templates. We just show them, as-is.
	public function get_sample_campaign_templates() {
		$campaign_templates = [
			[
				'id'    => static::$SMS_TEMPLATE_ID_SELLER,
				'label' => 'Homeowner Update',
				'body'  => '{ContactFirstName}, your neighborhood real estate update from {AgentName} has'
					. ' {ListingsCount} properties- {Link}',
			],
			[
				'id'    => static::$SMS_TEMPLATE_ID_BUYER,
				'label' => 'Buyer Search',
				'body'  => '{ContactFirstName}, your home search from {AgentName} has {ListingsCount} properties -'
					. ' {Link}',
			],
		];
		return $campaign_templates;
	}

	public function send_campaign_alert($save_this_id, $results) {
		$get_message_fn = function ($sms_template_id, $replacements) use ($results) {
			$replacements['ListingsCount'] = iFoundSaveThis::new_hookless()
				->get_updated_campaign_listings_results_count_str($results);
			$inner_message = $sms_template_id === static::$SMS_TEMPLATE_ID_SELLER
				? 'neighborhood real estate update'
				: 'home search';
			$full_message = "{ContactFirstName}, your {$inner_message} from {AgentName} has {ListingsCount} properties"
				. ' - {Link}';
			$message = $this->merge_template($full_message, $replacements);
			return $message;
		};
		$this->send_sms_for_campaign($save_this_id, $get_message_fn, 'Send SMS to contact for campaign update',
			static::$PURPOSE_CONTACT_CAMPAIGN);
	}

	// "bulk" in the function name means it's special for that purpose. We check if their mphone is set; we don't care
	// about mphone_spouse. We don't check the agent's intro title.
	public function may_send_bulk_sms($contact_id) {
		return has_term(iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE, iFoundContacts::$the_taxonomy, $contact_id)
			&& !!(get_post_meta($contact_id, 'mphone', true))
			&& !iFoundJointContact::new_hookless()->has_opted_out_of_sms($contact_id);
	}


	private function send_sms_for_campaign($save_this_id, $get_message_fn, $activity_log_action, $purpose) {
		if (!apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)) {
			return;
		}
		$contact_id = get_post_meta($save_this_id, 'contact_id', true);
		$contact = get_post($contact_id);
		if (!$contact) {
			// Could alert agent for this situation but it shouldn't happen
			return;
		}
		$sms_template_id = get_post_meta($save_this_id, iFoundSharedCampaign::$SMS_TEMPLATE_ID_KEY, true);
		if (!$sms_template_id) {
			// Could alert agent for this situation but it shouldn't happen
			return;
		}
		$crm_settings_for_agent = $this->get_option_for_agent(iFoundCrm::$crm_settings_option_name, null,
			['contact_id' => $contact_id]);
		$sms_intro_title = $crm_settings_for_agent[iFoundCrm::$SMS_INTRO_TITLE_KEY];
		if (!$sms_intro_title) {
			$link = menu_page_url(iFoundCrm::$crm_settings_menu_slug, false);
			$content = <<<EOT
				You are missing your intro title necessary to send text messages to your contacts. Please visit your
				iFound CRM Settings at $link.
			EOT;
			$this->maybe_notify_agent_of_sms_problem([
				'transient_name'       => 'missing_sms_intro_title',
				'campaign_id'          => $save_this_id,
				'contact_id'           => $contact_id,
				'email_subject'        => 'Missing intro title',
				'email_content'        => $content,
				'activity_log_action'  => $activity_log_action,
				'activity_log_message' => 'Missing SMS Intro Title in iFound CRM Settings',
			]);
			return;
		}
		$to_sms = get_post_meta($save_this_id, iFoundSharedCampaign::$TO_SMS_KEY, true);
		$to_smss = $this->util()->split_on_comma($to_sms);
		$contact_name = iFoundJointContact::new_hookless()->get_full_name($contact_id);
		if (!$to_smss) {
			$link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
			$content = <<<EOT
				No mobile phone numbers exist on the contact for this campaign. Visit the campaign at the link below,
				click on the contact, and set the Mobile Phone and optionally the Spouse Mobile Phone, and the campaign
				will send successfully the next time the campaign is run.

				Contact name: {$contact_name}
				Link: $link
			EOT;
			$this->maybe_notify_agent_of_sms_problem([
				'transient_name'       => "missing_to_sms_campaign_id:{$save_this_id}",
				'campaign_id'          => $save_this_id,
				'contact_id'           => $contact_id,
				'email_subject'        => 'Missing mobile phone numbers for contact',
				'email_content'        => $content,
				'activity_log_action'  => $activity_log_action,
				'activity_log_message' => 'Missing mobile phone numbers for contact',
			]);
			return;
		}
		if (!has_term(iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE, iFoundContacts::$the_taxonomy, $contact_id)) {
			$link = $this->util()->build_post_href($contact_id, 'edit', ['ensure_href_is_url' => true]);
			$content = <<<EOT
				The contact does not have a status of Previous Relationship. If you don't have a previous relationship
				with the contact, you may not send them text messages. Otherwise, visit the contact at the link below,
				and ensure the Previous Relationship status checkbox is checked.

				Contact name: {$contact_name}
				Link: $link
			EOT;
			$this->maybe_notify_agent_of_sms_problem([
				'transient_name'       => "no_previous_relationship_contact_id:{$contact_id}",
				'campaign_id'          => $save_this_id,
				'contact_id'           => $contact_id,
				'email_subject'        => 'No Previous Relationship on contact',
				'email_content'        => $content,
				'activity_log_action'  => $activity_log_action,
				'activity_log_message' => 'No Previous Relationship on contact',
			]);
			return;
		}
		if (iFoundJointContact::new_hookless()->has_opted_out_of_sms($contact_id)) {
			$link = $this->util()->build_post_href($contact_id, 'edit', ['ensure_href_is_url' => true]);
			$content = <<<EOT
				The contact has opted out of further text message communication.

				Contact name: {$contact_name}
				Link: $link
			EOT;
			$this->notify_agent_of_sms_problem([
				'transient_name'       => "contact_opted_out_contact_id:{$contact_id}",
				'campaign_id'          => $save_this_id,
				'contact_id'           => $contact_id,
				'email_subject'        => 'Contact has opted out of text message communication',
				'email_content'        => $content,
				'activity_log_action'  => $activity_log_action,
				'activity_log_message' => 'Contact has opted out of text message communication',
			]);
			iFoundSharedCampaign::new_hookless()->disable_sms($save_this_id, 'Contact previously opted out of text messages');
			return;
		}
		$agent_name = $crm_settings_for_agent['from_name'];
		$campaign_view_link = $this->util()->build_post_href($save_this_id, 'view', ['ensure_href_is_url' => true]);
		$campaign_view_link = $this->util()->add_id_ext_to_url($campaign_view_link, ['contact_id' => $contact_id]);
		foreach ($to_smss as $mphone) {
			$reason = $this->get_possible_phone_number_invalid_reason($mphone);
			if ($reason) {
				$link = $this->util()->build_post_href($contact_id, 'edit', ['ensure_href_is_url' => true]);
				$content = <<<EOT
					The contact has an invalid phone number as their mobile phone or spouse mobile phone.  Visit the contact
					at the link below and fix the phone number.

					Invalid phone number: {$mphone}
					Reason: {$reason}

					Contact name: {$contact_name}
					Link: $link
				EOT;
				$mphone_digits = $this->get_digits_only($mphone);
				$this->maybe_notify_agent_of_sms_problem([
					'transient_name'       => "contact_invalid_mphone_{$mphone_digits}_contact_id:{$contact_id}",
					'campaign_id'          => $save_this_id,
					'contact_id'           => $contact_id,
					'email_subject'        => "Invalid phone number {$mphone}",
					'email_content'        => $content,
					'activity_log_action'  => "Send Text Message to {$mphone}",
					'activity_log_message' => "Invalid phone number {$mphone}",
				]);
				continue;
			}
			$contact_first_name = iFoundJointContact::new_hookless()
				->get_contact_first_name_by_mphone($contact_id, $mphone);
			$replacements = [
				'ContactFirstName' => $contact_first_name,
				'AgentName'        => $agent_name,
				'Link'             => $campaign_view_link,
			];
			$message = $get_message_fn($sms_template_id, $replacements, $contact_id);
			$user_id = get_current_user_id() ?: $contact->post_author;
			$this->send_to_contact_with_potential_intro($mphone, $message, $sms_intro_title, $save_this_id, $user_id,
				$activity_log_action, $purpose);
		}
	}

	public function send_instant_update($save_this_id, $type, $results) {
		// Dale said to send a text for each listing in the instant update results. I'll cap it at 3.
		$listings = array_slice($results->listings, 0, 3);
		foreach ($listings as $listing) {
			$partial_address = $this->util()->make_street_address($listing);
			$get_message_fn = function ($sms_template_id, $replacements, $contact_id) use ($type, $listing,
				$partial_address
			) {
				$inner_message = 'here is a home for sale';
				if ($sms_template_id === 'seller') {
					if ($type === iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED) {
						$inner_message = 'here is a home that recently sold in your neighborhood';
					} else {
						$inner_message = 'here is a new home for sale in your neighborhood';
					}
				} else {
					$inner_message = 'here is a new home for your search';
				}
				$inner_message .= " ({$partial_address})";
				$full_message = "{ContactFirstName}, {$inner_message} from {AgentName} - {Link}";
				$replacements['Link'] = $this->util()->make_address_slug($listing, ['contact_id' => $contact_id]);
				$message = $this->merge_template($full_message, $replacements);
				return $message;
			};
			$activity_log_action = $type === iFoundEmail::$INSTANT_UPDATE
				? 'Send SMS to contact for instant update'
				: 'Send SMS to contact for instant update recently closed';
			$activity_log_action .= " ({$listing->ListingID} - {$partial_address})";
			$purpose = $type === iFoundEmail::$INSTANT_UPDATE
				? static::$PURPOSE_CONTACT_INSTANT_UPDATE
				: static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED;
			$this->send_sms_for_campaign($save_this_id, $get_message_fn, $activity_log_action, $purpose);
		}
	}

	private function merge_template($template_body, $vals) {
		$message = $template_body;
		$message = str_replace('{ContactFirstName}', $vals['ContactFirstName'], $message);
		$message = str_replace('{AgentName}', $vals['AgentName'], $message);
		$message = str_replace('{ListingsCount}', $vals['ListingsCount'], $message);
		$message = str_replace('{Link}', $vals['Link'], $message);
		return $message;
	}

	public function send_sms_later($args) {
		[
			'mphone' => $mphone,
			'message' => $message,
			'sms_intro_title' => $sms_intro_title,
			'campaign_id' => $campaign_id,
			'user_id' => $user_id,
			'activity_log_action' => $activity_log_action,
			'purpose' => $purpose,
		] = $args;
		// Don't allow deferral. This is to protect us from the small chance of an infinite loop where a message keeps
		// deferring and never actually sends, which most likely would happen during dev where we're forcing messages
		// after hours and also triggering the scheduled send after hours also.
		$options = ['allow_deferral' => false];
		$this->send_to_contact_with_potential_intro($mphone, $message, $sms_intro_title, $campaign_id, $user_id, $activity_log_action, $purpose, $options);
	}

	// Don't use E.164 formatting when calling this, because 1) we do it here, 2) we don't want it for the activity log.
	private function send_to_contact_with_potential_intro($mphone, $message, $sms_intro_title, $campaign_id, $user_id,
		$activity_log_action, $purpose, $options = []
	) {
		$options = wp_parse_args($options, [
			'allow_deferral' => true,
		]);
		if ($options['allow_deferral'] && !$this->util()->is_during_visiting_hours()) {
			$timestamp = $this->util()->get_next_visiting_hours_start()->getTimestamp();
			$args = [
				[
					'mphone' => $mphone,
					'message' => $message,
					'sms_intro_title' => $sms_intro_title,
					'campaign_id' => $campaign_id,
					'user_id' => $user_id,
					'activity_log_action' => $activity_log_action,
					'purpose' => $purpose,
				],
			];
			as_schedule_single_action($timestamp, static::$SEND_SMS_LATER_HOOK, $args, 'ifound');
			return;
		}

		$original_mphone = $mphone;
		$mphone = $this->get_digits_only($mphone);
		$mphone = $this->ensure_e164($mphone);
		$crm_id = $this->crm_id_from_user_id($user_id);
		$note = "Sent SMS to {$original_mphone}";
		if (!$this->has_intro_by_phone_number($crm_id, $mphone)) {
			$intro_message = sprintf(iFoundCrm::$intro_title_template, $sms_intro_title, home_url());
			['meta_id' => $meta_id] = iFoundActivity::new_hookless()->activity_log($campaign_id,
				'Send intro SMS to contact', $note);
			$response = $this->send($mphone, $intro_message, static::$PURPOSE_CONTACT_INTRO, [
				'user_id' => $user_id,
				'post_id' => $campaign_id,
				'meta_id' => $meta_id,
			]);
			$error_message = $this->get_error_message_from_response($response);
			if ($error_message) {
				return;
			}
			$this->insert_sms_intros_record([
				'crm_id'       => $crm_id,
				'mobile_phone' => $mphone,
			]);
		}
		['meta_id' => $meta_id] = iFoundActivity::new_hookless()->activity_log($campaign_id, $activity_log_action, $note);
		$response = $this->send($mphone, $message, $purpose, [
			'user_id' => $user_id,
			'post_id' => $campaign_id,
			'meta_id' => $meta_id,
		]);
	}

	public function get_digits_only($phone_number) {
		$phone_number = preg_replace('/\D/', '', $phone_number);
		return $phone_number;
	}

	private function get_possible_phone_number_invalid_reason($phone_number) {
		$phone_number = $this->get_digits_only($phone_number);
		if (strlen($phone_number) !== 10) {
			if (strlen($phone_number) === 11) {
				if (substr($phone_number, 0, 1) !== '1') {
					return null;
				}
			}
			return 'Phone number must be exactly 10 digits (parentheses and dashes are optional)';
		}
		return null;
	}

	// At this point, the input is expected to be exactly 10 digits, or 11 if it starts with a 1.
	public function ensure_e164($phone_number_digits_only) {
		$e164 = $phone_number_digits_only;
		$first_char = substr($phone_number_digits_only, 0, 1);
		if ($first_char !== '1') {
			$e164 = '1' . $e164;
		}
		$e164 = '+' . $e164;
		return $e164;
	}

	private function maybe_notify_agent_of_sms_problem($vals) {
		// We usually just tack the CRM ID right on the end. But that won't work here because the transient name might
		// already end in a number, such as a contact ID. In that case we wouldn't be able to distinguish e.g.
		// "my_transient_name_contact_id:12345". Is that a contact ID of 123 followed by a CRM ID of 45, or perhaps 12
		// followed by 345? This disambiguates it (although it will be blank for the admin).
		$transient_name = "ifound_sms_problem_{$vals['transient_name']}_crm_id:";
		$contact_id = $vals['contact_id'];
		$crm_id_options = ['contact_id' => $contact_id];
		if ($this->get_transient_for_agent($transient_name, $crm_id_options)) {
			return;
		}
		$this->notify_agent_of_sms_problem($vals);
		$this->set_transient_for_agent($transient_name, 'x', 60 * 60 * 24, $crm_id_options);
	}

	private function notify_agent_of_sms_problem($vals) {
		$campaign_id = $vals['campaign_id'];
		$subject = $vals['email_subject'];
		$content = $vals['email_content'];
		$activity_log_action = $vals['activity_log_action'];
		$acitivity_log_message = $vals['activity_log_message'];

		$subject = "Problem sending text message(s): {$subject}";
		$content = <<<EOT
			On your website powered by iFoundAgent, we were unable to send a text message to a contact for a campaign.

			{$content}

			This message will be sent to you at most once per 24 hours if the situation persists.

			-iFoundAgent Support
		EOT;

		iFoundEmail::new_hookless()->agent_email($subject, $content);
		do_action('ifound_activity_log', $campaign_id, $activity_log_action, $acitivity_log_message);
	}

	private function has_intro_by_phone_number($crm_id, $phone_number) {
		global $wpdb;

		$table_name = $this->get_sms_intros_table_name();
		$sql = <<<EOT
			SELECT * FROM {$table_name}
			WHERE crm_id = %s
			AND mobile_phone = %s
		EOT;
		$results = $wpdb->get_results($wpdb->prepare($sql, [$crm_id, $phone_number]));
		return !!$results;
	}

	private function get_sms_intros_table_name() {
		return $this->util()->get_table_name(static::$SMS_INTROS_TABLE_NAME);
	}

	public function insert_sms_intros_record($record) {
		global $wpdb;

		$record['created_date_gmt'] = current_time('mysql', true);
		return $wpdb->insert($this->get_sms_intros_table_name(), $record);
	}

	public function create_sms_intros_table() {
		global $wpdb;
		$table_name = $this->get_sms_intros_table_name();
		$sql = <<<EOT
			CREATE TABLE IF NOT EXISTS $table_name (
				id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
				crm_id VARCHAR(10) NOT NULL,
				mobile_phone VARCHAR(30) NOT NULL,
				created_date_gmt datetime NOT NULL,
				primary key (id),
				key crm_id (crm_id),
				key mobile_phone (mobile_phone)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		EOT;
		return $wpdb->query($sql);
	}

	private function get_sms_messages_table_name() {
		return $this->util()->get_table_name(static::$SMS_MESSAGES_TABLE_NAME);
	}

	public function insert_sms_messages_record($record) {
		global $wpdb;

		$record['created_date_gmt'] = current_time('mysql', true);
		$result = $wpdb->insert($this->get_sms_messages_table_name(), $record);
		$x = $wpdb->last_error;
		return $result;
	}

	public function create_sms_messages_table() {
		global $wpdb;
		$table_name = $this->get_sms_messages_table_name();
		$sql = <<<EOT
			CREATE TABLE IF NOT EXISTS $table_name (
				id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
				idx_server_id BIGINT(20) UNSIGNED,
				user_id BIGINT(20) UNSIGNED NOT NULL,
				post_id BIGINT(20) UNSIGNED,
				meta_id BIGINT(20) UNSIGNED,
				from_number VARCHAR(30) NOT NULL,
				to_number VARCHAR(30) NOT NULL,
				body TEXT CHARACTER SET utf8mb4 NOT NULL,
				purpose VARCHAR(100) NOT NULL,
				num_segments TINYINT,
				success TINYINT(1) NOT NULL,
				error_message TEXT,
				clicked_count INT DEFAULT 0,
				date_first_clicked_gmt datetime,
				created_date_gmt datetime NOT NULL,
				primary key (id),
				unique key idx_server_id (idx_server_id),
				key user_id (user_id),
				key post_id (post_id),
				key meta_id (post_id),
				key from_number (from_number),
				key to_number (to_number),
				key purpose (purpose),
				key created_date_gmt (created_date_gmt)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		EOT;
		return $wpdb->query($sql);
	}

	// Mark all contacts on site (regardless of which agent/team member owns them) with this phone number as opted out.
	public function opt_out_number($input_phone_number, $opt_out_type) {
		global $wpdb;

		$post_types = iFoundJointContact::$all_contact_post_types_quoted_str_for_db;
		$sql = <<<EOT
			SELECT p.ID as contact_id, meta_key, meta_value
			FROM $wpdb->posts p
			JOIN  $wpdb->postmeta pm
			ON pm.post_id = p.ID
			WHERE 1=1
			AND p.post_type IN ($post_types)
			AND (
				meta_key = 'mphone'
				OR meta_key = 'mphone_spouse'
			)
		EOT;
		$iFoundSms = static::new_hookless();
		$results = $wpdb->get_results($sql, ARRAY_A);
		foreach ($results as $contact) {
			$contact_phone_number = $contact['meta_value'];
			$result_digits_only_mphone = $iFoundSms->get_digits_only($contact_phone_number);
			$result_m164_mphone = $iFoundSms->ensure_e164($result_digits_only_mphone);
			if ($result_m164_mphone !== $input_phone_number) {
				continue;
			}
			$is_spouse = $contact['meta_key'] === 'mphone_spouse';
			$opt_out_val = static::$CONTACT_SMS_OPT_OUT_YES;
			$contact_type = $is_spouse ? 'Spouse' : 'Contact';
			$in_out_str = 'out of';
			$ifound_opt_out_type = 'contact_opt_out';
			if ($opt_out_type === static::$OPT_OUT_TYPE_START) {
				$opt_out_val = static::$CONTACT_SMS_OPT_OUT_NO;
				$ifound_opt_out_type = 'contact_opt_in';
				$in_out_str = 'in to';
			}
			$action = "{$contact_type} has opted {$in_out_str} text messages";
			$phone_number_with_parens = $this->format_phone_number_with_parens($input_phone_number);
			$msg = "Via phone number {$phone_number_with_parens}";
			$contact_id = $contact['contact_id'];
			if (!$is_spouse) {
				update_post_meta($contact_id, iFoundContacts::$CONTACT_SMS_OPT_OUT_KEY, $opt_out_val);
			}
			do_action('ifound_activity_log', $contact_id, $action, $msg);
			iFoundJointContact::new_hookless()->add_opt_in_record($ifound_opt_out_type, $contact_id);
			$args = iFoundSaveThis::new_hookless()->get_campaigns_args_for_contact($contact_id);
			$campaigns = get_posts($args);
			foreach ($campaigns as $campaign) {
				$force_disable_sms = !$is_spouse;
				iFoundSharedCampaign::new_hookless()->remove_phone_number_from_campaign($campaign->ID,
					$contact_phone_number, $force_disable_sms);
			}
		}
	}

	// This makes sense for phone numbers in the US. Need to rewrite code if we allow other countries.
	public function format_phone_number_with_parens($phone_number) {
		$digits_only = $this->get_digits_only($phone_number);
		// Trim country code of 1.
		if (strlen($digits_only) === 11 && $digits_only[0] === '1') {
			$digits_only = substr($digits_only, 1);
		}
		preg_match('/^(\d{3})(\d{3})(\d{4})$/', $digits_only, $matches);
		if ($matches) {
			return "({$matches[1]})-{$matches[2]}-{$matches[3]}";
		}
		return $phone_number;
	}

	public function has_campaign_sent_out_too_many_smss_recently($save_this_id) {
		global $wpdb;

		$table_name = $this->get_sms_messages_table_name();
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_sms_overactivity().
		$max_smss = 10;
		// The offset is the number to skip, so we want one less than the max.
		$offset = $max_smss - 1;
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_overactivity().
		$time_period = 60 * 60 * 24;
		// This try/catch is because better safe than sorry. Safer to not kill further processing if some calculation
		// here is off.
		$purposes_str = join(', ', $this->util()->quote_elements(static::$PURPOSES_FOR_CONTACT_OVERUSE));
		try {
			$sql = <<<EOT
			select created_date_gmt
			from $table_name ifs
			join {$wpdb->posts} p on p.id = ifs.post_id
			where p.id = $save_this_id
			and purpose in ($purposes_str)
			order by created_date_gmt desc
			limit 1
			offset $offset
EOT;
			$results = $wpdb->get_results($sql, ARRAY_A);
			if (count($results) === 1) {
				$created_date_gmt = $results[0]['created_date_gmt'];
				$created_date_gmt_timestamp = strtotime($created_date_gmt);
				$current_time = current_time('U', true);
				$diff_seconds = $current_time - $created_date_gmt_timestamp;
				if ($diff_seconds <= $time_period) {
					return true;
				}
			}
		} catch (Exception $e) {
			// Do nothing. We return false below.
		}
		return false;
	}

	public function has_campaign_sent_out_too_many_unclicked_smss($save_this_id) {
		global $wpdb;

		$table_name = $this->get_sms_messages_table_name();
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_unclicked_smss().
		$max_smss = 15;
		// This try/catch is because better safe than sorry. Safer to not kill further processing if some calculation
		// here is off.
		try {
			$enabled_date_gmt = get_post_meta($save_this_id, iFoundSaveThis::$ENABLED_DATETIME_KEY, true);
			// We only count since the time the campaign was enabled. This means if the campaign re-enabled, the
			// enabled date will be changed. The enabled date is a new piece of meta data, so if it doesn't exist,
			// we don't look at it.
			$enabled_date_clause = $enabled_date_gmt
				? "and created_date_gmt > '$enabled_date_gmt'"
				: '';
			$purposes_str = join(', ', $this->util()->quote_elements(static::$PURPOSES_FOR_CONTACT_OVERUSE));
			$sql = <<<EOT
			select created_date_gmt, clicked_count
			from $table_name ifs
			join {$wpdb->posts} p on p.id = ifs.post_id
			where p.id = $save_this_id
			$enabled_date_clause
			and purpose in ($purposes_str)
			order by created_date_gmt desc
			limit $max_smss
EOT;
			$results = $wpdb->get_results($sql, ARRAY_A);
			$results = array_map(function ($x) {
				$x['clicked_count'] = intval($x['clicked_count']);
				return $x;
			}, $results);
			$unread_smss = array_filter($results, function ($x) {
				return $x['clicked_count'] === 0;
			});
			if (count($unread_smss) === $max_smss) {
				return true;
			}
			if (count($results) > 0) {
				$most_recent_clicked_sms = $this->util()->array_find($results, function ($x) {
					return $x['clicked_count'] > 0;
				});
				if ($most_recent_clicked_sms) {
					$created_date_gmt = $most_recent_clicked_sms['created_date_gmt'];
					$created_date_gmt_timestamp = strtotime($created_date_gmt);
					$current_time = current_time('U', true);
					$diff_seconds = $current_time - $created_date_gmt_timestamp;
					$how_often = get_post_meta($save_this_id, 'how_often', true);
					$date_interval = iFoundSaveThis::new_hookless()->get_date_interval_from_how_often($how_often);
					$date_interval_seconds = $this->util()->date_interval_to_seconds($date_interval);
					// If $how_often is Monthly, then we want to wait 5 months.
					// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_unclicked_smss().
					$multiplier = 5;
					if ($diff_seconds >= $multiplier * $date_interval_seconds) {
						return true;
					}
				}
			}
		} catch (Exception $e) {
			// Do nothing. We return false below.
		}
		return false;
	}

	public function find_by_post_id($post_id) {
		global $wpdb;

		$table_name = $this->get_sms_messages_table_name();
		$sql = <<<EOT
			SELECT *
			FROM {$table_name}
			WHERE post_id = %d
		EOT;
		$records = $wpdb->get_results($wpdb->prepare($sql, $post_id), ARRAY_A);
		return $records;
	}

	public function find_by_idx_server_id($idx_server_id) {
		global $wpdb;

		$table_name = $this->get_sms_messages_table_name();
		$sql = <<<EOT
			SELECT *
			FROM {$table_name}
			WHERE idx_server_id = %d
		EOT;
		$prepared_sql = $wpdb->prepare($sql, $idx_server_id);
		$results = $wpdb->get_results($prepared_sql, ARRAY_A);
		if (count($results)) {
			$record = $results[0];
			// Change strings to integer where appropriate.
			$int_field_names = ['id', 'idx_server_id', 'user_id', 'post_id', 'meta_id', 'num_segments',
				'clicked_count'];
			foreach ($int_field_names as $int_field_name) {
				$record[$int_field_name] = intval($record[$int_field_name]);
			}
			return $record;
		}
		return null;
	}

	public function increment_clicked_count($idx_server_id) {
		$record = $this->find_by_idx_server_id($idx_server_id);
		if ($record) {
			try {
				$record['clicked_count']++;
				$current_time = current_time('mysql', true);
				if (!$record['date_first_clicked_gmt']) {
					$record['date_first_clicked_gmt'] = $current_time;
				}
				$this->update_by_idx_server_id($idx_server_id, $record);

				// If the meta_id is from a campaign, put a "clicked" message in its activity log,
				// as well as the contact's activity log.
				$post_id = iFound::new_hookless()->get_post_id_from_meta_id($record['meta_id']);
				if ($post_id) {
					$post = get_post($post_id);
					if ($post && $post->post_type === iFoundSaveThis::$the_post_type) {
						$to_number_formatted = $this->format_phone_number_with_parens($record['to_number']);
						iFoundSharedCampaign::new_hookless()->add_activity_log_click($post->ID, 'sms',
							$to_number_formatted);
					}
				}
			} catch (Exception $e) {
				// Something went wrong. Don't bother trying to update the DB.
			}
		}
	}

	private function update_by_idx_server_id($idx_server_id, $data) {
		global $wpdb;

		$wpdb->update($this->get_sms_messages_table_name(), $data, ['idx_server_id' => $idx_server_id]);
	}

	private function _get_counts($author_ids_param, $purposes, $filter) {
		global $wpdb;
		$table_name = $this->get_sms_messages_table_name();
		$interval = '-30 day';

		$purpose_param = join(', ', array_map(function($x) { return "'{$x}'"; }, $purposes));
		$limiter = '';
		if ($filter) {
			if (!in_array($filter, ['clicked'], true)) {
				throw new Exception("Invalid filter: '$filter'");
			}
			$limiter = "and ifsm.date_first_${filter}_gmt is not null";
		}
		$sql = <<<EOT
			select count(*) as count
			from {$wpdb->posts} p
			join {$wpdb->postmeta} pm on pm.post_id = p.ID
			join {$table_name} ifsm on ifsm.meta_id = pm.meta_id
			where p.post_author in ({$author_ids_param})
			and ifsm.created_date_gmt > date_add(now(), interval {$interval})
			and ifsm.purpose in ({$purpose_param})
			{$limiter}
			;
		EOT;
		$count = intval($wpdb->get_results($sql, ARRAY_A)[0]['count']);
		return $count;
	}

	private function get_counts($author_ids_param, $purposes) {
		$sent = $this->_get_counts($author_ids_param, $purposes, '');
		$clicked = $this->_get_counts($author_ids_param, $purposes, 'clicked');
		return [
			'sent' => $sent,
			'clicked' => $clicked,
		];
	}

	private function get_stats() {
		$author_ids = iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids();
		$author_ids_param = join(',', $author_ids);

		$all_types = [
			static::$PURPOSE_CONTACT_CAMPAIGN,
			static::$PURPOSE_CONTACT_INSTANT_UPDATE,
			static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED,
		];
		$data = [
			static::$PURPOSE_CONTACT_CAMPAIGN => $this->get_counts($author_ids_param, [static::$PURPOSE_CONTACT_CAMPAIGN]),
			static::$PURPOSE_CONTACT_INSTANT_UPDATE => $this->get_counts($author_ids_param,
				[static::$PURPOSE_CONTACT_INSTANT_UPDATE]),
			static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED => $this->get_counts($author_ids_param,
				[static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED]),
			'totals' => $this->get_counts($author_ids_param, $all_types),
		];
		return $data;
	}

	public function display_stats() {
		$data = $this->get_stats();
		$title_map = [
			static::$PURPOSE_CONTACT_CAMPAIGN => 'Campaign',
			static::$PURPOSE_CONTACT_INSTANT_UPDATE => 'Instant Update',
			static::$PURPOSE_CONTACT_INSTANT_UPDATE_RECENTLY_CLOSED => 'Instant Update <br /> Recently Closed',
			'totals' => 'Total',
		];
		?>
		<div class="ifound_email_stats">
			<table class="striped widefat">
				<thead>
				<tr>
					<th></th>
					<th>Sent</th>
					<th>Unique Clicks</th>
					<th>Click %</th>
				</tr>
				</thead>
				<tbody>
				<?
				foreach ($data as $type => $row) {
					$title = $title_map[$type];
					$sent = $row['sent'];
					$clicked = $row['clicked'];
					$clicked_percent_str = $this->util()->get_percent_str($clicked, $sent);
					$css_class = $type === 'totals' ? 'totals' : '';
					?>
					<tr>
						<td class="<?= $css_class ?>"><?= $title ?></td>
						<td class="<?= $css_class ?>"><?= $sent ?></td>
						<td class="<?= $css_class ?>"><?= $clicked ?></td>
						<td class="<?= $css_class ?>"><?= $clicked_percent_str ?></td>
					</tr>
					<?
				}
				?>
				</tbody>
			</table>
		</div>
		<?php
	}
}

iFoundSms::static_init();
