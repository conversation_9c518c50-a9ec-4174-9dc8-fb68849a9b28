IMPORTANT:
--------------------

* To Apply, complete the short (10 - 15 minutes) test on GitHub and follow the instructions for applying via oDesk: https://github.com/esimplicity/php-mysql-test (submit results here as a zip file along with your application).
* If you complete the test, you will be in the small pool of applicants who do. All applicants who complete the test will be interviewed, and in our history, almost all applicants who complete the test have been hired.
* Applicants who do not complete the test will not be considered.
* When you apply for this job, start your application with the words "I've completed the test". That way I know it's not spam.

Description
----------------

I'm looking for a wordpress expert. This is a short project but I'd like to find someone who's good enough to hire for longer term.

We want to allow users to register on our wordpress site. That's easy enough with some of the existing plugins, but it doesn't feel professional enough. It would be great if registering only required an email address, as opposed to a username. As part of this, we want to be able to send rich-text emails for the login and welcome process.

More importantly, we want to provide logged-in users extra functionality. Our site is an IDX-system for real estate, meaning users will be viewing homes in our listings. We want to allow users to 'save homes' to view later. This will require extra tables in the database. We also want to allow users to save searches they've done for homes. At first, this will be a trivial saving of a search URL against their user account. We want to show saved homes and saved searches on a 'user home page' for each user.

Technology
----------------

PHP, MySQL, Wordpress, AJAX, git, make

Preview of Tasks
------------------------

* Determine if existing plugins, such as "Login with Ajax", wp-email-login, and wp-email-smtp can help us.
* Allow rich-text emails for registration process.
* Allow email address for login instead of username.
* Create user home page
* Allow users to save homes, and searches
* Show saved homes and searches on user page

Workflow:
--------------

* A git repository will be provided on BitBucket
    * this will provide a starting point for code
    * details, requirements, and instructions will be provided in the README file
* Use of Skype for IM is required
* Excellent communication skills and English required