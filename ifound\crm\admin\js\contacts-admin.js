jQuery(document).ready(function($) {
	// Don't allow a contact to be published without an email address.
	$( '#publish' ).on( 'click', function(e) {
		// Only run this if this is a contact.
		if (!isCustomPostTypeAContact()) {
			return;
		}

		$('#email').removeClass('required-red');

		var email = $('#email').val();
		if( email.length < 1 ) {
			e.preventDefault();
			$('#email').addClass('required-red');
			return false;
		}
	});

	$( '.inline-edit-status' ).remove();

	// Reposition the wp-sync button
	$('#wa-sync').insertAfter('.wp-header-end').css({
		'display': 'table',
		'margin': '15px 0px',
	});

	function fixLinks() {
		// In ifound/crm/teams/includes/class-ifound-private-contact.php, in our pre_get_posts hook, we set the post_type to
		// an array. But Wordpress must not support this very well (not much turns up in Google), because then bulk action
		// stuff in JavaScript uses the value in the post_type_page hidden input as the post_type, which is the string Array,
		// which then fails when you subsequently try to e.g. trash a private contact. So we just force the value to be
		// private_contact, and then it works. This is not a perfect solution.
		// * If a user tries to trash a contact and private_contact at the same time, they'll get the error "Sorry, you are
		//   not allowed to move this item to the Trash." Hopefully that's clear enough, but the user might wonder which
		//   contact is the problem, because we don't currently make it obvious.
		// * The "undo" link after trashing a private_contact doesn't work for this same "Array" problem. I'm not going to
		//   bother dealing with that scenario, as there's a workaround, by going to the Trash and restoring there.
		$('input.post_type_page').each(function (i, node) {
			var $node = $(node);
			if ($node.val() === 'Array') {
				$node.val('private_contact');
			}
		});

		function fixPostTypeInLinks(selector) {
			$(selector).each(function(i, node) {
				var url = new URL(node.href);
				if (url.searchParams.get('post_type') === 'contacts') {
					url.searchParams.set('post_type', 'private_contact');
					node.href = url.toString();
				}
			});
		}

		// ifound_contacts_admin is global from wp_localize_script in PHP.
		var vals = ifound_contacts_admin.vals;
		if (vals.is_team_member) {
			// As described above, I don't think WP really expects to show two different post_types on the same page, but we are
			// doing it here. If the user is a team member and clicks a value in the "Contacts Status" column to filter, they
			// will get a permissions error if the contact is a contact and not a private_contact. So we force those links to
			// always have post_type=private_contact.
			fixPostTypeInLinks('td.taxonomy-contacts_status a');

			// If the agent trashes a contact, we need to fix the "Undo" link, just as we've been doing above regarding
			// contacts vs private_contact types.
			fixPostTypeInLinks('#message a');

			// If an admin views/changes a team lead, and a team member views that same contact, Wordpress will tell the
			// team member "admin is already editing this post. Do you want to take over?". It shows a "take over"
			// button, which works, and a "Contacts" link button that doesn't work because of the post_type. Fix it.
			fixPostTypeInLinks('.post-locked-message a.button');

			// Fix the links for filtering by Contact Tags
			fixPostTypeInLinks('td.taxonomy-' + ifound_contacts_admin.team_member_contact_tag_taxonomy + ' a');
		}
	}

	fixLinks();

	function isCustomPostTypeAContact() {
		var postType = '';
		var classes = $('body').attr('class').split(' ');
		classes.forEach(function(x) {
			if (x.startsWith('post-type-')) {
				postType = x.substring(10);
			}
		});
		return ['contacts', 'private_contact'].includes(postType);
	}

	// Admins share contact tags, so we'll warn if an admin intends to change or delete one. Team members do not share,
	// so we don't need to warn.
	function warnOnEditOrDeleteStatusTaxonomy() {
		var url = new URL(window.location.href);
		var taxonomySlug = url.searchParams.get('taxonomy');
		if (!
			(
				url.pathname.endsWith('edit-tags.php')
				&& ifound_contacts_admin.admin_contact_tag_taxonomy === taxonomySlug
				&& ifound_contacts_admin.admin_contact_post_type === url.searchParams.get('post_type')
			)
		) {
			return;
		}

		// Ideally I'd put this on the 'click' event, but I can't (reasonably easily) control the order of events fired,
		// and our event would fire after Wordpress core's event. So we'll use mouseup.
		var title = taxonomySlug === 'contacts_status' ? 'Contact Statuses' : 'Contact Tags';
		var message = 'Be advised that changing ' + title + ' changes them for all admin users of this'
			+ ' website';
		$('.row-actions a.delete-tag').on('mouseup', function(event) {
			window.alert(message)
		});
		$('.row-actions .edit a').on('click', function(event) {
			window.alert(message);
		});
		$('button.button-link.editinline').on('click', function(event) {
			window.alert(message)
		});
		$('.inline-edit-save.submit button.save.button.button-primary').on('click', function(event) {

		});
	}

	warnOnEditOrDeleteStatusTaxonomy();
});
