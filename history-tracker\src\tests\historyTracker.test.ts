import { describe, it, expect, vi, beforeEach } from 'vitest'
import { detectChanges, processListingChange, processListingRecords } from '../historyTracker.js'
import * as database from '../database.js'

// Mock database functions
vi.mock('../database.js', () => ({
	getLatestPropertyHistory: vi.fn(),
	insertPropertyHistory: vi.fn(),
	upsertPropertyChanges: vi.fn(),
	withTransaction: vi.fn().mockImplementation(async (callback) => {
		// Mock connection object
		const mockConnection = { }
		return await callback(mockConnection)
	}),
}))

// Mock config
vi.mock('../config.js', () => ({
	default: {
		getMlsClass: vi.fn(() => ({
			property_changes_table_name: 'test_residential_property_changes',
			property_history_table_name: 'test_residential_property_history',
			data_directory: 'test/residential',
		})),
	},
}))

const mockDatabase = vi.mocked(database)

describe('historyTracker', () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	describe('detectChanges', () => {
		it('should detect first time listing', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValue(null)

			const result = await detectChanges(mockProperty, 'test', 'residential')

			expect(result).toEqual({
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: null,
				hasChanges: false,
				standardStatusChanged: false,
				priceChanged: false,
				isFirstTime: true,
			})
		})

		it('should detect no changes when values are same', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const mockHistory = {
				id: 1,
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T09:00:00Z'),
				created_at: new Date('2024-01-01T09:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValue(mockHistory)

			const result = await detectChanges(mockProperty, 'test', 'residential')

			expect(result).toEqual({
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: mockHistory,
				hasChanges: false,
				standardStatusChanged: false,
				priceChanged: false,
				isFirstTime: false,
			})
		})

		it('should detect StandardStatus change', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Sold',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const mockHistory = {
				id: 1,
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T09:00:00Z'),
				created_at: new Date('2024-01-01T09:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValue(mockHistory)

			const result = await detectChanges(mockProperty, 'test', 'residential')

			expect(result).toEqual({
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: mockHistory,
				hasChanges: true,
				standardStatusChanged: true,
				priceChanged: false,
				isFirstTime: false,
			})
		})

		it('should detect Price change', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 550000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const mockHistory = {
				id: 1,
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T09:00:00Z'),
				created_at: new Date('2024-01-01T09:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValue(mockHistory)

			const result = await detectChanges(mockProperty, 'test', 'residential')

			expect(result).toEqual({
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: mockHistory,
				hasChanges: true,
				standardStatusChanged: false,
				priceChanged: true,
				isFirstTime: false,
			})
		})

		it('should detect changes for Closed listing (using ClosePrice as Price)', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Closed',
				Price: 485000, // This would have come from ClosePrice in JSON processing
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const mockHistory = {
				id: 1,
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000, // Previous ListPrice
				ModificationTimestamp: new Date('2024-01-01T09:00:00Z'),
				created_at: new Date('2024-01-01T09:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValue(mockHistory)

			const result = await detectChanges(mockProperty, 'test', 'residential')

			expect(result).toEqual({
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: mockHistory,
				hasChanges: true,
				standardStatusChanged: true, // Active -> Closed
				priceChanged: true, // 500000 -> 485000 (ClosePrice is different from previous ListPrice)
				isFirstTime: false,
			})
		})
	})

	describe('processListingChange', () => {
		it('should insert history for first time listing', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const result = {
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: null,
				hasChanges: false,
				standardStatusChanged: false,
				priceChanged: false,
				isFirstTime: true,
			}

			await processListingChange(result, 'test', 'residential')

			expect(mockDatabase.insertPropertyHistory).toHaveBeenCalledWith(
				{
					ListingId: 'TEST123',
					StandardStatus: 'Active',
					Price: 500000,
					ModificationTimestamp: mockProperty.ModificationTimestamp,
				},
				'test_residential_property_history',
				undefined
			)
			expect(mockDatabase.upsertPropertyChanges).not.toHaveBeenCalled()
		})

		it('should insert history and upsert changes when changes detected', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Sold',
				Price: 550000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const result = {
				listingId: 'TEST123',
				currentProperty: mockProperty,
				lastHistory: {
					id: 1,
					ListingId: 'TEST123',
					StandardStatus: 'Active',
					Price: 500000,
					ModificationTimestamp: new Date('2024-01-01T09:00:00Z'),
					created_at: new Date('2024-01-01T09:00:00Z'),
				},
				hasChanges: true,
				standardStatusChanged: true,
				priceChanged: true,
				isFirstTime: false,
			}

			await processListingChange(result, 'test', 'residential')

			expect(mockDatabase.insertPropertyHistory).toHaveBeenCalledWith(
				{
					ListingId: 'TEST123',
					StandardStatus: 'Sold',
					Price: 550000,
					ModificationTimestamp: mockProperty.ModificationTimestamp,
				},
				'test_residential_property_history',
				undefined
			)

			const upsertCall = mockDatabase.upsertPropertyChanges.mock.calls[0]
			const upsertData = upsertCall[0]
			const upsertTableName = upsertCall[1]

			expect(upsertData.ListingId).toBe('TEST123')
			expect(upsertData.StandardStatus).toBe('Sold')
			expect(upsertData.Price).toBe(550000)
			expect(upsertData.StandardStatus_ModificationTimestamp).toEqual(
				mockProperty.ModificationTimestamp
			)
			expect(upsertData.Price_ModificationTimestamp).toEqual(mockProperty.ModificationTimestamp)
			expect(upsertData.StandardStatus_updated_at).toBeInstanceOf(Date)
			expect(upsertData.Price_updated_at).toBeInstanceOf(Date)
			expect(upsertTableName).toBe('test_residential_property_changes')
		})

	})

	describe('processListingRecords', () => {
		it('should process multiple listing records', async () => {
			const mockProperty1 = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			const mockProperty2 = {
				ListingId: 'TEST456',
				StandardStatus: 'Sold',
				Price: 600000,
				ModificationTimestamp: new Date('2024-01-01T11:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValueOnce(null).mockResolvedValueOnce(null)

			const results = await processListingRecords([mockProperty1, mockProperty2], 'test', 'residential')

			expect(results).toHaveLength(2)
			expect(results[0].listingId).toBe('TEST123')
			expect(results[1].listingId).toBe('TEST456')
			expect(mockDatabase.insertPropertyHistory).toHaveBeenCalledTimes(2)
		})

		it('should process Closed listing with ClosePrice correctly', async () => {
			const mockClosedProperty = {
				ListingId: 'TEST789',
				StandardStatus: 'Closed',
				Price: 485000, // This came from ClosePrice in fileProcessor
				ModificationTimestamp: new Date('2024-01-01T12:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockResolvedValueOnce(null)

			const results = await processListingRecords([mockClosedProperty], 'test', 'residential')

			expect(results).toHaveLength(1)
			expect(results[0].listingId).toBe('TEST789')
			expect(results[0].currentProperty.StandardStatus).toBe('Closed')
			expect(results[0].currentProperty.Price).toBe(485000) // ClosePrice was used
			expect(results[0].isFirstTime).toBe(true)

			// Verify the database was called with the correct Price (from ClosePrice)
			expect(mockDatabase.insertPropertyHistory).toHaveBeenCalledWith(
				{
					ListingId: 'TEST789',
					StandardStatus: 'Closed',
					Price: 485000, // ClosePrice stored as Price
					ModificationTimestamp: mockClosedProperty.ModificationTimestamp,
				},
				'test_residential_property_history',
				expect.anything() // Connection object from withTransaction
			)
		})

		it('should throw error with context when processing fails', async () => {
			const mockProperty = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			mockDatabase.getLatestPropertyHistory.mockRejectedValue(new Error('Database connection failed'))

			await expect(processListingRecords([mockProperty], 'test', 'residential')).rejects.toThrow(
				'Failed to process ListingId TEST123: Database connection failed'
			)
		})
	})
})
