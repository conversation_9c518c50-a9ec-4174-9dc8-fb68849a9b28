jQuery(document).ready(function ($) {
	if (register.required) {
		if (readCookie('popupRegistrationBetween') == null) {
			var delay = 0;
			if (register.delay) {
				delay = register.delay;
			}
			setTimeout(popupRegistration, delay);
		}
	}

	function popupRegistration() {
		var $window = $(window);
		var window_top_position = $window.scrollTop();
		var img_top_position = (window_top_position + 50);
		$('.ifound-popup').css('top', img_top_position);
		$('.ifound-popup, .ifound-popup-backdrop, .ifound-popup-close').fadeIn('slow');
		/* If registration is not forced permit the time delay between popups
		 * if it's forced it will prevent delaying the form on page refresh
		 */
		if (!register.forced) {
			createCookie('popupRegistrationBetween', 'SessionActive', register.between);
		}

		/* Show the form again on the same page without any refreshing and possible loss
		 * of data. Tail recursion doesn't blow up the stack if you stay on the page for
		 * a 100 years lol
		 */
		setTimeout(popupRegistration, register.between * 1000);
	}

	$('.account-already-login').on('click', function (e) {

		$('.empty-input').removeClass('empty-input');

		var input = $('#registered-email').val();

		if (!input.length) {
			$('#registered-email').addClass('empty-input');
			return false;
		}

		jQuery.ajax({
			url: registration.endpoint,
			type: 'post',
			data: {
				action: 'account_already',
				input: input,
				registration_nonce: registration.nonce,
			},
			beforeSend: function () {
				prepareToLogin();
			},
			complete: ajaxComplete,
			error: ajaxError,
			success: function (response) {
				postLogin(response)
				if (response.action) {
					$('#registered-email').addClass('empty-input');
				}
			},
			dataType: 'json'
		});
	});

	/** @link https://www.gravityhelp.com/documentation/article/gform_confirmation_loaded/ */
	$(document).bind('gform_confirmation_loaded', function () {
		$('.ifound-popup, .ifound-popup-backdrop, .ifound-popup-close').removeClass('active');
	});

	function prepareToLogin() {
		$('.login-error').html('');
		$('.fa-account-already').removeClass('fa-sign-in fa-exclamation-triangle').addClass('fa-spinner fa-spin');
		$('.while-we-wait').addClass('active');
	}

	function postLogin(response) {
		$('.fa-account-already').removeClass('fa-spinner fa-spin').addClass(response.class);
		$('.while-we-wait').removeClass('active');
		var success = typeof response === 'object' && !response.action;
		if (success) {
			$('.ifound-popup, .ifound-popup-backdrop, .ifound-popup-close').fadeOut('slow');
			if (typeof updateContactID === 'function') {
				updateContactID(response.contact_id);
			} else {
				location.reload();
			}
		}
	}

	function ajaxComplete(jqXHR, textStatus) {
		var responseValue = null;
		try {
			responseValue = JSON.parse(jqXHR.responseText);
		} catch (error) {
			responseValue = jqXHR.responseText;
		}
		postLogin(responseValue);
	}

	function ajaxError(jqXHR, textStatus, errorThrown) {
		if (jqXHR.responseText === '-1') {
			// This happens when the nonce is rejected. When I've seen this, it's because the page (and the
			// nonce) was generated, then in another browser tab, the user logged out, which means the nonce
			// is no longer valid. Reloading the page should fix it because the nonce will then be valid.
			var message = 'Your login request was rejected because the security token was not valid. You must reload this page. Then try again.';
			$('.login-error').html(message);
		} else {
			$('.login-error').html(jqXHR.responseText);
		}
	}

	function sendSocialLoginInfo(socialLoginType, socialAuthToken) {
		$('.login-error').html('');
		jQuery.ajax({
			url: registration.endpoint,
			method: 'POST',
			data: {
				action: 'social_login',
				social_login_type: socialLoginType,
				registration_nonce: registration.nonce,
				social_auth_token: socialAuthToken,
			},
			beforeSend: function () {
				prepareToLogin();
			},
			complete: ajaxComplete,
			error: ajaxError,
			dataType: 'json',
		});
	}

	window._iFound_SignInWithGoogle_callback = function(response) {
		const isError = response && response.stack && response.message;
		if (isError) {
			$('.login-error').html(response.message);
			return;
		}

		sendSocialLoginInfo('google', response.credential);
	}
});

window.iFound_SignInWithGoogle_callback = function(response) {
	window._iFound_SignInWithGoogle_callback(response);
}
