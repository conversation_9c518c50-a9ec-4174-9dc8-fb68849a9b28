-- History Tracker Database Schema
-- MariaDB 10.3.28

-- Test Tables (for integration testing)
DROP TABLE IF EXISTS test_residential_property_changes;
CREATE TABLE test_residential_property_changes (
    ListingId VARCHAR(255) PRIMARY KEY,
    StandardStatus VARCHAR(255),
    StandardStatus_ModificationTimestamp DATETIME(3) NULL COMMENT 'ModificationTimestamp when StandardStatus change was detected',
    StandardStatus_updated_at DATETIME(3) NULL COMMENT 'When StandardStatus was updated by HistoryTracker',
    Price DECIMAL(14,2),
    Price_ModificationTimestamp DATETIME(3) NULL COMMENT 'ModificationTimestamp when Price change was detected',
    Price_updated_at DATETIME(3) NULL COMMENT 'When Price was updated by HistoryTracker',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    INDEX StandardStatus (StandardStatus),
    INDEX StandardStatus_ModificationTimestamp (StandardStatus_ModificationTimestamp)
);

DROP TABLE IF EXISTS test_residential_property_history;
CREATE TABLE test_residential_property_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ListingId VARCHAR(255) NOT NULL,
    StandardStatus VARCHAR(255),
    Price DECIMAL(14,2),
    ModificationTimestamp DATETIME(3),
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    INDEX idx_listing_id (ListingId),
    INDEX idx_modification_timestamp (ModificationTimestamp),
    INDEX idx_created_at (created_at)
);
