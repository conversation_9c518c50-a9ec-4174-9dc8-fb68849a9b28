<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

/**
 * Contacts Class
 *
 * @since 1.0.0
 * @since 1.2.0 Remove CRM Version
 */

class iFoundContacts extends iFoundCrm {
	use UtilTrait;
	use NewHooklessTrait;

	public static $the_post_type = 'contacts';
	public static $the_taxonomy = 'contacts_status';
	public static $taxonomy_singular_label = 'Contact Status';
	public static $taxonomy_plural_label = 'Contact Statuses';
	// We cannot initialize this value until Wordpress's init hook because it relies on knowing the user. At that point
	// we want it to be a static value shared by all.
	public static $contact_tag_taxonomy = null;
	// We will be creating a taxonomy per CRM ID, but for convenience we'll often want to just refer to the generic
	// concept rather than the user's specific taxonomy.
	private static $registered_contact_tag_taxonomies = [];
	public static $generic_contact_tag_taxonomy = 'contact_tag';
	public static $contact_tag_taxonomy_singular_label = 'Contact Tag';
	public static $contact_tag_taxonomy_plural_label = 'Contact Tags';
	public static $manage_contact_tags_cap_name = 'manage_contact_tags';

	public static $PREVIOUS_RELATIONSHIP_LABEL = 'Previous Relationship';
	public static $PREVIOUS_RELATIONSHIP_SNAKE_CASE = 'previous_relationship';
	public static $PREVIOUS_RELATIONSHIP_KEBAB_CASE = 'previous-relationship';
	public static $CONTACT_SMS_OPT_OUT_KEY = 'contact_sms_opt_out';

	// Similar to NOTE_279085, we need some way to know WHY a certain downstream method is being called. So here's a
	// global variable to track it.
	public static $is_currently_reassigning_contact_owner = false;

	private static $essential_contact_status_slugs = ['unsubscribed', 'previous-relationship'];

	protected $post_type;
	protected $query_var		= 'ifound-contacts';
	protected $label_name 		= 'Contact';
	protected $label_names		= 'Contacts';
	protected $taxonomy;
	protected $taxonomys 		= array( 'contacts_status' );

	/**
	 * init iFoundContacts class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$this->post_type = static::$the_post_type;
		$this->taxonomy = static::$the_taxonomy;

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', function() {
				// Initialize this taxonomy slug here so that it can include the CRM ID, which allows a taxonomy per CRM
				// ID.
				static::$contact_tag_taxonomy = $this->build_contact_tag_taxonomy();
			});
			add_action('init', array($this, 'post_type'));
			add_action('init', array($this, 'taxonomy'));
			add_action('init', [$this, 'register_contact_tag_taxonomy']);
			add_action('init', array('iFoundWiseAgent', 'init'));
			add_action('gform_after_submission', array($this, 'add_contact_from_form'), 10, 2);
			add_action('gform_after_submission', array($this, 'westusa_agent_registration_form'), 10, 2);
			add_action('ifound_unsubscribe', array($this, 'unsubscribe'));
			add_action('ifound_contacts_dropdown', array($this, 'contacts_dropdown'), 10, 1);
			add_filter('contacts_status_row_actions', [$this, 'contacts_status_row_actions'], 10, 2);
			// We will not allow the term unsubscribed to be deleted. Unfortunately, the only error message the user
			// will see will be "Something went wrong" or some generic error message, but as we also won't show them the
			// delete button, this is a backup/fail-safe anyway.
			add_action('pre_delete_term', [$this, 'pre_delete_term']);
			add_filter('default_hidden_columns', [$this, 'default_hidden_columns'], 10, 2);

			// add_filter('gform_pre_send_email', [$this, 'gform_pre_send_email'], 10, 4);
		}
	}

	private function build_contact_tag_taxonomy($crm_id_options = []) {
		$contact_tag_taxonomy = static::$generic_contact_tag_taxonomy;
		$crm_id = $this->crm_id($crm_id_options);
		if ($crm_id) {
			$contact_tag_taxonomy .= "_{$crm_id}";
		}
		return $contact_tag_taxonomy;
	}

	/**
	 *	Post Type
	 *
	 *	@since 1.0.0
	 *
	 *	@link https://codex.wordpress.org/Function_Reference/register_post_type
	 */

	public function post_type() {

		$labels 		= $this->is_site_admin() ? $this->admin_labels() : $this->team_labels();

		register_post_type( $this->post_type, [
			'labels' 				=> $labels,
			'query_var'				=> $this->query_var,
			'show_in_menu'			=> $this->is_site_admin(),
			'menu_position'			=> 3,
			'show_ui'				=> true,
			'public' 				=> false,
			'has_archive' 			=> false,
			'hierarchical' 			=> true,
			'show_in_nav_menus'		=> false,
			'show_in_admin_bar'		=> false,
			'supports'				=> array( 'title' ),
			'map_meta_cap'			=> true,
			'capability_type'		=> $this->post_type,
			'taxonomies'			=> [iFoundContacts::$contact_tag_taxonomy],
		]);

	}

	private function admin_labels() {

		return array(
			'name' 			=> __( $this->label_names ),
			'singular_name' => __( $this->label_name ),
			'add_new'		=> __( 'Add New ' . $this->label_name ),
			'add_new_item'	=> __( 'Add New ' . $this->label_name ),
			'edit_item'		=> __( 'Edit ' . $this->label_name ),
			'new_item'		=> __( 'New ' . $this->label_name ),
			'view_item'		=> __( 'View ' . $this->label_name ),
			'view_items'	=> __( 'View ' . $this->label_names ),
			'search_items'	=> __( 'Search ' . $this->label_names ),
			'all_items'		=> __( $this->label_names ),
			'attributes'	=> __( $this->label_name . ' Attributes' ),
			'menu_name'		=> __( 'iFound CRM' ),
		);

	}

	private function team_labels() {

		return array(
			'name' 			=> __( $this->label_names ),
			'singular_name' => __( $this->label_name ),
			'add_new'		=> __( 'Add New ' . $this->label_name ),
			'add_new_item'	=> __( 'Add New ' . $this->label_name ),
			'edit_item'		=> __( 'Edit Team Lead' ),
			'new_item'		=> __( 'New ' . $this->label_name ),
			'view_item'		=> __( 'View ' . $this->label_name ),
			'view_items'	=> __( 'View ' . $this->label_names ),
			'search_items'	=> __( 'Search ' . $this->label_names ),
			'all_items'		=> __( $this->label_names ),
			'attributes'	=> __( $this->label_name . ' Attributes' ),
			'menu_name'		=> __( 'iFound CRM' ),
			'not_found'     => __( "No {$this->label_names} found"),
		);

	}

	/**
	 *	Taxonomy
	 *
	 *	@since 1.0.0
	 *
	 *	@link https://codex.wordpress.org/Function_Reference/register_taxonomy
	 */

	public function taxonomy() {

		// Add new taxonomy, make it hierarchical (like categories)
		$labels = array(
			'name'              => _x( static::$taxonomy_plural_label, iFound::$default_context, 'ifound' ),
			'singular_name'     => _x( static::$taxonomy_singular_label, iFound::$default_context, 'ifound' ),
			'search_items'      => __( 'Search ' . static::$taxonomy_plural_label, 'ifound' ),
			'all_items'         => __( 'All ' . static::$taxonomy_plural_label, 'ifound' ),
			'parent_item'       => __( 'Parent ' . static::$taxonomy_singular_label, 'ifound' ),
			'parent_item_colon' => __( 'Parent ' . static::$taxonomy_singular_label, 'ifound' ),
			'edit_item'         => __( 'Edit ' . static::$taxonomy_singular_label, 'ifound' ),
			'update_item'       => __( 'Update ' . static::$taxonomy_singular_label, 'ifound' ),
			'add_new'           => __( 'Add New ' . static::$taxonomy_singular_label, 'ifound' ),
			'add_new_item'      => __( 'Add New ' . static::$taxonomy_singular_label, 'ifound' ),
			'new_item_name'     => __( 'New ' . static::$taxonomy_singular_label, 'ifound' ),
			'menu_name'         => __( static::$taxonomy_singular_label, 'ifound' ),
		);

		$args = array(
			'hierarchical'      => true,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_in_menu'		=> true,
			'show_admin_column' => true,
			'show_in_nav_menus'	=> false,
			'query_var'         => true,
			'public' 			=> false,
			'show_tagcloud'		=> false,
            'rewrite' 			=> false,
            'capabilities'      => array(
				'assign_terms' => 'edit_contactss',
				'edit_terms'   => 'edit_posts',
				'manage_terms' => 'edit_posts',
			)
		);

		register_taxonomy($this->taxonomy, [$this->post_type, iFoundPrivateContact::$the_post_type], $args);
	}

	// We allow the taxonomy to be passed in because one scenario (so far) where we need to register the taxonomy later
	// than the WP init hook, after we've determined which user is the context, and therefore what the taxonomy is.
	public function register_contact_tag_taxonomy($contact_tag_taxonomy = null) {
		if (!$contact_tag_taxonomy) {
			$contact_tag_taxonomy = static::$contact_tag_taxonomy;
		}
		if (in_array($contact_tag_taxonomy, static::$registered_contact_tag_taxonomies)) {
			return;
		}
		static::$registered_contact_tag_taxonomies[] = $contact_tag_taxonomy;

		// Add new taxonomy, make it hierarchical (like categories)
		$labels = array(
			'name'              => _x( static::$contact_tag_taxonomy_plural_label, iFound::$default_context, 'ifound' ),
			'singular_name'     => _x( static::$contact_tag_taxonomy_singular_label, iFound::$default_context, 'ifound' ),
			'search_items'      => __( 'Search ' . static::$contact_tag_taxonomy_plural_label, 'ifound' ),
			'all_items'         => __( 'All ' . static::$contact_tag_taxonomy_plural_label, 'ifound' ),
			'parent_item'       => __( 'Parent ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'parent_item_colon' => __( 'Parent ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'edit_item'         => __( 'Edit ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'update_item'       => __( 'Update ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'add_new'           => __( 'Add New ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'add_new_item'      => __( 'Add New ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'new_item_name'     => __( 'New ' . static::$contact_tag_taxonomy_singular_label, 'ifound' ),
			'menu_name'         => __( static::$contact_tag_taxonomy_plural_label, 'ifound' ),
		);

		$args = array(
			'hierarchical'      => true,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_in_menu'		=> true,
			'show_admin_column' => true,
			'show_in_nav_menus'	=> false,
			// It's easier to work with this generic query var name rather than the user-specific one, i.e. it's easier
			// to know the query var is contact_tag rather than e.g. contact_tag_123.
			'query_var'         => static::$generic_contact_tag_taxonomy,
			'public' 			=> false,
			'show_tagcloud'		=> false,
			'rewrite' 			=> false,
			'capabilities'      => array(
				'manage_terms' => static::$manage_contact_tags_cap_name,
				'edit_terms'   => static::$manage_contact_tags_cap_name,
				'delete_terms' => static::$manage_contact_tags_cap_name,
				'assign_terms' => static::$manage_contact_tags_cap_name,
			),
		);

		register_taxonomy($contact_tag_taxonomy, [$this->post_type, 'private_contact'], $args);
	}

	/**
	 * Save Contact Meta
	 *
	 * This saves and updates the contact meta.
	 *
	 * @since 1.0.0
	 * @since 1.0.26 Add ifound_external_crm_save_contact_data action hook.
	 * @since 2.4.3  Optimize method.
	 * @since 2.5.34 Return cleaned $input.
	 * @since 2.5.43 Add contact_id to $entry.
	 * @since 2.5.58 Trim input data.
	 *
	 * @param  int 	 $contact_id The ID of the post being saved.
	 * @param  array $input 	 The contact data.
	 * @return array $entry     An clean filtered array of email entry data.
	 */

	public function save_contact_meta( $contact_id, $input ) {

		do_action( 'ifound_external_crm_save_contact_data', $contact_id, $input );

		$contact_fields = $this->contact_fields();
		$meta = $this->util()->get_single_metas($contact_id, array_keys((array)$contact_fields));
		$previous_email_and_mphone_values = $this->util()->array_map_modify_both(function($i, $field_name) use ($meta) {
			return [$field_name, $meta[$field_name]];
		}, array_merge(iFoundJointContact::$EMAIL_FIELD_NAMES, iFoundJointContact::$MPHONE_FIELD_NAMES));

		$entry = array();

		foreach( $contact_fields as $key => $value ) {

			if( ! empty( $input[$key] ) ) {

				$data = apply_filters( 'ifound_sanitize', $input[$key] );

				update_post_meta( $contact_id, $key, $data );

				$entry[$key] = $data;

			} else {
				// If the value wasn't empty, but now is, remove the postmeta record.
				if ($meta[$key]) {
					delete_post_meta($contact_id, $key, $meta[$key]);
				}
			}

		}

		$updated_email_and_mphone_values = $this->util()->array_map_modify_both(function($i, $field_name) use ($input) {
			// The ifound_santitize filter will turn digits-only phone numbers into ints in php, which will fail our
			// "===" comparison later.
			return [$field_name, empty($input[$field_name]) ? null : strval(apply_filters('ifound_sanitize',
				$input[$field_name]))];
		}, array_merge(iFoundJointContact::$EMAIL_FIELD_NAMES, iFoundJointContact::$MPHONE_FIELD_NAMES));
		if ($previous_email_and_mphone_values['mphone'] !== $updated_email_and_mphone_values['mphone']) {
			// Reminder: this will create an opt-in record (type 'updated_mphone') when the contact is created,
			// regardless if there is an mphone or not. I think this is a good thing. It gives a fuller picture of
			// opt-in status. It'll show us what the mphone value was when the contact was first created.
			iFoundJointContact::new_hookless()->add_opt_in_record('updated_mphone', $contact_id);
		}
		$this->update_campaign_to_values($contact_id, $previous_email_and_mphone_values,
			$updated_email_and_mphone_values);

		$entry['contact_id'] = $contact_id;

		return $entry;

	}

	// If the contact's email addresses are updated, we need to update the campaigns using those email addresses.
	// Same thing with mphone values.
	private function update_campaign_to_values($contact_id, $previous_values, $updated_values) {
		$actually_changed_values = [];
		foreach ($previous_values as $key => $value) {
			// We use != instead of !== so that null and the empty string are considered equal.
			if ($previous_values[$key] != $updated_values[$key]) {
				if ($previous_values[$key] === null) {
					// They set an email address/phone that hadn't previously been set. It could not previously have
					// been used on a campaign, so we don't need to do anything.
					continue;
				}
				// Create a map from old value to new value
				$actually_changed_values[$previous_values[$key]] = $updated_values[$key];
			}
		}
		if (count($actually_changed_values) === 0) {
			// No values changed. Nothing to do.
			return;
		}


		$args = iFoundSaveThis::new_hookless()->get_campaigns_args_for_contact($contact_id);
		$posts = get_posts($args);
		$keys = [iFoundSharedCampaign::$TO_EMAIL_KEY, iFoundSharedCampaign::$TO_SMS_KEY];
		foreach ($posts as $post) {
			$save_this_id = $post->ID;
			foreach ($keys as $key) {
				$to_value = get_post_meta($save_this_id, $key, true);
				if (empty($to_value)) {
					// I'm not sure if all campaigns have a to_email address. For example,
					// iFoundEmail::campaign_alert_email() uses the contact's main email address if not.
					// If there is no to_email, there's nothing to do.
					continue;
				}
				$to_values = explode(',', $to_value);
				$changes_to_make = [];
				// Force keys to be strings because if they happen to be digits-only (mainly talking about phones here),
				// then when they were are as array keys in PHP, they will be converted to actual integers.
				$array_keys = array_map('strval', array_keys($actually_changed_values));
				// We now update the email addresses/phones used. We do it the following way to prevent this example
				// scenario: If email address A and B are on the campaign, and the contact switches the two emails (for
				// example, the main contact email and the main spouse email), if we replace A with B, then when we
				// replace B with A, there will be two A's and no B's. So, keep track of which changes we need to make
				// and make them after.
				for ($i = 0; $i < count($to_values); $i++) {
					$val = $to_values[$i];
					if (in_array($val, $array_keys, true)) {
						$changes_to_make[$i] = $actually_changed_values[$val];
					}
				}
				if (count($changes_to_make) > 0) {
					foreach ($changes_to_make as $index => $new_val) {
						$to_values[$index] = $new_val;
					}
					$updated_to_val = implode(',', array_filter($to_values));
					update_post_meta($save_this_id, $key, $updated_to_val);
				}
			}
			// Scenarios to disable:
			// 1. Campaign was only to spouse email, which has been removed (main email cannot be removed).
			// 2. Campaign was only to a phone or phones, which have been removed
			if (has_term('active', iFoundSaveThis::$the_taxonomy2, $post)) {
				$iFoundSharedCampaign = iFoundSharedCampaign::new_hookless();
				if (!get_post_meta($save_this_id, iFoundSharedCampaign::$TO_EMAIL_KEY, true)) {
					if ($iFoundSharedCampaign->is_do_email_yes($save_this_id)) {
						$iFoundSharedCampaign->disable_email($save_this_id, 'All email addresses removed from campaign');
					}
				}
				if (!get_post_meta($save_this_id, iFoundSharedCampaign::$TO_SMS_KEY, true)) {
					if ($iFoundSharedCampaign->is_do_sms_yes($save_this_id)) {
						$iFoundSharedCampaign->disable_sms($save_this_id, 'All phone numbers removed from campaign');
					}
				}
			}
		}
	}

	/**
	 * Contact Fields
	 *
	 * The fields available in contact record.
	 * NOTE: These are used in @see iFOUND_contacts::export(). The order of this object determines the order of the csv export.
	 *
	 * @since 1.0.0
	 *
	 * @return object $contact_fields Key => Label pairs for the contact fields.
	 */

	public function contact_fields() {

		return (object) array(
			'fname' 			=> 'First',
			'lname' 			=> 'Last',
			'email' 			=> 'Email',
			'email2' 			=> 'Email 2',
			'hphone' 			=> 'Home Phone',
			'wphone' 			=> 'Work Phone',
			'mphone' 			=> 'Mobile Phone',
			'birthday'			=> 'Birthday',
			'address'			=> 'Address',
			'address_2'			=> 'Address 2',
			'city'				=> 'City',
			'state'				=> 'State',
			'zip'				=> 'Zip',
			'acquired_from' 		=> 'Acquired From',
			'acquired_date'		=> 'Acquired Date',
			'fname_spouse' 		=> 'First - Spouse',
			'lname_spouse' 		=> 'Last - Spouse',
			'email_spouse' 		=> 'Email - Spouse',
			'email2_spouse' 	=> 'Email 2 - Spouse',
			'hphone_spouse' 	=> 'Home Phone - Spouse',
			'wphone_spouse' 	=> 'Work Phone - Spouse',
			'mphone_spouse' 	=> 'Mobile Phone - Spouse',
			'birthday_spouse'	=> 'Birthday - Spouse'

			// Reminder: we also now support importing a Contact Tags field, but can't consider it a field here due to
			// the way this method is used, which is basically to set a single value as post_meta, but tags are taxonomy
			// values.
		);

	}

	/**
	 * Add Contact from Form
	 *
	 * This function is called via the Gravity Forms Action Hook. gform_after_submission
	 * The data supplied has been sanitized by Gravity Forms;
	 * We want to take that data and create a new CRM contact.
	 *
	 * @uses GForm::rgar()
	 * @link https://www.gravityhelp.com/documentation/article/entry-object/
	 *
	 * @since 1.0.0
	 *
	 * @link https://www.gravityhelp.com/documentation/article/gform_after_submission/
	 *
	 * @param array $entry The entry object from a GForm submit.
	 * @param array $form  The form object of the GForm submitted.
	 */

	public function add_contact_from_form( $entry, $form ) {

		/** If the submitted form is an address capture form. We can not create a contact. */
		if( $form['id'] == get_option( 'cmc_address_capture_form_id' ) ) return;

		// If the form has a hidden field named ifound_contact_form_version, we assume the fields have what gravity
		// forms calls admin labels on them, and we use them to know which postmeta fields to fill out.
		$ifound_contact_form_version_field = $this->util()->array_find($form['fields'], function($field) {
			return $field['label'] === 'ifound_contact_form_version';
		});
		if ($ifound_contact_form_version_field) {
			$val = $entry[$ifound_contact_form_version_field['id']];
			if (intval($val) === 2) {
				$adminLabelToPostmetaKeyMap = [
					'First'                 => 'fname',
					'Last'                  => 'lname',
					'Email'                 => 'email',
					'Email 2'               => 'email2',
					'Home Phone'            => 'hphone',
					'Work Phone'            => 'wphone',
					'Mobile Phone'          => 'mphone',
					'Birthday'              => 'birthday',
					// Address is the only special one because it conflicts with Address as a composite field. So we
					// allow Address 1 as a workaround.
					'Address 1'             => 'address',
					'Address 2'             => 'address_2',
					'City'                  => 'city',
					'State'                 => 'state',
					'Zip'                   => 'zip',
					'Acquired From'         => 'acquired_from',
					'Acquired Date'         => 'acquired_date',

					// Let's assume we don't need these for now:
					//
					// 'First - Spouse'        => 'fname_spouse',
					// 'Last - Spouse'         => 'lname_spouse',
					// 'Email - Spouse'        => 'email_spouse',
					// 'Email 2 - Spouse'      => 'email2_spouse',
					// 'Home Phone - Spouse'   => 'hphone_spouse',
					// 'Work Phone - Spouse'   => 'wphone_spouse',
					// 'Mobile Phone - Spouse' => 'mphone_spouse',
					// 'Birthday - Spouse'     => 'birthday_spouse',
				];
				$subfieldInputToPostmetaKeyMap = [
					// Reminder for all: it doesn't matter if you use custom sub labels. The internal labels that we use
					// will not be affected.

					'Name' => [
						'First' => 'fname',
						'Last'  => 'lname',
					],
					'Address' => [
						// Reminder: it doesn't matter if you use a different Address Type in Gravity Forms. It doesn't
						// matter if you use International, United States, or Canadian. These labels will still be the
						// same.
						'Street Address' => 'address',
						'Address Line 2' => 'address_2',
						'City' => 'city',
						'State / Province' => 'state',
						'ZIP / Postal Code' => 'zip',
					],
				];
				$contact_data = [];
				$notes = null;
				foreach ($form['fields'] as $field) {
					$adminLabel = $field['adminLabel'];
					if ($adminLabel === 'Notes') {
						$notes_val = rgar($entry, $field['id']);
						if ($notes_val) {
							$notes = $notes_val;
						}
					} else if (in_array($adminLabel, array_keys($subfieldInputToPostmetaKeyMap))) {
						$possible_subfields = $subfieldInputToPostmetaKeyMap[$adminLabel];
						foreach ($possible_subfields as $subfield_label => $subfield_key) {
							$subfield = $this->util()->array_find($field['inputs'], function($_subfield) use ($subfield_label) {
								return $_subfield['label'] === $subfield_label;
							});
							if ($subfield) {
								$contact_data[$subfield_key] = rgar($entry, $subfield['id']);
							}
						}
					} else {
						$postmeta_key = $adminLabelToPostmetaKeyMap[$adminLabel];
						if ($postmeta_key) {
							$contact_data[$postmeta_key] = rgar($entry, $field['id']);
							if ($postmeta_key === 'birthday' && $contact_data[$postmeta_key]) {
								// Gravity forms uses Y-m-d. Our format for this field is m/d/Y.
								try {
									$contact_data[$postmeta_key] = date('m/d/Y', strtotime($contact_data[$postmeta_key]));
								} catch (Exception $e) {
									// Ignore.
								}
							}
						}
					}
				}
				$contact_data['acquired_date'] = $contact_data['acquired_date'] ?: current_time('mysql');
				if (isset($contact_data['email']) && is_email($contact_data['email'])) {
					$this->create_contact_from_gravity_forms_values($entry, $form, $contact_data,
						function($contact_id) use ($notes) {
							if ($notes) {
								iFoundNotes::new_hookless()->add_note($contact_id, $notes);
							}
						}
					);
				}
				return;
			}
		}

		// We didn't return above, so it's not a new form with a hidden "ifound_contact_form_version" field. We'll
		// fallback to our old assumptions, where if field 2 is an email, we assume it's a contact form, further
		// assuming a few key field types by their ID.

		/** Let's make sure we have an email address. */
		if( empty( rgar( $entry, '2' ) ) || ! is_email( rgar( $entry, '2' ) ) ) return;

		$contact_data = array(
			'fname' 		=> ucwords( rgar( $entry, '1.3' ) ),
			'lname'			=> ucwords( rgar( $entry, '1.6' ) ),
			'email'			=> rgar( $entry, '2' ),
			// Reminder: we used to assume it was a home phone. Now we assume it's mobile.
			'mphone'		=> rgar( $entry, '3' ),
			'comments'		=> rgar( $entry, '4' ),
			'acquired_from'	=> rgar( $entry, '5' ),
			'acquired_date'	=> current_time( 'mysql' ),
		);

		$this->create_contact_from_gravity_forms_values($entry, $form, $contact_data);
	}

	private function create_contact_from_gravity_forms_values($entry, $form, $contact_data, $afterFn = null) {
		/** Do not allow dupe email addresses */
		if( ! $contact_id = $this->get_post_id_by_key_value( 'email', $contact_data['email'] ) ) {

			$post = $this->get_post_table_fields_data_to_create_contact();
			$post['post_title'] = ( ! empty( $contact_data['fname'] ) || ! empty( $contact_data['lname'] ) ) ? trim( $contact_data['fname'] . ' ' . $contact_data['lname'] ) : $contact_data['email'];

			$contact_data['contact_id'] = wp_insert_post( $post );
			$contact_id = $contact_data['contact_id'];

			$this->save_contact_meta( $contact_id, $contact_data );

			$this->after_add_contact($contact_data['contact_id'], $contact_data['email']);

			iFoundLeadGeneration::new_hookless()->handle_new_contact_from_gravity_forms($contact_data, $entry, $form);

			if ($afterFn) {
				$afterFn($contact_id);
			}
		}

		add_post_meta( $contact_id, 'track_code' , intval( $_COOKIE[$this->cookie] ) );
	}

	// Custom for some West USA sites, specifically for agent registration for free sites. I originally got this code
	// from add_contact_from_form(). However it's pretty different, in that it's specific for West USA and the Gravity
	// Forms are set up differently. Because it's a free site registration, we don't handle the situation where it's a
	// contact form for a team member. We set a contact tag, but we do not set an additional contact tag based on the
	// acquired_from.
	public function westusa_agent_registration_form($entry, $form) {
		$config = [
			'hosts' => ['westusaagents.com', 'westusachoiceagents.com'],
			'form_id' => 7,
			'drip_template_id' => 68100,
			'post_author' => 1,
		];
		if (!iFound::new_hookless()->is_php_env_production()) {
			$config = [
				'hosts' => ['armlsspark.ifoundsites.test'],
				'form_id' => 10,
				'drip_template_id' => 679,
				'post_author' => 61,
			];
		}

		if (!(in_array($this->util()->get_host(), $config['hosts']) && intval($form['id']) === $config['form_id'])) {
			return;
		}

		$e = array(
			'fname' 		=> ucwords( rgar( $entry, '1.3' ) ),
			'lname'			=> ucwords( rgar( $entry, '1.6' ) ),
			'email'			=> rgar( $entry, '3' ),
			'mphone'		=> rgar( $entry, '28' ),
			'acquired_date'	=> current_time( 'mysql' ),
			'title'			=> ucwords( rgar( $entry, '1.3' ) ) . ' ' . ucwords( rgar( $entry, '1.6' ) ),
		);

		// Do not allow dupe email addresses
		if ($this->get_post_id_by_key_value('email', $e['email'])) {
			return;
		}

		$post = array(
			/** Use the email address if no name was provided. */
			'post_title'    => ( ! empty( $e['fname'] ) || ! empty( $e['lname'] ) ) ? trim( $e['fname'] . ' ' . $e['lname'] ) : $e['email'],
			'post_status'   => 'publish',
			'post_type'		=> $this->post_type,
			'post_author'   => iFoundAdmin::new_hookless()->get_primary_admin_id(),
		);
		$e['contact_id'] = wp_insert_post( $post );
		$contact_id = $e['contact_id'];
		$this->save_contact_meta( $contact_id, $e );
		$this->after_add_contact($e['contact_id'], $e['email']);

		// Auto start drip campaign
		$start_date_one_day_in_future = date('Y-m-d', strtotime('+1 day', $this->current_time()));
		wp_set_object_terms($contact_id, 'Free Site', static::$generic_contact_tag_taxonomy, true);
		$drip_template = get_post($config['drip_template_id']);
		if ($drip_template) {
			$crm = $this->crm_from_user_id($config['post_author']);
			$drip_campaign_data = (object) [
				'post_author'              => $config['post_author'],
				'contact_id'               => $contact_id,
				'campaign_title'           => 'WUSA Free Site 1',
				iFoundSharedCampaign::$TO_EMAIL_KEY => $e['email'],
				'drip_template_id'         => $drip_template->ID,
				'header'                   => $crm->header,
				'signature'                => $crm->signature,
				'footer'                   => $crm->footer,
				'upcoming_step_index'      => 0,
				'start_date'               => $start_date_one_day_in_future,
				'time_of_day'              => 'now',
			];
			iFoundDripCampaign::new_hookless()->create_drip_campaign($drip_campaign_data);
		}

		add_post_meta( $contact_id, 'track_code' , intval( $_COOKIE[$this->cookie] ) );
	}

	// We don't need this, because we already have email notifications to team members when leads are assigned to them.
	// I'm leaving this code here, commented out, as a reminder about how to use this gform_pre_send_email hook.
	//
	// public function gform_pre_send_email($email, $message_format, $notification, $entry) {
	// 	// Only do for this one site and for the admin notification (on the site, it's named Admin Notification).
	// 	if ($this->util()->is_monetization_project() && strtolower($notification['name']) === 'admin notification') {
	// 		// Why would it be a string?! Make it an int.
	// 		$form_id = intval($entry['form_id']);
	// 		$post_author = iFoundLeadGeneration::new_hookless()->maybe_get_user_id_for_form_id($form_id);
	// 		if ($post_author) {
	// 			$crm = $this->crm_from_user_id($post_author);
	// 			$to = iFoundEmail::new_hookless()->make_to_address($crm->from_email, $crm->from_name);
	// 			if (isset($email['headers']['Cc'])) {
	// 				$email['headers']['Cc'] .= ", {$to}";
	// 			} else {
	// 				$email['headers']['Cc'] = "Cc: {$to}";
	// 			}
	// 		}
	// 	}
	// 	return $email;
	// }

	private function after_add_contact($contact_id, $email) {
		do_action( 'ifound_activity_log', $contact_id, 'New Contact Registered', $email );

		wp_set_object_terms($contact_id, 'New', $this->taxonomy);

		// The $e used here is an undefined variable. If we ever need this again, the $e should refer to a Gravity Forms
		// entry, not an email address, and we'd need to use $entry['email'] where appropriate. And obviously the
		// caller of this function should pass an entry object.
		// do_action( 'ifound_external_crm_new_submission', apply_filters( 'ifound_obj', $e ) );

		do_action( 'ifound_new_team_lead', $contact_id );
		do_action( 'ifound_wiseagent_new', $contact_id );
	}

	private function get_post_table_fields_data_to_create_contact() {
		$post = [
			'post_status' => 'publish',
			'post_type' => $this->post_type,
			'post_author' => iFoundAdmin::new_hookless()->get_primary_admin_id(),
		];
		// If the visitor is 'bound' to a team member, the contact should be private and
		// owned by that team member.
		$agent_id = apply_filters('ifound_get_agent_id', null);
		if ($agent_id && iFoundTeams::new_hookless()->user_has_team_member_role(['id' => $agent_id])) {
			$post['post_type'] = iFoundPrivateContact::$the_post_type;
			$post['post_author'] = $agent_id;
		}
		return $post;
	}

	public function add_contact($contact_data) {
		$post = $this->get_post_table_fields_data_to_create_contact();
		$post['post_title'] = $contact_data['name'];

		$contact_id = wp_insert_post($post);

		$fname = null;
		$lname = null;
		$name = null;
		if (isset($contact_data['fname'])) {
			$fname = $contact_data['fname'];
			$lname = $contact_data['lname'];
			$name = $fname . ' ' . $lname;
		} else {
			$name = $contact_data['name'];
			$name_parts = explode(' ', $name);
			$fname = array_shift($name_parts);
			$lname = implode(' ', $name_parts);
		}

		$contact_info = [
			'title' => $name,
			'fname' => $fname,
			'lname' => $lname,
			'email' => $contact_data['email'],
			'acquired_date' => current_time( 'mysql' ),
		];
		if (isset($contact_data['social_login_type'])) {
			$social_login_type = $contact_data['social_login_type'];
			$user_id_meta_key = $social_login_type . '_user_id';
			update_post_meta($contact_id, $user_id_meta_key, $contact_data['social_login_user_id']);
			$contact_info[$user_id_meta_key] = $contact_data['social_login_user_id'];
		}
		$entry = $this->save_contact_meta( $contact_id, $contact_info );
		update_post_meta($contact_id, 'title', $contact_info['title']);

		// Add Gravity Form entry and send notification. This keeps the functionality of this method on par with when
		// the user registers via the form.
		$registration_form_id = get_option('ifound_registration_settings')['registration_form_id'];
		if ($registration_form_id) {
			$form = GFAPI::get_form($registration_form_id);
			if ($form) {
				$entry = [
					'form_id' => $registration_form_id,
					'1.3' => $fname,
					'1.6' => $lname,
					'2' => $contact_data['email'],
				];
				GFAPI::add_entry($entry, $form['id']);
				GFAPI::send_notifications($form, $entry);
			}
		}

		$this->after_add_contact($contact_id, $contact_data['email']);
		add_post_meta( $contact_id, 'track_code' , intval( $_COOKIE[$this->cookie] ) );
		return $contact_id;
	}

	/**
	 * Unsubscribe
	 *
	 * Display this Unsubscribe.
	 *
	 * @since 1.0.7
	 */

	public function unsubscribe(){

		$settings = get_option( 'ifound_unsubscribe_settings' );
		$body = $settings['unsubscribe_body'] ?: false;

		if( $body ) {

			echo $body;

		}

	}

	/**
	 * Contact Profile
	 *
	 * The contact info displayed in the contact record.
	 *
	 * @since 1.0.0
	 * @since 1.0.26 Add ifound_external_crm_contact_inputs action hook.
	 */

	public function contact_profile( $contact_id = false ) {

 		wp_enqueue_script( 'contacts_js' );
		wp_enqueue_script( 'jquery-ui-datepicker' );

		$contact_id = is_int( $contact_id ) ? $contact_id : get_the_ID();
		$is_unsubscribed = has_term('unsubscribed', static::$the_taxonomy, $contact_id);
		?>

		<div class="contact_meta">

			<div class="inside">

				<? echo get_avatar( get_post_meta( $contact_id, 'email', true ), 50 ); ?>
				<? if (iFoundContactsAdmin::new_hookless()->is_contact_from_mls($contact_id)) { ?>
				<div style="float: right;">
					Imported from <img src="<?= iFoundContactsAdmin::new_hookless()->get_mls_image() ?>">
				</div>
				<? } ?>
				<div style="float: right;">
					<? if ($is_unsubscribed): ?>
						<div>
						<span style="color: red;">
							<i class="fas fa-ban" area-hidden="true"></i>
							<i class="far fa-envelope" area-hidden="true"></i>
						</span>
							<span class="contact_sms_opt_out_msg">This contact has unsubscribed from email</span>
						</div>
					<? endif ?>
					<? if (get_post_meta($contact_id, static::$CONTACT_SMS_OPT_OUT_KEY, true) ===
						iFoundSms::$CONTACT_SMS_OPT_OUT_YES
					) { ?>
						<div>
						<span style="color: red;">
							<i class="fas fa-ban" area-hidden="true"></i>
							<i class="far fa-comment" area-hidden="true"></i>
						</span>
							<span class="contact_sms_opt_out_msg">This contact has opted out of text messages</span>
						</div>
					<? } ?>
				</div>
			</div>

			<table class="form-table contact-table">

				<tbody>

					<tr>

						<th scope="row"><label for="acquired_date"><? _e( 'Acquired Date:', 'ifound' ); ?></label></th>
						<td>
							<input type="text" name="acquired_date" id="acquired_date" class="regular-text contacts-datepicker" value="<? echo get_post_meta( $contact_id, 'acquired_date', true ); ?>">
						</td>

						<th scope="row"><label for="acquired_from"><? _e( 'Acquired From:', 'ifound' ); ?></label></th>
						<td><input type="text" name="acquired_from" id="acquired_from" value="<? echo get_post_meta( $contact_id, 'acquired_from', true ); ?>" class="regular-text"></td>

					</tr>

					<tr>

						<th scope="row"><label for="fname"><? _e( 'First Name', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="fname" id="fname" value="<? echo get_post_meta( $contact_id, 'fname', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="lname"><? _e( 'Last Name', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="lname" id="lname" value="<? echo get_post_meta( $contact_id, 'lname', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="email"><? _e( 'Email', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="email" id="email" value="<? echo get_post_meta( $contact_id, 'email', true ); ?>" class="regular-text required-input">

						</td>

						<th scope="row"><label for="email2"><? _e( 'Email 2', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="email2" id="email2" value="<? echo get_post_meta( $contact_id, 'email2', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="hphone"><? _e( 'Home Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="hphone" id="hphone" value="<? echo get_post_meta( $contact_id, 'hphone', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="wphone"><? _e( 'Work Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="wphone" id="wphone" value="<? echo get_post_meta( $contact_id, 'wphone', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="mphone"><? _e( 'Mobile Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="mphone" id="mphone" value="<? echo get_post_meta( $contact_id, 'mphone', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="birthday"><? _e( 'Birthday', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="birthday" id="birthday" class="regular-text contacts-datepicker" value="<? echo get_post_meta( $contact_id, 'birthday', true ); ?>">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="address"><? _e( 'Address', 'ifound' ); ?></label></th>

						<td colspan="3">

							<input type="text" name="address" id="address" value="<? echo get_post_meta( $contact_id, 'address', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="address_2"><? _e( 'Address 2', 'ifound' ); ?></label></th>

						<td colspan="3">

							<input type="text" name="address_2" id="address_2" value="<? echo get_post_meta( $contact_id, 'address_2', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="city"><? _e( 'City', 'ifound' ); ?></label></th>

						<td colspan="3">

							<input type="text" name="city" id="city" value="<? echo get_post_meta( $contact_id, 'city', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="state"><? _e( 'State:', 'ifound' ); ?></label></th>
						<td>
							<input type="text" name="state" id="state" class="regular-text" value="<? echo get_post_meta( $contact_id, 'state', true ); ?>" placeholder="2 digit state abbreviation">
						</td>

						<th scope="row"><label for="zip"><? _e( 'Zip:', 'ifound' ); ?></label></th>
						<td><input type="text" name="zip" id="zip" value="<? echo get_post_meta( $contact_id, 'zip', true ); ?>" class="regular-text"></td>

					</tr>

				</tbody>

			</table>

			<h4 class="spouse"><? _e( 'Spouse Info', 'ifound' ); ?></h4>

			<table class="form-table contact-table">

				<tbody>

					<tr>

						<th scope="row"><label for="fname_spouse"><? _e( 'First Name', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="fname_spouse" id="fname_spouse" value="<? echo get_post_meta( $contact_id, 'fname_spouse', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="lname_spouse"><? _e( 'Last Name', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="lname_spouse" id="lname_spouse" value="<? echo get_post_meta( $contact_id, 'lname_spouse', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="email_spouse"><? _e( 'Email', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="email_spouse" id="email_spouse" value="<? echo get_post_meta( $contact_id, 'email_spouse', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="email2_spouse"><? _e( 'Email 2', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="email2_spouse" id="email2_spouse" value="<? echo get_post_meta( $contact_id, 'email2_spouse', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="hphone_spouse"><? _e( 'Home Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="hphone_spouse" id="hphone_spouse" value="<? echo get_post_meta( $contact_id, 'hphone_spouse', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="wphone_spouse"><? _e( 'Work Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="wphone_spouse" id="wphone_spouse" value="<? echo get_post_meta( $contact_id, 'wphone_spouse', true ); ?>" class="regular-text">

						</td>

					</tr>

					<tr>

						<th scope="row"><label for="mphone_spouse"><? _e( 'Mobile Phone', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="mphone_spouse" id="mphone_spouse" value="<? echo get_post_meta( $contact_id, 'mphone_spouse', true ); ?>" class="regular-text">

						</td>

						<th scope="row"><label for="birthday_spouse"><? _e( 'Birthday', 'ifound' ); ?></label></th>

						<td>

							<input type="text" name="birthday_spouse" id="birthday_spouse" class="regular-text contacts-datepicker" value="<? echo get_post_meta( $contact_id, 'birthday_spouse', true ); ?>">

						</td>

					</tr>

				</tbody>

			</table><? do_action( 'ifound_external_crm_contact_inputs' ); ?>

		</div><?

	}

	// Disable both search campaigns and drip campaigns.
	// Originally meant for when a contact unsubscribes. If there's another use, be sure to update the message we pass
	// to the ifound_update_campaign_status action.
	public function disable_all_campaigns($contact_id) {
		$build_campaign_args = function($post_type, $taxonomy) use ($contact_id) {
			$args = array(
				'post_type' 		=> $post_type,
				'post_status'		=> 'publish',
				'posts_per_page' 	=> -1,
				'fields'			=> 'ids',
				'tax_query' 		=> array(
					array(
						'taxonomy'	=> ifoundsavethis::$the_taxonomy2,
						'field'    	=> 'slug',
						'terms'    	=> array( 'active' )
					)
				),
				'meta_query' => array(
					array(
						'key'     	=> 'contact_id',
						'value' 	=> $contact_id,
					),
				),
			);
			return $args;
		};

		$search_campaign_args = $build_campaign_args(iFoundSaveThis::$the_post_type, iFoundSaveThis::$the_taxonomy);
		$post_ids = get_posts($search_campaign_args);
		foreach ($post_ids as $post_id) {
			do_action('ifound_update_campaign_status', $post_id, 'inactive', 'Contact unsubscribed');
		}

		$drip_campiagn_args = $build_campaign_args(iFoundDripCampaign::$the_post_type,
			iFoundDripCampaign::$the_post_type);
		$post_ids = get_posts($drip_campiagn_args);
		foreach ($post_ids as $post_id) {
			do_action('ifound_update_campaign_status', $post_id, 'inactive', 'Contact unsubscribed');
		}
	}

	public function contacts_status_row_actions($actions, $term) {
		// Always remove these actions. They show advanced stuff that we don't use and would only confuse users.
		unset($actions['edit']);
		unset($actions['view']);

		if ($this->is_essential_contact_status($term->slug)) {
			unset($actions['delete']);
			unset($actions['inline hide-if-no-js']);
			$actions['may-not-alter'] = 'You may not alter this status';
		}
		return $actions;
	}

	public function pre_delete_term($term_id) {
		$term = get_term($term_id);
		if ($term->taxonomy === static::$the_taxonomy && $this->is_essential_contact_status($term->slug)) {
			wp_die('You may not delete this contact status');
		}
	}

	private function is_essential_contact_status($slug) {
		return in_array($slug, static::$essential_contact_status_slugs);
	}

	// Hide these columns by default.
	public function default_hidden_columns($hidden, $screen) {
		if ($screen->id === 'edit-' . static::$the_post_type) {
			return ['mls_contact', 'wiseagent'];
		}
		return $hidden;
	}
}
