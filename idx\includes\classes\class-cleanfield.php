<?php

class Cleaner {

	private $fixup = array(
		'to_number' => array(
			'Bathrooms',
			'FEAT20110510163155967817000000',       // Parking
			'FEAT20110510163434654648000000',       // Carport Spaces
			'FEAT20110510163623017500000000',       // Slab Parking
			'LotSquareFeet',
			'DaysMarket',
		),

		'fix_trailzero' => array('Bathrooms'),

		'fix_roundformat' => array('SquareFeet'),

		'to_dollars' => array(
			'CleaningDepositFee',
			'ClosePrice',
			'CreditCheckAmount',
			'EarnestDeposit',
			'HOAFee',
			'ListPrice',
			'MonthlyRateHigh',
			'MonthlyRateLow',
			'PetDepositFee',
			'SecurityDeposit',
			'TotalTaxes',
		),

		'ucwords2' => array(
			'BuilderName',
			'City',
			'County',
			'CrossStreet',
			'ElementarySchool',
			'HighSchool',
			'MiddleSchool',
			'StreetName',
			'StreetSuffix',
		),

		'ucwords3' => array(
			'Subdivision',
		),

		'fix_sentencecase' => array('BrokerRemarks'),

		'short_date' => array('ModificationTimestamp', 'ListDate'),

		'yes_or_no' => array('HOA'),

		'reword' => array('DeedType'),

		'fix_commas' => array(
			'Appliances',
			'Attics',
			'BasementType',
			'CentralAir',
			'Certifications',
			'CookingFuel',
			'Cooling',
			'DiningKitchen',
			'KitchenFeatures',
			'Exterior',
			'ExteriorFeatures',
			'ExtraBedFeatures',
			'FireplaceCount',
			'FireplaceFeatures',
			'Floor',
			'Heating',
			'HotWater',
			'InteriorFeatures',
			'LandFeatures',
			'Landscaping',
			'LaundryType',
			'LotDescription',
			'MainBedroom',
			'MasterBathFeatures',
			'PoolType',
			'PorchDeck',
			'RentIncludes',
			'Roof',
			'RoomList',
			'Spa',
			'Technology',
			'Utilities',
		),
	);

	public $funcmap;

	public function __construct() {
		// Create a reverse map for each of the fields
		foreach ($this->fixup as $funcname => $fields) {
			foreach ($fields as $id) {
				$this->funcmap[$id] = $funcname;
			}
		}
	}

    /**
     * @param $data_array
     * @return array
     */
    public function cleanArray($data_array) {
	    foreach ($data_array as $key => $val) {
			$data_array[$key] = $this->cleanField($key, $val);
	    }
		return $data_array;
    }

    private function reword($val) {
    	switch ($val) {
    		case 'Fee Simple':
    			return 'Single Family Residence';
    	}
    	return $val;
    }

	private function to_number($val) {
		return number_format(floatval($val));
	}

	private function ucwords2($string) {
		if (is_array($string)) {
			return ucwords(strtolower($string[0]));
		}
		return ucwords(strtolower($string));
	}

	private function ucwords3($string) {
	   return preg_replace_callback('/[A-Z]{3,}/', array('Cleaner', 'ucwords2'), $string);
	}

	private function yes_or_no($val) {
		return $val;
	}

	private function short_date($datestr) {
	       return date('n/j/Y', strtotime($datestr));
	}
	/**
	 * @param $strg
	 * @return string
	 */
	private function fix_shortdate($strg) {
		return date('n/j/Y', strtotime($strg));
	}

    /**
     * This calls the various methods of the
     * class to clean each field appropriately.
     *
     * @param $key
     * @param $value
     * @return $value
     */
    public function cleanField($key, $value){
	    if (array_key_exists($key, $this->funcmap)) {
		    $funcname = $this->funcmap[$key];
            return call_user_func(array($this, $funcname), $value);
	    } else {
            return $value;
        }
    }

    /**
     * Correction to items like bath
     * and any other .0 items
     *
     * @param $strg
     * @return string
     */
    private function fix_trailzero($strg){
	    return rtrim($strg, ".0");
    }

    /**
     * Correction to numbers that need rounding and format
     * Like sqft
     *
     * @param $strg
     * @return string
     */
    private function fix_roundformat($strg){
        if ($strg === null) {
            return 0;
        }

        return number_format(str_replace(',', '', $strg));
    }

    /**
     * Correction to items of price
     *
     * @param $val
     * @return string
     */
    private function to_dollars($val) {
	    return "$" . number_format(floatval($val));
    }

    /**
     * @param $strg
     * @return string
     */
    private function fix_sentencecase($strg){

	    $ret = '';
        $str = strtolower($strg);
        $cap = true;
        for($x = 0; $x < strlen($str); $x++){
            $letter = substr($str, $x, 1);
            if($letter == "." || $letter == "!" || $letter == "?"){
                $cap = true;
            }elseif($letter != " " && $cap == true){
                $letter = strtoupper($letter);
                $cap = false;
            }
            $ret .= $letter;
        }
        return $ret;

    }

    // The values in RETS are often missing spaces after commas (presumably to allow the lister more "content" without
	// the "fluff" of the whitespace. However, it breaks word-wrapping. So we force all commas to be replaced with a comma
	// and then a space.
	protected function fix_commas($val) {
		return str_replace(",", ", ", $val);
	}
}
