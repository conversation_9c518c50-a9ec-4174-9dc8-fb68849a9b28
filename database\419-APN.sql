alter table armls_property_A add column APN varchar(20);
alter table armls_property_A change column APN char(8);

update armls_property_A set APN = concat(lpad(LIST_124, 3, '0'), lpad(LIST_125, 2, '0'), lpad(LIST_126, 3, '0'));

drop trigger if exists APN_calculation;

delimiter //
CREATE TRIGGER APN_calculation
BEFORE INSERT ON armls_property_A
FOR EACH ROW
BEGIN
  SET NEW.APN = concat(lpad(NEW.LIST_124, 3, '0'), lpad(NEW.LIST_125, 2, '0'), lpad(NEW.LIST_126, 3, '0'));
END;//
delimiter ;

create index APN_index on armls_property_A (APN);

-- Create indexes for 'most common' search fields, which to me means
-- the fields we use on our quick search form.

-- ARMLS
-- city
-- I chose 50 because the longest current city is Unincorporated County at 21 chars.
create index LIST_39 on armls_property_A (LIST_39(50));
-- subdivision
create index LIST_131 on armls_property_A (LIST_131);
-- zip
create index LIST_43 on armls_property_A (LIST_43(5));
-- bedrooms
create index LIST_66 on armls_property_A (LIST_66);
-- bathrooms
create index LIST_67 on armls_property_A (LIST_67);
optimize table armls_property_A;

-- SNDMLS
-- city
create index CITY_f58 on sndmls_property_9 (CITY_f58);
-- subdivision
-- I chose 30 because the longest current property is CARMEL MOUNTAIN RANCH at 21 chars.
create index COMMUNITY_f61 on sndmls_property_9 (COMMUNITY_f61(30));
-- zip
create index RETSZIP_f2030 on sndmls_property_9 (RETSZIP_f2030);
-- bedrooms
create index TOTBEDS_f1711 on sndmls_property_9 (TOTBEDS_f1711);
-- bathrooms
create index TOTBATHS_f1603 on sndmls_property_9 (TOTBATHS_f1603);
optimize table sndmls_property_9;

-- TRENDMLS
-- city
create index CityName on trendmls_property_RES (CityName);
-- subdivision
create index Subdivision on trendmls_property_RES (Subdivision);
-- zip
create index PostalCode on trendmls_property_RES (PostalCode);
-- bedrooms
create index Beds on trendmls_property_RES (Beds);
-- bathrooms
-- There is no mapping for bathrooms for Trendmls currently.
optimize table trendmls_property_RES;
