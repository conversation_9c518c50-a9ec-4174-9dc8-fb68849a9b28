<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundEmailTrackingPixel {
	use NewHooklessTrait;
	use UtilTrait;

	// Does not include WP table prefix, e.g. wp_ or wp_123_
	public static $table_name = 'ifound_emails';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'do_email_tracking_update_part1'));
			add_action('init', array($this, 'do_email_tracking_update_part2'));
			add_action('init', array($this, 'do_email_tracking_update_part3'));
		}
	}

	// The pixel_id is the part after the prefix, i.e. in ifound_tracking_pixel:123, it's the 123.
	public function find_by_pixel_id($pixel_id) {
		global $wpdb;

		$table_name = $this->get_table_name();
		$sql = "select * from {$table_name} where tracking_pixel = %s;";
		$prepared_sql = $wpdb->prepare($sql, $pixel_id);
		$results = $wpdb->get_results($prepared_sql, ARRAY_A);
		if (count($results)) {
			$record = $results[0];
			// Good googly moogly. It's 2022 but I'm still dealing with database drivers converting results to string.
			// Change them back to integer where appropriate.
			$int_field_names = ['id', 'meta_id', 'read_count'];
			foreach ($int_field_names as $int_field_name) {
				$record[$int_field_name] = intval($record[$int_field_name]);
			}
			return $record;
		}
		return null;
	}

	public function find_by_post_id($post_id) {
		global $wpdb;

		$table_name = $this->get_table_name();
		$sql = <<<EOT
			SELECT *
			FROM {$table_name} ife
			JOIN {$wpdb->postmeta} pm on pm.meta_id = ife.meta_id
			JOIN {$wpdb->posts} p on p.ID = pm.post_id
			WHERE p.ID = %d
		EOT;
		$records = $wpdb->get_results($wpdb->prepare($sql, $post_id), ARRAY_A);
		return $records;
	}

	public function create($record) {
		global $wpdb;

		$record['date_sent_gmt'] = get_gmt_from_date($record['date_sent']);

		$wpdb->insert($this->get_table_name(), $record);
	}

	// $type is 'read' or 'clicked'
	public function update_by_pixel_id($pixel_id, $data, $type) {
		global $wpdb;

		$fields_to_update_gmt = ["date_first_{$type}", "date_most_recent_{$type}"];
		foreach ($fields_to_update_gmt as $field_name) {
			$data[$field_name . '_gmt'] = get_gmt_from_date($data[$field_name]);
		}

		$wpdb->update($this->get_table_name(), $data, ['tracking_pixel' => $pixel_id]);
	}

	public function get_table_name() {
		return $this->util()->get_table_name(static::$table_name);
	}

	// The general idea here is we used to store email tracking information ('pixels', allowing us to know when an email
	// has been read) in the postmeta table. But that's too slow when we want to run reports and sort. So we create our
	// own table.
	// I'm calling it part 1 because what's not being done here is deleting the postmeta records that currently store
	// the data. After we're sure everything is working as expected, we can do that, calling it part 2.
	public function do_email_tracking_update_part1() {
		global $wpdb;

		// 25753321 is the case number in teamwork.com
		$option_name_prefix = 'ifound_update_25753321_part1_';

		if (get_option($option_name_prefix . 'done', 'no') === 'yes') {
			return;
		}

		$email_tracking_meta_key = iFoundActivity::$email_tracking_meta_key;

		$table_name = $this->get_table_name();
		$sql = <<<EOT
			CREATE TABLE IF NOT EXISTS $table_name (
				id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
				meta_id bigint(20) unsigned not null,
				tracking_pixel char(32) not null,
				to_email varchar(255) not null,
				date_sent datetime not null,
				date_sent_gmt datetime not null,
				date_first_read datetime,
				date_first_read_gmt datetime,
				date_most_recent_read datetime,
				date_most_recent_read_gmt datetime,
				read_count smallint unsigned not null default 0,
				primary key (id),
				unique key meta_id (meta_id),
				unique key tracking_pixel (tracking_pixel),
				key to_email (to_email),
				key date_sent (date_sent),
				key date_sent_gmt (date_sent_gmt),
				key date_first_read (date_first_read),
				key date_first_read_gmt (date_first_read_gmt),
				key date_most_recent_read (date_most_recent_read),
				key date_most_recent_read_gmt (date_most_recent_read_gmt)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
EOT;
		// Why not use WP's dbDelta function here? Because it has a number of shortcomings, and shouldn't be needed
		// because this plugin does all upgrade steps sequentially.
		$result = $wpdb->query($sql);

		$offset = get_option($option_name_prefix . 'offset', 0);
		$limit = 5000;

		do {
			$sql = <<<EOT
				select meta_id, meta_value
				from {$wpdb->prefix}postmeta
				where meta_key = 'activity_log'
				and meta_value like '%$email_tracking_meta_key:%'
				order by meta_id
				limit $limit
				offset {$offset}
				;
EOT;
			$activity_meta_records = $wpdb->get_results($sql, ARRAY_A);

			$sql = <<<EOT
				select meta_key, meta_value
				from {$wpdb->prefix}postmeta
				where meta_key like '$email_tracking_meta_key:%'
				order by meta_id
				limit $limit
				offset {$offset}
				;
EOT;
			$tracking_meta_records = $wpdb->get_results($sql, ARRAY_A);
			$tracking_by_pixel_id = array_reduce($tracking_meta_records, function($a, $v) {
				$tracking_pixel = substr($v['meta_key'], -32);
				$a[$tracking_pixel] = $v;
				return $a;
			}, []);

			$wpdb->query('START TRANSACTION');
			foreach ($activity_meta_records as $activity_meta_record) {
				$meta_id = $activity_meta_record['meta_id'];
				$meta_value = $activity_meta_record['meta_value'];
				$activity_log_items = explode(';', $meta_value);
				$tracking_pixel = null;
				foreach ($activity_log_items as $field) {
					if (strpos($field, $email_tracking_meta_key) === 0) {
						$tracking_pixel = substr($meta_value, -32);
					}
				}
				if (!$tracking_pixel) {
					continue;
				}

				$date_sent = $activity_log_items[0];
				$date_sent_gmt = get_gmt_from_date($date_sent);
				$tracking_record_serialized = $tracking_by_pixel_id[$tracking_pixel];
				$tracking_record = unserialize($tracking_record_serialized['meta_value']);
				$email_address_item = $activity_log_items[2];
				$email_sent_message = iFoundEmail::$success_message_base;
				$email_sent_message_length = strlen($email_sent_message);
				if (strpos($email_address_item, $email_sent_message) === 0) {
					$email_address = substr($email_address_item, $email_sent_message_length);
				}
				$date_first_read_gmt = null;
				if ($tracking_record['first_read']) {
					$date_first_read_gmt = get_gmt_from_date($tracking_record['first_read']);
				}
				$date_most_recent_read_gmt = null;
				if ($tracking_record['most_recent_read']) {
					$date_most_recent_read_gmt = get_gmt_from_date($tracking_record['most_recent_read']);
				}
				$read_count = $tracking_record['read_count'] ?: 0;
				$data = [
					'meta_id'                   => $meta_id,
					'tracking_pixel'            => $tracking_pixel,
					'to_email'                  => $email_address,
					'date_sent'                 => $date_sent,
					'date_sent_gmt'             => $date_sent_gmt,
					'date_first_read'           => $tracking_record['first_read'],
					'date_first_read_gmt'       => $date_first_read_gmt,
					'date_most_recent_read'     => $tracking_record['most_recent_read'],
					'date_most_recent_read_gmt' => $date_most_recent_read_gmt,
					'read_count'                => $read_count,
				];
				$wpdb->insert(iFoundEmailTrackingPixel::new_hookless()->get_table_name(), $data);
			}
			$offset += $limit;
			update_option($option_name_prefix . 'offset', $offset);
			$wpdb->query('COMMIT');
		} while (count($activity_meta_records) >= $limit);

		update_option($option_name_prefix . 'done', 'yes');
		delete_option($option_name_prefix . 'offset');
	}

	public function do_email_tracking_update_part2() {
		global $wpdb;

		$option_name_prefix = 'ifound_update_25753321_part2_';
		if (get_option($option_name_prefix . 'done', 'no') === 'yes') {
			return;
		}

		$email_tracking_meta_key = iFoundActivity::$email_tracking_meta_key;
		$sql = "delete from {$wpdb->postmeta} where meta_key like '{$email_tracking_meta_key}:%'";
		$wpdb->query($sql);

		update_option($option_name_prefix . 'done', 'yes');
	}

	public function do_email_tracking_update_part3() {
		global $wpdb;

		$option_name = 'ifound_update_25753321_part3_status';
		if (in_array(get_option($option_name, 'none'), ['done', 'pending'])) {
			return;
		}
		// Immediately mark it as pending so only one 'thread' (PHP request) does this work.
		update_option($option_name, 'pending');

		$table_name = $this->get_table_name();
		$sql = <<<EOT
			alter table {$table_name}

			add column type varchar(50),
			add column date_first_clicked datetime,
			add column date_first_clicked_gmt datetime,
			add column date_most_recent_clicked datetime,
			add column date_most_recent_clicked_gmt datetime,
			add column clicked_count smallint unsigned not null default 0,

			add index type (type),
			add index date_first_clicked (date_first_clicked),
			add index date_first_clicked_gmt (date_first_clicked_gmt),
			add index date_most_recent_clicked (date_most_recent_clicked),
			add index date_most_recent_clicked_gmt (date_most_recent_clicked_gmt)
			;
EOT;
		$wpdb->query($sql);

		update_option($option_name, 'done');
	}

	public function has_campaign_sent_out_too_many_emails_recently($save_this_id) {
		global $wpdb;

		$table_name = $this->get_table_name();
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_email_overactivity().
		$max_emails = 10;
		// The offset is the number to skip, so we want one less than the max.
		$offset = $max_emails - 1;
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_overactivity().
		$time_period = 60 * 60 * 24;
		// This try/catch is because better safe than sorry. Safer to not kill further processing if some calculation
		// here is off.
		try {
			$sql = <<<EOT
			select date_sent_gmt
			from $table_name ife
			join {$wpdb->postmeta} pm on pm.meta_id = ife.meta_id
			join {$wpdb->posts} p on p.id = pm.post_id
			where p.id = $save_this_id
			order by ife.date_sent_gmt desc
			limit 1
			offset $offset
EOT;
			$results = $wpdb->get_results($sql, ARRAY_A);
			if (count($results) === 1) {
				$date_sent_gmt = $results[0]['date_sent_gmt'];
				$date_sent_gmt_timestamp = strtotime($date_sent_gmt);
				$current_time = current_time('U', true);
				$diff_seconds = $current_time - $date_sent_gmt_timestamp;
				if ($diff_seconds <= $time_period) {
					return true;
				}
			}
		} catch (Exception $e) {
			// Do nothing. We return false below.
		}
		return false;
	}

	public function has_campaign_sent_out_too_many_unread_emails($save_this_id) {
		global $wpdb;

		$table_name = $this->get_table_name();
		// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_unread_emails().
		$max_emails = 15;
		// This try/catch is because better safe than sorry. Safer to not kill further processing if some calculation
		// here is off.
		try {
			$enabled_date_gmt = get_post_meta($save_this_id, iFoundSaveThis::$ENABLED_DATETIME_KEY, true);
			// We only count since the time the campaign was enabled. This means if the campaign re-enabled, the
			// enabled date will be changed. The enabled date is a new piece of meta data, so if it doesn't exist,
			// we don't look at it.
			$enabled_date_clause = $enabled_date_gmt
				? "and ife.date_sent_gmt > '$enabled_date_gmt'"
				: '';
			$sql = <<<EOT
			select date_sent_gmt, read_count
			from $table_name ife
			join {$wpdb->postmeta} pm on pm.meta_id = ife.meta_id
			join {$wpdb->posts} p on p.id = pm.post_id
			where p.id = $save_this_id
			$enabled_date_clause
			order by ife.date_sent_gmt desc
			limit $max_emails
EOT;
			$results = $wpdb->get_results($sql, ARRAY_A);
			$results = array_map(function($x) {
				$x['read_count'] = intval($x['read_count']);
				return $x;
			}, $results);
			$unread_emails = array_filter($results, function($x) {
				return $x['read_count'] === 0;
			});
			if (count($unread_emails) === $max_emails) {
				return true;
			}
			if (count($results) > 0) {
				$most_recent_read_email = $this->util()->array_find($results, function($x) {
					return $x['read_count'] > 0;
				});
				if ($most_recent_read_email) {
					$date_sent_gmt = $most_recent_read_email['date_sent_gmt'];
					$date_sent_gmt_timestamp = strtotime($date_sent_gmt);
					$current_time = current_time('U', true);
					$diff_seconds = $current_time - $date_sent_gmt_timestamp;
					$how_often = get_post_meta($save_this_id, 'how_often', true);
					$date_interval = iFoundSaveThis::new_hookless()->get_date_interval_from_how_often($how_often);
					$date_interval_seconds = $this->util()->date_interval_to_seconds($date_interval);
					// If $how_often is Monthly, then we want to wait 5 months.
					// Make sure this matches with iFoundProcessAlerts::disable_campaign_due_to_unread_emails().
					$multiplier = 5;
					if ($diff_seconds >= $multiplier * $date_interval_seconds) {
						return true;
					}
				}
			}
		} catch (Exception $e) {
			// Do nothing. We return false below.
		}
		return false;
	}

	// $type is 'read' or 'clicked'
	public function increment_stat($pixel_id, $type) {
		$record = $this->find_by_pixel_id($pixel_id);
		if ($record) {
			try {
				$record_to_update = $this->util()->pick($record, [
					"{$type}_count",
					"date_first_{$type}",
					"date_most_recent_{$type}",
				]);
				$record_to_update["{$type}_count"]++;
				$current_time = current_time('mysql');
				if (!$record_to_update["date_first_{$type}"]) {
					$record_to_update["date_first_{$type}"] = $current_time;
				}
				$record_to_update["date_most_recent_{$type}"] = $current_time;
				$this->update_by_pixel_id($pixel_id, $record_to_update, $type);

				// If type was clicked and the meta_id is from a campaign, put a "clicked" message in its activity log,
				// as well as the contact's activity log.
				if ($type === 'clicked') {
					$post_id = iFound::new_hookless()->get_post_id_from_meta_id($record['meta_id']);
					if ($post_id) {
						$post = get_post($post_id);
						if ($post && $post->post_type === iFoundSaveThis::$the_post_type) {
							iFoundSharedCampaign::new_hookless()->add_activity_log_click($post->ID, 'email',
								$record['to_email']);
						}
					}
				}
			} catch (Exception $e) {
				// Something went wrong. Don't bother trying to update the DB.
			}
		}
	}

	private function _get_counts($author_ids_param, $types, $filter) {
		global $wpdb;
		$table_name = $this->get_table_name();
		$interval = '-30 day';

		$type_param = join(', ', array_map(function($x) { return "'{$x}'"; }, $types));
		$limiter = '';
		if ($filter) {
			if (!in_array($filter, ['read', 'clicked'], true)) {
				throw new Exception("Invalid filter: '$filter'");
			}
			$limiter = "and iftp.date_first_${filter}_gmt is not null";
		}
		$sql = <<<EOT
		select count(*) as count
		from {$wpdb->posts} p
		join {$wpdb->postmeta} pm on pm.post_id = p.ID
		join {$table_name} iftp on iftp.meta_id = pm.meta_id
		where p.post_author in ({$author_ids_param})
		and iftp.date_sent_gmt > date_add(now(), interval {$interval})
		and iftp.type in ({$type_param})
		{$limiter}
		;
EOT;
		$count = intval($wpdb->get_results($sql, ARRAY_A)[0]['count']);
		return $count;
	}

	private function get_counts($author_ids_param, $types) {
		$sent = $this->_get_counts($author_ids_param, $types, '');
		$read = $this->_get_counts($author_ids_param, $types, 'read');
		$clicked = $this->_get_counts($author_ids_param, $types, 'clicked');
		return [
			'sent' => $sent,
			'read' => $read,
			'clicked' => $clicked,
		];
	}

	private function get_stats() {
		$author_ids = iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids();
		$author_ids_param = join(',', $author_ids);

		$all_types = [
			iFoundEmail::$CAMPAIGN_ALERT,
			iFoundEmail::$INSTANT_UPDATE,
			iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED,
		];
		$data = [
			iFoundEmail::$CAMPAIGN_ALERT => $this->get_counts($author_ids_param, [iFoundEmail::$CAMPAIGN_ALERT]),
			iFoundEmail::$INSTANT_UPDATE => $this->get_counts($author_ids_param, [iFoundEmail::$INSTANT_UPDATE]),
			iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED => $this->get_counts($author_ids_param,
				[iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED]),
			'totals' => $this->get_counts($author_ids_param, $all_types),
		];
		return $data;
	}

	public function display_stats() {
		$data = $this->get_stats();
		$title_map = [
			iFoundEmail::$CAMPAIGN_ALERT => 'Campaign',
			iFoundEmail::$INSTANT_UPDATE => 'Instant Update',
			iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED => 'Instant Update <br /> Recently Closed',
			'totals' => 'Total',
		];
		?>
		<div class="ifound_email_stats">
			<table class="striped widefat">
				<thead>
				<tr>
					<th></th>
					<th>Sent</th>
					<th>Read</th>
					<th>Read %</th>
					<th>Unique Clicks</th>
					<th>Click %</th>
				</tr>
				</thead>
				<tbody>
				<?
				foreach ($data as $type => $row) {
					$title = $title_map[$type];
					$sent = $row['sent'];
					$read = $row['read'];
					$read_percent_str = $this->util()->get_percent_str($read, $sent);
					$clicked = $row['clicked'];
					$clicked_percent_str = $this->util()->get_percent_str($clicked, $sent);
					$css_class = $type === 'totals' ? 'totals' : '';
					?>
					<tr>
						<td class="<?= $css_class ?>"><?= $title ?></td>
						<td class="<?= $css_class ?>"><?= $sent ?></td>
						<td class="<?= $css_class ?>"><?= $read ?></td>
						<td class="<?= $css_class ?>"><?= $read_percent_str ?></td>
						<td class="<?= $css_class ?>"><?= $clicked ?></td>
						<td class="<?= $css_class ?>"><?= $clicked_percent_str ?></td>
					</tr>
					<?
				}
				?>
				</tbody>
			</table>
		</div>
		<?php
	}
}
