<?php

defined('ABSPATH') or die('You do not have access!');

class iFoundSearchController {
	use UtilTrait;

	public static $endpoint_namespace = 'ifound';
	public static $endpoint_base = '/multibox';
	private static $max_results = 7;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('rest_api_init', [$this, 'rest_api_init_hook']);
	}

	public function rest_api_init_hook() {
		$this->register_routes();
	}

	private function register_routes() {
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/search', array(
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array($this, 'search'),
			),
		));
	}

	public function search(WP_REST_Request $request) {
		$q = $request->get_param('q');
		$mls_class = $request->get_param('mls_class');
		$mls_class_code = iFoundIdx::new_hookless()->mls_class($mls_class);
		$data_from_web_request = $this->query_idx_multibox($q, $mls_class_code);
		$data = [
			'city'        => $this->search_city($q),
			'school'      => $this->search_school($q),
			'subdivision' => $data_from_web_request['subdivision'],
			'mls_area'    => $data_from_web_request['mls_area'],
			'zip'         => $this->search_zip($q),
			'address'     => $data_from_web_request['address'],
			'mls_id'      => $data_from_web_request['mls_id'],
		];
		if (iFoundIdx::mls_name() === 'realtracs') {
			$data['county'] = $this->search_county($q);
			$this->util()->move_item($data, 'county', 'down', 'city');
		}
		if (iFoundIdx::mls_name() === 'brightmls') {
			$data['school_district'] = $this->search_school_district($q);
			$this->util()->move_item($data, 'school_district', 'down', 'school');
			$data['township'] = $this->search_township($q);
			$this->util()->move_item($data, 'township', 'down', 'city');
		}
		return new WP_REST_Response($data, 200);
	}

	// Just a simple way to make it easy to compare input terms to possible results. Maybe it's not good enough, but
	// it's a start.
	private function simplify($str) {
		$new_str = strtolower($str);
		// Remove punctuation and whitespace.
		$new_str = preg_replace('/[[:punct:]]|\s/', '', $new_str);
		return $new_str;
	}

	private function search_city($q) {
		$cities = iFoundSearch::new_hookless()->lookups_to_criteria()->city->values;
		$results = [];
		$simplified_q = $this->simplify($q);
		foreach ($cities as $city) {
			$simplified_city = $this->simplify($city);
			if (strpos($simplified_city, $simplified_q) !== false) {
				$results[] = $city;
			}
			if (count($results) > static::$max_results) {
				break;
			}
		}
		return $results;
	}

	private function search_township($q) {
		$townships = iFoundSearch::new_hookless()->lookups_to_criteria()->township->values;
		$results = [];
		$simplified_q = $this->simplify($q);
		foreach ($townships as $township) {
			$simplified_township = $this->simplify($township);
			if (strpos($simplified_township, $simplified_q) !== false) {
				$results[] = $township;
			}
			if (count($results) > static::$max_results) {
				break;
			}
		}
		return $results;
	}

	private function search_county($q) {
		$counties = iFoundSearch::new_hookless()->lookups()->county->values;
		$results = [];
		$simplified_q = $this->simplify($q);
		foreach ($counties as $county) {
			$simplified_city = $this->simplify($county);
			if (strpos($simplified_city, $simplified_q) !== false) {
				$results[] = $county;
			}
			if (count($results) > static::$max_results) {
				break;
			}
		}
		return $results;
	}

	private function search_school($q) {
		$lookups = iFoundSearch::new_hookless()->lookups();
		// Some MLSs don't have schools, like PAAR.
		if (!isset($lookups->high_school)) {
			return [];
		}
		$schools_by_type = [
			'elementary_school' => $lookups->elementary_school->values,
			'middle_school' => $lookups->middle_school->values,
			'high_school' => $lookups->high_school->values,
		];
		$results = [];
		$simplified_q = $this->simplify($q);
		foreach ($schools_by_type as $type => $schools) {
			foreach ($schools as $school) {
				$simplified_school = $this->simplify($school);
				if (strpos($simplified_school, $simplified_q) !== false) {
					$results[] = [
						'type'  => $type,
						'value' => $school,
					];
				}
				if (count($results) >= static::$max_results) {
					break;
				}
			}
		}
		return $results;
	}

	// Note: I'm initially writing this for brightmls, which has one school district field, whereas I think other MLSs
	// have a field for each type (elementary, middle, high). (Fun fact: ARMLS has elementary and high, but not middle.)
	// So I'm going to hard-code the type for now into a value called 'all', but in the future, if we run this for other
	// MLSs, we'd want to break it out properly.
	private function search_school_district($q) {
		global $mls_associations;
		$school_districts = $mls_associations->school_district;
		$results = [];
		$simplified_q = $this->simplify($q);
		foreach ($school_districts as $school_district) {
			$simplified_school_district = $this->simplify($school_district);
			if (strpos($simplified_school_district, $simplified_q) !== false) {
				$results[] = [
					'type'  => 'all_school_districts',
					'value' => $school_district,
				];
			}
			if (count($results) >= static::$max_results) {
				break;
			}
		}
		return $results;
	}

	private function query_idx_multibox($q, $mls_class_code) {
		$results = iFoundIdx::new_hookless()->multibox_request($q, $mls_class_code);
		$result_buckets_that_need_url = ['mls_id', 'address'];
		foreach ($result_buckets_that_need_url as $bucket_name) {
			$results[$bucket_name] = array_map(function($x) {
				return array_merge($x, [
					// FIXME: this does not use the "Property Detail URL" format from the SEO Settings. But the functions
					// that create the slugs expect a full MLS listing, and I don't want to do that work right now in
					// MultiboxSearch.iced. Because the slug doesn't actually matter (only the MLS ID at the end of the URL
					// is used), this seems good enough.
					'url' => apply_filters('ifound_get_detail_url', $x['fullAddress'], $x['mlsId']),
				]);
			}, $results[$bucket_name]);
		}
		return $results;
	}

	private function search_zip($q) {
		if (preg_match('/^\d{5}$/', $q)) {
			return [$q];
		}
		return [];
	}
}
