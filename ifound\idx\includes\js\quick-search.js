/**
 * @summary Quick Search.
 *
 * @since 5.0.0
 */

jQuery( document ).ready( function( $ ) {
	$( '#quick-search-button' ).on( 'click', function() {
		var overlay = $('.overlay');
		if(overlay !== null) $(overlay).val('');
		var uri = '';

		function getCityStateParts(input) {
			let vParts = $(input).val().split(' ');
			if(vParts[vParts.length -1].length == 2) {
				let joined = vParts.join(' ');
				let state = vParts[vParts.length -1];
				return {
					city: joined.substr(0, joined.indexOf(state) -1 ).replace(/,/g, ''),
					state: state,
				};
			}
			else return null;
		}

		if(quick_search.isMultipleStateMls) {
			var inputs = $.merge($('.quick-search .number-input-city'), $('.quick-search .number-input-school_district'));
			for(let input of inputs) {
				if($(input).attr('name') === 'city' || $(input).attr('name') === 'school_district') {
					let clone = $(input).clone();
					clone.attr('name', 'state');
					clone.css('display', 'none');
					let parts = getCityStateParts(input);
					if(parts !== null) {
						$(clone).val(parts.state);
						$(input).val(parts.city);
					}
					$(input).closest('.number-wrap').append(clone);
				}
			}
		}

		var items = $( '#quick-search-form' ).serializeArray();
		$.each( items, function() {
			if( this.value.length ) {
				uri += this.name + '-' + encodeURI( this.value ) + '/';
			}
		});
		window.location = quick_search.url + uri;
	});
	var overlay = $('.number-input-city:first').clone();
	overlay.css({
		'position': 'absolute',
		'top': 0,
		'left': 0,
		'color': '#777',
		'width': '100%',
	}).addClass('overlay').attr({
		'placeholder': '',
		'id': '',
	}).prop('disalbed', true);

	$('.number-input-city').css({
		'position': 'relative',
		'z-index': '1',
		'background': 'transparent',
		'width': '100%',
		}).parent().append(overlay);

	$(document).on('input', '.number-input-city', function() {
		autofill($(this), cities);
	});

	$(document).on('input', '.number-input-school_district', function() {
		autofill($(this), sDistricts);
	});

	$(document).on('input', '.number-input-zip', function() {
		autofill($(this), zips);
	});

	function autofill(inputField, targetList) {
		var nameParts = $(inputField).val().split(' ');
		for(let i = 0; i < nameParts.length; i++) {
			nameParts[i] = nameParts[i].toLowerCase();
			let isStatePart = quick_search.isMultipleStateMls && nameParts.length > 1 && i === nameParts.length -1
				&& nameParts[i-1].includes(',');
			if(isStatePart) {
				nameParts[i] = nameParts[i].toUpperCase();
			} else {
				nameParts[i] = nameParts[i].replace(/[a-z]/, nameParts[i].charAt(0).toUpperCase());
			}
			$(inputField).val(nameParts.join(' '));
		}

		var currVal = $(inputField).val();
		var input = $(inputField);
		var overlay = $(inputField).next('.overlay');
		if(currVal === '') {
			$(overlay).val('');
			$('.autofill-dropdown').remove();
			return;
		}

		for(let i = 0; i < targetList.length; i++) {
			if(targetList[i].startsWith(currVal)) {
				$(overlay).val(currVal + targetList[i].replace(currVal, ''));
				var matchArray = [targetList[i]];
				while( i !== targetList.length -1 && targetList[++i].startsWith(currVal) && matchArray.length < 11) {
					matchArray.push(targetList[i]);
				}
				$.autofillDropdown(inputField, matchArray);
(inputField).parent().on('keydown',function(e) {
					if(e.which == 39 || e.which == 9) {
						$(input).val($(overlay).val());
					}
				});
				return;
			}
			if(i === targetList.length -1) overlay.val('');
		}
	}

	function autofillDropdown(mainInput, cities) {
		$('.autofill-dropdown').remove();
		var dropdownBeg = '<div class="autofill-dropdown">';
		var dropdownEnd = '</div>';

		var inputs = '';
		for(let city of cities) {
			inputs += '<div class="autofill-hint" value="' + city + '">' + city + '</div>';
		}

		$(mainInput).parent().append(dropdownBeg + inputs + dropdownEnd);
		$('.autofill-hint').css({
			'cursor': 'pointer',
			'width': '100%',
		});
		$('.autofill-dropdown').css({
			'position': 'absolute',
			'top': $(mainInput).height() + 3 + 'px',
			'padding': '10px 20px',
			'background': '#fff',
			'z-index': 10001,
			'font-size': $(mainInput).css('font-size'),
		});
		$(mainInput).parent().find('.autofill-hint').on('click', function() {
			$(mainInput).val($(this).attr('value'));
			$(mainInput).next('.overlay').val($(this).attr('value'));
			$(this).parent().slideUp('slow').remove();
		});
	}
	$.autofillDropdown = autofillDropdown;
});
