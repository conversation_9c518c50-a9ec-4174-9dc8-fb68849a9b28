USE pfndidx_azdb;
CREATE TABLE mls_associations(
    id int not null auto_increment, 
    name varchar(255) not null,
    display_name varchar(255) not null,
    city text not null,
    contingent text not null,
    list_status text not null,
    mls_class text not null,
    prop_type text not null,
    geo text not null,
    PRIMARY KEY (id)
);
INSERT INTO mls_associations(name,display_name,city,contingent,list_status,mls_class,prop_type,geo)
VALUES ('armls', 'ARMLS',
'{
"Aguila": "city",
"Ahwatukee": "city",
"Ajo": "city",
"Anthem": "city",
"Apache Junction": "city",
"Aripine": "city",
"Arizona City": "city",
"Arlington": "city",
"Ash Fork": "city",
"Avondale": "city",
"Bagdad": "city",
"Benson": "city",
"Bisbee": "city",
"Black Canyon City": "city",
"Buckeye": "city",
"Camp Creek": "city",
"Camp Verde": "city",
"Carefree": "city",
"Casa Grande": "city",
"Cave Creek": "city",
"Chandler": "city",
"Chino Valley": "city",
"Chloride": "city",
"Christopher Creek": "city",
"Cibola": "city",
"Clarkdale": "city",
"Clay Springs": "city",
"Claypool": "city",
"Clifton": "city",
"Concho": "city",
"Congress": "city",
"Coolidge": "city",
"Cornville": "city",
"Cottonwood": "city",
"Crown King": "city",
"Desert Hills": "city",
"Dewey Humboldt": "city",
"Eagar": "city",
"El Mirage": "city",
"Elgin": "city",
"Eloy": "city",
"Flagstaff": "city",
"Florence": "city",
"Forest Lakes": "city",
"Fort Mcdowell": "city",
"Fountain Hills": "city",
"Gila Bend": "city",
"Gilbert": "city",
"Gisela": "city",
"Glendale": "city",
"Globe": "city",
"Gold Canyon": "city",
"Golden Valley": "city",
"Goodyear": "city",
"Greer": "city",
"Guadalupe": "city",
"Hackberry": "city",
"Happy Jack": "city",
"Hayden": "city",
"Heber": "city",
"Holbrook": "city",
"Huachuca City": "city",
"Humboldt": "city",
"Kearny": "city",
"Kingman": "city",
"Kirkland": "city",
"Lakeside": "city",
"Laveen": "city",
"Linden": "city",
"Litchfield Park": "city",
"Marana": "city",
"Maricopa": "city",
"Mayer": "city",
"Mesa": "city",
"Miami": "city",
"Mormon Lake": "city",
"Morristown": "city",
"Munds Park": "city",
"Munds Park/pinew": "city",
"New River": "city",
"Nutrioso": "city",
"Overgaard": "city",
"Paradise Valley": "city",
"Parks": "city",
"Paulden": "city",
"Payson": "city",
"Peeples Valley": "city",
"Peoria": "city",
"Phoenix": "city",
"Pima": "city",
"Pine": "city",
"Pinedale": "city",
"Pinetop": "city",
"Pinetop lakeside": "city",
"Prescott": "city",
"Prescott Valley": "city",
"Quartzsite": "city",
"Queen Creek": "city",
"Queen Valley": "city",
"Red Rock": "city",
"Rimrock": "city",
"Rio Verde": "city",
"Riverside": "city",
"Safford": "city",
"Saint David": "city",
"Salome": "city",
"San Manuel": "city",
"San Simon": "city",
"San Tan Valley": "city",
"Sanders": "city",
"Scottsdale": "city",
"Sedona": "city",
"Seligman": "city",
"Sentinel": "city",
"Show Low": "city",
"Snowflake": "city",
"Somerton": "city",
"Stanfield": "city",
"Star Valley": "city",
"Strawberry": "city",
"Sun City": "city",
"Sun City West": "city",
"Sun Lakes": "city",
"Sunflower": "city",
"Superior": "city",
"Surprise": "city",
"Taylor": "city",
"Tempe": "city",
"Tolleson": "city",
"Tombstone": "city",
"Tonopah": "city",
"Tonto Basin": "city",
"Tonto Village": "city",
"Truxton": "city",
"Tucson": "city",
"Vernon": "city",
"Vicksburg": "city",
"Waddell": "city",
"Wellton": "city",
"White Hills": "city",
"White Mountain Lake": "city",
"Wickenburg": "city",
"Wikieup": "city",
"Wilhoit": "city",
"Willcox": "city",
"Williams": "city",
"Winkelman": "city",
"Winslow": "city",
"Wittmann": "city",
"Yarnell": "city",
"Young": "city",
"Youngtown": "city",
"Yuma": "city",
"Yuma Prooving Guard": "city"
}',
'{
"UCB (Under Contract-Backups)": "UCB",
"CCBS (Contract Contingent on Buyer Sale)": "CCBS"
}',
'{
"Active": "Active",
"Closed": "Closed",
"Pending": "Pending"
}',
'{
"Residential": "res",
"Rentals": "rentals",
"Lots and Land": "land"
}',
'{
"Homes": "Single Family",
"Condos": "Apartment",
"Patio Homes": "Patio Home",
"Lofts": "Loft",
"Apartments": "Apartment",
"Townhouses": "Townhouse",
"Mobile Homes": "Mobile Housing"
}',
'{
"center_lat": 33.55097, 
"center_lng": -111.97114,
"zoom": 10
}');
