<?php

/* vim: set expandtab tabstop=4 shiftwidth=4 softtabstop=4: */

/**
 * This is a simple class designed to print out alerts for a given page.
 *
 * When a process script is run and messages are placed in a session or pushed to the resulting page, 
 * This script helps to process and print them out.
 *
 * LICENSE: None.
 *
 * @category   Script Class
 * @package    Standard/none
 * <AUTHOR> <<EMAIL>>
 * @copyright  1997-2011 Pinion Media, llc
 * @license    http://www.pinionmedia.com/license
 * @version    v1.1
 */

/**
 * This is a "Docblock Comment," also known as a "docblock."  The class'
 * docblock, below, contains a complete description of how to write these.
 */


class Alerts {
	
	
/**
 * Accepts an array or string and prints the appropriate markup
 * to display the alert data.
 *
 * Example code:
 * <code>
 * require_once 'class-alerts.php';
 *
 * $a = new Alerts();
 * echo $a->getAlerts($array) . "\n";
 * </code>
 *
 * @param array/string $arg1 the string or array 
 							 to print out
 *
 * @return string the string of markup
 *
 * @access public
 * @see Net_Sample::$foo, Net_Other::someMethod()
 * @since Method available since Release 1.0
 */
public function getAlerts($msg=NULL) {
	
	$msg_strg = '';
	
	if(is_array($msg)) {
		$msg_strg .= '<p class="alert_msg">';
		$msg_strg .= '<ul class="alert_msg">';
		foreach ($msg as $id => $msgs) {  
			$msg_strg .= '<li>'.$msgs.'</li>';
		}
		$msg_strg .= '</ul></p>';
	}else
	if($msg) {
		$msg_strg = $msg;
	}
	
	return $msg_strg;
	
} //  End getAlerts function





	
	
/**
 * Accepts an array or string and prints the appropriate markup
 * to display the alert data.
 *
 * Example code:
 * <code>
 * require_once 'class-alerts.php';
 *
 * $a = new Alerts();
 * echo $a->getErrors($array) . "\n";
 * </code>
 *
 * @param array/string $arg1 the string or array 
 							 to print out
 *
 * @return string the string of markup
 *
 * @access public
 * @see Net_Sample::$foo, Net_Other::someMethod()
 * @since Method available since Release 1.0
 */
public function getErrors($error=NULL){
	
	$error_strg = '';
	
	if(is_array($error['msg'])){
		$error_strg .= '<p class="alert_errors">';
		$error_strg .= 'We had trouble processing your form.<br />To make it easier to see the problem area(s) they are highlighted in red.';
		$error_strg .= '<ul class="alert_errors">';
		foreach ($error['msg'] as $id => $msgs) {  
			$error_strg .= '<li>'.$msgs.'</li>';
		}
		$error_strg .= '</ul></p>';
	}else
	if($error) {
		$error_strg = $error;
	}
	
	return $error_strg;
	
} //  End getErrors function





	
	
} // End alerts class





?>