import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
	build: {
		lib: {
			entry: resolve(__dirname, 'src/index.ts'),
			name: 'HistoryTracker',
			fileName: 'index',
			formats: ['es'],
		},
		rollupOptions: {
			external: ['mysql2', '@sendgrid/mail', 'twilio', 'fs', 'path'],
		},
		target: 'node18',
	},
	test: {
		globals: true,
		environment: 'node',
		env: {
			NODE_ENV: 'test',
		},
	},
})
