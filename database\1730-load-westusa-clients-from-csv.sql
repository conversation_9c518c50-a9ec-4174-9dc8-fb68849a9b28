USE pfndidx_azdb;

-- Create temporary table for CSV data
DELETE FROM access WHERE access_account_id LIKE 'wusa_%';

-- Load data from CSV into temp table
LOAD DATA LOCAL INFILE 'wusa-to-load-unique.csv' INTO TABLE access
FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'
(@LastName, @FirstName, @OfficeID, access_phone, @Email, @MemberID, access_domain)
SET
  access_fullname = CONCAT(SUBSTR(@FirstName, 1, 1), LOWER(SUBSTR(@FirstName, 2)), ' ', SUBSTR(@LastName, 1, 1), LOWER(SUBSTR(@LastName, 2))),
  access_company = 'West USA Realty',
  mls = 'armls',
  access_office_id = LOWER(@OfficeID),
  access_emailaddress = @Email,
  access_account_id = CONCAT('wusa_', SUBSTR(@Email, 1, LOCATE('@', @Email) - 1)),
  access_member_id = LCASE(@MemberID),
  access_apikey = LOWER(CONCAT(LPAD(CONV(FLOOR(RAND() * POW(36, 8)), 10, 36), 8, 0), LPAD(CONV(FLOOR(RAND() * POW(36, 8)), 10, 36), 8, 0))),
  feature_gui = 0,
  created_at = NOW(),
  updated_at = NOW();

-- Populate `access_meta` table
INSERT INTO access_meta
	(
		access_id,
		meta_prop_url,
		meta_prop_title,
		meta_prop_h1,
		meta_prop_keywords,
		meta_prop_description,
		meta_result_title,
		meta_result_h1,
		meta_result_keywords,
		meta_result_description,
		meta_result_prop_h2,
		meta_result_prop_content,
		meta_cat_title,
		meta_cat_h1,
		meta_cat_keywords,
		meta_cat_description,
		meta_geoapi,
		meta_links_seo,
		created_at,
		updated_at
	)
SELECT
	a.access_id,
	meta_prop_url,
	meta_prop_title,
	meta_prop_h1,
	meta_prop_keywords,
	meta_prop_description,
	meta_result_title,
	meta_result_h1,
	meta_result_keywords,
	meta_result_description,
	meta_result_prop_h2,
	meta_result_prop_content,
	meta_cat_title,
	meta_cat_h1,
	meta_cat_keywords,
	meta_cat_description,
	meta_geoapi,
	meta_links_seo,
	NOW(),
	NOW()
FROM
	access a,
	access_meta am
WHERE
	a.access_id NOT IN (
		SELECT
			access_id
		FROM
			access_meta
		)
	AND am.access_id=998;
