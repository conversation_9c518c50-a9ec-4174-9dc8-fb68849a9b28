<?

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundCrmLogin extends iFoundCrm {
	// We were originally doing this in the upgrade code, and this key made sense. Still, I can't think of a better
	// one.
	private static $v5_15_3_meta_key = 'ifound_v5_15_3-master_data';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('wp_login', [$this, 'wp_login'], 10, 2);
		}
	}

	// When a user logs in, we might need to set up some data for them, e.g. email templates, etc. If they are an admin,
	// we might create shared data. This type of thing might be done instead in our update code, but it takes too long
	// when there are many users, e.g. on westusaagents.com where there are 850 users. The consequence here is that the
	// user won't get the examples if they are already logged in; they'll need to log out and back in.
	public function wp_login($user_login, $user) {
		// We can't use $this->crm_id() here (get_current_user_id() is 0), so we do our own calculations using $user.
		$crm_id = $user->ID;
		$is_admin_or_team_member = false;
		if ($this->util()->user_has_admin_or_super_role($user->ID)) {
			// Use the shared (string) ID, which is the empty string.
			$crm_id = '';
			$is_admin_or_team_member = true;
		} else if (iFoundTeams::new_hookless()->user_has_team_member_role(['id' => $user->ID])) {
			$is_admin_or_team_member = true;
		}

		if (!$is_admin_or_team_member) {
			return;
		}

		$crm_login_with_crm_id_key = 'ifound_crm_login' . $crm_id;
		$login_data = get_option($crm_login_with_crm_id_key, []);
		$has_drip_campaigns_feature = function() {
			return apply_filters('ifound_has_feature', 'drip-campaigns');
		};
		$made_change = false;

		// We had a bug previously where we acted like we had created the example templates, but if the account didn't
		// have the drip-campaigns feature, we really hadn't. So let's undo that, meaning, if they don't have the
		// drip-campaigns feature, and we have previously recorded that we created the example templates, but there
		// aren't any, then we un-record it.
		if (!apply_filters('ifound_has_feature', 'drip-campaigns') && $login_data['example_drip_template_created']) {
			$email_templates = get_posts([
				'author' => $user_id,
				'numberposts' => 1,
				'post_type' => iFoundEmail::$the_post_type,
				'tax_query' => [
					[
						'taxonomy' => iFoundEmail::$the_taxonomy,
						'field'    => 'slug',
						'terms'    => ['drip-email'],
					],
				],
				'meta_query' => [
					[
						'key' => static::$v5_15_3_meta_key,
						'value' => 'true',
					],
				],
			]);
			if (!count($email_templates)) {
				unset($login_data['example_drip_template_created']);
				unset($login_data['example2_drip_template_created']);
				unset($login_data['example3_drip_template_created']);
				$made_change = true;
			}
		}

		$tasks = [
			'example_drip_template_created' => [
				'should_run' => $has_drip_campaigns_feature,
				'callback' => [$this, 'create_example_drip_template'],
			],
			'example2_drip_template_created' => [
				'should_run' => $has_drip_campaigns_feature,
				'callback' => [$this, 'create_example_drip_template2'],
			],
			'example3_drip_template_created' => [
				'should_run' => $has_drip_campaigns_feature,
				'callback' => [$this, 'create_example_drip_template3'],
			],
			'blank_contact_titles_fixed' => [
				'should_run' => '__return_true',
				'callback' => [$this, 'fix_blank_contact_titles'],
			],
		];
		$user_id = $crm_id ?: iFoundAdmin::$shared_owner_ID;
		foreach ($tasks as $key => $value) {
			if (!$login_data[$key] && $value['should_run']()) {
				call_user_func($value['callback'], $user_id);
				$login_data[$key] = true;
				$made_change = true;
			}
		}
		if ($made_change) {
			update_option($crm_login_with_crm_id_key, $login_data);
		}
	}

	// Create example drip template (and corresponding email templates)
	private function create_example_drip_template($user_id) {
		$email = iFoundEmail::new_hookless();
		$email->post_type();
		$email->taxonomy();
		iFoundDripTemplate::new_hookless()->post_type();


		// Email templates content
		$email_templates = [];
		$month_names = [
			'January',
			'February',
			'March',
			'April',
			'May',
			'June',
			'July',
			'August',
			'September',
			'October',
			'November',
			'December',
		];
		for ($i = 1; $i <= 12; $i++) {
			$new_post_content = $this->util()->get_html('email/example1/', "tip-of-the-month-{$i}");
			$month_name = $month_names[$i - 1];
			$email_templates[] = [
				'title'   => "{$month_name} Homeowner Tip of the Month",
				'subject' => "{$month_name} Home Reminder from {AgentName}",
				'content' => $new_post_content,
			];
		}

		// Create email templates
		$email_template_ids = [];
		foreach ($email_templates as $email_template) {
			$email_template_data = [
				'post_title'    => $email_template['title'],
				'post_content'  => $email_template['content'],
				'post_status'   => 'publish',
				'post_type'		=> iFoundEmail::$the_post_type,
				'post_author' 	=> $user_id,
			];
			$email_template_id = wp_insert_post($email_template_data);
			$email_template_ids[] = $email_template_id;
			wp_set_object_terms($email_template_id, 'drip-email', iFoundEmail::$the_taxonomy, true);
			add_post_meta($email_template_id, 'subject', $email_template['subject']);
			add_post_meta($email_template_id, static::$v5_15_3_meta_key, 'true');
		}

		// Create drip template
		$example_drip_template_name = 'Homeowner Tip of the Month - Month Specific';
		$steps = [];
		foreach ($email_template_ids as $index => $email_template_id) {
			$month_num = $index + 1;
			$steps[] = [
				'customer_template_id' => $email_template_id,
				'reminder_template_id' => null,
				'interval'             => $month_num === 1 ? 'Campaign Start' : '1 Month',
			];
		}
		iFoundDripTemplate::new_hookless()->create_template($example_drip_template_name, $user_id, $steps, [
			static::$v5_15_3_meta_key => 'true',
		]);
	}

	// Create another set of example email templates and drip template
	private function create_example_drip_template2($user_id) {
		$email = iFoundEmail::new_hookless();
		$email->post_type();
		$email->taxonomy();
		iFoundDripTemplate::new_hookless()->post_type();

		// Email templates content
		$email_templates = [];
		for ($i = 1; $i <= 12; $i++) {
			$new_post_content = $this->util()->get_html('email/example2/', "homeowner-tip-{$i}");
			$email_templates[] = [
				'title'   => "Homeowner Tip {$i} of 12",
				'subject' => "Home Reminder from {AgentName}",
				'content' => $new_post_content,
			];
		}

		// Create email templates
		$email_template_ids = [];
		foreach ($email_templates as $email_template) {
			$email_template_data = [
				'post_title'    => $email_template['title'],
				'post_content'  => $email_template['content'],
				'post_status'   => 'publish',
				'post_type'		=> iFoundEmail::$the_post_type,
				'post_author' 	=> $user_id,
			];
			$email_template_id = wp_insert_post($email_template_data);
			$email_template_ids[] = $email_template_id;
			wp_set_object_terms($email_template_id, 'drip-email', iFoundEmail::$the_taxonomy, true);
			add_post_meta($email_template_id, 'subject', $email_template['subject']);
			add_post_meta($email_template_id, static::$v5_15_3_meta_key, 'true');
		}

		// Create drip template
		$example_drip_template_name = 'Homeowner Tips - non time specific';
		$steps = [];
		foreach ($email_template_ids as $index => $email_template_id) {
			$month_num = $index + 1;
			$steps[] = [
				'customer_template_id' => $email_template_id,
				'reminder_template_id' => null,
				'interval'             => $month_num === 1 ? 'Campaign Start' : '1 Month',
			];
		}
		iFoundDripTemplate::new_hookless()->create_template($example_drip_template_name, $user_id, $steps, [
			static::$v5_15_3_meta_key => 'true',
		]);
	}

	// Create a third example drip template
	private function create_example_drip_template3($user_id) {
		$email = iFoundEmail::new_hookless();
		$email->post_type();
		$email->taxonomy();
		iFoundDripTemplate::new_hookless()->post_type();

		// Create reminder text email template
		$reminder_post_content = $this->util()->get_html('email/example3/', 'reminder-text');
		$reminder_text_title = 'Reminder Text';
		$reminder_template_data = [
			'post_title'   => $reminder_text_title,
			'post_content' => $reminder_post_content,
			'post_status'  => 'publish',
			'post_type'    => iFoundEmail::$the_post_type,
			'post_author'  => $user_id,
		];
		$reminder_template_id = wp_insert_post($reminder_template_data);
		wp_set_object_terms($reminder_template_id, 'reminder-text', iFoundEmail::$the_taxonomy, true);
		// Subject doesn't matter for reminder texts, but it's confusing so just make it blank.
		add_post_meta($reminder_template_id, 'subject', '');
		add_post_meta($reminder_template_id, static::$v5_15_3_meta_key, 'true');

		// Create restart text email template
		$restart_post_content = $this->util()->get_html('email/example3/', 'restart-content-drip-campaign');
		$restart_text_title = 'Restart Content Drip Campaign';
		$restart_template_data = [
			'post_title'   => $restart_text_title,
			'post_content' => $restart_post_content,
			'post_status'  => 'publish',
			'post_type'    => iFoundEmail::$the_post_type,
			'post_author'  => $user_id,
		];
		$restart_template_id = wp_insert_post($restart_template_data);
		wp_set_object_terms($restart_template_id, 'reminder-text', iFoundEmail::$the_taxonomy, true);
		// Subject doesn't matter for reminder texts, but it's confusing so just make it blank.
		add_post_meta($restart_template_id, 'subject', '');
		add_post_meta($restart_template_id, static::$v5_15_3_meta_key, 'true');

		// Create drip template
		$example_drip_template_name = 'Reminder Texts';
		$steps = [];
		for ($i = 0; $i < 5; $i++) {
			$steps[] = [
				'customer_template_id' => null,
				'reminder_template_id' => $reminder_template_id,
				'interval'             => '3 Months',
			];
		}
		$steps[] = [
			'customer_template_id' => null,
			'reminder_template_id' => $restart_template_id,
			'interval'             => '1 Day',
		];
		iFoundDripTemplate::new_hookless()->create_template($example_drip_template_name, $user_id, $steps, [
			static::$v5_15_3_meta_key => 'true',
		]);
	}

	public function fix_blank_contact_titles($user_id) {
		$contacts = iFoundJointContact::new_hookless()->get_all_contacts($user_id);
		foreach ($contacts as $contact) {
			if ($contact->post_title === '') {
				$fname = get_post_meta($contact->ID, 'fname', true);
				$lname = get_post_meta($contact->ID, 'lname', true);
				$contact->post_title = $fname . ' ' . $lname;
				wp_update_post($contact);
			}
		}
	}
}