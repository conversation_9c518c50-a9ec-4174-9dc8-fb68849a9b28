jQuery( document ).ready( function( $ ) {
    $( '.save-campaign' ).on( 'click', function( e ) {
        if( ! validate() ) return;

        var input = new Object();
        input.form = $('#ifound-drip-campaign-form').serialize();
        input.contact_id = $('#contact_id').val();

        jQuery.ajax ( {
            url : ifound_drip_campaign.endpoint,
            type : 'post',
            data : {
                action : 'drip_campaign_ajax',
                input : input,
                drip_campaign_nonce : ifound_drip_campaign.nonce,
            },
            beforeSend: function() {
                $('.while-we-wait').addClass('active');
                $( '#save-campaign-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
                $( '.success-msg' ).fadeOut( 100 );
                $( '.success-sub-msg' ).html('');
                $( '.failed-msg' ).fadeOut( 100 );
            },
            success: function( response ) {
                $( '#save-campaign-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response.class );
                $('.while-we-wait').removeClass('active');
                if( response.drip_campaign_id > 0 ) {
                    $( '.success-sub-msg' ).html('You can view the campaign by <a href="' + response.drip_campaign_link + '">clicking here</a>');
                    $( '.success-msg' ).fadeIn( 100 );
                } else {
                    $( '.failed-msg' ).fadeIn( 100 );
                }
            },
            error: function() {
                $( '.failed-msg' ).fadeIn( 100 );
            },
            dataType:'json'
        });
    });

    function validate(){
        var validate = true;
        /** Clean up all the empty inputs from previos attempt to save. */
        $( '.empty-input' ).removeClass( 'empty-input' );
        if( $( '#contact_id' ).val().length < 1 ) {
            $( '#contact_autocomplete' ).addClass( 'empty-input' ).focus();
            alert( 'You must select a contact to save this campaign.' );
            validate = false;
            return false;
        }
        $( '.ifound-validate' ).each( function(){
            if(
                (!($(this)).val() || $( this ).val().length < 1)
                && ! $(this).prop('disabled')
            ) {
                $( this ).addClass( 'empty-input' ).focus();
                alert( 'Complete REQUIRED fields to save this campaign.' );
                validate = false;
                return false;
            }
        });
        return validate;
    }

    function init() {
        iFoundGlobal.syncStepsWithSelectedDripTemplate();
    }

    init();
});
