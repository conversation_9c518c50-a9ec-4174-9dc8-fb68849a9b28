<?
/**
 * The template for displaying IFound Results
 *
 * @since 1.0
 * @since 2.3.0 Move hooks to appropriate methods.
 * @version 1.1
 */

get_header();
//wp_enqueue_script( 'quick_search_page_js' ); ?>

<div id="primary" class="content-area">

	<main id="main" class="site-main" role="main">

		<article class="results-page">

			<!-- Entry Header -->
			<header class="entry-header">

				<h1 class="entry-title"><?
					do_action( 'ifound_seo_h1' ); ?>
				</h1>

			</header>
			<!-- Entry Header -->

			<div class="entry-content listing-results">

				<!-- Start Results Section -->
				<div class="results-content-main">

					<div class="ifound-wrap">

					<!-- Start Search Bar Section --><?
						if ( is_active_sidebar( 'search-results-before-criteria' ) ) :
							dynamic_sidebar( 'search-results-before-criteria' );
	 					endif;
						?> <div class="ifound-dynamic-form-wrapper"> <?
						do_action( 'ifound_search_bar', $results, $extra );
						?> </div> <?
						if ( is_active_sidebar( 'search-results-after-criteria' ) ) :
							dynamic_sidebar( 'search-results-after-criteria' );
	 					endif;
						?><!-- End Search Bar Section -->

						<!-- Start Map Section --><?
						do_action( 'ifound_before_map', $results );
						do_action( 'ifound_qsearch_page_button' );
						do_action( 'ifound_show_map_button' );
                                                if ( is_active_sidebar( 'search-results-after-map' ) ) :
                                                        dynamic_sidebar( 'search-results-after-map' );
						endif;
						if (!iFoundUtil::new_hookless()->is_advanced_search_page()) :
							do_action( 'ifound_display_map', $results, [] );
						endif;
						do_action( 'ifound_after_map', $results );

						do_action( 'aerial_sphere_search', $results );
						//do_action( 'aerial_sphere_map_html' );
						?><!-- End Map Section -->

						<!-- Start Results Section -->
						<div class="replace-results-here">

							<!-- Start Price Stats Section --><?
							do_action( 'ifound_price_stats', $results );
							?><!-- End Price Stats Section --><?

							do_action( 'ifound_display_results', $results );
							?><!-- End Results Section --><?

							do_action( 'ifound_display_pagination', $results );
							?><!-- End Pagination Section -->

						</div><?

						if ( is_active_sidebar( 'search-results-after-results' ) ) :
							dynamic_sidebar( 'search-results-after-results' );
	 					endif; ?>

					</div>

				</div>

			</div><!-- .entry-content -->

		</article><!-- #post-## -->

	</main><!-- #main -->

</div><!-- #primary --><?

get_footer();

/** Required Hooks **DO NOT REMOVE** */
do_action( 'ifound_registration', $results );
do_action( 'ifound_tracking', $results, 'results_view' );
apply_filters( 'ifound_footer', $results );
