<?

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundAddress {
	use NewHooklessTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
		}
	}

	public function build_address($address_array, $options = []) {
		$options = wp_parse_args($options, [
			'for_geocoding' => false,
		]);
		$a = $address_array;
		if ($options['for_geocoding']) {
			return "{$a['address']} {$a['city']} {$a['state']} {$a['zip']}";
		} else {
			return "{$a['address']} {$a['address2']}, {$a['city']}, {$a['state']}, {$a['zip']}";
		}
	}
}
