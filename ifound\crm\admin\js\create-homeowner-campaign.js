jQuery(function ($) {
	var iFoundCreateHomeownerCampaignConfig = window.ifound_create_homeowner_campaign_js;

	var $radiusCheckbox = $('input[name=include_radius]');
	var $radiusSelect = $('select[name=radius]');
	var $daysBackClosedChecbox = $('input[name=days_back]');
	var $closedListingStatusCheckbox = $('input[name="list_status[]"][value=Closed]');
	var $submit = $('button[type=submit]');
	var $doEmail = $('#do_email');
	var $doSms = $('#do_sms');

	$radiusCheckbox.on('click', function() {
		$radiusSelect.prop('disabled', function(i, v) { return !v; });
	});

	$closedListingStatusCheckbox.on('click', function() {
		$daysBackClosedChecbox.prop('disabled', function(i, v) { return !v; });
	})

	function selectHomeownerCampaignEmailTemplate() {
		var $emailTemplateSelect = $('.edit-email-wrapper .template_id');
		var options = $('.edit-email-wrapper .template_id option');
		var templateElem = Array.prototype.find.call(options, function(x) {
			return x.value === iFoundCreateHomeownerCampaignConfig.homeownerCampaignTemplateId;
		});
		if (!templateElem) {
			return;
		}
		var templateId = $(templateElem).attr('value');
		$emailTemplateSelect.val(templateId);
		$emailTemplateSelect.change();
	}
	// Delay this by a little to make sure the handler is set first.
	setTimeout(selectHomeownerCampaignEmailTemplate, 100);

	var $useRadiusRadioButton = $('input[name="post_status"][value="publish"]');
	var $usePolygonAutomatorRadioButton = $('input[name="post_status"][value="draft"]');
	var $performRadiusSearch = $('input[name="include_radius"]');
	var $subdivision = $('input[name="subdivision"]');
	function toggleWhichSectionIsEnabled(event) {
		var $this = $(this)
		if ($this.val() === 'publish') {
			$performRadiusSearch.removeAttr('disabled');
			$radiusSelect.removeAttr('disabled');
			$subdivision.removeAttr('disabled');
			$submit.text('Send campaigns now')
		} else {
			$performRadiusSearch.attr('disabled', 'disabled');
			$radiusSelect.attr('disabled', 'disabled');
			$subdivision.attr('disabled', 'disabled');
			$submit.text('Submit and set up polygons in next step')
		}
	}
	$useRadiusRadioButton.on('click', toggleWhichSectionIsEnabled);
	$usePolygonAutomatorRadioButton.on('click', toggleWhichSectionIsEnabled);
	$performRadiusSearch.on('click', function() {
		if ($(this).is(':checked')) {
			$radiusSelect.removeAttr('disabled');
		} else {
			$radiusSelect.attr('disabled', 'disabled');
		}
	});

	$submit.on('click', function(event) {
		if ($useRadiusRadioButton.is(':checked')) {
			if (!$performRadiusSearch.is(':checked') && !$subdivision.val()) {
				event.preventDefault();
				alert('You must check "Perform radius search" and/or set a subdivision name');
				return;
			}
		}
		if (!$doEmail.is(':checked') && !$doSms.is(':checked')) {
			event.preventDefault();
			if (iFoundCreateHomeownerCampaignConfig.has_sms_feature) {
				alert('You must check Include email, Include text message(s), or both');
			} else {
				alert('You must check Include email');
			}
			return;
		}
	});

	$doEmail.on( 'change', function(){
		$('.email-validate').toggleClass('ifound-feel-disabled');
		$('.ifound-wp-editor-wrapper').toggleClass('ifound-feel-disabled');
	});
	if (!$doEmail.is(':checked')) {
		$('.email-validate').toggleClass('ifound-feel-disabled');
		$('.ifound-wp-editor-wrapper').toggleClass('ifound-feel-disabled');
	}
	$doSms.on( 'change', function(){
		$('.mphone-validate').toggleClass('ifound-feel-disabled');
	});
	if (!$doSms.is(':checked')) {
		$('.mphone-validate').toggleClass('ifound-feel-disabled');
	}
	$('#sms_template_id').on('change', function(event) {
		var template = iFoundCreateHomeownerCampaignConfig.sms_campaign_templates.find(function(t) {
			return t.id === event.target.value;
		});
		if (template) {
			$('.sms_template_body').text(template.body);
		}
	});
});
