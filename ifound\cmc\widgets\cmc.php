<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

if ( ! class_exists( 'iFoundCmc' ) ) die( 'You do not have access.' );

class iFoundCmcWidget extends WP_Widget {
	
	public function __construct(){

		parent::__construct( 
			'ifound_cmc_form', 
			'iFound CMC Form', 
			array(
			'description' => 'Display a Current Market Comparison Form'
		));
		
	}
	
	public function widget( $args, $instance ){
		
		echo $args['before_widget']; ?>
		
		<div class="cmc-widget">
		
			<div class="wrap">
			
				<div class="cmc-widget-heading">
					<? echo stripslashes( get_option( 'cmc_widget_title' ) ); ?>
				</div><?
		
				echo iFoundCmc::cmc_form(); ?>
				
			</div>
			
		</div><?
		
		echo $args['after_widget'];
		
	}

}
