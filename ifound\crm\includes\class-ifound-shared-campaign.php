<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

// This class exists as a central location to put code that applies to both search campaigns and drip campaigns.
class iFoundSharedCampaign extends iFoundIdx {
	public static $DO_EMAIL_KEY = 'do_email';
	public static $DO_EMAIL_YES = 'yes';
	public static $TO_EMAIL_KEY = 'to_email';
	public static $DO_SMS_KEY = 'do_sms';
	public static $DO_SMS_YES = 'yes';
	public static $TO_SMS_KEY = 'to_sms';
	public static $SMS_TEMPLATE_ID_KEY = 'sms_template_id';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('ifound_remove_email_address_from_campaign', [$this, 'remove_email_address_from_campaign'], 10, 3);
	}

	public function remove_email_address_from_campaign($post_id, $email_address, $contact_id) {
		if ($contact_id !== intval(get_post_meta($post_id, 'contact_id', true))) {
			$this->show_wp_template_page_and_die(403);
			return;
		}
		$to_emails = explode(',', get_post_meta($post_id, iFoundSharedCampaign::$TO_EMAIL_KEY, true));
		$count = count($to_emails);
		$to_emails = array_filter($to_emails, function($to_email) use ($email_address) {
			return $to_email !== $email_address;
		});
		if (count($to_emails) !== $count) {
			if (count($to_emails) === 0) {
				iFoundSharedCampaign::new_hookless()->disable_email($post_id, 'Unsubscribed: ' . $email_address);
				$post = get_post($post_id);
				if ($post->post_type === iFoundDripCampaign::$the_post_type) {
					iFoundDripCampaign::new_hookless()->unschedule_cron_if_needed($post_id);
				}
			} else {
				update_post_meta($post_id, iFoundSharedCampaign::$TO_EMAIL_KEY, implode(',', $to_emails));
			}
		}
	}

	public function remove_phone_number_from_campaign($post_id, $phone_number, $force_disable_sms = false) {
		$to_smss = explode(',', get_post_meta($post_id, iFoundSharedCampaign::$TO_SMS_KEY, true));
		$original_count = count($to_smss);
		$to_smss = array_filter($to_smss, function($to_sms) use ($phone_number) {
			return $to_sms !== $phone_number;
		});
		if (count($to_smss) !== $original_count || $force_disable_sms) {
			$action = 'Contact opted out of text messages';
			$phone_number_with_parens = iFoundSms::new_hookless()->format_phone_number_with_parens($phone_number);
			$msg = "Removed {$phone_number_with_parens} from campaign";
			do_action('ifound_activity_log', $post_id, $action, $msg);
			if (count($to_smss) === 0 || $force_disable_sms) {
				iFoundSharedCampaign::new_hookless()->disable_sms($post_id, 'Unsubscribed: ' . $phone_number_with_parens);
				// Drip campaigns don't allow text messages yet. But just keeping this in mind.
				// $post = get_post($post_id);
				// if ($post->post_type === iFoundDripCampaign::$the_post_type) {
				// 	iFoundDripCampaign::new_hookless()->unschedule_cron_if_needed($post_id);
				// }
			} else {
				update_post_meta($post_id, iFoundSharedCampaign::$TO_SMS_KEY, implode(',', $to_smss));
			}
		}
	}

	public function contact_info_column( $column, $post_id ) {
		if ($column == 'contact') {
			$contact_id = get_post_meta( $post_id, 'contact_id', true );
			if( get_post_status( $post_id ) ) {
				$href = $this->util()->build_post_href($contact_id, 'edit');
				$full_name = get_post_meta($contact_id, 'fname', true) . ' ' . get_post_meta($contact_id, 'lname', true);
				$link = "<a href=\"{$href}\">{$full_name}</a>";
				$label = $link;
			} else {
				$label = 'Contact deleted';
			}
			echo $label;
		}
	}

	public function disable_all_active_campaigns_for_contact_id($contact_id) {
		$activity_log_message = 'Disabled campaign because contact was assigned to another user';

		$search_campaign_ids = iFoundSaveThis::new_hookless()->get_active_campaign_ids_for_contact_id($contact_id);
		foreach ($search_campaign_ids as $campaign_id) {
			do_action('ifound_update_campaign_status', $campaign_id, 'inactive', $activity_log_message);
		}

		$drip_campaign_ids = iFoundDripCampaign::new_hookless()->get_active_campaign_ids_for_contact_id($contact_id);
		foreach ($drip_campaign_ids as $campaign_id) {
			do_action('ifound_update_campaign_status', $campaign_id, 'inactive', $activity_log_message);
		}
	}

	public function is_do_email_yes($campaign_id) {
		$do_email = get_post_meta($campaign_id, iFoundSharedCampaign::$DO_EMAIL_KEY, true);
		return $do_email === iFoundSharedCampaign::$DO_EMAIL_YES;
	}

	public function is_do_sms_yes($campaign_id) {
		$do_email = get_post_meta($campaign_id, iFoundSharedCampaign::$DO_SMS_KEY, true);
		return $do_email === iFoundSharedCampaign::$DO_SMS_YES;
	}

	public function disable_email($campaign_id, $activity_log_message) {
		update_post_meta($campaign_id, static::$DO_EMAIL_KEY, false);
		$this->disable_campaign_if_no_communication_channels($campaign_id, $activity_log_message);
	}

	public function disable_sms($campaign_id, $activity_log_message) {
		update_post_meta($campaign_id, static::$DO_SMS_KEY, false);
		$this->disable_campaign_if_no_communication_channels($campaign_id, $activity_log_message);
	}

	private function disable_campaign_if_no_communication_channels($campaign_id, $activity_log_message) {
		if (
			!$this->is_do_email_yes($campaign_id)
			&& !$this->is_do_sms_yes($campaign_id)
		) {
			$extra_message = ' - No communication channels left, so campaign was disabled';
			$full_message = $activity_log_message . $extra_message;
			do_action('ifound_update_campaign_status', $campaign_id, 'inactive', $full_message);
		}
	}

	// $type is 'email' or 'sms'
	// $contact_uri means an email address or phone number
	public function add_activity_log_click($campaign_id, $type, $contact_uri) {
		$type_str = '';
		if ($type === 'email') {
			$type_str = ' Email';
		} else if ($type === 'sms') {
			$type_str = ' SMS';
		}
		do_action('ifound_activity_log', $campaign_id, "Clicked{$type_str} Link", $contact_uri);

		$contact_id = get_post_meta($campaign_id, 'contact_id', true);
		$campaign_title = get_the_title($campaign_id);
		iFoundJointContact::new_hookless()->add_activity_log_click($contact_id, $type, $campaign_title);
	}
}
