<?php

require_once(__DIR__ . '/EmailParticipationReporter.php');

class ArmlsParticipationReporter extends EmailParticipationReporter {
	protected static $internal_mls_display_name = 'ARMLS';
	protected static $to_email = '<EMAIL>';
	protected static $email_subject = 'ARMLS Subscriber Report from iFoundAgent';
	protected static $email_message = <<<EOT
Please see the attached for the ARMLS Subscriber Report from iFoundAgent.

This report was autogenerated.
EOT;

	protected function generate_participation_report() {
		$conn = $this->client->make_conn_to_railsadmin();
		$sql = "select MEMBER_17 as agent_id, OFFICESHORT as office_id from armls_agent_ActiveAgent";
		$statement = $conn->prepare($sql);
		$statement->execute();
		$results = $statement->fetchAll();
		$access_office_id_by_agent_id = [];
		foreach ($results as $result) {
			$agent_id = strtolower($result['agent_id']);
			$access_office_id_by_agent_id[$agent_id] = $result['office_id'];
		}

		$posts = $this->get_active_clients();
		$items = array_map(function($post) use ($access_office_id_by_agent_id) {
			$client_info = get_post_meta($post->ID, 'client_info', true);
			$website = get_post_meta($post->ID, 'website', true);
			$agent_id = strtolower($client_info['agent_id']);
			return [
				'First Name'     => $client_info['fname'],
				'Last Name'      => $client_info['lname'],
				'Email'          => $client_info['email'],
				'Agent ShortID'  => $agent_id,
				'Office ShortID' => $access_office_id_by_agent_id[$agent_id],
				'Website'        => $website['domain'],
			];
		}, $posts);
		// Sort the items to make it easier to compare to the wp-admin where we can filter by mls and visually prove
		// that we have the right clients in the list.
		usort($items, function($a, $b) {
			return $a['First Name'] <=> $b['First Name'];
		});

		return $items;
	}
}
