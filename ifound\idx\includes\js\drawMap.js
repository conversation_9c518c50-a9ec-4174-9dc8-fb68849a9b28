/*
 * Drawing the map by stealing it from antother section
 */

jQuery(function($) {
	// If the map is under the button, just re-draw it
        $( '.advanced-button' ).click(function() {
                if( !$( '.button-wrapper' ).after().hasClass( '.map-and-palette-wrapper' ) ) {
                        putMapBack();
                }
        });
	
        $('#draw-polygon-button').click(drawMap);

	function drawMap() {
		$(window).trigger('ifound:toggle-map');
		// Toggle the button wording for "Show Map" since the map isn't there anymore
		let mapButton = $( '.map-button' );
		if( $( mapButton ).text().includes( 'Hide' ) ) {
			changeMapButton( 'Show' );
		}
				
                let map = $( '.map-and-palette-wrapper' );
		if( $( '#draw-map-body > div' ).hasClass( 'map-and-palette-wrapper' ) ) {
			$( map ).css( 'display', 'block' );
		} else {
			$( map ).remove();
			$( map ).css( 'display', 'block' );
			$( '#draw-polygon-button' ).next().append( $( map ) );
		}
        }

	/*
 	 * Putting map back below the buttons
	 */
        function putMapBack() {
                let map = $( '.map-and-palette-wrapper' );
		$( map ).remove().css( 'display', 'none' ).insertAfter( '.button-wrapper' );
        }

	/*
	 * Changing the wording for the 'Show/Hide' Map button
	 * @param string
	 */	
	function changeMapButton( actionString ) {
		let mapButton = $( '.map-button' );
		let iconOuter = $( '> i', mapButton ).prop( 'outerHTML' );
		
		$( mapButton ).html( iconOuter + ' ' + actionString + ' Map' );
	}
});
