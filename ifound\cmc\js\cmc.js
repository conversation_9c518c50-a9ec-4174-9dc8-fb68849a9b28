/**
 * @see https://developers.google.com/maps/documentation/javascript/examples/places-autocomplete-addressform
 * @see https://developers.google.com/maps/documentation/javascript/places-autocomplete
 *
 */
jQuery( document ).ready( function( $ ) {

	window.iFoundGlobal.loadGoogleMaps(['places']).then(function() {
		initAutocomplete();
	});

	function initAutocomplete() {
		var defaultBounds = new google.maps.LatLngBounds(
			new google.maps.LatLng(cmc_data.settings.south, cmc_data.settings.west),
  			new google.maps.LatLng(cmc_data.settings.north, cmc_data.settings.east)
		);

		var input = document.getElementById('cmc-autocomplete');
		var options = {
			componentRestrictions: {country: 'us'},
  			bounds: defaultBounds,
  			types: ['geocode']
		};

		autocomplete = new google.maps.places.Autocomplete( input, options );

		//only return the geometry fields
		autocomplete.setFields(['geometry']);

		// When the user selects an address from the dropdown, populate the coordinate inputs in Gravity Form
        autocomplete.addListener('place_changed', populate);

	}

	var form_ID = cmc_data.form_id;
	var form2_ID = cmc_data.form2_id;

	$( '#cmc-button' ).on( 'click', function() {

		if( ! $( '#cmc-autocomplete' ).val().length ) {
			$( '#cmc-autocomplete' ).addClass( 'required' );
			return;
		}

		$( '#cmc-autocomplete' ).removeClass( 'required' );
		$( '#input_' + form_ID + '_6' ).val( $( '#cmc-autocomplete' ).val() );
		$( '#gform_' + form2_ID ).submit();
		initPopUp();
		initPopMap();
	});

	$( '.pop-close' ).on( 'click', function() {
		$( '.pop-form, .pop-backdrop, .pop-close' ).removeClass( 'active' );
	});

 	function populate() {

		var place = autocomplete.getPlace();

		lat = place.geometry.location.lat(),
    	lng = place.geometry.location.lng();

		$( '#input_' + form_ID + '_24' ).val( lat );
		$( '#input_' + form_ID + '_25' ).val( lng );
		$( '#input_' + form_ID + '_6' ).val( $( '#cmc-autocomplete' ).val() );
		$( '#input_' + form2_ID + '_1' ).val( $( '#cmc-autocomplete' ).val() );
		$( '#form_address' ).html( $( '#cmc-autocomplete' ).val() );

	}

	function initPopMap() {

		if(typeof lat == 'undefined' || typeof lng == 'undefined' ) return;

		var myLatLon = { lat: lat, lng: lng }
  		var map = new google.maps.Map(document.getElementById('pop-map'), {
   			zoom: 16,
    		center: myLatLon,
			...window.iFoundGlobal.sharedGoogleMapBaseOptions,
			// Hide businesses.
			// I got it from here: https://stackoverflow.com/a/7510891/135101
			// Also here: https://developers.google.com/maps/documentation/javascript/examples/hiding-features
			styles: [
				{
					featureType: 'poi.business',
					stylers: [{ visibility: 'off' }],
				},
			],
  		});

		new google.maps.Size(20, 32);
  		var houseMarker = new google.maps.Marker({
    		position: myLatLon,
    		map: map,
    		icon: cmc_data.icon
  		});
	}

	function initPopUp() {

		var $window = $(window);
		var window_top_position = $window.scrollTop();
		var img_top_position = (window_top_position + 50);

		$('.pop-form').css('top', img_top_position);
		$( '.pop-form, .pop-backdrop, .pop-close' ).addClass( 'active' );
	}

});
