<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class Template extends iFoundAdmin {

	private $post_type		= 'template';
	private $label_name 	= 'Template';
	private	$label_names	= 'Templates';
	protected $blog_id		= 6;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	 
	public function __construct() {
		add_action( 'init', array( $this, 'template_post_type' ) );
		add_action( 'save_post_template', array( $this, 'save_template' ), 10, 1 );

		add_action( 'text', array( $this, 'text' ), 10, 2 );
		add_action( 'textarea', array( $this, 'textarea' ), 10, 2 );

		add_action( 'admin_enqueue_scripts', array( $this, 'code_editor' ) );

	}

	//https://make.wordpress.org/core/tag/codemirror/
	public function code_editor() {

		if ( 'template' !== get_current_screen()->id ) {
        	return;
    	}
  
    	// Enqueue code editor and settings for manipulating HTML.
    	$settings = wp_enqueue_code_editor( array( 'type' => 'text/html' ) );
  
    	// Bail if user disabled CodeMirror.
    	if ( false === $settings ) {
        	return;
    	}
  
    	wp_add_inline_script(
        	'code-editor',
        	sprintf(
        		'jQuery(document).ready(function($) {
        			$(".code").each( function(){
        				wp.codeEditor.initialize( this, %s );
        			});
        		});',
            	wp_json_encode( $settings )
        	)
    	);

	}

	public function template_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ) . ' - DO NOT USE THESE, see: https://ifoundagent.teamwork.com/app/tasks/37384020',
					'singular_name' => __( $this->label_name ) . ' - DO NOT USE THESE, see: https://ifoundagent.teamwork.com/app/tasks/37384020',
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ) . ' - DO NOT USE THESE, see: https://ifoundagent.teamwork.com/app/tasks/37384020',
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'register_meta_box_cb'	=> array( $this, 'metabox' )
			)
	  	);
		
	}

	public function template_fields( $i = false ) {
		
		$fields = array(
	     	'search'			=> array(
	     		10 	=> array(
	     			'key'		=> 'search',
	     			'name'		=> 'result_prop_content',
	     			'label' 	=> 'Property Content',
	     			'type'		=> 'textarea',
	     			'rows'		=> 16
	     		)
	        ),
	     	'details'				=> array(
	     		30 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'main',
	     			'label' 	=> 'Main Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		40 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'features',
	     			'label' 	=> 'Property Features',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		50 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'property',
	     			'label' 	=> 'Property Description',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		60 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'general',
	     			'label' 	=> 'General Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		70 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'school',
	     			'label' 	=> 'School Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		80 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'community',
	     			'label' 	=> 'Community Features',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		90 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'lot',
	     			'label' 	=> 'Lot Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		100 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'rooms',
	     			'label' 	=> 'Rooms Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		),
	     		110 	=> array(
	     			'key'		=> 'details',
	     			'name'		=> 'location',
	     			'label' 	=> 'Location Info',
	     			'type'		=> 'textarea',
	     			'rows'		=> 24
	     		)
	     	)
	 	);

	 	return $i ? $fields[$i] : $fields;
		
	}

	public function metabox() {

		add_meta_box( 
			'search', 
			__( '<i class="far fa-search"></i> Search Templates', 'ifound' ), 
			array( $this, 'box'), 
			$this->post_type,
			'advanced',
            'high'
		);

		add_meta_box( 
			'details', 
			__( '<i class="far fa-home"></i> Property Details Templates', 'ifound' ), 
			array( $this, 'box'), 
			$this->post_type,
			'advanced',
            'high'
		);

		add_meta_box( 
			'copy', 
			__( '<i class="far fa-copy"></i> Copy Templates', 'ifound' ), 
			array( $this, 'copy_metabox'), 
			$this->post_type,
			'side',
            'low'
		);
		
	}

	public function copy_metabox() {

		$template_id = get_the_ID();
		$href = $this->get_config()['template_origin'] . '/wp-admin/post-new.php?post_type=template&template_id=' . $template_id;
		?>

		<div>
			<a href="<? echo $href; ?>" class="button button-primary"><? _e( 'Create Copy of Templates', 'ifound' ); ?></a>
		</div>

		<div class="clear"></div><?

	}

	public function box( $post, $box ) { 
		
		$template_id = isset( $_GET['template_id'] ) ? intval( $_GET['template_id'] ) : get_the_ID();

		$key 	= $box['id'];
		$data = get_post_meta( $template_id, $key, true ); ?>
		
		<div class="client_meta">
				
			<table class="form-table client-table">
					
				<tbody><?

					foreach( $this->template_fields( $key ) as $template ) { ?>
		
						<tr>
								
							<th scope="row"><label for="<? echo $template['name']; ?>"><? _e( $template['label'], 'ifound' ); ?></label></th>
								
							<td><?
									
								do_action( $template['type'], $template, $data ); ?>
									
							</td>
								
							
						</tr><?

					} ?>
							
				</tbody>
					
			</table>
				
		</div><?

	}

	public function textarea( $template, $data ) { 

		$value = empty( $data[$template['name']] ) ? '' : $data[$template['name']]; ?>
 
		<textarea name="<? echo $template['key']; ?>[<? echo $template['name']; ?>]" class="large-text code" id="code" rows="<? echo $template['rows']; ?>"><? echo $value; ?></textarea><?
		
	}

	public function text( $template, $data ) { 

		$value = empty( $data[$template['name']] ) ? '' : $data[$template['name']]; ?>
  
		<input type="text" name="<? echo $template['key']; ?>[<? echo $template['name']; ?>]" class="large-text" value="<? echo $value; ?>"><?
		
	}

	public function save_template( $template_id ){
  
		if ( ! current_user_can( 'edit_posts' ) ) return;
		
		global $post; 

		$meta = array();
    
		if ( $post->post_type == $this->post_type && isset( $_POST['original_publish'] ) ) {

			foreach( $this->template_fields() as $key => $value ) {

				$meta = $this->sanitize( $_POST[$key] );

				update_post_meta( $template_id, $key, $meta );

			}

		}
		
	}

	public static function get_template_dropdown( $mls_class, $selected ) {

		switch_to_blog( 6 );

		$exclude_args = array( 
			'post_type' 		=> 'template', 
			'posts_per_page' 	=> -1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				array(
					'taxonomy'	=> 'mls_class',
					'field'    	=> 'slug',
					'terms'    	=> array( $mls_class ),
					'operator'  => 'NOT IN'
				)
			)
		);

		$exclude_ids = get_posts( $exclude_args );

		$args = array(
		    'selected'              => $selected,
		    'exclude'      			=> join( ',', $exclude_ids ),
		    'name'                  => 'template_id_' . $mls_class,
		    'id'                    => 'template_id',
		    'class'                 => 'template_id',
		    'option_none_value'     => '',
		    'show_option_none'      => 'Select Template',
		    'post_type'				=> 'template'
		);

		wp_dropdown_pages( $args );

		restore_current_blog();

	}

	public static function get_template( $mls_class, $mls_name ) {

		switch_to_blog( 6 );

		$args = array( 
			'post_type' 		=> 'template', 
			'posts_per_page' 	=> 1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_class',
					'field'    	=> 'slug',
					'terms'    	=> array( $mls_class )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $mls_name )
				)
			)
		);

		$template_ids = get_posts( $args );

		$template_id = $template_ids[0];

		$templates = array(
			'address' 		=> get_post_meta( $template_id, 'address', true ),
			'search' 		=> get_post_meta( $template_id, 'search', true ),
			'details' 		=> get_post_meta( $template_id, 'details', true )
		);

		restore_current_blog();

		return $templates;

	}

}
