<?php

namespace Profound\MLS;

class SdcrcaImageHandler extends ImageHandler {
	static $mlsname = "sdcrca";

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$img_array = array();
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Preferred` DESC, `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		foreach ($results as $key => $result) {
			$loc = preg_replace('/^(https?:)?\/\/IMG-PARAGON.SANDICOR.COM\/ParagonImages\/Property\/Q1/i', 'https://cdnparap00.paragonrels.com/ParagonImages/Property/p0', $result["Location"]);

			$img_array[$key]['normal_url'] = $loc;
			$img_array[$key]['thumbnail_url'] = $loc;
			$img_array[$key]['highres_url'] = $loc;
			$img_array[$key]['description'] = $result['Content-Description'];
		}
		return $img_array;
	}
}
