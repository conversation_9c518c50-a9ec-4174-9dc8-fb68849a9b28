<?
$genesis_settings = array(
	'genesis-vestige'           => array(
		'nav_superfish'         => 1,
		'nav_home'              => 1,
		'subnav_superfish'      => 1,
		'subnav_home'           => 1
	),
	'genesis-settings'          => get_option( 'genesis-settings' ),
	'theme_mods_' . $this->website['stylesheet']  => array(
		'nav_menu_locations'    => array(
			'primary'           => $this->menu_ids['main']
		),
		'custom_css_post_id'    => -1
	)
);


$sidebars_widgets = array(
	'sidebars_widgets' => array(
		'wp_inactive_widgets' => array(),
		'sidebar' => array(
			0 => 'ifound_quick_search-2',
			1 => 'custom_html-14',
			2 => 'recent-posts-2',
		),
		'front-page-1' => array(
			0 => 'metaslider_widget-2',
		),
		'search-bar' => array(
			0 => 'ifound_quick_search-3',
			1 => 'ifound_search_nearby-2'
		),
		'front-page-2-left' => array(
			0 => 'strong-testimonials-view-widget-2',
		),
		'front-page-2-right' => array(
			0 => 'custom_html-15',
		),
		'front-page-3' => array(
			0 => 'ifound_featured_listings-2',
		),
		'front-page-4' => array(
			0 => 'custom_html-6',
		),
		'front-page-5' => array(
			1 => 'featured-page-5',
			2 => 'featured-page-6',
			3 => 'featured-page-7',
			4 => 'featured-page-8',
			5 => 'featured-page-9',
			6 => 'featured-page-10',
		),
		'front-page-6' => array(
			0 => 'custom_html-4',
		),
		'front-page-7' => array(
			0 => 'custom_html-4',
		),
		'footer-1' => array(
			0 => 'nav_menu-2',
		),
		'footer-2' => array(
			0 => 'recent-posts-3',
		),
		'footer-3' => array(
			0 => 'custom_html-10',
			1 => 'ifound_social_media-2',
		),
		'after-entry' => array(
		),
		'details-before-slider' => array(
			0 => 'ifound_whats_my_payment-2',
		),
		'details-after-slider' => array(
			0 => 'ifound_save_this_property-2',
		),
		'after-details' => array(
			0 => 'ifound_featured_listings-3',
			1 => 'ifound_text_me_now-3',
			2 => 'ifound_call_me_now-2',
		),
		'search-results-before-criteria' => array(
		),
		'search-results-after-criteria' => array(
		),
		'search-results-after-map' => array(
			0 => 'ifound_save_this_search-2',
		),
		'search-results-after-results' => array(
		),
		'mobile-phone-me-now' => array(
			0 => 'ifound_call_me_now-3',
			1 => 'ifound_text_me_now-3',
		),
		'array_version' => 3,
	)
);

$custom_agent_info = <<<EOT
<img src="https://olympus.ifoundsites.com/wp-content/uploads/sites/834/2019/07/team-member-silhouette-male2x_4.jpg" width="150" height="150" class="alignnone/" alt="Agent">
<div class="phone">
	<i class="fas fa-mobile-android-alt"></i> <a href="tel:{$this->get_agent_info()['phone']}">{$this->get_agent_info()['phone']}</a>
</div>
<p><a class="button" href="/contact">Contact</a></p>
EOT;

$widgets = array(
	'widget_archives' => array(
		2 => array(
			'title' => '',
			'count' => 0,
			'dropdown' => 0,
		),
		'_multiwidget' => 1,
	),
	'widget_autotext' => array(
		'_multiwidget' => 1,
	),
	'widget_calendar' => array(
		'_multiwidget' => 1,
	),
	'widget_categories' => array(
		2 => array(
			'title' => '',
			'count' => 0,
			'hierarchical' => 0,
			'dropdown' => 0,
		),
		3 => array(
			'title' => 'Recent Search Blogs',
			'count' => 0,
			'hierarchical' => 0,
			'dropdown' => 0,
		),
		'_multiwidget' => 1,
	),
	'widget_featured-page' => array(
		2 => array(
			'title' => '',
			'page_id' => $this->post_ids['listing-search'],
			'show_image' => '1',
			'image_size' => 'medium',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		3 => array(
			'title' => '',
			'page_id' => $this->post_ids['whats-my-home-worth'],
			'show_image' => '1',
			'image_size' => 'medium',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		4 => array(
			'title' => $this->get_agent_name(),
			'page_id' => $this->post_ids['about'],
			'image_size' => 'thumbnail',
			'image_alignment' => 'alignnone',
			'show_content' => '1',
			'content_limit' => '390',
			'more_text' => 'More about ' . $this->get_agent_name(),
		),
		5 => array(
			'title' => $this->location[1],
			'page_id' => $this->post_ids[$this->page_slug[1]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		6 => array(
			'title' => $this->location[2],
			'page_id' => $this->post_ids[$this->page_slug[2]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		7 => array(
			'title' => $this->location[3],
			'page_id' => $this->post_ids[$this->page_slug[3]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		8 => array(
			'title' => $this->location[4],
			'page_id' => $this->post_ids[$this->page_slug[4]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		9 => array(
			'title' => $this->location[5],
			'page_id' => $this->post_ids[$this->page_slug[5]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		10 => array(
			'title' => $this->location[6],
			'page_id' => $this->post_ids[$this->page_slug[6]],
			'show_image' => '1',
			'image_size' => 'large-communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'content_limit' => '',
			'more_text' => '',
		),
		'_multiwidget' => 1,
	),
	'widget_featured-post' => array(
		2 => array(
			'title' => 'Latest Real Estate News',
			'posts_cat' => '0',
			'posts_num' => 3,
			'posts_offset' => '0',
			'orderby' => 'date',
			'order' => 'DESC',
			'gravatar_size' => '45',
			'gravatar_alignment' => 'alignnone',
			'show_image' => '1',
			'image_size' => 'communities',
			'image_alignment' => 'alignnone',
			'show_title' => '1',
			'post_info' => '[post_date] By [post_author_posts_link] [post_comments]',
			'show_content' => 'content-limit',
			'content_limit' => '200',
			'more_text' => 'Read More',
			'extra_title' => '',
			'extra_num' => '',
			'more_from_category_text' => 'More Posts from this Category',
		),
		'_multiwidget' => 1,
	),
	'widget_gform_widget' => array(
		'_multiwidget' => 1,
	),
	'widget_ifound_broker_logo' => array(
		2 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_cmc_form' => array(
		'_multiwidget' => 1,
	),
	'widget_ifound_featured_listings' => array(
		2 => array(''),
		3 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_quick_search' => array(
		2 => array(''),
		3 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_save_this_property' => array(
		2 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_save_this_search' => array(
		2 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_search_nearby' => array(
		2 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_text_me_now' => array(
		2 => array(''),
		3 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_call_me_now' => array(
		2 => array(''),
		3 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_whats_my_payment' => array(
		2 => array(''),
		'_multiwidget' => 1,
	),
	'widget_ifound_social_media' => array(
		2 =>  array(''),
		3 =>  array(''),
		'_multiwidget' => 1,
	),
	'widget_media_audio' => array(
		'_multiwidget' => 1,
	),
	'widget_media_image' => array(
		2 => [
			"size"                 => "full",
			"width"                => 1000,
			"height"               => 1000,
			"caption"              => "",
			"alt"                  => "",
			"link_type"            => "none",
			"link_url"             => "",
			"image_classes"        => "",
			"link_classes"         => "",
			"link_rel"             => "",
			"link_target_blank"    => false,
			"image_title"          => "",
			"attachment_id"        => 108,
			"url"                  => "https://peak.ifoundsites.com/wp-content/uploads/sites/1136/2021/06/hahn.jpg",
			"title"                => "",
			"column-classes"       => "one-half",
			"column-classes-first" => true,
		],
		3 => [
			"size"              => "full",
			"width"             => 150,
			"height"            => 76,
			"caption"           => "",
			"alt"               => "",
			"link_type"         => "none",
			"link_url"          => "",
			"image_classes"     => "",
			"link_classes"      => "",
			"link_rel"          => "",
			"link_target_blank" => false,
			"image_title"       => "",
			"attachment_id"     => 104,
			"url"               => "https://peak.ifoundsites.com/wp-content/uploads/sites/1136/2021/06/broker-logo-1.png",
			"title"             => "",
		],
		'_multiwidget' => 1,
	),
	'widget_media_video' => array(
		'_multiwidget' => 1,
	),
	'widget_meta' => array(
		2 => array(
			'title' => '',
		),
		'_multiwidget' => 1,
	),
	'widget_metaslider_widget' => array(
		2 => array (
			// Need to replace this
			'slider_id' => -1,
			'title' => '',
		),
		'_multiwidget' => 1,
	),
	'widget_nav_menu' => array(
		2 => array(
			'title' => 'Communities',
			'nav_menu' => $this->menu_ids['communities'],
		),
		'_multiwidget' => 1,
	),
	'widget_pages' => array(
		'_multiwidget' => 1,
	),
	'widget_recent-comments' => array(
		2 => array(
			'title' => '',
			'number' => 5,
		),
		'_multiwidget' => 1,
	),
	'widget_recent-posts' => array(
		2 => array(
			'title' => 'Latest Real Estate News',
			'number' => 5,
		),
		3 => array(
			'title' => 'Real Estate News',
			'number' => 5,
			'show_date' => false,
		),
		'_multiwidget' => 1,
	),
	'widget_rev-slider-widget' => array(
		2 => array(
			"rev_slider_title" => "",
			"rev_slider" => "1",
			"rev_slider_pages" => ""
		),
		'_multiwidget' => 1,
	),
	'widget_rss' => array(
		'_multiwidget' => 1,
	),
	'widget_search' => array(
		'_multiwidget' => 1,
	),
	'widget_strong-testimonials-view-widget' => array(
		2  => array(
			'title'   => '',
			'text'    => '',
			'filter'  => false,
			'view'    => 1,
		),
		'_multiwidget' => 1,
	),
	'widget_tag_cloud' => array(
		'_multiwidget' => 1,
	),
	'widget_custom_html' => array(
		4 => array(
			'title' => '',
			'content' => <<<EOT
<div class="two-thirds first">
<h2>
	Do You Know What Your Home is Worth?
</h2>
</div>
<div class="one-third">
<a href="/whats-my-home-worth/" class="button taglinebutton">Find Out</a></div>
EOT
		),
		5 => array(
			'title' => '',
			'content' => '',
		),
		6 => array(
			'title' => 'Featured Communities',
			'content' => '',
		),
		7 => array(
			'title' => '',
			'content' => '<div class="find-home"><a href="#findhome">Find a Home</a></div><div class="home-worth"><a href="/' . $this->home_worth_slug() . '/">What\'s My Home\'s Value?</a></div>',
		),
		8 => array(
			'title' => '',
			'content' => '<h5 style="margin-top: 40px;">"' . stripcslashes( $this->dev['tagline'] ) . '"</h5>',
		),
		9 => array(
			'title' => '',
			'content' => $this->header_phone(),
		),
		10 => array(
			'title' => 'Contact',
			'content' => $this->contact_info(),
		),
		11 => array(
			'title' => '',
			'content' => $this->my_account_link(),
		),
		12 => array(
			'title' => '',
			'content' => '<a class="button" href="listing-search">Search Homes</a> <a class="button" href="for-sellers">Sell Your Home</a>',
		),
		13 => array(
			'title' => 'Marketing',
			'content' => <<<EOT
<ul class="marketing owl-carousel">
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/hd-photo-bl.png" alt="Professional Photography" /><br />Professional Photography</li></a>
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/hd-video-bl.png" alt="Real Estate Videography" /><br />Real Estate Videography</li></a>
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/home-staging-bl.png" alt="Home Staging"/><br />Home Staging</li></a>
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/social-media-bl.png" alt="Social Media" /><br />Social Media</li></a>
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/print-bl.png" alt="Print Marketing"/><br />Print Marketing</li></a>
<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/open-houses-bl.png" alt="Open Houses"/><br />Open Houses</li></a>
	<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/coming-soon-bl.png" alt="Coming Soon Marketing"/><br />Coming Soon Marketing</li></a>
	<a href="/marketing/"><li><img src="/wp-content/themes/ifound-peaks/images/website-syndication-bl.png" alt="Website Marketing Syndication"/><br />Website Syndication</li></a>
</ul>
<div class="clear"></div>
EOT
		),
		14 => array(
			'title' => '',
			'content' => $custom_agent_info,
		),
		15 => array(
			'title' => $this->get_agent_name(),
			'content' => $custom_agent_info,
		),
		'_multiwidget' => 1,
	),
	'widget_user-profile' => array(
		'_multiwidget' => 1,
	)
);


$theme_settings = array_merge( $genesis_settings, $sidebars_widgets, $widgets );