<?
/**
 * iFound_save_this_property
 *
 * Save this Propery <PERSON>.
 *
 * @package iFOUND
 * @since 1.0.8
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );
 
class iFound_save_this_property extends WP_Widget {
	
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Save This Property Button', 
			array(
			'description' => 'Add a Save This Property Button to the property detail pages.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {
		
		echo $args['before_widget']; ?>

		<div class="ifound-save-this-property-button">

			<div class="widgets-wrap">

				<? do_action( 'ifound_save_property_button' ); ?>

			</div>

		</div>

		<?

		echo $args['after_widget'];

	}
	
}
