import { registerBlockType } from '@wordpress/blocks';
import { __ } from '@wordpress/i18n';
import Edit from './edit';
import { fromShortcode } from '../lib/transform';

import './style.scss';

registerBlockType( 'ifound/map-maker', {
	title: __( 'iFound Map Maker', 'ifound' ),
	description: __(
		'Add a map into your post',
		'ifound'
	),
	category: 'embed',
	icon: {
		src: <img src="/wp-content/plugins/ifound/idx/admin/images/polygon-drawing-icon.png" style={{ width: '100%' }} />
	},
	supports: {
		// Removes support for an HTML mode.
		html: false,
	},
	attributes: {
		// Note: create-polygon.js seems to have 3 other attributes: height, zoom, center. But it seems to not really
		// use them, and then restores whatever they were when the map maker is closed. <PERSON> thinks they never work.
		// So I'm going to ignore them altogether.

		polygons: {
			type: 'string',
		},
		labels: {
			type: 'string',
		},
	},
	transforms: {
		from: [{
			// This transform is used when the block is 'classic', and the user uses the triple dots and clicks "Convert
			// to blocks".
			type: 'shortcode',
			tag: 'ifound_polygon_maker',
			attributes: {
				polygons: {
					type: 'string',
					shortcode({ named }) {
						return named.polygons;
					},
				},
				labels: {
					type: 'string',
					shortcode({ named }) {
						return named.labels;
					},
				},
			},
		}, {
			...fromShortcode('ifound_polygon_maker', 'ifound/map-maker'),
		}],
	},
	edit: Edit,
	// We don't need a save function. We'll use the built-in save which returns null, which means save the attributes
	// in JSON format.
	// Update: When deploying to older WP versions like what's on getprequalified.com right now (5.1.8), you'll see
	// this error if you don't specify the save function:
	// The "save" property must be specified and must be a valid function
	save: () => null,
} );
