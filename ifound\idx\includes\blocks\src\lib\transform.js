import { createBlock } from '@wordpress/blocks';
import shortcode from '@wordpress/shortcode';

export function fromShortcode(shortcodeName, createBlockName) {
	// This transform is used when the block is a shortcode block, and the user clicks the
	// "Change block type or style" button.
	// I'm not sure why the transform and isMatch functions I've got here aren't the defaults supplied by Wordpress,
	// or why they didn't at least document this simple approach. I only figured out this shortcode.next() thing
	// from https://lkwdwrd.com/wp-shortcode-wp-html-wordpress-shortcodes-javascript
	return {
		type: 'block',
		blocks: ['core/shortcode'],
		transform(attributes) {
			const obj = shortcode.next(shortcodeName, attributes.text);
			return createBlock(createBlockName, obj.shortcode.attrs.named);
		},
		isMatch(attributes) {
			const obj = shortcode.next(shortcodeName, attributes.text);
			return obj && obj.shortcode.tag === shortcodeName;
		},
	}
}