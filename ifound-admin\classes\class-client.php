<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

require_once(__DIR__ . '/ParticipationReporting/ParticipationReporter.php');

class Client extends iFoundAdmin {
    public static $the_post_type = 'clients';

	private $post_type;
	private $label_name 	= 'Client';
	private	$label_names	= 'Clients';
	protected $blog_id		= 1;

	private static $status_map = array(
		'1' => array(
			'label' => 'Active',
			'class' => 'bg-green',
			'value' => 1,
		),
		'0' => array(
			'label' => 'Inactive',
			'class' => 'bg-red',
			'value' => 0,
		),
	);

	public static function init() {
		$class = __CLASS__;
        new $class;
    }


	public function __construct() {
        $this->post_type = static::$the_post_type;

		add_action( 'rest_api_init', array( $this, 'signup_route' ) );
		add_action( 'rest_api_init', array( $this, 'update_website_route' ) );
		add_action( 'init', array( $this, 'clients_post_type' ) );
		add_action( 'admin_init', array( $this, 'manual_rails_profile_listener' ) );
		add_action( 'wp_ajax_update_access_status', array( $this, 'update_access_status' ) );
		add_action( 'save_post_clients', array( $this, 'save_client' ), 10, 1 );
		add_action( 'admin_enqueue_scripts', array( $this, 'clients_scripts' ) );

		add_filter( 'manage_clients_posts_columns' , array( $this, 'add_clients_columns' ) );
		add_action( 'manage_clients_posts_custom_column' , array( $this, 'site_url_column'), 10, 2 );
		add_action( 'manage_clients_posts_custom_column' , array( $this, 'access_status_column'), 10, 2 );
		add_action( 'manage_clients_posts_custom_column' , array( $this, 'version_column'), 10, 2 );

		add_action( 'pre_get_posts', array( $this, 'show_clients_production' ), 10, 1 );

		add_action('admin_menu', [$this, 'custom_menus']);
	}

	public function custom_menus() {
		if( get_current_blog_id() == $this->blog_id ) {
			add_menu_page(
				'Participation Reporting',
				'Participation Reporting',
				'manage_options',
				'participation_reporting',
				[$this, 'participation_reporting_page'],
				'dashicons-media-spreadsheet'
			);
		}
	}

	public function clients_scripts(){

		wp_register_script( 'client_form_js', plugins_url( 'js/client-form.js', __DIR__ ), array( 'jquery' ), iFOUND_ADMIN_VERSION );

		wp_register_script( 'access_status_js', plugins_url( 'js/access-status.js', __DIR__ ), array( 'jquery' ), iFOUND_ADMIN_VERSION );
		wp_localize_script( 'access_status_js', 'access_status', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'access_status_secure_me' )
		));

	}

	public function has_ifound() {
		return has_term( 'ifound', 'idx_plugin', get_the_ID() );
	}

	public function not_production() {
		return ! has_term( 'production', 'client_status', get_the_ID() );
	}

	public function clients_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'register_meta_box_cb'	=> array( $this, 'add_metaboxes' )
			)
	  	);

	}

	public function add_clients_columns( $columns ) {

		if( isset( $_GET['version'] ) ) {

			return array_merge(
				array( 'cb' 						=> __( 'checkall', 'ifound' ) ),
				array( 'title' 						=> __( 'Title', 'ifound' ) ),
				array( 'website' 					=> __( 'Website', 'ifound' ) ),
				array( 'taxonomy-client_status' 	=> __( 'Website Status', 'ifound' ) ),
				array( 'version'					=> __( 'iFound Version', 'ifound' ) ),
				array( 'taxonomy-idx_plugin'		=> __( 'Plugin', 'ifound' ) ),
				array( 'taxonomy-multi_site' 		=> __( 'Multi Site', 'ifound' ) ),
				array( 'access_status' 				=> __( 'IDX Access', 'ifound' ) )
			);

		}

		return array_merge(
			array( 'cb' 						=> __( 'checkall', 'ifound' ) ),
			array( 'title' 						=> __( 'Title', 'ifound' ) ),
			array( 'website' 					=> __( 'Website', 'ifound' ) ),
			array( 'taxonomy-client_status' 	=> __( 'Website Status', 'ifound' ) ),
			array( 'taxonomy-mls_name'			=> __( 'MLS Name', 'ifound' ) ),
			array( 'taxonomy-developer'			=> __( 'Developer', 'ifound' ) ),
			array( 'taxonomy-plugin_features' 	=> __( 'Plugin Features', 'ifound' ) ),
			//array( 'taxonomy-multi_site' 		=> __( 'Multi Site', 'ifound' ) ),
			array( 'access_status' 				=> __( 'IDX Access', 'ifound' ) )
		);

	}

	public function site_url_column( $column, $client_id ) {

		if ( $column == 'website' ) {

			ob_start(); ?>

			<div><?

				$website 	= get_post_meta( $client_id, 'website', true );
				$url 		= $this->current_url( $client_id ); ?>

				<div>
					<a href="<? echo $url; ?>" target="_blank" class="button button-primary"><? _e( 'Home Page', 'ifound' ); ?></a>
					<a href="<? echo $url; ?>/wp-admin/" target="_blank" class="button button-primary"><? _e( 'WP Admin', 'ifound' ); ?></a>
				</div>
				<div><? _e( 'URL: ' . $url, 'ifound' ); ?></div>
				<div><? _e( 'Style Sheet: ' . $website['stylesheet'], 'ifound' ); ?></div>

			</div><?

			echo ob_get_clean();

		}

	}

	public function access_status_column( $column, $id ) {

		if( $column == 'access_status' ) {

			wp_enqueue_script( 'access_status_js' );

			ob_start(); ?>

			<div class="this-access-status"><?

				if ( $this->has_ifound() ) {

					$api_key = get_post_meta( get_the_ID(), 'api_key', true );

					if( $api_key ) {

						echo $this->get_access_status_form( $api_key );

					} else { ?>

						<div class="button button-primary" style="background: purple;"><? _e( 'Current Status - Denied', 'ifound' ); ?></div><?

					}

				} else { ?>

					<div><? _e( 'Non-IDX', 'ifound' ); ?></div><?


				} ?>

			</div><?

			echo ob_get_clean();

		}

	}

	public function version_column( $column, $id ) {

		if( $column == 'version' ) {

			ob_start(); ?>

			<div class="this-version"><?

				if ( $this->has_ifound() ) {

					$website = get_post_meta( get_the_ID(), 'website', true );

					$version = empty( $website['version'] ) ? 'NA' : $website['version'];

					$class = ( isset( $_GET['version'] ) && $version == $_GET['version'] ) ? 'bg-green' : 'bg-red'; ?>

					<div class="<? echo $class; ?>"><h3 style="color:#fff; padding: 2px"><? _e( $version, 'ifound' ); ?></h3></div><?

				} else { ?>

					<div><? _e( 'Non-IDX', 'ifound' ); ?></div><?


				} ?>

			</div><?

			echo ob_get_clean();

		}

	}

	public function show_clients_production( $query ) {

		global $typenow;

		if( $query->is_main_query() && $typenow == $this->post_type && empty( $_GET['post_status'] ) ) {
        	$query->set(
        		'tax_query',
        		array(
	        		array(
						'taxonomy'	=> 'client_status',
						'field'		=> 'slug',
						'terms'    	=> array( 'production' )
					)
	        	)
        	);

    	}

	}

	public function client_fields( $section = false ) {

		$client_fields = array(
	     	'client_info'			=> array(
	     		'avatar' 				=> array(
	     			'label' 			=> 'Avatar',
	     			'action' 			=> 'avatar',
	     			'params'			=> array( 'email' => $this->data['email'] ?? '' )
	     		),
	     		'fname' 				=> array(
	     			'label' 			=> 'First Name *',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'lname' 				=> array(
	     			'label' 			=> 'Last Name *',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'email' 				=> array(
	     			'label' 			=> 'Email *',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'phone' 				=> array(
	     			'label' 			=> 'Phone',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'agent_id' 				=> array(
	     			'label' 			=> 'Agent ID *',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'state_id'				=> array(
	     			'label' 			=> 'Agent State ID',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'broker_name' 			=> array(
	     			'label' 			=> 'Broker Name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'broker_id' 			=> array(
	     			'label' 			=> 'Broker ID',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        	'team_name' 			=> array(
	     			'label' 			=> 'Team Name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	        ),
	     	'website'				=> array(
	     		'stylesheet'			=> array(
	     			'label' 			=> 'Style Sheet',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'stylesheet', 'blog_id' => 2 )
	     		),
	     		'blog_id'				=> array(
	     			'label' 			=> 'Blog ID',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	     		'dev_url'				=> array(
	     			'label' 			=> 'Development URL',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
	     		'domain' 				=> array(
	     			'label' 			=> 'Domain Name',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
	     		'version'				=> array(
	     			'label' 			=> 'iFound Version',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	     		'convert_shortcodes'		=> array(
	     			'label' 			=> false,
	     			'action' 			=> 'update_website_button',
	     			'params'			=> array(
	     				'button_text' 	=> 'Convert Profound Shortcodes',
	     				'url' 			=> $this->data['domain'] ?? '',
	     				'url_params' 	=> '/?hasprofound',
	     				'target'		=> '_blank',
	     				'msg'			=> false,
	     				'condition'		=> ! empty( $this->data['domain'] ?? '' )
	     			)
	     		),
	     		'update_website'		=> array(
	     			'label' 			=> false,
	     			'action' 			=> 'update_website_button',
	     			'params'			=> array(
	     				'button_text' 	=> 'Update Website Data',
	     				'url' 			=> $this->current_url( $this->client_id ),
	     				'url_params' 	=> '/?getupdate&redirect=' . urlencode( home_url( $_SERVER['REQUEST_URI'] ) ),
	     				'target'		=> '',
	     				'msg'			=> 'Updating website data requires a valid Domain URL if in Production. Otherwise, it requires a valid Dev URL.',
	     				'condition'		=> true
	     			)
	     		)
	     	),
	        'development'			=> array(
	        	'build_button' 			=> array(
	     			'label' 			=> '',
	     			'action' 			=> 'build_button',
	     			'params'			=> array()
	     		),
	        	'sales_notes' 			=> array(
	     			'label' 			=> 'Sales Notes',
	     			'action' 			=> 'client_textarea',
	     			'params'			=> array( 'rows' => 5 )
	     		),
	     		'products' 				=> array(
	     			'label' 			=> 'Products',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'facebook' 				=> array(
	     			'label' 			=> 'Facebook',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'twitter' 				=> array(
	     			'label' 			=> 'Twitter',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'linkedin' 				=> array(
	     			'label' 			=> 'Linkedin',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'youtube' 				=> array(
	     			'label' 			=> 'YouTube',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'instagram' 			=> array(
	     			'label' 			=> 'Instagram',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'googleplus' 			=> array(
	     			'label' 			=> 'Google+',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'pinterest' 			=> array(
	     			'label' 			=> 'Pinterest',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'vimeo'      			=> array(
			        'label' 			=> 'Vimeo',
			        'action' 			=> 'url_input',
			        'params'			=> array()
		        ),
		        'tumblr'        		=> array(
			        'label' 			=> 'Tumblr',
			        'action' 			=> 'url_input',
			        'params'			=> array()
		        ),
		        'Tiktok'     			=> array(
			        'label' 			=> 'Tiktok',
			        'action' 			=> 'url_input',
			        'params'			=> array()
		        ),
		        'custom_1' 				=> array(
	     			'label' 			=> 'Custom 1',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'custom_2' 				=> array(
	     			'label' 			=> 'Custom 2',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'custom_3' 				=> array(
	     			'label' 			=> 'Custom 3',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),

		        'registrar' 			=> array(
	     			'label' 			=> 'Registar',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'registrar_un' 			=> array(
	     			'label' 			=> 'Registar UN',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'registrar_pw' 			=> array(
	     			'label' 			=> 'Registar PW',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),

		        'buffer_un' 			=> array(
	     			'label' 			=> 'Buffer UN',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'buffer_pw' 			=> array(
	     			'label' 			=> 'Buffer PW',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),

		        'color' 				=> array(
	     			'label' 			=> 'Colors',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),

		        'sliders' 				=> array(
	     			'label' 			=> 'Sliders',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'sub_section'			=> array(
	     			'label' 			=> 'Slider Sub Section',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'sub_section', 'blog_id' => 5 )
	     		),
		        'broker_logo' 			=> array(
	     			'label' 			=> 'Broker Logo URL',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'team_logo' 			=> array(
	     			'label' 			=> 'Team Logo URL',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),
		        'headshot' 				=> array(
	     			'label' 			=> 'Headshot URL',
	     			'action' 			=> 'url_input',
	     			'params'			=> array()
	     		),

		        'site_title' 			=> array(
	     			'label' 			=> 'Site Title',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'tagline' 				=> array(
	     			'label' 			=> 'Tag Line',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'bio' 					=> array(
	     			'label' 			=> 'Bio',
	     			'action' 			=> 'client_textarea',
	     			'params'			=> array( 'rows' => 7 )
	     		),
	     		'brand' 				=> array(
	     			'label' 			=> 'Branding',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),

		        'featured_criteria' 	=> array(
	     			'label' 			=> 'Featured Criteria',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_mls_ids' 		=> array(
	     			'label' 			=> 'Featured MLS IDs',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),

		        'featured_1' 			=> array(
	     			'label' 			=> 'Featured Community 1',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_1' 		=> array(
	     			'label' 			=> 'Easyname 1',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),
		        'featured_2' 			=> array(
	     			'label' 			=> 'Featured Community 2',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_2' 		=> array(
	     			'label' 			=> 'Easyname 2',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),
		        'featured_3' 			=> array(
	     			'label' 			=> 'Featured Community 3',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_3' 		=> array(
	     			'label' 			=> 'Easyname 3',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),
		        'featured_4' 			=> array(
	     			'label' 			=> 'Featured Community 4',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_4' 		=> array(
	     			'label' 			=> 'Easyname 4',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),
		        'featured_5' 			=> array(
	     			'label' 			=> 'Featured Community 5',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_5' 		=> array(
	     			'label' 			=> 'Easyname 5',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),
		        'featured_6' 			=> array(
	     			'label' 			=> 'Featured Community 6',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'featured_type_6' 		=> array(
	     			'label' 			=> 'Easyname 6',
	     			'action' 			=> 'category_dropdown',
	     			'params'			=> array( 'taxonomy' => 'easy_name', 'blog_id' => 1 )
	     		),

		        'primary_contact' 		=> array(
	     			'label' 			=> 'Primary Contact',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'primary_fname' 		=> array(
	     			'label' 			=> 'Primary Contact First name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'primary_lname' 		=> array(
	     			'label' 			=> 'Primary Contact Last Name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'primary_relationship' 	=> array(
	     			'label' 			=> 'Primary Contact Relationship',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'primary_phone' 		=> array(
	     			'label' 			=> 'Primary Contact Phone',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'primary_email' 		=> array(
	     			'label' 			=> 'Primary Contact Email',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		)
		    ),
	        'billing'				=> array(
		        'billing_fname' 		=> array(
	     			'label' 			=> 'Billing First Name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_lname' 		=> array(
	     			'label' 			=> 'Billing Last Name',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
	     		'billing_email' 		=> array(
	     			'label' 			=> 'Billing Email',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_address' 		=> array(
	     			'label' 			=> 'Billing Address',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_adress_2' 		=> array(
	     			'label' 			=> 'Billing Address 2',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_city' 			=> array(
	     			'label' 			=> 'Billing City',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_state' 		=> array(
	     			'label' 			=> 'Billing State',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_zip' 			=> array(
	     			'label' 			=> 'Billing Zip',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'billing_country' 		=> array(
	     			'label' 			=> 'Billing Country',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'payment_amount' 		=> array(
	     			'label' 			=> 'Billing Amount',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'payment_freq' 			=> array(
	     			'label' 			=> 'Billing Frequency',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'total_setup_fee'		=> array(
	     			'label' 			=> 'Billing Total Setup Fee',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'initial_setup_fee'		=> array(
	     			'label' 			=> 'Billing Initial Setup Fee',
	     			'action' 			=> 'text_input',
	     			'params'			=> array()
	     		),
		        'payment_notes_terms'	=> array(
	     			'label' 			=> 'Payment Notes/Terms',
	     			'action' 			=> 'client_textarea',
	     			'params'			=> array( 'rows' => 3 )
	     		)
		    )

	 	);

	 	return $section ? $client_fields[$section] : $client_fields;

	}

	public function add_metaboxes() {

		$this->client_id = get_the_ID();

		add_action( 'admin_footer', array( $this, 'payment_portal' ) );
		add_action( 'url_input' , array( $this, 'url_input' ), 10, 3 );
		add_action( 'avatar' , array( $this, 'avatar' ), 10, 3 );
		add_action( 'text_input' , array( $this, 'text_input' ), 10, 3 );
		add_action( 'client_textarea' , array( $this, 'textarea' ), 10, 3 );
		add_action( 'category_dropdown' , array( $this, 'category_dropdown' ), 10, 3 );
		add_action( 'update_website_button' , array( $this, 'update_website_button' ), 10, 3 );
		add_action( 'build_button' , array( $this, 'build_button' ), 10, 3 );

		if( $this->has_ifound() ) {

			add_meta_box(
				'idx_access_metabox',
				__( '<i class="far fa-universal-access"></i> IDX Access', 'ifound' ),
				array( $this, 'idx_access_metabox'),
				$this->post_type,
				'advanced',
	            'high'
			);

			add_meta_box(
				'website',
				__( '<i class="far fa-info"></i> Website Info', 'ifound' ),
				array( $this, 'metabox'),
				$this->post_type,
				'advanced',
	            'high'
			);

		}

		add_meta_box(
			'client_info',
			__( '<i class="fal fa-address-book"></i> Client Info', 'ifound' ),
			array( $this, 'metabox'),
			$this->post_type,
			'advanced',
            'high'
		);

		add_meta_box(
			'billing',
			__( '<i class="fal fa-address-book"></i> Billing Info', 'ifound' ),
			array( $this, 'metabox'),
			$this->post_type,
			'advanced',
            'high'
		);

		if( $this->not_production() ) {

			add_meta_box(
				'development',
				__( '<i class="fal fa-address-book"></i> Development', 'ifound' ),
				array( $this, 'metabox'),
				$this->post_type,
				'advanced',
		   		'high'
			);

		}

	}

	public function metabox( $post, $box ) {

		$this->data = get_post_meta( $this->client_id, $box['id'], true );

		wp_enqueue_script( 'build_site_js' );
		wp_enqueue_script( 'client_form_js' );
		wp_enqueue_script( 'access_status_js' ); ?>

		<div class="client_meta">

			<table class="form-table client-table">

				<tbody><?

					foreach( $this->client_fields( $box['id'] ) as $key => $value ) { ?>

						<tr><?

							if ( ! empty( $value['label'] ) ) { ?>

								<th scope="row"><label for="<? echo $key; ?>"><? _e( $value['label'], 'ifound' ); ?></label></th><?

							}

							do_action( $value['action'], $box['id'], $key, $value['params'] ); ?>

						</tr><?

					} ?>

				</tbody>

			</table>

		</div><?

	}

	public function avatar( $box_id, $key, $params ) {

		echo get_avatar( $params['email'], 50 );

	}

	public function text_input( $box_id, $key, $params ) { ?>

		<td>
			<input type="text" name="<? echo $box_id; ?>[<? echo $key; ?>]" id="<? echo $key; ?>" value="<? echo $this->data[$key] ?? ''; ?>" class="large-text">
		</td><?

	}

	public function url_input( $box_id, $key, $params ) { ?>

		<td>
			<input type="url" name="<? echo $box_id; ?>[<? echo $key; ?>]" id="<? echo $key; ?>" value="<? echo $this->data[$key] ?? ''; ?>" class="large-text" placeholder="https://">
		</td><?

	}

	public function textarea( $box_id, $key, $params ) { ?>

		<td>
			<textarea name="<? echo $box_id; ?>[<? echo $key; ?>]" id="<? echo $key; ?>" class="large-text" rows="<? echo $params['rows']; ?>"><? echo $this->data[$key] ?? ''; ?></textarea>
		</td><?

	}

	public function category_dropdown( $box_id, $key, $params ) {

		switch_to_blog( $params['blog_id'] );

		$args = array(
			'show_option_none'   => 'Select One',
			'hide_empty'         => 0,
			'selected'           => $this->data[$key] ?? '',
			'name'               => $box_id . '[' . $key . ']',
			'id'                 => $key,
			'class'              => 'postform',
			'taxonomy'           => $params['taxonomy'],
			'hide_if_empty'      => false,
			'value_field'	     => 'slug',
		); ?>

		<td><?

			wp_dropdown_categories( $args ); ?>

		</td><?

		restore_current_blog();

	}

	public function update_website_button( $box_id, $key, $params ) {

		if ( $params['condition'] ) { ?>

			<td colspan="2">
				<a href="<? echo $params['url']; echo $params['url_params']; ?>" class="button button-primary" target="<? echo $params['target']; ?>">
					<? _e( $params['button_text'], 'ifound' ); ?>
				</a>
			</td><?

			if ( ! empty( $params['msg'] ) ) { ?>

				<tr>

					<td colspan="2"><? _e( $params['msg'], 'ifound' ); ?></td>

				</tr><?

			}

		}

	}

	public function build_button() {

		$api_key = get_post_meta( get_the_ID(), 'api_key', true );

		if( $api_key ) { ?>

			<div class="build-site button button-primary" client_id="<? echo get_the_ID(); ?>"><? _e( 'Build Site', 'ifound' ); ?></div>
			<i class="far fa-spin fa-2x response"></i>
			<span class="build-site-msg" style="color:red;"></span>
			<span class="build-site-success" style="color:green;"></span>
			<?

		}

	}

	public function payment_portal() {

		if( ! is_super_admin() ) return; ?>

		<div id="wpcontent">

			<div class="client_meta postbox" style="max-width: 1200px;">

				<div id="poststuff">

					<h2 class="hndle ui-sortable-handle">
						<span><i class="far fa-dollar-sign"></i> <? _e( 'Payment Portal - ***This is a Prototype Only***', 'ifound' ); ?></span>
					</h2>

					<div class="inside" style="float:left;max-width: 767px;">

						<table class="form-table client-table">

							<tbody>

								<tr>

									<th scope="row" colspan="2"><label style="color:red;" class="payment-msg"></label></th>

								</tr>

								<!-- <form action="https://www.eProcessingNetwork.com/cgi-bin/dbe/transact.pl" method="POST"> -->
								<form action="https://abrentco.com">

									<tr>

										<th scope="row"><label for="CardNo"><? _e( 'Recurring', 'ifound' ); ?></label></th>

										<td><input type="checkbox" name="?????" value="" class="regular-text payment-input"></td>

									</tr>

									<tr>

										<th scope="row"><label for="CardNo"><? _e( 'Card Number', 'ifound' ); ?></label></th>

										<td><input type="text" name="CardNo" value="" class="regular-text payment-input"></td>

									</tr>

									<tr>

										<th scope="row"><label for="ExpMonth"><? _e( 'Card Exp. 00/0000', 'ifound' ); ?></label></th>

										<td><input type="text" name="ExpMonth" value="" class="small-text payment-input">/<input type="text" name="ExpYear" value="" class="small-text payment-input"></td>

									</tr>

									<tr>

										<th scope="row"><label for="CardNo"><? _e( 'CSV Number', 'ifound' ); ?></label></th>

										<td><input type="text" name="??????" value="" class="small-text payment-input"></td>

									</tr>

									<tr>

										<th scope="row"><label for="Total"><? _e( 'Total', 'ifound' ); ?></label></th>

										<td><input type="text" name="Total" value="" class="regular-text payment-input"></td>

									</tr>

									<tr>

										<td colspan="2"><input type="submit" value="Submit Transaction" class="button button-primary payment-input"></td>

									</tr>

									<tr>

										<th scope="row" colspan="2"><label><? _e( 'This form populates with the billing info above. We DO NOT store credit card numbers.', 'ifound' ); ?></label></th>

									</tr>

									<input type="hidden" name="Address" value="BillingStreetAddress">
									<input type="hidden" name="Zip" value="BillingZip">
									<input type="hidden" name="EMail" value="CustomersEMailAddress">
									<input type="hidden" name="ePNAccount" value="MerchantseProcessingNetworkAccountNumber">
									<input type="hidden" name="ID" value="YourUniqueDatabaseID">
									<input type="hidden" name="ReturnApprovedURL" value="https://ifoundadmin/transaction/approved/">
									<input type="hidden" name="ReturnDeclinedURL" value="https://ifoundadmin/transaction/declined/">
									<!-- Please make sure to use a 6 digit hex value -->
									<input type="hidden" name="BackgroundColor" value="ffffff">
									<!-- Please make sure to use a 6 digit hex value -->
									<input type="hidden" name="TextColor" value="000000">

								</FORM>

							</tbody>

						</table>

					</div>

					<div class="inside" style="float:left;max-width: 430px;">


						<table class="form-table client-table">

							<tbody>

								<tr>

									<th scope="row"><label for="Total"><? _e( 'Transactions', 'ifound' ); ?></label></th>

								</tr>

								<tr>

									<td>April 12, 2018 - $99999.00 - Approved - Initial Setup Fee</td>

								</tr>

							</tbody>

						</table>

					</div>

				</div>

			</div>

		</div><?

	}

	public function idx_access_metabox() {

		if( $data = get_post_meta( get_the_ID(), 'client_info', true ) ) { ?>

			<div class="client_meta">

				<table class="form-table client-table">

					<tbody>

						<tr><?

							$api_key = get_post_meta( get_the_ID(), 'api_key', true ); ?>

							<th scope="row"><label for="api_key"><? _e( 'API Key', 'ifound' ); ?></label></th>

							<td><?

								if( empty( $api_key ) ) {

									if( has_term( '', 'mls_name' ) && ! empty( $data['fname'] ) && ! empty( $data['lname'] ) && is_email( $data['email'] ) && ! empty( $data['agent_id'] ) ) { ?>

										<form method="post">
											<input type="hidden" name="client_id" value="<? echo get_the_ID(); ?>">
											<input type="submit" value="Get API Key" class="button button-primary" name="create_manual_apikey">
										</form><?

									} else {

										_e( '* Complete Required Fields, Assign an MLS, then Update Profile Before Getting API Key', 'ifound' );

									}


								} else {

									_e( $api_key, 'ifound' );

								} ?>

							</td>

						</tr><?

						if( $api_key ) { ?>

							<tr>

								<th scope="row"><label for="api_key"><? _e( 'IDX Status', 'ifound' ); ?></label></th>

								<td>

									<div class="this-access-status"><?

											echo $this->get_access_status_form( $api_key ); ?>

									</div>

								</td>

							</tr>

							<tr>

								<th scope="row" colspan="2"><? _e( '** The templates feature is currently not enabled. **', 'ifound' ); ?></label></th>


							</tr>

							<tr>

								<th scope="row"><label for="template"><? _e( 'Residential Templates', 'ifound' ); ?></label></th>

								<td><?

									if( $api_key ) {

										// FIXME: We are not saving this data on save_post
										Template::get_template_dropdown( 'res', $selected );

									} ?>

								</td>

							</tr>

							<tr>

								<th scope="row"><label for="template"><? _e( 'Rental Templates', 'ifound' ); ?></label></th>

								<td><?

									// FIXME: We are not saving this data on save_post
									Template::get_template_dropdown( 'rentals', $selected ); ?>

								</td>

							</tr>

							<tr>

								<th scope="row"><label for="template"><? _e( 'Lots and Land Templates', 'ifound' ); ?></label></th>

								<td><?

									// FIXME: We are not saving this data on save_post
									Template::get_template_dropdown( 'Land', $selected ); ?>

								</td>

							</tr><?

						} ?>

					</tbody>

				</table>

			</div><?

		}

	}

	public function get_access_status_form( $api_key ) {

		$status = $this->get_access_status( $api_key );
		if (!$status) {
			return 'WARNING: client not found (by API key)';
		}
        $html_parts = static::$status_map[$status];

		ob_start(); ?>

		<input type="hidden" class="access_apikey" value="<? echo $api_key; ?>">
		<input type="hidden" class="access_status" value="<? echo $html_parts['value']; ?>">
		<div class="button button-primary <? echo $html_parts['class']; ?> access-status-button"><? _e( 'Current Status - ' . $html_parts['label'], 'ifound' ); ?></div><?

		return ob_get_clean();

	}

	private $access_statuses_by_api_key = null;
	public function get_access_status( $api_key ) {
		if ($this->access_statuses_by_api_key === null) {
		    $this->load_access_statuses_by_api_key();
		}
        return $this->access_statuses_by_api_key[$api_key];
	}

	private function load_access_statuses_by_api_key() {
		$conn = $this->make_conn_to_railsadmin();
		$statement = $conn->prepare("SELECT access_status, access_apikey FROM access");
		$statement->execute();
		$access_statuses = $statement->fetchAll();
		$this->access_statuses_by_api_key = [];
		foreach ($access_statuses as $access_status) {
			$this->access_statuses_by_api_key[$access_status['access_apikey']] = $access_status['access_status'];
		}
	}

	public function update_access_status() {

		check_ajax_referer( 'access_status_secure_me', 'access_status_nonce' );

		$conn = $this->make_conn_to_railsadmin();

		$api_key 	= sanitize_text_field( $_REQUEST['access_apikey'] );
		$status 	= ( $_REQUEST['access_status'] == '1' ) ? 0 : 1;

		$sql = "
			UPDATE access
			SET access_status = " . $status . "
			WHERE access_apikey = '" . $api_key . "'
		";

		$conn->query( $sql );

		$conn = null;

		echo json_encode( $this->get_access_status_form( $api_key ) );

		die();

	}

	public function signup_route() {

		register_rest_route(
			'ifound-admin/' . $this->api_version,
			'/signup/',
			array(
				'methods'  				=> WP_REST_Server::EDITABLE,
				'callback' 			  	=> array( $this, 'signup' ),
				'permission_callback' 	=> function( $request ) {
					$this->validate_signup_key();
          			return $this->validate_signup_referer();
				}
			)
		);

	}

	public function signup() {

		$entry_id = intval( $_POST['entry_id'] );

		if( is_int( $entry_id ) && $entry_id > 0 ) {

			if( $entry = $this->fetch_sidnup_entry( $entry_id ) ) {

				// Pull out data related to the build and not info submitted by the client.
				$meta = isset($entry['meta']) ? $entry['meta'] : null;
				unset($entry['meta']);
				$is_entry_level_site = isset($meta['entry_level']) && $meta['entry_level'];

				$entry['full_name'] 	= $entry['fname'] . ' ' . $entry['lname'];
				$entry['idx_plugin'] 	= $this->get_idx_plugin( $entry['products'] );

				$post = array(
					'post_title'	=> $entry['full_name'],
			  		'post_type'   	=> $this->post_type,
			  		'post_status'   => 'publish'
				);

				$this->client_id = wp_insert_post( $post );

				if( $entry['idx_plugin'] == 'ifound' ) {

					$api_key = $this->set_api_key_to_rails( $entry );

					update_post_meta( $this->client_id, 'api_key', $api_key );

				}

				$this->add_meta( $entry );

				$this->set_default_terms( $entry, $is_entry_level_site ? 'Production' : 'New Project' );
				$this->set_features( $entry['products'] );

				// If this is an entry level site, build it right away
				if ($is_entry_level_site) {
					do_action('ifound_build_site', $this->client_id, true);
				}

				return 'Success';

			}

			wp_die('An Error Has Occured, did not fetch entry by entry ID');
		}

		wp_die('An Error Has Occured, no entry id');

	}

	public function add_meta( $entry ) {

		$data = array();

		foreach( $this->client_fields() as $meta_name => $keys ) {

			foreach( $keys as $key => $label ) {

				if( is_array( $entry[$key] ) )

					$data[$key] = join( ',', $entry[$key] );

				else

					$data[$key] = is_email( $entry[$key] ) ? sanitize_email( $entry[$key] ) : sanitize_text_field( $entry[$key] );

			}

			update_post_meta( $this->client_id, $meta_name, $data );

			unset( $data );

		}

	}

	public function fetch_sidnup_entry( $entry_id ) {

		$signup_db = $this->signup_db();

		$sql = "
			SELECT *
			FROM access_signup
			WHERE id = $entry_id
		";

		foreach ( $signup_db->query( $sql ) as $row ) {
			$entry = $row['entry'];
		}


		return empty( $entry ) ? false : $this->mb_unserialize( $entry );

	}

	// See: https://stackoverflow.com/a/27924449/135101
	function mb_unserialize($string) {
		$string2 = preg_replace_callback(
			'!s:(\d+):"(.*?)";!s',
			function($m){
				$len = strlen($m[2]);
				$result = "s:$len:\"{$m[2]}\";";
				return $result;

			},
			$string);
		return unserialize($string2);
	}

	private function signup_db() {

		$servername = SIGNUP_HOST;
		$username 	= SIGNUP_USER;
		$password 	= SIGNUP_PASS;
		$dbname 	= SIGNUP_NAME;

		return new PDO( "mysql:host=$servername;dbname=$dbname", $username, $password );

	}

	public function update_website_route() {

		register_rest_route(
			'ifound-admin/' . $this->api_version,
			'/update-website/',
			array(
				'methods'  	=> WP_REST_Server::EDITABLE,
				'callback' 	=> array( $this, 'update_website' )
			)
		);

	}

	public function update_website () {

		if( $client_id = $this->get_client_id() ) {

			$current 	= get_post_meta( $client_id, 'website', true );
			$data 		= $this->sanitize( $_POST );

			$website = array(
				'dev_url'		=> empty( $data['dev_url'] ) 	? $current['dev_url'] 		: $data['dev_url'],
				'domain' 		=> empty( $data['domain'] ) 	? $current['domain'] 		: $data['domain'],
				'blog_id'		=> empty( $data['blog_id'] ) 	? $current['blog_id'] 		: $data['blog_id'],
				'version'		=> empty( $data['version'] ) 	? $current['version'] 		: $data['version'],
				'stylesheet' 	=> empty( $data['stylesheet'] )	? $current['stylesheet']	: $data['stylesheet']
			);

			update_post_meta( $client_id, 'website', $website );

		}

	}

	public function manual_rails_profile_listener() {

		$client_id = intval( $_POST['client_id'] );

		if( is_super_admin() && is_user_logged_in() && isset( $_POST['create_manual_apikey'] ) && is_int( $client_id ) && $client_id > 0 ) {

			$client_info = get_post_meta( $client_id, 'client_info', true );

			$clean = array(
				'agent_id'	=> $client_info['agent_id'],
				'email' 	=> $client_info['email'],
				'mls_name'	=> $this->get_mls_name( $client_id ),
				'full_name' => $client_info['fname'] . ' ' . $client_info['lname']
			);

			$api_key = $this->set_api_key_to_rails( $clean );

			update_post_meta( $client_id, 'api_key', $api_key );

		}

	}

	public function set_api_key_to_rails( $clean ) {

		$api_key = $this->get_api_key();

		$this->insert_to_rails( array(
			'account_id'	=> sanitize_key( $clean['full_name'] ) . time(),
			'api_key'		=> $api_key,
			'agent_id'		=> $clean['agent_id'],
			'full_name'		=> $clean['full_name'],
			'email' 		=> $clean['email'],
			'mls_name'		=> $clean['mls_name']
		));

		return $api_key;

	}

	public function get_api_key() {

		$Block1 	= substr( uniqid(), 8, 5 );

		$TimeArray 	= explode( '.', microtime() );
		$TimeArray2 = explode( ' ', $TimeArray[1] );
		$TimeArray3 = sha1( base_convert( $TimeArray2[0] . $TimeArray2[1], 10, 16 ) );
		$Block2 	= substr( $TimeArray3, 5, 5 );

		$Sub1 		= sha1( substr( $TimeArray3, 10, 5 ) );
		$Sub2 		= substr( $Sub1, rand( 0, strlen( $Sub1 ) - 5 ),rand( 0, strlen( $Sub1) - 5 ) + 5 );
		$Block3 	= substr($Sub2, 0, 5 );

		$Block4 	= substr( rand(), 0, 5 );

		$char 		= "0213456789abcdefghijklmnopqrstuvwxyz";
	 	$Block5 	= substr( str_shuffle( str_repeat( $char, 5 ) ), 0, 2 );
		$Block6 	= substr( str_shuffle( str_repeat( $char, 5 ) ), 0, 2 );
		$Sub3 		= substr( str_shuffle( str_repeat( $char, 5 ) ), 0, 5 );
		$sub4 		= substr( MD5( $Sub3 ), 10, 5 );

		$Block9 	= str_replace( substr( $Block4, 3, 2 ), $Block5, $Block4 );

		$Block10 	= str_replace( substr( $Block2, 3, 2 ), $Block6, $Block2 );

		$subs 		= array( $Sub3, $sub4, $Block1, $Block3 );
		$rand 		= array_rand( $subs, 2 );
		$Block11 	= $subs[$rand[0]];
		$Block12 	= $subs[$rand[1]];

		$DateBlock	= date( 'M-Y' );

		$api_key   = $Block9 . '-' . $Block10 . '-' . $Block11 . '-' . $Block12 . '-' . $DateBlock ;

		if( $this->exists( 'api_key', $api_key ) ) {
			$this->get_api_key();
		}

		return strtoupper( $api_key );

	}

	private function insert_to_rails( $input ) {
		extract( $input );
		$conn = $this->make_conn_to_railsadmin();
		$conn->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
		try {
            $sql = "select
                meta_result_prop_content,
                meta_pdp_property_value,
                meta_pdp_general_value,
                meta_pdp_school_value,
                meta_pdp_community_value,
                meta_pdp_lot_value,
                meta_pdp_rooms_value,
                meta_pdp_location_value,
                meta_pdp_main_value,
                meta_pdp_features_value
                from mls_systems
                where name = '$mls_name'
            ";
            $result = $conn->query($sql)->fetchAll();
            $mls_system = $result[0];
            $conn->beginTransaction();
			$sql = "
				INSERT INTO access
				( access_account_id, access_apikey, access_member_id, access_fullname, access_emailaddress, mls )
				values
				('" . $account_id . "', '" . $api_key . "', '" . $agent_id . "', '" . $full_name . "', '" . $email . "', '" . $mls_name . "')
			";
			$conn->exec( $sql );
			$access_id = $conn->lastInsertId();
			if( ! empty( $access_id ) ) {
				$sql = "
                 INSERT INTO access_meta
                 (
                    access_id,
                    meta_prop_url,
                    meta_prop_title,
                    meta_prop_h1,
                    meta_prop_description,
                    meta_result_prop_h2,
                    meta_result_prop_content,
                    meta_pdp_property_value,
                    meta_pdp_general_value,
                    meta_pdp_school_value,
                    meta_pdp_community_value,
                    meta_pdp_lot_value,
                    meta_pdp_rooms_value,
                    meta_pdp_location_value,
                    meta_address,
                    meta_street_address,
                    meta_pdp_main_value,
                    meta_pdp_features_value
                ) values (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?
                )
            ";
                $p = $conn->prepare($sql);
				$p->execute([
					intval( $access_id ),
					'RequiredFieldDoNotRemove',
					'RequiredFieldDoNotRemove',
					'RequiredFieldDoNotRemove',
					'RequiredFieldDoNotRemove',
					'RequiredFieldDoNotRemove',
					$mls_system['meta_result_prop_content'],
                    $mls_system['meta_pdp_property_value'],
                    $mls_system['meta_pdp_general_value'],
                    $mls_system['meta_pdp_school_value'],
                    $mls_system['meta_pdp_community_value'],
                    $mls_system['meta_pdp_lot_value'],
                    $mls_system['meta_pdp_rooms_value'],
                    $mls_system['meta_pdp_location_value'],
                    'RequiredFieldDoNotRemove',
                    'RequiredFieldDoNotRemove',
                    $mls_system['meta_pdp_main_value'],
                    $mls_system['meta_pdp_features_value'],
                ]);
			}
            $conn->commit();
		} catch(PDOException $e) {
			$message = 'The profile was not created in Rails. ' . $e->getMessage();
			die( $message );
		}
	}

	public function save_client( $client_id ){

		if ( ! current_user_can( 'edit_posts' ) ) return;

		global $post;

		if ( $post->post_type == $this->post_type && isset( $_POST['original_publish'] ) ) {

			$this->client_id = $client_id;

			$this->save_meta( $_POST );

			$this->features_push( $client_id );

		}

	}

	public function save_meta( $entry ) {

		$data = array();

		foreach( $this->client_fields() as $meta_name => $keys ) {

			if( ! isset( $entry[$meta_name] ) ) continue;

			foreach( $keys as $key => $label ) {

				$value = sanitize_text_field( $entry[$meta_name][$key] );

				if( is_array( $value ) )

					$data[$key] = join( ',',$value );

				else

					$data[$key] = $value;

			}

			update_post_meta( $this->client_id, $meta_name, $data );

			unset( $data );

		}

	}

	public function features_push( $client_id ) {

		$website 	= get_post_meta( $client_id, 'website', true );
		$key 		= has_term( 'production', 'client_status', $client_id ) ? 'domain' : 'dev_url';;

		$url_parts = array(
			$website[$key] ?? null,
			'wp-json',
			'ifound',
			'1.0.0',
			'features'
		);

		$features = wp_get_object_terms( $client_id, 'plugin_features', array( 'fields' => 'slugs' ) );

		$url = join( '/', $url_parts );
		$response = wp_remote_post(
			$url,
			array(
				'body' => array(
					'features' => $features
				),
				'headers' => array(
		            'Accept' 			=> 'application/json',
					'Referer' 			=> site_url(),
					'Authentication'	=> get_post_meta( $client_id, 'api_key', true )
				),
				'sslverify' => $this->get_config()['environment'] === 'production',
			)
		);
		if (is_wp_error($response)) {
			elog('Feature push error', $response);
		}

	}

	public function set_default_terms( $clean, $client_status ) {

		$defaults = array(
			$client_status			=> 'client_status',
			$clean['mls_name']		=> 'mls_name',
			$clean['sales_rep']		=> 'sales_rep',
			$clean['idx_plugin']	=> 'idx_plugin'
		);

		foreach( $defaults as $key => $value )
			wp_set_object_terms( $this->client_id, $key, $value, true );

	}

	public function set_features( $products ) {

		$features = array(
			'idx',
			'crm',
			'cmc',
			'drip-campaigns',
			'teams'
		);

		foreach( $features as $feature ) {

			if( is_array( $products ) && in_array( $feature, $products ) ){
			 	wp_set_object_terms( $this->client_id, $feature, 'plugin_features', true );
			}

		}

	}

	public function current_url( $client_id ) {

		$website 	= get_post_meta( $client_id, 'website', true );
		$key 		= has_term( 'production', 'client_status', $client_id ) ? 'domain' : 'dev_url';

		return $website[$key] ?? null;

	}

	public function get_idx_plugin( $products ) {

		if( is_array( $products ) && in_array( 'no-idx', $products ) )
			return 'no-idx';

		if( is_array( $products ) && in_array( 'ihomefinder', $products ) )
			return 'ihomefinder';

		return 'ifound';

	}

	public function participation_reporting_page() {
	    $mls_name = $_GET['mls_name'];

        if ($mls_name) {
	        $should_submit_report = isset($_POST['send_participation_report']);
	        if ($should_submit_report) {
		        $mls_name = $_POST['mls_name'];
	        }
	        $reporter = ParticipationReporter::create($mls_name, $this);
	        if ($should_submit_report) {
		        echo $reporter->submit();
	        } else {
		        $home_link = remove_query_arg('mls_name', (new Util())->current_url());
                ?>
                <div>
                    <a href="<?= $home_link ?>">&lt; Back to Participation Reporting Home</a>
                </div>
                <?php
		        $reporter->print_report_preview();
	        }
            ?><h2>Participation Reporting History</h2><?php
	        $reporter->print_participation_reporting_history();
        } else {
	        ParticipationReporter::choose_mls_page();
        }
	}
}
