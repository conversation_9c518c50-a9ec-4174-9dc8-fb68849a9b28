// @link https://hugh.blog/2015/12/18/create-a-custom-featured-image-box/
jQuery(document).ready(function($) {

	// Uploading files
	var file_frame;
	var section_id;

	$( document ).on( 'click', '.page_img_button', function() {
		
		section_id = $( this ).attr( 'id' );
		
		// If the media frame already exists, reopen it.
		if ( file_frame ) {
		  file_frame.open();
		  return;
		}

		// Create the media frame.
		file_frame = wp.media.frames.file_frame = wp.media({
		  	title: $( this ).data( 'uploader_title' ),
		  	button: {
		    	text: $( this ).data( 'uploader_button_text' ),
		  	},
		  	multiple: false
		});

		// When an image is selected, run a callback.
		file_frame.on( 'select', function() {
		  
		  	var attachment = file_frame.state().get( 'selection' ).first().toJSON();
		
		  	$( '#page_img_input_' + section_id ).val( attachment.url );
		  	$( '#page_img_' + section_id ).attr( 'src', attachment.url );
		  	$( '#page_img_' + section_id ).show();
		  	$( '#' + section_id ).removeClass( 'page_img_button' ).addClass( 'remove_page_img_button' );
		  	$( '#' + section_id ).html( '<i class="far fa-trash-alt"></i> Remove featured image' );
		
		});

		// Finally, open the modal
		file_frame.open();
	});

	$( document ).on( 'click', '.remove_page_img_button', function() {
		section_id = $( this ).attr( 'id' );
		$( '#page_img_input_' + section_id ).val( '' );
		$( '#page_img_' + section_id ).attr( 'src', '' );
		$( '#page_img_' + section_id ).hide();
		$( '#' + section_id ).removeClass( 'remove_page_img_button' ).addClass( 'page_img_button' );
		$( '#' + section_id ).html( '<i class="far fa-image"></i> Set featured image' );
	});

	$( '.clone-parent-page-button' ).on( 'click', function() {
		var clone = $( this ).siblings( '.clone-parent-page-input' ).val();
		console.log(clone);
		$( '.clone-parent-page-input' ).each(function(){
			if( ! $( this ).val().length ){
				$( this ).val( clone );
				$( this ).parents('.featuredimagediv').children( '.clone-parent-page-img' ).attr( 'src', clone );
			}
		});
	});

	$( '.clone-parent-featured-button' ).on( 'click', function() {
		var clone = $( this ).siblings( '.clone-parent-featured-input' ).val();
		console.log(clone);
		$( '.clone-parent-featured-input' ).each(function(){
			if( ! $( this ).val().length ){
				$( this ).val( clone );
				$( this ).parents('.featuredimagediv').children( '.clone-parent-featured-img' ).attr( 'src', clone );
			}
		});
	});

});