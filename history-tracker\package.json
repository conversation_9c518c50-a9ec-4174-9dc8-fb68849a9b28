{"name": "history-tracker", "version": "1.0.0", "description": "Track history of real estate listing changes", "main": "dist/index.js", "type": "module", "scripts": {"dev": "NODE_ENV=dev tsx src/index.ts", "prod": "NODE_ENV=prod tsx src/index.ts", "build": "tsc && vite build", "test": "vitest --run", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "start": "NODE_ENV=prod node dist/index.js", "format": "prettier --write \"src/**/*.ts\" \"*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"*.ts\""}, "keywords": ["real-estate", "history", "tracking"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.0.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "prettier": "^3.0.0", "tsx": "^4.20.3", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^1.0.0"}, "dependencies": {"@sendgrid/mail": "^8.1.0", "mysql2": "^3.6.0", "twilio": "^4.19.0"}}