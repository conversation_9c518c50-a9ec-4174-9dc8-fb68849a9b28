# Add an MLS

This document describes how to add an additional MLS system.

The IDX connects to multiple MLS systems via RETS. As of this writing, we connect to ARMLS, PAAR, TREND, Sandicor, GLVAR, MRED, and TARMLS.

The naming convention is for MLS systems is to use their company's designation, optionally followed by the state, in lowercase. So for PAAR which is in Arizona, it's paaraz.

This document was initially written while connecting to PAAR (paaraz), so concrete examples will use it. 

## Database

### MLS system

Add MLS to mls_systems table. You can copy the example in `idx2/db/migrate/20140515144924_add_sdcrca_to_mls_systems.rb`.

### Field mappings

Use `mysqldump` to save the server's `field_mapping` table into the `field_mapping.sql` file.

There is file at `database/field_mappings.sql`. Add a new column to the table's definition for the mls, and then add the definitions for the MLS following the pattern on all the INSERT INTO lines.

Run the file through MySQL, which will drop the existing field_mapping table and create another one with the latest data.

## Update code

### Rails Admin

In project's `idx2` directory:

- `app/assets/javascripts/angular/controllers/field_mappings.coffee`: add the MLS nickname to the MLSs array
- `app/controllers/api/field_mappings_controller.rb`: add the MLS nickname to the `params.permit` method call within `create_params` method

Deploy with:

`cap deploy`

### IDX (PHP)

In project's `idx` directory:

#### IDX.php

Update the `getLocalMlsClassFromGeneric` method to reflect added MLS and MLS classes.

#### Mls class

`includes/classes/Profound/MLS/Mls.php`:

* `::create`: Add abbreviation to list.
* `::getMlsNameAbbrevs`: Add abbreviation to list.

#### `<Camelcased version of MLS name>.php` e.g. Paaraz.php:

Copy an existing MLS class.


#### ImageHandler class

`includes/classes/Profound/MLS/<new mls nickname>ImageHandler.php`

Copy an existing image handler class.

### IDX (Node.js)

`server/lib/PropSearch.iced'

Do a search for 'armls'. Basically any place you see it is a place that needs to take *consideration* of the new MLS. Here are at least 3 considerations that the file currently handles:

* Active vs closed listings
* Image paths
* Listing contingent status

`server/lib/FieldCleaner.iced`

This file is where fields are 'cleaned', or formatted, meaning values meant to be shown as currency have dollar signs, etc.

### Wordpress plugin

In project's `plugin` directory:

#### Mls class

`classes/Profound/MLS/Mls.php`:

* `::create`: Add abbreviation to list.

`classes/Profound/MLS/MlsClass.php`:

* `::getMlsClassMapping`: Added MLS and classes to mapping.

* Add naming for each of the following (and create another class if you add a new mls class):

    * `classes/Profound/MLS/Residential.php`
    * `classes/Profound/MLS/Rentals.php`
    * `classes/Profound/MLS/Land.php`

#### `<Camelcased version of MLS name>.php` e.g. Paaraz.php:

Copy an existing MLS's class

### Sync RETS data

#### Add to config

Edit `idx/config.ini` with the new MLS. Copy an existing MLS section for hints.

#### Run sync

* Run it once to get the table set up.
* Add fields and indexes as described below, and save the definition of these indexes in a file like `db/564-sandicor-paragon.sql`.
  * Add fields for latitude/longitude (if they don't already exist)
  * UPDATE: The following should no longer be relevant. We shouldn't need indexes directly on the MySQL tables, because the searching is now handled by Solr.
    * Then add indexes. You'll want indexes for at least the following field definitions:
      * listing key
      * MLS ID (if this is different)
      * timestamp
      * MLS member
      * MLS office
      * city
      * postal code
      * bathrooms
      * bedrooms
      * subdivision
* Need to sync 'fields' (lookups). E.g. from within the `idx` directory: `$ php includes/classes/rets.php --mls sdcrca --class RE_1 --cmd fields`.
  * Add indexes for these fields:
    * SystemName (this seems to be created automatically? perhaps by our code that creates the table)
    * LookupName
    * Interpretation

## Solr

Solr is our search indexing tool.  Read more at [Solr](Solr.md). You'll need to regenerate the configs, but you should do this after first syncing some data, because the MySQL tables must exist first, because the solr config gen tool looks at the table definitions.

## Add new client via Profound IDX Admin

Be sure to modify all the meta settings, and do not include any that are not mapped. For example, when doing paaraz, I copied the client's metadata from an ARMLS client. ARMLS has a mapping for LotSquareFeet, so "{LotSquareFeet}" was on the result SEO meta data. But that causes the database to blow up. Removing LotSquareFeet was the fix.

## Add Wordpress site

Be sure to run `?pfmls_idx_update`.

## Update Property Details Page

Add any desired fields that you have field mappings for.

## Update cron jobs

On our IDX server, add the MLS so it will be synced.

Also add a sync job for the lookups.

## Update Makefile

At the least, you'll need to update the Makefile target 'optimize' (in the project root). Add an line for each table for the MLS, including the images table.
