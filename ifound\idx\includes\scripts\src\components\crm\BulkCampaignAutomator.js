import React, { useState } from 'react';
import BulkCampaignPublisher from './BulkCampaignPublisher';
import cloneDeep from 'lodash/cloneDeep';
import PropTypes from 'prop-types';
import useFetcher from '../../hooks/useFetcher';
import axios from 'axios';
import urljoin from 'url-join';
import useIfoundToasts from '../../hooks/useIfoundToasts';
import _difference from 'lodash/difference'

BulkCampaignAutomator.propTypes = {
	googleMapsApiKey: PropTypes.string,
	campaigns: PropTypes.array.isRequired,
	subjectMapIcon: PropTypes.string.isRequired,
	endpoint: PropTypes.string.isRequired,
};

function BulkCampaignAutomator(props) {
	const {
		googleMapsApiKey,
		campaigns,
		subjectMapIcon,
		endpoint,
	} = props;

	const [publishedCampaigns, setPublishedCampaigns] = useState([]);
	const [unpublishedCampaigns, setUnpublishedCampaigns] = useState(() => cloneDeep(campaigns));
	const [selectedCampaigns, setSelectedCampaigns] = useState([]);
	const [campaignIdToDelete, setCampaignIdToDelete] = useState(null);
	const [selectedBulkAction, setSelectedBulkAction] = useState('bulk_actions');
	const { showToastForFetchError } = useIfoundToasts();

	function toggleSelectCampaign(campaignId) {
		if (selectedCampaigns.find(x => x.ID === campaignId)) {
			setSelectedCampaigns(selectedCampaigns.filter(x => x.ID !== campaignId));
		} else {
			const campaign = unpublishedCampaigns.find(x => x.ID === campaignId);
			setSelectedCampaigns(selectedCampaigns.concat(campaign));
		}
	}

	const {
		isLoading: deleteSingleIsLoading,
		run: runDeleteSingle,
	} = useFetcher({
		fetchFn: () => {
			return axios({
				url: urljoin(endpoint, campaignIdToDelete.toString()),
				method: 'DELETE',
			})
				.then(() => {
					setUnpublishedCampaigns(unpublishedCampaigns.filter(x => x.ID !== campaignIdToDelete));
					setSelectedCampaigns(selectedCampaigns.filter(x => x.ID !== campaignIdToDelete));
				})
				.catch(showToastForFetchError)
				.finally(() => {
					setCampaignIdToDelete(null);
				});
		},
	});

	const {
		isLoading: deleteSelectedIsLoading,
		run: runDeleteSelected,
	} = useFetcher({
		fetchFn: () => {
			return axios({
				url: urljoin(endpoint, 'delete_many'),
				method: 'POST',
				data: {
					ids: selectedCampaigns.map(x => x.ID)
				},
			})
				.then(() => {
					setUnpublishedCampaigns(_difference(unpublishedCampaigns, selectedCampaigns))
					setSelectedCampaigns([]);
				})
				.catch(error => {
					if (error.response.data.message) {
						error.response.data.message +=
							' - You should refresh your browser to refetch the list of unpublished campaigns';
					}
					showToastForFetchError(error);
				})
		},
	});

	function deleteCampaign(campaignId) {
		if (window.confirm(`Are you sure you want to delete campaign #${campaignId}?`)) {
			setCampaignIdToDelete(campaignId);
			runDeleteSingle();
		}
	}

	function deleteSelected() {
		if (window.confirm('Are you sure you want to delete multiple campaigns?')) {
			runDeleteSelected();
		}
	}

	function markSelectedCampaignsPublished() {
		// For convenience to the user, if they only published one campaign, automatically select the next one for them.
		const wasOnlyOneCampaignSelected = selectedCampaigns.length === 1;
		let indexThatWasSelectedFromUnpublished = null;
		if (wasOnlyOneCampaignSelected) {
			const campaignId = selectedCampaigns[0].ID;
			indexThatWasSelectedFromUnpublished = unpublishedCampaigns.findIndex(x => x.ID === campaignId);
		}

		setPublishedCampaigns(publishedCampaigns.concat(selectedCampaigns));
		const nextUnpublishedCampaigns = unpublishedCampaigns.filter(x => !selectedCampaigns.find(y => y.ID === x.ID))
		setUnpublishedCampaigns(nextUnpublishedCampaigns)

		if (wasOnlyOneCampaignSelected && nextUnpublishedCampaigns.length) {
			// Because we only deleted one, the index we want to use remains the same, unless it was the last one.
			let indexToUseFromUnpublished = indexThatWasSelectedFromUnpublished;
			if (indexThatWasSelectedFromUnpublished > nextUnpublishedCampaigns.length - 1) {
				indexToUseFromUnpublished = nextUnpublishedCampaigns.length - 1;
			}
			setSelectedCampaigns([nextUnpublishedCampaigns[indexToUseFromUnpublished]]);
		} else {
			setSelectedCampaigns([]);
		}
	}

	function selectAllOrNone() {
		if (selectedCampaigns.length === unpublishedCampaigns.length) {
			setSelectedCampaigns([]);
		} else {
			setSelectedCampaigns(unpublishedCampaigns)
		}
	}

	return <div className="owl-plus">
		<div>
			<div><strong>Unpublished Campaigns ({unpublishedCampaigns.length})</strong></div>
			<div style={{ marginTop: '10px' }}>
				<select value={selectedBulkAction} onChange={x => setSelectedBulkAction(x.target.value)}>
					<option value="bulk_actions" disabled={true}>Bulk actions</option>
					<option value="delete">Delete campaign(s)</option>
				</select>
				<button
					style={{ marginLeft: '5px' }}
					className="button action"
					onClick={deleteSelected}
					disabled={selectedBulkAction !== 'delete' || !selectedCampaigns.length}
				>Apply</button>
			</div>
			<div style={{ maxHeight: '200px', overflowY: 'auto', marginTop: '10px' }}>
				<table className="widefat striped">
					<thead className="sticky">
					<tr>
						<th>
							Select (<a
								style={{ cursor: 'pointer' }}
								className="btn link"
								onClick={selectAllOrNone}
							>all/none</a>)
						</th>
						<th>Campaign ID</th>
						<th>Email Subject</th>
						<th>Contact</th>
					</tr>
					</thead>
					<tbody>
					{!unpublishedCampaigns.length && <tr>
						<td colSpan={4}>Yay! No campaigns left to publish!</td>
					</tr>}
					{unpublishedCampaigns.map(x => {
						return <tr key={x.ID}>
							<td>
								<input
									type="checkbox"
									checked={!!selectedCampaigns.find(y => x.ID === y.ID)}
									onChange={() => toggleSelectCampaign(x.ID)}
								/>
							</td>
							<td>
								{x.ID} <a href={x.view_link} target="_blank">view</a>,
								{' '}
								<a href={x.edit_link} target="_blank">edit</a>,
								{' '}
								<a
									style={{ cursor: 'pointer' }}
									className="btn link"
									onClick={() => deleteCampaign(x.ID)}
								>delete</a>
								{((deleteSingleIsLoading && x.ID === campaignIdToDelete)
									|| (deleteSelectedIsLoading && !!selectedCampaigns.find(y => x.ID === y.ID)))
									&& <>{' '}<i className="fa fa-spinner fa-spin" /></>}
							</td>
							<td>{x.custom_subject}</td>
							<td>
								<a href={x.contact.link} target="_blank">{x.contact.fname} {x.contact.lname}</a>
							</td>
						</tr>;
					})}
					</tbody>
				</table>
			</div>
		</div>
		<div>
			<div><strong>Published Campaigns ({publishedCampaigns.length})</strong></div>
			<div style={{ maxHeight: '200px', overflowY: 'auto' }}>
				<table className="widefat striped">
					<thead>
					<tr>
						<th>Campaign ID</th>
						<th>Email Subject</th>
						<th>Contact</th>
					</tr>
					</thead>
					<tbody>
					{publishedCampaigns.length === 0 && <tr>
						<td colSpan={3}>None yet</td>
					</tr>}
					{publishedCampaigns.map(x => {
						return <tr key={x.ID}>
							<td>
								{x.ID} (<a href={x.view_link} target="_blank">view</a>,
								{' '}
								<a href={x.edit_link} target="_blank">edit</a>)
							</td>
							<td>{x.custom_subject}</td>
							<td>{x.contact.fname} {x.contact.lname}</td>
						</tr>
					})}
					</tbody>
				</table>
			</div>
		</div>
		<BulkCampaignPublisher
			googleMapsApiKey={googleMapsApiKey}
			campaigns={selectedCampaigns}
			subjectMapIcon={subjectMapIcon}
			endpoint={endpoint}
			markSelectedCampaignsPublished={markSelectedCampaignsPublished}
			toggleSelectCampaign={toggleSelectCampaign}
		/>
	</div>;
}

export default BulkCampaignAutomator;
