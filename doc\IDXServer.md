# IDX Server

The [IDX](https://en.wikipedia.org/wiki/Internet_Data_Exchange) server works in conjunction with the [IDX Plugin](IDXPlugin.md) to allow real-estate agent websites to search and display real estate listings from their MLS.

## Overview

The WordPress [IDX Plugin](IDXPlugin.md) depends heavily on the IDX server for:

* configuration
* property search
* property details

The IDX server is being re-written in node.js + CoffeeScript.  Currently, the [Property Search](PropertySearch.md) functionality is implemented.  The rest of the functionality is still provided by the PHP-based code.

The [Old IDX Server](OldIDXServer.md) page provides a few details about the legacy PHP-based server.

## Technology

* [ExpressJS]()
* [IcedCoffeeScript]()
* [Apache Solr]()
* [MySQL]()

## Directories & Files

* `server`
    * `app`
        * `server.iced` - the main node.js application code, which sets up & runs the ExpressJS server
        * `api`
            * `search.iced` - contains the route/callback for handling the [Property Search](PropertySearch.md)
    * `config` - configuration files for [node-config](https://github.com/lorenwest/node-config) - `default.yaml`, `production.yaml`, `local.yaml`, etc. 
    * `lib` - CoffeeScript classes containing major pieces of functionality
        * `PropSearch.iced` - the main [IDX Search](IDXSearch.md) code
        * `QuickSearch.iced` - translates [Quick Search]() query parameters to Solr query strings
    * `log` - directory for logs in production
    * `models` - CoffeeScript classes for MySQL database tables - a bit of a hacky, minimal ORM
* `ubuntu`
    * `init` - Upstart scripts
        * `idx.conf` - service configuration for the IDX server

## Running

To run the IDX server, change into the `server/` directory and run `make`:

    cd server
    make

## Production

The IDX server is configured to run using an [Upstart]() script on the Amazon EC2 server in production.  To start/stop/restart it:

    sudo service idx {start|stop|restart}

## WordPress Debug

For any WordPress page that makes request to the IDX server, you may add `idxurls` as a query parameter to the URL, and it will show a translucent div near the top-right of the page, with the API URLs for the IDX requests.  Clicking any of these links will open a new tab & display the IDX search results JSON in your browser.

**Example**:

* [Ocotillo Homes in Chandler - Ocotillo Subdivisions - Search Chandler Homes](http://houseschandleraz.com/ocotillo-homes-in-chandler/?idxurls)

## New Relic

The IDX server is monitored for performance & errors by New Relic.  The application name is **IDX Server 2**.

* [IDX Server 2 - New Relic](https://rpm.newrelic.com/accounts/815479/applications/7301598)

### Custom Parameters

These are used by the IDX server to add custom parameters to each **Transaction** that is logged by New Relic.  The parameters include information about:

* WordPress URL - sent as the HTTP Referer header
* IDX account - company, email, member ID, office ID, username, full name
* Search query & results - page, number of results, query time, MLS, MLS class, Solr core, Solr query URL, sort parameters, limit/rows

In addition, each IDX query from the [IDX Plugin](IDXPlugin.md) sets several custom HTTP headers.   These headers are extracted by the IDX server code, and can be used to get information about the end-user's IP, location, and browser.

Some of the parameters added by this method:

* IP info - ASN name, city, country, region/state
* User Agent - user agent device, platform, OS, etc.

The code for this is inside the `trackClientInfo()` function inside `server.iced`.  

### Dashboard

A dashboard has been created using New Relic Insights.  It can be used to review the performance & query statistics for IDX searches.  It makes heavy use of the custom paramters described above that are added via the New Relic node.js API.

* [Insights: IDX Searches](https://insights.newrelic.com/accounts/815479/dashboards/72999)

## Testing

The `tools/idx-log-tool` script can be used to analyze the search URLs in the nginx IDX log files, to test the new IDX server by itself, or to test the IDX server against the old PHP-based version (for searches).

See [Property]

## npm modules

* [felixge/node-mysql](https://github.com/felixge/node-mysql/) - for accessing MySQL
