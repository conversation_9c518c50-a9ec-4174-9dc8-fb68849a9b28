jQuery(document).ready(function($) {
	
	$( '.add-log' ).on( 'click', function() {
		
		input = new Object;
		input.text = $( '#new-log-text' ).val();
		input.client_id = $( '#client_id' ).val();

		$.ajax ( {
			url : log.endpoint,
			type : 'post', 
			data : {
				action : 'new_log_ajax',
				input : input,
				log_nonce : log.nonce,
			},
			beforeSend: function() {
				$( '.new-log-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
				$('.new-log').removeClass('new-log');
			},
			success: function( response ) {
				$( '.new-log-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response.class );
				if( response.success ) {
					$( '#ifound-log-wrapper' ).prepend( response.log );
					$( '#new-log-text' ).val('').focus();
				}
			},
			dataType:'json'
		});
	})
	
});