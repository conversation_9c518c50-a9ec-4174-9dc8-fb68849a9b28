-- getprofo_wp1

CREATE TABLE IF NOT EXISTS wp_55_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_58_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_59_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_61_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_66_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_110_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_125_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_160_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_173_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_174_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_175_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_179_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_181_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_182_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_183_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_201_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_207_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_218_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_219_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_220_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_221_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_227_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_235_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_244_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_245_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_251_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_254_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_259_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_260_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_269_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_272_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_278_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_281_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_282_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_283_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_284_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_291_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_297_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_303_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_306_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_307_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_308_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_311_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_316_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_318_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_320_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_324_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_328_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_335_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_353_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_358_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_360_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_362_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_363_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_373_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_374_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_382_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_390_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_395_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_396_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_409_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_423_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_424_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_425_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_426_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_427_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_428_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_429_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_430_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_431_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_433_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_434_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_435_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_436_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_437_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_438_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_439_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_441_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_442_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_443_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- round 2
CREATE TABLE IF NOT EXISTS wp_5_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_9_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_11_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_13_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_15_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_18_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_19_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_24_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_25_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_26_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_27_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_28_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_30_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_32_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_42_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_50_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_63_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_64_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_70_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_71_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_73_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- round 3
insert wp_5_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_9_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_11_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_13_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_15_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_18_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_19_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_24_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_25_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_26_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_27_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_28_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_30_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_32_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_42_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_50_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_63_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_64_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_70_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_71_gf_addon_feed select * from wp_412_gf_addon_feed;
insert wp_73_gf_addon_feed select * from wp_412_gf_addon_feed;



-- tamsms_wp
CREATE TABLE IF NOT EXISTS wp_4_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_5_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_9_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_10_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_11_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_13_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_15_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_18_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_19_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_24_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_25_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_26_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_27_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_28_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_30_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_32_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_36_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_42_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_44_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_46_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_48_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_50_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_59_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_61_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_63_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_64_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_70_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_71_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_73_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_74_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_75_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_78_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_80_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_85_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_86_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_89_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_100_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_101_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_102_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_103_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_104_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_105_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE IF NOT EXISTS wp_106_gf_addon_feed (`id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT, `form_id` mediumint(8) unsigned NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT '1', `meta` longtext COLLATE utf8mb4_unicode_ci, `addon_slug` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL, PRIMARY KEY (`id`), KEY `addon_form` (`addon_slug`,`form_id`) ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
