<?php

/**
 * This class is used to fill in the SEO templates and clean the data from the database
 */
require_once __DIR__ . '/../custom_error_handler.php';
require_once("class-parse_seo.php");
require_once('IDXBase.php');

use Profound\CRM\Tracker;
use Profound\MLS\MlsClass;

/**
 * Main class for the ProFoundIDX
 */
class IDX extends IDXBase
{
	/**
	 * Record the start time of the request from within PHP
	 */
	public function __construct($configfile, $apis = array()) {
		$this->num_results = 0;
		parent::__construct($configfile, $apis);
	}

	/**
	 * Record the end time for the request and log to the DB
	 */
	public function __destruct() {
		$this->log_access();
	}

	private function log_access() {
		// Only log to our access_log if specified in the config file.
		if (isset($this->config['log']['log_access']) && $this->config['log']['log_access'] == true) {
			// Debug stuff could go here.
		} else {
			return;
		}

		$profiler = $this->db->getProfiler();

		# Fields that might be sent by the client
		$req_fields = array(
			'Hostname' => 'Hostname',
			'BrowserIP' => 'BrowserIP',
			'UserAgent' => 'UserAgent',
			'q' => 'PropID',
			'features' => 'Features',
			'words' => 'Words',
			'cat_name' => 'Category',
			'sort' => 'SortOrder',
			'city' => 'City',
			'price' => 'Price',
			'sqft' => 'Sqft',
			'bedroom' => 'Bedroom',
			'bathroom' => 'Bathroom',
			'zipcode' => 'ZipCode',
			'mlsnum' => 'PropID',
			'proptype' => 'PropType',
			'pp' => 'Page',
			'RequestURL' => 'RequestURL',
			'Referer' => 'Referer',
		);

		$data = array(
			'Created' => date('Y-m-d H:i:s'),
			'access_id' => $this->access_array['access_id'],
			'QueryType' => $this->query_type,
			'ClientIP' => $_SERVER['REMOTE_ADDR'],
			'QueryTime' => $profiler->getTotalElapsedSecs() * 1000,
			'NumQueries' => $profiler->getTotalNumQueries(),
			'NumResults' => $this->num_results,
		);
		$dir = $this->config['log']['dir'];

		// Fill in data for fields sent from the client
		foreach ($req_fields as $key => $name) {
			if (isset($data[$name])) continue;

			$data[$name] = isset($_REQUEST[$key]) ? $_REQUEST[$key] : '';
		}
		$this->db->insert('access_log', $data);

		// Fill in blank fields with '-' for logging purposes
		foreach ($data as $key => $val) {
			if ($val === '') $data[$key] = '-';
		}

		// Log to the file system as well
		$line = implode("\t", array_values($data)) . "\n";
		file_put_contents($dir . '/idx.log', $line, FILE_APPEND);
	}

	/**
	 * Authenticate the request based on the Account ID and API key that were sent in the request
	 */
	public function authenticate() {
		require_once('class-auth.php');
		$auth = new Auth();
		$this->access_array = $auth->authenticate($_REQUEST);

		$this->mls = (object) $this->config[$this->access_array['mls']];
		$this->mlsname = $this->access_array['mls'];

		$this->overrideConfig();
	}

	/**
	 * Previously, we'd only ever use one MLS "class" at a time (e.g. residential) instead of allowing
	 * for multiple (e.g. residential, commercial, etc). This is now where we override the config
	 * in the appropriate place to allow for the switch of class.
	 */
	public function overrideConfig() {
		$mls_class = isset($_REQUEST['mls_class']) ? $_REQUEST['mls_class'] : "res";
		$this->mls->mls_class = $mls_class;
		$this->mls->db_table = $this->getDbTableNameByMlsClass($this->mls->mls_class);
	}

	// $mls_class is generic class name like 'res', 'rentals', etc.
	public function getDbTableNameByMlsClass($mls_class) {
		$db_table_prefix = $this->mls->db_table_prefix;
		$mls_class_name = "mls_class_$mls_class";
		$db_table_suffix = $this->mls->$mls_class_name;
		return "{$db_table_prefix}{$db_table_suffix}";
	}

	// $mls_class is generic class name like 'res', 'rentals', etc.
	public function getFieldsTableName($mls, $mls_class) {
		$db_table_prefix = "{$mls}_fields_";
		$mls_class_name = "mls_class_$mls_class";
		$db_table_suffix = $this->mls->$mls_class_name;
		return "{$db_table_prefix}{$db_table_suffix}";
	}

	/**
	 * Get a set of properties and the preferred image ID from the database
	 * @param $fstr Fields to return
	 * @param $where SQL WHERE clause used to select properties
	 * @return array List of properties that were retrieved
	 */
	private function fetchListings($fstr, $where) {
		$tblname = $this->mls->db_table;
		$listkey = $this->mls->listing_id;
		$imgtable = $this->access_array['mls'] . '_images';

		# FIXME: this is a complete hack due to duplicate field in image table for TREND
		if ($this->access_array['mls'] == 'trendmls') $fstr = preg_replace('/ListingID/', "$tblname.ListingID", $fstr);

		$sql = "
			SELECT
				$fstr
			FROM
				$tblname
			WHERE
				$where
		";
		try {
			return $this->db->fetchAll($sql);
		} catch (\Exception $ex) {
			\D::elog("access_id: " . $this->access_array['access_id'], "-");
			\D::elog($sql);
			throw $ex;
		}
	}

	/**
	 * Get a list of properties
	 *
	 * These listings are used to fill in the Featured Listings widget in the WordPress plugin.
	 *
	 * @return  array List of properties
	 */
	public function getFeaturedListings() {
		$this->authenticate();
		$this->query_type = 'FeatList';

		# TODO: save small thumbnails for the properties and send the URL for those images instead?

		$imgtable = $this->access_array['mls'] . '_images';

		// Get the URI mapping for the this user
		$map = $this->getMetaPropUrl();

		$fmap = $this->getFieldMapping();

		// Get the list of fields to fetch from the DB: combine default list with fields from URL map
		$fnames = array('ListingKey', 'ListPrice', 'ListingID', 'StreetNumber', 'StreetDirPrefix', 'StreetName', 'UnitNumber', 'StreetSuffix', 'City', 'State', 'PostalCode');

		$fields = $this->mapFieldNames($fnames);

		preg_match_all('/{(.*?)}/', $map, $matches);
		$fields = array_unique(array_merge($fields, $this->mapFieldNames($matches[1])));
		$fstr = "DISTINCT " . implode(', ', $fields);

		if (!$_REQUEST['listids']) {
			return array();
		}
		// Replace commas and spaces by single spaces.
		$list_ids = preg_replace('/[\s,]+/', ' ', trim($_REQUEST['listids']));
		// Remove empty entries.
		// From: http://us1.php.net/explode
		// Quoting manual page "If the callback function is not supplied, array_filter() will remove all the entries of input that are equal to FALSE.".
		$ids = array_filter(explode(' ', $list_ids));
		$listings = $this->fetchListings($fstr, "{$fmap->ListingID} IN (\"" . implode('","', $ids) . "\")");

		$listings = $this->reverseMap($listings, true);

		// TODO: A bit of a hack. Re-order the listings to match list entered in WP Admin.  This is ugly.
		foreach ($listings as $listing) {
			$listmap[$listing['ListingID']]	= $listing;
		}
		$list2 = array();
		foreach ($ids as $id) {
			if (isset($listmap[$id])) $list2[] = $listmap[$id];
		}
		$listings = $list2;

		foreach ($listings as &$prop) {
			$prop['img_url'] = $this->getPreferredImage($prop['ListingKey']);
		}

		$seo_obj = new SEO_Parse();

		foreach ($listings as &$prop) {
			// Clean the fields
			foreach ($prop as $key => $val) {
				$prop[$key] = $seo_obj->cleanField($key, $val);
			}

			unset($prop['ListingKey']);
			// Add the property URL to each of the listings
			$prop['uri'] = $seo_obj->parse_urlmap($prop, $map . '-idx-' . $prop['ListingID']);
		}

		return $listings;
	}

	/**
	 *  Get the URL map for the customer that is used to generate the Property Details URL
	 * @return string URL map for this customer
	 */
	public function getMetaPropUrl() {
		return $this->access_array['meta_prop_url'];
	}

	// Expected to be used after listing has been mapped, i.e. it has the key ListStatus, not e.g. LIST_15.
	private function isListingClosed($prop_array) {
		return in_array($prop_array['ListStatus'], ['Closed', 'Sold'], true);
	}

	// Fetch the listing info from the database. From just the MLS ID, we don't know what
	// table it's in the database, i.e. we don't know what MLS class the ID is for.
	// So we'll search all of them. This is not the fastest solution (see some alternatives at
	// https://taskbump.com/case-details/52004cff7e779a6268001236#52c2734c99ea5155ce000004
	// but it's pretty fast and will probably suffice until we get much more traffic and are
	// using many MLS classes (at the moment we're using 2, gearing for 3).
	// Returns the listing info array.
	// Returns the MLS class found via $mls_class.
	protected function getListingInfo($mls_id, $where, &$mls_class) {
		$mls_classes = MlsClass::getGenericMlsClassesForMls($this->access_array['mls']);
		foreach ($mls_classes as $current_class) {
			$table = $this->getDbTableNameByMlsClass($current_class);
			$listing_id_field_name = $this->getListingIDFieldName();

			$sql = sprintf("SELECT * FROM %s WHERE %s = \"%s\" $where", $table, $listing_id_field_name, $mls_id);

			// Record the SQL query in New Relic
			if (extension_loaded('newrelic')) {
				newrelic_add_custom_parameter("idx_type", 'Property Details');
				newrelic_add_custom_parameter("mls_id", $mls_id);
				newrelic_add_custom_parameter("prop_sql", $sql);
			}

			$prop = $this->db->fetchRow($sql);
			if ($prop) {
				$mls_class = $current_class;
				if ($this->isResoWebApi() && ($prop['InternetAddressDisplayYN'] ?? 0) === 0) {
					return null;
				}
				return $prop;
			}
		}
		return null;
	}

	public function getFieldMapping($mls_class = 'res') {
		return parent::getFieldMapping($this->mls->mls_class);
	}

	private function isResoWebApi() {
		// In the following line, the '1', as a string, represents the line "is_reso_web_api = true" in the .ini file.
		return isset($this->mls->is_reso_web_api) && $this->mls->is_reso_web_api === '1';
	}

	public function propDetails($mls_id = null) {
		require_once("class-auth.php");
		require_once("class-query_db.php");
		require_once("class-parse_seo.php");
		require_once("class-sanitizer.php");

		$this->query_type = 'Details';

		// TODO: replace class-query_db.php so that we can use the zend DB adapter here instead

		$query_info = $_REQUEST;

		// Process The Form Cleaner (you can't trust anyone these days)
		$sanitizer_obj = new sanitizer;
		$query_info = $sanitizer_obj->cleandata($query_info); //1.7

		$mls_id = $mls_id ?: $query_info['q'];

		$seo_obj = new SEO_Parse();

		// Get authorization or die
		$this->authenticate($query_info);

		// Get the field name mapping from the DB
		$map = $this->getFieldMapping();
		$this->map = $map;

		// Remove AddressNoExport and UCB properties if applicable
		$where = '';
		$this->limitProperties($where);

		$prop_array = $this->getListingInfo($mls_id, $where, $mls_class);
		$this->mls->mls_class = $mls_class;

		// If we didn't find the property, assume that the listing has expired
		// Reminder: Saved Property Alerts rely on this value. So if we change it here, change it there.
		if (!$prop_array) {
			return array('expired' => true);
		}

		$this->num_results = 1;

		if($this->access_array['mls'] == "mredil")
			$img_array = $this->getImagePathsList($prop_array[$this->mls->listing_id], $table = $this->getDbTableNameByMlsClass($mls_class));
		else
			$img_array = $this->getImagePathsList($prop_array[$this->mls->listing_id]);
		$prop_array['images'] = $img_array;

		unset($prop_array['property_id']);
		unset($prop_array['property_enterdate']);

		// For RESO Web API MLSs:
		// Replace all lookup fields with official values.
		// If the field isn't a lookup, but its name ends in YN, it's a boolean. Return 'Yes' or 'No'.
		// NAAR Spark has some special handling, mentioned inline below.
		if ($this->isResoWebApi()) {
			$fieldsTableName = $this->getFieldsTableName($this->access_array['mls'], $mls_class);
			$sql = "SELECT LookupName, SystemName, Interpretation FROM {$fieldsTableName}";
			$results = $this->db->fetchAll($sql);
			$fieldsBySystemName = [];
			foreach ($results as $result) {
				$fieldsBySystemName[$result['SystemName']] = $result;
			}
			$sql = "SELECT LookupName, Value, LongValue FROM property_lookup_opts WHERE mls = ?";
			$results = $this->db->fetchAll($sql, $this->access_array['mls']);
			$propLookupOptsByLookupName = [];
			foreach ($results as $result) {
				$propLookupOptsByLookupName[$result['LookupName']] = $propLookupOptsByLookupName[$result['LookupName']] ?? [];
				$propLookupOptsByLookupName[$result['LookupName']][$result['Value']] = $result['LongValue'];
			}

			foreach ($prop_array as $key => $value) {
				$lookup = $fieldsBySystemName[$key] ?? null;
				// NAAR Spark does not handle their lookup stuff properly, it seems. They have lookups, but they don't
				// seem to use them. It's confusing. For example, I'm looking at listing 191152, with LaundryFeatures
				// value of "Laundry Room". There are 12 lookups for LaundryFeatures, none of them are 'Laundry Room'.
				// On the same listing, the InteriorFeatures has two bum values: 'Dual Sink' and 'Shower' that aren't in
				// the lookups.
				if ($this->mlsname !== 'naar_spark' && $lookup
					&& ($lookup['Interpretation'] === 'Lookup' || $lookup['Interpretation'] === 'LookupMulti')
				) {
					$splitValues = explode(',', $value);
					$replacementValues = [];
					foreach ($splitValues as $splitValue) {
						if ($splitValue === '' || preg_match('/^s+$/', $splitValue)) {
							continue;
						}
						// It is possible for invalid values to enter the MLS system. Ignore them here to silence the logs.
						$replacementValue = null;
						if (isset($propLookupOptsByLookupName[$lookup['LookupName']][$splitValue])) {
							$replacementValue = $propLookupOptsByLookupName[$lookup['LookupName']][$splitValue];
						}
						$replacementValues[] = $replacementValue;
					}
					$prop_array[$key] = implode(', ', $replacementValues);
				} else {
					$lastTwoLettersOfFieldName = substr($key, -2);
					if ($lastTwoLettersOfFieldName === 'YN') {
						if (in_array($prop_array[$key], ['1', 1], true)) {
							$prop_array[$key] = 'Yes';
						} else if (in_array($prop_array[$key], ['0', 0], true)) {
							$prop_array[$key] = 'No';
						} else {
							// Do nothing. At this point, it's not a '0' or '1', meaning it's probably some other
							// intentional value by the MLS.
						}
					}
				}
			}
		}

		// Map the key names from the DB to the standard keys we've defined
		// We keep a copy of the "raw" listing data for use later, e.g. if we want the price, unadorned with a dollar sign and commas.
		$raw_listing_data = $prop_array;
		$prop_array = $this->reverseMap($prop_array);

		// Fill in the location coordinates if missing
		$prop_array = $this->maybeGeocode($prop_array);

		// This is data meant to be used by machines, as opposed to displayed
		// to humans. It will be useful to allow downstream programs to use
		// the data. These fields are hand-picked according to what clients
		// request and what seems generally useful. To get this list, I spit
		// out all fields before and after the cleanArray() function and then
		// diffed to see what had changed. Basically I'm only interested in
		// the numbers.
		// Reminder: I couldn't believe it, but all values from mysql are
		// returned as strings! So we must cast thems ourselves to make them
		// more useful.
		$includeMachineData = false;
		$machineData = array();
		$optionsJson = @$_REQUEST['options'];
		// Try to get aggregate data.
		try {
			$options = json_decode($optionsJson, true);
			if (isset($options['include_machine_data']) && $options['include_machine_data']) {
				$includeMachineData = true;
				$fieldsForMachineData = array(
					'ModificationTimestamp' => array('type' => 'string'),
					'ListPrice' => array('type' => 'float'),
					'SquareFeet' => array('type' => 'float'),
					'Bathrooms' => array('type' => 'float'),
					'TotalTaxes' => array('type' => 'float'),
					'LotSquareFeet' => array('type' => 'float'),
					'HOAFee' => array('type' => 'float'),
				);
				foreach ($fieldsForMachineData as $fieldName => $meta) {
					$value = @$prop_array[$fieldName];
					// Sometimes certain fields won't exist for different
					// MLSs, etc, e.g. TotalTaxes.
					if (!$value) {
						continue;
					}
					switch ($meta['type']) {
						case 'string':
							// Do nothing
							break;
						case 'float':
							$value = floatval($value);
							break;
					}
					$machineData[$fieldName] = $value;
				}
			}
		} catch (\Exception $ex) {
			// Bail silently.
		}
		if ($this->isResoWebApi()) {
			$modificationTimestampZ = $prop_array['ModificationTimestamp'] . 'Z';
			$prop_array['ModificationTimestampZ'] = str_replace(' ', 'T', $modificationTimestampZ);
		}
		$prop_array = $seo_obj->cleanArray($prop_array);


		if (@$prop_array['AddressExportAllowed'] == "N") {
			$street_fields = ["UnitNumber", "CrossStreet", "StreetAddress",
				"StreetDirPrefix", "StreetDirSuffix",
				"StreetNumber", "StreetNumberNumeric",
				"StreetName", "StreetSuffix", "UnitNumber"];
			foreach ($street_fields as $street_field)
				$prop_array[$street_field] = "";
		}

		// Case 1969: Formatted address based on url set in rails admin
		$address_tokens = explode(" ", $this->access_array['meta_address']);
		$property_address = "";
		foreach ($address_tokens as $token) {
			$token = str_replace(array("{", "}"), "", $token);
			$value = isset($prop_array[$token]) ? $prop_array[$token] : '';
			//	Should be all caps
			if (in_array($token, array("State", "StreetDirPrefix"))) {
				$property_address .= strtoupper($value);
			} else {
				//	title case
				$property_address .= ucfirst($value);
			}
			$property_address .= " ";
		}
		// remove trailing space
		$property_address = rtrim($property_address, " ");
		$prop_array['PropertyAddress'] = $property_address;

		// Handle broker attribution
		// UPDATE to the below: Now we are using Bright via RETS, and it's different, as I'll explain in the Bright
		// section.
		//
		// As of this writing, broker attribution is done a certain way for all of our non-RESO Web API MLSs. It so
		// happens that all of them are on Flexmls; I'm not sure if it would work this same way if it were another
		// vendor. I believe that the RESO Web API way of doing the attribution is to have it on the Property resource
		// only, but I'm basing this only on Realtracs. Trestle doesn't have the field in any resource. Anyway, for
		// Realtracs, if the value is on the listing, then there is no need to fall back to look at other resources.
		$attributionMapName = 'AttributionContact';
		if ($this->mlsname === 'brightmls') {
			$office_key_fieldname = $this->mls->office_id;
			$office_key = $raw_listing_data['ListOfficeKey'];
			$office_attribution_contact_fieldname = $this->mls->office_attribution_contact;
			$office_table_name = "{$this->mlsname}_{$this->mls->office_table_infix}_{$this->mls->office_resource}";
			$sql_office = "SELECT $office_attribution_contact_fieldname FROM $office_table_name WHERE
						$office_key_fieldname = '$office_key'";
			$office_attribution = $this->db->fetchOne($sql_office);
			$prop_array[$attributionMapName] = $office_attribution;
			if ($prop_array[$attributionMapName] && gettype($prop_array[$attributionMapName]) === 'string') {
				// Let's format it to look like a phone number. (In the template we use for attribution, we're also
				// referencing ListOfficeName.)
				$phone_number = $prop_array[$attributionMapName];
				if (strlen($phone_number) === 10) {
					$phone_number = substr($phone_number, 0, 3) . '-'
						. substr($phone_number, 3, 3) . '-'
						. substr($phone_number, 6);
					$prop_array[$attributionMapName] = $phone_number;
				}

			}
		} else if (!$this->isResoWebApi()) {
			if (!isset($prop_array[$attributionMapName]) || !$prop_array[$attributionMapName]) {
				$agent_id = $prop_array['ListAgentID'];
				$agent_attribution_contact_fieldname = $this->mls->agent_attribution_contact;
				$agent_table_name = "{$this->mlsname}_{$this->mls->agent_table_infix}_{$this->mls->agent_resource}";
				$agent_short_id_fieldname = $this->mls->agent_short_id;
				$sql_agent = "SELECT $agent_attribution_contact_fieldname FROM $agent_table_name WHERE
					$agent_short_id_fieldname = '$agent_id'";
				$agent_attribution = $this->db->fetchOne($sql_agent);
				if ($agent_attribution) {
					$prop_array[$attributionMapName] = $agent_attribution;
				} else {
					$office_id = $prop_array['ListOfficeID'];
					$office_attribution_contact_fieldname = $this->mls->office_attribution_contact;
					$office_table_name = "{$this->mlsname}_{$this->mls->office_table_infix}_{$this->mls->office_resource}";
					$office_short_id_fieldname = $this->mls->office_short_id;
					$sql_office = "SELECT $office_attribution_contact_fieldname FROM $office_table_name WHERE
						$office_short_id_fieldname = '$office_id'";
					$office_attribution = $this->db->fetchOne($sql_office);
					$prop_array[$attributionMapName] = $office_attribution;
				}
			}
		}

		// Handle buyer broker compensation
		// In Trestle, there is a field BuyerAgencyCompensation, which is already formatted, e.g. $X or X%.
		// In CRMLS, the percentages have 3 decimals, but I'm not going to format (e.g.) 2.500 to 2.5% unless someone
		// complains, I'll just show it as is.
		// In Realtracs, it's already formatted if it's a dollar amount, but not if it's a percentage. Brilliant.
		// In Bright, there is $, %, % of Gross, % of Base, and no type, which means just use the
		// BuyerBrokerCompensation.
		if (isset($prop_array['BuyerBrokerCompensation'])) {
			$percentType = '%';
			$dollarType = '$';
			if ($this->access_array['mls'] === 'brightmls') {
				$comp_type = $prop_array['BuyerBrokerCompensationType'];
				if ($comp_type) {
					if ($comp_type === $dollarType) {
						$prop_array['BuyerBrokerCompensation'] = '$' . $prop_array['BuyerBrokerCompensation'];
					} else {
						$prop_array['BuyerBrokerCompensation'] .= $comp_type;
					}
				} else {
					// Don't need to do anything; take BuyerBrokerCompensation as is.
				}
			} else {
				// We only handle $ vs % here for CRMLS, although they technically have Mexican Pesos, "other", etc.
				// Oh well.
				if ($this->access_array['mls'] === 'crmls') {
					// Item1 means percent for CRMLS.
					$percentType = 'Item1';
				}
				$comp = $prop_array['BuyerBrokerCompensation'];
				if ($comp && isset($prop_array['BuyerBrokerCompensationType'])) {
					$type = $prop_array['BuyerBrokerCompensationType'];
					if ($type === $percentType) {
						$prop_array['BuyerBrokerCompensation'] .= '%';
					} else if ($type === $dollarType && $this->mlsname !== 'realtracs') {
						$prop_array['BuyerBrokerCompensation'] = '$' . $prop_array['BuyerBrokerCompensation'];
					}
				}
			}
		}
		if (!isset($prop_array['BuyerBrokerCompensationDisclaimer'])
			|| !$prop_array['BuyerBrokerCompensationDisclaimer']
		) {
			$prop_array['BuyerBrokerCompensationDisclaimer'] = "The listing broker's offer of compensation is made"
				. " only to participants of the MLS where the listing is filed.";
		}

		// 1969: street address
		$address_tokens = explode(" ", $this->access_array['meta_street_address']);
		$street_address = "";
		foreach ($address_tokens as $token) {
			$token = str_replace(array("{", "}"), "", $token);
			$value = isset($prop_array[$token]) ? $prop_array[$token] : '';
			//	Should be all caps
			if (in_array($token, array("State", "StreetDirPrefix"))) {
				$street_address .= strtoupper($value);
			} else {
				//	title case
				$street_address .= ucfirst($value);
			}
			$street_address .= " ";
		}
		// remove trailing space
		$street_address = rtrim($street_address, " ");
		$prop_array['StreetAddress'] = $street_address;

		if ($includeMachineData) {
			$prop_array['machine_data'] = $machineData;
		}

		$prop_array['mls_class'] = $mls_class;

		// Limit images to the primary one, if listing is Closed
		if ($this->isListingClosed($prop_array)) {
			$prop_array['images'] = array_slice($img_array, 0, 1);
		}

		// Add the property URI based on mapping sent or by default
		$prop_array['uri'] = $seo_obj->parse_urlmap($prop_array, $this->access_array['meta_prop_url']).'-idx-'.$prop_array['ListingID'];

		// Insert the SEO info
		$seo_array['title'] = $seo_obj->parse_seomap($prop_array, $this->access_array['meta_prop_title']);
		$seo_array['h1'] = $seo_obj->parse_seomap($prop_array, $this->access_array['meta_prop_h1']);
		$seo_array['description'] = $seo_obj->parse_seomap($prop_array, $this->access_array['meta_prop_description']);
		$seo_array['keywords'] = $seo_obj->parse_seomap($prop_array, $this->access_array['meta_prop_keywords']);

		$prop_array['seo'] = $seo_array;

		// PDP template parts
		$pdp_fields = ['pdp_main_value', 'pdp_property_value', 'pdp_general_value', 'pdp_school_value', 'pdp_community_value', 'pdp_lot_value', 'pdp_rooms_value', 'pdp_location_value', 'pdp_features_value'];
		foreach ($pdp_fields as $field) {
			$strg = $this->access_array['meta_'.$field];
			// We decided we'll only allow virtual tour values that start with http, otherwise we set the value (used as
			// an iframe src) to empty string. However, we have a problem where we have JavaScript that doesn't handle
			// the empty string properly, and the real problem is that the JavaScript is not in our codebase but is
			// in the database. We did this to allow possible customization. Anywho, a longer more proper fix is
			// suggested in the following case, but this hack should do for now.
			// To be clear about the problem the empty string causes, it's not even something the user would notice,
			// it shows up as "Uncaught TypeError: Cannot read property 'src' of null".
			// 1) I hate having "known errors" in the console. Developers end up with alarm fatigue.
			// 2) I'm not sure if there'd be other downstream errors. I suspect not, but why risk it?
			// See https://ifoundagent.teamwork.com/#/tasks/20616559
			if ($field === 'pdp_property_value') {
				$strg = str_replace('(!e.src.includes("https', '(e && !e.src.includes("https', $strg);
			}
			// Complete fields
			foreach ($prop_array as $key => $value) {
				/**
				 * Fix TB 1982
				 * $prop_array['images'] is an array. We cannot evaluate it as a string.
				 */
				if ( is_array( $value ) || is_object( $value ) ) continue;
				if ($key === 'ListPrice' && $this->isListingClosed($prop_array)) {
					// TODO: We use isset to quiet the logs, but should really find out why it's undefined sometimes.
					$strg = str_replace('{' . $key . '}', isset($prop_array['ClosePrice']) ? $prop_array['ClosePrice'] : '', $strg);
				} else {
					$strg = str_replace('{' . $key . '}', $value, $strg);
				}
			}
			$pdp_array[$field] = $strg;
		}
		$prop_array['pdp_template'] = $pdp_array;

		$prop_array['last_updated'] = $this->getLastUpdated();

		// Add the youtube video ID, or set the value to null if a video doesn't exist.
		// TODO: check for global youtube id for each agent after i add that feature (!!!)

		$youtube_sql = "SELECT youtube_id FROM videos WHERE access_id=" . $this->access_array['access_id'] . " AND MLS_id='" . $mls_id . "'";
		$result = $this->db->fetchOne($youtube_sql);

		if ($result != null) {
			//take the specific video for the property
			$prop_array['youtube_id'] = $result;
		} else {
			//if there isn't one, look to see if the agent has a global video
			$global_youtube_sql = "SELECT access_youtube_id FROM access WHERE access_id=" . $this->access_array['access_id'];
			$global_result = $this->db->fetchOne($global_youtube_sql);
			$prop_array['youtube_id'] = $global_result; //ok to have a null value here
		}

		$demograph_array = null;
		if (is_array($demograph_array)) {
			foreach ($demograph_array as $id => $result) {
				$prop_array['demographics'] = $result;
			}
		}

		// Clean up the Demographics Data
		if (isset($prop_array['demographics'])) {
			foreach ($prop_array['demographics'] as $key => $value) {
				if (preg_match('/Population|Households/', $key)) {
					$prop_array['demographics'][$key] = number_format($value);
				} else if (preg_match('/Income|Value/', $key)) {
					$prop_array['demographics'][$key] = $this->to_dollars($value);
				}
			}
		}

		$m = &$this->mls;
		$open_house_sql = <<<EOT
			SELECT {$m->open_house_event_start}, {$m->open_house_event_end}
			FROM {$m->open_house_table}
			WHERE {$m->open_house_listing_id} = ?
			ORDER BY {$m->open_house_event_start}
			LIMIT 4
EOT;
		$open_houses = $this->db->fetchAll($open_house_sql, $raw_listing_data[$m->listing_id]);
		$prop_array['open_houses'] = array_map(function($x) use ($m) {
			$startDate = DateTime::createFromFormat($m->open_house_php_format, $x[$m->open_house_event_start]);
			$endDate = DateTime::createFromFormat($m->open_house_php_format, $x[$m->open_house_event_end]);
			if (isset($m->open_house_convert_to_timezone)) {
				$z = new DateTimeZone('UTC');
				$startDate = DateTime::createFromFormat($m->open_house_php_format, $x[$m->open_house_event_start], $z);
				$endDate = DateTime::createFromFormat($m->open_house_php_format, $x[$m->open_house_event_end], $z);
				$startDate->setTimezone(new DateTimeZone($m->open_house_convert_to_timezone));
				$endDate->setTimezone(new DateTimeZone($m->open_house_convert_to_timezone));
			}
			$humanFormatString = 'M j, g:i a';
			return [
				'StartDateString' => $startDate->format($humanFormatString),
				'EndDateString'   => $endDate->format($humanFormatString),
				'RangeDateString' => $startDate->format($humanFormatString) . ' - ' . $endDate->format('g:i a'),
			];
		}, $open_houses);

		(new Tracker())->viewListing($query_info, $prop_array);

		return $prop_array;
	}


	// TODO: should this be moved somewhere else?
	// Get the demographics
	public function getCurlData($c_url, $vars = NULL) {

		// use cURL to post data to the ProFound IDX Server
		$ch = curl_init($c_url);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 0);
		//curl_setopt($ch, CURLOPT_POSTFIELDS,http_build_Myquery($_REQUEST, '', '&'));
		curl_setopt($ch, CURLOPT_POSTFIELDS,$vars);

		if ($result=curl_exec ($ch)) {
			return json_decode($result, true);
		}
		curl_close($ch);

	}

	// TODO: should this be moved somewhere else?
	public function to_dollars($value) {
		return "$" . number_format($value);
	}


	/**
	 * Get the timestamp for when we last updated from the RETS service
	 * @return mixed
	 */
	protected function getLastUpdated() {
		$table = $this->getDbTableNameByMlsClass($this->mls->mls_class);
		$fmap = $this->getFieldMapping();
		$sql = "select {$fmap->ModificationTimestamp} from $table order by {$fmap->ModificationTimestamp} desc limit 1";
		return strtotime($this->db->fetchOne($sql));
	}

	/**
	 * Limit properties from the query results
	 *
	 * @param $sql Original SQL 'WHERE' string
	 */
	protected function limitProperties(&$sql, $options = array()) {
		$this->getMls()->getPropertyLimiter()->limitProperties($sql, $options);
	}

	protected function getLocalMlsClassFromGeneric($mls, $genericMlsClass) {
		switch ($mls) {
			case 'armls':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'B';
					case 'land':
						return 'C';
				}
			case 'glvarnv':
				switch ($genericMlsClass) {
					case 'res':
						return '1';
					case 'rentals':
						return '9';
					case 'land':
						return '5';
				}
			case 'mredil':
				switch ($genericMlsClass) {
					case 'res':
						return 'ResidentialProperty';
					case 'rentals':
						return 'RentalHome';
					case 'land':
						return 'LotsAndLand';
				}
			case 'paaraz':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'F';
					case 'land':
						return 'B';
				}
			case 'paaraz_mlg':
				switch ($genericMlsClass) {
					case 'res':
						return 'res';
					case 'rentals':
						return 'rentals';
					case 'land':
						return 'land';
				}
			case 'sdcrca':
				switch ($genericMlsClass) {
					case 'res':
						return 'RE_1';
					case 'rentals':
						return 'RT_4';
					case 'land':
						return 'LN_3';
				}
			case 'trendmls':
				switch ($genericMlsClass) {
					case 'res':
						return 'RES';
					case 'rentals':
						return 'RNT';
					case 'land':
						return 'LOT';
				}
			case 'brightmls':
				switch ($genericMlsClass) {
					case 'res':
						return 'RESI';
					case 'rentals':
						return 'RLSE';
					case 'land':
						return 'LAND';
				}
			case 'tarmlsaz':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'E';
					case 'land':
						return 'B';
				}
			case 'naar':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'B';
					case 'land':
						return 'C';
				}
			case 'cabor':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'B';
					case 'land':
						return 'C';
				}
			case 'wmar':
				switch ($genericMlsClass) {
					case 'res':
						return 'A';
					case 'rentals':
						return 'D';
					case 'land':
						return 'B';
				}
			case 'crmls':
				switch ($genericMlsClass) {
					case 'res':
						return 'res';
					case 'rentals':
						return 'rentals';
					case 'land':
						return 'land';
				}
			case 'recolorado':
				switch ($genericMlsClass) {
					case 'res':
						return 'RESI';
					case 'rentals':
						return 'INCOME';
					case 'land':
						return 'LAND';
				}
			case 'recolorado_mlg':
				switch ($genericMlsClass) {
					case 'res':
						return 'res';
					case 'rentals':
						return 'rentals';
					case 'land':
						return 'land';
				}
			case 'wardex':
				switch ($genericMlsClass) {
					case 'res':
						return 'ResidentialProperty';
					case 'rentals':
						return 'Rental';
					case 'land':
						return 'LotsAndLand';
				}
			case 'realtracs':
				switch ($genericMlsClass) {
					case 'res':
						return 'Residential';
					case 'rentals':
						return 'ResidentialLease';
					case 'land':
						return 'Land';
				}
			default:
				return $genericMlsClass;
		}
	}
}
