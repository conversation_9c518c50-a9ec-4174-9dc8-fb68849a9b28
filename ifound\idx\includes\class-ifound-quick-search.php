<?
/**
 * iFoundQuickSearch class
 *
 * @since 5.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundQuickSearch extends iFoundIdx {
	
	private $settings;
	private $criteria;

	/**
	 * init iFoundQuickSearch class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
		$class = __CLASS__;
		return new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 5.0.0
	 */
	 
	public function __construct() {

		$this->settings = $this->settings();
		$this->mappings = $this->mappings();
		$this->criteria = $this->lookups_to_criteria();
		
		add_action( 'ifound_quick_search_select', array( $this, 'select' ), 10, 1 );
		add_action( 'ifound_quick_search_number', array( $this, 'number' ), 10, 1 );
		add_action( 'ifound_quick_search_text', array( $this, 'text' ), 10, 1 );
		add_action( 'ifound_quick_search_multibox', array( $this, 'multibox' ), 10, 1 );
		add_action( 'ifound_quick_search_checkbox', array( $this, 'checkbox' ), 10, 1 );

	}

	public function settings() {
	    $agent_id = apply_filters('ifound_get_agent_id', null);
	    $option_name = 'ifound_quick_search_criteria' . $agent_id;
		return (object) get_option($option_name);
	}

	public function body() {
		wp_enqueue_script( 'quick_search_js' );
		wp_enqueue_script('ifound_spa_js');

		$fields = iFoundQuickSearchAdmin::new_hookless()->get_fields();
		?>

		<div class="ifound-quick-search">

			<div class="widgets-wrap">
		
				<h3 class="widget-title ifound-widget-title"><? _e( 'Quick Search', 'ifound' ); ?></h3>
				
				<div class="quick-search">
				
					<form id="quick-search-form"><?
		
						foreach( $this->settings as $easy_name ) {

							$action = 'ifound_quick_search_' . $fields->$easy_name;
		
							do_action( $action, $easy_name );
					
						} ?>
						
						<div class="quick-search-button-wrap">

							<div class="button" id="quick-search-button"><? _e( 'Search', 'ifound' ); ?></div>

						</div>
						
					</form>
					
				</div>
				
				<div class="ifound-more-options">
					
					<a href="<? echo site_url( '/listing-search/' ); ?>"><? _e( 'More Options', 'ifound' ); ?></a>
					
				</div>
				
			</div>
			
		</div><?

	} 

	/**
	 * Select
	 *
	 * @since 5.0.0
	 * 
	 * @param string $easy_name The name for this input.
	 */
	
	public function select( $easy_name ) { 
 
		$values 	= $this->criteria->$easy_name->values; 
		$label 		= $this->mappings->$easy_name->display_name;

		// Let's check if the field is one of the text box autofill fields
		$isAutofill = strpos($easy_name, 'city') !== FALSE
				|| strpos($easy_name, 'school_district') !== FALSE
				|| strpos($easy_name, 'zip') !== FALSE;
 
		if($isAutofill) {
			$label = $this->mappings->$easy_name->display_name; ?>

			<div style="position:relative;" class="number-wrap number-wrap-<? echo $easy_name; ?>">
				<input type="text" name="<? echo $easy_name; ?>" style="width:100%;" class="ifound-text-input number-input number-input-<? echo $easy_name; ?>" placeholder="<? echo $label; ?>" autocomplete="off">
			</div> <?

			if($values === NULL) {
				$mls_associations_origin = $this->get_config()['mls_associations_origin'];
				$response = wp_remote_get($mls_associations_origin . iFoundIDX::mls_name() . '/');
				$json = json_decode($response['body'], true);
				$values = $json[$easy_name];
			}

			$js_var_name = 'no_var_error';
			switch($easy_name) {
				case 'city': $js_var_name = 'cities';
				break;
				case 'school_district': $js_var_name = 'sDistricts';
				break;
				case 'zip': $js_var_name = 'zips';
			}


			if ( is_array( $values ) ) {
				echo '<script>var ' . $js_var_name . ' = [';
				$script_string = '';
				foreach( $values as $value ) {
					if( ! empty( $value ) ) {

						// delete extra zip codes from armls
						if($easy_name === 'zip' && iFoundIDX::mls_name() === 'armls') {
							if(substr($value, 0, 1) !== '8') continue;
						}

						$script_string .= '"' . $value . '", ';
					}
				}
				echo $script_string . '];</script>';
			}
		} else {
			if ($easy_name === 'prop_type_mapped') {
				$choices = iFoundIdx::new_hookless()->get_prop_type_mapped();
				$values = iFoundIdx::new_hookless()->get_agent_filtered_prop_type_mapped_labels($choices);
			}
			$this->output_select($easy_name, $label, $values);
		}
	}

	private function output_select($easy_name, $label, $values) {
		?>
		<div class="select-wrap <? echo $easy_name; ?>">
			<select name="<? echo $easy_name; ?>" class="ifound-select-input ifound-select_<? echo $easy_name; ?>">
				<option value=""><? _e( $label, 'ifound' ); ?></option><?
				if ( is_array( $values ) ) {
					$values = $this->make_value_to_label_mapping($values, $easy_name);
					foreach( $values as $value => $label ) {
						if( ! empty( $value ) ) { ?>
							<option value="<? echo $value; ?>"><? _e( $label, 'ifound' ); ?></option><?
						}
					}
				} ?>
			</select>
		</div>
		<?
	}

	/**
	 * Number
	 *
	 * @since 5.0.0
	 * 
	 * @param string $easy_name The name for this input.
	 */
	
	public function number( $easy_name ) { 
  
		$label = $this->mappings->$easy_name->display_name; ?>

		<div class="number-wrap number-wrap-<? echo $easy_name; ?>">

			<input type="text" name="<? echo $easy_name; ?>_min" class="ifound-text-input number-input number-input-<? echo $easy_name; ?>" placeholder="Min <? echo $label; ?>">
			<input type="text" name="<? echo $easy_name; ?>_max" class="ifound-text-input number-input number-input-<? echo $easy_name; ?>" placeholder="Max <? echo $label; ?>">
			
		</div><?
		
	}

	/**
	 * Text
	 *
	 * @since 5.0.0
	 * 
	 * @param string $easy_name The name for this input.
	 */
	
	public function text( $easy_name ) { 
  
		$label = $this->mappings->$easy_name->display_name; ?>

		<div class="text-wrap text-wrap-<? echo $easy_name; ?>">

			<input type="text" name="<? echo $easy_name; ?>" class="ifound-text-input text-input-<? echo $easy_name; ?>" placeholder="<? echo $label; ?>">
			
		</div><?
		
	}

	public function multibox($easy_name) {
		$endpoint = rest_url(iFoundSearchController::$endpoint_namespace . iFoundSearchController::$endpoint_base
			. '/search');
		$icons = iFoundMap::new_hookless()->map_icons();
		?>
		<script>
			jQuery(document).ready(function($) {
				var domContainer = document.querySelector('.ifound-multibox-quicksearch-spa');
				var props = {
					endpoint: '<?= $endpoint ?>',
					icons: <?= json_encode($icons) ?>,
					isMultipleStateMls: <?= static::is_multiple_state_mls() ? 'true' : 'false' ?>,
				};
				window.ifound_spa.render_app('multibox_quicksearch', domContainer, props);
			});
		</script>
		<span class="ifound-multibox-quicksearch-spa"></span>
		<?
	}

	public function checkbox( $easy_name ) {
		$label = $this->mappings->$easy_name->display_name;
		?>
		<div class="checkbox-wrap checkbox-wrap-<?= $easy_name ?>">
			<? /* I put value=Y on the input because it's all I need for now. Might need it to be smarter later. */ ?>
			<input id="quick-search-<?= $easy_name ?>" type="checkbox" name="<?= $easy_name ?>" class="ifound-checkbox-input checkbox-input-<?= $easy_name ?>" value="Y">
			<label for="quick-search-<?= $easy_name ?>"><?= $label ?></label>
		</div>
		<?
	}
}
