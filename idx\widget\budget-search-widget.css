body { 
	background-color: #6077C7;
	font-family: arial;
}

.budget-search-widget {
	color: white;
}

.header {
	font-size: x-large;
	font-weight: bold;
	text-shadow: black 0.1em 0.1em 0.2em
}

.one-line {
	margin-top: 5px;
}

.header, .one-line {
	text-align: center;
}

.one-line div {
	display: inline-block;
}

.pfp-label {
	font-size: small;
}

.btn {
	-webkit-appearance: none;
	-webkit-border-image: none;
	-webkit-box-align: center;
	-webkit-box-shadow: rgba(255, 255, 255, 0.2) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.0470588) 0px 1px 2px 0px;
	-webkit-writing-mode: horizontal-tb;
	background-color: rgb(245, 245, 245);
	background-image: -webkit-linear-gradient(top, rgb(255, 255, 255), rgb(230, 230, 230));
	background-repeat: repeat-x;
	border-bottom-color: rgba(0, 0, 0, 0.247059);
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-collapse: separate;
	border-left-color: rgba(0, 0, 0, 0.14902);
	border-left-style: solid;
	border-left-width: 1px;
	border-right-color: rgba(0, 0, 0, 0.14902);
	border-right-style: solid;
	border-right-width: 1px;
	border-top-color: rgba(0, 0, 0, 0.14902);
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border-top-style: solid;
	border-top-width: 1px;
	box-shadow: rgba(255, 255, 255, 0.2) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.0470588) 0px 1px 2px 0px;
	box-sizing: border-box;
	color: rgb(51, 51, 51);
	cursor: pointer;
	display: inline-block;
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-weight: normal;
	height: 30px;
	letter-spacing: normal;
	line-height: 20px;
	margin-bottom: 0px;
	margin-left: 0px;
	margin-right: 0px;
	margin-top: -1px;
	padding-bottom: 4px;
	padding-left: 12px;
	padding-right: 12px;
	padding-top: 4px;
	text-align: center;
	text-indent: 0px;
	text-shadow: rgba(255, 255, 255, 0.74902) 0px 1px 1px;
	text-transform: none;
	vertical-align: middle;
	width: 71px;
	word-spacing: 0px;
	writing-mode: lr-tb;
}

.input-prepend {
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-size: 0px;
	height: 30px;
	line-height: 20px;
	margin-bottom: 5px;
	white-space: nowrap;
}

.add-on {
	-webkit-border-image: none;
	background-color: rgb(238, 238, 238);
	border-bottom-color: rgb(204, 204, 204);
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 0px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-left-color: rgb(204, 204, 204);
	border-left-style: solid;
	border-left-width: 1px;
	border-right-color: rgb(204, 204, 204);
	border-right-style: solid;
	border-right-width: 1px;
	border-top-color: rgb(204, 204, 204);
	border-top-left-radius: 4px;
	border-top-right-radius: 0px;
	border-top-style: solid;
	border-top-width: 1px;
	color: rgb(51, 51, 51);
	display: inline-block;
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-weight: normal;
	height: 20px;
	line-height: 20px;
	margin-right: -1px;
	min-width: 16px;
	padding-bottom: 4px;
	padding-left: 5px;
	padding-right: 5px;
	padding-top: 4px;
	text-align: center;
	text-shadow: rgb(255, 255, 255) 0px 1px 0px;
	vertical-align: top;
	white-space: nowrap;
	width: 16px;
}

input.pfp {
	-webkit-appearance: none;
	-webkit-border-image: none;
	-webkit-box-shadow: rgba(0, 0, 0, 0.0745098) 0px 1px 1px 0px inset;
	-webkit-rtl-ordering: logical;
	-webkit-transition-delay: 0s, 0s;
	-webkit-transition-duration: 0.20000000298023224s, 0.20000000298023224s;
	-webkit-transition-property: border, box-shadow;
	-webkit-transition-timing-function: linear, linear;
	-webkit-user-select: text;
	-webkit-writing-mode: horizontal-tb;
	background-color: rgb(255, 255, 255);
	border-bottom-color: rgb(204, 204, 204);
	border-bottom-left-radius: 0px;
	border-bottom-right-radius: 4px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-left-color: rgb(204, 204, 204);
	border-left-style: solid;
	border-left-width: 1px;
	border-right-color: rgb(204, 204, 204);
	border-right-style: solid;
	border-right-width: 1px;
	border-top-color: rgb(204, 204, 204);
	border-top-left-radius: 0px;
	border-top-right-radius: 4px;
	border-top-style: solid;
	border-top-width: 1px;
	box-shadow: rgba(0, 0, 0, 0.0745098) 0px 1px 1px 0px inset;
	color: rgb(85, 85, 85);
	cursor: auto;
	display: inline-block;
	float: none;
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-weight: normal;
	height: 30px;
	letter-spacing: normal;
	line-height: 20px;
	margin-bottom: 0px;
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0px;
	min-height: 1px;
	padding-bottom: 4px;
	padding-left: 6px;
	padding-right: 6px;
	padding-top: 4px;
	position: relative;
	text-align: start;
	text-indent: 0px;
	text-shadow: none;
	text-transform: none;
	white-space: nowrap;
	width: 56px;
	word-spacing: 0px;
	writing-mode: lr-tb;
}