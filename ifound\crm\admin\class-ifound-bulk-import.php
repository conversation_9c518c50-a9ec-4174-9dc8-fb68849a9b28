<?php
defined('ABSPATH') or die('You do not have access!');

class iFoundBulkImport {
	use UtilTrait;

	protected $contacts = null;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('admin_enqueue_scripts', array($this, 'admin_scripts'), 4);
		add_action('admin_menu', array($this, 'menu'), 6);
	}

	public function admin_scripts() {
		wp_register_style('bulk_import_css', plugins_url('css/bulk-import.css', __FILE__), array(), iFOUND_PLUGIN_VERSION);
	}

	public function menu() {
		add_submenu_page(
			iFoundCrm::new_hookless()->crm_menu(),
			__('Bulk Import', 'ifound'),
			__('Bulk Import', 'ifound'),
			'crm_import',
			'bulk_import_contacts',
			array($this, 'bulk_import_page')
		);
	}

	public function bulk_import_page() {
		if (!apply_filters('ifound_has_feature', 'bulk-campaigns')) {
			do_action('ifound_warn_feature', 'bulk-campaigns');
			return;
		}

		wp_enqueue_style('bulk_import_css');

		?>
        <div class="ifound-wrap bulk_import_page">
            <h1 class="ifound-admin-h1"><? _e('Bulk Import', 'ifound'); ?> </h1>
			<?
			if (!current_user_can('crm_import')) {
				?> You do not have permissions to import contacts. <?php
			} else {
				$this->csvImport();
				if ($this->contacts !== null) {
					$this->display_list($this->contacts);
				} else {
					?>
                    <div>
                        <h3 class="ifound-admin-h3"> Please choose a CSV file to upload </h3>
                    </div>

                    <form method="post" enctype="multipart/form-data">
                        <input type="file" name="bulk_import_csv">
                        <button class="button" type="submit">Upload</button>
                    </form>

                    <div>
                        To ensure a successful import, please make sure your csv follows the order of data as our
                        template:
                        <a href="https://ifoundagent.com/wp-content/uploads/sites/1/2020/07/ifound_crm_bulk_import_template.csv"
                           download>Download template</a>
                    </div>
					<?php
				}
			}
			?>
        </div>
		<?php
	}

	public function csvImport() {
		if (isset($_FILES['bulk_import_csv'])) {
			$file = $_FILES['bulk_import_csv'];
			$file_name = $file['name'];
			$file_size = $file['size'];
			$file_tmp = $file['tmp_name'];
			$parts = explode('.', $file_name);
			$file_ext = strtolower(end($parts));
			$file_error = $file['error'];
			$allowed = 'csv';

			if ($file_ext == $allowed) {
				if ($file_error == 0) {
					if ($file_size <= 262144000) {
						$this->csvRead($file_tmp);
					}
				}
			}
		}
	}

	public function csvRead($file_to_import) {
		$contacts = array();
		try {
			$orig_value = ini_get('auto_detect_line_endings');
			ini_set('auto_detect_line_endings', true);
			if (($handle = fopen($file_to_import, "r")) !== FALSE) {
				try {
					while (($line = fgetcsv($handle, 0, ",")) !== FALSE) {
						$num = count($line);
						$fN = $lN = $eM = $eM2 = $hP = $wP = $mP = $bDay = $aD = $aD2 = $city = $state = $zip = $acquiredFrom = $acquiredDate = $spouseFName = $spouseLName = $spouseEmail = $spouseEmail2 = $spouseHPhone = $spouseWPhone = $spouseMPhone = $spouseBDay = $tags = $contact_statuses = '';
						for ($c = 0; $c < $num; $c++) {
							if ($line[$c] != "")
								switch ($c) {
									case 0:
										$fN = $line[$c];
										if ($fN === 'First name') {
											// This line is a header. Skip it.
											// Use continue 3 for 3 levels: switch, for, while.
											continue 3;
										}
										break;
									case 1:
										$lN = $line[$c];
										break;
									case 2:
										$eM = $line[$c];
										break;
									case 3:
										$eM2 = $line[$c];
										break;
									case 4:
										$hP = $line[$c];
										break;
									case 5:
										$wP = $line[$c];
										break;
									case 6:
										$mP = $line[$c];
										break;
									case 7:
										$bDay = $line[$c];
										break;
									case 8:
										$aD = $line[$c];
										break;
									case 9:
										$aD2 = $line[$c];
										break;
									case 10:
										$city = $line[$c];
										break;
									case 11:
										$state = $line[$c];
										break;
									case 12:
										$zip = $line[$c];
										break;
									case 13:
										$acquiredFrom = $line[$c];
										break;
									case 14:
										$acquiredDate = $line[$c];
										break;
									case 15:
										$spouseFName = $line[$c];
										break;
									case 16:
										$spouseLName = $line[$c];
										break;
									case 17:
										$spouseEmail = $line[$c];
										break;
									case 18:
										$spouseEmail2 = $line[$c];
										break;
									case 19:
										$spouseHPhone = $line[$c];
										break;
									case 20:
										$spouseWPhone = $line[$c];
										break;
									case 21:
										$spouseMPhone = $line[$c];
										break;
									case 22:
										$spouseBDay = $line[$c];
										break;
									case 23:
										$tags = $line[$c];
										break;
									case 24:
										$contact_statuses = $line[$c];
										break;
								}
						}

						$info = [
							'fname' => $fN,
							'lname' => $lN,
							'email' => $eM,
							'email2' => $eM2,
							'hphone' => $hP,
							'wphone' => $wP,
							'mphone' => $mP,
							'birthday' => $bDay,
							'address' => $aD,
							'address_2' => $aD2,
							'city' => $city,
							'state' => $state,
							'zip' => $zip,
							'acquired_from' => $acquiredFrom,
							'acquired_date' => $acquiredDate,
							'fname_spouse' => $spouseFName,
							'lname_spouse' => $spouseLName,
							'email_spouse' => $spouseEmail,
							'email2_spouse' => $spouseEmail2,
							'hphone_spouse' => $spouseHPhone,
							'wphone_spouse' => $spouseWPhone,
							'mphone_spouse' => $spouseMPhone,
							'birthday_spouse' => $spouseBDay,
							'tags' => $tags,
							'contact_statuses' => $contact_statuses,
						];

						$contacts[] = $info;
					}
					$this->bulk_save_contacts($contacts);
				} finally {
					fclose($handle);
				}
			}
		} finally {
			ini_set('auto_detect_line_endings', $orig_value);
		}
	}


	public function display_list($contacts) {
		$areContacts = true;
		if ($contacts === null) {
			$areContacts = false;
		} else {
			$allContactTypes = array_merge($contacts['contacts'], $contacts['duplicates'], $contacts['missing_email']);
			if (count($allContactTypes) === 0) {
				$areContacts = false;
			}
		}
		if (!$areContacts) {
			?>
            <div>No contacts were uploaded</div> <?php
		} else {
			?>
            <div class="section">
                <div>Here are the contacts you uploaded:</div>
	            <?php $this->display_contacts($contacts['contacts']); ?>
                <? if (!empty($this->contacts['contacts'])): ?>
                <div>
                    <form method="GET" action="<?=admin_url('admin.php')?>">
                        <input type="hidden" name="page" value="create_homeowner_campaign">
                        <? foreach($contacts['contacts'] as $contact) : ?>
                            <input type="hidden" name="contact_ids[]" value="<?=$contact['contact_id']?>">
                        <? endforeach ?>
                        <button type="submit">Bulk create campaigns for these contacts</button>
                        (Alternatively you can create campaigns for any number of contacts from the contacts page)
                    </form>
                </div>
                <? endif ?>
            </div>
            <div class="section">
                <div>These contacts were duplicates based on email address and were ignored:</div>
				<?php $this->display_contacts($contacts['duplicates']); ?>
            </div>
            <div class="section">
                <div>These contacts were missing an email address and were ignored:</div>
				<?php $this->display_contacts($contacts['missing_email']); ?>
            </div>
			<?php
		}
		?>
        <div class="section">
            <a href="">Upload more</a>
        </div>
		<?php
	}

	private function display_contacts($contacts) {
		if (!$contacts) {
			?> None <?php
			return;
		}
		?>
        <table style="width: 100%">
            <thead>
            <tr align="left">
                <th>First name</th>
                <th>Last name</th>
                <th>Email</th>
            </tr>
            </thead>
            <tbody>
			<?php foreach ($contacts as $row) {
				$row = array_map('htmlentities', $row);
				?>
                <tr align="left">
                    <td><?= $row['fname'] ?></td>
                    <td><?= $row['lname'] ?></td>
                    <td><?= $row['email'] ?></td>
                </tr>
				<?php
			}
			?>
            </tbody>
        </table>
		<?php
	}

	public function bulk_save_contacts($contacts_to_save) {
		$contacts = [];
		$duplicates = [];
		$missing_email = [];
		foreach ($contacts_to_save as $individual) {

			$email = trim($individual['email']);

			if (is_email($email)) {

				$contact_id = iFoundCrm::new_hookless()->get_post_id_by_key_value('email', sanitize_email($email));

				if ($contact_id) {
					$duplicates[] = $individual;
				} else {
					$title = (empty ($individual['fname']) && empty ($individual['lname'])) ? $email : $individual['fname'] . ' ' . $individual['lname'];

					/** Create a new contact */
					$my_post = array(
						'post_title' => trim(wp_strip_all_tags($title)),
						'post_status' => 'publish',
						'post_type' => iFoundJointContact::new_hookless()->get_new_contact_type());

					// Insert the post into the database
					$contact_id = wp_insert_post($my_post);
					$individual['contact_id'] = $contact_id;

					$entry = $this->util()->save_contact_meta($contact_id, $individual);

					// Handle contact tags
					$tags_array = $this->util()->split_on_comma($individual['tags']);
					foreach ($tags_array as $tag) {
						wp_set_object_terms($contact_id, $tag, iFoundContacts::$contact_tag_taxonomy, true);
					}
					// Handle contact statuses
					$statuses_array = $this->util()->split_on_comma($individual['contact_statuses']);
					foreach ($statuses_array as $status) {
						wp_set_object_terms($contact_id, $status, iFoundContacts::$the_taxonomy, true);
					}

					do_action('ifound_external_crm_new_submission', iFound::new_hookless()->obj($entry));
					do_action('ifound_wiseagent_new', $contact_id);
					$contacts[] = $individual;
				}
			} else {
				$missing_email[] = $individual;
			}
		}

		$this->contacts = [
			'contacts' => $contacts,
			'duplicates' => $duplicates,
			'missing_email' => $missing_email,
		];
	}
}
