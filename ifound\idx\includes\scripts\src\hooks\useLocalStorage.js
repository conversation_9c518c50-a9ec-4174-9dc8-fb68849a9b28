import { useState } from 'react';
import _get from 'lodash/get';
import _set from 'lodash/set';
import produce from 'immer';

function useLocalStorage() {
    let [value, setValue] = useState(() => {
        const ifound = localStorage.getItem('ifound');
        if (ifound) {
            return JSON.parse(ifound);
        }
        return {};
    });

    function setKeyValue(key, v) {
        const newState = produce(value, draftState => {
            _set(draftState, key, v);
        });
        setValue(newState);
        save(newState);
    }

    function save(newState) {
        localStorage.setItem('ifound', JSON.stringify(newState));
    }

    return {
        value,
        setKeyValue,
    }
}

export default useLocalStorage;
