<?
defined('ABSPATH') or die('You do not have access!');

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

if (!class_exists('iFoundCrm')) die('You do not have access!');

class iFoundDripCampaign extends iFoundCrm {
    use NewHooklessTrait;

	public static $the_post_type = 'drip_campaign';
	public static $the_label_name = 'Drip Campaign';
	public static $cron_event_name = 'ifound_run_drip_campaign';
	public static $cron_upcoming_reminders_email_setting_name = 'upcoming_reminders_time_of_day';
	public static $upcoming_reminders_hook_name = 'ifound_send_upcoming_reminders_email';

	protected $post_type;
	private $label_name;
	private $query_var = 'drip-campaign';
	private $label_names = 'Drip Campaigns';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$this->post_type = static::$the_post_type;
		$this->label_name = static::$the_label_name;

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'post_type'));
			add_action('add_meta_boxes_drip_campaign', [$this, 'add_meta_boxes_drip_campaign']);
			add_action(static::$cron_event_name, [$this, 'ifound_run_drip_campaign']);
			// delete_post has 2 parameters as of WP 5.5, but our code will still work with 1.
			add_action('delete_post', [$this, 'delete_post_hook']);
			// before_delete_post has 2 parameters as of WP 5.5, but our code will still work with 1.
			add_action('before_delete_post', [$this, 'before_delete_post_hook']);
			add_action('wp_ajax_update_campaign', array($this, 'toggle_campaign_status'));
			add_action('save_post_' . $this->post_type, [$this, 'save_post_hook']);
			add_action('wp_ajax_remove_this', array($this, 'remove_this'));
			add_action('current_screen', [$this, 'current_screen_hook']);
			add_action('pre_get_posts', [$this, 'pre_get_posts']);
			// Reminder: map_meta_cap, at the least, prevents other team members from editing others' posts.
			add_filter('map_meta_cap', [$this, 'map_meta_cap'], 10, 4);
			add_action('admin_init', array($this, 'handle_contact_manager_settings_changes'), 99);
			add_action(static::$upcoming_reminders_hook_name, [$this, 'send_upcoming_reminders_email']);
		}
	}

	public function post_type() {
		$args = [
			'labels'               => array(
				'name'          => __($this->label_names),
				'singular_name' => __($this->label_name),
				'add_new'       => __('Add New ' . $this->label_name),
				'add_new_item'  => __('Add New ' . $this->label_name),
				'edit_item'     => __('Edit ' . $this->label_name),
				'new_item'      => __('New ' . $this->label_name),
				'view_item'     => __('View ' . $this->label_name),
				'view_items'    => __('View ' . $this->label_names),
				'search_items'  => __('Search ' . $this->label_names),
				'all_items'     => __($this->label_names),
				'attributes'    => __($this->label_name . ' Attributes'),
			),
			'query_var'            => $this->query_var,
			'show_ui'              => true,
			'show_in_menu'         => $this->crm_menu(),
			'show_in_nav_menus'    => false,
			'show_in_admin_bar'    => false,
			'public'               => false,
			'has_archive'          => false,
			'hierarchical'         => true,
			'supports'             => array('title'),
			'taxonomies'           => [iFoundSaveThis::$the_taxonomy2],
		];
		if (!$this->is_site_admin()) {
			$args['capability_type'] = $this->post_type;
			// We want to forward users to our Drip Campaign Builder page when they try to use the "Add New"
			// functionality. That happens in current_screen_hook(). But that hook won't be called if WP deems that the
			// user doesn't have permission to do so. As explained in iFoundDripTemplate->post_type(), based on how we
			// are using 'show_in_menu' and that we're in a submenu, WP will say non-admins don't have permission. This
			// is how we get around that. The difference is that if you use "Add New" to create a drip template, you'll
			// see a new top level menu of Drip Templates with a highlighted sub menu of Add New. But for drip
			// campaigns, we forward the user to the Campaign Builder > Drip Campaign page, so there won't be that same
			// top level menu situation as there is for drip templates.
			if (strpos($this->current_url(), 'post-new.php?post_type=' . $this->post_type) !== false) {
				unset($args['show_in_menu']);
			}
		}
		register_post_type($this->post_type, $args);
	}

	public function add_meta_boxes_drip_campaign() {
		add_meta_box(
			'drip_campaign_meta',
			__( '<i class="fal fa-database" aria-hidden="true"></i> Saved Campaign Data', 'ifound' ),
			[$this, 'drip_campaign_metabox'],
			$this->post_type,
			'advanced',
			'high'
		);
	}

	public function drip_campaign_metabox() {
		$drip_campaign_id = get_the_ID();
		$drip_campaign_meta = $this->util()->get_single_metas($drip_campaign_id, [
			'contact_id',
			'drip_template_id',
			'to_email',
			'upcoming_step_index',
			'is_done',
		]);
		$contact_id = $drip_campaign_meta['contact_id'];
		$contact_post = get_post($contact_id);
		$contact_name = $contact_post->post_title;
		$contact_link = admin_url("post.php?post=$contact_id&action=edit");
		$drip_template_id = $drip_campaign_meta['drip_template_id'];
		$drip_template_post = get_post($drip_template_id);
		$drip_template_title = $drip_template_post->post_title;
		$drip_template_link = admin_url("post.php?post=$drip_template_id&action=edit");
		$contact_meta = $this->util()->get_single_metas($contact_id, ['email', 'email2', 'email_spouse',
			'email2_spouse']);
		$to_emails = explode(',', $drip_campaign_meta['to_email']);
		$emails_map = [
			'email' => $contact_meta['email'],
			'email2' => $contact_meta['email2'],
			'email_spouse' => $contact_meta['email_spouse'],
			'email2_spouse' => $contact_meta['email2_spouse'],
		];
		$upcoming_step_index = intval($drip_campaign_meta['upcoming_step_index']);
		$upcoming_step_num = $upcoming_step_index + 1;
		$is_active = has_term('active', iFoundSaveThis::$the_taxonomy2, $drip_campaign_id);
		$is_done = $drip_campaign_meta['is_done'] === 'true';
		$campaign_status = $is_active ? 'Active' : 'Inactive' . ($is_done ? ', Done' : '');
		?>

        <?php /* Using a <fieldset disabled> will disable all these inputs */ ?>
        <fieldset <?= $is_done ? 'disabled="disabled"' : '' ?>>
            <table class="form-table views_meta">
                <tbody>
                <tr>
                    <th scope="row"><label><? _e('Status', 'ifound'); ?></label></th>
                    <td><?= $campaign_status ?></td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Created', 'ifound'); ?></label></th>
                    <td><? echo get_the_date('M d, Y', $drip_campaign_id); ?></td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Contact', 'ifound'); ?></label></th>
                    <td>
                        <a href="<?= $contact_link ?>"><?= $contact_name ?></a>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Drip Template', 'ifound'); ?></label></th>
                    <td>
                        <a href="<?= $drip_template_link ?>"><?= $drip_template_title ?></a>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('To Email(s)', 'ifound'); ?></label></th>
                    <td>
                        <input type="hidden" name="to_email" id="to_email"
                               value="<?= $drip_campaign_meta['to_email'] ?>">
                        <strong class="no_contact_selected_warning">No contact selected</strong>
                        <div class="to_emails"></div>
                        <strong class="no_email_addresses_checked_warning hidden">You must check at least one email
                            address</strong>
                        <script>
                            jQuery(document).ready(function ($) {
                                var emails_map = <?= json_encode($emails_map); ?>;
                                var options = <?= json_encode(['selected_emails' => $to_emails]) ?>;
                                window.ifound_populateEmailsTo(emails_map, options);
                            });
                        </script>
                    </td>
                </tr>

                <tr>
                    <td colspan="2">
                        <div class="default-criteria-heading">
                            <i class="fal fa-calendar-alt" aria-hidden="true"></i>
                            <? _e('Branding', 'ifound'); ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Header', 'ifound'); ?></label></th>
                    <td><? do_action('ifound_email_dropdown', 'header', 'header', get_post_meta($drip_campaign_id, 'header', true)); ?></td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Signature', 'ifound'); ?></label></th>
                    <td><? do_action('ifound_email_dropdown', 'signature', 'signature', get_post_meta($drip_campaign_id, 'signature', true)); ?></td>
                </tr>
                <tr>
                    <th scope="row"><label><? _e('Footer', 'ifound'); ?></label></th>
                    <td><? do_action('ifound_email_dropdown', 'footer', 'footer', get_post_meta($drip_campaign_id, 'footer', true)); ?></td>
                </tr>

                <tr>
                    <td colspan="2">
                        <div class="default-criteria-heading">
                            <i class="fal fa-calendar-alt" aria-hidden="true"></i>
                            <? _e('Schedule', 'ifound'); ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <? $this->show_schedule($drip_campaign_id, $drip_template_id, $upcoming_step_index) ?>
                    </td>
                </tr>
                </tbody>
            </table>
        </fieldset>

		<?php
	}

	public function ifound_run_drip_campaign($drip_campaign_id) {
		$drip_campaign_meta = $this->util()->get_single_metas($drip_campaign_id, [
			'drip_template_id',
			'upcoming_step_index',
			'contact_id',
		]);
		$drip_template = get_post($drip_campaign_meta['drip_template_id']);
		$drip_template_data = get_post_meta($drip_template->ID, 'template_data', true);
		$steps = $drip_template_data['steps'];
		$upcoming_step = $steps[$drip_campaign_meta['upcoming_step_index']];

		if ($upcoming_step['customer_template_id']) {
			do_action('ifound_email', $drip_campaign_id, 'drip_campaign_customer', [
				'email_template_id' => $upcoming_step['customer_template_id'],
			]);
		}
		if ($upcoming_step['reminder_template_id']) {
			$contact_id = $drip_campaign_meta['contact_id'];
			$crm_id = $this->crm_id(['contact_id' => $contact_id]);
			$crm = $this->crm($crm_id);
			if ($crm->sms_opt_in === 'enabled' && $crm->sms_number) {
				$email_template_id = $upcoming_step['reminder_template_id'];
				$content = iFoundEmail::new_hookless()->get_drip_reminder_content($email_template_id,
					$contact_id);
				$iFoundSms = iFoundSms::new_hookless();
				$post_owner_id = get_post($drip_campaign_id)->post_author;
				$response = $iFoundSms->send($crm->sms_number, $content, iFoundSms::$PURPOSE_AGENT_DRIP_REMINDER, [
					'user_id' => $post_owner_id,
					'post_id' => $drip_campaign_id,
				]);
				$iFoundSms->write_to_activity_log($drip_campaign_id, 'Send Drip Campaign Reminder SMS to agent',
					$content, $response);
			}
		}

		$new_upcoming_step_index = $drip_campaign_meta['upcoming_step_index'] + 1;
		update_post_meta(
			$drip_campaign_id,
			'upcoming_step_index',
			$new_upcoming_step_index
		);
		update_post_meta(
			$drip_campaign_id,
			'previous_event_timestamp',
			current_time('timestamp', true)
		);
		$is_campaign_done = $new_upcoming_step_index >= count($steps);
		if ($is_campaign_done) {
			do_action('ifound_update_campaign_status', $drip_campaign_id, 'inactive');
			update_post_meta($drip_campaign_id, 'is_done', 'true');
		} else {
			$interval = $steps[$new_upcoming_step_index]['interval'];
			$cron_time = $this->util()->strtotime_improved($interval, current_time('timestamp', true));
			wp_schedule_single_event($cron_time, static::$cron_event_name, [$drip_campaign_id]);
		}
	}

	public function show_schedule($drip_campaign_id, $drip_template_id, $upcoming_step_index) {
		$is_campaign_active = iFoundSaveThis::new_hookless()->is_campaign_active($drip_campaign_id);
		$steps = get_post_meta($drip_template_id, 'template_data', true)['steps'];
		$start_step_index = intval(get_post_meta($drip_campaign_id, 'start_step_index', true));
		$email_templates_by_id = array_reduce($steps, function($carry, $value) {
			$keys = ['customer_template_id', 'reminder_template_id'];
			foreach ($keys as $key) {
				if (!$value[$key]) {
					continue;
				}
				$template_id = $value[$key];
				if (!isset($carry[$template_id])) {
					$carry[$template_id] = get_post($template_id);
				}
			}
			return $carry;
		}, []);
		$upcoming_event_timestamp = wp_next_scheduled(static::$cron_event_name, [$drip_campaign_id]);
		$previous_event_timestamp = $upcoming_event_timestamp;
		?>

		<table class="widefat striped">
			<thead>
				<tr>
					<th>Step #</th>
					<th>Email Template for Customer</th>
					<th>Reminder to Agent</th>
					<th>Interval from Previous Step</th>
					<th>Expected Delivery Date</th>
				</tr>
			</thead>
			<tbody>
			<?php
			foreach ($steps as $index => $step) {
				if ($index < $start_step_index) {
					?>
					<tr style="opacity: 50%;">
						<td><?= $index + 1 ?></td>
					</tr>
					<?php
					continue;
				}

				$is_in_past = $index < $upcoming_step_index;
				$expected_delivery_date_string = '(See the Activity Log)';
				if (!$is_in_past) {
					$expected_delivery_date_string = '(Campaign is inactive)';
					if ($is_campaign_active) {
						$expected_delivery_timestamp = $upcoming_event_timestamp;
						if ($index !== $upcoming_step_index) {
							$expected_delivery_timestamp = $this->util()->strtotime_improved($step['interval'], $previous_event_timestamp);
						}
						$previous_event_timestamp = $expected_delivery_timestamp;
						$expected_delivery_date_string = $this->pretty_date($expected_delivery_timestamp, true);
					}
				}
				$step_message = ($index !== 0 && $index === $start_step_index) ? ' (Started here)' : '';
				$interval = $index === $start_step_index ? 'Campaign Start' : $step['interval'];
				?>
				<tr <?= ($is_in_past || !$is_campaign_active) ? 'style="opacity: 50%;"' : '' ?>>
					<td><?= $index + 1 ?><?= $step_message ?></td>
					<td><?= $email_templates_by_id[$step['customer_template_id']]->post_title ?? '-' ?></td>
					<td><?= $email_templates_by_id[$step['reminder_template_id']]->post_title ?? '-' ?></td>
					<td><?= $interval ?></td>
					<td><?= $expected_delivery_date_string ?></td>
				</tr>
				<?
			}
			?>
			</tbody>
		</table>

		<?php
	}

	public function delete_post_hook($post_id) {
		$post = get_post($post_id);
		if ($post->post_type === static::$the_post_type) {
			$this->unschedule_cron_if_needed($post_id);
		}
	}

	public function get_campaigns_args_for_contact($contact_id) {
		$args = array(
			'post_type' 		=> $this->post_type,
			'posts_per_page'	=> -1,
			'meta_query' => array(
				array(
					'key'     => 'contact_id',
					'value'   => $contact_id,
				)
			)
		);

		return $args;
	}

	// Toggle campaign status. Does not check if campaign is already done.
	// If the new status is active, we must set up the cron event. Otherwise, if inactive, remove the cron event.
	public function toggle_campaign_status() {
		check_ajax_referer('update_campaign_secure_me', 'update_campaign_nonce');
		$post_id = intval($_REQUEST['id']);
		// This hook runs for both search campaigns and drip campaigns. Only run this code if it's this type.
		if ($post_id > 0 && is_int($post_id) && get_post_type($post_id) == $this->post_type) {
			$campaign_status = has_term('active', iFoundSaveThis::$the_taxonomy2, $post_id) ? 'inactive' : 'active';
			if ($campaign_status == 'active') {
				// Do not allow campaign to be re-enabled if it is not owned by this user (initially expected scenario
				// is an admin who can see a contact that they assigned to a team member).
				$user_crm_id = $this->crm_id();
				$contact_id = get_post_meta($post_id, 'contact_id', true);
				$contact_crm_id = $this->crm_id_from_user_id(get_post($contact_id)->post_author);
				if ($user_crm_id !== $contact_crm_id) {
					$msg = 'The campaign cannot be enabled because the contact does not belong to you';
					$this->util()->respond_with_error($msg);
				}
			}
			do_action('ifound_update_campaign_status', $post_id, $campaign_status);
			$next_time = null;
			if ($campaign_status == 'active') {
				$class = 'fa-toggle-on';
				$upcoming_step_index = get_post_meta($post_id, 'upcoming_step_index', true);
				$drip_template_id = get_post_meta($post_id, 'drip_template_id', true);
				$steps = get_post_meta($drip_template_id, 'template_data', true)['steps'];
				$upcoming_step_interval = $steps[$upcoming_step_index]['interval'];
				$next_time = $this->schedule_cron_event($post_id, $upcoming_step_interval);
			} else {
				$this->unschedule_cron_if_needed($post_id);
				$class = 'fa-toggle-off';
				$next_time = '-';
			}
			$response = array(
				'status'    => ucwords($campaign_status),
				'class'     => $class,
				'next_time' => apply_filters('pretty_date', $next_time, true),
			);
			echo json_encode($response);
			die();
		}
	}

	// Schedule a campaign's next cron event from an interval, based on the campaign's most recent event.
	public function schedule_cron_event($campaign_id, $interval) {
		$previous_event_timestamp = intval(get_post_meta($campaign_id, 'previous_event_timestamp', true));
		$upcoming_event_time = $this->util()->strtotime_improved($interval, $previous_event_timestamp);
		$current_time = current_time('timestamp', true);
		$next_time = $current_time < $upcoming_event_time ? $upcoming_event_time : $current_time;
		wp_schedule_single_event($next_time, static::$cron_event_name, [$campaign_id]);
		return $next_time;
	}

	// When a contact is deleted, Wordpress will delete all associated things like drip campaigns, but we need to
	// unschedule the cron events for scheduled drip campaigns. We must do this with the before_delete_post hook because
	// if we try to use the deleted_post hook, the contacts' campaigns will already have been deleted, and thus we won't
	// be able to delete their associated cron events.
	public function before_delete_post_hook($post_id) {
		$post_type = get_post_type($post_id);
		if (in_array($post_type, [iFoundContacts::$the_post_type, iFoundPrivateContact::$the_post_type], true)) {
			$posts = get_posts($this->get_campaigns_args_for_contact($post_id));
			foreach ($posts as $post) {
				$this->unschedule_cron_if_needed($post->ID);
			};
		}
	}

	public function unschedule_cron_if_needed($post_id) {
		$next_scheduled_timestamp = wp_next_scheduled(static::$cron_event_name, [$post_id]);
		if ($next_scheduled_timestamp) {
			wp_unschedule_event($next_scheduled_timestamp, static::$cron_event_name, [$post_id]);
		}
	}

	public function save_post_hook($post_id) {
		if( isset( $_POST['original_publish' ] ) ) {
			do_action( 'ifound_activity_log', $post_id, 'Update Campaign Data', 'Campaign ID:' . $post_id . ' Updated' );

			$data = $this->obj($_POST);
			$clean_data = apply_filters('ifound_sanitize', [
				'to_email'   => $data->to_email,
				'header'     => $data->header,
				'signature'  => $data->signature,
				'footer'     => $data->footer,
			]);
			foreach ($clean_data as $key => $value) {
				update_post_meta($post_id, $key, $value);
			}
		}
	}

	// If the feature is disabled, we need to deactivate all drip campaigns and remove all associated cron events.
	public function disable_feature() {
		$args = array(
			'post_type' 		=> $this->post_type,
			'posts_per_page'	=> -1,
		);
		$posts = get_posts($args);
		foreach ($posts as $post) {
			$is_active = has_term( 'active', iFoundSaveThis::$the_taxonomy2, $post->ID);
			if ($is_active) {
				do_action('ifound_update_campaign_status', $post->ID, 'inactive');
				$this->unschedule_cron_if_needed($post->ID);
			}
		}
	}

	public function update_campaigns_from_updated_template($drip_template_id, $previous_template_data,
														   $new_template_data) {
		$previous_steps = $previous_template_data['steps'];
	    $new_steps = $new_template_data['steps'];
	    $campaigns = get_posts([
		    'post_type'   => static::$the_post_type,
		    'numberposts' => -1,
		    'meta_query'  => [
			    [
				    'key'   => 'drip_template_id',
				    'value' => $drip_template_id,
			    ],
		    ],
	    ]);

	    // We'll need to make changes to some of the campaigns. We can keep track of which ones have already been
	    // processed to not do duplicate work.
	    $already_processed_campaigns_by_id = [];
	    // If a campaign's upcoming step number is more than the new number of steps, then we need to mark the
	    // campaign as inactive and done.
	    if (count($new_steps) < count($previous_steps)) {
		    foreach ($campaigns as $campaign) {
			    $upcoming_step_index = get_post_meta($campaign->ID, 'upcoming_step_index', true);
			    $is_campaign_done = $upcoming_step_index >= count($new_steps);
			    if ($is_campaign_done) {
				    $is_campaign_active = has_term('active', iFoundSaveThis::$the_taxonomy2, $campaign->ID);
				    // Check if the campaign is already inactive, so we don't unnecessarily write to the activity
				    // log.
				    if ($is_campaign_active) {
					    do_action('ifound_update_campaign_status', $campaign->ID, 'inactive');
				    }
				    update_post_meta($campaign->ID, 'is_done', 'true');
				    $this->unschedule_cron_if_needed($campaign->ID);
				    $already_processed_campaigns_by_id[$campaign->ID] = $campaign;
			    }
		    }
	    }

	    // Now check if intervals have changed. If so, update cron events for active campaigns.
		$step_indexes_that_have_changed_interval = [];
		foreach ($new_steps as $index => $new_step) {
		    if (
				// Ignore any newly added steps.
				$index + 1 <= count($previous_steps)
				&& $new_step['interval'] !== $previous_steps[$index]['interval']
		    ) {
			    $step_indexes_that_have_changed_interval[$index] = true;
		    }
	    }
	    foreach ($campaigns as $campaign) {
		    if (isset($already_processed_templates_by_id[$campaign->ID])) {
			    continue;
		    }
		    $is_campaign_active = has_term('active', iFoundSaveThis::$the_taxonomy2, $campaign->ID);
		    if ($is_campaign_active) {
			    $upcoming_step_index = get_post_meta($campaign->ID, 'upcoming_step_index', true);
			    if (isset($step_indexes_that_have_changed_interval[$upcoming_step_index])) {
				    $this->unschedule_cron_if_needed($campaign->ID);
				    $this->schedule_cron_event($campaign->ID, $new_steps[$upcoming_step_index]['interval']);
			    }
		    }
	    }
	}

	public function create_drip_campaign($data) {
		$contact_id = $data->contact_id;

		define( 'iFOUND_CONTACT_ID', $contact_id );

		$campaign_title = sanitize_text_field($data->campaign_title);

		$drip_campaign_post = array(
			'post_title'    => $campaign_title,
			'post_status'   => 'publish',
			'post_type'		=> static::$the_post_type,
			'post_author'	=> $data->post_author ?? get_current_user_id(),
		);

		$drip_campaign_id = wp_insert_post( $drip_campaign_post );
		$data->is_done = 'false';
		$data->previous_event_timestamp = current_time('timestamp', true);

		$clean_data = apply_filters('ifound_sanitize', [
			'contact_id'               => $data->contact_id,
			'to_email'                 => $data->to_email,
			'drip_template_id'         => $data->drip_template_id,
			'header'                   => $data->header,
			'signature'                => $data->signature,
			'footer'                   => $data->footer,
			'upcoming_step_index'      => $data->upcoming_step_index,
			'is_done'                  => $data->is_done,
			'previous_event_timestamp' => $data->previous_event_timestamp,
			'start_step_index'         => $data->upcoming_step_index,
		]);
		foreach ($clean_data as $key => $value) {
			update_post_meta($drip_campaign_id, $key, $value);
		}

		do_action('ifound_update_campaign_status', $drip_campaign_id, 'active');
		do_action( 'ifound_activity_log', $contact_id, 'Save Drip Campaign', $campaign_title );

		$cron_time = $this->get_cron_time($data->start_date, $data->time_of_day);
		wp_schedule_single_event($cron_time, static::$cron_event_name, [$drip_campaign_id]);

		return $drip_campaign_id;
	}

	private function get_cron_time($year_month_day, $time_of_day) {
		// We do the following in non-UTC because the values are from the user's perspective, as in, in their time zone.
		$time_string = $year_month_day . ($time_of_day === 'now' ? current_time('H:i:s') : $time_of_day);
		$time        	= strtotime($time_string);
		$current_time 	= current_time('timestamp');
		$next_time 		= $time < $current_time ? $time + 86400 : $time;
		$cron_time		= $next_time - get_option('gmt_offset') * 3600;
		return $cron_time;
	}

	// Reminder that this hook exists for both search campaigns and drip campaigns.
	public function remove_this() {
	    check_ajax_referer('remove_this_secure_me', 'remove_this_nonce');
	    $post_id = intval($_REQUEST['id']);
	    if (get_post_type($post_id) === $this->post_type) {
			$this->unschedule_cron_if_needed($post_id);
			$title = get_the_title($post_id);
			wp_delete_post($post_id, true);
			do_action('ifound_activity_log', $this->get_contact_id(), 'Delete Drip Campaign', $title);
			echo json_encode('success');
		    die();
	    }
	}

	// We don't want to allow the normal "Add New" flow for our custom post type. Forward to the drip campaign builder
	// page.
	public function current_screen_hook() {
		global $current_screen;
		if ($current_screen->post_type === static::$the_post_type && $current_screen->action === 'add') {
			$url = menu_page_url(iFoundDripCampaignBuilder::$menu_slug, false);
			wp_redirect($url);
			exit;
		}
	}

	// Don't show a user campaigns that belong to other users.
	public function pre_get_posts( $query ) {
		global $pagenow;
		if ($pagenow !== 'edit.php') {
			return $query;
		}

		global $typenow;
		if( current_user_can( 'edit_others_posts' ) && $typenow !== $this->post_type ) {
			return $query;
		}

		if ($typenow === $this->post_type) {
			if ($this->is_site_admin()) {
				$query->set('author__in', iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids());
			} else {
				$query->set('author', get_current_user_id());
			}
		}

		return $query;
	}

	public function get_active_campaign_ids_for_user($user_id) {
		$args = [
			'post_type'      => $this->post_type,
			'posts_per_page' => -1,
			'author'         => $user_id,
			'fields'         => 'ids',
			'tax_query'      => [
				[
					'taxonomy' => iFoundSaveThis::$the_taxonomy2,
					'field'    => 'slug',
					'terms'    => 'active',
				],
			],
		];
		$posts = get_posts($args);
		return $posts;
	}

	public function get_active_campaign_ids_for_contact_id($contact_id) {
		$args = [
			'post_type'      => $this->post_type,
			'posts_per_page' => -1,
			'fields'         => 'ids',
			'tax_query'      => [
				[
					'taxonomy' => iFoundSaveThis::$the_taxonomy2,
					'field'    => 'slug',
					'terms'    => 'active',
				],
			],
			'meta_query' => [
				[
					'key'     => 'contact_id',
					'value'   => $contact_id,
					'compare' => '=',
				],
			],
		];
		$posts = get_posts($args);
		return $posts;
	}

	public function handle_contact_manager_settings_changes() {
		$this->update_cron_for_hook(static::$cron_upcoming_reminders_email_setting_name,
			static::$upcoming_reminders_hook_name);
	}

	public function send_upcoming_reminders_email($user_id) {
		$crm = $this->crm_from_user_id($user_id);
		if (isset($crm->{static::$cron_upcoming_reminders_email_setting_name})
			&& $crm->{static::$cron_upcoming_reminders_email_setting_name} != 'none'
		) {
			do_action('ifound_upcoming_reminders_email', $user_id);
		}
	}

	public function get_upcoming_reminders_email_content($user_id) {
		$crons = get_option('cron');
		if (!is_array($crons)) {
			return 'You have no upcoming task reminders';
		}
		$crm_id_of_user = $this->crm_id();
		$reminders = [];
		foreach ($crons as $timestamp => $cron) {
			$cutoff_timestamp = time() + 60 * 60 * 24 * 30;
			if ($timestamp > $cutoff_timestamp) {
				continue;
			}

			// Ignore the key/value of (e.g.) 'version' => 2
			if (is_array($cron)) {
				foreach ($cron as $hook => $info) {
					if ($hook === static::$cron_event_name) {
						foreach ($info as $key => $data) {
							if (count($data['args'])) {
								$drip_campaign_id = $data['args'][0];
								$drip_campaign = get_post($drip_campaign_id);
								if (!$drip_campaign) {
									continue;
								}
								$contact_id = get_post_meta($drip_campaign_id, 'contact_id', true);
								$contact = get_post($contact_id);
								if (!$contact) {
									continue;
								}
								$crm_id_of_contact_owner = $this->crm_id(['contact_id' => $contact_id]);
								if ($crm_id_of_user === $crm_id_of_contact_owner) {
									$drip_campaign_meta = $this->util()->get_single_metas($drip_campaign_id, [
										'drip_template_id',
										'upcoming_step_index',
									]);
									$drip_template = get_post($drip_campaign_meta['drip_template_id']);
									$drip_template_data = get_post_meta($drip_template->ID, 'template_data', true);
									$steps = $drip_template_data['steps'];
									$upcoming_step = $steps[$drip_campaign_meta['upcoming_step_index']];

									if ($upcoming_step['reminder_template_id']) {
										$email_template_id = $upcoming_step['reminder_template_id'];
										$content = iFoundEmail::new_hookless()->get_drip_reminder_content($email_template_id,
											$contact_id);
										$pretty_date = $this->pretty_date($timestamp);
										$drip_campaign_url = get_edit_post_link($drip_campaign_id);
										$contact_url = get_edit_post_link($contact_id);
										$reminders[] = [
											'drip_campaign_id'  => $drip_campaign_id,
											'drip_campaign_url' => $drip_campaign_url,
											'pretty_date'       => $pretty_date,
											'contact_name'      => $contact->post_title,
											'contact_url'       => $contact_url,
											'message'           => $content,
										];
									}
								}
							}
						}
					}
				}
			}
		}
		ob_start();
		?>
		<div>Below is your daily list of upcoming task reminders</div>
		<?php
		foreach ($reminders as $reminder) {
			?>
			<div><?= $reminder['pretty_date'] ?> - <a href="<?= $reminder['contact_url']?>"><?= $reminder['contact_name'] ?></a> (campaign <a href="<?= $reminder['drip_campaign_url']?>"><?= $reminder['drip_campaign_id'] ?></a>)</div>
			<div style="margin-left: 2rem;"><?= $reminder['message'] ?></div>
			<br />
			<?php
		}
		return ob_get_clean();
	}

	public function get_drip_templates() {
		$args = [
			'posts_per_page' => -1,
			'post_type' 	 => iFoundDripTemplate::$the_post_type,
			'author__in'     => iFoundadmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
		];
		$templates = get_posts($args);
		$template_ids_by_title = array_reduce($templates, function($carry, $x) {
			// We use the title as the key, and the ID as the value, as opposed to vice-versa, because when we use the
			// entries on the JavaScript side, iterating them happens in order of keys. As in, they'd be sorted by ID,
			// regardless of how we sort them here. Flipping the key and value gets around this.
			$carry[$x->post_title] = $x->ID;
			return $carry;
		}, []);
		uksort($template_ids_by_title, function($a, $b) {
			return strnatcmp($a, $b);
		});
		return $template_ids_by_title;
	}

	public function get_template_steps_and_titles() {
		$drip_templates = iFoundDripCampaign::new_hookless()->get_drip_templates();
		$drip_template_steps_by_template_id = [];
		$drip_template_ids = array_values($drip_templates);
		foreach ($drip_template_ids as $drip_template_id) {
			$template_data = get_post_meta($drip_template_id, iFoundDripTemplate::$template_data_key, 'true');
			$drip_template_steps_by_template_id[$drip_template_id] = $template_data['steps'];
		}
		$email_template_titles_by_id = [];
		foreach ($drip_template_steps_by_template_id as $steps) {
			foreach ($steps as $step) {
				$types = ['customer_template_id', 'reminder_template_id'];
				foreach ($types as $type) {
					$template_id = $step[$type];
					if ($template_id && !isset($email_template_titles_by_id[$template_id])) {
						$email_template_titles_by_id[$template_id] = get_post($template_id)->post_title;
					}
				}
			}
		}

		return [
			'drip_template_steps_by_template_id' => $drip_template_steps_by_template_id,
			'email_template_titles_by_id' => $email_template_titles_by_id,
		];
	}
}
