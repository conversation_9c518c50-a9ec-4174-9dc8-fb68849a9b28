<?php

namespace Profound\MLS;

// This class is meant for reducing database searches to a subset.
// For example, to force the Sandicor MLS to exclude non-active listings.

class PropertyLimiter {
	private $options;

	public function __construct($options) {
		$this->options = $options;
	}

	public function getMls() {
		return $this->options['mls'];
	}

	public function getMlsClass() {
		return $this->options['mls_class'];
	}

	public function limitProperties(&$sql, $options = array()) {
		$this->limitByPolygons($sql, $options);
	}

	public function limitCities(&$sql) {
		// Do nothing by default	
	}

	protected function limitByPolygons(&$sql, $options = array()) {
		// First determine if we need to do any limiting by polygons in the first place.
		$extendedDataString = @$options['extended_data'];
		if (!$extendedDataString) {
			return;
		}
		$extendedData = json_decode($extendedDataString, true);
		if (!(isset($extendedData['map']['polygons']['list']) && count($extendedData['map']['polygons']['list']) > 0)) {
			return;
		}

		$fieldMappings = $options['field_mappings'];
		// By including the where clause here, we dramatically limit the number of listings to do for the polygon check.
		$propsSql = "SELECT {$fieldMappings['listing_id']}, {$fieldMappings['lat']}, {$fieldMappings['lon']} from {$options['db_table']} where 1 $sql";
		try {
			$allProps = $this->getMls()->getDb()->fetchAll($propsSql);
		} catch (\Exception $ex) {
			\D::elog("Polygon error");
			\D::elog($propsSql);
		}
		$propsToInclude = $this->removeListingsOutsideOfPolygons($allProps, $extendedData['map']['polygons']['list'], $fieldMappings['lat'], $fieldMappings['lon']);
		$a = array();
		foreach($propsToInclude as $prop) {
			$a[] = $prop[$fieldMappings['listing_id']];
		}
		if (count($a) > 0) {
			$ids = implode(", ", $a);
			$sql .= " AND {$fieldMappings['listing_id']} IN ($ids)";
		} else {
			// This seems like a decent way to indicate we did a lat/lon search and there were no results.
			$sql .= " AND {$fieldMappings['lat']} = -1 AND {$fieldMappings['lon']} = -1";
		}
	}

	protected function removeListingsOutsideOfPolygons($props, $polygons, $lat, $lon) {
		$pointsOfPolygons = array();
		foreach ($polygons as $polygon) {
			$pointsOfPolygons[] = $this->getPolygonPoints($polygon);
		}
		$propsToInclude = array();
		foreach ($props as $prop) {
			$point = array($prop[$lat], $prop[$lon]);
			$pointInAtLeastOnePolygon = false;
			foreach($pointsOfPolygons as $polygonPoints) {
				if ($this->polygonContainsPoint($point, $polygonPoints)) {
					$pointInAtLeastOnePolygon = true;
					$propsToInclude[] = $prop;
					break;
				}
			}
		}
		return $propsToInclude;
	}

	// I got this from here: http://tutorialspots.com/php-detect-point-in-polygon-506.html
	// I'll clean it up later. There's a better description of what's
	// happening here:
	// http://jakescruggs.blogspot.com/2009/07/point-inside-polygon-in-ruby.html
	protected function polygonContainsPoint($point, $polygon)
	{
		if($polygon[0] != $polygon[count($polygon)-1])
			$polygon[count($polygon)] = $polygon[0];
		$j = 0;
		$oddNodes = false;
		$x = $point[1];
		$y = $point[0];
		$n = count($polygon);
		for ($i = 0; $i < $n; $i++)
		{
			$j++;
			if ($j == $n)
			{
				$j = 0;
			}
			if ((($polygon[$i][0] < $y) && ($polygon[$j][0] >= $y)) || (($polygon[$j][0] < $y) && ($polygon[$i][0] >=
				$y)))
			{
				if ($polygon[$i][1] + ($y - $polygon[$i][0]) / ($polygon[$j][0] - $polygon[$i][0]) * ($polygon[$j][1] -
					$polygon[$i][1]) < $x)
				{
					$oddNodes = !$oddNodes;
				}
			}
		}
		return $oddNodes;
	}

	protected function getPolygonPoints($polygon) {
		$pointsString = $polygon['paths'];
		// The path string should look like:
		// (33.123, -111.123)(34.123, -112.123)
		$pointsString = str_replace(array('(', ')', ',',' '), '|', $pointsString);
		$pointsArray = explode('|', $pointsString);
		// Remove every other element. We go backwards, so that removing the
		// element doesn't affect the number of indices.
		for ($i = count($pointsArray); $i > 0; $i -= 2) {
			array_splice($pointsArray, $i - 1, 1);
		}
		$points = array();
		for ($i = 0; $i < count($pointsArray); $i += 2) {
			$points[] = array($pointsArray[$i], $pointsArray[$i + 1]);
		}
		return $points;
	}

	// We only want to geocode for MLSs where we don't expect RetsCloud to geocode.
	public function shouldGeocode() {
		return false;
	}
}
