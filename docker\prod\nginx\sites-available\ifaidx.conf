upstream link_shortener_server {
    server newidx:8155;
}

# Should we forward http to https for a simple link shortening service? I'd say no, for the sake of speed. However,
# I'll say yes because 1) it's easier to set up, since I already know how to forward everything and would have to spend
# time to figure out how to forward all but the root, and 2) I want the root page to be https.
server {
    listen 80;
    server_name ifaidx.com www.ifaidx.com;

    location / {
        return 301 https://$host$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
}

server {
    listen 443 ssl http2;
    server_name www.ifaidx.com;

    access_log /var/log/nginx/ifaidx.log;
    error_log /var/log/nginx/ifaidx-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifaidx.com-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifaidx.com-0001/privkey.pem;

    location / {
        return 301 https://ifaidx.com$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name ifaidx.com;

    access_log /var/log/nginx/ifaidx.log;
    error_log /var/log/nginx/ifaidx-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifaidx.com-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifaidx.com-0001/privkey.pem;

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://link_shortener_server;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_pass_request_headers on;
    }
}
