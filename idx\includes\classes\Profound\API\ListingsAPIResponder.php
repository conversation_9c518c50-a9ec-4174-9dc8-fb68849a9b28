<?php

namespace Profound\API;

use \PropQuery;

class ListingsAPIResponder extends APIResponder {
	private $idx;
	private $response;
	
	protected function getDefaultAPI() {
		return API::explicit("listings", "v1.01", "json");
	}

	protected function computeResponseData() {
		$this->idx = new \IDX($this->getConfigFilePath());

		$listing_ids = json_decode($_REQUEST['listing_ids']);

		$listings_data = array();
		foreach($listing_ids as $listing_id) {
			$details = $this->idx->propDetails($listing_id);
			$listings_data[$listing_id] = $details;
		}

		$listings = array("listings" => $listings_data);

		return $listings;
	}
}