<?
/**
 * iFound_social_media
 *
 * Social Media Widget.
 *
 * @package iFOUND
 * @since 1.1.1
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );
 
class iFound_social_media extends WP_Widget {
	
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Social Media Buttons', 
			array(
			'description' => 'Add Social Media buttons to any widget area.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {
		
		$this->options = (object) get_option( 'ifound_sociaL_media_networks' );

		if( empty( $this->options->networks ) ) return;
		
		wp_enqueue_style( 'social_media_css' );

		echo $args['before_widget']; ?>

		<div class="ifound-social-media-wrapper ifound-social-<? echo $this->options->button_type; ?>">

			<div class="widgets-wrap">

				<? $this->social_media_buttons(); ?>

			</div>

		</div><?

		echo $args['after_widget'];

	}

	public function social_media_buttons() {

		foreach( $this->options->networks as $network ) { ?>

			<a href="<? echo esc_url( $network['url'] ); ?>" title="<? echo $network['name']; ?>" target="_blank">

				<div class="social-network-icon-wrapper">	
						
					<div class="ifound-wrap">
										
						<i class="fab fa-<? echo $network['class']; ?> social-network-icon" aria-hidden="true"></i>
						
					</div>
								
				</div>

			</a><? 

		}

	}
	
}
