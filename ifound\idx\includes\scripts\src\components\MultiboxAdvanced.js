// This is for "multibox" searches, which is a single text box where the user can type a single search term and get back
// potential results related to address, school, city, zip, subdivision (neighborhood), and MLS ID.

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import AsyncSelect from 'react-select/async';
import {
	debouncedLoadOptionsCallback,
	buildLabelMap,
	noOptionsMessage,
	HiddenComponent,
	GroupHeadingMaker,
	OptionMaker,
	labelSubTypeMap,
} from '../lib/multibox';

const labelMap = buildLabelMap('advanced');
const bucketsThatGoToPdp = ['mls_id', 'address'];

function MultiboxAdvanced({
	endpoint,
	addSearchCriteria,
	mlsClass,
	icons,
	isCampaignBuilder,
	isShortcodeCreator,
	isMultipleStateMls,
}) {
	const [inputValue, setInputValue] = useState('');

	function onChange(selections, actionType) {
		if (!selections.length) {
			return;
		}

		const selection = selections[0];

		if (bucketsThatGoToPdp.includes(selection.type) && !isCampaignBuilder && !isShortcodeCreator) {
			window.open(selection.value.url, '_blank').focus();
			return;
		}

		// 'name' is the "easy name" of the search field.
		let name = selection.type;
		let value = selection.value;
		let label = labelMap[selection.type];
		if (name === 'mls_id') {
			value = selection.value.mlsId;
		} else if (name === 'address') {
			value = selection.value.fullAddress;
		} else if (name === 'school') {
			name = selection.value.type;
			value = selection.value.value;
			label = labelSubTypeMap[selection.value.type];
		}
		if (isMultipleStateMls && ['city', 'school_district'].includes(name)) {
			const stringValue = name === 'school_district' ? selection.value.value : selection.value;
			const [itemValue, stateOrProvince] = stringValue.split(/, ?/)
			addSearchCriteria({
				name,
				label,
				display: itemValue,
				value: itemValue,
			});
			addSearchCriteria({
				name: 'state',
				label: 'State',
				display: stateOrProvince,
				value: stateOrProvince,
			});
		} else {
			addSearchCriteria({
				name,
				label,
				display: value,
				value,
			});
		}
	}

	const components = {
		DropdownIndicator: HiddenComponent,
		IndicatorSeparator: HiddenComponent,
		GroupHeading: GroupHeadingMaker(icons),
		Option: OptionMaker(bucketsThatGoToPdp, mlsClass, icons),
	}
	// Hide the menu unless there is a search term.
	if (!inputValue) {
		components.Menu = HiddenComponent;
	}

	return <>
		<AsyncSelect
			isClearable={true}
			isMulti
			defaultOptions={[]}
			// We use the callback form of this prop, as opposed to returning a promise, just for the sake of being able
			// to debounce it.
			loadOptions={(inputValue, cb) => debouncedLoadOptionsCallback(inputValue, endpoint, labelMap, mlsClass, cb)}
			placeholder={'City, zip, school, subdivision, address, MLS ID'}
			inputValue={inputValue}
			onInputChange={setInputValue}
			onChange={onChange}
			value={null}
			components={components}
			noOptionsMessage={noOptionsMessage}
			className="ifound-react-select-multibox-advanced-container"
			classNamePrefix="ifound-react-select-multibox-advanced"
		/>
	</>
}

MultiboxAdvanced.propTypes = {
	endpoint: PropTypes.string.isRequired,
	addSearchCriteria: PropTypes.func.isRequired,
	mlsClass: PropTypes.string.isRequired,
	icons: PropTypes.shape({
		active: PropTypes.string.isRequired,
		closed: PropTypes.string.isRequired,
		subject: PropTypes.string.isRequired,
	}).isRequired,
	isCampaignBuilder: PropTypes.bool.isRequired,
	isShortcodeCreator: PropTypes.bool.isRequired,
	isMultipleStateMls: PropTypes.bool,
};

export default MultiboxAdvanced;
