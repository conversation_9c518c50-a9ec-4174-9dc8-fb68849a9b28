jQuery(document).ready(function($) {
	$( '#contact_id' ).autocomplete({
		source: new_contact.contacts_json
	});

	// Clear the selected contact ID. What they've typed doesn't matter, we only want to consider what they pick from
	// the autocomplete drop-down.
	$( "#contact_autocomplete" ).on('input', function() {
		$( "#contact_id" ).val('');
		$('.no_contact_selected_warning').show();
		$('.no_mphone_warning').hide();
		ifound_unpopulateEmailsTo();
		ifound_unpopulateToSmss();
	});
	$( "#contact_autocomplete" ).autocomplete({
		minLength: 0,
		source: new_contact.contacts_json,
		delay: 0,
		select: function( event, ui ) {
			setContact(ui.item.key);
		},
	});
	function setContact(contactId) {
		var contact = new_contact.contacts_json.find(x => x.key === contactId);
		$( "#contact_autocomplete" ).val( contact.value );
		$( "#contact_id" ).val( contact.key );
		$(window).trigger('ifound:chose-contact-address', [{
			id: contact.key,
			address: contact.address,
			owned_listings: contact.owned_listings,
		}]);
	}
	function checkForContactId() {
		var contactId = $('#contact_id').val();
		if (contactId) {
			contactId = parseInt(contactId, 10);
			setContact(contactId);
		}
	}

	// Run this once on page load to see if we should pre-load a contact.
	checkForContactId();
});
