<?php

namespace Profound\API;

class API {
	private $name;
	private $version;
	private $format;

	public static function from_string($api_string) {
		$api = new self();
		$api->parse_api_string($api_string);
		return $api;
	}

	public static function explicit($name, $version, $format) {
		$api = new self();
		$api->setName($name);
		$api->setVersion($version);
		$api->setFormat($format);
		return $api;
	}

	public function setName($name) {
		$this->name = $name;
	}

	public function getName() {
		return $this->name;
	}

	public function setVersion($version) {
		$this->version = $version;
	}

	public function getVersion() {
		return $this->version;
	}

	public function setFormat($format) {
		$this->format = $format;
	}

	public function getFormat() {
		return $this->format;
	}

	// $api_string is expected to come from an HTTP header
	public function parse_api_string($api_string) {
		preg_match('/(.*)-(.*)\+(.*)/', $api_string, $matches);
		if (isset($matches[0])) {
			$this->setName($matches[1]);
			$this->setVersion($matches[2]);
			$this->setFormat($matches[3]);
		}
	}

	public function get_api_string() {
		return "{$this->getName()}-{$this->getVersion()}+{$this->getFormat()}";
	}
}