/* -->ASIC Advantage Site Styles<-- */
/* -->Last Modified 071009<-- */

body, p, td {
	font: 12px/14px Arial, Helvetica, sans-serif;
	color: #333;
}
h1, h2, h3, h4, h5 {
	margin-bottom: 5px;
}
#wrapper {
	width: 1000px;
	margin-left: auto;
	margin-right: auto;
}
#container {
	width: 100%;
	border: 1px solid #000000;
	background: #FFFFFF;
}
.layout_sep {
	clear: both;
}
img, a img, table {
	border: 0;
}
a:link, a:visited { 
	font: bold 12px/12px Arial, Helvetica, sans-serif;
	color: #242e3f;
}
a:hover {
	color: #333333;
	text-decoration: none;
}
.float_r,
.float_right {
	float: right;
}
.float_l,
.float_left {
	float: left;
}
#header {
	height: 50px;
}
#server_status {
	float: right;
	width: 460px;
}
	

/* --- Form Styles --------------------------------------------------------- */
.form_r_col > input, input.txt, textarea.txt, select.txt, checkbox.txt {
	font: 12px/14px Arial, Helvetica, sans-serif;
	color: #333333;
	background: #f4f4f4 url(/images/innershadow.gif) top left no-repeat;
	border: 1px solid #999999;
	padding-left: 2px;
}
input.txt_req, textarea.txt_req, select.txt_req, checkbox.txt_req {
	font: 12px/14px Arial, Helvetica, sans-serif;
	color: #333333;
	background: #FFFFCC url(/images/innershadow.gif) top left no-repeat;
	border: 1px solid #999999;
	padding-left: 2px;
}
input.txt_er, textarea.txt_er, select.txt_er, checkbox.txt_er {
	font: 12px/14px Arial, Helvetica, sans-serif;
	color: #333333;
	background: #FFCCCC url(/images/innershadow.gif) top left no-repeat;
	border: 1px solid #999999;
	padding-left: 2px;
}
input.btn {
	font: bold 12px/14px Arial, Helvetica, sans-serif;
	color: #FFFFFF;
	background: #50618F;
	border: 1px outset #515151;
	padding: 0 2px 0 2px;
	margin-top: 20px;
}
input:focus {
	border: 1px solid #333333;
}
.form_table,
.list_table {
	width: 100%;
}
.form_table th,
.list_table th {
	text-align: left;
	border-bottom: 1px solid #999;
	font: 12px/14px Arial, Helvetica, sans-serif;
	font-weight:bold;
	color: #333;
	background: #fff;
}
.form_table th {
	padding: 2px 10px;
}
.list_table th {
	padding: 4px 10px;
}
.form_table td {
	padding: 2px 10px;
}
.list_table td {
	padding: 6px 10px;
}
.host_form_table td {
	padding: 4px 4px;
}
.list_table td.td_underline {
	border-bottom: 1px solid #999;
}
td.form_l_col {
	text-align: right;
	width: 150px;
}
td.form_r_col {
	text-align: left;
}
.host_form_table td.form_l_col {
	width: 15%;
}
.host_form_table td.form_r_col {
	width: 30%;
}
label.error { 
	float: none;
	display:block;
	color: red; 
}

#form-buttons {
	text-align: center;
	margin-top: 8px;
}

/* --- Header Styles --------------------------------------------------------- */
#header {
	width: 100%;
	height: 40px;
}

/* --- Content Styles --------------------------------------------------------- */
#content {
	width: 100%;
	background: #FFFFFF;
}
#menu_section {
	width: 149px;
	float: left;
	background: #3a3a3a;
	border-right: 1px solid #000000;
	border-bottom: 1px solid #000000;
}
#body_section {
	width: 789px; /*709px;*/
	float: left;
	margin: 20px;
}
#index_body_section {
	width: 789px; /*709px;*/
	float: left;
	margin: 20px 0 20px 20px;
}
#index_body_section #body_content {
	float: left;
}
#body_header{
	padding-bottom: 10px;
	margin-bottom: 20px;
	border-bottom: 3px solid #e9e9e9;
}
#body_title{
	/*float: left;*/
}
#body_title_img{
	float: right;
}
.body_list_lt {
	background: #FFFFFF;
}
.body_list_drk {
	background: #f6f6f6;
}
.info_bubble {
	position: absolute; 
	display: none; 
	border: 1px solid #5871B3; 
	background-color: #E6E6E6; 
	width: 240px;  
	padding: 10px;
}
.info_bubble img {
	float: right;
}
#pricebook {
	width: 708px;
	height: 480px;
	overflow: scroll;
}
#pricebook td, #volpoint td {
	border: 1px solid #e9e9e9;
	padding: 2px;
}
#pricebook tr.table_header, #volpoint tr.table_header{
	background: #99CCFF;
}
.form_group {
	width: 600px;
	background: #f6f6f6;
	border: 1px solid #e9e9e9;
	padding: 10px;
	margin-bottom: 10px;
}
.form_group ul {
	list-style: none;
	margin: 0;
	padding: 0;
	padding: 0 0 0 20px;
}
td.action_rule {
	border-bottom: 4px solid #39F;
}

/* --- Alert Styles --------------------------------------------------------- */
.alert_errors {
	color: #C00;
}
.alert_msg {
	color: #C00;
}
ul.alert_errors,
ul.alert_msg {
	list-style: none;
	margin: 0;
	padding: 0;
}

/* --- Message Styles --------------------------------------------------------- */
#message_section {
	font: bold 14px/14px Arial, Helvetica, sans-serif;
	color: #b2b2b2;
	float: right;
	width: 149px;
	margin: 0 0 20px 0;
	padding-bottom: 100px;
	text-align: center;
	border-left: 1px solid #e9e9e9;
}
.message {
	width: 135px;
	padding: 5px 5px 10px 5px;
	margin: 10px 2px 0 2px;
	text-align: left;
	background: #f6f6f6;
}
.message_title {
	float: left;
	width: 115px;
	padding-bottom: 6px;
	font: bold 12px/14px Arial, Helvetica, sans-serif;
	color: #242e3f;
}
.message_close {
	float: right;
}
.message_body {
	clear: both;
	font: 10px/12px Arial, Helvetica, sans-serif;
	color: #959595;
}


/* --- Navigation Styles --------------------------------------------------------- */
#navigation {
	border-right: 1px solid #525252;
	border-bottom: 1px solid #525252;
	padding: 20px 0 30px 15px;
}
.nav_header {
	font: bold 14px/14px Arial, Helvetica, sans-serif;
	color: #c4d8ff;
}
#navigation ul {
	list-style: none;
	margin: 0;
	padding: 0;
	padding: 8px 0 20px 10px;
}
#navigation li {
	display: block;
	margin-bottom: 2px;
}
#navigation a:link, #navigation a:visited {  
	font: bold 11px/11px Arial, Helvetica, sans-serif;
	color: #FFFFFF;
	text-decoration: none;
}
#navigation a:hover {
	color: #999999;
}


/* --- Text only Styles --------------------------------------------------------- */

.title { 
	font: bold 24px/24px Arial, Helvetica, sans-serif;
	color: #b2b2b2;
}
a.title:link, a.title:visited { 
	font: bold 24px/24px Arial, Helvetica, sans-serif;
	color: #b2b2b2;  
}
a.title:hover { 
	color: #666666;
	text-decoration: none;
}


.subtitle { 
	font: bold 16px/16px Arial, Helvetica, sans-serif;
	color: #242e3f;
	padding-bottom: 10px;
}
a.subtitle:link, a.subtitle:visited { 
	font: bold 16px/16px Arial, Helvetica, sans-serif;
	color: #242e3f;  
}
a.subtitle:hover { 
	color: #666666;
	text-decoration: none;
}


.caption { 
	font: 9px/11px Arial, Helvetica, sans-serif;
	color: #242e3f;
}
a.caption:link, a.caption:visited { 
	font: 9px/11px Arial, Helvetica, sans-serif;
	color: #242e3f;  
}
a.caption:hover {
	text-decoration: none;
	color: #666666;
}


/* --- Project pulldown Styles ------------------------------------------------ */

.proj_concern {
	background-color: #feffdd;
	color: #666;
}



/* --- Footer Styles --------------------------------------------------------- */

#footer { 
	text-align: center;
	padding-top: 10px;
	font: 9px/11px Arial, Helvetica, sans-serif;
	color: #FFFFFF;
}
a.footer:link, a.footer:visited { 
	font: 9px/11px Arial, Helvetica, sans-serif;
	color: #FFFFFF;  
}
a.footer:hover {
	text-decoration: none;
	color: #999999;
}

/* --- Deployment Tab ------------------------------------------------------- */

.deploy-table div {
	display: table-cell;
}

.deploy-host {
	width: 300px;
}

/* --- Form fields ---------------------------------------------------------- */

.access_company, .access_address, .access_address2, .access_emailaddress, .access_fullname {
	width: 250px;
}

.access_account_id, .access_apikey {
	width: 180px;
}	

.access_zipcode, .access_office_id, .access_member_id {
	width: 100px;
}

.form_table textarea {
	width: 350px;
	height: 50px;
}

.deploy-btns {
	margin-top: 12px;
	font-size: 13px;
}
