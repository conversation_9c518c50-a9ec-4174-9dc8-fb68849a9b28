<?
/**
 * iFound_whats_my_payment
 *
 * Display What's my Payment Forms.
 *
 * @package iFOUND
 * @since 1.0.8
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );
 
class iFound_whats_my_payment extends WP_Widget {
	
		
	public function __construct(){

		parent::__construct( 
			false, 
			"iFound What's My Payment", 
			array(
			'description' => 'Add the Whats My Payment forms the property detail pages. This will only display on active listings.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {

		echo $args['before_widget']; ?>

		<div class="ifound-whats-my-payment">

			<div class="widgets-wrap">

				<? if( defined( 'DOING_DETAILS' ) ) {
		
					do_action( 'ifound_whats_my_payment' ); 
				
				} else {
				
					_e( 'This widget will only display on Property Detail Pages.', 'ifound' );
				
				} ?>

			</div>

		</div><?

		echo $args['after_widget'];

	}
	
}
