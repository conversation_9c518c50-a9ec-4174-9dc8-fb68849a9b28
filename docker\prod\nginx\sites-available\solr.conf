upstream solr_web {
    server solr:8983;
}

server {
    listen 80;
    server_name solr.ifoundagent.com;

    location / {
        return 301 https://$host$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
}

server {
    listen 443 ssl http2;
    server_name solr.ifoundagent.com;

    access_log /var/log/nginx/solr.log;
    error_log /var/log/nginx/solr-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifoundagent.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifoundagent.com/privkey.pem;

    include /etc/nginx/snippets/authelia-location.conf;

    location / {
        include /etc/nginx/snippets/proxy.conf;
        include /etc/nginx/snippets/authelia-authrequest.conf;

        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://solr_web;

        proxy_pass_request_headers on;
    }
}
