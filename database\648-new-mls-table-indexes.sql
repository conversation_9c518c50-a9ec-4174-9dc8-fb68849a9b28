-----------------
-- Listings table
-----------------

-- Listing ID
create index field_LIST_105 on paaraz_property_A (field_LIST_105);
-- City
alter table paaraz_property_A change column field_LIST_39 field_LIST_39 varchar(100);
create index field_LIST_39 on paaraz_property_A (field_LIST_39);
-- Contingent status
alter table paaraz_property_A change column field_LIST_19 field_LIST_19 varchar(100);
create index field_LIST_19 on paaraz_property_A (field_LIST_19);
-- The original column type is string! Change to a numerical format to allow normal numerical sorting.
alter table paaraz_property_A change column field_LIST_22 field_LIST_22 decimal(14,0);
-- Subdivision
alter table paaraz_property_A change column field_LIST_77 field_LIST_77 varchar(100);
create index field_LIST_77 on paaraz_property_A (field_LIST_77);
-- Square feet
alter table paaraz_property_A change column field_LIST_48 field_LIST_48 int;
-- Bathrooms
alter table paaraz_property_A change column field_LIST_67 field_LIST_67 smallint;

---------------
-- Images table
---------------
-- I'm using varchar(76) because that's how LIST_1 is defined in the listings table.
alter table paaraz_images change column MLS_ID MLS_ID varchar(76);
create index MLS_ID on paaraz_images (MLS_ID);