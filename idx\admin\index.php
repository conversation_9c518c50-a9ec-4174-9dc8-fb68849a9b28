<?php
session_start();
require_once("../includes/classes/class-query_db.php");
include_once("../includes/classes/class-alerts.php");
include_once("../includes/classes/class-serial.php");
require_once("IDXVideo.php");
require_once("../includes/classes/IDXAdmin.php");

$serial_obj = new serial();
$alert_obj = new Alerts();
$db_obj = new sql_query();
$IDXAdmin_obj = new IDXVideos();

$log_array = $db_obj->get_process_sql('SELECT * FROM `action_log` WHERE 1 ORDER BY `action_startdate` DESC LIMIT 10');
//print "<pre>"; print_r($log_array); print "</pre>";

$access_array = $db_obj->get_process_sql("SELECT * FROM `access` WHERE 1 ORDER BY `access_company`");
//print "<pre>"; print_r($access_array); print "</pre>";

$idx = new IDXAdmin('../config.ini');

// Time Duration
function duration($seconds){
	if($seconds <= 60){
		// Show seconds
		//$duration = number_format(($seconds), 2).'sec';
		$duration = date('00:00:s', $seconds);
	}else
	if($seconds > 60 && $seconds <= 3600){
		// Show minutes
		//$duration = number_format(($seconds / 60), 2).'min';
		$duration = date('00:i:s', $seconds);
	}else
	if($seconds > 3600){
		// Show hours
		$duration = '0'.floor($seconds / 3600).date(':i:s', $seconds);
	}
	return $duration;
	
}

?>
<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>ProFound IDX Administration</title>
<link type="text/css" href="css/styles.css" rel="stylesheet" />
<link type="text/css" href="css/redmond/jquery-ui-1.8.16.custom.css" rel="stylesheet" />
<link type="text/css" href="css/poplight_styles.css" rel="stylesheet" />
<script type="text/javascript" src="js/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="js/jquery.validate.js"></script>
<script type="text/javascript" src="js/functions.js"></script>
<script type="text/javascript">
$(function(){
	
	$( "#log_entries" ).accordion({ 
    	active: false
	});
	
	$( "#access_users" ).accordion({ 
    	active: false
	});
	
	$('#tabs').tabs();
	
	/*
	window.setInterval(server_uptime, 5000);
	function server_uptime() { 
		$.get('ajax/system_uptime.php', function(data) { 
			$('#uptime').html(data); 
		}); 
	}
	*/
	
	$( ".button" ).button();
	
});
</script>
</head>

<body>
<div id="wrapper">
	<div id="header">
    	<div id="server_status"><p><strong>Server:</strong> <span id="uptime"><?php system('uptime'); ?></span></p></div>
		<h1>ProFound IDX Admin</h1>
    </div>
    
    
<?php
// Extenuate alerts Messages
echo $alert_obj->getErrors($_SESSION['error']); 
echo $alert_obj->getAlerts($_SESSION['msg']); 
?> 
    
	<p class="alert_msg"></p>
		<div id="tabs">
        
			<ul>
				<li><a href="#access_tab">Customer Access</a></li>
                <li><a href="#video_tab" onclick="getvideos()">Videos</a></li>
				<li><a href="#deploy_tab">Deployment</a></li>
				<li><a href="#log_tab">RETS Sync Log</a></li>
			</ul>
            
            
			<div id="access_tab">
            	<h2>API Access Customers</h2>
                
            	<div class="float_right"><a href="#" rel="0" class="poplight_access"><img src="/images/icon_add_sm.gif">Add Customer</a></div>
                
                <table class="list_table" cellspacing="0">
                      <tr>
                        <th>Company Name</th>
                        <th>Edit</th>
	                      <th>FTP Settings</th>
                        <th>Category SEO</th>
                        <th>Results SEO</th>
                        <th>Prop SEO</th>
                        <th>Status</th>
                      </tr>
                      <tr>
                        <td colspan="6" style="height:8px;"></td>
                      </tr>
<? //}

$bgc = 1;
if (is_array($access_array)) {
	foreach ($access_array as $id => $result) { 
?>
                      <tr class="<?=($bgc % 2 != 0)? 'body_list_drk' : 'body_list_lt';?>">
                        <td><?=$result['access_company'];?>
                        <a href="#access" onClick="return overlay(this, 'access_bubble_id_<?=$id;?>', 'leftbottom')" style="cursor:help"><img src="/images/info_bubble_l.gif" alt="Access" title="Access Description" align="absmiddle"></a>
							<div id="access_bubble_id_<?=$id;?>" class="info_bubble" >
							<div align="right"><a href="#" onClick="overlayclose('access_bubble_id_<?=$id;?>'); return false"><img src="/images/icon_delete.gif" alt="Close" title="Close"></a></div>
                          	<? 
							echo ($result['access_company'])? '<strong>'.$result['access_company'].'</strong><br>' : '';
							echo ($result['access_fullname'])? $result['access_fullname'].'<br>' : '';
							
							echo ($result['access_address'])? $result['access_address'].'<br>' : '';
							echo ($result['access_address2'])? $result['access_address2'].'<br>' : '';
							echo ($result['access_city'])? $result['access_city'] : '';
							echo ($result['access_city'] && $result['access_state'])? ', ' : ' ';
							echo ($result['access_state'])? $result['access_state'].' ' : '';
							echo ($result['access_zipcode'])? $result['access_zipcode'].'<br>' : '';
							
							echo ($result['access_phone'])? $result['access_phone'].'<br>' : '';
							echo ($result['access_emailaddress'])? '<a href="mailto:'.$result['access_fullname'].'<'.$result['access_emailaddress'].'>">'.$result['access_emailaddress'].'</a><br>' : '';
							
							?>
                            </div></td>
	                      
	                      
                        <td style="width:240px"><a href="#?w=500" rel="<?=$id;?>" vv="<?=$serial_obj->get_serialized_array(array('action'=>'edit', 'fwd'=>'access'));?>" class="poplight_access"><img src="/images/icon_detail.gif" alt="Edit" title="Edit Access"></a></td>

	                      <td style="width:60px"><a href="#?w=500" rel="<?=$id;?>" vv="<?=$serial_obj->get_serialized_array(array('action'=>'edit', 'fwd'=>'access'));?>" class="poplight_ftp"><img src="/images/icon_detail.gif" alt="Edit" title="Edit FTP Settings"></a></td>
	                      
                        <td style="width:60px"><a href="#?w=500" rel="<?=$id;?>" vv="<?=$serial_obj->get_serialized_array(array('action'=>'edit', 'fwd'=>'access'));?>" class="poplight_cat"><img src="/images/icon_detail.gif" alt="Edit" title="Edit Cateogry SEO"></a></td>
	                      
                        <td style="width:60px"><a href="#?w=500" rel="<?=$id;?>" vv="<?=$serial_obj->get_serialized_array(array('action'=>'edit', 'fwd'=>'access'));?>" class="poplight_result"><img src="/images/icon_detail.gif" alt="Edit" title="Edit Search Results SEO"></a></td>
	                      
                        <td style="width:60px"><a href="#?w=500" rel="<?=$id;?>" vv="<?=$serial_obj->get_serialized_array(array('action'=>'edit', 'fwd'=>'access'));?>" class="poplight_prop"><img src="/images/icon_detail.gif" alt="Edit" title="Edit Property SEO"></a></td>
                        <td><?=($result['access_status'] == '1')? 'Active' : 'Inactive';?></td>
                      </tr>
<?		$bgc++;
	}
}else{
?>
                      <tr>
                        <td colspan="6">None to list.</td>
                      </tr>
<? } ?>
                    </table>
                
            </div><!-- #access_tab -->
            
            <div id="video_tab">
                <h2>Videos</h2>
                <div id="video_message"></div>
	            <table>
		            <tr>
			            <td style="vertical-align: top">
							<h3>Agent Property Videos</h3>
							<form id="videos" onsubmit='getvideos()'>
								 <select id="video-form-companies" onchange="getvideos()">
									<?php foreach ($access_array as $id=>$company): ?>
										<option value="<?php echo $id; ?>"><?=$company['access_company'];?></option>
									<?php endforeach; ?>
								 </select>
								<div id="video_content"></div>
							</form>
						</td>
			            <td style="vertical-align: top">
							<h3>Agent Global Video</h3>
							<form id="global_video" onsubmit='getglobalvideo()'>
								<div id="global_video_content"></div>
							</form>
						</td>
		            </tr>
		        </table>
            </div>


			<div id="deploy_tab">
				<form id="deploy_form">
					Deploy plugin to the following sites:
					<div class="deploy-btns">
						<button onclick="setChecks(true); return false;">Select All</button>
						<button onclick="setChecks(false); return false;">Select None</button>
					</div>
					<table class="deploy-table">
						<?php foreach ($idx->getHosts() as $host): ?>
						<tr class="deploy-table">
							<td>
								<input type="checkbox" access_id="<?= $host->access_id ?>" checked />
							</td>
							<td>
								<?= $host->access_company ?>
							</td>
							<td>
								<a target="_blank" href="http://<?= $host->domain ?>"><?= $host->domain ?></a>
							</td>
							<td id="dmsg-<?= $host->access_id?>" class="deploy-message"></td>
						</tr>
						<?php endforeach ?>
					</table>
					<input id="deploy-btn" type="button" value="Deploy" onClick="deployPlugin() "/>
				</form>
			</div>
            
			<div id="log_tab">
              <h2>RETS Synchronization Log</h2>
              <p>This is a set of 10 latest log entries from the ProFound IDX Synchronization System.</p>
            
			  <div id="log_entries">
<?php
// This is the sync script log history ----------------------------
$i = 1;
if(is_array($log_array)){
	$entry_count = count($log_array);
	foreach ($log_array as $id=>$result){
		
		$start_date = ($result['action_startdate'])? date("m/d/y g:ia", $result['action_startdate']) : ' - ';
		$end_date = ($result['action_enddate'])? date("m/d/y g:ia", $result['action_enddate']) : ' - ';
		$seconds = ($result['action_startdate'] && $result['action_enddate'])? ($result['action_enddate'] - $result['action_startdate']) : 0;
		$duration = duration($seconds);
		#$process_status = ($seconds == 0)? script_process($result['action_field_id'], $result['action_startdate']) : '';
		$status = ($result['action_status'])? 'Complete' : 'Incomplete';
		//$select = ($entry_count == $i)? ' class="selected"' : '';
		$select = ($i == 1)? ' class="selected"' : '';
		
		echo '<h3'.$select.'><a href="#">'.$result['action_field'].' - '.$result['mls'].' : '.$start_date.' - '.$end_date.' ('.$duration.')</a></h3>';
		echo '<div><pre>'.$result['action_notes'].' '.'</pre><strong>Status:</strong> '.$status.'</div>';
	
		$i++;
	}
}
		echo '<h3'.$select.'><a href="#"> Refresh Scripts </a></h3>';
				  
		# FIXME: should extract this from the config.ini file
		$mlslist = array('armls' => 'ARMLS', 'trendmls' => 'TREND', 'brightmls' => 'BRIGHTMLS');
				  
		?>
		<div>
              <p>Manually update data from the RETS service.</p>
			<? foreach ($mlslist as $mls => $mlsname): ?> 
				<h3><?= $mlsname ?></h3>
              <p>
	            <a href="/cron/rets.php?cmd=fields&mls=<?= $mls ?>" target="_blank" class="button">Get Fields and Options</a> 
			    <a href="/cron/rets.php?cmd=props&images=1&mls=<?= $mls ?>" target="_blank" class="button">Get Property Data</a> 
				<a href="/cron/rets.phpcmd=verify&mls=<?= $mls ?>" target="_blank" class="button">Verify Members</a>
              </p>
			<? endforeach ?>
		</div>
				</div>
    
            </div>
            
            
		</div>
        
</div>
<?php
$_SESSION['error'] = NULL;
$_SESSION['msg'] = NULL;
?>
</body>
</html>