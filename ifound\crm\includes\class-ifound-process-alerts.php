<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

/**
 * Process Alerts Class
 *
 * @since 2.5.3
 */

class iFoundProcessAlerts extends iFoundCrm {
	use UtilTrait;
	use NewHooklessTrait;

	private static $time_to_add_in_seconds = 60 * 15;
	private static $most_recently_run_option_name_prefix = 'ifound_most_recently_run_instant_update_save_this_id';

	private $post_type	= 'save_this';
	private $taxonomy	= 'save_type';
	private $taxonomy2	= 'campaign_status';

	/**
	 * init iFOUND_process_alerts class.
	 *
	 * @since 2.5.3
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 2.5.3
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_filter('cron_schedules', array($this, 'cron_job_recurrence'));
			add_action( 'admin_init', array( $this, 'set_process_alerts_cron' ), 99 );

			add_action('process_alerts', function() {
				// 42408653 is the case number
				$smooth_option_name = 'ifound_42408653_has_cron_been_smoothed';
				$has_cron_been_smoothed = get_option($smooth_option_name);
				if ($has_cron_been_smoothed === 'yes') {
					// Schedule the next one for X minutes out.
					$cron_time = time() + static::$time_to_add_in_seconds;
					wp_schedule_single_event($cron_time, 'process_alerts');
				} else {
					// Unschedule the recurring one.
					$next_scheduled = wp_next_scheduled('process_alerts');
					if ($next_scheduled) {
						wp_unschedule_event($next_scheduled, 'process_alerts');
					}

					// Schedule the next one in a non-recurring fashion, somewhere between normal and 2x out. To
					// determine where in between, base it on a numerical hash of the domain.
					$host = get_option('siteurl');
					// To generate a numerical hash, I used this idea:
					// https://stackoverflow.com/a/3379493/135101
					$bignum = hexdec(substr(sha1($host), 0, 15));
					$extra_seconds = $bignum % static::$time_to_add_in_seconds;
					$cron_time = time() + static::$time_to_add_in_seconds + $extra_seconds;
					wp_schedule_single_event($cron_time, 'process_alerts');
					update_option($smooth_option_name, 'yes');
					die();
				}
			});
			add_action('process_alerts', array($this, 'process_all_alert_types'));
		}
	}

	public function process_all_alert_types() {
		// We only need this check for a while after deploying a change that upgrades campaigns. But after a short time,
		// we no longer need it and it's nothing but CPU overhead.
		// $should_process_alerts = !(
		// 	   as_has_scheduled_action(iFoundSaveThisUpgrader::$ifound_upgrade_campaigns, null, 'ifound')
		// 	|| as_has_scheduled_action(iFoundSaveThisUpgrader::$ifound_upgrade_campaign, null, 'ifound')
		// 	|| as_has_scheduled_action(iFoundSaveThisUpgrader::$ifound_rollback_campaigns, null, 'ifound')
		// 	|| as_has_scheduled_action(iFoundSaveThisUpgrader::$ifound_rollback_campaign, null, 'ifound')
		// );
		$should_process_alerts = true;
		if ($should_process_alerts) {
			$this->process_campaign_alerts();
			// We are disabling property alerts until further notice.
			// If you re-enable them, be sure to read my note in the method first.
			// $this->process_property_alerts();
			$this->process_instant_updates();
		}
	}

	public function set_process_alerts_cron() {
		if (!wp_next_scheduled('process_alerts')) {
			// Schedule the event once. When the process_alerts hook runs, it will schedule itself again.
			$cron_time = time() + static::$time_to_add_in_seconds;
			wp_schedule_single_event($cron_time, 'process_alerts');
		}
	}

	/**
	 * Process Property Alerts
	 *
	 * Check to see if any emails need sent. Then check to see if the campaign is over.
	 *
	 * @since 2.5.3
	 * @since 2.5.21 Remove search-update from tax_query.
	 * @since 2.5.44 Require a contact ID. Remove unsubscribe requirement.
	 * @since 3.0.0 Use get_posts() to query campaigns.
	 * @since 3.6.2 Use string as param for iFoundIdx::pdp_request()
	 */

	public function process_property_alerts() {



		// Reminder!!
		// We disabled sending property alerts around 2024-03-14.
		// If we ever decide to re-enable them, there's not technically anything we need to do, but we probably should.
		// Property alert campaigns don't have a to_email postmeta value; we just assume the contact's primary email
		// address. To make property alerts consistent with search campaigns, we should have a do_email=yes postmeta on
		// the campaign, as well as a to_email="<EMAIL>,<EMAIL>" postmeta. The idea would be
		// that, somday, as a new feature, the user could decide to also get text alerts, or zero kinds of alerts.



		$args = array(
			'post_type' 		=> $this->post_type,
			'post_status'       => 'publish',
			'posts_per_page' 	=> -1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> $this->taxonomy,
					'field'    	=> 'slug',
					'terms'    	=> array( 'property-alert' )
				),
				array(
					'taxonomy'	=> $this->taxonomy2,
					'field'    	=> 'slug',
					'terms'    	=> array( 'active' )
				)
			),
			'meta_query' => array(
				array(
					'key'     	=> 'contact_id',
					'compare' 	=> 'EXISTS',
				)
			)
		);

		$save_this_ids = get_posts( $args );

		foreach ( $save_this_ids as $save_this_id ) :

			if( $params = get_post_meta( $save_this_id, 'params', true ) ) {

				if( $listing = $this->pdp_request( $params['mls_id'] ) ) {
					if ($listing->expired) {
						do_action('ifound_update_campaign_status', $save_this_id, 'inactive',
							'Listing was removed by MLS');
						continue;
					}

					$mod_time  = strtotime($listing->ModificationTimestamp);
					if ($this->is_reso_web_api()) {
						$datetime = new \DateTime($listing->ModificationTimestampZ, new \DateTimeZone('UTC'));
						$datetime->setTimeZone($this->util()->wp_timezone());
						// This is a little weird. We're getting a timestamp, which is always supposed to be in UTC, but
						// we're adding the offset based on the local timezone. Yeah, it's weird, but we do it to line
						// up with the fact that here in Wordpress we're saving off the local time below, and need to
						// compare that with the time coming from the server.
						$mod_time = $datetime->getTimestamp() + $datetime->getOffset();
					}
					$last_time = strtotime( get_post_meta( $save_this_id, 'last_time', true ) );

					// We only want to send once per day. Using the following, it's once per 24 hours. Close enough?
					if ($mod_time && $last_time && $mod_time > ($last_time + 60 * 60 * 24)) {
						update_post_meta( $save_this_id, 'last_time', current_time( 'mysql' ) );
						do_action( 'ifound_email', $save_this_id, 'property_alert' );
					}

					/** We notified the contact about the status change. Now we can change the status. */
					if( $listing->ListStatus == 'Closed' || $listing->ListStatus == 'Sold' ) {

						do_action( 'ifound_update_campaign_status', $save_this_id, 'inactive' );

					}

				}


			}

		endforeach;

	}

	/**
	 * Process Campaign Alerts
	 *
	 * This decides if the email should be sent. Then decides if it should be deleted or scheduled for another email.
	 *
	 * @since 1.0.0
	 * @since 2.1.0 Evaluate the dates and recurring differently for campaign deletion.
	 * @since 2.5.3 Rename to process_campaign_alerts. Move to iFOUND_process_alerts class.
	 * @since 2.5.44 Require a contact ID. Remove unsubscribe requirement. Do not allow empty campaign status.
	 * @since 3.0.0 Use get_posts() to query campaigns.
	 */

	private function process_campaign_alerts() {
		$args = array(
			'post_type' 		=> $this->post_type,
			'post_status'       => 'publish',
			'posts_per_page' 	=> -1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> $this->taxonomy,
					'field'    	=> 'slug',
					'terms'    	=> array( 'property-alert' ),
					'operator'  => 'NOT IN'
				),
				array(
					'taxonomy'	=> $this->taxonomy,
					'operator'  => 'EXISTS'
				),
				array(
					'taxonomy'	=> $this->taxonomy2,
					'field'    	=> 'slug',
					'terms'    	=> array( 'active' )
				)
			),
			'meta_query' => array(
				array(
					'key'     	=> 'contact_id',
					'compare' 	=> 'EXISTS',
				),
			),
			// Order by the oldest next_time. This way, if the cron job goes past its timeout and is killed, the next
			// time it's run, it will start with the campaigns that were neglected.
			// A note here: At first I couldn't get this working, and when I traced through and saw the SQL that was
			// generated by WP and ran it in MySQL Workbench, it gave error 1055: "Expression #1 of ORDER BY clause is
			// not in GROUP BY clause...". That's because in my MySQL Workbench session, the @@sql_mode included
			// ONLY_FULL_GROUP_BY, which is not compatible with Wordpress. I looked at @@sql_mode on our web host, and
			// it didn't have ONLY_FULL_GROUP_BY, so I thought that was the problem. However, the real problem was that
			// the sort is DESC by default and I needed to change it to ASC. I think WP changes the @@sql_mode on its
			// own to what it needs.
			'orderby' => 'meta_value',
			// The default order is DESC so we force ASC
			'order' => 'ASC',
			// This meta_key goes hand in hand with the orderby
			'meta_key' => 'next_time',
		);

		$save_this_ids = get_posts( $args );
		foreach ($save_this_ids as $save_this_id) {
			$this->process_campaign_alert($save_this_id);
		}
	}

	public function process_campaign_alert($save_this_id, $ignoreTimeOfNextScheduledSend = false) {
		if ($next_time = get_post_meta($save_this_id, 'next_time', true)) {
			if ($ignoreTimeOfNextScheduledSend || $this->current_time() > strtotime($next_time)) {
				$do_email_yes = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id);
				$do_sms_yes = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id);
				if (!($do_email_yes || $do_sms_yes)) {
					do_action('ifound_update_campaign_status', $save_this_id, 'inactive',
						'Campaign was active but was not set to send emails or SMSs');
					return;
				}
				if ($this->maybe_disable_campaign_due_to_unengaged($save_this_id)) {
					return;
				}
				iFoundSaveThis::new_hookless()->send_campaign_now($save_this_id);
				if (!$this->maybe_disable_campaign_due_to_overactivity($save_this_id)) {
					iFoundSaveThis::new_hookless()->update_campaign_status_or_next_time($save_this_id);
				}
			}
		}
	}

	/**
	 * Process Instant Updates
	 *
	 * This decides if the email should be sent. Then decides if it should be deleted or scheduled for another email.
	 *
	 * @since 2.5.3
	 * @since 2.5.44 Require a contact ID. Remove unsubscribe requirement.
	 * @since 3.0.0 Use get_posts() to query campaigns.
	 */

	public function process_instant_updates() {
		$this->do_instant_updates(iFoundEmail::$INSTANT_UPDATE);
		$this->do_instant_updates(iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED);
	}

	private function do_instant_updates($type) {
		$kabob_type = str_replace('_', '-', $type);
		$args = array(
			'post_type' 		=> $this->post_type,
			'post_status'       => 'publish',
			'posts_per_page' 	=> -1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> $this->taxonomy,
					'field'    	=> 'slug',
					'terms'    	=> [$kabob_type],
					'operator'  => 'IN'
				),
				array(
					'taxonomy'	=> $this->taxonomy2,
					'field'    	=> 'slug',
					'terms'    	=> array( 'active' )
				)
			),
			'meta_query' => array(
				array(
					'key'     	=> 'contact_id',
					'compare' 	=> 'EXISTS',
				),
			),
			'orderby' => 'ID',
			'order' => 'ASC',
		);

		$save_this_ids = get_posts( $args );

		// If there are too many campaigns to handle in a single cron job, the cron will die and the campaigns at the
		// end of the list will never get run. So, as we iterate over the campaigns, we'll store off the most recently
		// run. That way, the next time the cron runs, we can look at which one was most recently run, and put all the
		// ones before it at the back of the line.
		$recently_run_option_name = static::$most_recently_run_option_name_prefix . '_' . $type;
		$most_recently_run_save_this_id = intval(get_option($recently_run_option_name, 0));
		$index = array_search($most_recently_run_save_this_id, $save_this_ids);
		if ($index !== false) {
			$subset = array_splice($save_this_ids, 0, $index + 1);
			$save_this_ids = [...$save_this_ids, ...$subset];
		}

		foreach ($save_this_ids as $save_this_id) {
			$this->do_instant_update($save_this_id, $type);
			update_option($recently_run_option_name, $save_this_id);
		}

		// If we've gotten to this point, the cron hasn't died, and we've gotten through all campaigns, so we don't need
		// to store which campaign was most recently run.
		delete_option($recently_run_option_name);
	}

	// This function should be thought of as private. It's marked public only for the sake of manual testing.
	public function do_instant_update($save_this_id, $type, $min_date_for_debug = null) {
		$do_email_yes = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id);
		$do_sms_yes = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id);
		if (!($do_email_yes || $do_sms_yes)) {
			return;
		}
		if ($results = $this->get_instant_update_listings($save_this_id, $type, $min_date_for_debug)) {
			if ($this->maybe_disable_campaign_due_to_unengaged($save_this_id)) {
				return;
			}
			if ($do_email_yes) {
				do_action('ifound_email', $save_this_id, $type, $results);
			}
			if ($do_sms_yes) {
				iFoundSms::new_hookless()->send_instant_update($save_this_id, $type, $results);
			}

			// Reminder: we used to call $this->update_campaign_status_or_next_time() here, which would push the
			// next time the campaign's periodic email would go out another month (or whatever) into the future.
			// This was unexpected behavior because a periodic email should go out periodically.

			$this->maybe_disable_campaign_due_to_overactivity($save_this_id);
			// I'm assuming that if we're debugging, we'll want to be able to run it multiple times, so don't update
			// the "last time".
			if (!$min_date_for_debug) {
				update_post_meta($save_this_id, 'last_time_' . $type, current_time('mysql'));
			}
		}
	}

	function convert_local_mysql_datetime_str_to_utc( $local_datetime ) {
		// Get the site's timezone
		$timezone_string = get_option( 'timezone_string' );
		if ( ! $timezone_string ) {
			$gmt_offset = get_option( 'gmt_offset' );
			$timezone_string = timezone_name_from_abbr( '', $gmt_offset * 3600, 0 );
		}

		// Create DateTime from local time
		$dt = new DateTime( $local_datetime, new DateTimeZone( $timezone_string ) );

		// Convert to UTC
		$dt->setTimezone( new DateTimeZone( 'UTC' ) );

		// Return formatted string
		return $dt->format( 'Y-m-d\TH:i:s\Z' );
	}

	/**
	 * New Listings
	 *
	 * Request new listings since our last email.
	 * NOTE: We need to search both min amd max list_date.
	 * I noticed in ARMLS, some of the list dates were in the future.
	 * This allowed emails to be sent every cron run.
	 * Giving this a max time created a range and resolved that issue.
	 *
	 * @since 2.5.64
	 * @since 5.0.4  Check $results->query->numFound isset and is_numeric in case of IDX server error.
	 *
	 * @param  int  $save_this_id The ID if the save this.
	 * @return bool $new_listings Bool if new listings are available or not.
	 */

	public function get_instant_update_listings( $save_this_id, $type, $min_date_for_debug = null ) {

		$last_time = get_post_meta( $save_this_id, 'last_time', true );
		$params    = get_post_meta( $save_this_id, 'params', true );
		$contact_id = get_post_meta($save_this_id, 'contact_id', true);

		// Check that we've sent out the initial campaign before we send any instant updates, which would matter if the
		// campaign were scheduled for a future start date.
		if( $params && $last_time ) {
			// In theory we shouldn't need to geocode here. Campaigns done by radius should all have a nearby radius
			// with a lat/lng for a centroid. But if the agent did not put an address on the contact, then we could get
			// to this situation. We might as well do this unless we find it hurting performance.
			$params = $this->util()->maybe_geocode_campaign($save_this_id);

			// We'll use a virtual field that will be interpreted by the server and will use our history tracker
			// tables.
			$field = $type;
			$last_time_for_type = get_post_meta( $save_this_id, 'last_time_' . $type, true );
			if (!$last_time_for_type) {
				$last_time_for_type = $last_time;
			}
			$params[$field]['min'] = $last_time_for_type;
			if ($min_date_for_debug) {
				$params[$field]['min'] = $min_date_for_debug;
			}
			$params[$field]['max'] = current_time( 'mysql' );
			foreach(['min', 'max'] as $key) {
				$params[$field][$key] = $this->convert_local_mysql_datetime_str_to_utc($params[$field][$key]);
			}
			if ($type === 'instant_update_recently_closed') {
				$params['list_status'] = ['Closed', 'Sold'];
				unset($params['days_back']);
			}

			$results = $this->process_input($params, [
				'stats' => [
					'show' => 'no_stats',
				],
				'trace' => [
					'save_this_id' => $save_this_id,
					'instant_update_type' => $type,
				],
				'email_content_options' => [
					'should_include' => true,
					'options' => [
						'base_url' => site_url('/'),
						'contact_id' => $contact_id,
						'email_type' => $type,
					],
				],
			]);

			if( isset( $results->query->numFound ) && is_numeric( $results->query->numFound  ) && $results->query->numFound > 0 ) {
				return $results;
			}
		}
		return false;
	}

	/**
	 * Cron Job Recurrance
	 *
	 * Schedule a cron to run every 10 minutes.
	 *
	 * @since 1.0.0
	 * @since 2.5.3 Move to iFOUND_process_alerts class
	 */

	public function cron_job_recurrence( $schedules ) {
		$schedules['10min'] = array(
			'display' => __( 'Every 10 Minutes', 'ifound' ),
			'interval' => 600,
		);
		return $schedules;
	}

	private function email_about_disabling($subject, $message) {
		iFoundEmail::new_hookless()->email_ifoundagent_adhoc('<EMAIL>', '<EMAIL>',
			$subject, $message);
	}

	private function disable_campaign_due_to_email_overactivity(int $save_this_id): void {
		$post = get_post($save_this_id);
		$crm_id = $this->crm_id_from_user_id($post->post_author);
		$contact_name = get_post(get_post_meta($save_this_id, 'contact_id', true))->post_title;
		$subject = 'A campaign was disabled due to sending too many emails: ' . $contact_name;
		$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
		$message = <<<EOF
		On your website powered by iFoundAgent, we recently disabled a campaign that was sending out too many emails.

		Contact name: {$contact_name}
		To perhaps restart the campaign, click the following link: {$post_link}

		Q&A

		Q: How many emails are too many?
		A: In order to protect our excellent email service rating, we limit all search-based campaigns (buyer and homeowner) to 10 emails per rolling 24 hour period.

		Q: How can I be sure not to send too many emails for this campaign?
		A: There are several things that influence how many emails are sent.
		   1. The primary reason why campaigns are disabled is because the search used in the search campaign is too broad and the campaign is sending instant updates. For example, if the campaign filters are only for a city with many listings, with nothing to further filter the number of results, that will likely lead to many instant updates. If the search parameters are too broad, they can be modified and the campaign can be re-enabled (see below). You can also just start a new search campaign with narrower filters.
		   2. Ensure you want to send Instant Updates. We recommend them for both homeowner neighborhood campaigns as well as buyer campaigns. Disabling Instant Updates is a guaranteed way to prevent the campaign from sending too many emails. To disable Instant Updates, visit the campaign link above, and in the Campaign Type section, uncheck the Instant Update and Instant Update Recently Closed checkboxes, then press UPDATE CAMPAIGN.
		   3. Ensure the To Email(s) on the campaign are appropriate. We've seen situations where agents have the contact's Email and Email 2 as the same email address, and even the spouse's email address as the same. This causes duplicate and wasted emails.

		Q: How can I re-enable the campaign?
		A: Visit the campaign link above, and change the Status toggle control to "Enable". You might want to wait 24 hours from when the campaign was disabled to re-enable it, to ensure it is not disabled again on the next email, based on the threshhold.

		Additional questions? Please reach <NAME_EMAIL>.

		-iFoundAgent Support
		EOF;
		iFoundEmail::new_hookless()->agent_email($subject, $message, ['crm_id' => $crm_id]);

		$activity_log_message = 'Disabled campaign due to too many emails per time period';
		do_action('ifound_update_campaign_status', $save_this_id, 'inactive', $activity_log_message);
		$message = "This campaign just sent an email that pushed it over the overactivity threshhold: {$post_link}";
		$message .= "\n\nIt has been disabled. The agent was alerted via email.";
		$message .= "\n\n";
		$subject = 'Too many emails per time period for campaign';
		$this->email_about_disabling($subject, $message);
	}

	private function disable_campaign_due_to_sms_overactivity(int $save_this_id): void {
		$post = get_post($save_this_id);
		$crm_id = $this->crm_id_from_user_id($post->post_author);
		$contact_name = get_post(get_post_meta($save_this_id, 'contact_id', true))->post_title;
		$subject = 'A campaign was disabled due to sending too many text messages: ' . $contact_name;
		$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
		$message = <<<EOF
		On your website powered by iFoundAgent, we recently disabled a campaign that was sending out too many text messages.

		Contact name: {$contact_name}
		To perhaps restart the campaign, click the following link: {$post_link}

		Q&A

		Q: How many text messages are too many?
		A: In order to protect our excellent text message service rating, we limit all search-based campaigns (buyer and homeowner) to 10 text messages per rolling 24 hour period.

		Q: How can I be sure not to send too many text messages for this campaign?
		A: There are several things that influence how many text messages are sent.
		   1. The primary reason why campaigns are disabled is because the search used in the search campaign is too broad and the campaign is sending instant updates. For example, if the campaign filters are only for a city with many listings, with nothing to further filter the number of results, that will likely lead to many instant updates. If the search parameters are too broad, they can be modified and the campaign can be re-enabled (see below). You can also just start a new search campaign with narrower filters.
		   2. Ensure you want to send Instant Updates. We recommend them for both homeowner neighborhood campaigns as well as buyer campaigns. Disabling Instant Updates is a guaranteed way to prevent the campaign from sending too many text messages. To disable Instant Updates, visit the campaign link above, and in the Campaign Type section, uncheck the Instant Update and Instant Update Recently Closed checkboxes, then press UPDATE CAMPAIGN.
		   3. Ensure the To Phone Number(s) on the campaign are appropriate. We've seen situations where agents have the contact's Mobile Phone and Spouse Mobile Phone as the same number. This causes duplicate and wasted text messages.

		Q: How can I re-enable the campaign?
		A: Visit the campaign link above, and change the Status toggle control to "Enable". You might want to wait 24 hours from when the campaign was disabled to re-enable it, to ensure it is not disabled again on the next text message, based on the threshhold.

		Additional questions? Please reach <NAME_EMAIL>.

		-iFoundAgent Support
		EOF;
		iFoundEmail::new_hookless()->agent_email($subject, $message, ['crm_id' => $crm_id]);

		$activity_log_message = 'Disabled campaign due to too many text messages per time period';
		do_action('ifound_update_campaign_status', $save_this_id, 'inactive', $activity_log_message);
		$message = "This campaign just sent an SMS that pushed it over the overactivity threshhold: {$post_link}";
		$message .= "\n\nIt has been disabled. The agent was alerted via email.";
		$message .= "\n\n";
		$subject = 'Too many SMSs per time period for campaign';
		$this->email_about_disabling($subject, $message);
	}

	private function disable_campaign_due_to_unread_emails(int $save_this_id): void {
		$post = get_post($save_this_id);
		$crm_id = $this->crm_id_from_user_id($post->post_author);
		$contact_name = get_post(get_post_meta($save_this_id, 'contact_id', true))->post_title;
		$subject = 'A campaign was disabled due to too many unread emails: ' . $contact_name;
		$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
		$message = <<<EOF
		On your website powered by iFoundAgent, we recently disabled a campaign that has sent out too many emails that have gone unread.

		Contact name: {$contact_name}
		To perhaps restart the campaign, click the following link: {$post_link}

		Q&A

		Q: Why was my campaign disabled?
		A: The campaign has sent out too many emails that haven't been read, which indicates that the customer isn't interested in the campaign, isn't getting the email because of a bad email address, or the email is going to spam and they haven't seen it. To protect your reputation and ours, we disable such campaigns.

		Q: Why was the campaign disabled now?
		A: We were about to send another email for the campaign, but before we did, we checked the campaign's history, and deemed that sending another would be too many.

		Q: How many emails are too many?
		A: 15 unread emails in a row is too many. Or, regardless of the number, if it's been more than 5 times the campaign's period since the most recent read email, it's been too long. For example, if your campaign is set to run monthly, then if 5 months have passed since an email has been read, it's been too long.

		Q: How can I re-enable the campaign?
		A: Visit the campaign link above, and change the Status toggle control to "Enable". Once the campaign has been re-enabled, the counter or clock will start over from that point.

		Additional questions? Please reach <NAME_EMAIL>.

		-iFoundAgent Support
		EOF;
		iFoundEmail::new_hookless()->agent_email($subject, $message, ['crm_id' => $crm_id]);

		$activity_log_message = 'Disabled campaign due to too many unread emails';
		do_action('ifound_update_campaign_status', $save_this_id, 'inactive', $activity_log_message);
		$message = "This campaign was about to send an email after too many unread emails: {$post_link}";
		$message .= "\n\nIt has been disabled. The agent was alerted via email.";
		$message .= "\n\n";
		$subject = 'Too many unread emails for campaign';
		$this->email_about_disabling($subject, $message);
	}

	private function disable_campaign_due_to_unclicked_smss(int $save_this_id): void {
		$post = get_post($save_this_id);
		$crm_id = $this->crm_id_from_user_id($post->post_author);
		$contact_name = get_post(get_post_meta($save_this_id, 'contact_id', true))->post_title;
		$subject = 'A campaign was disabled due to too many unclicked text messages: ' . $contact_name;
		$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
		$message = <<<EOF
		On your website powered by iFoundAgent, we recently disabled a campaign that has sent out too many text messages that have gone unclicked.

		Contact name: {$contact_name}
		To perhaps restart the campaign, click the following link: {$post_link}

		Q&A

		Q: Why was my campaign disabled?
		A: The campaign has sent out too many text messages that haven't been read, which indicates that the customer isn't interested in the campaign or isn't getting the text message because of a wrong number. To protect your reputation and ours, we disable such campaigns.

		Q: Why was the campaign disabled now?
		A: We were about to send another text message for the campaign, but before we did, we checked the campaign's history, and deemed that sending another would be too many.

		Q: How many text messages are too many?
		A: 15 unclicked text messages in a row is too many. Or, regardless of the number, if it's been more than 5 times the campaign's period since the most recent clicked text message, it's been too long. For example, if your campaign is set to run monthly, then if 5 months have passed since a text message has been clicked, it's been too long.

		Q: How can I re-enable the campaign?
		A: Visit the campaign link above, and change the Status toggle control to "Enable". Once the campaign has been re-enabled, the counter or clock will start over from that point.

		Additional questions? Please reach <NAME_EMAIL>.

		-iFoundAgent Support
		EOF;
		iFoundEmail::new_hookless()->agent_email($subject, $message, ['crm_id' => $crm_id]);

		$activity_log_message = 'Disabled campaign due to too many unclicked text messages';
		do_action('ifound_update_campaign_status', $save_this_id, 'inactive', $activity_log_message);
		$message = "This campaign just sent an email that pushed it over the threshhold: {$post_link}";
		$message .= "\n\nIt has been disabled. The agent was alerted via email.";
		$message .= "\n\n";
		$subject = 'Too many emails per time period for campaign';
		$this->email_about_disabling($subject, $message);
	}

	// If we are sending out too many emails, disable this campaign and alert iFoundAgent staff.
	// Returns true if the campaign was sending out too many emails and was disabled, otherwise false.
	// Now includes SMSs too.
	private function maybe_disable_campaign_due_to_overactivity(int $save_this_id): bool {
		if ($this->is_site_exempt('overactivity')) {
			return false;
		}
		$do_email_yes = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id);
		if ($do_email_yes &&
			iFoundEmailTrackingPixel::new_hookless()->has_campaign_sent_out_too_many_emails_recently($save_this_id)
		) {
			$this->disable_campaign_due_to_email_overactivity($save_this_id);
			return true;
		}
		$do_sms_yes = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id);
		if ($do_sms_yes && iFoundSms::new_hookless()->has_campaign_sent_out_too_many_smss_recently($save_this_id)) {
			$this->disable_campaign_due_to_sms_overactivity($save_this_id);
			return true;
		}
		return false;
	}

	// Returns true if the campaign was disabled, otherwise false.
	private function maybe_disable_campaign_due_to_unengaged(int $save_this_id): bool {
		if ($this->is_site_exempt('unreads')) {
			return false;
		}
		$do_email_yes = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id);
		$do_sms_yes = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id);

		if ($do_email_yes && $do_sms_yes) {
			$has_sent_too_many_unread_emails = iFoundEmailTrackingPixel::new_hookless()
				->has_campaign_sent_out_too_many_unread_emails($save_this_id);
			$has_sent_too_many_unclicked_smss = iFoundSms::new_hookless()
				->has_campaign_sent_out_too_many_unclicked_smss($save_this_id);

			if ($has_sent_too_many_unread_emails && $has_sent_too_many_unclicked_smss) {
				// The point is to disable the campaign. Our message could either be due to emails or SMSs, but I figure
				// SMSs is the more impactful statistic here, so I'm choosing it.
				$this->disable_campaign_due_to_unclicked_smss($save_this_id);
				return true;
			}

			// Disable one channel or the other (email or SMS), but don't disable the campaign.
			if ($has_sent_too_many_unread_emails) {
				$activity_log_action = 'Disabled email channel due to too many unread emails';
				$activity_log_message = 'Did not disable campaign because SMS channel is still active';
				iFoundSharedCampaign::new_hookless()->disable_email($save_this_id, $activity_log_message);
				update_post_meta($save_this_id, iFoundSharedCampaign::$DO_EMAIL_KEY, false);
				do_action('ifound_activity_log', $save_this_id, $activity_log_action, $activity_log_message);

				$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
				$message = 'This campaign was about to send an email that WOULD HAVE pushed it over the threshhold:';
				$message .= "\n\n{$post_link}";
				$message .= "\n\nHowever, because the campaign is also sending out text messages, we did not disable"
					. ' the campaign. We did not alert the agent.';
				$message .= "\n\n";
				$subject = 'Warning: Too many unread emails per time period for campaign';
				$this->email_about_disabling($subject, $message);

				return true;
			} else if ($has_sent_too_many_unclicked_smss) {
				$activity_log_action = 'Disabled SMS channel due to too many unclicked SMSs';
				$activity_log_message = 'Did not disable campaign because email channel is still active';
				iFoundSharedCampaign::new_hookless()->disable_sms($save_this_id, $activity_log_message);
				update_post_meta($save_this_id, iFoundSharedCampaign::$DO_SMS_YES, false);
				do_action('ifound_activity_log', $save_this_id, $activity_log_action, $activity_log_message);

				$post_link = $this->util()->build_post_href($save_this_id, 'edit', ['ensure_href_is_url' => true]);
				$message = 'This campaign was about to send an SMS that WOULD HAVE pushed it over the threshhold:';
				$message .= "\n\n{$post_link}";
				$message .= "\n\nHowever, because the campaign is also sending out emails, we did not disable"
					. ' the campaign. We did not alert the agent.';
				$message .= "\n\n";
				$subject = 'Warning: Too many unclicked SMSs per time period for campaign';
				$this->email_about_disabling($subject, $message);

				return true;
			}
		} else {
			if ($do_email_yes &&
				iFoundEmailTrackingPixel::new_hookless()->has_campaign_sent_out_too_many_unread_emails($save_this_id)
			) {
				$this->disable_campaign_due_to_unread_emails($save_this_id);
				return true;
			}
			if ($do_sms_yes &&
				iFoundSms::new_hookless()->has_campaign_sent_out_too_many_unclicked_smss($save_this_id)
			) {
				$this->disable_campaign_due_to_unclicked_smss($save_this_id);
				return true;
			}
		}

		return false;
	}

	private function is_site_exempt($type) {
		// Dev sites are exempt.
		if (!$this->is_php_env_production()) {
			return true;
		}
		if ($type === 'unreads') {
			$exempt_sites = ['metgrouprealestate.com'];
			if (in_array($this->util()->get_host(), $exempt_sites)) {
				return true;
			}
		}
		return false;
	}
}
