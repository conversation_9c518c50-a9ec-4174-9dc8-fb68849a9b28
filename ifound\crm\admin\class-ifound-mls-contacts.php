<?php

defined('ABSPATH') or die('You do not have access!');

require_once(plugin_dir_path( __FILE__ ) . '../../vendor/woocommerce/action-scheduler/action-scheduler.php');

class iFoundMlsContacts extends iFoundContacts {
	use UtilTrait;
	use NewHooklessTrait;

	private static $option_state_name = 'ifound_mls_contacts';
	private static $utc_format_string = 'Y-m-d\TH:i:s\Z';
	private static $spark_datetime_format = 'Y-m-d\TH:i:sP';
	private static $nonce_base = 'ifound_mls_contacts_nonce';
	private static $menu_slug = 'ifound_mls_contacts';
	private static $last_contact_imported_modtime_str = 'last_contact_imported_modtime';
	private static $utc_dtz;

	public static $mls_id_meta_key = 'ifound_mls_id';

	public static function static_init() {
		static::$utc_dtz = new DateTimeZone('UTC');
	}

	// Here's what the state looks like:
	// ifound_mls_contacts[CRM_ID]
	//   status: never, reauthorized, pending, importing, done, error
	//   message: "Imported initiated"
	//            "Imported 57 out of 103 contacts"
	//            "Imported 103 out of 103 contacts"
	//            "Error: XYZ happened"
	//            "Error: must reauthorize"
	//   action_id: the Action Scheduler job ID
	//   started_at: ...
	//   updated_at: ...
	//   last_contact_imported_at: date
	//   owner: <-- This is needed due to the concept of the shared admin. There could be multiple admins,
	//              each with their own MLS account, and if one began an import, and later another admin did an
	//              import, all contacts from both would be imported.
	//     sub: (their MLS id)
	//   history: Array<{ datetime: date string, id: int, name: string }>
	//   access_token: xyz
	//   access_token_expiry: date
	//   refresh_token: xyz
	private $state;
	private $exception_from_code_exchange;
	// The job run by Action Scheduler won't have the agent/CRM ID available. So we store it off when we enqueue the job
	// and store it here when the job runs.
	private $crm_id;
	// Same with user ID
	private $user_id;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_init', [$this, 'admin_init']);
			add_action('admin_menu', array($this, 'contacts_menu'));
			add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
			add_action('ifound_mls_contacts_run_job', array($this, 'run_job'), 10, 1);

			if (!$this->is_php_env_production()) {
				// I got this from https://wordpress.org/support/topic/scheduled-actions-menu-disappears/
				// Let's not do it in production.
				add_filter('wp_mail_smtp_tasks_admin_hide_as_menu', '__return_false');
			}
		}
	}

	public function admin_scripts() {
		wp_register_style( 'ifound_mls_contacts_css', plugins_url( 'css/mls-contacts.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		wp_register_script('ifound_mls_contacts_js', plugins_url( 'js/mls-contacts.js', __FILE__ ), ['jquery', 'jquery-ui-accordion'], iFOUND_PLUGIN_VERSION);
	}

	public function admin_init() {
		$this->state = $this->get_option_for_agent(static::$option_state_name);
		if (!$this->state) {
			$this->state = [];
			$this->update_states([
				'status' => 'never',
				'message' => 'Contacts have never been imported from the MLS',
			]);
		}

		$code = $_GET['code'];
		if ($code) {
			$nonce = $_GET['nonce'];
			$this->exchange_code_for_token_and_maybe_redirect($code, $nonce);
		} else if ($_POST['do_import'] === 'do_import') {
			$this->enqueue_job();
			wp_redirect(iFound::current_url());
			die();
		} else if ($_POST['request_authorization'] === 'request_authorization') {
			$url = $this->get_authorization_url();
			wp_redirect($url);
			die();
		} else if ($_POST['remove_tokens'] === 'remove_tokens') {
			$this->remove_access_tokens();
			$this->redirect_to_reload_and_die();
		} else if ($_POST['reset_last'] === 'reset_last') {
			$this->update_states([static::$last_contact_imported_modtime_str => null]);
			$this->redirect_to_reload_and_die();
		}
	}

	private function redirect_to_reload_and_die() {
		// Now redirect so the user doesn't stay on a POSTed page.
		$url = $this->get_page_url();
		wp_redirect($url);
		die();
	}

	private function remove_access_tokens() {
		$this->update_states([
			'access_token' => null,
			'access_token_expiry' => null,
			'refresh_token' => null,
		]);
	}

	private function get_now_utc_date_formatted($timestamp = null) {
		if ($timestamp === null) {
			$timestamp = time();
		}
		return gmdate(static::$utc_format_string, $timestamp);
	}

	private function enqueue_job() {
		// Because the job callback will be called using call_user_func_array(), if we want to use an associative array,
		// we need to wrap the associative array in an outer array.
		$args = [[
			'crm_id' => $this->crm_id(),
			'user_id' => get_current_user_id(),
		]];
		$action_id = as_enqueue_async_action('ifound_mls_contacts_run_job', $args, 'ifound');
		$this->update_states([
			'status' => 'pending',
			'message' => 'Import job pending; it should start within 2 minutes. Refresh the page to see updates.',
			'action_id' => $action_id,
		]);
	}

	public function run_job($args) {
		$this->crm_id = $args['crm_id'];
		$this->user_id = $args['user_id'];
		$this->state = $this->get_option_for_agent(static::$option_state_name);

		$this->update_states([
			'status' => 'importing',
			'started_at' => $this->get_now_utc_date_formatted(),
			'message' => 'Import job started',
		]);
		try {
			$this->download_and_import_contacts();
			$this->update_states(['status' => 'done']);
			// No need to update the message here, as I expect it to have been done above, with something like
			// "Imported 123 out of 123 contacts."
		} catch (\Exception $exception) {
			$this->update_states([
				'status' => 'error',
				'message' => $exception->getMessage(),
			]);
		}
	}

	private function make_nonce_transient_name($nonce) {
		$transient_name = $this->get_option_name_for_agent(static::$nonce_base . '_' . $nonce . '_');
		return $transient_name;
	}

	private function maybe_make_dev_url($url) {
		if (!$this->is_php_env_production()) {
			$url_parts = parse_url($url);
			$origin = $url_parts['scheme'] . '://' . $url_parts['host'];
			if ($url_parts['port']) {
				$origin .= ':' . $url_parts['port'];
			}
			$dev_origin = $cfg['api_base_url'];
			$url = str_replace($origin, $dev_origin, $url);
			return $url;
		}
		return $url;
	}

	private function exchange_code_for_token_and_maybe_redirect($code, $nonce) {
		$transient_name = $this->make_nonce_transient_name($nonce);
		$transient_value = get_transient($transient_name);
		if (!$transient_value) {
			$message = "The nonce of '{$nonce}' was not issued by this server, or has expired";
			$this->exception_from_code_exchange = new Exception($message);
			return;
		}
		$mls_config = $this->get_mls_contacts_config_for_mls();
		$url = $mls_config['token_endpoint'];
		$url = $this->maybe_make_dev_url($url);
		$data = null;
		try {
			$data = $this->fetch_json($url, [
				'headers' => [
					'apikey' => $this->api_secret(),
				],
				'body'    => [
					'code' => $code,
				],
			], 'POST');
		} catch (Exception $e) {
			$this->exception_from_code_exchange = $e;
			return;
		}
		$id_token_decoded = $this->decode_jwt($data['id_token']);
		$new_owner = [
			// Reminder: initially my idea was to also store the 'name' field here, and that way in our error message we
			// could say exactly who the previous owner that imported contacts was. However, if we only use the oauth
			// scope of 'openid' and don't include 'profile', then we don't get the name in the id_token. We could get
			// the name by using the v1/my/account endpoint, but it seems like overkill to bother.
			'sub' => $id_token_decoded['sub'],
		];
		$previous_owner = $this->state['owner'];
		if ($previous_owner && ($previous_owner['sub'] !== $new_owner['sub'])) {
			$message = "Access token owner does not match previous owner";
			$this->exception_from_code_exchange = new Exception($message);
			return;
		}
		$this->update_states([
			// In case we are re-authorizing, ensure there is no error status.
			'status' => 'reauthorized',
			'message' => 'Ready',

			'owner' => $new_owner,
		]);
		$this->save_access_token_data($data);
		delete_transient($transient_name);
		$redirect_url = remove_query_arg(['code', 'state', 'nonce']);
		wp_redirect($redirect_url);
		die();
	}

	private function save_access_token_data($data) {
		$timestamp = time() + $data['expires_in'];
		$this->update_states([
			'access_token' => $data['access_token'],
			'access_token_expiry' => $this->get_now_utc_date_formatted($timestamp),
			'refresh_token' => $data['refresh_token'],
		]);
	}

	private function decode_jwt($token) {
		$parts = explode('.', $token);
		$payload = $parts[1];
		$replaced = str_replace('_', '/', str_replace('-', '+', $payload));
		$decoded = json_decode(base64_decode($replaced), true);
		return $decoded;
	}

	private function download_and_import_contacts() {
		$skiptoken = null;
		$page_size = 25;
		$page = 0;
		$expected_pages_str = '?';
		$total_str = '?';
		$done = false;
		$imported_count = 0;
		while (!$done) {
			$page++;
			$this->update_states(['message' => "Downloading page $page out of $expected_pages_str;"
				. " total contacts expected: $total_str"]);
			$access_token = $this->get_or_fetch_access_token();
			$url = $this->get_mls_contacts_config_for_mls()['contacts_endpoint'];
			$last_contact_imported_at_string = $this->state[static::$last_contact_imported_modtime_str] ?: '1970-01-01T00:00:00Z';
			$select = join(',', [
				'Id',
				'PrimaryEmail',
				'ModificationTimestamp',
				'DisplayName',
				'GivenName',
				'FamilyName',
				'ReferredBy',
				'HomePhoneNumber',
				'MobilePhoneNumber',
				'HomeStreetAddress',
				'HomeLocality',
				'HomePostalCode',
				'HomeRegion',
				'HomeStreetAddress',
				'SecondaryEmails',
				'Tags',
			]);
			$url = add_query_arg([
				'_select' => $select,
				'_limit' => $page_size,
				'_page' => $page,
				'_pagination' => 1,
				// TODO: If I do "Tags Ne 'ifa-exclude'", then only contacts that have a tag, but not that tag, are
				//   returned. I don't know how to say "with a tag or without, but not that one". So for now I filter
				//   below here in PHP.
				// '_filter' => "ModificationTimestamp Ge $last_contact_imported_at And Tags Ne 'ifa-exclude'",
				'_filter' => "ModificationTimestamp Ge {$last_contact_imported_at_string}",
				// As I mention elsewhere, Notes from Flexmls don't seem to come through, so this isn't needed.
				// '_expand' => 'Notes',
				'_orderby' => 'ModificationTimestamp',
			], $url);
			if ($skiptoken) {
				$url = add_query_arg('_skiptoken', $skiptoken, $url);
			}
			try {
				$data = $this->fetch_json($url, [
					'headers' => [
						'Authorization' => "Bearer {$access_token}",
						// Do we need a better user agent string? See ideas here:
						// https://developers.whatismybrowser.com/learn/browser-detection/user-agents/user-agent-best-practices
						'User-Agent'    => 'iFoundAgent',
					],
				]);
			} catch (Exception $e) {
				$this->handle_revoked_auth($e);
			}
			$data = $data['D'];
			$success = $data['Success'];
			if (!$success) {
				// TODO: don't expose this error. Let's have a private vs public message.
				// TODO: check for the Errors key for further info
				throw new \Exception("Spark error ({$data['Code']}): {$data['Message']}");
			}
			$results = $data['Results'];
			$results = array_filter($results, function($x) {
				$lowercase_tags = array_map(function($tag) use ($x) { return strtolower($tag); }, $x['Tags']);
				$has_tag = in_array(iFoundCrm::$exclude_tag_name, $lowercase_tags, true);
				return !$has_tag;
			});
			$skiptoken = $data['SkipToken'];
			$total_str = $data['Pagination']['TotalRows'];
			$expected_pages_str = $data['Pagination']['TotalPages'];

			$imported_count += $this->import_contacts($results);

			// From my testing, there was not a SkipToken in the response.
			// $done = !!$skiptoken;
			// It is possible for CurrentPage to be greater than the TotalPages. Let's be safe and ensure we don't try
			// to download forever.
			$done = $page >= $data['Pagination']['TotalPages'];
		}
		$this->update_states(['message' => "Imported {$imported_count} out of {$imported_count} contacts"]);
	}

	private function fetch_json($url, $args = [], $http_method = 'GET') {
		// Just a reminder that Spark API calls return error info in their JSON. I was thinking about using it here but
		// am still worried it will expose sensitive info, so I won't bother for now.
		// See: http://sparkplatform.com/docs/supporting_documentation/standard_response_format
		return $this->util()->fetch_json($url, $args, $http_method);
	}

	private function get_or_fetch_access_token() {
		if ($this->is_expiry_still_valid($this->state['access_token_expiry'])) {
			$access_token = $this->state['access_token'];
			return $access_token;
		}
		$data = $this->refresh_token($this->state['refresh_token']);
		$this->save_access_token_data($data);
		return $data['access_token'];
	}

	private function handle_revoked_auth($e) {
		$previous = $e->getPrevious();
		if ($previous) {
			$code = $previous->getCode();
			if ($code === 401) {
				$was_auth_revoked = true;
			} else if ($code === 400) {
				$body = $previous->getMessage();
				$data = json_decode($body, true);
				if (!json_last_error()) {
					if ($data['error'] === 'invalid_grant') {
						$was_auth_revoked = true;
					}
				}
			}
			if ($was_auth_revoked) {
				$this->remove_access_tokens();
				throw new Exception('Your authorization has been revoked. You must re-authorize.');
			}
		}
		throw $e;
	}

	private function refresh_token($refresh_token) {
		$mls_config = $this->get_mls_contacts_config_for_mls();
		$url = $mls_config['refresh_token_endpoint'];
		$url = $this->maybe_make_dev_url($url);
		try {
			$data = $this->fetch_json($url, [
				'headers' => [
					'apikey' => $this->api_secret(),
				],
				'body'    => [
					'refresh_token' => $refresh_token,
				],
			], 'POST');
		} catch (Exception $e) {
			$this->handle_revoked_auth($e);
		}
		return $data;
	}

	private function convert_from_mls_datestring($mls_datestring) {
		// This is for Spark MLSs to start. As is everything so far I guess.
		$z = DateTime::createFromFormat(static::$spark_datetime_format, $mls_datestring)
			->setTimezone(static::$utc_dtz);
		return $z->format(static::$utc_format_string);
	}

	private function import_contacts($contacts_from_mls) {
		$args = iFoundJointContact::new_hookless()->get_all_contacts_args();
		$args['fields'] = 'ids';
		$contact_ids_from_db = get_posts($args);
		$contact_ids_from_db_by_mls_id = array_reduce($contact_ids_from_db, function($a, $contact_id) {
			$mls_id = get_post_meta($contact_id, static::$mls_id_meta_key, true);
			if ($mls_id) {
				$a[$mls_id] = $contact_id;
			}
			return $a;
		}, []);
		$post_author = iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id($this->user_id);
		$post_type = iFoundJointContact::new_hookless()->get_new_contact_type($this->user_id);
		$imported_count = 0;
		$history = $this->state['history'] ?: [];
		$mod_datetime_meta_key = 'ifound_mls_mod_datetime';
		foreach ($contacts_from_mls as $contact_from_mls) {
			$mls_id = $contact_from_mls['Id'];
			$this->update_states(['message' => 'Creating contact for MLS ID ' . $mls_id]);
			$has_email = !!$contact_from_mls['PrimaryEmail'];
			if (!$has_email) {
				continue;
			}
			$contact_id = $contact_ids_from_db_by_mls_id[$mls_id];
			$contact_mod_datetime = $this->convert_from_mls_datestring($contact_from_mls['ModificationTimestamp']);
			if ($contact_id) {
				$datetime = get_post_meta($contact_id, $mod_datetime_meta_key, true);
				if ($datetime === $contact_mod_datetime) {
					continue;
				}
			} else {
				$contact_id = wp_insert_post([
					'post_type'   => $post_type,
					'post_author' => $post_author,
					'post_title'  => $contact_from_mls['DisplayName'],
					'post_status' => 'publish',
				]);
				if (is_wp_error($contact_id)) {
					$wp_error = $contact_id;
					throw new Exception($wp_error);
				}
			}
			$required_fields = [
				'Id' => static::$mls_id_meta_key,
				'PrimaryEmail' => 'email',
				'GivenName' => 'fname',
				'FamilyName' => 'lname',
			];
			foreach ($required_fields as $mls_field => $ifound_field) {
				update_post_meta($contact_id, $ifound_field, $contact_from_mls[$mls_field]);
			}
			update_post_meta($contact_id, $mod_datetime_meta_key, $contact_mod_datetime);
			$optional_fields = [
				'ReferredBy' => 'acquired_from',
				// Don't use birthday. It's an arbitrary text field in Flexmls.
				// 'Birthday' => 'birthday',
				'HomePhoneNumber' => 'hphone',
				'MobilePhoneNumber' => 'mphone',
				// Just a reminder that the work number doesn't come through. We should probably report this to Spark.
				// 'WorkPhoneNumber' => 'wphone',
				// Hmm. There's no ability in Flexmls to add spouse info.
				// 'SpouseGivenName' => 'fname_spouse',
				// 'SpouseFamilyName' => 'lname_spouse',
				'HomeStreetAddress' => 'address',
				'HomeLocality' => 'city',
				'HomePostalCode' => 'zip',
				'HomeRegion' => 'state',
			];
			foreach ($optional_fields as $mls_field => $ifound_field) {
				if ($contact_from_mls[$mls_field]) {
					if ($mls_field === 'HomeStreetAddress') {
						$address_lines = explode("\n", $contact_from_mls[$mls_field]);
						update_post_meta($contact_id, $ifound_field, $address_lines[0]);
						array_shift($address_lines);
						update_post_meta($contact_id, 'address_2', join(' ', $address_lines));
					} else {
						update_post_meta($contact_id, $ifound_field, $contact_from_mls[$mls_field]);
					}
				}
			}
			if ($contact_from_mls['SecondaryEmails'] && count($contact_from_mls['SecondaryEmails'])) {
				update_post_meta($contact_id, 'email2', $contact_from_mls['SecondaryEmails'][0]);
			}
			// I'm not doing notes because Notes don't seem to come in from Spark. The field is always null for some
			// reason. If that ever gets fixed, then we need to reconsider using add_post_meta(), because
			// if ($contact_from_mls['Note']) {
			// 	add_post_meta($contact_id, 'notes', $contact_from_mls['Note']);
			// }
			// Update: I added a couple test notes to a contact in Flexmls, and using _expand=Notes in the Spark
			// API call doesn't error, but there is no Notes field on the contact, only a null Note field.
			// TODO: Look at $contact_from_mls['Notes']. It should be an array and we should use it to sync more
			// notes. But it's not documented on sparkplatform.com.
			$history[] = [
				'datetime' => $this->get_now_utc_date_formatted(),
				'id' => $contact_id,
				'name' => $contact_from_mls['DisplayName'],
			];
			$this->update_states([
				static::$last_contact_imported_modtime_str => $contact_mod_datetime,
				'history'                                  => $history,
			]);
			$imported_count++;
		}
		return $imported_count;
	}

	public function contacts_menu() {
		$mls_supports_syncing_contacts = !!$this->get_mls_contacts_config_for_mls();
		if ($mls_supports_syncing_contacts) {
			add_submenu_page(
				$this->crm_menu(),
				__('Import from MLS', 'ifound'),
				__('Import from MLS', 'ifound'),
				'crm_import',
				static::$menu_slug,
				array($this, 'mls_import_contacts_page')
			);
		}
	}

	private function display_error_page($ex) {
		?>
		<h2>There was an error</h2>
		<div><?= $ex->getMessage() ?></div>
		<div>
			<a href="<?= $this->get_page_url() ?>">Return to main import page</a>
		</div>
		<?php
	}

	private function get_datetime_from_utc_string($datetime_string) {
		return DateTime::createFromFormat(static::$utc_format_string, $datetime_string, static::$utc_dtz);
	}

	private function display_updated_at() {
		$timestamp = $this->get_datetime_from_utc_string($this->state['updated_at'])->getTimestamp();
		$updated_at_string = $this->pretty_date($timestamp, true, true);
		?>
		<div>Updated at <?= $updated_at_string ?></div>
		<?php
	}

	private function display_status() {
		?>
		<h2>Status: <?= $this->state['status'] ?></h2>
		<div><?= $this->state['message'] ?></div>
		<?php
	}

	public function mls_import_contacts_page() {
		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script('ifound_mls_contacts_js');
		wp_enqueue_style('ifound_mls_contacts_css');
		?>
		<div class="ifound_mls_contacts_page">
		<h1>Import Contacts from Your MLS</h1>
		<?php

		// Check out this cool technique. It helps me ensure I can close the div above. Compare to my previous commit.
		do {
			if (array_key_exists('show_history', $_GET)) {
				$this->display_history_page();
				break;
			}
			if ($this->exception_during_code_exchange) {
				$this->display_error_page($this->exception_from_code_exchange);
				break;
			}

			$mls = iFoundAdmin::mls_name();
			?>
			<?php

			$s = $this->state;
			if ($s['status'] === 'pending') {
				$this->display_status();
				$this->display_updated_at();
				break;
			} else if ($s['status'] === 'importing') {
				$job_status = iFoundActionScheduler::new_hookless()->get_action_scheduler_job_status($s['action_id']);
				if ($job_status !== 'in-progress') {
					?>
					<h2>Something went wrong during import</h2>
					<div>We're not sure if it completed, failed, or somewhere in between.</div>
					<div><NAME_EMAIL></div>
					<?php
					$this->display_updated_at();
					break;
				}
				$this->display_status();
				$this->display_updated_at();
				break;
			} else if ($s['status'] === 'done') {
				?>
				<h2>Status: successfully imported contacts</h2>
				<div><?= $s['message'] ?></div>
				<?php
				$this->display_updated_at();
			} else if ($s['status'] === 'error') {
				$this->display_status();
				$this->display_updated_at();
			}

			if ($s['access_token']) {
				$history_link = add_query_arg('show_history', 'true');
				?>
				<div style="margin-top: 10px;">
					<form method="POST">
						<button type="submit" class="button button-primary" name="do_import" value="do_import">
							Import contacts from <?= $mls ?></button>
					</form>
				</div>
				<div style="margin-top: 10px;">
					<a target="_blank" href="<?= $history_link ?>">View import history</a>
				</div>
				<?php
				if (iFoundAdmin::new_hookless()->is_user_staff()) {
					$this->display_staff_page_section();
				}
				?>
				<?php
			} else {
				?>
				<form method="POST">
					<div>
						In another browser tab, ensure you are logged into your MLS portal first.
					</div>
					<button
						type="submit"
						class="button button-primary"
						name="request_authorization"
						value="request_authorization"
					>
						Request authorization from <?= $mls ?>
					</button>
				</form>
				<?php
			}
		} while (false);
		?>
		</div>
		<?php
	}

	private function display_history_page() {
		?>
		<h2>Import History</h2>
		<table class="wp-list-table widefat striped">
			<thead>
			<tr>
				<th>Date</th>
				<th>Name</th>
			</tr>
			</thead>
			<tbody>
			<?
				if (!$this->state['history']) {
					?>
					<div>No contacts have been imported yet</div>
					<?
				} else {
					foreach (array_reverse($this->state['history']) as $x) {
						$timestamp = $this->get_datetime_from_utc_string($x['datetime'])->getTimestamp();
						$datetime_formatted = $this->pretty_date($timestamp, true, true);
						$link = get_edit_post_link($x['id']);
						?>
						<tr>
							<td><?= $datetime_formatted ?></td>
							<td>
								<a target="_blank" href="<?= $link ?>"><?= $x['name'] ?></a>
							</td>
						</tr>
						<?
					}
				}
			?>
			</tbody>
		</table>
		<?php
	}

	private function display_staff_page_section() {
		?>
		<div class="staff_only">
			<h3>
				<?= iFoundAdmin::new_hookless()->get_staff_name() ?> staff
			</h3>
			<div>
				<div>
					<form method="POST">
						<button type="submit" class="button button-small" name="remove_tokens" value="remove_tokens">
							Remove auth tokens
						</button>
						<div>This will immediately remove the access tokens used to import contacts</div>
					</form>
				</div>
				<div style="margin-top: 10px;">
					<i class="fa fa-external-link" aria-hidden="true"></i>
					<a target="_blank" href="<?= $this->get_mls_contacts_config_for_mls()['revoke_endpoint'] ?>">
						Visit Spark to permanently revoke authorization from this application
					</a>
				</div>
				<div style="margin-top: 20px;">
					<form method="POST">
						<button type="submit" class="button button-small" name="reset_last" value="reset_last">
							Reset last imported time, so that all contacts will be imported next time
						</button>
					</form>
				</div>
			</div>
		</div>
		<?php
	}

	private function is_expiry_still_valid($expiry) {
		if (!$expiry) {
			return false;
		}
		$date_from_expiry = $this->get_datetime_from_utc_string($expiry);
		return time() < $date_from_expiry->getTimestamp();
	}

	private function get_mls_contacts_config() {
		$mls = $this->mls_name();
		$config = $this->get_config()['mls_contacts'];
		return $config;
	}

	private function get_mls_contacts_config_for_mls() {
		$mls = $this->mls_name();
		$config = $this->get_config()['mls_contacts']['mls'][$mls];
		return $config;
	}

	private function get_authorization_url() {
		$mls_config = $this->get_mls_contacts_config_for_mls();
		$url = $mls_config['authorization_endpoint'];
		$unique_value = $this->util()->get_host() . ' ' . $this->crm_id() . ' ' . time();
		$nonce = hexdec(substr(sha1($unique_value), 0, 11));
		// We must include the nonce itself in the transient name. Otherwise we'd only be able to have a single nonce
		// (per CRM id). We must allow for multiple nonces. It's possible for one person to go through this process on
		// multiple devices simultaneously.
		$transient_name = $this->make_nonce_transient_name($nonce);
		set_transient($transient_name, $nonce, 60 * 15);
		$page_url = $this->get_page_url();
		$state_query_param = [
			'callback_uri' => $page_url,
			'nonce' => $nonce,
		];
		$state_string = base64_encode(json_encode($state_query_param));
		$url = add_query_arg([
			'client_id' => $mls_config['client_id'],
			'response_type' => 'code',
			'redirect_uri' => $mls_config['redirect_endpoint'],
			'scope' => 'openid',
			'state' => $state_string,
		], $url);
		return $url;
	}

	private function get_page_url() {
		$page_url = menu_page_url(static::$menu_slug, false);
		$page_url = str_replace('&#038;', '&', $page_url);
		return $page_url;
	}

	// The term 'states' signals that it's an array allowing multiple fields to be updated at once.
	private function update_states($states) {
		foreach ($states as $key => $value) {
			$this->state[$key] = $value;
		}
		// My thought here is if the job is killed, this will help us detect that it's probably not running any more.
		// So we should update this value a lot, i.e. any time we do anything like download a page of contacts of for
		// each contact imported.
		// Another idea here would be to enqueue another Action Scheduler job ourselves if we're going longer than some
		// threshhold.
		$this->state['updated_at'] = $this->get_now_utc_date_formatted();
		$this->update_option_for_agent(static::$option_state_name, $this->state);
	}

	public function get_option_name_for_agent($option_name, $options = []) {
		if ($this->is_running_as_action_scheduler_job()) {
			return $option_name . $this->crm_id;
		}
		return $option_name . $this->crm_id();
	}

	private function is_running_as_action_scheduler_job() {
		return !!$this->user_id;
	}
}

iFoundMlsContacts::static_init();
