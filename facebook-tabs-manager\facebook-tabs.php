<?
session_start();

add_action( 'init', 'fb_page_redirect' );

function fb_page_redirect() {
	
	if( isset( $_GET['fb_tab_search_homes'] ) || isset( $_GET['fb_tab_home_value'] ) ) {
		
		//require_once plugin_dir_path( __FILE__ ) . 'Facebook/autoload.php';
	
		/** For Search Tabs */
		if(	$_GET['fb_tab_search_homes'] ){
		
			$fb = new Facebook\Facebook([
				'app_id' 				=> '371250183235723',
				'app_secret' 			=> '6d58d5efff50dd42503d3684c7b8720a',
				'default_graph_version'	=> 'v2.8',
			]);
				
			$type = 'search_homes';
		
		/** For Home Value Tabs */
		} elseif( $_GET['fb_tab_home_value'] ){
		
			$fb = new Facebook\Facebook([
				'app_id' 				=> '1631030900533241',
				'app_secret' 			=> '88b9efa87fde34275902bee9ffd4dcfe',
				'default_graph_version'	=> 'v2.8',
			]);
				
			$type = 'home_value';
			
		}
	
		if( isset( $fb ) ) {
			
			$helper = $fb->getPageTabHelper();
			$page_id = $helper->getPageId();
			
		}

		if( isset( $page_id ) ) {
			
			$args = array(
				'post_type' 	=> 'fb_tab_apps',
			   	'meta_query'	=> array(
				   array(
					   'key' 		=> 'fb_page_id',
					   'value' 		=> $page_id,
					   'compare'	=> '=',
				   )
			   )
			);
					
			$my_query = new WP_Query( $args );
			$post_id = $my_query->post->ID;
			$url = get_post_meta( $post_id, $type, true );
			
		}
		
		isset( $url ) ? wp_redirect( $url ) : die( 'This was an error loading this app.' );

		exit;
	
	}

}

?>