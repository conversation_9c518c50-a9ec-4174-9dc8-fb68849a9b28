/**
 * @summary Results Map.
 *
 * Display a map eith pins on results pages.
 *
 * @since 1.0.0
 * @link http://bl.ocks.org/knownasilya/89a32e572989f0aff1f8
 */

jQuery(document).ready(function ($) {
	let currUrl = window.location.href;

	var loadOnDefault = map_data.show_map_by_default;

	$(window).on('ifound:toggle-map', displayMap);
	$(window).on('ifound:drop-pin', dropPin);

	if (currUrl.includes('/save-this/') || currUrl.includes('/map-search/')) {
		$mapButton = $('.map-button');
		$mapButton.css('display', 'inline-block');
		$mapButton.click(displayMap);
		let sphere = $('#as1');
		if (sphere.length !== 0) {
			$(sphere).css('visibility', 'visible');
		}
	}

	var map;
	var loaded = false;
	var markers = new Array();
	var polygons = new Array();
	var coordinates = new Array();
	var pins = map_data.pins || false;
	var shapes = map_data.shapes || false;
	var options = map_data.options || false;
	// other_items are what come from our server. They are data.
	var other_items = map_data.other_items || false;
	// other_google_maps_items are the actual items by doing e.g. new google.maps.Marker() that we display on the map.
	var other_google_maps_items = new Array();

	var form_id = '#ifound-dynamic-form';
	var drawingManager;
	var selectedShape;
	var colorPalette;
	var colors = ['#1E90FF', '#FF1493', '#eef848', '#32CD32', '#FF8C00', '#4B0082', '#030237'];
	var selectedColor;
	var colorButtons = {};
	// This is a good "neighborhood level" zoom.
	var maxInitialZoom = 16;
	var extraMapData = {
		dropped_pins: [],
	};

	var $extraMapDataElem = $('#extra-map-data');

	var styles = [
		{
			featureType: "all",
			stylers: [
				{saturation: 0}
			],
		}, {
			featureType: "road.arterial",
			elementType: "geometry",
			stylers: [
				{hue: "#ff9009"},
				{saturation: 20}
			],
		},
	];

	function clearSelection() {
		if (selectedShape) {
			if (selectedShape.type !== 'marker') {
				selectedShape.setEditable(false);
			}

			selectedShape = null;
		}
	}

	function setSelection(shape) {
		if (shape.type !== 'marker') {
			clearSelection();
			shape.setEditable(true);
			selectColor(shape.get('fillColor') || shape.get('strokeColor'));
		}

		selectedShape = shape;
	}

	function deleteSelectedShape() {
		if (selectedShape) {
			selectedShape.setMap(null);
		}
	}

	function selectColor(color) {
		selectedColor = color;
		for (var i = 0; i < colors.length; ++i) {
			var currColor = colors[i];
			colorButtons[currColor].style.border = currColor == color ? '2px solid #789' : '2px solid #fff';
		}

		var polygonOptions = drawingManager.get('polygonOptions');
		polygonOptions.fillColor = color;
		drawingManager.set('polygonOptions', polygonOptions);
	}

	function setSelectedShapeColor(color) {
		if (selectedShape) {
			if (selectedShape.type == google.maps.drawing.OverlayType.POLYLINE) {
				selectedShape.set('strokeColor', color);
			} else {
				selectedShape.set('fillColor', color);
			}
			$('#color-' + selectedShape.id).val(color);
			$('.polygon-id-' + selectedShape.id).css('background-color', color);
		}
	}

	function makeColorButton(color) {
		var button = document.createElement('span');
		button.className = 'color-button';
		button.style.backgroundColor = color;
		button.addEventListener('click', function () {
			selectColor(color);
			setSelectedShapeColor(color);
		});

		return button;
	}

	function buildColorPalette() {
		if (!loaded) {
			colorPalette = document.getElementById('color-palette');
			for (var i = 0; i < colors.length; ++i) {
				var currColor = colors[i];
				var colorButton = makeColorButton(currColor);
				colorPalette.appendChild(colorButton);
				colorButtons[currColor] = colorButton;
			}
			selectColor(colors[0]);
		}
		loaded = true;
	}

	function initMap() {

		if (typeof ifound_paging !== 'undefined') {
			ifound_paging.has_map = true;
		}

		map = new google.maps.Map(document.getElementById('ifound-shapes-map'), {
			zoom: ifound_map.geo.zoom,
			center: new google.maps.LatLng(ifound_map.geo.center_lat, ifound_map.geo.center_lng),
			styles: styles,
			...window.iFoundGlobal.sharedGoogleMapBaseOptions,
		});

		var polyOptions = {
			strokeWeight: 0,
			fillOpacity: 0.2,
			editable: true,
			draggable: true,
		};
		// Creates a drawing manager attached to the map that allows the user to draw
		// markers, lines, and shapes.
		drawingManager = new google.maps.drawing.DrawingManager({
			/* This sets the drawing mode on. But causes problems clicking markers */
			//drawingMode: google.maps.drawing.OverlayType.POLYGON,
			drawingControlOptions: {
				position: google.maps.ControlPosition.BOTTOM_CENTER,
				drawingModes: ['polygon']
			},
			markerOptions: {
				draggable: true
			},
			polylineOptions: {
				editable: true,
				draggable: true
			},
			rectangleOptions: polyOptions,
			circleOptions: polyOptions,
			polygonOptions: polyOptions,
			map: map
		});

		/** This gets the polygon coord and put them in the input.*/
		google.maps.event.addListener(drawingManager, 'polygoncomplete', function (polygon) {
			polygon.id = Math.round(new Date().getTime() + (Math.random() * 100));
			polygonInput(polygon, form_id);
			polygons.push(polygon);
			google.maps.event.addListener(polygon.getPath(), 'set_at', function () {
				coordinates = (polygon.getPath().getArray());
				$('#paths-' + polygon.id).val(coordinates);
			});
		});

		google.maps.event.addListener(drawingManager, 'overlaycomplete', function (e) {
			var newShape = e.overlay;

			newShape.type = e.type;

			if (e.type !== google.maps.drawing.OverlayType.MARKER) {
				// Switch back to non-drawing mode after drawing a shape.
				drawingManager.setDrawingMode(null);

				// Add an event listener that selects the newly-drawn shape when the user
				// mouses down on it.
				google.maps.event.addListener(newShape, 'click', function (e) {
					if (e.vertex !== undefined) {
						if (newShape.type === google.maps.drawing.OverlayType.POLYGON) {
							var path = newShape.getPaths().getAt(e.path);
							path.removeAt(e.vertex);
							if (path.length < 3) {
								newShape.setMap(null);
							}
						}
						if (newShape.type === google.maps.drawing.OverlayType.POLYLINE) {
							var path = newShape.getPath();
							path.removeAt(e.vertex);
							if (path.length < 2) {
								newShape.setMap(null);
							}
						}
					}
					setSelection(newShape);
				});
				setSelection(newShape);
			} else {
				google.maps.event.addListener(newShape, 'click', function (e) {
					setSelection(newShape);
				});
				setSelection(newShape);

			}

		});

		// Clear the current selection when the drawing mode is changed, or when the
		// map is clicked.
		google.maps.event.addListener(drawingManager, 'drawingmode_changed', clearSelection);
		google.maps.event.addListener(map, 'click', clearSelection);

		//google.maps.event.addDomListener(document.getElementById('delete-button'), 'click', deleteSelectedShape);

		if (pins !== false) setMarkers();
		if (pins !== false && shapes === false) setBounds();
		if (shapes !== false && shapes.length) setPolygons(form_id);
		if (shapes !== false && polygons.length) fitPolygons();
		if (other_items) setOtherItems();

		buildColorPalette();

	}

	function setMarkers() {

		var image = {
			active: ifound_map.active_icon,
			closed: ifound_map.closed_icon
		};

		z = 100;

		pins.forEach(function(pin) {
			var icon = image[pin.status];
			if (pin.has_open_house) {
				icon = ifound_map.open_house_icon;
			}
			var marker = new google.maps.Marker({
				position: {lat: pin.center_lat, lng: pin.center_lng},
				map: map,
				icon: icon,
				title: pin.title,
				zIndex: z
			});
			z++;

			attachLink(marker, pin.mls_id);

			markers.push(marker);
		});

	}

	function attachLink(marker, anchorID) {
		marker.addListener('click', function () {
			$('.zoom-marker').removeClass('zoom-marker');
			$('[name="' + anchorID + '"]').addClass('zoom-marker');
		});
	}

	$(document).on('click', '.zoom-marker-close', function () {
		$('.zoom-marker').removeClass('zoom-marker');
	});

	/** @see https://stackoverflow.com/questions/15299113/google-maps-v3-fitbounds-on-visible-markers*/
	function setBounds() {

		var bounds = new google.maps.LatLngBounds();

		$.each(markers, function () {
			if (this.getVisible()) {
				bounds.extend(this.getPosition());
			}
		});

		fitBounds(map, bounds);

	}

	function fitBounds(map, bounds) {
		function doFit() {
			map.fitBounds(bounds);
			if (map.zoom > maxInitialZoom) {
				map.setZoom(maxInitialZoom);
			}
		}

		// We call map.fitBounds() twice. The second time, in the 'tilesloaded' event callback, should always work
		// but it might show (animate) the zoom changing, which could be distracting to the user.
		// So if we call map.fitBounds() ahead of time, and it works (because the map happens to already be set in
		// the DOM), then this prevents the zoom animation because the bounds will already have been set.
		doFit();
		// The map must be showing to fit the bounds.
		google.maps.event.addListenerOnce(map, 'tilesloaded', function () {
			doFit();
		});
	}

	function setOtherItems() {
		$.each(other_items, function(index, item) {
			if (item.metadata.type === 'nearby') {
				var other_google_maps_item = new google.maps.Marker({
					position: item.data.position,
					icon: {
						url: ifound_map.subject_icon,
						scaledSize: new google.maps.Size(40, 60),
					},
					title: 'Radius Center',
					map: map,
					// 101 because it's bigger than the 100 used for markers.
					zIndex: 101,
					animation: google.maps.Animation.DROP,
				});
				other_google_maps_items.push(other_google_maps_item);
				other_google_maps_item = new google.maps.Circle({
					center: item.data.position,
					map: map,
					// Convert miles to meters.
					radius: item.metadata.radius * 1609.34,
					strokeColor: colors[0],
					strokeOpacity: 0.3,
					strokeWeight: 2,
					fillColor: colors[0],
					fillOpacity: 0.05,
				});
				other_google_maps_items.push(other_google_maps_item);
			} else if (item.metadata.type === 'dropped_pin') {
				var other_google_maps_item = new google.maps.Marker({
					position: item.data.position,
					icon: {
						url: ifound_map.subject_icon,
						scaledSize: new google.maps.Size(40, 60),
					},
					title: 'Dropped pin',
					map: map,
					// 101 because it's bigger than the 100 used for markers.
					zIndex: 101,
					animation: google.maps.Animation.DROP,
				});
				other_google_maps_items.push(other_google_maps_item);
			}
		});
	}

	function clearMarkers() {
		$.each(markers, function () {
			this.setMap(null);
		});
		markers.length = 0;

		clearOtherItems();
	}

	function clearOtherItems() {
		other_google_maps_items.forEach(function(item) { item.setMap(null); });
		other_google_maps_items.length = 0;
	}

	function getCityStateParts(input) {
		let vParts = $(input).val().split(' ');
		if (vParts[vParts.length - 1].length == 2) {
			let joined = vParts.join(' ');
			let state = vParts[vParts.length - 1];
			return {
				city: joined.substr(0, joined.indexOf(state) - 1).replace(/,/g, ''),
				state: state,
			};
		} else {
			return null;
		}
	}

	function runSearch() {
		if (ifound_map.isMultipleStateMls) {
			let inputs = $('#ifound-dynamic-form .dynamic-input input');
			for (let input of inputs) {
				if ($(input).attr('name') === 'city[]' || $(input).attr('name') === 'school_district[]') {
					let parts = getCityStateParts(input);
					if (parts !== null) {
						let clone = $(input).clone();
						clone.attr('name', 'state[]');
						$(clone).val(parts.state);
						$(input).val(parts.city);
						$(input).closest('.dynamic-input').append(clone);
					}
				}
			}
		}
		var input = $('#ifound-dynamic-form').serialize();
		getResults(input);
	}

	$('.advanced-button').on('click', runSearch);
	var debouncedRunSearch = _.debounce(function() {
		// Don't do for campaign builder or shortcode creator.
		if (!isCampaignBuilder() && !isShortcodeCreator()) {
			runSearch();
		}
	}, 2000);
	jQuery(window).on('ifound:search', debouncedRunSearch);

	$('.advanced-button-wrapper').on('click', function () {
		$(this).toggleClass('active');
		$('.advanced-criteria-section').slideToggle('slow');
	});

	/** This is for Campaign Builder to hide results. */
	$('.hide-results').on('click', function () {
		$('.display-results-wrapper').slideUp('slow');
		$('.hide-results-wrapper').fadeOut('slow');
	});

	$.getResults = function (input) {
		getResults(input);
	}

	function moveSphereUp(sphere) {
		// Might be a null or an empty string
		if (typeof aerial_sphere === 'undefined') return;
		if (aerial_sphere.origResults === null || aerial_sphere.origResults === '') {
			var childHeight = $(sphere).children('.aerial-sphere-map-wrapper').css('height');
			$(sphere).css('margin-bottom', '-' + childHeight);
		} else {
			$('.aerial-sphere-map-wrapper').css({
				'visibility': 'visible',
				'margin-bottom': 'initial'
			});
		}
	}

	// Moving the sphere up on the advanced-search page load.
	//moveSphereUp($('.aerial-sphere-advanced-search'));

	function getResults(input) {
		clearMarkers();

		$('.while-we-wait').addClass('active');

		/** Show the hide results button in Campaign Builder. */
		$('.hide-results-wrapper').fadeIn('slow');

		if ($('.doing_campaign').length) {
			input = input + '&doing_campaign';
		}

		var url = ifound_map.site_url + ifound_map.endpoint + '?' + input;
		$.getJSON(url, function (data) {
			if (loadOnDefault || isMapShowing()) {
				drawMap(data);
			} else {
				var pageLoaded = false;
				$('.button-invisible').css('display', 'inline-block');
				$('.map-button').click(function () {
					if (!pageLoaded) {
						drawMap(data);
						pageLoaded = true;
					} else displayMap(true);
				});
			}

			$('html, body').animate({scrollTop: 0}, 1000);
			$('.display-results-wrapper').html(data.body).fadeIn('slow');
			$('.while-we-wait').removeClass('active');
			// @since 2.5.9 @see dynamic.js
			$.reMarkActive();
			manageVTButtons();
			drawAerialSphere(data);
		});
	}

	function manageVTButtons() {
		var links = $('.vt-link');
		Array.prototype.forEach.call(links, function (link) {
			if (isCampaignBuilder()) {
				$(link).css({'font-size': '.7rem', 'padding': '2px'});
			}
		});
	}

	function isShortcodeCreator() {
		return window.location.href.includes('post-new.php')
			|| (
				window.location.href.includes('post.php')
				&& window.location.href.includes('action=edit')
			);
	}

	function isCampaignBuilder() {
		return window.location.href.includes('?page=campaign-builder');
	}

	function drawAerialSphere(data) {
		$('.aerial-sphere-map-wrapper').css('display', 'block');
		asphere = $('.aerial-sphere-advanced-search');
		if ($('.ifound_no_results').length > 0) {
			$(asphere).css('display', 'none');
			return;
		} else if ($(asphere).css('display') === 'none') {
			$(asphere).css('display', 'initial');
		}

		$(asphere).css({
			'visibility': '',
			'margin-bottom': 'initial'
		});

		//add markers to the aerial sphere
		if (typeof AerialSphere === 'function') {
			var markerData = $.getMarkerData(data.body);
			//clear existing markers
			window.aerialSphere.removeAllMarkers();
			window.aerialSphere.setWidgetEnabled('geoinfo', false);

			var lat = 0, lng = 0;
			const pins = data.map_data.pins;
			for (let i = 0; i < pins.length; i++) {
				lat += pins[i].center_lat;
				lng += pins[i].center_lng;

				pins[i].title = pins[i].title + '<a style="color:#000" href="'
					+ markerData[i].href + '" target="_blank"><div style="height:140px"><img src="'
					+ markerData[i].img + '" /></div></a>'
					+ markerData[i].stat + markerData[i].propData;

				switch (pins[i].status) {
					case 'active':
						icon = 'https://i.ibb.co/qMQ7qrp/active-icon.png';
						break;
					case 'pending':
						icon = 'https://i.ibb.co/tmQ0FPX/ucb-icon.png';
						break;
					case 'closed':
						icon = 'https://i.ibb.co/c3YNdx1/sold-icon.png';
						break;
					default:
						icon = 'https://i.ibb.co/c3YNdx1/sold-icon.png';
						break;
				}
				window.aerialSphere.addMarker(pins[i].center_lat, pins[i].center_lng, '', pins[i].title, icon, 'Homes For Sale', pins[i].mls_id, '', function () {
					console.log('Marker callback hit!')
				});
			}
			window.aerialSphere.getNearestPano(getMedian(pins, 'center_lat'), getMedian(pins, 'center_lng'), panoObj => {
				if (panoObj.distance > 3000) {
					document.getElementById(this.id).parentElement.parentElement.style.display = 'none';
				}

				var lat = panoObj.panoMarker.marker.position.lat;
				var lng = panoObj.panoMarker.marker.position.lng;
				aerialSphere.openPanorama(lat, lng, lat, lng);

				// FIXME
				/*setTimeout(() => {
					aerialSphere.setAutorotate(true);
				}, 2500); */
			});
		}
	}

	function getMedian(pins, key) {
		var sorted = pins.sort((a, b) => a[key] - b[key]);
		return sorted[Math.floor(sorted.length / 2)][key];
	}

	function getMarkerData(data) {
		var resultPart = data.split('results-section')[1];
		var listings = resultPart.split('results-image');
		var markerData = [];
		var linkRE = /href=\"(http.+?)"/;
		var imgRE = /src=\"(http.+?)"/;
		var statRE = /(<div class=\"status-wrap.+?<\/div>)/;
		var propRE = new RegExp('(<div class=\"prop-item.+)<img src', 's');
		for (var i = 1; i < listings.length; i++) {
			markerData.push({
				href: linkRE.exec(listings[i])[1],
				img: imgRE.exec(listings[i])[1],
				stat: statRE.exec(listings[i])[1],
				propData: propRE.exec(listings[i])[1]
			});
		}
		return markerData;
	}

	$.getMarkerData = getMarkerData;

	function isMapShowing() {
		var map = $('.map-and-palette-wrapper');
		return $(map).css('display') != 'none' && !$('#draw-map-body > div').hasClass('map-and-palette-wrapper')	;
	}

	// Reminder: I'm not currently tracking these pins (adding them to an array), like I am for other markers. This is
	// because they are added manually by the user during the session they are using the map, as opposed to data coming
	// from an external source (like a list of MLS listings). But we COULD track them in that same way and clear them
	// in clearMarkers().
	function dropPin(event, position) {
		displayMap(true);
		var droppedPinMarker = new google.maps.Marker({
			position: position,
			icon: {
				url: ifound_map.subject_icon,
				scaledSize: new google.maps.Size(40, 60),
			},
			map: map,
			animation: google.maps.Animation.DROP,
		});
		addDroppedPin(droppedPinMarker);
		// Add a button to remove the pin
		var element = document.createElement('button');
		element.type = 'button';
		element.className = 'button';
		element.appendChild(document.createTextNode('Remove pin'));
		const infowindow = new google.maps.InfoWindow();
		infowindow.setContent(element);
		element.onclick = function() {
			droppedPinMarker.setMap(null);
			infowindow.close();
			removeDroppedPin(droppedPinMarker);
			droppedPinMarker = null;
		};
		droppedPinMarker.addListener('click', function() {
			infowindow.open({
				anchor: droppedPinMarker,
				map: map,
				shouldFocus: false,
			});
		});
		map.setZoom(maxInitialZoom);
		map.panTo(droppedPinMarker.position);
	}

	function addDroppedPin(droppedPinMarker) {
		extraMapData.dropped_pins.push({
			position: {
				lat: droppedPinMarker.getPosition().lat(),
				lng: droppedPinMarker.getPosition().lng(),
			},
		});
		writeExtraMapData();
	}
	function removeDroppedPin(droppedPinMarker) {
		var indexToRemove = extraMapData.dropped_pins.findIndex(function(x) {
			return x.lat === droppedPinMarker.getPosition().lat() && x.lng === droppedPinMarker.getPosition.lng();
		});
		extraMapData.dropped_pins.splice(indexToRemove, 1);
		writeExtraMapData();
	}
	function writeExtraMapData() {
		$extraMapDataElem.val(JSON.stringify(extraMapData));
	}

	function displayMap(forceShow) {
		// Ignore the forceShow param if this function was called as an event handler or triggered by an event.
		if (forceShow === undefined || typeof forceShow !== 'boolean') {
			forceShow = false;
		}
		let icon = $('.map-button > i').prop('outerHTML');
		let map = $('.map-and-palette-wrapper');

		var shouldShowMap = forceShow || !isMapShowing();

		if (shouldShowMap) {
			if ($('#draw-map-body > div').hasClass('map-and-palette-wrapper')) {
				$(map).remove().css('display', 'block').insertAfter('.button-wrapper');
			} else {
				$(map).slideDown('slow');
			}
			$('.map-button').html(icon + ' Hide Map');
		} else {
			$(map).toggle(false);
			$('.map-button').html(icon + ' Show Map');
		}
	}

	function drawMap(data) {
		displayMap(true);
		pins = false;
		pins = data.map_data.pins || false;
		other_items = data.map_data.other_items || false;
		if (pins) {
			setMarkers();
			setBounds();
		}
		if (other_items) {
			setOtherItems();
		}
	}

	$('.ifound-close').on('click', function () {
		$('.lookups-body').slideUp();
	});

	$(document).on('click', '.dynamic-input-remove', function () {
		var ID = $(this).attr('polyid');
		$.each(polygons, function () {
			if (this.id == ID) {
				this.setMap(null);
			}
		});
		$(this).parents('.dynamic-input').remove();
	});

	function setPolygons(form_id) {

		$.each(shapes, function () {
			var randID = Math.round(new Date().getTime() + (Math.random() * 100));
			var polygon = new google.maps.Polygon({
				paths: this.paths,
				id: randID,
				strokeColor: this.color,
				strokeOpacity: 0.3,
				strokeWeight: 0,
				fillColor: this.color,
				fillOpacity: 0.2
			});

			google.maps.event.addListener(polygon, 'click', function () {
				this.setOptions({editable: true, draggable: true});
				setSelection(polygon);
			});


			polygonInput(polygon, form_id);

			google.maps.event.addListener(polygon.getPath(), 'set_at', function () {
				$('#paths-' + polygon.id).val(coordinates);
			});

			var location = this.location;

			if (location) {
				polygon.addListener('click', function () {
					window.location.href = ifound_map.site_url + location;
				});
			}

			var color = this.color;

			google.maps.event.addListener(polygon, "mouseover", function () {
				this.setOptions({strokeColor: color, fillColor: color, fillOpacity: 0.3});
			});

			google.maps.event.addListener(polygon, "mouseout", function () {
				this.setOptions({strokeColor: color, fillColor: color, fillOpacity: 0.2});
			});

			polygon.setMap(map);

			polygons.push(polygon);

		});

	}

	function fitPolygons() {

		var bounds = new google.maps.LatLngBounds();

		$.each(polygons, function () {
			var paths = this.getPaths();
			paths.forEach(function (path) {
				var ar = path.getArray();
				for (var i = 0, l = ar.length; i < l; i++) {
					bounds.extend(ar[i]);
				}
			});
		});

		fitBounds(map, bounds);

	}

	function polygonInput(polygon, form_id) {

		coordinates = (polygon.getPath().getArray());
		var color = (typeof polygon.fillColor === 'undefined') ? '#030237' : polygon.fillColor;

		var input = '<div class="dynamic-input">'
		input += '<div class="dynamic-input-label">';
		input += '<span class="polygon-color polygon-id-' + polygon.id + '" style="background:' + color + '"></span> - Polygon';
		input += '<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true" polyid="' + polygon.id + '"></i>';
		input += '</div>';
		input += '<input type="hidden" name="polygons[' + polygon.id + '][paths]" value="' + coordinates + '" class="dynamic-value" id="paths-' + polygon.id + '"/>';
		input += '<input type="hidden" name="polygons[' + polygon.id + '][color]" value="' + color + '" class="dynamic-value" id="color-' + polygon.id + '"/>';
		input += '</div>';

		$(form_id).append(input);

	}

	$.initMapOnPop = function () {
		initMap();
	}

	$.clearMap = function() {
		clearPolygons();
		clearOtherItems();
	}

	/**
	 * Clear Ploygons
	 *
	 * This is uused to remove all instanses of a polygon when a user closes the shortcode editor.
	 * The purpose in have a blank map when the map is popped for the second time. If a shortcode is highlighted,
	 * only the map objects for that shortcode will be displayed.
	 *
	 * @see create-shortcode.js
	 * @since 1.0.0
	 */
	$.clearPolygons = function () {
		clearPolygons();
	}

	function clearPolygons() {
		$.each(polygons, function () {
			this.setMap(null);
		});
		polygons.length = 0;
		map_data.shapes = false;
		shapes.length = 0;
	}

	$.dynamicForm = getDynamicForm;

	function getDynamicForm(input, form_id) {

		clearMarkers();

		var url = ifound_map.site_url + ifound_map.shortcode + input;

		return $.getJSON(url, function (data) {
			setDynamicForm(data, form_id);
		});

	}

	$.setDynamicForm = function (data, form_id) {
		setDynamicForm(data, form_id);
	}

	function setDynamicForm(data, form_id) {
		$(form_id).html(data.body);
		shapes = data.map_data.shapes || false;
		if (shapes) {
			setPolygons(form_id);
		}
		other_items = data.map_data.other_items || false;
		if (other_items) {
			setOtherItems();
		}
	}

	window.iFoundGlobal.loadGoogleMaps(['drawing']).then(function() {
		initMap();
		if (loadOnDefault) {
			displayMap(true);
		}
	});

	$('.criteria-collapse-control').on('click', function () {
		var advSection = $('.advanced-criteria-section');
		$(advSection).toggle();
		$('.advanced-button-wrapper').toggleClass('active');
	});
});
