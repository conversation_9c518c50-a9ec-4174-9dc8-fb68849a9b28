<?php

require_once(__DIR__ . '/ArmlsParticipationReporter.php');
require_once(__DIR__ . '/CrmlsParticipationReporter.php');
require_once(__DIR__ . '/TarmlsazParticipationReporter.php');
require_once(__DIR__ . '/../class-util.php');

abstract class ParticipationReporter {
	protected $mls_name;
	/** @var Client */
	protected $client;

	protected static $participation_reporting_option_name = 'ifound_participation_reporting';

	public function __construct($mls_name, $client) {
		$this->mls_name = $mls_name;
		$this->client = $client;
	}

	abstract public function submit(): string;
	abstract public function print_report_preview(): void;
	abstract protected function is_history_record_success($record): bool;
	abstract protected function get_result_from_history_record($record);

	public static function create($mls_name, $client) {
		if ($mls_name === 'armls') {
			return new ArmlsParticipationReporter($mls_name, $client);
		} else if ($mls_name === 'crmls') {
			return new CrmlsParticipationReporter($mls_name, $client);
		} else if ($mls_name === 'tarmlsaz') {
			return new TarmlsazParticipationReporter($mls_name, $client);
		} else {
			throw new \Exception('Invalid mls name: ' . $mls_name);
		}
	}

	public static function choose_mls_page() {
		$util = new Util();
		$current_url = $util->current_url();
		$mls_names = [
			'armls',
			'crmls',
			'tarmlsaz',
		];
		$links = $util->array_map_modify_both(function ($k, $v) use ($current_url) {
			return [$v, add_query_arg('mls_name', $v, $current_url)];
		}, $mls_names);

		?>
		<h1>Participation Reporting</h1>
		<div>
			<h2>Please choose an MLS:</h2>
			<ul>
				<li><a href="<?= $links['armls'] ?>">ARMLS</a></li>
				<li><a href="<?= $links['crmls'] ?>">CRMLS</a></li>
				<li><a href="<?= $links['tarmlsaz'] ?>"><s>TARMLSAZ</s></a> (Reminder: this basically needs to be submitted by hand, so don't use this link. See more info <a href="https://ifoundagent.teamwork.com/app/tasks/39338258">here</a>.)</li>
			</ul>
		</div>
		<?php
	}

	public function print_participation_reporting_history() {
		$history = $this->get_participation_reporting_history($this->mls_name);
		?>
		<h2 style="margin-top: 20px;">History</h2>
		<div>Sorted by date desc</div>
		<div>
			<table class="history">
				<thead>
				<tr>
					<th>Date</th>
					<th>Success?</th>
					<th>Result/Response</th>
				</tr>
				</thead>
				<tbody>
				<?
				if (count($history)) {
					foreach ($history as $h) {
						$result = $this->get_result_from_history_record($h);
						?>
						<tr>
							<td><?= $h['datetime']->format('c') ?></td>
							<td><?= $this->is_history_record_success($h)  ? 'Success' : 'Error' ?></td>
							<td>
								<pre><code style="font-family: monospace;"><?= is_array($result) ? json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) : $result ?></code></pre>
							</td>
						</tr>
						<?php
					}
				} else {
					?>
					<tr>
						<td colspan="3">No reports</td>
					</tr>
					<?php
				}
				?>
				</tbody>
			</table>
		</div>
		<?php
	}

	protected function is_http_history_record_success($record): bool {
		return $this->is_http_success($record['http_status_code']);
	}

	protected function get_participation_reporting_history() {
		$history = get_option(static::$participation_reporting_option_name);
		if (!$history || !isset($history[$this->mls_name])) {
			return [];
		}
		$mls_history = $history[$this->mls_name];
		// Sort by date desc
		usort($mls_history, function($a, $b) {
			return -($a['datetime'] <=> $b['datetime']);
		});
		return $mls_history;
	}

	public function capture_report_result($result, $was_http = false) {
		$data = get_option(static::$participation_reporting_option_name);
		if (!$data) {
			$data = [];
		}
		if (!isset($data[$this->mls_name])) {
			$data[$this->mls_name] = [];
		}
		$datetime = new DateTime('now', new DateTimeZone('UTC'));
		if ($was_http) {
			$response = $result;
			if (is_wp_error($response)) {
				$data[$this->mls_name][] = [
					'datetime'         => $datetime,
					'http_status_code' => $response->get_error_codes()[0],
					'response'         => $response->get_error_messages(),
				];
			} else {
				$data[$this->mls_name][] = [
					'datetime'         => $datetime,
					'http_status_code' => wp_remote_retrieve_response_code($response),
					'response'         => json_decode(wp_remote_retrieve_body($response), true),
				];
			}
		} else {
			$result['datetime'] = $datetime;
			$data[$this->mls_name][] = $result;
		}
		update_option(static::$participation_reporting_option_name, $data);
	}

	protected function is_http_success($code) {
		return $code >= 200 && $code < 300;
	}

	protected function get_active_clients() {
		$posts = get_posts([
			'numberposts' => -1,
			'post_type'   => Client::$the_post_type,
			'tax_query'   => [
				'relation' => 'and',
				[
					'taxonomy' => 'mls_name',
					'field'    => 'slug',
					'terms'    => array($this->mls_name),
				],
				[
					'taxonomy' => 'client_status',
					'field'    => 'slug',
					'terms'    => array('production'),
				],
			],
		]);

		// Only include clients who have active status.
		$posts = array_filter($posts, function($post) {
			$api_key = get_post_meta($post->ID, 'api_key', true);
			$status = $this->client->get_access_status($api_key);
			// My local dev env has the status as an int, but I originally wrote it with '1', and it's been working in
			// production, so it must be differnt there. Not sure why but let's deal with it the easy way.
			return $status === '1' || $status === 1;
		});
		$posts = array_values($posts);
		return $posts;
	}
}
