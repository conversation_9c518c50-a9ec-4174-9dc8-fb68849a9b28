# Measurements

Customers and <PERSON> are complaining of slowness on the (WordPress) site. This document will capture noted slowness, so that we know where to start to improve speed.

## Timings

Here are some informal timings I've done.

2013-01-21

### waltersrealtygroup.com

* http://waltersrealtygroup.com/east-valley-mls/gilbert-homes-for-sale-latest-listings/
	* Page load (main file): 13 s, everything else was very fast
	* Page load (main file): 1.8 minutes. Wow. Why?
	* Page load (main file): 1.7 minutes. What is going on?


Things I've noted
* mysqld eats up more than 50% CPU constantly.
* Wordpress stuff loads quickly. It's when it touches the IDX it's slow.
* The RETS sync is amazingly slow. Why?
	* TREND is several times slower than ARMLS. Why?


## MySQL investigation

### By table

#### armls_images

* SELECT 1 FROM armls_images WHERE `Object-ID` = 14 AND `Content-ID` = 20130109211511319801000000
* We have no index on Object-ID on armls_images

* SELECT * FROM armls_images WHERE `Content-ID` = '20120714223936417148000000' AND Preferred = 1 
* No index on preferred

#### property

```
Existing indexes

* property_id
* LIST_1
* listing_member_shortid
* listing_office_shortid
* LIST_105

* SELECT * FROM property WHERE LIST_105 = 4872920
* We DO have an index on LIST_105

* SELECT property_id FROM property WHERE 1  AND LIST_39 = 'Litchfield Park' AND (((`GF200709141342226...
* No index on LIST_39

* SELECT * FROM property WHERE LIST_105 = 4868450  AND LIST_19 <> 'AWC'
* No index on LIST_19
```