[Home](Home)

[TOC]

# Local Multisite Install

## Installation

1. Download and unpack zip file of latest version of WordPress

2. Create user and database and grant privileges.  On the last install, I had to grant all on \*.\*; not sure why.

3. Copy `root/wp-config-sample.php` to `wp-config.php` and update database details

4. Set up apache virtualhost

5. Turn on debugging in `wp-config.php` - See [the Codex](https://codex.wordpress.org/Debugging_in_WordPress)

6. Point your browser to `mysite/wp-admin/install.php`

	NOTE: if you get a blank screen, it can be due to incorrect credentials in `wp-config.php`

7. Add to `wp-config.php`: 

		/* Multisite */
		define( 'WP_ALLOW_MULTISITE', true );

8. Access the admin panel of the site and run through steps in Tools → Network Setup
	
	NOTE that you have to copy/paste EVERY LINE on the final page of that process into wp-config.php because there are two separate lines mentioning multisite:
	
		define('WP_ALLOW_MULTISITE', true);
		define('MULTISITE', true);

	BOTH of these lines need to be there.

9. Set up wildcard DNS.  In Ubuntu 12.04, this is done using a tool called dnsmasq.  Ubuntu uses network-manager out of the box, which has dnsmasq-base installed as a dependency, which will cause a conflict if you attempt to also use dnsmasq.  The following steps are taken from [this blog](https://coderwall.com/p/6dgpsw).
	
	- First, stop network-manager from using dnsmasq-base:

			# /etc/NetworkManager/NetworkManager.conf 

			[main]
			plugins=ifupdown,keyfile
			#dns=dnsmasq

			[ifupdown]
			managed=false


	- edit `/etc/dnsmasq.conf`: uncomment `listen-address` and set to `listen-address=127.0.0.1`
	- set the following in `/etc/dnsmasq.conf` to enable wildcard subdomains for, e.g., the wpmulti local site: 

			address=/wpmulti/127.0.0.1

	- verify that `/etc/resolv.conf` contains the following:

			nameserver 127.0.0.1

	- restart network manager: 

			sudo restart network-manager

	- start dnsmasq: 

			sudo service dnsmasq start

10. Add a `ServerAlias` directive in your Apache config file: 

		ServerAlias *.wpmulti

11. If the database connection is broken at this point, see the note under step 8.

## Troubleshooting

### Cookies

In Chrome, I was unable to log in after completing the multisite install.  I wasn't given any error messages - I was just redirected to the same login page with no explanation over and over.  Logging in with Firefox worked fine, however.

A related problem manifests as an error message about cookies when attempting to log in to the multisites:

		ERROR: Cookies are blocked or not supported by your browser. You must enable cookies to use WordPress.

There are two fixes for these problems.  Using either one should resolve the issue.

1. Use a local domain with a dot somewhere in the name

2. Add the following to `wp-config.php`:

		/* Cookie settings to prevent weird error message on multisite login */
		define('ADMIN_COOKIE_PATH', '/');
		define('COOKIE_DOMAIN', '');
		define('COOKIEPATH', '');
		define('SITECOOKIEPATH', '');