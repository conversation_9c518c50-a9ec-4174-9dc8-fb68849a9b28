<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

if( ! class_exists( 'iFoundCrm' ) ) die( 'You do not have access!' );

/**
 * iFoundTeams Class
 *
 * @since 3.0.0
 */

class iFoundTeams extends iFoundCrm{
	use UtilTrait;
	use NewHooklessTrait;

	protected $post_type		= 'team_members';
	private $query_var			= 'team-members';
	private $label_name 		= 'Team Member';
	private	$label_names		= 'Team Members';
	protected $agent            = 'agent';
	private $agent_obj;

	public static $role_name = 'team_member';
	private static $team_member_crm_option_names = [
        'ifound_campaign_bulder_criteria',
        'ifound_more_campaign_bulder_criteria',
        'ifound_adv_search_criteria',
        'ifound_more_adv_search_criteria',
        'ifound_quick_search_criteria',
    ];
    private static $enable_crm_meta_key = 'ifa-enable-crm';

	/**
	 * init iFoundTeams class.
	 *
	 * @since 3.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 3.0.0
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'endpoints'));
			add_action('init', array($this, 'id_redirect'));
			if (!is_admin()) {
				add_action('init', array($this, 'register_and_enqueue_team_script'));
			}

			add_filter('template_include', [$this, 'template_include']);

            add_action('ifound_agent_page', [$this, 'agent_template'], 10, 1);

			add_action('show_user_profile', array($this, 'additional_user_fields'));
			add_action('edit_user_profile', array($this, 'additional_user_fields'));
			add_action('personal_options_update', array($this, 'save_additional_user_fields'));
			add_action('edit_user_profile_update', array($this, 'save_additional_user_fields'));

			if ($this->should_show_bios()) {
				add_filter('document_title_parts', [$this, 'document_title_parts']);
				add_filter('genesis_seo_title', [$this, 'genesis_site_title'], 10, 3);
			}

			//good to have both depending on what type opf Nav we are loading
			add_filter('genesis_nav_items', array($this, 'modify_home_nav'), 10, 2);
			add_filter('wp_nav_menu_items', array($this, 'modify_home_nav'), 10, 2);

			// Add teams body class.
			add_filter('body_class', [$this, 'ifoundteam_add_body_class']);

			add_action('init', array($this, 'install_teams'));

			add_action('post_updated', array($this, 'post_updated_hook'), 10, 3);
			add_action('remove_user_from_blog', array($this, 'remove_user_from_blog'), 10, 3);
			add_action('delete_user', array($this, 'delete_team_member'), 10, 2);

			add_action('add_meta_boxes_contacts', array($this, 'add_assign_team_members_metabox'));

			add_action('ifound_new_team_lead', array($this, 'new_team_lead'), 10, 1);

			add_filter('ifound_crm_id', array($this, 'teams_crm_id'));
			add_filter( 'ifound_admin_columns', array( $this, 'admin_columns' ) );

			add_action('user_register', [$this, 'user_register']);
			add_action('wpmu_activate_user', [$this, 'wpmu_activate_user'], 10, 3);
			add_action('set_user_role', [$this, 'set_user_role'], 10, 3);
			add_action('wp_login', [$this, 'wp_login'], 10, 2);

			// Sometimes I make these functions public because they are working with global data, but for this one,
			// we need access to state ($this->agent_obj) so we'll use a filter.
			add_filter('ifound_get_agent_id', [$this, 'get_agent_id']);

			// We want admins to be able to edit users. This is not normally possible for multisite. There is a
			// capability called manage_network_users that could be used, but it allows admins to edit any user, not
			// just the users on their site.
			// I got this from:
			// https://thereforei.am/2011/03/15/how-to-allow-administrators-to-edit-users-in-a-wordpress-network/
			if (is_multisite()) {
				add_filter('map_meta_cap', [$this, 'mc_admin_users_caps'], 1, 4);
				remove_all_filters('enable_edit_any_user_configuration');
				add_filter('enable_edit_any_user_configuration', '__return_true');
				add_filter('admin_head', [$this, 'mc_edit_permission_check'], 1, 4);
			}

			add_action('gform_user_registered', [$this, 'gform_user_registered'], 10, 4);
		}
	}

    public function template_include($template) {
        if ($this->is_agent_landing_page()) {
            $this->prepare_agent_page();
            return locate_template('agent_page_ifound.php');
        }
        return $template;
    }

	private function should_show_bios() {
		$show_bios = isset(get_option('ifound_teams_settings')['show_bios']);
		return $show_bios;
	}

	public function allow_override_broker_logo() {
	    $options = get_option('ifound_teams_settings');
		$allow_override_broker_logo = isset($options['allow_override_broker_logo']);
		return $allow_override_broker_logo;
	}

	private function get_sub_option($option_name) {
		$option_value = isset(get_option('ifound_teams_settings')[$option_name]);
		return $option_value;
    }

	public function ifoundteam_add_body_class( $classes ) {
		if ($this->is_agent_landing_page()) {
			$classes[] = 'single-team';
			$agent_id = $this->get_agent_id();
			if ($agent_id && $this->has_crm_enabled(['id' => $agent_id])) {
				$classes[] = 'ifound-has-crm';
			}
		}
		return $classes;
	}

	public function register_and_enqueue_team_script() {
		wp_register_script('ifound_teams_js', plugins_url('includes/js/teams.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);
		wp_enqueue_script('ifound_teams_js');
	}

	// Assumes $this->agent_obj exists.
	private function get_agent_home_link() {
		$username = $this->agent_obj->username;
		return "/{$this->agent}/{$username}";
	}

	public function modify_home_nav($menu, $args) {
		//reroute the Home nav button to agent page

		if ($this->agent_obj && $this->should_show_bios() && $this->get_sub_option('home_link_to_bio')) {
			$agent_home_link = $this->get_agent_home_link();
			// This assumes that the WP menu's "home" link's URL has been set to exactly "/".
			return str_replace('href="/"', "href=\"{$agent_home_link}\"", $menu);
		}
		return $menu;
	}

	public function id_redirect(){
		if (is_admin()) {
			return;
		}
        if (strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
           return;
        }

		if (!isset($_REQUEST['aid']) && isset($_COOKIE['agent'])) {
			$cookie = sanitize_text_field($_COOKIE['agent']);
			// Redirects to domain with query param
			$clean_cookie = str_replace("\\", "", $cookie);
			$location = $this->current_url();

			$agent_id = json_decode($clean_cookie)->id;
			$location = add_query_arg('aid', $agent_id, $location);
			// TODO: Previously this code used the 'id' query param. For a little while, let's assume that if the 'id'
			// query param exists, it's from users with bookmarks. But we should remove this line in the future. The
			// goal is to not hijack the 'id' query param; it's too generic for us to assume it's ours.
			$location = remove_query_arg('id', $location);

			if ($_SERVER['REQUEST_METHOD'] === 'GET') {
				wp_redirect($location);
				// Previously I had a note saying don't use exit here, because then login wouldn't work. I checked
				// and it seems to work fine; I'm not sure why it wasn't working before. We want this exit here, not
				// just because one usually does after a redirect, but specifically to avoid sending a "Person X has
				// visited your website" email multiple times; once on the original request and another after the
				// redirect.
				exit;
			} else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
				$this->set_agent_by_id($agent_id);
			}
		} else if (isset($_REQUEST['aid'])) {
			$this->set_agent_by_id($_REQUEST['aid']);
		}
	}

	private function get_nicename_from_url() {
		global $wp_query;
		$nicename = $wp_query->get($this->agent);
		return $nicename;
	}

	public function get_agent_id() {
	    if (isset($this->agent_obj)) {
	        return $this->agent_obj->id;
		}
		return null;
	}

	public function document_title_parts($parts) {
		if ($this->is_agent_landing_page() && $this->agent_obj) {
		    $parts['title'] = $this->agent_obj->name;
		}

		return $parts;
	}

	public function genesis_site_title($title, $inside, $wrap) {
	    if (!$this->agent_obj) {
		    return $title;
	    }

		$home_link = home_url();
		if ($this->get_sub_option('home_link_to_bio')) {
			$home_link = $this->get_agent_home_link();
		}
		$name = $this->agent_obj->name;

		ob_start();

		genesis_markup( array(
			'open' => "<$wrap %s>",
			'context' => 'site-title',
		) );

		echo "<a href=\"{$home_link}\">{$name}</a>";

		genesis_markup( array(
			'close'    => "</$wrap %s>",
			'context' => 'site-title',
		) );

		$new_title = ob_get_clean();
		return $new_title;
	}

	public function endpoints() {
		if ($this->should_show_bios()) {
			// Reminder: this will add a query var, but as mentioned in the docs:
			// "The template_redirect action hook should test this query variable."
			// https://developer.wordpress.org/reference/functions/add_rewrite_endpoint/
			add_rewrite_endpoint($this->agent, EP_ALL);
		}
	}

	private function is_agent_landing_page() {
		global $wp_query;
		// If this returns false when you think it should be true, don't forget to flush rewrite rules!
		// If this returns false when you think it should be true, don't forget to flush rewrite rules!
		// If this returns false when you think it should be true, don't forget to flush rewrite rules!
		return isset($wp_query->query_vars[$this->agent]);
	}

	// Look up the user by username. Return null if not found, or if the user is not a team member.
	private function get_team_member_by_nicename($nicename) {
		$results = get_users(['nicename' => $nicename]);
		if (count($results)) {
			$agent = $results[0];
			if ($this->user_has_team_member_role(['id' => $agent->ID])) {
				return $agent;
			}
		}
		return null;
	}

	public function prepare_agent_page() {
        $nicename = $this->get_nicename_from_url();
        $agent = $this->get_team_member_by_nicename($nicename);
        if ($agent) {
	        $usermeta = get_user_meta($agent->ID);
	        $aid_matches_actual_agent_id = $_GET['aid'] === (string)$agent->ID;
	        if (!$aid_matches_actual_agent_id) {
		        $location = $this->current_url();
		        $location = add_query_arg('aid', $agent->ID, $location);
		        wp_redirect($location);
		        exit;
	        }
	        $this->set_agent_cookie($agent->ID, implode(" ", $usermeta['nickname']), $agent->user_nicename, $agent->user_email);
        } else {
            // Show a blank page with this error message. We could have a nicer page. We could even have a "normal"
            // agent page, except show this message instead of the agent bio. But since is the way we've been doing it,
            // I'll leave it how it's been.
	        echo "{$nicename} is not a user or is not a team member";
            exit;
        }
	}

	public function set_agent_by_id($id) {
		$agent = get_userdata($id);
		if ($agent && $this->user_has_team_member_role(['id' => $agent->ID])) {
			$usermeta = get_user_meta($agent->ID);
			$this->set_agent_cookie($agent->ID, implode(" ",$usermeta['nickname']), $agent->user_nicename , $agent->user_email);
		}
	}

	public function set_agent_cookie($id, $name, $username, $email) {
		$this->agent_obj = new stdClass();

		$this->agent_obj->name = $name;
		$this->agent_obj->email = $email;
		$this->agent_obj->username = $username;
		$this->agent_obj->id = $id;

		$cookie_name = "agent";
		$cookie_data = json_encode($this->agent_obj);
		setcookie($cookie_name, $cookie_data, time() + ( 86400 * 365 ), "/"); // 1 year cookie
	}

	public function agent_template($options = []) {
        $options = wp_parse_args($options, [
            'show_bio_first' => true,
        ]);

        $agent = get_userdata($this->agent_obj->id);
        $agentmeta = get_user_meta($this->agent_obj->id);

		$broker_name = isset($agentmeta['ifa-agent-broker-name']) ? $agentmeta['ifa-agent-broker-name'][0] : "";
		$street_address = isset($agentmeta['ifa-agent-street-address']) ? $agentmeta['ifa-agent-street-address'][0] : "";
		$street_address2 = isset($agentmeta['ifa-agent-street-address2']) ? $agentmeta['ifa-agent-street-address2'][0] : "";
		$city = isset($agentmeta['ifa-agent-city']) ? $agentmeta['ifa-agent-city'][0] : "";
		$state = isset($agentmeta['ifa-agent-state']) ? $agentmeta['ifa-agent-state'][0] : "";
		$zip = isset($agentmeta['ifa-agent-zip']) ? $agentmeta['ifa-agent-zip'][0] : "";
		$address_combined = implode(' ', [$street_address, $street_address2, $city, $state, $zip]);
		$has_address = strlen(trim($address_combined)) > 0;
		$office_phone = isset($agentmeta['ifa-agent-office-phone']) ? $agentmeta['ifa-agent-office-phone'][0] : "";
		$mobile_phone = isset($agentmeta['ifa-agent-mobile-phone']) ? $agentmeta['ifa-agent-mobile-phone'][0] : "";
		$fax = isset($agentmeta['ifa-agent-fax']) ? $agentmeta['ifa-agent-fax'][0] : "";
		$headshot = isset($agentmeta['ifa-user-headshot']) ? $agentmeta['ifa-user-headshot'][0] : "";
		$facebook = isset($agentmeta['ifa-facebook']) ? $agentmeta['ifa-facebook'][0] : false;
		$instagram = isset($agentmeta['ifa-instagram']) ? $agentmeta['ifa-instagram'][0] : false;
		$linkedin = isset($agentmeta['ifa-linkedin']) ? $agentmeta['ifa-linkedin'][0] : false;
		$twitter = isset($agentmeta['ifa-twitter']) ? $agentmeta['ifa-twitter'][0] : false;
		$youtube = isset($agentmeta['ifa-youtube']) ? $agentmeta['ifa-youtube'][0] : false;
		?>

		<div id="primary" class="content-area details">

			<main id="main" class="site-main" role="main">

				<article id="team-details" class="team-details">

					<div class="entry-content">

						<div class="ifound-details">

							<div class="ifound-wrap">

								<div class="team-member-details team-member-image"><!-- Team member image -->
									<img src="<?echo $headshot ?>">
								</div>

								<div class="team-member-details team-member-description">
									<h2 class="team-member-title"><?echo implode(" ",$agentmeta['nickname'])?></h2>
									<?php if ($options['show_bio_first']): ?>
                                        <div class="team-member-bio">
                                            <p><? echo implode(" ", $agentmeta['description']) ?></p>
                                        </div>
									<?php endif ?>
									<div class="team-member-info">
										<div class="contact-info">
											<ul>
												<? if($broker_name) { ?><li class="team-member-email"><?= $broker_name?></li><? } ?>
												<? if($agent->user_email) { ?><li class="team-member-email"><i class="fal fa-envelope"></i> <span><a href="mailto:<?echo $agent->user_email ?>"><?echo $agent->user_email ?></a></span></li><? } ?>
												<? if($office_phone) { ?><li class="team-member-phone"><i class="fas fa-phone" title="office phone"></i> <span><a href="tel:<?echo $office_phone ?>"><?echo $office_phone ?></a></span></li><? } ?>
												<? if($mobile_phone) { ?><li class="team-member-phone"><i class="fas fa-mobile-android-alt" title="mobile phone"></i> <span><a href="tel:<?echo $mobile_phone ?>"><?echo $mobile_phone ?></a></span></li><? } ?>
												<? if($fax) { ?><li class="team-member-fax"><i class="fas fa-fax" title="fax"></i> <span><a href="tel:<?echo $fax ?>"><?echo $fax ?></a></span></li><? } ?>
												<? if($has_address) { ?><li class="team-member-location"><i class="far fa-map-marker-alt"></i> <span><?echo $address_combined ?></span></li><? } ?>
											</ul>
										</div>
										<div class="social-icons">
											<? if($facebook) { ?><a href="<? echo $facebook ?>"><i class="fab fa-facebook-square"></i></a><? } ?>
											<? if($instagram) { ?><a href="<? echo $instagram ?>"><i class="fab fa-instagram"></i></a><? } ?>
											<? if($linkedin) { ?><a href="<? echo $linkedin ?>"><i class="fab fa-linkedin"></i></a><? } ?>
											<? if($twitter) { ?><a href="<? echo $twitter ?>"><i class="fab fa-twitter-square"></i></a><? } ?>
											<? if($youtube) { ?><a href="<? echo $youtube ?>"><i class="fab fa-youtube-square"></i></a><? } ?>
										</div>
									</div>
									<?php if (!$options['show_bio_first']): ?>
                                        <div class="team-member-bio">
                                            <p><? echo implode(" ", $agentmeta['description']) ?></p>
                                        </div>
									<?php endif ?>
								</div>

							</div>

						</div>

					</div><!-- .entry-content -->

				</article><!-- #post-## -->

			</main><!-- #main -->

		</div><!-- #primary -->

		<?
	}

	public function additional_user_fields($user) {
	    if (!$this->user_has_team_member_role(['id' => $user->ID])) {
	        return;
	    }

		// Make form able to accept images and hide the gravatar
		echo '<script>document.addEventListener(\'DOMContentLoaded\',function() { document.getElementById(\'your-profile\').enctype=\'multipart/form-data\';document.getElementsByClassName(\'user-profile-picture\')[0].remove();});</script>';

		?>
		<table class="form-table ifa-form-table">
			<tr>
				<th><label for="ifa-facebook"><? _e('Facebook'); ?></label></th>
				<td>
					<input type="text" name="ifa-facebook" id="ifa-facebook" value="<? echo get_user_meta($user->ID, 'ifa-facebook', true); ?>" class="regular-text" />
				</td>
			</tr>
			<tr>
				<th><label for="ifa-linkedin"><? _e('LinkedIn'); ?></label></th>
				<td>
					<input type="text" name="ifa-linkedin" id="ifa-linkedin" value="<? echo get_user_meta($user->ID, 'ifa-linkedin', true); ?>" class="regular-text" />
				</td>

			</tr>
			<tr>
				<th><label for="ifa-twitter"><? _e('Twitter'); ?></label></th>
				<td>
					<input type="text" name="ifa-twitter" id="ifa-twitter" value="<? echo get_user_meta($user->ID, 'ifa-twitter', true); ?>" class="regular-text" />
				</td>

			</tr>
			<tr>
				<th><label for="ifa-youtube"><? _e('YouTube'); ?></label></th>
				<td>
					<input type="text" name="ifa-youtube" id="ifa-youtube" value="<? echo get_user_meta($user->ID,'ifa-youtube', true); ?>" class="regular-text" />
				</td>

			</tr>
			<tr>
				<th><label for="ifa-instagram"><? _e('Instagram'); ?></label></th>
				<td>
					<input type="text" name="ifa-instagram" id="ifa-instagram" value="<? echo get_user_meta($user->ID, 'ifa-instagram', true); ?>" class="regular-text" />
				</td>

			</tr>
			<tr>
				<th><label for="ifa-agent-mls-id"><? _e('MLS ID'); ?></label></th>
				<td>
					<input type="text" name="ifa-agent-mls-id" id="ifa-agent-mls-id" value="<? echo get_user_meta($user->ID, 'ifa-agent-mls-id', true); ?>" class="regular-text" />
				</td>

			</tr>
            <tr>
                <th><label for="ifa-agent-broker-name"><? _e('Broker Name'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-broker-name" id="ifa-agent-broker-name" value="<? echo get_user_meta($user->ID, 'ifa-agent-broker-name', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-street-address"><? _e('Street Address'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-street-address" id="ifa-agent-street-address" value="<? echo get_user_meta($user->ID, 'ifa-agent-street-address', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-street-address2"><? _e('Street Address Line 2'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-street-address2" id="ifa-agent-street-address2" value="<? echo get_user_meta($user->ID, 'ifa-agent-street-address2', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-city"><? _e('City'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-city" id="ifa-agent-city" value="<? echo get_user_meta($user->ID, 'ifa-agent-city', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-state"><? _e('State'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-state" id="ifa-agent-state" value="<? echo get_user_meta($user->ID, 'ifa-agent-state', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-zip"><? _e('Zip'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-zip" id="ifa-agent-zip" value="<? echo get_user_meta($user->ID, 'ifa-agent-zip', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-office-phone"><? _e('Office Phone'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-office-phone" id="ifa-agent-office-phone" value="<? echo get_user_meta($user->ID, 'ifa-agent-office-phone', true); ?>" class="regular-text" />
                </td>

            </tr>
            <tr>
                <th><label for="ifa-agent-mobile-phone"><? _e('Mobile Phone'); ?></label></th>
                <td>
                    <input type="text" name="ifa-agent-mobile-phone" id="ifa-agent-mobile-phone" value="<? echo get_user_meta($user->ID, 'ifa-agent-mobile-phone', true); ?>" class="regular-text" />
                </td>

            </tr>
			<tr>
				<th><label for="ifa-agent-fax"><? _e('Fax'); ?></label></th>
				<td>
					<input type="text" name="ifa-agent-fax" id="ifa-agent-fax" value="<? echo get_user_meta($user->ID, 'ifa-agent-fax', true); ?>" class="regular-text" />
				</td>

			</tr>
			<tr>
				<th><label for="ifa-user-headshot"><? _e('Head Shot'); ?></label></th>
				<td>
					<input type="file" name="ifa-user-headshot" id="ifa-user-headshot" accept="image/*" multiple="false" />
					<? wp_nonce_field('ifa-user-headshot', 'ifa-user-headshot-nonce'); ?>
					<div>
						<img src="<? echo get_user_meta($user->ID, 'ifa-user-headshot', true); ?>" width="250"/>
					</div>
				</td>
			</tr>
			<?
            if ($this->allow_override_broker_logo()) {
                ?>
                <tr>
                    <th><label for="ifa-broker-logo"><? _e('Broker Logo'); ?></label></th>
                    <td>
                        <input type="file" name="ifa-broker-logo" id="ifa-broker-logo" accept="image/*" multiple="false" />
		                <? wp_nonce_field('ifa-broker-logo', 'ifa-broker-logo-nonce'); ?>
                        <div>
                            <img src="<? echo get_user_meta($user->ID, 'ifa-broker-logo', true); ?>" width="250"/>
                        </div>
                    </td>
                </tr>
                <?
            }
            ?>
			<tr>
				<th><label for="ifa-signature-graphic"><? _e('Signature Graphic'); ?></label></th>
				<td>
					<input type="file" name="ifa-signature-graphic" id="ifa-signature-graphic" accept="image/*" multiple="false" />
					<? wp_nonce_field('ifa-signature-graphic', 'ifa-signature-graphic-nonce'); ?>
					<div>
						<img src="<? echo get_user_meta($user->ID, 'ifa-signature-graphic', true); ?>" width="250"/>
					</div>
                    <div>
                        <em>Shown in emails if you use the merge tag {SignatureGraphic}</em>
                    </div>
                </td>
            </tr>
            <?
            if ($this->is_site_admin()) {
			?>
				<tr>
					<th><label>CRM Settings</label></th>
					<td>
						<div>
							<input id="<?= static::$enable_crm_meta_key ?>" type="checkbox" name="<?= static::$enable_crm_meta_key ?>" id="<?= static::$enable_crm_meta_key ?>" value="true" <?= $this->has_crm_enabled(['id' => $user->ID]) ? 'checked="checked"' : ''?>>
                            <label for="<?= static::$enable_crm_meta_key ?>">Enable CRM</label>
						</div>
                        <div style="margin-top:8px;">
                            When checked, iFoundAgent support will be notified and will contact the agent to get billing information
                        </div>
					</td>
				</tr>
			<?php
		}
		?>
		</table>
		<?php
	}

	private function require_php_image_handling_files() {
		require_once(ABSPATH . 'wp-admin/includes/image.php');
		require_once(ABSPATH . 'wp-admin/includes/file.php');
		require_once(ABSPATH . 'wp-admin/includes/media.php');
	}

	public function save_additional_user_fields($user_id) {
		if(!current_user_can('edit_user', $user_id)) {
			return;
		}
		if (!$this->user_has_team_member_role(['id' => $user_id])) {
		    return;
		}
		update_user_meta($user_id, 'ifa-facebook', $_POST['ifa-facebook']);
		update_user_meta($user_id, 'ifa-linkedin', $_POST['ifa-linkedin']);
		update_user_meta($user_id, 'ifa-twitter', $_POST['ifa-twitter']);
		update_user_meta($user_id, 'ifa-youtube', $_POST['ifa-youtube']);
		update_user_meta($user_id, 'ifa-instagram', $_POST['ifa-instagram']);
		// We are capturing the MLS ID, the idea to use it someday although as of this moment we don't.
		update_user_meta($user_id, 'ifa-agent-mls-id', $_POST['ifa-agent-mls-id']);
		update_user_meta($user_id, 'ifa-agent-broker-name', $_POST['ifa-agent-broker-name']);
		update_user_meta($user_id, 'ifa-agent-street-address', $_POST['ifa-agent-street-address']);
		update_user_meta($user_id, 'ifa-agent-street-address2', $_POST['ifa-agent-street-address2']);
		update_user_meta($user_id, 'ifa-agent-city', $_POST['ifa-agent-city']);
		update_user_meta($user_id, 'ifa-agent-state', $_POST['ifa-agent-state']);
		update_user_meta($user_id, 'ifa-agent-zip', $_POST['ifa-agent-zip']);
		update_user_meta($user_id, 'ifa-agent-office-phone', $_POST['ifa-agent-office-phone']);
		update_user_meta($user_id, 'ifa-agent-mobile-phone', $_POST['ifa-agent-mobile-phone']);
		update_user_meta($user_id, 'ifa-agent-fax', $_POST['ifa-agent-fax']);
		if(wp_verify_nonce($_POST['ifa-user-headshot-nonce'], 'ifa-user-headshot')) {
			$this->require_php_image_handling_files();

			$attachment_id = media_handle_upload('ifa-user-headshot', 0);
			if(!is_wp_error($attachment_id)) {
				update_user_meta($user_id, 'ifa-user-headshot', wp_get_attachment_url($attachment_id));
			}
		}
		if ($this->allow_override_broker_logo() && wp_verify_nonce($_POST['ifa-broker-logo-nonce'], 'ifa-broker-logo')) {
			$this->require_php_image_handling_files();

			$attachment_id = media_handle_upload('ifa-broker-logo', 0);
			if(!is_wp_error($attachment_id)) {
				update_user_meta($user_id, 'ifa-broker-logo', wp_get_attachment_url($attachment_id));
			}
		}
		if(wp_verify_nonce($_POST['ifa-signature-graphic-nonce'], 'ifa-signature-graphic')) {
			$this->require_php_image_handling_files();

			$attachment_id = media_handle_upload('ifa-signature-graphic', 0);
			if(!is_wp_error($attachment_id)) {
				update_user_meta($user_id, 'ifa-signature-graphic', wp_get_attachment_url($attachment_id));
			}
		}

		// We only handle this value for admins, because the value is not shown (or submitted) for non-admins.
		if ($this->is_site_admin()) {
			$previous_campaign_builder_enabled_string = get_user_meta($user_id, static::$enable_crm_meta_key, true);
			$enable_crm_string = (isset($_POST[static::$enable_crm_meta_key]) && $_POST[static::$enable_crm_meta_key] === 'true') ? 'true' : 'false';
			if ($enable_crm_string !== $previous_campaign_builder_enabled_string
				// If we the value is false and hasn't been set before, no need to set the value or send an email.
				&& !($enable_crm_string === 'false' && $previous_campaign_builder_enabled_string === '')
			) {
				update_user_meta($user_id, static::$enable_crm_meta_key, $enable_crm_string);
				$user = get_userdata($user_id);
				$name = $user->display_name;
				$link = admin_url('user-edit.php?user_id=' . $user_id);
				$body = <<<EOF
			Name: $name
			Enabled: $enable_crm_string
			Link: $link
EOF;

				iFoundEmail::new_hookless()->email_ifoundagent_staff('CRM status change for multi-user agent', $body);

                if ($enable_crm_string !== 'true') {
                    $this->disable_all_campaigns_for_user($user_id);
                }
			}
		}
	}

    private function disable_all_campaigns_for_user($user_id) {
        $active_search_campaign_ids = iFoundSaveThis::new_hookless()->get_active_campaign_ids_for_user($user_id);
        foreach ($active_search_campaign_ids as $campaign_id) {
            do_action('ifound_update_campaign_status', $campaign_id, 'inactive');
        }

        $ifound_drip_campaign_obj = iFoundDripCampaign::new_hookless();
	    $active_drip_campaign_ids = $ifound_drip_campaign_obj->get_active_campaign_ids_for_user($user_id);
	    foreach ($active_drip_campaign_ids as $campaign_id) {
		    do_action('ifound_update_campaign_status', $campaign_id, 'inactive');
            $ifound_drip_campaign_obj->unschedule_cron_if_needed($campaign_id);
	    }
    }

	// Ensure we always return a string, to prevent === from returning false on int to string comparisons.
	public function teams_crm_id( $options = [] ) {
		if (is_admin() && get_current_user_id()) {
			return (string) get_current_user_id();
		}

		$contact_id = null;
		if (isset($options['contact_id'])) {
			$contact_id = $options['contact_id'];
		} else if (defined('iFOUND_CONTACT_ID')) {
			$contact_id = iFOUND_CONTACT_ID;
		}
		if ($contact_id) {
			$post_author_id = get_post_field('post_author', $contact_id);
			// If the author is an admin, use the shared CRM ID (blank).
			if ($post_author_id && $this->util()->user_has_admin_or_super_role($post_author_id)) {
				return '';
			}
			return (string) $post_author_id;
		}
		if ($this->agent_obj && $this->get_agent_id()) {
			return $this->get_agent_id();
		}
		return '';
	}

	/**
	 * New Team Lead
	 *
	 * Process a new team lead.
	 *
	 * @since 3.0.0
	 *
	 * @param int $contact_id The ID of the new contact.
	 */

	public function new_team_lead( $contact_id ) {
	    $is_team_lead = get_post($contact_id)->post_type !== iFoundPrivateContact::$the_post_type;
		if ($is_team_lead) {
			$content = $this->build_content_for_team_lead_email($contact_id);
			do_action('ifound_team_lead_email', $contact_id, $content);
		}
	}

	public function build_content_for_team_lead_email($contact_id ) {

		ob_start();

		$href   = admin_url( '/post.php?action=edit&post=' . $contact_id );
		$button = '{ContactFirstName} {ContactLastName} Profile'; ?>

        <div class="wrapper">

        <div class="ifound-wrap">

            <h2><? _e( 'Assign your new team lead to a team member.', 'ifound' ); ?></h2>

            <div class="wrapper">

                <div class="ifound-wrap">

                    <a href="<? echo $href; ?>" class="button"><? _e( $button, 'ifound' ); ?></a>

                </div>

            </div>

        </div>

        </div><?

		return ob_get_clean();

	}

	public function install_teams() {

		if( iFOUND_PLUGIN_VERSION !== get_option( 'install_teams' ) ) {

			$this->install_teams_roles_and_caps();
			flush_rewrite_rules();

			update_option( 'install_teams', iFOUND_PLUGIN_VERSION );

		}

	}

	private function install_teams_roles_and_caps() {

		if( get_role( static::$role_name ) ){
			remove_role( static::$role_name );
		}

		add_role(
			static::$role_name,
			__( 'Team Member' ),
			array(
				'read'         					=> true,
				'edit_posts'   					=> false,
				'delete_posts' 					=> false,

				'read_ifound_emails' 			=> true,
				'delete_ifound_emails' 			=> true,
				'edit_ifound_emails' 			=> true,
				'edit_others_ifound_emails'		=> false,
				'publish_ifound_emails' 		=> true,
				'create_ifound_emails' 			=> true,

				'edit_contactss' 				=> true,
				'edit_published_contactss' 		=> true,
				'edit_others_contactss' 		=> false,
				'publish_contactss' 			=> true,
				'create_contactss'				=> false,
				iFoundContacts::$manage_contact_tags_cap_name => true,

				'edit_crm_settings'				=> true,

				'create_campaigns'				=> true,
				'edit_save_this'				=> true,
				'read_save_this'				=> true,
				'delete_save_thiss'				=> true,
				'edit_save_thiss'				=> true,
				'edit_others_save_thiss'		=> false,
				'publish_save_thiss'			=> true,
				'create_save_thiss' 			=> false,

				'edit_private_contact'			=> true,
				'read_private_contact'			=> true,
				'delete_private_contact'		=> true,
				'edit_private_contacts'			=> true,
				'edit_others_private_contacts'	=> false,
				'delete_private_contacts'		=> true,
				'publish_private_contacts'		=> true,
				'create_private_contacts'		=> true,

				'crm_import'					=> true,
				'crm_export'					=> true,

				'edit_drip_template'            => true,
				'read_drip_template'            => true,
				'delete_drip_template'          => true,
				'edit_drip_templates'           => true,
				'edit_others_drip_templates'    => false,
				'delete_drip_templates'         => true,
				'publish_drip_templates'        => true,
				'create_drip_templates'         => true,

				'edit_drip_campaign'            => true,
				'read_drip_campaign'            => true,
				'delete_drip_campaign'          => true,
				'edit_drip_campaigns'           => true,
				'edit_others_drip_campaigns'    => false,
				'delete_drip_campaigns'         => true,
				'publish_drip_campaigns'        => true,
				'create_drip_campaigns'         => true,
			)
		);

	}

	public function admin_columns( $columns ) {

		global $typenow;

		if($this->is_site_admin() && ( $typenow == 'save_this' || $typenow == 'contacts' ) ) {
			$columns = array_slice( $columns, 0, 5, true ) + array( 'author' => __( 'Team Member', 'ifound' ) ) + array_slice( $columns, 5, count( $columns ) - 1, true );
		} else if ($typenow === 'private_contact') {
			$columns = array_slice( $columns, 0, 5, true ) + array( 'owner' => __( 'Owner', 'ifound' ) ) + array_slice( $columns, 5, count( $columns ) - 1, true );
		}

		return $columns;

	}

	public function add_assign_team_members_metabox() {

		global $typenow;

		if( $this->is_site_admin() ) {

			add_meta_box(
				'assign_team_members_metabox',
				__( '<div><i class="far fa-user"></i> Assign Team Member</div>', 'ifound' ),
				array( $this, 'assign_team_members_metabox'),
				$typenow,
				'side',
				'low'
			);

		}

	}

	public function assign_team_members_metabox() {

		global $post;

		$args = array(
			'show_option_none'  => 'Select Team Member',
			'option_none_value' =>  get_current_user_id(),
			'show'              => 'display_name',
			'echo'              => true,
			'selected'          => $post->post_author,
			'name'              => 'post_author',
			'id'                => 'user_id',
			'class'             => 'user_id',
			'role'              => static::$role_name,
		); ?>

		<div class="ifound-wrap">

			<? wp_dropdown_users( $args ); ?>

		</div><?

	}

	public function remove_user_from_blog($user_id, $blog_id, $reassign) {
		// Here's a conundrum. This hook is only called for multisite. Technically the user is only having their access
		// to a particular blog removed. It could be added back later! So, should we call our delete_team_member()? That
		// permanently deletes things, which doesn't feel right if the user isn't actually being deleted. However, our
		// plugin isn't necessarily activated on the multisite itself, and that case, when the user is deleted, our
		// delete_team_member() won't run, so the user's data will never be cleaned up. Our options seem to be:
		//   1) Be on the safe side and don't delete the data, basically meaning it will never be deleted. So the site
		//      will have cruft that never gets cleaned up.
		//   2) Be agressive and delete it, but making it difficult/impossible to restore the user to the blog and
		//      expecting all features of our plugin to work for that user.
		// I will go with option 1, the better-safe-than-sorry approach. This is effectively what we've always done,
		// because before I realized the distinction of removing a user from a blog and deleting them, I didn't even
		// know about this hook.
		//
		// $this->delete_team_member($user_id, $reassign);
	}

	// Reminder that we could get here actually from the remove_user_from_blog hook, if this is a multisite. See our
	// code that hooks into that.
	public function delete_team_member($user_id, $reassign) {
		// When a user is deleted in Wordpress, Wordpress has built-in functionality to transfer ownership to
		// another user. While that might be good for contacts and campaigns, we don't want that for email
		// templates. So delete them.
        $user = get_userdata($user_id);
		if ($reassign && in_array(static::$role_name, $user->roles, true)) {
		    iFoundEmail::new_hookless()->delete_email_templates($user_id);
		}

		$option_names = array_merge(static::$team_member_crm_option_names, [
			'ifound_crm_settings',
		]);
		foreach ($option_names as $option_name) {
			delete_option($option_name . $user_id);
		}

		// Only for West USA, if a team member is deleted, delete the contact.
		$westUsaDomains = [
			'armlsspark.ifoundsites.test',
			'westusaagents.com',
			'westusachoiceagents.com',
		];
		if (in_array($this->util()->get_host(), $westUsaDomains, true)) {
			$args = [
				'meta_key' => 'email',
				'meta_value' => $user->user_email,
				'post_type' => 'contacts',
				'post_status' => 'publish',
			];
			$contacts = get_posts($args);
			if (count($contacts) === 1) {
				$contact = $contacts[0];
				wp_delete_post($contact->ID, true);
			}
		}
	}

	public function post_updated_hook( $contact_id, $after, $before ) {
		// Reminder: we used to guard this code with current_user_can('edit_others_contactss'), but we might not have a
		// user here. One such expected scenario is when a user fills out the registration Gravity Form, which we then
		// create (and potentially subsequently update) a contact record.
		// Furthermore, the old code also checked for isset($_POST['original_publish']), which would only be set in the
		// WP-Admin context.

		// Only do this for contacts posts, not private_contact posts.
		$post_type = get_post_type($contact_id);
		if ($post_type === iFoundContacts::$the_post_type) {
			if ($after->post_author !== $before->post_author) {
				// Reminder: we previously reassigned campaigns from the $before user to the $after user. But it doesn't
				// really make sense, plus it would be weird from a data perspective because the campaign would be owned
				// by the $after user, but it'd refer to header/signature/footer templates owned by the $before user.

				iFoundSharedCampaign::new_hookless()->disable_all_active_campaigns_for_contact_id($contact_id);

				// Notify $after user of assigned contact.
				if (iFoundTeams::new_hookless()->user_has_team_member_role(['id' => $after->post_author])) {
					$crm_id = $this->crm_id_from_user_id($after->post_author);
					$full_name = iFoundJointContact::new_hookless()->get_full_name($contact_id);
					$host = $this->util()->get_host();
					$subject = "A contact on {$host} has been assigned to you: {$full_name}";
					$link = $this->util()->build_post_href($contact_id, 'edit', true);
					$message = <<<EOT
						A contact on {$host} has been assigned to you: {$full_name}

						{$link}
						EOT;
					iFoundEmail::new_hookless()->agent_email($subject, $message, ['crm_id' => $crm_id]);
				}
			}
		}
		if (iFoundJointContact::new_hookless()->is_post_type_a_contact($post_type)) {
			if ($after->post_author !== $before->post_author) {
				if (has_term(iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE, iFoundContacts::$the_taxonomy,
					$after)) {
					iFoundContacts::$is_currently_reassigning_contact_owner = true;
					wp_remove_object_terms($contact_id, iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE,
						iFoundContacts::$the_taxonomy);
					iFoundContacts::$is_currently_reassigning_contact_owner = false;
				}
			}
		}
	}

	// This will copy the email templates on the admin account to the user.
	// The exception is for the signature template, which wouldn't make sense, so we'll copy it from the HTML file
	// template.
	//
	// This also sets the Default Email Templates for the user.
	private function copy_admin_email_templates_to_user($user_id) {
		$admin_id = 1;
		$email_templates = get_posts([
			'post_type'   => 'ifound_email',
			'numberposts' => -1,
			'author' => $admin_id,
		]);
		$crm_settings_for_admin = get_option('ifound_crm_settings');
		$default_email_template_names = [
			'header',
			'footer',
			'signature',
			'search_notice',
			'prop_notice',
            iFoundEmail::$INSTANT_UPDATE,
            iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED,
			'prop_alert',
			'homeowner_campaign',
            iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE,
		];
		// We create a map that is keyed by email template IDs, with values being the name of the crm setting.
		// That way we can copy the setting from the admin to the new user with his/her email template ID.
		$default_email_templates_map = array_reduce($default_email_template_names, function($carry, $default_email_template_name) use ($crm_settings_for_admin) {
			$carry[$crm_settings_for_admin[$default_email_template_name]] = $default_email_template_name;
			return $carry;
		}, []);
		$ifound_crm_settings_option_name_for_user = 'ifound_crm_settings' . $user_id;
		$crm_settings_for_user = get_option($ifound_crm_settings_option_name_for_user);
		// Must register the taxonomy before we insert a term using the taxonomy.
		iFoundEmail::new_hookless()->taxonomy();
		foreach ($email_templates as $email_template) {
			$original_post_id = $email_template->ID;
			$original_post_type = $email_template->post_type;

			// Set the ID to null so we create a copy.
			$email_template->ID = null;
			$email_template->post_author = $user_id;
			// Just for the signature, get the content from the HTML file.
			if (isset($default_email_templates_map[$original_post_id])) {
			    if ($default_email_templates_map[$original_post_id] === 'signature') {
				    $content = $this->util()->get_html('email/', 'default-signature-team-member');
			        $email_template->post_content = $content;
			    }
			}
			$new_team_member_email_template_id = wp_insert_post($email_template);

			$original_meta_data = get_post_meta($original_post_id);
			foreach ($original_meta_data as $meta_key => $meta_value) {
				update_post_meta($new_team_member_email_template_id, $meta_key, maybe_unserialize($meta_value[0]));
			}

			// Copy over all taxonomies.
			$taxonomies = get_object_taxonomies($original_post_type);
			foreach ($taxonomies as $taxonomy) {
				$post_terms = wp_get_object_terms($original_post_id, $taxonomy, array( 'fields' => 'slugs' ));
				wp_set_object_terms($new_team_member_email_template_id, $post_terms, $taxonomy, false);
			}

			// Set the Default Email Templates for the user.
			if (isset($default_email_templates_map[$original_post_id])) {
			    $crm_settings_for_user[$default_email_templates_map[$original_post_id]] = $new_team_member_email_template_id;
			}
		}
		update_option($ifound_crm_settings_option_name_for_user, $crm_settings_for_user);
	}

	public function create_crm_settings($user_id) {
	    $user = get_userdata($user_id);
	    $crm_settings = [
		    // We use the display_name here instead of nickname because the nickname hasn't been set yet, and we don't
		    // want the email from name as e.g. 'user123'.
		    'from_name'                    => $user->display_name,
		    'from_email'                   => $user->user_email,
		    'time_of_day'                  => '8:00:00',
		    'qty_logs_to_report'           => 30,
		    'contact_visit_email'          => 'enabled',
		];
		update_option('ifound_crm_settings' . $user_id, $crm_settings);

		$this->copy_admin_email_templates_to_user($user_id);
	}

	// I'm calling these 'base' options as opposed to admin options only because they aren't necessarily associated
	// with a particular ID. However it's traditionally been an admin making changes to them in our plugin.
	public function copy_base_options_to_user($user_id) {
		foreach (static::$team_member_crm_option_names as $option_name) {
			$value = get_option($option_name);
			$option_name_for_new_user = $option_name . $user_id;
			update_option($option_name_for_new_user, $value);
		}
	}

	// When a user is created with a role of team member, we need to set them up, by creating email templates, adding
	// settings to the options table, etc.
	// Reminder: when a new user is created on a multi-site, their role is not set when this hook is called (the role is
	// always subscriber as this point, as far as my testing goes). So for multi-site, see the wpmu_activate_user hook.
	public function user_register($user_id) {
	    if ($this->user_has_team_member_role(['id' => $user_id])) {
	        $this->set_up_team_member($user_id);
	    }
	}

	public function set_up_team_member_if_needed($user_id) {
		$settings = get_option('ifound_crm_settings' . $user_id);
		if (!$settings) {
			$this->set_up_team_member($user_id);
		}
	}

	public function set_up_team_member($user_id) {
		$this->create_crm_settings($user_id);
		$this->copy_base_options_to_user($user_id);
	}

	// When a new user is created via multi-site, their actual role is not set in the user_register hook. It is set
	// by the time this hook runs, so we set up the team member here if their role is team member.
	// Actually, turns out this is only called when a super admin checks the "Skip Confirmation Email" checkbox when
	// creating a new user. (Technically, the hook is always called, however, if the checkbox is not checked, or if the
	// new user is created by a non-super admin such that there isn't a checkbox, then the user will be sent a
	// confirmation email, and then the hook will be called when the user follows the link in the email, which takes
	// them to wp-activate.php, which does not load any plugins including ours. Hence, we make use of the wp_login hook,
	// see below.)
	public function wpmu_activate_user($user_id, $password, $meta) {
		if ($meta['new_role'] === static::$role_name) {
			$this->set_up_team_member($user_id);
		}
	}

	public function set_user_role($user_id, $role, $old_roles) {
		// TODO: If the user's role is changed to team member, we should do all the things that we do for a new user
		// whose role is team member. However, we'd need to be smart about it and not duplicate any values for the
		// scenario where they were previously a team member, then weren't, then are again.
		// I am not going to bother with it for now.
	}

	// In the scenario that we are on multisite, and a user is sent a confirmation email when a new user is created, we
	// can't hook into any hooks, as explained in the wpmu_activate_user() above. So, when the user logs in, we check if
	// they are a team member and if we have not run set_up_team_member() for them before. A reasonable question to ask
	// is: why do we use the user_register and wpmu_activate_user hooks at all, and not just always rely on the wp_login
	// hook only? My thought is that super admins might not want to wait for the new user to activate their account and
	// login. Just a hunch and we could change it if it's inconvenient to use several hooks.
	// If you like pain, you can follow the bug filed about this situation: https://core.trac.wordpress.org/ticket/23197
	public function wp_login($user_login, $user) {
		// get_current_user_id() is 0 here, so we get the ID from $user->ID
		if ($this->user_has_team_member_role(['id' => $user->ID])) {
			$this->set_up_team_member_if_needed($user->ID);
		}
	}

	public function user_has_team_member_role($options = []) {
		$options = wp_parse_args($options, [
			'id' => get_current_user_id(),
		]);
		return $this->util()->user_has_role($options['id'], static::$role_name);
	}

	public function mc_admin_users_caps( $caps, $cap, $user_id, $args ){

		foreach( $caps as $key => $capability ){

			if( $capability != 'do_not_allow' )
				continue;

			switch( $cap ) {
				case 'edit_user':
				case 'edit_users':
					$caps[$key] = 'edit_users';
					break;
				case 'delete_user':
				case 'delete_users':
					$caps[$key] = 'delete_users';
					break;
				case 'create_users':
					$caps[$key] = $cap;
					break;
			}
		}

		return $caps;
	}

	public function mc_edit_permission_check() {
		global $current_user, $profileuser;

		$screen = get_current_screen();

		get_currentuserinfo();

		if( ! is_super_admin( $current_user->ID ) && in_array( $screen->base, array( 'user-edit', 'user-edit-network' ) ) ) { // editing a user profile
			if ( is_super_admin( $profileuser->ID ) ) { // trying to edit a superadmin while less than a superadmin
				wp_die( __( 'You do not have permission to edit this user.' ) );
			} elseif ( ! ( is_user_member_of_blog( $profileuser->ID, get_current_blog_id() ) && is_user_member_of_blog( $current_user->ID, get_current_blog_id() ) )) { // editing user and edited user aren't members of the same blog
				wp_die( __( 'You do not have permission to edit this user.' ) );
			}
		}

	}

	public function has_crm_enabled($options = []) {
		$options = wp_parse_args($options, [
			'id' => get_current_user_id(),
		]);
		$crm_enabled = get_user_meta($options['id'], static::$enable_crm_meta_key, true) === 'true';
		return $crm_enabled;
	}

	// We have Agent Registration pages where we allow agents of certain brokers to quickly sign up for a team member
	// account. The page has a gravity form, which uses the User Registration add-on to create the user. However, it's
	// possible the agents will never log in, which means our team member set up code will not run, which means their
	// search (and quick search) criteria will not be set and even their public pages will look bad or not work. So,
	// let's set up their team member data.
	public function gform_user_registered($user_id, $feed, $entry, $user_pass) {
		$form = \GFAPI::get_form($feed['form_id']);
		$form_type_field = array_reduce($form['fields'], function($carry, $field) {
			if (strtolower($field->label) === 'ifound form type') {
				return $field;
			}
			return $carry;
		});
		if ($form_type_field) {
			$entry_id_for_ifound_form_type = $form_type_field->id;
			$form_type = $entry[$entry_id_for_ifound_form_type];
			if ($form_type === 'team_member_registration') {
				$this->set_up_team_member_if_needed($user_id);

				// It is possible for the agent to pay for their account on the form. If they do so, let's check the
				// "Enable CRM" checkbox for their user, so our staff doesn't have to do it manually.
				$package_type_field = array_reduce($form['fields'], function($carry, $field) {
					if (strtolower($field->label) === 'please choose a free website package') {
		                return $field;
	                }
	                return $carry;
				});
				if ($package_type_field) {
					$entry_id_for_package_type = $package_type_field->id;
					$package_type = $entry[$entry_id_for_package_type];
					if (strpos(strtolower($package_type), 'free site + crm') !== false) {
						$key = static::$enable_crm_meta_key;
						update_user_meta($user_id, static::$enable_crm_meta_key, 'true');
					}
				}
			}
		}
	}

	public function get_team_member_url($site_url) {
		$parsed_url = parse_url($site_url);
		$user_id = get_current_user_id();
		$user_data = get_userdata($user_id);
		$parsed_url['path'] = "/{$this->agent}/{$user_data->user_nicename}";
		$site_url = $this->util()->unparse_url($parsed_url);
		return $site_url;
	}
}

foreach ( glob( plugin_dir_path( __FILE__ ) . 'includes/*.php' ) as $file ) {
	require_once $file;
}
