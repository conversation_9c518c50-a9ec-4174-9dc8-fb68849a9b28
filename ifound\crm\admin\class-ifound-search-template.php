<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );
	
/**
 * iFoundSearchTemplate Class
 *
 * @since 3.4.0
 */
 
class iFoundSearchTemplate extends iFound {

	protected $table    	= 'ifound_meta';
	private   $object_id 	= 1;
	private   $meta_key		= 'search_template';

	/**
	 * init iFoundSearchTemplate class.
	 *
	 * @since 3.4.0
	 */
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }
	
	/**
	 * Constructor
	 *
	 * @since 3.4.0
	 */
	 
	public function __construct() {

		add_action( 'admin_enqueue_scripts', array( $this, 'scripts' ) );
		add_action( 'ifound_search_template_box', array( $this, 'search_template_box' ) );

		add_action( 'wp_ajax_load_search_template', array( $this, 'load_search_template' ) );
		add_action( 'wp_ajax_add_search_template', array( $this, 'add_search_template' ) );
		add_action( 'wp_ajax_update_search_template', array( $this, 'update_search_template' ) );
		add_action( 'wp_ajax_delete_search_template', array( $this, 'delete_search_template' ) );

	}

	/**
	 * Scripts
	 *
	 * @since 3.4.0
	 */
	 
	public function scripts(){

		wp_register_style( 'search_template_css', plugins_url( 'css/search-template.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		wp_register_script( 'search_template_js', plugins_url( 'js/search-template.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );	
		wp_localize_script( 'search_template_js', 'search_template', array(
			'endpoint' 	=> admin_url( 'admin-ajax.php' ),
			'nonce' 	=> wp_create_nonce( 'search_template_secure_me' ),
			'json'		=> $this->search_template_json()
		));

	}

	/**
	 * Search Template Box
	 *
	 * Add search template box to campaign builder.
	 *
	 * @since 3.4.0
	 */
	
	public function search_template_box() {
		
		wp_enqueue_script( 'search_template_js' ); 
		wp_enqueue_style( 'search_template_css' ); ?>

		<!-- Strat Search Template -->

		<div class="search-template-wrappper" id="search-template-wrappper">
			
			<div class="ifound-wrap">

				<div class="criteria-heading"><i class="fal fa-clone"></i> <? _e( 'Saved Search Templates', 'ifound' ); ?></div>

				<div class="criteria-body">
				
					<table class="form-table">
						
						<tbody>

							<tr class="search-template-heading">
								
								<td colspan="2"><? _e( 'Choose from an existing template or enter new title.', 'ifound' ); ?></td>
							
							</tr>
							
							<tr>

								<td>
									
									<input type="text" id="search-template" class="large-text" placeholder="Start Typing..." autocomplete="off">

									<input type="hidden" id="meta-id">

								</td>

								<td>

									<div class="search-template-button-wrap">

										<div class="ifound-wrap">

											<span class="button button-primary do-search-template" id="load_search_template"><i class="far fa-search-plus"></i> <? _e( 'Load', 'ifound' ); ?></span>

										</div>

									</div>

								</td>

								<td class="search-template-td">

									<div class="search-template-button-wrap">

										<div class="ifound-wrap">

											<span class="button button-primary do-search-template" id="add_search_template"><i class="far fa-save"></i> <? _e( 'Save As New', 'ifound' ); ?></span>

										</div>

									</div>

									<div class="search-template-button-wrap">

										<div class="ifound-wrap">

											<span class="button button-primary do-search-template" id="update_search_template"><i class="far fa-edit"></i> <? _e( 'Update', 'ifound' ); ?></span>

										</div>

									</div>

									<div class="search-template-button-wrap">

										<div class="ifound-wrap">
											
											<span class="button button-primary do-search-template" id="delete_search_template"><i class="far fa-trash-alt"></i> <? _e( 'Delete', 'ifound' ); ?></span>

										</div>

									</div>

								</td>

							</tr>

						</tbody>

					</table>

				</div>

			</div>

		</div>

		<!-- End Search Template --><?
	
	} 

	public function search_template_json() {

		$rows = $this->get_meta( $this->object_id, $this->meta_key );

		if( is_array( $rows ) ) {

			foreach ($rows as $row ) {
				
				$items[] = array( 'key' => $row->meta_id, 'value' => $row->meta_value['search_template'] );

			}

		}

		return empty( $items ) ? false : $items;

	}

	public function load_search_template() {

		check_ajax_referer( 'search_template_secure_me', 'search_template_nonce' );

		$response = $this->request_failed();

		$input = $_REQUEST['input'];

		$meta_id = intval( $input['meta_id'] );

		if ( is_int( $meta_id ) && $meta_id > 0 ) {

			if( $meta = $this->get_meta_single( $meta_id ) ) {
				
				ob_start();
				do_action( 'ifound_dynamic_form', $meta['search_criteria'] );
				$body = ob_get_clean();

				$results['input_obj'] = $meta['search_criteria'];
				
				$response = array(
					'class' 	=> 'fa-search-plus',
					'success'	=> true,
					'data'		=> array(
						'body' 		=> $body,
						'map_data' 	=> apply_filters( 'ifound_map_data', apply_filters( 'ifound_obj', $results ) )
					)
				);

			}

		}

		echo json_encode( $response );

		die();

	}

	public function add_search_template() {

		check_ajax_referer( 'search_template_secure_me', 'search_template_nonce' );

		$input = $_REQUEST['input'];

		$template = apply_filters( 'ifound_sanitize', $input['search_template'] );
		$criteria = $this->prepare_criteria( $input );

		if ( ! empty( $template ) && ! empty( $criteria ) ) {

			$meta_value = array(
				'search_template' => $template,
				'search_criteria' => $criteria
			);

			$meta_id = $this->add_meta( $this->object_id, $this->meta_key, $meta_value );

			$response = array(
				'class' 	=> 'fa-save',
				'success'	=> true,
				'item'		=> array( 
					'key' 	=> $meta_id, 
					'value' => $template 
				)
			);

		} else {

			$response = $this->request_failed();

		}

		echo json_encode( $response );

		die();

	}

	public function update_search_template() {

		check_ajax_referer( 'search_template_secure_me', 'search_template_nonce' );

		$input = $_REQUEST['input'];

		$meta_id  = intval( $input['meta_id'] );
		$template = apply_filters( 'ifound_sanitize', $input['search_template'] );
		$criteria = $this->prepare_criteria( $input );

		if ( is_int( $meta_id ) && $meta_id > 0 && ! empty( $template ) && ! empty( $criteria ) ) {

			$meta_value = array(
				'search_template' => $template,
				'search_criteria' => $criteria
			);

			$this->update_meta( $meta_id, $this->meta_key, $meta_value );

			$response = array(
				'class' 	=> 'fa-edit',
				'success'	=> true
			);

		} else {

			$response = $this->request_failed();

		}

		echo json_encode( $response );

		die();

	}

	public function delete_search_template() {

		check_ajax_referer( 'search_template_secure_me', 'search_template_nonce' );

		$input = apply_filters( 'ifound_sanitize', $_REQUEST['input'] );

		$meta_id  = intval( $input['meta_id'] );

		if ( is_int( $meta_id ) && $meta_id > 0 ) {

			$this->delete_meta( $meta_id );

			$response = array(
				'class' 	=> 'fa-trash-alt',
				'success'	=> true
			);

		} else {

			$response = $this->request_failed();

		}

		echo json_encode( $response );

		die();

	}

	public function request_failed() {

		return array(
			'class' 	=> 'fa-exclamation-triangle',
			'success'	=> false
		);

	}

	public function prepare_criteria( $input ) {

		parse_str( $input['search_criteria'], $criteria );
		return array_filter( $criteria );

	}

}
