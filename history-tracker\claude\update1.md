Here's a change I need you to make. Earlier, I told you the tables we're dealing with are property, property_changes, and property_history. However, the system actually connects to many different MLSs, and the real estate listings for each MLS are in a separate property table.

When the History Maker CLI tool runs, it needs to take a configuration name on the command line. The configuration name will be a key, that will grab a new section of the config.js file. Name the section mls_systems. Each "mls_system" will look something like this:

property_table_name: property
property_changes_table_name: property_changes
property_history_table_name: property_history

So, you won't use the hard-coded table names I gave you, but will instead get them from an entry in the config.js file. Make sense? You'll need to update the source code and tests.
