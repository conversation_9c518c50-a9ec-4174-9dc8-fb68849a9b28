# User Login

## Background

We're working with an IDX system (real estate / MLS stuff) that is deployed as a series of wordpress sites. The sites allow end users to search for homes and view details about them.

Our existing code is a wordpress plugin.

You'll be writing PHP code that we'll use from our wordpress plugin.

## Requirements

* Allow user logins via email (no username), see the **Plugins** section later
* Save MLS IDs to database. We need to timestamp the records in the database.
* Save searches (URL strings) to database. We'll also need to save a human readable string so the user will know what the search does without having to try to decipher the URL. Again, a timestamp.
* 'Home page' for user, which will display these saved values
	* One table to show saved homes, in reverse chronologic order of when they were saved
	* One table to show saved searches, also in reverse chronologic order

## Task outline

### Wordpress

Set up your own new wordpress installation. WMPS (aka WPMU) is preferred because that is what we're moving to, even though we do have non-multisite installations. We are targeting wordpress 3.4+.

### Database

Set up your own wordpress database (as part of the wordpress installation above). To accomplish the requirements, you'll need to create extra tables in the wordpress database. Each table will need to be joined to the existing wordpress users table, so you'll need a users_id field that's a foreign key. Read more in the **Code** section.

### Plugins

I'm hoping using plugins such as "Login with Ajax", wp-email-login, and wp-email-smtp can accomplish the requirement to allow login with email only. However, this might not be so. We might need some extra code to handle this. Please look into this task last.

### Code

All of the code in the repo can be thought of as if going directly in a wordpress "plugins" directory's subfolder. Feel free to split your code into multiple files if you want. Your code should be well organized into classes.

Each file from you is expected to be dropped into our plugin folder. You may organize yours into a subfolder if you wish.

#### Vision

Your code should expose an API that will be called at the appropriate time by our code. To accomplish the requirements, the API will look something like this:

* `yourAPIObj->createTables()` - this code will be called when our plugin is installed, and will do the actual work of creating your tables inside the wordpress database.
* `yourAPIObj->saveMLSForUser(mlsID, userID)`
* `yourAPIObj->saveSearchForUser(url, searchName, userID)`

You should also have 'action' methods that write (HTML and such) to the output stream. We use `ob_start()` and `ob_get_clean()` calls to buffer the output. These methods should look something like:

* `yourAPIObj->renderSavedHomesForUser(userID)`
* `yourAPIObj->renderSavedSearchesForUser(userID)`

#### Requirements

* Use tabs, not spaces
* If you plan to use a piece of technology other than the plugins mentioned on the oDesk post, please ask. Hopefully this should be pretty simple stuff, but if you prefer to use ORM tools and the like, let me know first.

#### Testing

TDD using PHPSpec(2) is strongly encouraged.

Unless you use PHPSpec or similar, you'll have to figure out how to simulate the calls to your code from wordpress.

## Beyond

I'm looking for a good developer. Ideally that person would know wordpress well, but a good programmer who can communicate well (and often) will have an opportunity to stay on longer with other projects.