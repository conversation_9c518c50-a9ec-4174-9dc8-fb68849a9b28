upstream idx_server {
    server newidx:8155;
}

# server {
#     listen 80;
#     server_name api.profoundidx.com;
#
#     location / {
#         return 301 https://$host$request_uri;
#     }
#
#     location /.well-known/acme-challenge/ {
#         root /var/www/certbot;
#     }
# }

# Our old plugin is using http (not https)
server {
    listen 80;
    server_name api.profoundidx.com;

    access_log off;
    error_log /var/log/nginx/newidx-error.log;

    location / {
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header Host $http_host;
       proxy_set_header X-NginX-Proxy true;

       proxy_pass http://idx_server;
       proxy_redirect off;

       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";

       proxy_pass_request_headers on;
   }
}

server {
    listen 443 ssl;
    server_name api.profoundidx.com;

    access_log off;
    error_log /var/log/nginx/newidx-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/profoundidx.com-0002/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/profoundidx.com-0002/privkey.pem;

    location / {
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header Host $http_host;
       proxy_set_header X-NginX-Proxy true;

       proxy_pass http://idx_server;
       proxy_redirect off;

       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";

       proxy_pass_request_headers on;
   }
}
