<?
/**
 * iFOUND_create_shortcode class
 *
 * Create Shortcodes
 *
 * @package iFOUND
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );
 
class iFoundCreateShortcode extends iFoundIdx {
	
	/**
	 * init iFOUND_create_shortcode class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}
	
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	 
	public function __construct() {
		add_action( 'admin_head', array( $this, 'mce_buttons' ) );
		add_action( 'admin_head', array( $this, 'ifound_admin' ) );
		add_action( 'admin_footer', array( $this, 'shortcode_pop' ) );
		add_action( 'ifound_shortcode_editor', array( $this, 'shortcode_editor' ), 10, 1 );
		add_action( 'wp_ajax_shortcode_ajax', array( $this, 'shortcode_ajax' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'scripts' ) );
	}
	
	public function ifound_admin() {
		global $typenow;
		
    if( ! current_user_can( 'edit_posts' ) && ! current_user_can( 'edit_pages' ) )
      return;
    	
		if( ! in_array( $typenow, array( 'post', 'page' ) ) )
        	return;
		
		$endpionts = array(
			'endpoint' 	=> admin_url( 'admin-ajax.php' ),
			'nonce' 	=> wp_create_nonce( 'shortcode_ajax_secure_me' )
		);
		
		?>
		
		<script>
			var ifound_admin = <? echo json_encode( $endpionts ); ?>
		</script>
		
		<?
		
	}
	
	public function mce_buttons() {
		
    	global $typenow;
		
    	if( ! current_user_can( 'edit_posts' ) && ! current_user_can( 'edit_pages' ) )
    		return;
    	
		if( ! in_array( $typenow, array( 'post', 'page' ) ) )
        	return;
    	
		if( get_user_option( 'rich_editing' ) == 'true' ) {
       	 	
			add_filter( 'mce_external_plugins', array( $this, 'tinymce_plugin' ) );
        	add_filter( 'mce_buttons', array( $this, 'register_button' ) );
    	
		}
		
	}

	public function scripts() {
		if (method_exists(get_current_screen(), 'is_block_editor') && get_current_screen()->is_block_editor()) {
			wp_register_script('ifound_shortcode', plugins_url('js/create-shortcode.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);
			wp_localize_script('ifound_shortcode', 'ifound_shortcode', [
				'is_block_editor' => true,
			]);
			wp_enqueue_script('ifound_shortcode');
		}
	}
	
	public function tinymce_plugin( $plugin_array ) {
    	
		$plugin_array['ifound_shortcode'] = plugins_url( 'js/create-shortcode.js?ver=' . iFOUND_PLUGIN_VERSION, __FILE__ );
   			
		return $plugin_array;
		
	}
	
	public function register_button( $buttons ) {
   		
		array_push( $buttons, 'ifound_shortcode' );
  		 
		return $buttons;
		
	}
	
	public function shortcode_pop() {
		
		global $typenow;
    	
		if( ! current_user_can( 'edit_posts' ) && ! current_user_can( 'edit_pages' ) )
    		return;
    	
		if( ! in_array( $typenow, array( 'post','page' ) ) )
        	return;
    	
		if( get_user_option( 'rich_editing' ) == 'true' ) {
			
			wp_enqueue_script( 'cookie_js' );
			wp_enqueue_script( 'ifound_map_js' );
			wp_enqueue_script( 'save_this_js' );
			wp_enqueue_script( 'dynamic_js' );
			wp_enqueue_script( 'ifound_spa_js' );
			wp_enqueue_script( 'jquery-ui-datepicker' );
			
			wp_enqueue_style( 'shortcode_dashboard_css' ); ?>
			
			<div class="pop-up pop-drop">
			
				<div class="pop-up pop-box">
					
					<div class="wrap">
						
						<div class="shortcode-close-wrapper">
							<i class="fal fa-2x fa-times-circle shortcode-close" aria-hidden="true"></i>
						</div>
						
						<div class="shortcode-heading">
							<i class="fal fa-tachometer" aria-hidden="true"></i>
							<? _e( 'Search Creator Dashboard', 'ifound' ); ?>
						</div><?
					
						do_action( 'ifound_load_map', $extra );
						do_action( 'ifound_shapes_map' ); ?>
						
						<div class="default-search-criteria">
				
							<div class="ifound-wrap">
				
								<div class="default-criteria-heading">
						
									<i class="fal fa-search" aria-hidden="true"></i>
									<? _e( 'Filters', 'ifound' ); ?>
										
								</div>
						
								<div class="default-criteria-wrapper"><?
						
									do_action( 'ifound_search_criteria', 'ifound_adv_search_criteria', 'search-', true ); ?>
							
								</div>
				
							</div>
			
						</div>
								
						<div class="additional-criteria">
				
							<div class="ifound-wrap">

								<div class="criteria-heading">

									<i class="fal fa-plus-circle" aria-hidden="true"></i>
									<? _e( 'More Filters', 'ifound' ); ?>


								</div><?

								do_action( 'ifound_search_criteria', 'ifound_more_adv_search_criteria' ); ?>

							</div>

						</div><?
						
						do_action( 'ifound_dynamic_form_wrapper', false ); ?>
						
						<div class="backup-wrapper">
							<h4><i class="fal fa-clone" aria-hidden="true"></i> <? _e( 'Backup Query', 'ifound' ); ?></h4>
							<div class="wrap">
								<form id="ifound-backup-form">
									
								</form>
								<div class="clear"></div>
							</div>
							<div class="backup-button button">
								<? _e( 'Create Backup Query', 'ifound' ); ?>
							</div>
						</div>

						<div class="display-options-wrapper">
				
							<div class="ifound-wrap">
				
								<div class="display-options-criteria-heading">
						
									<i class="fal fa-television" aria-hidden="true"></i>
									<? _e( 'Display Options', 'ifound' ); ?>
							
								</div>
						
								<div class="display-options">
				
									<? do_action( 'ifound_display_options_form', false ); ?>		
							
								</div>
					
							</div>
					
						</div>

						<input type="hidden" name="extra_map_data" id="extra-map-data">

						<div class="display-stats-wrapper">

							<div class="ifound-wrap">

								<div class="display-stats-criteria-heading">

									<i class="fal fa-chart-line" aria-hidden="true"></i>
									<? _e( 'Stats', 'ifound' ); ?>

								</div>

								<div class="display-stats">

									<? do_action( 'ifound_display_stats_form', false ); ?>

								</div>

							</div>

						</div>

						<div class="shortcode-button-wrapper">
							
							<div class="shortcode-button button button-large wp-core-ui button-primary">
								<i class="fal fa-plus-square" aria-hidden="true"></i>
								<? _e( 'Create/Save Search', 'ifound' ); ?>
							</div>
							
							<div class="shortcode-close button button-large wp-core-ui button-primary">
								<i class="fal fa-sign-out" aria-hidden="true"></i>
								<? _e( 'Exit Without Saving', 'ifound' ); ?>
							</div>
							
						</div>
							
					</div>
				
				</div>
			
			</div>
			
			<?
			
		}
	}
	
	public function shortcode_ajax() {
		
		check_ajax_referer( 'shortcode_ajax_secure_me', 'shortcode_ajax_nonce' );

		$input = (object) $_REQUEST['input'];
		
		$meta_id = intval( $input->meta_id );
		$post_id = intval( $input->post_id );
		
		parse_str( $input->params, $query );
		$query = array_filter( $query );
		
		parse_str( $input->backup, $backup_query );
		$backup_query = array_filter( $backup_query );

		parse_str( $input->display, $display );
		$display = array_filter( $display );

		parse_str( $input->stats, $stats );
		$stats = array_filter( $stats );
		if (count($stats) > 0) {
		    $stats = json_decode($stats['stats_data'], true);
			// Let's version the stats so we can more easily upgrade them in the future.
			$stats['version'] = static::$stats_version;
		}
		$extra_map_data = json_decode(stripslashes($input->extra_map_data), true);

		$query['items_per_page'] 		= empty( $display['items_per_page'] ) ? false : $display['items_per_page'];
		$backup_query['items_per_page'] = empty( $display['items_per_page'] ) ? false : $display['items_per_page'];
		unset( $display['items_per_page'] );

		$data = array_filter( array(
			'query'          => array_filter($query),
			'backup_query'   => array_filter($backup_query),
			'display'        => array_filter($display),
			'stats'          => array_filter($stats),
			'extra_map_data' => array_filter($extra_map_data ?? []),
		));
		
		/** Update */
		if( $meta_id ) {
			
			$this->update_meta_by_id( $meta_id, $data );
				 
		} else {
			
			if( $post_id ) {
				
				//Add new Meta
				$meta_id = add_post_meta( $post_id, 'save_this_shortcode', $data );
				
			}
			
		}
		
		if( $meta_id && $meta_id > 0 ) {
		
			echo json_encode( $meta_id );
			
		}

		die();
		
	}
	
}
