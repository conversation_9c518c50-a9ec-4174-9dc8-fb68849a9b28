// @link https://hugh.blog/2015/12/18/create-a-custom-featured-image-box/
jQuery(document).ready(function($) {

	// Uploading files
	var file_frame;

	jQuery.fn.upload_featured_image = function( button ) {
		var button_id = button.attr('id');
		var field_id = button_id.replace( '_button', '' );

		// If the media frame already exists, reopen it.
		if ( file_frame ) {
		  file_frame.open();
		  return;
		}

		// Create the media frame.
		file_frame = wp.media.frames.file_frame = wp.media({
		  title: $( this ).data( 'uploader_title' ),
		  button: {
		    text: $( this ).data( 'uploader_button_text' ),
		  },
		  multiple: false
		});

		// When an image is selected, run a callback.
		file_frame.on( 'select', function() {
		  var attachment = file_frame.state().get('selection').first().toJSON();
		  $("#"+field_id).val(attachment.id);
		  $("#featuredimagediv img").attr('src',attachment.url);
		  $( '#featuredimagediv img' ).show();
		  $( '#' + button_id ).attr( 'id', 'remove_featured_image_button' );
		  $( '#remove_featured_image_button' ).html( '<i class="far fa-trash-alt"></i> Remove featured image' );
		});

		// Finally, open the modal
		file_frame.open();
	};

	$('#featuredimagediv').on( 'click', '#upload_featured_image_button', function( event ) {
		event.preventDefault();
		$.fn.upload_featured_image( $(this) );
	});

	$('#featuredimagediv').on( 'click', '#remove_featured_image_button', function( event ) {
		event.preventDefault();
		$( '#upload_featured_image' ).val( '' );
		$( '#featuredimagediv img' ).attr( 'src', '' );
		$( '#featuredimagediv img' ).hide();
		$( this ).attr( 'id', 'upload_featured_image_button' );
		$( '#upload_featured_image_button' ).html( '<i class="far fa-images"></i> Set featured image' );
	});

});