jQuery( document ).ready( function( $ ) {
  	
	$( '.build-site' ).on( 'click', function() {
		
		var client_id = $( this ).attr( 'client_id' );
		$('.build-site-msg, .build-site-success').html('');

		$.ajax ( {
			url : build_site.endpoint,
			type : 'post', 
			data : {
				action : 'build_site',
				client_id : client_id,
				build_site_nonce : build_site.nonce,
			},
			beforeSend: function() {
				$( '.response' ).addClass( 'fa-spinner' );
			},
			success: function( response ) {
                $('.build-site-success').html('Success!');
			},
            error: function( jqXHR, textStatus, errorString ) {
                $('.build-site-msg').text('Error: ' + jqXHR.responseText);
            },
            complete: function() {
                $( '.response' ).removeClass( 'fa-spinner' );
            },
		} );
	} );
	
} );