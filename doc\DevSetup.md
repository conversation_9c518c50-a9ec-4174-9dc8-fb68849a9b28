# Development Environment Setup

The development environment setup process is now automated through Dock<PERSON>, but a few steps are still needed to get it running. 

### Installing Docker

Docker can be installed on Linux, Mac, and Windows.  However, Windows is not tested, supported, or recommended for development for this project.

For instructions specific to your operating system, see [Docker Installation Guide](https://docs.docker.com/engine/installation/).

You will need to make sure [Docker Compose](https://docs.docker.com/compose/) is installed as well.  This comes with the Mac OS X and Windows installs by default, but is separate on Linux.

### Configuration files

All needed configuration files should be created automatically when you start docker for the first time. However, if any already exist, they will not be overwritten. Please note the following files:

```
idx2/db/seeds/passwords.yml
idx2/config/database.yml
idx/includes/classes/dbconfig.ini
idx/config.ini
plugin/config.ini
```

### Starting the development environment

You should already have the profoundidx repository cloned from BitBucket. Navigate to `profoundidx/docker` and run the command `make setup`.

This command downloads files necessary to setup the development environment, which currently requires ssh access to `<EMAIL>`. The local wordpress development site is cloned from <http://houseschandleraz.com>, which includes the profoundmls plugin.

Create a file named `.env` in the docker folder. This contains docker environmental variables. Populate with IP_RANGE=172.10.0 for local development.

Please have at least 10 GB of free space on your hard drive before running `make setup` for the first time. The command will probably take 10 minutes or longer to run.

To stop the development environment, use the command `make stop`. When you want to start the environment again, you should use `make start`.

`make stop` preserves all changes to the IDX and wordpress databases. To start fresh for any reason, use the command `make reset`, after which `make setup` should be run again. `make reset` may need to be run with root permissions to delete files created by the docker service.

### Compiled files

If you edit files that need to be recompiled, use the command `make compile`. Currently, this is limited to coffeescript and stylus files for the plugin.

### Accessing the Services

Use `make hosts` to add necessary hostnames to your `/etc/hosts` file. You will be asked for your password before your `/etc/hosts` file can be modified.

Once the development environment has been started, and you have updated your hosts file, you can use the following URLs to access the services:

IDX Servers / Admin

* **Old IDX Server**: <http://idx.test>
* **New IDX Server**: <http://idx-server.test>
* **New IDX Admin**:  <http://idx-admin.test>

WordPress 

* **Pro-Found MLS plugin test site**: <http://profoundwp.test>

Other / Tools

* **Solr Interface**: <http://solr.test>
* **phpmyadmin for idx database**: <http://phpmyadmin.test>

### Useful targets

There are several targets in the `Makefile` that will probably be useful for debugging.  Probably the most useful ones:

#### Log tails

* `make idx-tail`: Tail the nginx logs for the IDX services (PHP server, node.js server, and Rails Admin)
* `make idx-log`: Tail the log from the node.js IDX server application
* `if-tail`: Tail the nginx log for the **ifoundwp** test site

#### Shells

* `make if-shell`: Log into the `ifoundwp` container, where WordPress is installed.  Useful for running WP CLI commands.
* `make db-shell`: Log into the IDX MySQL database with `mysql`, inside the `idx_db` container

Other Shells:

* Run `make {shell}` for any of the following: `ng-shell` (nginx), `rails-shell` (railsadmin), `idx-shell` (PHP IDX server)

### Adding WordPress sites

Each WordPress site gets its own PHP-FPM Docker container.  We are re-using the `wpdb` container to store the WordPress databases, rather than creating another MySQL container.  We could change this later.

Here are the approximate steps for adding a new WordPress site:

* new directory under `php/wordpress` for the site
    * create a `Dockerfile` and use it to any files needed to the base WordPress image/install
* update `docker-compose.yml` file
    * add volume for the WordPress directory
        * this will be shared between the PHP-FPM container, and nginx, because we let nginx serve static content, instead of making PHP-FPM handle it
    * add new service
        * should define `build`, `hostname`, `expose`, `depends_on`, and `volumes`
    * update `nginx` service to add mounts under `volumes`
* add hosts file entry to `Makefile`
* add new nginx config in `sites-available`
    * copy existing config & edit it, for now
* add new image name to `DCIMAGES` variable in `Makefile`
* new setup targets in main `Makefile`
    * call `docker-compose run` to set up WordPress, plugins, themes, options
* new targets in `wordpress/Makefile` to set up plugin
    * re-use exsiting `setup` target.  see `pf-setup` or `if-setup` for an example

Several of these steps should be automated or simplified.
