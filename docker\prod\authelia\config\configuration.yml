theme: auto
server:
    host: 0.0.0.0
    port: 9091
log:
    level: info
authentication_backend:
    file:
        path: /config/users_database.yml
        password:
            algorithm: argon2
            argon2:
                variant: argon2id
access_control:
    default_policy: deny
    rules:
        -
            domain: authelia.ifoundagent.com
            policy: bypass
        -
            domain:
                - 'solr.ifoundagent.com'
                - 'openresync.ifoundagent.com'
            policy: one_factor
session:
    name: authelia_session
    expiration: 1w
    inactivity: 1h
    remember_me_duration: 1M
    domain: ifoundagent.com # Should match whatever your root protected domain is
regulation:
    max_retries: 3
    find_time: 120
    ban_time: 300
storage:
    local:
        path: /config/db.sqlite3
notifier:
    # For testing purposes, notifications can be sent in a file. Be sure to map the volume in docker-compose.
    filesystem:
        filename: /config/notification.txt
