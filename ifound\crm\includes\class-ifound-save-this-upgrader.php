<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

class iFoundSaveThisUpgrader {
	use NewHooklessTrait;
	use UtilTrait;

	public static $ifound_upgrade_campaigns = 'ifound_upgrade_campaigns';
	public static $ifound_upgrade_campaign = 'ifound_upgrade_campaign';
	public static $ifound_rollback_campaigns = 'ifound_rollback_campaigns';
	public static $ifound_rollback_campaign = 'ifound_rollback_campaign';

	// We'll make backups of params to this meta key. If there are problems and we need to rollback, we'll copy from
	// the backup.
	public static $copy_to_from_meta_key = 'params_old_1';
	private static $NO_CHANGE = 'NO_CHANGE';
	private static $copy_from_key_param_name = 'copy_from_key';
	public static $copy_to_shortcode_key = 'old_1';
	// The idea here is that we'll rarely do upgrades like this, and I'd anticipate only one at a time. When we do, just
	// change these values.
	public static $upgrade_from_mls = 'armls';
	public static $upgrade_to_mls = 'armls_spark';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('ifound_upgrade_campaigns', [$this, 'upgrade_campaigns']);
			add_action('ifound_upgrade_campaign', [$this, 'upgrade_campaign'], 10, 1);
			add_action('ifound_rollback_campaigns', [$this, 'rollback_campaigns']);
			add_action('ifound_rollback_campaign', [$this, 'rollback_campaign'], 10, 1);

			add_action('ifound_upgrade_shortcodes', [$this, 'upgrade_shortcodes']);
			add_action('ifound_upgrade_shortcode', [$this, 'upgrade_shortcode'], 10, 1);
			add_action('ifound_rollback_shortcodes', [$this, 'rollback_shortcodes']);
			add_action('ifound_rollback_shortcode', [$this, 'rollback_shortcode'], 10, 1);
		}
	}

	public function test_upgrade_campaign() {
		// $example_params = [
		// 	'mls_class' => 'Residential',
		// 	'sort' => 'latest_listing',
		// 	'bathrooms' => ['max' => 2],
		// 	'bedrooms' => ['max' => 2],
		// 	'city' => ['Tempe', 'Gilbert'],
		// 	'list_price' => ['max' => 500000],
		// 	'pool_private' => ['pool - private'],
		// 	'exterior_features' => ['separate guest house'],
		// 	'water' => ['-domestic well'],
		// ];
		$example_params = [
			'exterior_features' => ['Guest House', '-Guest House'],
			'architechtural_style' => ['1 Story', '-1 Story'],
			'list_status' => ['Active'],
			'contingent' => ['UCB'],
		];
		$params = $this->upgrade_params($example_params);

		error_log(print_r($params, true));
	}

	private function map_old_values_to_new($params, $overall_map) {
		foreach ($overall_map as $easy_name => $map) {
			if ($params[$easy_name]) {
				$values = &$params[$easy_name];
				$keys_to_remove = [];
				$other_easy_name_values_to_add = [];
				for ($i = 0; $i < count($values); $i++) {
					$negated = false;
					$old_val_lowercase = strtolower($values[$i]);
					if (strlen($old_val_lowercase) > 0 && substr($old_val_lowercase, 0, 1) === '-') {
						$negated = true;
						$old_val_lowercase = substr($old_val_lowercase, 1);
					}
					$mapped_value = $map[$old_val_lowercase];
					if ($mapped_value) {
						// If it's an array, it has more information on the replacement. Basically, use another field.
						if (is_array($mapped_value)) {
							$keys_to_remove[] = $i;
							$copy_mapped_value = $mapped_value;
							if ($negated) {
								$copy_mapped_value['value'] = '-' . $copy_mapped_value['value'];
							}
							$other_easy_name_values_to_add[] = $copy_mapped_value;
						} else {
							$values[$i] = ($negated ? '-' : '') . $mapped_value;
						}
					}
				}
				foreach (array_reverse($keys_to_remove) as $i) {
					array_splice($values, $i, 1);
				}
				if (count($values) === 0) {
					unset($params[$easy_name]);
				}
				foreach ($other_easy_name_values_to_add as $mapped_value) {
					if (!$params[$mapped_value['easy_name']] || $mapped_value['delete_others']) {
						$params[$mapped_value['easy_name']] = [];
					}
					$params[$mapped_value['easy_name']][] = $mapped_value['value'];
				}
			}
		}
		return $params;
	}

	public function add_action_scheduler_action_to_upgrade() {
		as_enqueue_async_action('ifound_upgrade_campaigns', [], 'ifound');
		as_enqueue_async_action('ifound_upgrade_shortcodes', [], 'ifound');
	}

	public function add_action_scheduler_action_to_rollback() {
		as_enqueue_async_action('ifound_rollback_campaigns', [], 'ifound');
		as_enqueue_async_action('ifound_rollback_shortcodes', [], 'ifound');
	}

	public function upgrade_campaigns() {
		$post_ids = $this->get_campaign_ids();
		foreach ($post_ids as $post_id) {
			as_enqueue_async_action('ifound_upgrade_campaign', [['post_id' => $post_id]], 'ifound');
		}
	}

	public function rollback_campaigns() {
		$post_ids = $this->get_changed_upgraded_campaign_ids();
		foreach ($post_ids as $post_id) {
			$args = ['post_id' => $post_id, static::$copy_from_key_param_name => static::$copy_to_from_meta_key];
			as_enqueue_async_action('ifound_rollback_campaign', [$args], 'ifound');
		}
	}

	public function get_campaign_ids($modify_args_fn = null) {
		$args = array(
			'post_type' 		=> iFoundSaveThis::$the_post_type,
			'posts_per_page' 	=> -1,
			'fields'			=> 'ids',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> iFoundSaveThis::$the_taxonomy,
					'field'    	=> 'slug',
					'terms'    	=> array( 'property-alert' ),
					'operator'  => 'NOT IN'
				),
				array(
					'taxonomy'	=> iFoundSaveThis::$the_taxonomy,
					'operator'  => 'EXISTS'
				),
			),
			'meta_query' => array(
				array(
					'key'     	=> 'contact_id',
					'compare' 	=> 'EXISTS',
				),
			),
		);
		if ($modify_args_fn) {
			$args = $modify_args_fn($args);
		}
		$post_ids = get_posts($args);
		return $post_ids;
	}

	public function get_changed_upgraded_campaign_ids($limit = null) {
		$post_ids = $this->get_campaign_ids(function($args) use ($limit) {
			if ($limit) {
				$args['posts_per_page'] = $limit;
			}
			$args['meta_query'] = array(
				'relation'      => 'AND',
				array(
					'key'     	=> static::$copy_to_from_meta_key,
					'compare' 	=> 'EXISTS',
				),
				array(
					'key' => static::$copy_to_from_meta_key,
					'compare' => '!=',
					'value' => static::$NO_CHANGE,
				),
			);
			return $args;
		});
		return $post_ids;
	}

	public function get_unchanged_upgraded_campaign_ids($limit = null) {
		$post_ids = $this->get_campaign_ids(function($args) use ($limit) {
			if ($limit) {
				$args['posts_per_page'] = $limit;
			}
			$args['meta_query'] = array(
				'relation'      => 'AND',
				array(
					'key'     	=> static::$copy_to_from_meta_key,
					'compare' 	=> 'EXISTS',
				),
				array(
					'key' => static::$copy_to_from_meta_key,
					'value' => static::$NO_CHANGE,
				),
			);
			return $args;
		});
		return $post_ids;
	}

	public function rollback_campaign($args) {
		$post_id = $args['post_id'];
		$copy_from_key = $args[static::$copy_from_key_param_name];
		$old_params = get_post_meta($post_id, $copy_from_key, true);
		if ($old_params !== static::$NO_CHANGE) {
			update_post_meta($post_id, 'params', $old_params);
		}
	}

	public function upgrade_campaign($args) {
		$post_id = $args['post_id'];
		$old_params = get_post_meta($post_id, 'params', true);
		$new_params = $this->upgrade_params($old_params);
		if ($old_params == $new_params) {
			update_post_meta($post_id, static::$copy_to_from_meta_key, static::$NO_CHANGE);
		} else {
			update_post_meta($post_id, static::$copy_to_from_meta_key, $old_params);
			update_post_meta($post_id, 'params', $new_params);
		}
	}

	public function upgrade_params($params) {
		$map = null;
		if (static::$upgrade_to_mls === 'armls_spark') {
			$map = [
				'contingent' => [
					// Note that this is not the real value but what our 'MLS Associations" value is.
					'ucb' => [
						'easy_name' => 'list_status',
						'value' => 'UCB (Under Contract-Backups)',
						'delete_others' => true,
					],
					// Note that this is not the real value but what our 'MLS Associations" value is.
					'ccbs' => [
						'easy_name' => 'list_status',
						'value' => 'CCBS (Contract Contingent on Buyer Sale)',
						'delete_others' => true,
					],
					'no' => [
						'easy_name' => 'list_status',
						'value' => 'Active',
						'delete_others' => true,
					],
				],
				'dwelling_type' => [
					// Let's convert to our MLS Association value. I'm of course going by what they are at the moment.
					'apartment style/flat' => ['easy_name' => 'prop_type', 'value' => 'Apartments'],
					'gemini/twin home' => ['easy_name' => 'prop_type', 'value' => 'Gemini Homes'],
					'loft style' => ['easy_name' => 'prop_type', 'value' => 'Lofts'],
					'mfg/mobile housing' => ['easy_name' => 'prop_type', 'value' => 'Mobile Homes'],
					// Except for this one, which we don't have an association for.
					'modular/pre-fab' => ['easy_name' => 'prop_type', 'value' => 'Modular/Pre-Fab'],
					'patio home' => ['easy_name' => 'prop_type', 'value' => 'Patio Homes'],
					'single family - detached' => ['easy_name' => 'prop_type', 'value' => 'Homes'],
					'townhouse' => ['easy_name' => 'prop_type', 'value' => 'Townhouses'],
				],
				'green_features' => [
					'solar panels' => ['easy_name' => 'energy_green_feature', 'value' => 'Solar Panels'],
					'multi-zones' => ['easy_name' => 'energy_green_feature', 'value' => 'Multi-Zones'],
				],
				'kitchen_features' => [
					'built-in microwave' => ['easy_name' => 'appliances', 'value' => 'Built-In Microwave'],
					'granite countertops' => ['easy_name' => 'interior_features', 'value' => 'Granite Counters'],
					'laminate counters' => ['easy_name' => 'interior_features', 'value' => 'Laminate Counters'],
					'kitchen island' => ['easy_name' => 'interior_features', 'value' => 'Kitchen Island'],
					'pantry' => ['easy_name' => 'interior_features', 'value' => 'Pantry'],
				],
				// I'm not sure how these got a space in them but easy enough to fix I guess
				'list_status' => [
					' active' => 'active',
					' closed' => 'closed',
				],
				'property_features' => [
					// For whatever reason, there are zero of these so far in armls_spark_res in LotFeatures. So we'll
					// use the WaterfrontYN field.
					'waterfront lot' => ['easy_name' => 'waterfront_yn', 'value' => 'Y'],

					// They swapped the plurality of the word border in the LotFeatures lookup. However, there are zero
					// uses of that lookup in the LotFeatures field, so we'll use our own ifa_property_features field,
					// where I haven't swapped the plurality.
					'border pres/pub lnd' => ['easy_name' => 'ifa_property_features', 'value' => 'Border Pres/Pub Lnd'],
					// They swapped the plurality of the word borders in the LotFeatures lookup. However, there are zero
					// uses of that lookup in the LotFeatures field, so we'll use our own ifa_property_features field,
					// where I haven't swapped the plurality.
					'borders common area' => ['easy_name' => 'ifa_property_features', 'value' => 'Borders Common Area'],
					'east/west exposure' => ['easy_name' => 'ifa_property_features', 'value' => 'East/West Exposure'],
					'north/south exposure' => ['easy_name' => 'ifa_property_features', 'value' => 'North/South Exposure'],
					'hillside lot' => ['easy_name' => 'ifa_property_features', 'value' => 'Hillside Lot'],
					'nat reg historic hms' => ['easy_name' => 'ifa_property_features', 'value' => 'Nat Reg Historic Hms'],
				],
				'dwelling_styles' => [
					'attached' => 'Y',
					'detached' => 'N',
				],
				'land_features' => [
					'gated community' => ['easy_name' => 'lot_features', 'value' => 'Gated Community'],
					'borders pres/pub lnd' => ['easy_name' => 'lot_features', 'value' => 'Borders Pres/Pub Lnd'],
				],
				'landscaping' => [
					'yrd wtring sys back' => ['easy_name' => 'lot_features', 'value' => 'Sprinklers In Rear'],
					'flood irrigation' => ['easy_name' => 'lot_features', 'value' => 'flood irrigation'],
					'grass back' => ['easy_name' => 'lot_features', 'value' => 'grass back'],
					'desert back' => ['easy_name' => 'lot_features', 'value' => 'desert back'],
					'natural desert back' => ['easy_name' => 'lot_features', 'value' => 'natural desert back'],
					'desert front' => ['easy_name' => 'lot_features', 'value' => 'desert front'],
				],
				'pool_private' => [
					'pool - private' => 'Private',
					'no pool' => 'None',
					'lap pool' => 'Lap',
					'heated pool' => 'Heated',
					'fenced pool' => 'Fenced',
					'above ground pool' => 'Above Ground',
				],
				'special_listing_cond' => [
					'probate/estate' => 'Probate Listing',
					'hud owned property' => 'HUD Owned',
				],
				'exterior_features' => [
					'balcony/deck(s)' => 'Balcony',
					'separate guest house' => ['easy_name' => 'structure_list', 'value' => 'Guest House'],
					'pvt tennis court(s)' => 'Tennis Court(s)',
					'pvt yrd(s)/crtyrd(s)' => 'Private Yard',
					'storage shed(s)' => 'Storage',
				],
				'roofing' => [
					'all tile' => 'Tile',
					'comp shingle' => 'Composition',
				],
				'comm_features' => [
					'biking' => 'Biking/Walking Path',
					'workout facility' => 'Fitness Center',
					'comm tennis court(s)' => 'Tennis Court(s)',
					'golf course' => 'Golf',
					'on-site guard' => 'Guarded Entry',
					'clubhouse/rec room' => 'Clubhouse',
					"golf course/allison and tim's arizona home search w" => 'Golf',
				],
				'new_financing' => [
					'farm home/ usda' => 'USDA Loan',
					'va' => 'VA Loan',
				],
				'fencing' => [
					'view/wrought iron' => 'Wrought Iron',
				],
				'parking_features' => [
					'rv parking' => 'RV Access/Parking',
					'assigned parking' => 'Assigned',
					'tandem garage' => 'Tandem',
					'gated parking' => 'Gated',
					"permit/decal req'd" => 'Permit Required',
				],
				'hoa_includes' => [
					'exterior mnt of unit' => 'Maintenance Exterior',
					'blanket ins policy' => 'Insurance',
					'common area maint' => 'Maintenance Grounds',
				],
				'interior_features' => [
					'fix-up needs repair' => ['easy_name' => 'property_condition', 'value' => 'Fixer'],
				],
				'horse_features' => [
					'corral' => 'Corral(s)',
				],
				'spa_features' => [
					'spa - private' => 'Private',
				],
				'water' => [
					'domestic well' => 'Domestic',
					'well - pvtly owned' => 'Pvt Water Company',
				],
				'sewer_septic' => [
					'sewer - public' => 'Public Sewer',
					'sewer - private' => 'Private Sewer',
					'septic-in & connectd' => 'Sewer-In & Connected',
				],
				'building_style' => [
					// TODO
					'clustered' => '',
				],
				'fireplace_features' => [
					'fireplace master bdr' => 'Master Bedroom',
					'gas fireplace' => 'Gas',
				],
				'architechtural_style' => [
					'santa barbara' => 'Santa Barbara/Tuscan',
					'territorial' => 'Territorial/Santa Fe',
				],
				'laundry' => [
					'inside laundry' => 'Inside',
				],
			];
		} else if (static::$upgrade_to_mls === 'paaraz_mlg') {
			$map = [
				'exterior_features' => [
					'guest house' => 'Carriage/Guest House',
				],
				'architechtural_style' => [
					'1 story' => 'One Story',
				],
			];
		} else {
			throw new \Exception("Invalid MLS: $to_mls");
		}
		$params = $this->map_old_values_to_new($params, $map);
		return $params;
	}

	public function transition_mls() {
		if (iFoundIdx::mls_name() === static::$upgrade_from_mls) {
			iFoundIdx::new_hookless()->change_mls_for_account_on_idx_server(static::$upgrade_to_mls);
			$option = get_option( 'ifound_api_settings' );
			$option['mls_name'] = static::$upgrade_to_mls;
			update_option('ifound_api_settings', $option);
			iFoundSaveThisUpgrader::new_hookless()->add_action_scheduler_action_to_upgrade();
			iFoundIdx::new_hookless()->refresh_transients(true);
		}
	}

	public function rollback_mls_transition() {
		if (iFoundIdx::mls_name() === static::$upgrade_to_mls) {
			iFoundIdx::new_hookless()->change_mls_for_account_on_idx_server(static::$upgrade_from_mls);
			$option = get_option( 'ifound_api_settings' );
			$option['mls_name'] = static::$upgrade_from_mls;
			update_option('ifound_api_settings', $option);
			iFoundSaveThisUpgrader::new_hookless()->add_action_scheduler_action_to_rollback();
			iFoundIdx::new_hookless()->refresh_transients(true);
		}
	}

	public function get_shortcode_meta_ids($limit = null) {
		global $wpdb;

		$key = iFoundShortcode::$meta_key;
		if ($limit) {
			$limit_string = "LIMIT $limit";
		}
		$sql = <<<EOT
			SELECT meta_id
			FROM $wpdb->postmeta
			WHERE meta_key = '$key'
			ORDER BY meta_id DESC
			$limit_string
EOT;
		$results = $wpdb->get_results($sql, ARRAY_A);
		$meta_ids = array_map(function($x) {
			return $x['meta_id'];
		}, $results);
		$meta_ids = array_values($meta_ids);
		return $meta_ids;
	}

	// $keep_type is 'changed' or 'unchanged'
	public function get_upgraded_shortcode_meta_ids($limit = null, $keep_type) {
		global $wpdb;

		$key = iFoundShortcode::$meta_key;
		if ($limit) {
			$limit_string = "LIMIT $limit";
		}
		$sql = <<<EOT
			SELECT meta_id, meta_value
			FROM $wpdb->postmeta
			WHERE meta_key = '$key'
			ORDER BY meta_id DESC
			$limit_string
EOT;
		$results = $wpdb->get_results($sql, ARRAY_A);
		$results = array_filter($results, function($x) use ($keep_type) {
			$data = unserialize($x['meta_value']);
			if (!$data) {
				return false;
			}
			$is_upgraded = !!$data[static::$copy_to_shortcode_key];
			if (!$is_upgraded) {
				return false;
			}
			$query_is_changed = $data[static::$copy_to_shortcode_key]['query'] !== static::$NO_CHANGE;
			if (($keep_type === 'unchanged' && $query_is_changed)
				|| ($keep_type === 'changed' && !$query_is_changed)
			) {
				return false;
			}
			if (!isset($data[static::$copy_to_shortcode_key]['backup_query'])) {
				return true;
			}
			$backup_is_changed = $data[static::$copy_to_shortcode_key]['backup_query'] !== static::$NO_CHANGE;
			if (($keep_type === 'unchanged' && $backup_is_changed)
				|| ($keep_type === 'changed' && !$backup_is_changed)
			) {
				return false;
			}
			return true;
		});
		$meta_ids = array_map(function($x) {
			return $x['meta_id'];
		}, $results);
		$meta_ids = array_values($meta_ids);
		return $meta_ids;
	}

	public function upgrade_shortcodes() {
		$meta_ids = $this->get_shortcode_meta_ids();
		foreach ($meta_ids as $meta_id) {
			as_enqueue_async_action('ifound_upgrade_shortcode', [['meta_id' => $meta_id]], 'ifound');
		}
	}

	public function upgrade_shortcode($args) {
		$meta_id = $args['meta_id'];
		$meta_value = iFound::new_hookless()->get_meta_by_id($meta_id);
		$upgraded_query = $this->upgrade_params($meta_value['query']);
		if ($upgraded_query === $meta_value['query']) {
			$meta_value[static::$copy_to_shortcode_key]['query'] = static::$NO_CHANGE;
		} else {
			$meta_value[static::$copy_to_shortcode_key]['query'] = $meta_value['query'];
			$meta_value['query'] = $upgraded_query;
		}
		if (isset($meta_value['backup_query'])) {
			$upgraded_backup_query = $this->upgrade_params($meta_value['backup_query']);
			if ($upgraded_backup_query === $meta_value['backup_query']) {
				$meta_value[static::$copy_to_shortcode_key]['backup_query'] = static::$NO_CHANGE;
			} else {
				$meta_value[static::$copy_to_shortcode_key]['backup_query'] = $meta_value['backup_query'];
				$meta_value['backup_query'] = $upgraded_backup_query;
			}
		}
		iFound::new_hookless()->update_meta_by_id($meta_id, $meta_value);
	}

	public function rollback_shortcodes() {
		$meta_ids = $this->get_shortcode_meta_ids();
		foreach ($meta_ids as $meta_id) {
			as_enqueue_async_action('ifound_rollback_shortcode', [['meta_id' => $meta_id]], 'ifound');
		}
	}

	public function rollback_shortcode($args) {
		$meta_id = $args['meta_id'];
		$meta_value = iFound::new_hookless()->get_meta_by_id($meta_id);
		if (!isset($meta_value[static::$copy_to_shortcode_key])) {
			return;
		}
		$need_to_write = false;
		if ($meta_value[static::$copy_to_shortcode_key]['query'] !== static::$NO_CHANGE) {
			$meta_value['query'] = $meta_value[static::$copy_to_shortcode_key]['query'];
			$need_to_write = true;
		}
		if (isset($meta_value[static::$copy_to_shortcode_key]['backup_query'])
			&& $meta_value[static::$copy_to_shortcode_key]['backup_query'] !== static::$NO_CHANGE
		) {
			$meta_value['backup_query'] = $meta_value[static::$copy_to_shortcode_key]['backup_query'];
			$need_to_write = true;
		}
		if ($need_to_write) {
			iFound::new_hookless()->update_meta_by_id($meta_id, $meta_value);
		}
	}

	// 2025-03-09 https://ifoundagent.teamwork.com/app/tasks/42497382
	// ARMLS is working on RESO 2.0 alignment and will change fields soon. I don't want to go through the pain of
	// upgrading the campaigns and shortcode like I did at the end of 2023 (using this class). It was a pain. To be
	// fair, I think this class did a good job of things, saving off old campaign params and being prepared for
	// rollback eventualities. Still, I'd rather not feel the stress of doing something so important too quickly, and
	// therefore will opt for this temp upgrade. By "temp", it's not really upgrading at all, just changing the values
	// on the fly. We can upgrade the values permanently later.
	//
	// It feels like half of these aren't needed, because even using values in my dev system, which are several months
	// old at the least, they have already been updated. However, the rest of the values will need to be converted,
	// meaning I can see the legacy values even using postman with the RESO Web API in the ARMLS Lookup resource.
	public function maybe_temp_upgrade_params($params) {
		if (iFoundIdx::mls_name() === 'armls_spark') {
			// error_log('params before: ' . print_r($params, true));
			// This map's keys is ordered by the PDF document from ARMLS listing the field changes (as opposed to by
			// our mapped names for the fields).
			$map = [
				// We have potential_use in armls, but not armls_spark, so we don't need this, I'm just documenting it.
				// 'potential_use' => [
				// 	'Other (See Remarks)' => 'Other',
				// 	'Ranch/Farm' => 'Ranch',
				// ],
				// Yes, it's spelled this goofy way in our system, at least for res and rentals. It's spelled correctly
				// for land.
				'architechtural_style' => [
					'other (see remarks)' => 'Other',
				],
				'hoa_includes' => [
					// Hmm. That's odd. It's already Electricity in our system. So I'm including it for documentation,
					// or perhaps "just in case".
					'eletric' => 'Electricity',
				],
				'comm_features' => [
					'gated community' => 'Gated',
					'lake subdivision' => 'Lake',
					'pickleball court(s)' => 'Pickleball',
				],
				// We have construction_finish mapped for armls, but not armls_spark. Don't bother mapping now if no
				// one has asked for it.
				// construction_finish
				'construction' => [
					'frame - metal' => 'Steel Frame',
					'frame - wood' => 'Wood Frame',
					'other (see remarks)' => 'Other',
					'straw-bale' => 'Straw',
				],
				// We have dwelling_type mapped for armls, but not armls_spark. Don't bother mapping now if no
				// one has asked for it.
				// dwelling_type
				'exterior_features' => [
					'circular drive' => 'Circular Driveway',
					'covered patio(s)' => 'Covered Patio',
					'gazebo/ramada' => 'Gazebo',
				],
				'interior_features' => [
					'drink wtr filter sys' => 'Water Purifier',
					'fire sprinklers' => 'Fire Sprinkler System',
					// The PDF document lists this as a label change, with the old label, but no new label. We'll ignore
					// for now and maybe the new label will be apparent when the lookups are actually changed via RESO.
					// 'Intercom' => '',
				],
				'fireplace_features' => [
					'other (see remarks)' => 'Other',
				],
				// The PDF says there will be a new field. I'll have to see what that means.
				// 'lot_features' => [
				// 	'City Light View(s)' =>
				// ],
				'parking_features' => [
					'dir entry frm garage' => 'Direct Access',
					'electric door opener' => 'Garage Door Opener',
				],
				// We had this field mapped for ARMLS... even though we didn't have a display name or easy name for it.
				// So this is probably not necessary, but better safe than sorry.
				'rent_includes' => [
					'electric' => 'Electricity',
					'internet access' => 'Internet',
					'other (see remarks)' => 'Other',
				],
				'roof' => [
					'comp shingle' => 'Composition',
					'other (see remarks)' => 'Other',
				],
				'sewer_septic' => [
					'other (see remarks)' => 'Other',
				],
				'lot_size_source' => [
					'county assessor' => 'Assessor',
				],
				'special_listing_cond' => [
					// In a perfect world I'd unique-ify the outcome of these duplicate outputs, but I think it won't
					// hurt to have multiples and it'll be rare.

					// Hmm. These are the legacy values listed in the PDF. But the actual ones in the system are the
					// following ones. I guess it shouldn't hurt to have both here.
					'previously approved short sale' => 'Short Sale',
					'short sale approval required' => 'Short Sale',
					'lender approved short sale' => 'Short Sale',

					'previously aprved ss' => 'Short Sale',
					'short sale aprvl req' => 'Short Sale',
					'lender approved ss' => 'Short Sale',
				],
				'utilities' => [
					'other (see remarks)' => 'Other',
				],
				'water' => [
					'private franchise' => 'Private',
					'well - pvtly owned' => 'Private Well',
				],
				'window_features' => [
					'low-e' => 'Low-Emissivity Windows',
					'sunscreen(s)' => 'Screens',
				],



				// This is the odd man out, sort of. The field itself only allows one value, but our search allows
				// multiple values such and then we OR them. So in the end we treat it the same, it's just not a
				// multi-lookup like the ones above.
				'prop_type' => [
					'apartment style/flat' => 'Apartment',
					'single family - detached' => 'Single Family Residence',
				],
			];

			// Make sure all keys are lowercase
			$map = $this->util()->array_change_key_case_recursive($map);

			// Look over all params. If we need to update their value, do so.
			foreach ($params as $key => $value) {
				$key = strtolower($key);
				// Here's what we're going to do. Today is their launch day, and I know more. They have not updated old
				// records, they're only using the new values for updated records. So, we're going to search for the
				// old value and the new value.
				if (array_key_exists($key, $map)) {
					$new_val = [];
					foreach ($value as $v) {
						$new_val[] = $v;
						$lowercase_v = strtolower($v);
						if (array_key_exists($lowercase_v, $map[$key])) {
							$new_val[] = $map[$key][$lowercase_v];
						}
					}
					$params[$key] = $new_val;
				}
			}
			// error_log('params after: ' . print_r($params, true));
		}
		return $params;
	}
}
