[Home](Home)

[TOC]

# **WpBuilder API JSON Blueprint**

This document provides an explanation of the JSON expected by the WpBuilder plugin API when creating and deploying a new blog.

		{
		    "wp_builder": {
		        "client": {
		            "access_fullname": "string",
		            "access_emailaddress": "string",
		            "access_phone": "string",
		            "access_company": "string"
		        },
		        "registration": {
		            "theme": "string",
		            "title": "string",
		            "tagline": "string",
		            "color": "string"
		        },
		        "user": {
		            "plaintext_password": "testpassword"
		        }
		    }
		}

## **Supported themes**

'8', '84', 'Ivy', 'Rysky', 'Sky'

## **Supported colors for each theme**

Note that color is case-insensitive in WpBuilder.

- Eight: Blue, Silver, <PERSON>, Dark Wood, Red, Gray
- Eightyfour:  Blue, Silver, Wood, Dark Wood, Red, Gray
- Ivy: Black, Blue, Wood, Red
- Rysky: Blue, Silver, <PERSON>, <PERSON> Wood, <PERSON>, Gray, White, Black
- Sky:  Silver, Wood, Red
- Prime: Charcoal, Orange, <PERSON>, <PERSON>, <PERSON>, <PERSON>

## **Supported social media accounts**

'facebook', 'googleplus', 'twitter', 'myspace', 'friendfeed', 'orkut', 'hyves', 'linkedin', 'asmallworld', 'flickr', 'instagram', 'picasa', 'pinterest', 'youtube', 'aboutme', 'vk', 'skyrock', 'skype', 'digg', 'reddit', 'delicious', 'stumble', 'tumblr', 'github', 'buzz', 'talk', 'vimeo', 'blogger', 'wordpress', 'yelp', 'lastfm', 'pandora', 'ustream', 'imdb', 'hulu', 'flixster', 'foursquare', 'meetup', 'plancast', 'slideshare', 'deviantart', 'itunes', 'live365', 'digitaltunes', 'soundcloud', 'bandcamp', 'etsy', 'bbb', 'merchantcircle', 'ebay', 'steam', 'slashdot', 'goodreads', 'cuttingsme', 'rss_url', 'subscribe', 'custom1name', 'custom1icon', 'custom1url', 'custom2name', 'custom2icon', 'custom2url', 'custom3name', 'custom3icon', 'custom3url', 'custom4name', 'custom4icon', 'custom4url', 'custom5name', 'custom5icon', 'custom5url', 'custom6name', 'custom6icon', 'custom6url', 'custom7name', 'custom7icon', 'custom7url', 'custom8name', 'custom8icon', 'custom8url', 'custom9name', 'custom9icon', 'custom9url', 'custom10name', 'custom10icon', 'custom10url', 'custom11name', 'custom11icon', 'custom11url', 'custom12name', 'custom12icon', 'custom12url', 'customiconsurl', 'customiconspath'