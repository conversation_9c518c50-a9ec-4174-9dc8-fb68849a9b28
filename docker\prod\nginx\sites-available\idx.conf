server {
    listen 80;
    server_name profoundidx.com;

    location / {
        return 301 https://$host$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
}

# Our old plugin is using http (not https)
server {
    listen 80;
    server_name get.profoundidx.com;

    root /var/www/idx;
    index index.php;

    access_log off;
    error_log /var/log/nginx/idx-error.log;

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        fastcgi_pass idx:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
        fastcgi_param PHP_VALUE "include_path=/www/includes/:/usr/share/php/";
    }

    include /etc/nginx/global/idx.conf;

}

server {
    listen 443 ssl http2;
    server_name profoundidx.com get.profoundidx.com;

    root /var/www/idx;
    index index.php;

    access_log off;
    error_log /var/log/nginx/idx-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/profoundidx.com-0002/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/profoundidx.com-0002/privkey.pem;

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        fastcgi_pass idx:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
        fastcgi_param PHP_VALUE "include_path=/www/includes/:/usr/share/php/";
    }

    include /etc/nginx/global/idx.conf;

}
