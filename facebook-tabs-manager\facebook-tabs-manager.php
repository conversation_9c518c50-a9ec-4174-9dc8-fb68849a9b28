<?
/*
Plugin Name: Facebook Tabs Manager
Plugin URI:  https://ifound.com
Description: Create and manage Facebook tabs
Version:     1.0.0
Author:      ifoundagent
Author URI:  https://ifoundagent.com
License:     GPL2
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ifound
*/

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

if ( ! defined( 'FB_TAB_APPS_PLUGIN_VERSION' ) )
    define( 'FB_TAB_APPS_PLUGIN_VERSION', '1.0.0' );
	
/**
 * FB_TAB_APPS Class
 *
 * @since 1.0.0
 */
 
class FB_TAB_APPS {
	
	/**
	 * init FB_TAB_APPS class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }
	
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	 
	public function __construct() {
		
		require_once plugin_dir_path( __FILE__ ) . 'Facebook/autoload.php';
		require_once plugin_dir_path( __FILE__ ) . 'facebook-tabs.php';
		
		/**
		 * Check current version.
		 *
		 * @since 1.0.0
		 */
		 
		add_action( 'init', array( $this, 'check_version' ) );
		
		add_action( 'admin_menu', array( $this, 'admin_menu' ) );

		add_action( 'init', array( $this, 'create_post_type' ) );
		add_action( 'init', array( $this, 'fb_tab_apps_add_taxonomy' ) );
		add_action( 'save_post', array( $this, 'save_fb_tab' ), 10, 3 );
		
		add_filter( 'parse_query', array( $this, 'fb_tab_apps_query' ) );
		add_action( 'restrict_manage_posts', array( $this, 'fb_tab_apps_dropdown' ), 10, 1 );
		
	}

	public function admin_menu() {
		remove_menu_page( 'index.php' );                  //Dashboard
  		remove_menu_page( 'edit.php' );                   //Posts
  		remove_menu_page( 'upload.php' );                 //Media
  		remove_menu_page( 'edit.php?post_type=page' );    //Pages
  		remove_menu_page( 'edit-comments.php' );          //Comments
  		remove_menu_page( 'themes.php' );                 //Appearance
  		remove_menu_page( 'plugins.php' );                //Plugins
  		remove_menu_page( 'users.php' );                  //Users
  		remove_menu_page( 'tools.php' );                  //Tools
  		remove_menu_page( 'options-general.php' );        //Settings
	}
	
	public function fb_tab_apps_add_taxonomy() {
		
		// Add new taxonomy, make it hierarchical (like categories)
		$labels = array(
			'name'              => _x( 'Tab Status', 'taxonomy general name', 'ifound' ),
			'singular_name'     => _x( 'Tab Status', 'taxonomy singular name', 'ifound' ),
			'search_items'      => __( 'Search Tab Status', 'ifound' ),
			'all_items'         => __( 'All Tab Status', 'ifound' ),
			'parent_item'       => __( 'Parent Tab Status', 'ifound' ),
			'parent_item_colon' => __( 'Parent Tab Status:', 'ifound' ),
			'edit_item'         => __( 'Edit Tab Status', 'ifound' ),
			'update_item'       => __( 'Update Tab Status', 'ifound' ),
			'add_new_item'      => __( 'Add New Tab Status', 'ifound' ),
			'new_item_name'     => __( 'New Tab Status Name', 'ifound' ),
			'menu_name'         => __( 'Tab Status', 'ifound' ),
		);
	
		$args = array(
			'hierarchical'      => true,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_in_menu'		=> true,
			'show_admin_column' => true,
			'query_var'         => true,
			'public' 			=> true,
            'rewrite' 			=> false
		);
	
		register_taxonomy( 'tab_status', array( 'fb_tab_apps' ), $args );
	}
	
	public function create_post_type() {
	  	
		if( ! is_user_logged_in() ) return;
		
		register_post_type( 'fb_tab_apps',
			array(
				'labels' => array(
					'name' 			=> __( 'Facebook Tabs' ),
					'singular_name' => __( 'Facebook Tab' ),
					'add_new_item'	=> __( 'Add New Facebook Tab' ),
					'edit_item'		=> __( 'Edit Facebook Tab' ),
					'new_item'		=> __( 'New Facebook Tab' ),
					'view_item'		=> __( 'View Facebook Tab' ),
					'view_items'	=> __( 'View Facebook Tabs' ),
					'search_items'	=> __( 'Search Facebook Tabs' ),
					'all_items'		=> __( 'All Facebook Tabs' ),
					'attributes'	=> __( 'Facebook Tab Attributes' ),
				),
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> true,
				'publicly_queryable'	=> false,
				'show_in_nav_menus'		=> false,
				'supports'				=> array( 'title' ),
				'menu_icon'             => 'dashicons-facebook-alt',
				'register_meta_box_cb'	=> array( $this, 'fp_tab_metabox' )
			)
	  	);
		
	}
	
	public function fb_tab_apps_dropdown() {
		
		global $typenow;
		
		$post_type = 'fb_tab_apps'; 
		$taxonomy  = 'tab_status';
		
		if ($typenow == $post_type) {
			
			$selected      = isset( $_GET[$taxonomy] ) ? $_GET[$taxonomy] : '';
			
			$info_taxonomy = get_taxonomy( $taxonomy );
			
			wp_dropdown_categories(array(
				'show_option_all' => __( 'Show All ' . $info_taxonomy->label ),
				'taxonomy'        => $taxonomy,
				'name'            => $taxonomy,
				'orderby'         => 'name',
				'selected'        => $selected,
				'show_count'      => true,
				'hide_empty'      => true,
			));
			
		}
		
	}
	
	
	public function fb_tab_apps_query( $query ) {
		
		global $pagenow;
		
		$post_type = 'fb_tab_apps';
		$taxonomy  = 'tab_status';
		
		$q_vars    = &$query->query_vars;
		
		if ( 
			$pagenow == 'edit.php' 
			&& 
			isset( $q_vars['post_type'] ) 
			&& 
			$q_vars['post_type'] == $post_type 
			&& 
			isset( $q_vars[$taxonomy] ) 
			&& 
			is_numeric( $q_vars[$taxonomy] ) 
			&& 
			$q_vars[$taxonomy] != 0 ) 
		{
			$term = get_term_by( 'id', $q_vars[$taxonomy], $taxonomy );
			$q_vars[$taxonomy] = $term->slug;
		}
		
	}
	
	public function fp_tab_metabox() {
		
		add_meta_box( 
			'fb_tab_apps', 
			__( 'Facebook Tab Data', 'ifound' ), 
			array( $this, 'fp_tab_metabox_display'), 
			'fb_tab_apps' 
		);
		
		add_meta_box( 
			'fb_tab_connect', 
			__( 'Connect Facebook Tabs', 'ifound' ), 
			array( $this, 'fp_tab_metabox_connect'), 
			'fb_tab_apps' 
		);
		
	}
	
	public function save_fb_tab( $post_id, $post, $update ) {
		
		$post_type = get_post_type( $post_id );
	
		// If this isn't a 'book' post, don't update it.
		if ( 'fb_tab_apps' != $post_type ) return;
	
		// - Update the post's metadata.
	
		if ( isset( $_POST['fb_page_id'] ) ) {
			update_post_meta( $post_id, 'fb_page_id', sanitize_text_field( $_POST['fb_page_id'] ) );
		}
	
		if ( isset( $_POST['search_homes'] ) ) {
			update_post_meta( $post_id, 'search_homes', sanitize_text_field( $_POST['search_homes'] ) );
		}
		
		if ( isset( $_POST['home_value'] ) ) {
			update_post_meta( $post_id, 'home_value', sanitize_text_field( $_POST['home_value'] ) );
		}
		
	}
	
	
	public function fp_tab_metabox_display() { ?>
		
		<table class="form-table">
			<tbody>

				<tr valign="top">
					<th scope="row">Facebook Page ID:</th>
					<td>
						<input type="text" name="fb_page_id" value="<? echo get_post_meta( get_the_ID(), 'fb_page_id', true ); ?>" size="30">
					</td>
				</tr>

				<tr valign="top">
					<th scope="row">Search Homes URL:</th>
					<td>
						<input type="text" name="search_homes" value="<? echo get_post_meta( get_the_ID(), 'search_homes', true ); ?>" size="70">
					</td>
				</tr>

				<tr valign="top">
					<th scope="row">Home Value URL:</th>
					<td>
						<input type="text" name="home_value" value="<? echo get_post_meta( get_the_ID(), 'home_value', true ); ?>" size="70">
					</td>
				</tr>
			
			</tbody>
		</table><?

	}
	
	public function fp_tab_metabox_connect() { 

		$fb_page_id = get_post_meta( get_the_ID(), 'fb_page_id', true ); ?>
		
		<table class="form-table">
			
			<tbody>

				<tr valign="top">
					
					<th scope="row">Search Homes:</th><?

					if ( $fb_page_id && get_post_meta( get_the_ID(), 'search_homes', true ) ) { ?>
					
						<td>
							<a href="http://www.facebook.com/dialog/pagetab?app_id=371250183235723&next=https://facebook.ifoundadmin.com/?fb_tab_search_homes=true" class="button button-primary" target="_blank">CONNECT</a>
						</td><?

					} else { ?>

						<td>Save this page before adding FB Tabs.</td><?

					} ?>
				
				</tr>

				<tr valign="top">
					
					<th scope="row">Home Value:</th><?

					if ( $fb_page_id && get_post_meta( get_the_ID(), 'home_value', true ) ) { ?>
					
						<td>
							<a href="http://www.facebook.com/dialog/pagetab?app_id=1631030900533241&next=https://facebook.ifoundadmin.com/?fb_tab_home_value=true" class="button button-primary" target="_blank">CONNECT</a>
						</td><?

					} else { ?>

						<td>Save this page before adding FB Tabs.</td><?

					} ?>
				
				</tr>
				
			
			</tbody>
		
		</table>
		
		<div>You must have admin permissions to connect. Go to <a href="https://business.facebook.com/home/<USER>" target="_blank">Facebook Business Manager</a> to request access.</div><?

	}

	
	/**
	 * FB_TAB_APPS activation.
	 *
	 * On plugin activation we update current version.
	 *
	 * @since 1.0.0
	 */
	
	public function activation() {
		
		/** Update corrent plugin version */
		update_option( 'FB_TAB_APPS_current_version', FB_TAB_APPS_PLUGIN_VERSION );
			
	}
	
	
	/**
	 * FB_TAB_APPS check version.
	 *
	 * Check to see if current version is stored in database, if no, call blogdog_activation.
	 *
	 * @since 1.0.0
	 */
	
	public static function check_version() {
		if ( FB_TAB_APPS_PLUGIN_VERSION !== get_option( 'FB_TAB_APPS_current_version' ))
    		self :: activation();
	}

}

/**
 * @see FB_TAB_APPS::init()
 *
 */
add_action( 'plugins_loaded', array( 'FB_TAB_APPS', 'init' ) );
?>