# IDX Setup

This page provides details on getting [Solr](Solr.md) and the node.js IDX server up & running.

## Configuration

The node.js server & scripts use YAML files and [node-config](https://github.com/lorenwest/node-config) for configuration.

Configuration files are stored in `server/config/`.  The default configuration is in `default.yaml`.

Create a `local.yaml` file in the configuration directory, and set the password for your local copy of MySQL:

```
database:
  password: xxxxx
```

## npm modules

Install the npm modules.  At the top level:

```
npm install
(cd server && npm install)
```

**Note:**: The `package.json` file under `server` will probably be merged with the top-level file later, eliminating this extra step.

## MySQL databases

Pull down MySQL databases from production:

    make pull-mls-data 

**Note**: These targets were created before the ARMLS tables were expanded to contain closed listings - this may take a while, and YMMV.

**TODO**: Include the image tables in the target ...

## Required Packages

The PCEL Solr package is required, you can install it with: `sudo pecl install -n solr`. Install the required dependencies in case of errors. In my case, I had to install `libcurl-dev libxml2-dev` on the system.

## Solr setup

An archive of [Solr](Solr.md) with the extra `.jar` files needed for spatial searching is stored on Amazon S3.  You can download the (300 MB) archive via the S3 web interface, or if you have the [AWS CLI]() installed & configured:

    aws s3 cp s3://pf-idx-dev/solr-dist.tar.gz solr/

Unzip it:

	cd solr
	tar xf solr-dist.tar.gz

Generate the configuration files for the Solr cores:

    tools/gen-solr-configs 

Start Solr:

    dist/bin/solr start

You can then import data into Solr:

    make full-all

You may also just import data for a single core:

    make full-import CORE=armls_res

## Solr Admin

Once Solr is started, you can perform many operations from the GUI.  Access it here:

* [http://localhost:8983/solr/](http://localhost:8983/solr/)

## Node.js server / IDX Search

Running the IDX server is simple:

    cd server
    make

This will run [supervisor](), which watches files for changes and restarts node.js (in this case, `iced`) when files change.

You will see output like this in the console:

```
[Thu, 20 Aug 2015 23:39:50 GMT] INFO        Server running at http://127.0.0.1:8155
[Thu, 20 Aug 2015 23:39:50 GMT] DEBUG       ****** BLOCKED FOR 327ms
```

## Testing

One easy way to test that the IDX server is working properly is to use the `tools/idx-log-tool` script to reply a bunch of search requests from the production nginx logs.

To do this, copy down the latest IDX log:

    scp <EMAIL>:/var/log/nginx/idx-server.log .

Then run the tool to replay the requests:

    tools/idx-log-tool -q idx-server.log
