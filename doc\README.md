# Pro-Found IDX & MLS

This is the source code for the [Pro-Found / IFoundAgent GetFoundIDX Solution for WordPress](http://ifoundagent.com/how-does-it-work/getfoundidx-features-and-benefits/).

## Technology

The following LAMP stack is used: Linux, Apache, MySQL, PHP.

In addition, a newer version of the [IDX server](doc/IDXServer.md) uses node.js, CoffeeScript, MySQL, and Solr.

## Directories

The main directories of interest:

* `database`: MySQL schema for the IDX server, misc. DB scripts for developed features
* `idx`: Source code for the [old IDX server](doc/IDXServer.md)
    * for [Property Details](), [Featured Listings](), [Lookups](), and [IDX Options]() queries
    * PHP-based [RETS](http://en.wikipedia.org/wiki/RETS) synchronization scripts
* `server`: New node.js/CoffeeScript IDX server
    * for [Property Search](doc/PropertySearch.md) queries
* `plugin`: Source code for the [WordPress](http://wordpress.org) IDX plugin
* `solr`: install & scripts for generating configuration files for [Solr](Solr.md)

Other directories:

* `build`: Use to store versions of the plugin dumped from git for archiving & deployment
* `testing`: [PHPUnit](http://en.wikipedia.org/wiki/PHPUnit) tests (out-of-date, not currently maintained)
* `tests`: PHPUnit tests that are kept up to date. This is a more standard directory. See the **Testing** section of this document.
* `ubuntu`: server configuration files for the production server

## Components

* [IDX Server](doc/IDXServer.md) - IDX Server handling property searches - built using node.js, MySQL, and Solr
    * [Property Search](doc/PropertySearch.md)
    * [Solr](doc/Solr.md)
* [WordPress Plugin](doc/IDXPlugin.md) - the "Pro-FoundMLS" WordPress IDX plugin
* [RETS Sync](doc/RETS-Sync.md) - scripts & services for syncing property data from MLSes using RETS
* [Old IDX Server](doc/OldIDXServer.md) - original PHP-based IDX server, using MySQL as the backend
* [Rails Admin](doc/RailsAdmin.md) - a newer IDX admin interface built using Ruby on Rails
* [WPBuilder](doc/wpbuilder/WPBuilder.md) - the custom, in-house WordPress plugin built for cloning sites inside a WP Multisite install

## Development

* [Development Setup](doc/DevSetup.md)
* [Deployment](doc/Deployment.md)

## Other

* [Mapping](doc/Mapping.md)

## Old

* [Testing](doc/OldTesting.md) - we used to do some automated testing on this project, but it's current rather dead
* [Misc](doc/Misc.md) - old docs that need cleaned up
* [System Diagram](doc/SystemDiagram.md)
