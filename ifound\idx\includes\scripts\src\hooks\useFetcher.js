import { useState, useEffect, useRef } from 'react';
import { getFetchErrorMessage, isAbortError } from '../lib/errors';

// You can call 'run' to manually control, or set the url and it'll run whenever it changes.
export default function useFetch({ fetchFn, url }) {
	const [isLoading, setIsLoading] = useState(false);
	const [errorMessage, setErrorMessage] = useState(null);
	const [data, setData] = useState(undefined);
	// The only reason we have isSuccess is for the theoretical case that the passed fetchFn returns undefined on
	// success, in which case we wouldn't be able to look at the data to know if it was successful.
	const [isSuccess, setIsSuccess] = useState(null);
	const [forcedRun, setForcedRun] = useState(false);
	// Axios can use an abort signal. I'm not sure what else can. But it won't hurt if it's not used by the fetchFn.
	const abortController = new AbortController();
	const abortControllerRef = useRef(abortController);

	function run() {
		setForcedRun(true);
	}

	function clear() {
		setErrorMessage(null);
		setData(undefined);
		setIsSuccess(false);
	}

	useEffect(() => {
		if (url || forcedRun) {
			if (isLoading) {
				// We never finished the previous fetch. It's a waste to let it finish. So, abort it.
				abortControllerRef.current.abort();
			}
			abortControllerRef.current = abortController;
			clear();
			setIsLoading(true);
			if (forcedRun) {
				setForcedRun(false);
			}
			fetchFn({ abortSignal: abortController.signal })
				.then(response => {
					if (!abortController.signal.aborted) {
						setData(response);
						setIsSuccess(true);
					}
				})
				.catch(error => {
					// Would it be better to check abortController.signal.aborted?
					if (!isAbortError(error)) {
						const errorMessage = getFetchErrorMessage(error);
						setErrorMessage(errorMessage);
						setIsSuccess(false);
					}
				})
				.finally(() => {
					if (!abortController.signal.aborted) {
						setIsLoading(false);
					}
				});
		}
	}, [url, forcedRun])

	return {
		isLoading,
		errorMessage,
		data,
		isSuccess,
		run,
		clear,
	};
}
