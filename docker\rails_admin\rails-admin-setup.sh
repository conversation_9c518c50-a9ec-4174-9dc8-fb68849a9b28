#!/bin/bash

source /etc/profile
rvm use ruby-2.0.0-p0
cd /profoundidx/idx2
bundler install

bash /profoundidx/docker/utils/wait-for-it.sh idx_db:3306 -t 0 

echo "Creating pfndidx_azdb tables"
# setup tables
rake db:create
rake db:schema:load
rake db:migrate
# import data
mysql -h idx_db -u root -proot pfndidx_azdb < /dev-idx-db.sql
mysql -h idx_db -u root -proot pfndidx_azdb -e "set session sql_mode='ALLOW_INVALID_DATES'; \
    drop table armls_property_A; \
    drop table armls_images; \
    create table armls_property_A like armls_prop_A_dump; \
    create table armls_images like armls_images_dump; \
    insert into armls_property_A select * from armls_prop_A_dump; \
    insert into armls_images select * from armls_images_dump;"
# import test users
rake db:seed
