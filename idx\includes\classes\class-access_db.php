<?php
require_once("classes/dbroot.php");

class AccessDB extends dbroot {



public function put_access( $a ){
		
	$sql = "INSERT INTO `access` ( `access_account_id`, `access_apikey`, `access_member_id`, `access_office_id`, `access_domain`, `access_ip`, `access_company`, `access_fullname`, `access_address`, `access_address2`, `access_city`, `access_state`, `access_zipcode`, `access_phone`, `access_emailaddress`, `access_status` )
						  VALUES ( '".$a['access_account_id']."', 
						  		   '".$a['access_apikey']."',
						  		   '".$a['access_member_id']."',
						  		   '".$a['access_office_id']."',
								   '".$a['access_domain']."', 
								   '".$a['access_ip']."', 
								   '".$a['access_company']."', 
								   '".$a['access_fullname']."', 
								   '".$a['access_address']."', 
								   '".$a['access_address2']."', 
								   '".$a['access_city']."', 
								   '".$a['access_state']."', 
								   '".$a['access_zipcode']."', 
								   '".$a['access_phone']."', 
								   '".$a['access_emailaddress']."', 
								   '".$a['access_status']."'
								 );";
	
	return $this->process('put', $sql);
	
} // End put_access Function




public function update_access( $a ){

	$sql = "UPDATE `access` SET `access_account_id` = '".$a['access_account_id']."', 
								`access_apikey` = '".$a['access_apikey']."',
								`access_member_id` = '".$a['access_member_id']."',
								`access_office_id` = '".$a['access_office_id']."',
								`access_domain` = '".$a['access_domain']."', 
								`access_ip` = '".$a['access_ip']."', 
								`access_company` = '".$a['access_company']."', 
								`access_fullname` = '".$a['access_fullname']."', 
								`access_address` = '".$a['access_address']."', 
								`access_address2` = '".$a['access_address2']."', 
								`access_city` = '".$a['access_city']."', 
								`access_state` = '".$a['access_state']."', 
								`access_zipcode` = '".$a['access_zipcode']."', 
								`access_phone` = '".$a['access_phone']."', 
								`access_emailaddress` = '".$a['access_emailaddress']."', 
								`access_status` = '".$a['access_status']."'
							WHERE `access_id` = '".$a['aid']."'";
			
	return $this->process('update', $sql);
	
} // End update_access Function


} //end class

?>