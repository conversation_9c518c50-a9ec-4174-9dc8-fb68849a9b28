<?php

class RegexTest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testRegex1() {
		$query = "price>2500";
		$signs = array('<', '>');
		foreach ($signs as $sign) {
			$query = preg_replace("/price\\s*$sign\\s*(\d+)/", "(LIST_22 > 0 and LIST_22 $sign \$1) or (LIST_22 = 0 and LIST_24 $sign \$1)", $query);
		}
		$this->assertEquals("(LIST_22 > 0 and LIST_22 > 2500) or (LIST_22 = 0 and LIST_24 > 2500)", $query);
	}

	public function testRegex2() {
		$query = "price>2500;price<4500";
		$signs = array('<', '>');
		foreach ($signs as $sign) {
			$query = preg_replace("/price\\s*$sign\\s*(\d+)/", "(LIST_22 > 0 and LIST_22 $sign \$1) or (LIST_22 = 0 and LIST_24 $sign \$1)", $query);
		}
		$this->assertEquals("(LIST_22 > 0 and LIST_22 > 2500) or (LIST_22 = 0 and LIST_24 > 2500);(LIST_22 > 0 and LIST_22 < 4500) or (LIST_22 = 0 and LIST_24 < 4500)", $query);
	}
}
