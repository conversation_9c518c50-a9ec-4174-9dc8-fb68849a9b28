<?php

namespace Profound\MLS;

require_once 'vendor/autoload.php';

use \Phactory\Sql\Phactory;
use \PHPUnit_Framework_TestCase;
use \Zend_Db_Adapter_Pdo_Mysql;

class FieldMappingsTest extends PHPUnit_Framework_TestCase {
	private $pdo;
	private $db;

	public function setUp() {
		parent::setUp();
		$dbConfig = array(
			'host' => $GLOBALS['DB_HOST'],
			'dbname' => $GLOBALS['DB_DBNAME'],
			'username' => $GLOBALS['DB_USER'],
			'password' => $GLOBALS['DB_PASSWD'],
		);
		$this->db = new Zend_Db_Adapter_Pdo_Mysql($dbConfig);
		$this->pdo = new \PDO($GLOBALS['DB_DSN'], $GLOBALS['DB_USER'], $GLOBALS['DB_PASSWD']);
		$this->phactory = new Phactory($this->pdo);
		$this->phactory->setInflection('field_mapping', 'field_mapping');
	}

	public function tearDown() {
		$this->phactory->reset();
	}

	public function testCanLoadFieldMappingByEasyName() {
		$this->phactory->define('field_mapping', array());
		$hoa = $this->phactory->create('field_mapping', array(
			'mls_class' => 'res',
			'armls' => 'FEAT20070914205202933704000000',
			'EasyName' => 'hoa',
			'MapName' => 'HOA',
		));
		$fieldMappings = new FieldMappings(array(
			'mls' => 'armls',
			'mls_class' => 'res',
			'db' => $this->db,
		));
		$this->assertEquals("FEAT20070914205202933704000000", $fieldMappings->getByEasyName('hoa')->mlsFieldName);
	}

	public function testCanLoadFieldMappingByMapName() {
		$this->phactory->define('field_mapping', array());
		$hoa = $this->phactory->create('field_mapping', array(
			'mls_class' => 'res',
			'armls' => 'FEAT20070914205202933704000000',
			'EasyName' => 'hoa',
			'MapName' => 'HOA',
		));
		$fieldMappings = new FieldMappings(array(
			'mls' => 'armls',
			'mls_class' => 'res',
			'db' => $this->db,
		));
		$this->assertEquals("FEAT20070914205202933704000000", $fieldMappings->getByMapName('HOA')->mlsFieldName);
	}

	public function testCanLoadProperFieldMappingByMlsClass() {
		$this->phactory->define('field_mapping', array());
		$hoa = $this->phactory->create('field_mapping', array(
			'mls_class' => 'res',
			'armls' => 'FEAT20070914205202933704000000',
			'EasyName' => 'hoa',
			'MapName' => 'HOA',
		));
		$hoa = $this->phactory->create('field_mapping', array(
			'mls_class' => 'rentals',
			'armls' => 'FEAT20110412222540943628000000',
			'EasyName' => 'hoa',
			'MapName' => 'HOA',
		));
		$fieldMappings = new FieldMappings(array(
			'mls' => 'armls',
			'mls_class' => 'rentals',
			'db' => $this->db,
		));
		$this->assertEquals("FEAT20110412222540943628000000", $fieldMappings->getByEasyName('hoa')->mlsFieldName);
	}

	// This is currently a one-off problem. We allow both 'price' and
	// 'list_price' as easy names for ListPrice. 'list_price' is the canonical
	// easy name in the database, but we need to allow our clients to use
	// 'price' too. As of this writing, I know price is used extensively, I'm
	// not sure if list price is used at all. If we knew, it'd simplify things.
	public function testCanAddSecondaryEasyName() {
		$this->phactory->define('field_mapping', array());
		$hoa = $this->phactory->create('field_mapping', array(
			'mls_class' => 'res',
			'armls' => 'LIST_22',
			'EasyName' => 'list_price',
			'MapName' => 'ListPrice',
		));
		$fieldMappings = new FieldMappings(array(
			'mls' => 'armls',
			'mls_class' => 'res',
			'db' => $this->db,
		));
		$this->assertEquals("LIST_22", $fieldMappings->getByEasyName('price')->mlsFieldName);
	}
}
