jQuery( document ).ready( function( $ ) {

	$( document ).on( 'click', '.access-status-button', function() {

		var status_div 	= $(this).closest('.this-access-status');
		var status 		= $(status_div).find('.access_status').val();
		var apikey 		= $(status_div).find('.access_apikey').val();
		
		jQuery.ajax ( {
			url : access_status.endpoint,
			type : 'post', 
			data : {
				action : 'update_access_status',
				access_status : status,
				access_apikey : apikey,
				access_status_nonce : access_status.nonce,
			},
			beforeSend: function() {
				$( status_div ).html( '<i class="far fa-spinner fa-spin"></i>' );	
			},
			success: function( response ) {
				$( status_div ).html( response );
			},
			dataType:'json'
		});
	});

});