-- getprofo_wp1

CREATE TABLE wp_125_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_128_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_130_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_131_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_133_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_135_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_137_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_140_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_153_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_164_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_188_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_191_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_192_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_193_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_194_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_195_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_196_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_199_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_200_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE wp_125_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_128_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_130_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_131_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_133_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_135_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_137_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_140_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_153_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_164_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_188_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_191_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_192_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_193_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_194_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_195_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_196_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_199_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_200_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;


-- natseo

CREATE TABLE wp_22_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_23_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_24_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_35_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_36_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_37_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_46_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_47_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_51_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_54_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_55_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_56_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_57_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_58_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_61_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_62_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_63_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_64_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_65_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_66_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_67_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_68_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE wp_22_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_23_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_24_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_35_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_36_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_37_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_46_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_47_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_51_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_54_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_55_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_56_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_57_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_58_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_61_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_62_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_63_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_64_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_65_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_66_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_67_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_68_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;


-- tamsms

CREATE TABLE wp_13_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_16_pfmls_saved_listings (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, mls_id VARCHAR(11) NOT NULL,	created TIMESTAMP NOT NULL,	UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE wp_13_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE wp_16_pfmls_saved_searches (id SERIAL, user_id BIGINT(20) UNSIGNED NOT NULL, url VARCHAR(171) NOT NULL, search_name VARCHAR(1024) NOT NULL, created TIMESTAMP NOT NULL, UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;