// TODO: this function definition is duplicated for the sake of a quick fix. We should de-dupe it by moving it to a shared file.
// This function comes from https://github.com/WordPress/gutenberg/blob/5cd71f42499173e6d6f10a20a4b066f3aab77e34/packages/dom-ready/src/index.js
// I am using it instead of jQuery.ready(callback) because the callback I was passing it is not being run in a certain scenario.
// The scenario is when using the block editor, WP's JS will transform shortcode into its content via an XHR call,
// (and in our code, it enqueues this JS file)
// and then the ready function callback is not being called. The confusing thing was that this file was being executed
// before the XHR. If I put console.log statements in OTHER JS file jQuery.ready callbacks, they'd fire, but not this one.
// I assume there is something that tells Wordpress not to run the callback if it comes from XHR.
// Another scenario occurs in Wordpress 5.6 (not 5.5 or before): the Create Shortcode TinyMCE button doesn't appear,
// even though the iFound Map Maker button does. The fix is to use this domReady function instead of jQuery.ready.
function domReady( callback ) {
    if (
        document.readyState === 'complete' || // DOMContentLoaded + Images/Styles/etc loaded, so we call directly.
        document.readyState === 'interactive' // DOMContentLoaded fires at this point, so we call directly.
    ) {
        return void callback();
    }

    // DOMContentLoaded has not fired yet, delay callback until then.
    document.addEventListener( 'DOMContentLoaded', callback );
}

domReady(function() {
	var $ = jQuery;

	var meta_id;
	// This allows us to call a certain callback instead of calling something global.
	// Thus, we could have to Listings Search blocks on a single page, and when the shortcode editor is closed,
	// it call the callback for the specific instance that launched it, rather than doing some kind of global
	// notification.
	var success_callback;

	$(window).on('ifound:show-shortcode-creator', function(event, id, callback) {
		meta_id = id;
		success_callback = callback;
		showShortcodeCreator();
	});

	// Clone off the originals, so that if the user shows the shortcode creator a second time, and they don't have an
	// existing shortcode highlighted, we can show them the original form elements.
	var hasClonedInitialShortcodeFormElements = false;
	var clonedShortcodeFormElements = {};
	var elementsToClone = [
		'#ifound-dynamic-form',
		'#ifound-backup-form',
		'#ifound-display-form',
		'#ifound-stats-form',
	];
	elementsToClone.forEach(function(selector) {
		clonedShortcodeFormElements[selector] = document.querySelector(selector).cloneNode(true);
	});
	function showShortcodeCreator() {
		$( '.pop-up, .while-we-wait' ).addClass( 'active' );
		elementsToClone.forEach(function(selector) {
			var cloneOfClone = clonedShortcodeFormElements[selector].cloneNode(true);
			$(selector).html(cloneOfClone.childNodes);
		});
		$.initMapOnPop();
		doFormStuff()
			.then(removePop);
	}

	(function() {
		tinymce.PluginManager.add('ifound_shortcode', function( editor, url ) {
			editor.addButton( 'ifound_shortcode', {
				title: 'iFound Search Creator',
				icon: 'icon ifound_shortcode',
				onclick: function() {
					getMetaIdFromTinyMCE();
					showShortcodeCreator();
				}
			});
		});
		$('.pop-up .dynamic-input-autofill').on('input', function() {
			$.autofill($(this));
		});
	})();

	function removePop(){
		 $('.while-we-wait').removeClass('active');
	}

	function getMetaIdFromTinyMCE(){
		/** Get the id from the highlighted shortcode. */
		var node = tinyMCE.activeEditor.selection.getContent();
		if( node && node.length ) {
			meta_id = parseInt(node.replace(/[^0-9\.]/g, ''), 10);
		} else {
			meta_id = false;
		}
	}

	function doFormStuff() {
		if( meta_id > 0 ) {
			return Promise.all([
				/** @see advanced.js */
				$.dynamicForm( meta_id + '/dynamic/','#ifound-dynamic-form'),
				$.dynamicForm( meta_id + '/backup/','#ifound-backup-form'),

				displayForm( meta_id + '/display/', '#ifound-display-form' ),
				renderSpaSection(meta_id + '/stats/', '.ifound-stats-spa', 'shortcode_creator_stats')
			]);
		}
		return renderSpaSection(null, '.ifound-stats-spa', 'shortcode_creator_stats')
	}

	function printShortcode(shortcode){
		tinymce.activeEditor.execCommand('mceInsertContent', false, shortcode);
	}

	function isBlockEditor() {
		return typeof ifound_shortcode !== 'undefined' && ifound_shortcode.is_block_editor;
	}

	function createShortcode(){

		var input = new Object();
			input.meta_id 			= meta_id;
			input.post_id 			= $( '#post_ID' ).val();
			input.params 			= $( '#ifound-dynamic-form' ).serialize();
			input.display			= $( '#ifound-display-form' ).serialize();
			input.backup			= $( '#ifound-backup-form' ).serialize();
			input.stats				= $( '#ifound-stats-form' ).serialize();
			input.extra_map_data	= $( '#extra-map-data' ).val();

		$.ajax ( {
			url : ifound_admin.endpoint,
			type : 'post',
			data : {
				action : 'shortcode_ajax',
				input : input,
				shortcode_ajax_nonce : ifound_admin.nonce,
			},
			success: function( response ) {
				var shortcode = '[ifound id=' + response + ']';
				if (isBlockEditor()) {
					success_callback(response);
				} else {
					printShortcode(shortcode);
				}
				clearClose();
			},
			dataType:'json'
		});

	}

	/** TODO: Reset defaults on the #ifound-display-form */
	function clearClose(){
		$( '.pop-up' ).toggleClass( 'active' );
		$.clearMap();
	}
	
	function displayForm(input, selector){
		var url = ifound_map.site_url + ifound_map.shortcode + input;
		return $.getJSON( url, function( data ) {
			$( selector ).html(data.body);
		});
	}

	function renderSpaSection(url_path, selector, app_name) {
		var data = {};
		function render(data) {
			var domContainer = document.querySelector(selector);
			window.ifound_spa.render_app(app_name, domContainer, data);
		}
		if (url_path) {
			var url = ifound_map.site_url + ifound_map.shortcode + url_path;
			return $.getJSON( url, function( data ) {
				render(data);
			});
		}
		render(data);
		return Promise.resolve();
	}

	$( '.shortcode-button' ).on( 'click', function() {
		createShortcode();
	});
	
	$( '.shortcode-close' ).on( 'click', function() {
  		clearClose();
	});
	
	$( '.backup-button' ).on( 'click', function() {
		var a = $('#ifound-dynamic-form').html();
		$('#ifound-backup-form').html(a);
	});

	// Handy for debugging to auto-pop the form.
	// setTimeout(function() {
	// 	meta_id = 9037;
	// 	showShortcodeCreator();
	// }, 1000);

});
