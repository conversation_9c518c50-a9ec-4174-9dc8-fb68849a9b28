h1
{
	font-size:14px;
	padding:0px;
	margin:0px;
}

p
{
	line-height:16px;
	padding:0px;
	margin:0px;
	margin-bottom:10px;
}

.page_title
{
	font-size:20px;
	font-weight:bold;
	margin-bottom:8px;
	clear:both;
	color:#608CD8;
}

.page_line
{
	font-size:14px;
	font-weight:bold;
	margin-bottom:10px;
	clear:both;
	color:#463C2F;
}

#realestate_menu
{
	margin:0px;
	margin-bottom:15px;
	padding:0px;
	list-style-type:none;
	border:1px #ccc solid;
}

#realestate_menu li
{
	height:20px;
	line-height:20px;
	min-height:20px;
	overflow:hidden;
	font-size:11px;
	font-family:verdana;
	color:#000;
	padding-left:8px;
	border-bottom:1px #ccc solid;
}

* html #realestate_menu li
{
	height:20px;
	overflow:visible;
}

#realestate_menu a
{
	text-decoration:none;
	font-weight:bold;
}

#realestate_menu a:hover
{
	text-decoration:underline;
	font-weight:bold;
}

#realestate_menu .main
{
	color:#fff;
	font-size:11px;
	font-family:arial;
	font-weight:bold;
	background-color:#6077C7;
	padding-left:3px;
}