server {
    listen 80;
    server_name authelia.ifoundagent.com;

    location / {
        return 301 https://$host$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
}

server {
    listen 443 ssl http2;
    server_name authelia.ifoundagent.com;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifoundagent.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifoundagent.com/privkey.pem;

    set $upstream http://authelia:9091;

    location / {
        include /etc/nginx/snippets/proxy.conf;
        proxy_pass $upstream;
    }

    location /api/verify {
        proxy_pass $upstream;
    }
}
