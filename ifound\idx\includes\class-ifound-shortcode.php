<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

/**
 * iFOUND Shortcode
 *
 * @since 1.0.6
 */

class iFoundShortcode extends iFoundIdx {
	use NewHooklessTrait;

	public static $meta_key = 'save_this_shortcode';

  private $atts;
  Private $data;
  private $markers;

  /**
   * init iFOUND_shortcode class.
   *
   * @since 1.0.6
   */

  public static function init() {
    $class = __CLASS__;
    new $class;
  }

  public function __construct($options = []) {
	  $options = wp_parse_args($options, [
		  // Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
		  // it without the hooks being engaged multiple times.
		  'enable_hooks' => true,
	  ]);

	  if ($options['enable_hooks']) {
		  add_shortcode('ifound', array($this, 'shortcode'));
		  add_shortcode('ifound_stats', array($this, 'shortcode_stats'));
		  add_shortcode('ifound_polygons', array($this, 'polygons'));
		  add_shortcode('ifound_area_map', array($this, 'area_map'));

		  add_action('ifound_polygon_script', array($this, 'polygon_script'), 10, 1);
		  add_action('ifound_polygon_html', array($this, 'polygon_html'));
		  add_action('ifound_after_map', array($this, 'after_map'), 10, 2);

		  add_filter('ifound_process_polygons', array($this, 'process_polygons'));

		  add_filter('ifound_listings_search', [$this, 'listings_search']);
	  }
  }

  public function listings_search($atts) {
	  // We might not be doing true Wordpress shortcode here. This is also called from our new Gutenberg "dynamic block"
	  // that renders a Listings Search. But, for the sake of rendering the correct template, it's effectively
	  // the same and we act as if we are doing shortcode.
	  define( 'DOING_SHORTCODE', true );

	  ob_start();

	  if (!isset($atts['id']) && !isset($atts['polygon_id'])) {
		  return '';
	  }
	  $meta_data = null;
	  $polygon_data = null;

	  if( $atts['id'] ) {
		  $meta_data = $this->get_meta_by_id( $atts['id'] );
		  // If we didn't find listing search criteria, we give up. If we don't do this, if the ID refers to a
		  // postmeta record that happens to have serialized data, but isn't ours, then there will be a big nasty error
		  // on the page which will embarass us. The scenario we've come across is (we think) when an agent upgrades
		  // from our old plugin to the new one, the shortcode will refer to old, bad IDs, that just
		  // incidentally/unluckily point to a metadata record with serialized data (if it wasn't serialized data, then
		  // later, when we try to unserialize it, that would fail, and we'd return an empty string).
		  if (!$meta_data || !is_array($meta_data) || !isset($meta_data['query'])) {
			  return '';
		  }
		  $input = $meta_data;
	  }

	  if( $atts['polygon_id'] ) {
		  $polygon_data = $this->request_polygon_by_id( $atts['polygon_id'] );
		  $array['polygons'][] = $polygon_data;
		  $input['query'] = $array;
	  }

	  if (!$meta_data && !$polygon_data) {
		  return '';
	  }

	  $options = [];
	  if (isset($input['stats'])) {
		  $options['stats'] = $input['stats'];
	  }
	  if (isset($input['extra_map_data'])) {
		  $options['extra_map_data'] = $input['extra_map_data'];
	  }

	  $results = $this->process_input( $input['query'], $options );
	  $results->input_obj = $input['query'];

	  if( empty( $results->listings ) && ! empty( $input['backup_query'] ) ) {
		  $results = $this->process_input( $input['backup_query'], $options );
		  $results->input_obj = $input['backup_query'];
		  $results->backup = true;
	  }

	  $results->display   = $input['display'];

	  /** Let's find the page template. */
	  $this->template( $results, [
		  'mls_class' => is_object($input['query']) ? $input['query']->mls_class : $input['query']['mls_class'],
		  'query' => $input['query'],
		  'stats' => $options['stats'],
          'extra_map_data' => $options['extra_map_data'],
	  ]);

	  return ob_get_clean();
  }

	/**
	 * Shortcode
	 *
	 * @since 1.0.2
   * @since 2.3.0 Move display rules and backup query here.
   * @since 2.4.17 Put the data from polygon_id into 'query'.
   *
   * @see iFOUND::endpoint_callback()
   * @see iFOUND::process_shortcode()
   */

  public function shortcode( $atts ) {
    wp_enqueue_script( 'save_this_js' );

    $atts = shortcode_atts(
      array(
        'id'      => false,
        'polygon_id'  => false
      ),
      $atts
    );

    return $this->listings_search($atts);
  }

	// This method is originally intended to be used by our staff, so there is less
	// rigorous error checking. We also don't check for polygons.
	public function shortcode_stats($atts) {
		if (!$atts['id']) {
			return;
		}
		$input = $this->get_meta_by_id($atts['id']);
		$options = null;
		if (isset($input['stats'])) {
			$options = ['stats' => $input['stats']];
			$options['stats']['show'] = 'stats_only';
		}
		$results = $this->process_input($input['query'], $options);
		$results->input_obj = $input['query'];

		if (empty($results->listings) && !empty($input['backup_query'])) {
			$results = $this->process_input($input['backup_query'], $options);
			$results->input_obj = $input['backup_query'];
			$results->backup = true;
		}

		ob_start();

		do_action('ifound_stats_only', $results, [
			'mls_class' => is_object($input['query']) ? $input['query']->mls_class : $input['query']['mls_class'],
			'query'     => $input['query'],
			'stats'     => $options['stats'],
		]);

		return ob_get_clean();
	}

  /**
   * Request Polygon by ID
   *
   * Make a request for a sinfle polygon using ID.
   *
   * @since 1.0.0
   * @since 2.5.27 Move to iFOUND_shortcode class.
   *
   * @param  int    $polygon_id The DB ID of the polygon.
   * @return object $data       The data to draw polygons.
   */

  public function request_polygon_by_id( $polygon_id ) {

    $data = apply_filters( 'ifound_map_api', 'polygon_id/', array( $polygon_id ) );

    if( $data ) {

      $colors = apply_filters( 'ifound_map_colors', false );

      $polygon = array(
        'paths' => $data[0]->paths,
        'color' => $colors[array_rand( $colors )],
        'title' => $data[0]->title
      );

      return $this->obj( $polygon );

    }

    return false;

  }

  /**
   * Polygons Shortcode
   *
   * @since 1.1.2
   * @since 1.2.43 Add zoom attribute.
   * @since 2.0.2  Add center attribute.
   * @since 2.5.27 Add ids attribute.
   */

  public function polygons( $atts ) {

    $this->atts = shortcode_atts(
      array(
        'title'   => 'all',
        'ids'   => false,
        'type'    => false,
        'column'  => 'na',
        'search'  => 'na',
        'template'  => '{Title} Homes for Sale',
        'slug'    => false,
        'height'  => false,
        'zoom'    => false,
        'center'  => false
      ),
      $atts
    );

    if( ! $this->atts['type'] && ! $this->atts['ids'] ) return 'A type is required.';

    $this->data = $this->atts['ids'] ? $this->request_polygons_by_ids() : $this->request_polygons();

    if( $this->data ) {

      $this->style = $this->atts['height'] ? ( 'height:' . $this->atts['height'] . 'px;' ) : '';

      $this->force_zoom   = $this->atts['zoom'] ? intval( $this->atts['zoom'] ) : 0;

      $center         = $this->atts['center']   ? list( $lat, $lng ) = explode( ',', $this->atts['center'] )          : false;
      $this->force_center = $center           ? json_encode( array( 'lat' => floatval( $lat ), 'lng' => floatval( $lng ) ) )  : 0;

      ob_start();

      do_action( 'ifound_polygon_script', $this->data );
      do_action( 'ifound_polygon_html' );

      return ob_get_clean();

    }


  }

  /**
   * Polygon Script
   *
   * @since 1.0.0
   */

  public function polygon_script( $polygons ) {

    wp_enqueue_script( 'polygons_map_js' ); ?>

    <script>
      var force_zoom      = <? echo isset( $this->force_zoom )  ? $this->force_zoom   : 0; ?>;
      var force_center  = <? echo isset( $this->force_center )  ? $this->force_center   : 0; ?>;
      var polygons_data   = <? echo apply_filters( 'ifound_process_polygons', $polygons ); ?>;
    </script><?

  }

  /**
   * Polygon HTML
   *
   * @since 1.0.0
   */

  public function polygon_html() { ?>

    <div class="polygons-map-wrapper">

      <div class="ifound-wrap">

        <div class="polygon-title-wrapper">

          <div class="polygon-title"></div>

        </div>

        <div id="polygons-map" class="polygons-map" style="<? echo isset( $this->style ) ? $this->style : ''; ?>"></div>

      </div>

    </div><?

  }

  /**
   * Request Polygons
   *
   * Make a request to the iFound API for polygons.
   *
   * @since 1.0.0
   * @since 2.5.27 Clean up params.
   *
   * @return object $data The data to draw polygons.
   */

  private function request_polygons() {

    $params_array = array(
      urlencode( $this->atts['title'] ),
      $this->atts['type'],
      $this->atts['column'],
      urlencode( $this->atts['search'] )
    );

    return apply_filters( 'ifound_map_api', 'polygons/', $params_array );

  }

  /**
   * Request Polygons by IDs
   *
   * Make a request for a list of polygons using IDs.
   *
   * @since 2.5.27
   *
   * @return object $data The data to draw polygons.
   */

  private function request_polygons_by_ids() {

    $params = $this->atts['ids'];

    return apply_filters( 'ifound_map_api', 'polygons_by_ids/', array( $params ) );

  }

  /**
   * Process Polygons
   *
   * Process the API response onto useable map data.
   *
   * @since 2.5.39
   *
   * @param  object $polygons The raw API response.
   * @return object $polygons The data to draw polygons on the map.
   */

  public function process_polygons( $polygons ) {

    $polygons = is_array( $polygons ) ? $polygons : array( $polygons );

    foreach( $polygons as $polygon ) {

      $map_data['polygons'][] = array(
        'paths' => $this->process_path( $polygon->paths ),
        'slug'  => $this->process_slug( $polygon ),
        'title' => $polygon->title
      );

    }

    return json_encode( $map_data );

  }

  /**
   * Process Path
   *
   * Process the paths for polygons.
   *
   * @since 2.5.39
   *
   * @param  string $paths The paths string.
   * @return array  $path  An array of polygon paths.
   */

  public function process_path( $paths ) {

    $paths = str_replace( '),(', ')(', $paths );

    $coords = explode( ')(', $paths );

    foreach( $coords as $coord ){

      list( $lat, $lng ) = explode( ',', $coord );
      $remove = array('(', ')');
      $path[] = array(
        'lat' => floatval( str_replace( $remove, '', $lat ) ),
        'lng' => floatval( str_replace( $remove, '', $lng ) )
      );

    }

    return $path;

  }

  /**
   * Process Slug
   *
   * Process the slug for the polygon click link.
   *
   * @uses get_page_by_path()
   * @link https://codex.wordpress.org/Function_Reference/get_page_by_path
   *
   * @since 2.5.39
   * @since 2.5.49 Check for parent page in slug.
   * @since 2.5.50 Use get_page_by_path to search for slug.
   *
   * @param  object $row  The db row for a single polygon.
   * @return string $slug The URI for the polygon click link.
   */

  public function process_slug( $row ) {

    if( $this->atts['type'] == 'master' )
      $templste = empty( $this->atts['slug'] ) ? '{Title}-subdivisions' : $this->atts['slug'];

    else
      $templste = empty( $this->atts['slug'] ) ? '{Title}-homes-for-sale' : $this->atts['slug'];

    $slug = str_replace( '{Title}', sanitize_title( $row->title ), $templste );
    $slug = str_replace( '{id}', $row->id, $slug );

    $slug_exists = ( NULL == get_page_by_path( $slug ) ) ? false : true;

    if( $slug_exists )
      return $slug;

    if( $this->atts['type'] == 'master' )
      return $this->subdivisions . '/' . $slug . '/' . $row->master;

    return $this->polygon_search . '/' . $slug . '/' . $row->id;

  }

  /**
   * Area Map
   *
   * @since 1.1.2
   */

  public function area_map( $atts ) {

    wp_enqueue_script( 'area_map_js' );

    ob_start();

    $this->markers = $this->request_markers(); ?>

    <script>
      var map_data = <? echo $this->process_markers(); ?>
    </script>


    <div class="area-map-wrapper">

      <div class="ifound-wrap">

        <div id="area-map" class="area-map"></div>

      </div>

    </div><?

    return ob_get_clean();

  }

  private function request_markers() {
    $map_settings = get_option( 'ifound_map_settings' );
    $cities_array = $map_settings['city'];

    if($this->is_multiple_state_mls()) {
      $states_array = [];
      foreach($cities_array as &$value) {
        $parts = explode(',', $value);
        $states_array[strtoupper(str_replace(' ', '', $parts[1]))][] = $parts[0];
      }
      $filtered = [];
      $i = 0;
      foreach($states_array as $key => $value) {
        $params_array[] = array(
          $key,
          urlencode(json_encode($value)),
        );
        $filtered[] = apply_filters('ifound_map_api', 'markers/', $params_array[$i]);
        $i++;
      }
      return $filtered;
    }

    $state = strtoupper( $map_settings['state'] );

    $params_array = array(
      $state,
      urlencode( json_encode( $cities_array ) )
    );


    return apply_filters( 'ifound_map_api', 'markers/', $params_array );
  }

  private function process_markers() {
    if($this->is_multiple_state_mls()) {
      foreach($this->markers as $marker) {
        foreach($marker as $row) {
          $map_data['markers'][] = array(
            'lat'   => floatval( $row->latitude ),
            'lng'   => floatval( $row->longitude ),
            'label' => $row->city,
            'url' => site_url( $this->search . '/city-' . urlencode( $row->city ) . '/' )
          );
        }
      }
    } else {
      foreach( $this->markers as $row ) {

        $map_data['markers'][] = array(
          'lat'   => floatval( $row->latitude ),
          'lng'   => floatval( $row->longitude ),
          'label' => $row->city,
          'url' => site_url( $this->search . '/city-' . urlencode( $row->city ) . '/' )
        );

      }
    }

    return json_encode( $map_data );
  }

  private function create_url_part($key, $unescaped_value) {
    return $key . '-' . urlencode($unescaped_value);
  }

  public function after_map($results, $extra = []) {
      // To shortcode sections, add a Modify Search link. Take the user to the advanced search page with the criteria used in the shortcode search.
      if (isset($extra['query'])) {
          $query = $extra['query'];
          $url_parts = [];
          $has_polygons = false;
          foreach ($query as $key => $value) {
              if ($key === 'polygons') {
                  $has_polygons = true;
                  $url_part = $this->create_url_part('polygons', json_encode($value));
                  $url_parts[] = $url_part;
              } else if (is_array($value)) {
                  if (isset($value[0])) {
                      foreach ($value as $v) {
                          $url_parts[] = $this->create_url_part($key, $v);
                      }
                  } else {
                      if (isset($value['min'])) {
                          $url_parts[] = $this->create_url_part($key . '_min', $value['min']);
                      }
                      if (isset($value['max'])) {
                          $url_parts[] = $this->create_url_part($key . '_max', $value['max']);
                      }
                  }
              } else {
                  $url_parts[] = $this->create_url_part($key, $value);
              }
          }
          $url = implode('/', $url_parts);
		  // I noticed something strange. If there's a polygon, the URL we generate looks good. However, upon clicking
		  // it, (at least in Chrome v101) the URL that opens in a new tab is missing the final '%7D%7D', which is '}}'
		  // when URL-decoded. This is pretty odd. But a hack to fix Chrome's interpretation seems to force the URL to
		  // end in a slash.
		  if ($has_polygons && substr($url, -1) !== '/') {
              $url .= '/';
		  }
	      ?>
          <div class="modify-search-button-wrapper">
              <a href="<?= site_url('/listing-search/' . $url) ?>" class="button">Modify Search</a>
          </div>
          <?php
      }
  }

}
