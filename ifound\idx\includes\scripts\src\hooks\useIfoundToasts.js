import { useToasts } from 'react-toast-notifications';
import { showToastForFetchError } from '../lib/errors';

// Reminder: when I was trying to use "export function" instead of "export default function", I was getting this error:
// Uncaught TypeError: Cannot read property 'call' of undefined at __webpack_require__
// I think this is a bug or misconfiguration of webpack, but not really something I can control since we using the
// Wordpress scripts package which handles the webpack config. Not a big deal to just export default.
// See: https://stackoverflow.com/questions/58073626/uncaught-typeerror-cannot-read-property-call-of-undefined-at-webpack-requir
export default function useIfoundToasts() {
    const { addToast, ...rest } = useToasts();

    const addErrorToast = message => addToast(message, { appearance: 'error', autoDismiss: false });
    const addSuccessToast = message => addToast(message, { appearance: 'success' });

    return {
        addErrorToast,
        showToastForFetchError: showToastForFetchError(addErrorToast),

        addToast,
        addSuccessToast,
        ...rest,
    };
}
