document.addEventListener('DOMContentLoaded', function() {
	if(window.location.href.includes('ifound_map_settings')) {
		var showMapByDefaultFieldId = 'ifound_map_settings[show_map_by_default]';
		var showMapByDefaultField = document.getElementById(showMapByDefaultFieldId);


		var showMapByDefaultQuestionIcon = addQuestionIcon(showMapByDefaultField);
		showMapByDefaultQuestionIcon.addEventListener('mouseover', function() {
			var msgText = '<p>Make sure you have a Google API key in <a href="/wp-admin/admin.php?page=ifound_api_settings">"iFound Settings" => "API Settings" => "Google API"</a> in order for this setting to work</p>';
			var popup = displayHelpPopup(msgText, showMapByDefaultQuestionIcon);
			showMapByDefaultQuestionIcon.addEventListener('mouseout', function() {
				setTimeout(function() {
					try {
						showMapByDefaultQuestionIcon.removeChild(popup);
					} catch(e) {
						return;
					}
				}, 1500);
			});
		});
	} else if(window.location.href.includes('page=crm_integration')) {
		document.getElementById('ifound_wiseagent_api_key').addEventListener('mouseover', function() {
			var msgText = '<p>The intial sync can take up to a few minutes to complete.<br />'
				    + 'Once you save your key,<br /> your contacts will start syncing.<br />'
				    + 'You\'re free to navigate throughout the site in the meantime!</p>';
			var pElem = this.parentElement.parentElement.parentElement.parentElement;
			var popup = displayHelpPopup(msgText, pElem);
			this.addEventListener('mouseout', function() {
				setTimeout(function() {
					try {
						pElem.removeChild(popup);
					} catch(e) {
						return;
					}
				}, 1500);
			});				
		});
	} else if(window.location.href.includes('page=ifound_api_settings')) {
		jQuery('.ifound-admin-h1').on('dblclick', function(event) {
			var cookieName = 'ifound_caps';
			var capsString = readCookie(cookieName) || '';
			var message = 'Enter your ifound capabilities, separated by commas. If blank, will delete the cookie.';
			var newCapsString = prompt(message, capsString);
			if (newCapsString) {
				createCookie(cookieName, newCapsString, 60 * 60 * 24 * 365 * 10);
				alert('Set cookie to ' + newCapsString);
			} else if (newCapsString === '') {
				deleteCookie(cookieName);
				alert('Deleted ifound_caps cokie');
			}
		});
	}
});

function addQuestionIcon(field) {
	var questionIcon = document.createElement('div');
	questionIcon.innerHTML = '<i class="fas fa-question" style="color:#fff"></i>';
	questionIcon.style.className += 'help-icon';
	questionIcon.style.display = 'inline';
	questionIcon.style.position = 'relative';
	questionIcon.style.marginLeft = '10px';
	questionIcon.style.background = '#df2218';
	questionIcon.style.color = '#fff !important';
	questionIcon.style.borderRadius = '50%';
	questionIcon.style.padding = '3px 5px';
	questionIcon.style.cursor = 'pointer';
	
	field.parentNode.appendChild(questionIcon);

	return questionIcon;
}

function displayHelpPopup(message, parent) {
	var popup = document.createElement('div');
	popup.style.position = 'absolute';
	popup.style.left = '300px';
	popup.style.width = '200px';
	popup.style.zIndex = '9999';
	popup.style.padding = '10px 20px';
	popup.style.background = '#fff';
	popup.style.border = '1px solid #629e3a'
	popup.innerHTML = message;
	
	parent.appendChild(popup);
	return popup;
}
