<?
/**
 * iFoundDetails class
 *
 * Displays a Properety Detail Page.
 *
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


class iFoundDetails extends iFoundIdx {

	public $details;
	private $settings;
	private $mls_id;

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct( $mls_id ) {









		// REMINDER! This is a normal class and is instantiated directly using the 'new' keyword, whereas most of our
		// other classes in this plugin are not done this way. Anyway, the important thing to note is that putting
		// actions here might not be appropriate; they might be registered too late. I tried to put an wp_ajax_ action
		// here and it was never called, because it was registered too late.














		define( 'DOING_DETAILS', true );

		$this->mls_id 	= $mls_id;
		$this->details 	= $this->pdp_request( $this->mls_id );

		$this->has_details();

		$this->settings = $this->settings();

		add_action( 'wp_head', array( $this, 'open_graph' ) );
		add_action( 'wp_head', array( $this, 'allow_facebook_sharing' ) );

		add_action( 'wp_footer', array( $this, 'details_slider_script' ) );
		add_action( 'ifound_display_slider', array( $this, 'display_slider' ) );
		add_action( 'ifound_display_details', array( $this, 'display_details' ), 10, 1 );
		if( $this->search_nearby_enabled() ) {
			add_action( 'ifound_whats_nearby', array( $this, 'whats_nearby' ) );
		}
		add_action( 'ifound_whats_my_payment', array( $this, 'whats_my_payment' ) );
		add_action( 'ifound_save_property_button', array( $this, 'save_property_button' ) );
		add_action( 'ifound_detail_header', array( $this, 'detail_header' ) );

		add_filter( 'ifound_video_code', array( $this, 'property_video_code' ) );

	}

	/**
	 * Settings
	 *
	 * The settings for a details page.
	 *
	 * @since 3.6.2
	 *
	 * @return object $settings An object of settings.
	 */

	public function settings() {
		return (object) get_option( 'ifound_detalis_settings' );
	}

	/**
	 * Has Details
	 *
	 * Check to see if we have details to display.
	 * We 301 redirect to the Advanced Search if not.
	 *
	 * @since 3.6.2
	 */

	public function has_details() {
		// This redirect behavior is annoying because it changes the current URL and the user can't simply click back, so disable it in dev.
		if (!$this->is_php_env_production()) {
			return;
		}

		if( ! isset( $this->details->ListingID ) ) {

			wp_safe_redirect( site_url( '/' . $this->search . '/' ), 301 );

			exit;

		}

	}

	/**
	 * Details Slider Script
	 *
	 * @since 1.0.0
	 * @since 1.2.9 Do not display slider script on closed listings.
	 * @since 2.4.21 Remove closed listing image limits.
	 */

	public function details_slider_script() {

		if( isset($this->details->images) && count( $this->details->images ) > 1 ) {

			if( ! empty( $this->settings->details_slider_script ) ) { ?>

				<script>

					jQuery( document ).ready( function( $ ) {

						<? echo $this->settings->details_slider_script; ?>

					});

				</script><?

			}

		}

	}

	/**
	 * Display Slider
	 *
	 * Display the image slider.
	 * NOTE: We check for NO image. If there is no image we use no-image.jpg.
	 * @see iFOUND::check_image()
	 *
	 * @since 1.0.0
	 * @since 1.2.9 Only allow 1 image for closed listings. Do not display slider nav on closed listings.
	 * @since 2.4.21 Remove closed listing image limits.
	 */

	public function display_slider(){

		wp_enqueue_style( 'featured_listings_css' );
		wp_enqueue_style( 'slick_css' );

		wp_enqueue_script( 'slick_js' );
		wp_enqueue_script( 'jquery-migrate' ); ?>

		<div id="details-slider" class="details-slider">

			<div class="ifound-wrap">

				<div class="ifound-details-slider"><?

					$i = 0;

					/** If no images, let's pass an empty array so we can get no-image.png  */
					$images = empty( $this->details->images ) ? array( false ) : $this->details->images;

					foreach( $images as $image ) {

						$src = apply_filters( 'ifound_check_image', $image->highres_url, true );

						if( $i == 0 ) { ?>

							<div><img src="<? echo $src; ?>" alt="Real Estate Photo"/></div><?

						} else { ?>


							<div><img data-lazy="<? echo $src; ?>" alt="Real Estate Photo"/></div><?

						}

						$i++;

					} ?>

				</div><?

				if( count( $images ) > 1 ) { ?>

					<div class="ifound-details-slider-nav"><?

						foreach( $images as $image ) {

							$src = apply_filters( 'ifound_check_image', $image->normal_url ); ?>

							<div><img src="<? echo $src; ?>" alt="Real Estate Photo"/></div><?

						} ?>

					</div><?

				} ?>

			</div>

		</div><?

	}

	private function get_open_house_section($open_houses) {
		if (!$open_houses) {
			return '';
		}

		ob_start();

		?>
		<h2 class="pdp-h2 open-houses">Open Houses</h2>

		<div class="ifound-open-houses-wrapper">
		<?php
		foreach ($open_houses as $open_house) {
			?>
			<div class="ifound-open-house">
				<i class="fa fa-home"></i>
				<div class="ifound-open-house-range"><?= $open_house->RangeDateString ?></div>
			</div>
			<?php
		}
		?>
		</div>
		<?php

		return ob_get_clean();
	}

	private function details_body() {
		$body = <<<EOT
<div class="ifound-details">
	<div class="ifound-wrap">
		<div class="details-section list-status">{Status}</div>
		<div class="details-section section-0">
			{open_house_section}
		</div>
		<div class="details-section section-1">
			{pdp_property_value}
		</div>
		<div class="details-section section-2">
			{pdp_general_value}
		</div>
		<div class="details-section section-3">
			{pdp_features_value}
		</div>
		<div class="details-section section-4">
			{pdp_school_value}
		</div>
		<div class="details-section section-5">
			{pdp_community_value}
		</div>
		<div class="details-section section-6">
			{pdp_lot_value}
		</div>
		<div class="details-section section-7">
			{pdp_rooms_value}
		</div>
		<div class="details-section section-8">
			{pdp_location_value}
		</div>
	</div>
</div>
EOT;
		return $body;
	}

	/**
	 * Display Details
	 *
	 * Display the details of a single property.
	 *
	 * @since 1.0.0
	 */

	public function display_details($extra = []) {
		wp_enqueue_style( 'property_details_css' );
		wp_enqueue_script( 'save_this_js' );

		$this->details->open_house_section = $this->get_open_house_section($this->details->open_houses);

		$details_body = $this->details_body();
		$this->details->Status = apply_filters( 'ifound_check_status', $this->details, $extra);
		echo apply_filters( 'ifound_filter_merge_tags', $details_body, $this->details );
	}

	/**
	 * Whats Nearby
	 *
	 * The HTML structire for the map.
	 *
	 * @since 1.0.0
	 */

	public function whats_nearby() {

		if( empty( $this->details->Latitude ) || empty( $this->details->Longitude ) ) return;

		wp_enqueue_script( 'jquery-ui' );
		wp_enqueue_script( 'nearby_places_js' );

		$center = array(
			'lat' => $this->details->Latitude,
			'lng' => $this->details->Longitude
		);

		$address = apply_filters( 'ifound_address', $this->details ); ?>

		<script> var nearby = <? echo json_encode( $center ); ?>;</script>

		<div class="ifound-nearby-places">

			<div class="ifound-wrap">

				<h4><? _e( "What's Nearby? : " . $address, 'ifound' ); ?></h4>

				<div class="nearby-places-map-wrap">

					<div class="nearby-places-map" id="nearby-places-map"></div>

				</div>

				<div class="nearby-places-choices-wrap">

					<div class="nearby-places-choices">

						<? $this->show_places(); ?>

					</div>

				</div>

			</div>

		</div><?

	}

	/**
	 * Show Places
	 *
	 * The buttons under the Whats Nearby map.
	 *
	 * @since 1.0.0
	 */

	public function show_places() {

		$places = array(
			'airport' 					=> 'Airport',
			'art_gallery'				=> 'Art Gallery',
			'bar'						=> 'Bar/Night Club',
			'bank'						=> 'Bank',
			'cafe'						=> 'Cafe',
			'church'					=> 'Church',
			'dentist'					=> 'Dentist',
			'doctor'					=> 'Doctor',
			'fire_station'				=> 'Fire Station',
			'grocery_or_supermarket'	=> 'Grocery Store',
			'gym'						=> 'Gym',
			'hospital'					=> 'Hospital',
			'laundry'					=> 'Laundry',
			'movie_theater'				=> 'Movie Theater',
			'park'						=> 'Park',
			'police'					=> 'Police',
			'post_office'				=> 'Post Office',
			'pharmacy'					=> 'Pharmacy',
			'restaurant'				=> 'Restaurant',
			'school'					=> 'School'
		);

		$i = 1;

		foreach( $places as $key => $value ) {

			$checked = $key == 'school' ? 'checked' : ''; ?>

			<div class="<? echo $key; ?>_wrap nearby-places-section">

				<input type="radio" name="nearby-place" class="nearby-places-radio" value="<? echo $key; ?>" id="radio-<? echo $i; ?>" <? echo $checked; ?>>

				<label for="radio-<? echo $i; ?>" class="nearby-label"><? _e( $value, 'ifound' ); ?></label>

			</div><?

			$i++;

		}

	}

	/**
	 * Save Property Button
	 *
	 * @since 3.6.2
	 */

	public function save_property_button() {
 		if( empty( $this->address ) ) {
                        $this->title = $this->details->StreetNumber . ' ' . $this->details->StreetPrefix . ' ' . $this->details->StreetName . ' ' . $this->details->StreetSuffix . ' ' . $this->details->City . ' ' . $this->details->State . ', ' . $this->details->PostalCode;
                        $this->title = str_replace('  ', ' ', $this->title);
                }
		do_action( 'ifound_save_this_button', 'property-alert', $this->title, $this->mls_id );
	}

	/**
	 * What's My Payment
	 *
	 * Display's in a widget area. Normally above the PDP slider.
	 *
	 * @since 1.0.0
	 * @since 3.3.4 Check ListStatus in all lower case.
	 */

	public function whats_my_payment() {

		$details 		= $this->details;

		$list_status 	= isset( $details->ListStatus ) ? strtolower( $details->ListStatus ) : false;
		$mls_class 		= isset( $details->mls_class )  ? strtolower( $details->mls_class )  : false;

		if( $list_status == 'active' && $mls_class == 'res' ) {

			$remove = array( ',', '$' );
			$price 	= intval( str_replace( $remove, '', $details->ListPrice ) );
			$tax 	= intval( str_replace( $remove, '', $details->TotalTaxes ) );

			wp_enqueue_script( 'cookie_js' );
			wp_enqueue_script( 'whats_my_payment_js' ); ?>

			<div class="whats-my-payment-wrapper">

				<div class="ifound-wrap">

					<div class="whats-my-payment-body">

						<div class="ifound-wrap">

							<div class="whats-my-payment-price-wrapper one-half first">
								<div class="whats-my-payment-wrapper">
									<span class="whats-my-payment-price-label"><? _e( 'Price', 'ifound' ); ?></span>
									<span class="whats-my-payment-price"><? _e( $details->ListPrice, 'ifound' ); ?></span>
									<span class="whats-my-payment-suffix"><? _e( '.00', 'ifound' ); ?></span>
								</div>
								<div class="whats-my-payment-compare">
									<a rel="nofollow" href="<? $this->compare_price_url(); ?>" class="compare-button" target="_blank"><? _e( 'Compare Prices', 'ifound' ); ?></a>
								</div>
							</div>

							<div class="whats-my-payment-payment-wrapper one-half">
								<div class="whats-my-payment-wrapper">
									<span class="whats-my-payment-price-label"><? _e( 'Approx', 'ifound' ); ?></span>
									<span class="whats-my-payment-payment"><i class="fal fa-spinner fa-spin" aria-hidden="true"></i></span>
									<span class="whats-my-payment-suffix"><? _e( '/month', 'ifound' ); ?></span>
								</div>
								<div class="whats-my-payment-more"><? _e( 'Get More Details', 'ifound' ); ?></div>
							</div>

						</div>

					</div>

					<div class="whats-my-payment-buttons">

						<div class="whats-my-payment-button one-third first">
							<div class="button prequal"><? _e( 'Get Pre-Qualified', 'ifound' ); ?></div>
						</div>

						<div class="whats-my-payment-button one-third">
							<div class="button make-offer"><? _e( 'Make an Offer', 'ifound' ); ?></div>
						</div>

						<div class="whats-my-payment-button one-third">
							<div class="button schedule-showing"><? _e( 'Schedule Showing', 'ifound' ); ?></div>
						</div>


					</div>

				</div>

			</div>

			<!-- Start Calc Popup Section -->
			<div class="more-backdrop">

				<div class="whats-my-payment-form">

					<div class="whats-my-payment-close"><i class="fal fa-window-close" aria-hidden="true"></i></div>

					<!-- Start Calc Section -->
					<div class="ifound-wrap calc-section">

						<div class="whats-my-payment-calc">

							<div class="one-half first">
								<div class="disc"><? _e( 'Sale Price:', 'ifound' ); ?></div>
							</div>

							<div class="one-half" id="sale-price-input">
								<? _e( '$' . number_format( $price ), 'ifound' ); ?>
								<input type="hidden" id="detail-price" value="<? echo $price; ?>"/>
							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Loan Type:', 'ifound' ); ?></div>
							</div>

							<div class="one-half">

								<div class="conv">
									<input type="radio" id="conv" value="20" name="myradio" class="myradio"/><? _e( ' Conventional - 5% down', 'ifound' ); ?>
								</div>

								<div class="fha">
									<input type="radio" id="fha" value="3.5" name="myradio" class="myradio"/><? _e( ' FHA - 3.5% down', 'ifound' ); ?>
								</div>

								<div class="va">
									<input type="radio" id="va"  value="0" name="myradio" class="myradio"/><? _e( ' VA - 0% down', 'ifound' ); ?>
								</div>

							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Additional $ Down:', 'ifound' ); ?></div>
							</div>

							<div class="one-half">
								<input type="number" min="0" max="20000000" step="100" id="update-down" class="calc"/>
							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Total Down Payment:', 'ifound' ); ?></div>
							</div>

							<div class="one-half">
								<div id="update-total" class="total-down"></div>
								<input type="hidden" id="budget-down"/>
							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Loan Term: (years)', 'ifound' ); ?></div>
							</div>

							<div class="one-half">
								<input type="number" min="1" max="30" step="1" id="budget-term" class="calc"/>
							</div>


							<div class="one-half first">
								<div class="disc"><? _e( 'Interest Rate: (%)', 'ifound' ); ?></div>
							</div>

							<div class="one-half">
								<input type="number" min="2" max="10" id="budget-rate" class="calc"/>
							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Property Tax: (year)', 'ifound' ); ?></div>
							</div>

							<div class="one-half" id="update-tax-input">
								<? _e( '$' . number_format( $tax ), 'ifound' ); ?>
								<input type="hidden" id="detail-tax" value="<? echo $tax; ?>" />
							</div>

							<div class="one-half first">
								<div class="disc"><? _e( 'Insurance: (year)', 'ifound' ); ?></div>
							</div>

							<div class="one-half">
								<input type="number" min="0" max="10000" step="10" id="update-ins" class="calc"/>
							</div>

						</div>

						<div class="whats-my-payment-form-button">

							<div class="ifound-wrap">
								<div id="update-form" class="calculate-payment button"><? _e( 'Calculate Payment', 'ifound' ); ?></div>
							</div>

						</div>

					</div>
					<!-- End Calc Section -->

					<!-- Start Answer Section -->
					<div class="ifound-wrap answer-section" style="display:none;">

						<div class="whats-my-payment-wrapper">
							<span class="whats-my-payment-price-label"><? _e( 'Approx', 'ifound' ); ?></span>
							<span class="whats-my-payment-payment"><i class="fal fa-spinner fa-spin" aria-hidden="true"></i></span>
							<span class="whats-my-payment-suffix"><? _e( '/month', 'ifound' ); ?></span>
						</div>

						<div class="whats-my-payment-form-button">

							<div class="ifound-wrap">

								<a rel="nofollow" href="<? $this->similar_url(); ?>" target="_blank" class="button"><? _e( 'Similar Properties', 'ifound' ); ?></a>

							</div>

						</div>

						<div class="not-budget-heading"><? _e( 'Not within your budget?', 'ifound' ); ?></div>

						<div class="one-half first">
							<div class="desired-paynebt"><? _e( 'Desired Payment:', 'ifound' ); ?></div>
						</div>

						<div class="one-half">
							<input type="number" min="100" step="100" id="budget-payment" class="new-budget"/>
						</div>

						<div class="whats-my-payment-form-button">

							<div class="ifound-wrap">

								<input type="hidden" class="budget-city" value="<? echo urlencode( $details->City ); ?>"/>

								<div class="button search-budget"><? _e( 'Search Your Budget', 'ifound' ); ?></div>

							</div>

						</div>

						<div class="back-to-calc"><? _e( 'Back', 'ifound' ); ?></div>

					</div>
					<!-- End Answer Section -->

				</div>

			</div>
			<!-- End Calc Popup Section -->

			<? $forms = get_option( 'ifound_registration_settings' ); ?>

			<div class="prequal-backdrop">

				<div class="whats-my-payment-prequal">

					<div class="ifound-wrap">

						<div class="whats-my-payment-close"><i class="fal fa-window-close" aria-hidden="true"></i></div>

						<h2><? _e( 'Get Pre-Qualified', 'ifound' ); ?></h2>

						<? if( ! empty( $forms['prequal_form_id'] ) ) {
							gravity_form( $forms['prequal_form_id'], false, false, false, null, true, 99 );
						}?>

					</div>

				</div>

			</div>

			<div class="make-offer-backdrop">

				<div class="whats-my-payment-make-offer">

					<div class="ifound-wrap">

						<div class="whats-my-payment-close"><i class="fal fa-window-close" aria-hidden="true"></i></div>

						<h2><? _e( 'Make An Offer', 'ifound' ); ?></h2>

						<? if( ! empty( $forms['make_offer_form_id'] ) ) {
							gravity_form( $forms['make_offer_form_id'], false, false, false, null, true, 99 );
						} ?>

					</div>

				</div>

			</div>

			<div class="schedule-backdrop">

				<div class="whats-my-payment-schedule">

					<div class="ifound-wrap">

						<div class="whats-my-payment-close"><i class="fal fa-window-close" aria-hidden="true"></i></div>

						<h2><? _e( 'Schedule Showing', 'ifound' ); ?></h2>

						<? if( ! empty( $forms['schedule_showing_form_id'] ) ) {
							gravity_form( $forms['schedule_showing_form_id'], false, false, false, null, true, 99 );
						} ?>

					</div>

				</div>

			</div><?

		}

	}

	/**
	 * Detail Header
	 *
	 * The header under the title on Detail pages.
	 *
	 * @since 3.6.2
	 */

	public function detail_header() {

		if( $this->details->mls_class == 'res') { ?>

			<div class="top-pdp-data">

				<div class="pdp-data-wrapper beds">

					<div class="pdp-label beds"><? _e( 'Bedrooms:', 'ifound' ); ?></div>
					<div class="pdp-data beds"><? _e( $this->details->Beds, 'ifound' ); ?></div>

				</div>

				<div class="pdp-data-wrapper baths">

					<div class="pdp-label baths"><? _e( 'Bathrooms:', 'ifound' ); ?></div>
					<div class="pdp-data baths"><? _e( $this->details->Bathrooms, 'ifound' ); ?></div>

				</div>

				<div class="pdp-data-wrapper int-sqft">

					<div class="pdp-label int-sqft"><? _e( 'Square Feet:', 'ifound' ); ?></div>
					<div class="pdp-data int-sqft"><? _e( $this->details->SquareFeet . ' sqft', 'ifound' ); ?></div>

				</div>

			</div><?

		}

	}

	/**
	 * Compare Price URL
	 *
	 * The URL to a CMC Report with the Details as criteria.
	 *
	 * @since 3.6.2
	 */

	public function compare_price_url() {

		$details = $this->details;

		$address = apply_filters( 'ifound_address', $this->details );

		$sqft = intval( str_replace( ',', '', $details->SquareFeet ) );

		$string = '/';

		$string .= 'current-market-comparison/?';

		$string .= 'Address=' . urlencode( $address ) . '&';

		$string .= 'PropType=' . urlencode( $details->PropType ) . '&';

		$string .= 'Beds=' . $details->Beds . '&';

		$string .= 'Bathrooms=' . $details->Bathrooms . '&';

		$string .= 'SquareFeet=' . round( ( $sqft * .90 ) ) . '-' . round( ( $sqft * 1.1 ) ) . '&';

		$string .= 'IntStories=' . $details->IntStories . '&';

		$string .= 'Latitude=' . $details->Latitude . '&';

		$string .= 'Longitude=' . $details->Longitude;

		echo site_url( $string );

	}

	/**
	 * Simalar URL
	 *
	 * The URL to a serach for properties similar to the current Details.
	 *
	 * @since 3.6.2
	 */

	public function similar_url() {

		$details = $this->details;

		$remove = array( ',', '$' );
		$price 	= intval( str_replace( $remove, '', $details->ListPrice ) );

		$string = '/';

		$string .= 'listing-search/';

		$string .= 'city-' . urlencode( $details->City ) . '/';

		//$string .= 'prop_type-' . urlencode( $details->PropType ) . '/'; TODO: convert this to display name.

		$string .= 'bedrooms_min-' . $details->Beds . '/';

		$string .= 'bathrooms_max-' . $details->Bathrooms . '/';

		$string .= 'list_price_min-' . round( ( $price * .97 ) ) . '/';

		$string .= 'list_price_max-' . round( ( $price * 1.03 ) ) . '/';

		echo site_url( $string );

	}

	/**
	 * Open Graph
	 *
	 * Add open graph meta tags to head on PDP pages.
	 *
	 * @since 1.5.0
	 */

	public function open_graph() {

		if( empty( $this->details->images ) ) return;

		$image_url = apply_filters( 'ifound_check_image', $this->details->images[0]->highres_url, true );

		$address = apply_filters( 'ifound_address', $this->details );

		$data = array(
			'title' 		=> $address,
			'type' 			=> 'website',
			'site_name'		=> get_option( 'blogname' ),
			'url' 			=> ( isset( $_SERVER['HTTPS'] ) ? 'https' : 'http' ) . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
			'description' 	=> $this->details->Remarks,
			'image' 		=> $image_url,
			'image:alt' 	=> 'Photo of ' . $address,
		);

		$data = array_filter( $data ); ?>

		<!-- Start iFound Open Graph --><?

		foreach ( $data as $property => $content ) { ?>

			<meta property="og:<? echo $property; ?>" content="<? echo $content; ?>" /><?

		} ?>

		<!-- End iFound Open Graph --><?

	}

	// Facebook is hammering our servers, and we can't differentiate their crawl requests from share requests. So we
	// have been blocking FB but we want to allow users to share PDP links on FB, so our solution is to put a special
	// query param in the URL, such that when a user copy-pastes it into FB, Site District will allow that particular
	// request.
	public function allow_facebook_sharing() {
		if (!$this->util()->is_user_agent_bot()) {
			?>
			<script type="application/javascript">
				var url = new URL(window.location.href);
				url.searchParams.set('allow_ifound_sharing', 'yes');
				window.history.replaceState(null, null, url.toString());
			</script>
			<?php
		}
	}

	/**
	 * Property Video Code
	 *
	 * This is a YouTube video code from video URL. If a video code is assigned to an MLS ID,
	 * That code will be returned. If no video code is assigned, we will look for a deafult code.
	 * If there is neither, we return bool false.
	 *
	 * @since 3.6.2
	 *
	 * @param  bool  $video_code This should always be false.
	 * @return mixed $video_code This will be a string value of the video code. Bool false if no code exists.
	 */

	public function property_video_code( $video_code ) {

		if ( $videos = get_option( 'ifound_property_videos', array() ) ) {

			foreach ( $videos as $video ) {

				if ( $this->mls_id == $video['mls_id'] ) {

					return $video['code'];

				}

			}

		}

		if ( $default = get_option( 'ifound_property_video_default' ) ) {

			return $default;

		}

		return false;

	}

}
