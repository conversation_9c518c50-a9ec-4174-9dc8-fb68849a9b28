jQuery( document ).ready( function( $ ) {
	
	$( '#ifound-footer-login' ).on( 'click', function() {
		var $window = $(window);
		var window_top_position = $window.scrollTop();
		var img_top_position = (window_top_position + 50);
		$('.ifound-login-popup').css('top', img_top_position);
		$( '.ifound-login-popup, .ifound-login-backdrop, .ifound-login-close' ).addClass( 'active' );
	});

	$( '#ifound-footer-logout' ).on( 'click', function() {
		var $window = $(window);
		var window_top_position = $window.scrollTop();
		var img_top_position = (window_top_position + 150);
		$( '.pop-msg' ).css('top', img_top_position);
		$( '.while-we-wait' ).addClass( 'active' );
		$.ajax({
			url : ifound_login.endpoint,
			type : 'post',
			data : {
				action : 'pop_logout',
				ifound_login_nonce : ifound_login.nonce
			},
			success : function(response){
				location.reload();
			}
		});
		
	});	
	
	$( '.ifound-login-close' ).on( 'click', function() {
		$( '.ifound-login-popup, .ifound-login-backdrop, .ifound-login-close' ).removeClass( 'active' );
	});
	
});