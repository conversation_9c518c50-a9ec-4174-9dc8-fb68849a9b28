<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');
require_once(__DIR__ . '/../../traits/UtilTrait.php');

class iFoundLeadGeneration extends iFoundCrm {
	use UtilTrait;
	use NewHooklessTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
		}
	}

	private function maybe_get_config() {
		$host = $this->util()->get_host();
		$uri = $_SERVER['REQUEST_URI'];

		if ($host === 'tempearizonahouses.com') {
			if (
				preg_match('#phoenix-area-homes-for-sale#', $uri)
				&& preg_match('#referralsource=Reply#', $uri)
			) {
				return [
					'form_id' => 16,
					'user_id' => 533,
				];
			} else if (preg_match('#phoenix-homes#', $uri)) {
				return [
					'form_id' => 15,
					'user_id' => 533,
				];
			}
		} else if ($host === 'armlsspark.ifoundsites.test') {
			// http://armlsspark.ifoundsites.test/phoenix-area-homes-for-sale?referralsource=Reply
			if (
				preg_match('#phoenix-area-homes-for-sale#', $uri)
				&& preg_match('#referralsource=Reply#', $uri)
			) {
				return [
					'form_id' => 11,
					'user_id' => 70,
				];
			}
		}

		return null;
	}

	public function maybe_get_user_id_for_form_id($form_id) {
		$config = $this->maybe_get_config();
		if ($config && $config['form_id'] === $form_id) {
			return $config['user_id'];
		}

		return null;
	}

	// Return null if we don't want to override the one from settings.
	public function maybe_get_registration_form_id() {
		$config = $this->maybe_get_config();
		if ($config) {
			return $config['form_id'];
		}

		return null;
	}

	public function should_site_and_page_force_registration($host, $uri) {
		$config = $this->maybe_get_config();
		return !!$config;
	}

	public function handle_new_contact_from_gravity_forms($contact_data, $entry, $form) {
		$this->maybe_handle_lead_for_team_member($contact_data, $entry, $form);
		$this->maybe_handle_agent_lead_for_our_company_crm($contact_data, $entry, $form);
	}

	public function maybe_handle_lead_for_team_member($contact_data, $entry, $form) {
		$config = $this->maybe_get_config();
		if (!$config) {
			return;
		}

		$contact_id = $contact_data['contact_id'];
		if (!isset($contact_data['city'])) {
			$contact_data['city'] = rgar($entry, '7');
		}
		$post_author = $config['user_id'];
		$iFoundContactsObj = iFoundContacts::new_hookless();
		if ($post_author) {
			// For the sake of firing any update hooks.
			wp_update_post([
				'ID' => $contact_id,
				'post_author' => $post_author,
			]);
			// CF here stands for Cassie Feldman.
			wp_set_object_terms(
				$contact_id,
				'PPC Buyer CF',
				iFoundContacts::$generic_contact_tag_taxonomy,
				true
			);

			$crm = $this->crm_from_user_id($post_author);
			$start_date_one_day_in_future = date('Y-m-d', strtotime('+1 day', $this->current_time()));
			$should_start_search_campaign = $crm->{iFoundCrm::$AUTO_START_SEARCH_CAMPAIGN_KEY} === 'enabled';
			if ($should_start_search_campaign) {
				$email_template = get_posts([
					'author'    => $post_author,
					'title'     => 'Buyer Search Template',
					'post_type' => iFoundEmail::$the_post_type,
				])[0];
				if ($email_template) {
					$data = (object) [
						'post_author'    => $post_author,
						'contact_id'     => $contact_id,
						'campaign_title' => "{$contact_data['city']} Home Search",
						'save_type'      => 'market-update',
						'params'         => [
							'mls_class'   => 'Residential',
							'sort'        => 'latest_listing',
							'list_status' => ['Active'],
							'city'        => [$contact_data['city']],
						],
						'stats'          => iFoundSaveThis::$stats_default,
						'extra_map_data' => [],
					];
					$campaign_id = iFoundSaveThisAdmin::new_hookless()->save_this_campaign($data);
					// We will not include instant updates for Cassie Feldman.
					// wp_set_object_terms($campaign_id, 'instant-update', iFoundSaveThis::$the_taxonomy, true);
					$data = (object) [
						iFoundSharedCampaign::$DO_EMAIL_KEY => iFoundSharedCampaign::$DO_EMAIL_YES,
						iFoundSharedCampaign::$TO_EMAIL_KEY => $contact_data['email'],
						'custom_content'           => $email_template->post_content,
						'custom_subject'           => get_post_meta($email_template->ID, 'subject', true),
						'recurring'                => 'yes',
						'how_often'                => 'Weekly',
						'header'                   => $crm->header,
						'signature'                => $crm->signature,
						'footer'                   => $crm->footer,
						'start_date'               => $start_date_one_day_in_future,
						'time_of_day'              => 'now',
						'end_date'                 => '',
					];
					iFoundSaveThis::new_hookless()->save_campaign_stuff_and_maybe_run($campaign_id, $data);
				}
			}
			$should_start_drip_campaign = $crm->{iFoundCrm::$AUTO_START_DRIP_CAMPAIGN_KEY} === 'enabled';
			if ($should_start_drip_campaign) {
				$drip_template = get_posts([
					'author'    => $post_author,
					'title'     => 'PPC Buyer Drip Campaign',
					'post_type' => iFoundDripTemplate::$the_post_type,
				])[0];
				if ($drip_template) {
					$drip_campaign_data = (object) [
						'post_author'              => $post_author,
						'contact_id'               => $contact_id,
						'campaign_title'           => 'West USA Buyer',
						iFoundSharedCampaign::$TO_EMAIL_KEY => $contact_data['email'],
						'drip_template_id'         => $drip_template->ID,
						'header'                   => $crm->header,
						'signature'                => $crm->signature,
						'footer'                   => $crm->footer,
						'upcoming_step_index'      => 0,
						'start_date'               => $start_date_one_day_in_future,
						'time_of_day'              => 'now',
					];
					iFoundDripCampaign::new_hookless()->create_drip_campaign($drip_campaign_data);
				}
			}
		}
	}

	// Hard-coding some auto start drip campaign stuff for ifoundagent.com.
	private function maybe_handle_agent_lead_for_our_company_crm($contact_data, $entry, $form) {
		$contact_id = $contact_data['contact_id'];
		$start_date_one_day_in_future = date('Y-m-d', strtotime('+1 day', $this->current_time()));
		$config = [
			'host' => 'ifoundagent.com',
			'form_id' => 152,
			'drip_template_id' => 14071,
			'post_author' => 1,
		];
		if (!iFound::new_hookless()->is_php_env_production()) {
			$config = [
				'host' => 'armlsspark.ifoundsites.test',
				'form_id' => 9,
				'drip_template_id' => 679,
				'post_author' => 61,
			];
		}

		if (!($this->util()->get_host() === $config['host'] && intval($form['id']) === $config['form_id'])) {
			return;
		}

		// Reminder: I've now created a system where we can make forms, and rather than forcing fields to have certain
		// IDs, as long as they have a certain admin label, we know what kind of field it is. However, I'm not going
		// to change this assumption because it's a special scenario on one site.
		$how_did_you_find_this_page = rgar($entry, '14');
		$term = $how_did_you_find_this_page;
		if ($term && is_string($term)) {
			wp_set_object_terms($contact_id, $term, iFoundContacts::$generic_contact_tag_taxonomy, true);
		}
		$drip_template = get_post($config['drip_template_id']);
		if ($drip_template) {
			$crm = $this->crm_from_user_id($config['post_author']);
			$drip_campaign_data = (object) [
				'post_author'              => $config['post_author'],
				'contact_id'               => $contact_id,
				'campaign_title'           => 'IFA marketing campaign 1',
				iFoundSharedCampaign::$TO_EMAIL_KEY => $contact_data['email'],
				'drip_template_id'         => $drip_template->ID,
				'header'                   => $crm->header,
				'signature'                => $crm->signature,
				'footer'                   => $crm->footer,
				'upcoming_step_index'      => 0,
				'start_date'               => $start_date_one_day_in_future,
				'time_of_day'              => 'now',
			];
			iFoundDripCampaign::new_hookless()->create_drip_campaign($drip_campaign_data);
		}
	}
}
