jQuery(document).ready(function($){
	
	$( '.whats-my-payment-more' ).on( 'click', function(){
		$( '.whats-my-payment-form' ).fadeIn( 'slow' );
		$( '.more-backdrop' ).fadeIn( 'slow' );
	});
	
	$( '.make-offer' ).on( 'click', function(){
		$( '.whats-my-payment-make-offer' ).fadeIn( 'slow' );
		$( '.make-offer-backdrop' ).fadeIn( 'slow' );
	});
	
	$( '.prequal' ).on( 'click', function(){
		$( '.whats-my-payment-prequal' ).fadeIn( 'slow' );
		$( '.prequal-backdrop' ).fadeIn( 'slow' );
	});

	$( '.schedule-showing' ).on( 'click', function(){
		$( '.whats-my-payment-schedule' ).fadeIn( 'slow' );
		$( '.schedule-backdrop' ).fadeIn( 'slow' );
	});
	
	$( '.whats-my-payment-close' ).on( 'click', function(){
		$( '.whats-my-payment-form, .whats-my-payment-make-offer, .whats-my-payment-prequal, .whats-my-payment-schedule, .prequal-backdrop, .make-offer-backdrop, .more-backdrop, .schedule-backdrop' ).fadeOut( 'slow' );
	});
	
	$( '.calculate-payment' ).on( 'click', function(){
		$( '.calc-section' ).slideUp( 'slow' );
		$( '.answer-section' ).slideDown( 'slow' );
	});
	
	$( '.back-to-calc' ).on( 'click', function(){
		$( '.answer-section' ).slideUp( 'fast' );
		$( '.calc-section' ).slideDown( 'slow' );
	});
	
	$( '.calc' ).on( 'keyup', function(){
		display();
	});
	
	$( 'input[name=myradio]' ).on( 'change', function(){
		display();
	});
	
	$( '.search-budget' ).on( 'click', function(){
		searchBudget();
	});
	
	function searchBudget(){
		var payment = $('#budget-payment').val();
		if( payment < 1 ){
			$( '#budget-payment' ).addClass('required-red');
			return false;
		}
		var Months = $('#budget-term').val() * 12;
		var MonthlyPaymentAmount = parseFloat(payment);
		var APR = $('#budget-rate').val() / 100;   //16.9% APR
		var InterestRate = (APR / 12);     //monthly interest
		var TotalAmountOfCredit = (MonthlyPaymentAmount / InterestRate) * (1 - Math.pow((1 + InterestRate), (-Months)));
		var budget = TotalAmountOfCredit + $('#budget-down').val();
		var min = Math.round( budget * .9 );
		var max = Math.round( budget * 1.1 );
		location.href = whats_my_payment.search_url + 'list_price_min-' + min + '/list_price_max-' + max + '/city-' + $( '.budget-city' ).val(); 
	}
	
	function runCalc(){
		
		var loanTypeId = getLoanTypeId();
		$('#' + loanTypeId).prop('checked', true)

		$('#budget-term').val(getTerm());
		$('#budget-rate').val(getRate());
		$('#update-down').val(getAddDown());
		$('#update-total').html(getDownPayment());
		$('#budget-down').val(getDownPayment());
		//$('#update-tax').val(getPropTax());
		$('#update-ins').val(getPropIns());
			
		display();
	
	}
	
	function display(){
		
		updateFormCookie();
		var total = getDownPayment()
		$('#update-total').html( '$' + numberWithCommas(total) );
		$('#budget-down').val(total);
		var payment = getPayment();
		$('.whats-my-payment-payment').html( '$' + numberWithCommas(payment) );
		
	}

	function getPayment() {
		
		var loan = salePrice();
		var tax = getPropTax();
		var ins = getPropIns();
		var ti = parseInt(tax + ins);
		var loanType = getLoanType();
		var addDown = getAddDown();
		var downPayment = getDownPayment();
		var rate = getRate();
		var term = getTerm();
		var amount = (loan - downPayment);
		var amount = parseInt(amount);
		var payment = getMonthlyPayment(amount, (rate /100) / 12, term * 12, ti);
		
		return payment; 
	}

	function salePrice(){
		var price = $( '#detail-price' ).val();
		return parseInt(price);
		
	}

	function getPropTax(tax){
		var tax = $( '#detail-tax' ).val();
		return parseInt(tax);
	}

	function getPropIns(ins){
	
		var c = 'update-ins';
		var cv = readCookie(c);
	
		if (cv != null){
			var ins = cv;
		}else{
			createCookie(c,1500,500000);
			var ins = readCookie(c);
		}
		var ins = parseInt(ins.replace(/\D/g,''));
		return ins;
	}

	function getMonthlyPayment(PR, IN, PE, TI) {
	
		var PAY = (PR * IN) / (1 - Math.pow(1 + IN, -PE));
		var PAY = PAY + (TI / 12);
		var PAY = parseInt(PAY);
	
		return PAY;
	}

	function getLoanType(loanType){
	
		var c = 'loanType';
		var cv = readCookie(c);
	
		if (cv != null){
			var loanType = cv;
		}else{
			createCookie(c,5,500000);
			var loanType = readCookie(c);
		}
		var loanType = Number(loanType);
		return loanType;
	}

	function getLoanTypeId(loanTypeId){
	
		var c = 'loanType';
		var cv = readCookie(c);
	
		if (cv == 5){
			var loanTypeId = 'conv';
		}
		else if(cv == 3.5){
			var loanTypeId = 'fha';
		}
		else if (cv == 0){
			var loanTypeId = 'va';
		}else{
			var loanTypeId = 'conv';
		}
	
		return loanTypeId;
	}

	function getDownPayment(){
		
		var loan = salePrice();
		var loanType = getLoanType();
		var addDown = getAddDown();
		
		var downPayment = loan * (loanType / 100);
		var downPayment = downPayment + parseInt(addDown);
		var downPayment = parseInt(downPayment);
	
		return 	downPayment;
	}

	function getAddDown(addDown){
		var c = 'update-down';
		var cv = readCookie(c);
	
		if (cv != null){
			var addDown = cv;
		}else{
			createCookie(c,0,500000);
			var addDown = readCookie(c);
		}
		var addDown = parseInt(addDown.replace(/\D/g,''));
		return addDown;
	}

	function getRate(rate){
	
		var c = 'budget-rate';
		var cv = readCookie(c);
	
		if (cv != null){
			var rate = cv;
		}else{
			createCookie(c,5.1,500000);
			var rate = readCookie(c);
		}
		var rate = round(rate)
		return rate;	
	}

	function getTerm(term){
	
		var c = 'budget-term';
		var cv = readCookie(c);
	
		if (cv != null){
			var term = cv;
		}else{
			createCookie(c,30,500000);
			var term = readCookie(c);
		}
		var term = parseInt(term.replace(/\D/g,''));
		return term;
	}
	
	function numberWithCommas(x) {
    	return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
	}
	
	// rounds a number to two decimal places
	function round(x) {
  		return Math.round(x*100)/100;
	}

/* Cookies */

	function updateFormCookie(){
		//There is a better way to find checked???
		if(document.getElementById('conv').checked){
			var v = 5;
		}else if(document.getElementById('fha').checked){
			var v = 3.5;
		}else if(document.getElementById('va').checked){
			var v = 0;
		}
		createCookie('loanType',v,500000);
		
		//Run though setting or updateing cookies
		var term = document.getElementById('budget-term');
		createCookie(term.id, term.value,500000);
		var rate = document.getElementById('budget-rate');
		createCookie(rate.id, rate.value,500000);
		var down = document.getElementById('update-down');
		createCookie(down.id, down.value,500000);
		var ins = document.getElementById('update-ins');
		createCookie(ins.id, ins.value,500000);
	}

	runCalc();
});

