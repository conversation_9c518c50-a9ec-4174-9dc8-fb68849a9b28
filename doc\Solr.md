# Solr

This page provides basic details on how [Apache Solr](https://wiki.apache.org/solr) is used on this project.

[TOC]

## Overview

Solr is used for the [Property Search](PropertySearch.md) functionality of the IDX server.  

For each MLS + class (i.e. ARMLS Residential) that is to be searchable, the data from the MySQL database table is imported into a [Solr core](https://cwiki.apache.org/confluence/display/solr/Solr+Cores+and+solr.xml) and indexed.  

All of the fields for a property are stored in the Solr index, so when a search is performed, the resulting JSON contains all the data that is needed, and the MySQL property table does need to be queried at all.

## Directories & files

The important directories & files are as follows:

* `solr/`
    * `configs/` - XML config files that are copied directly into the `conf` file for each core
    * `dist/` - directory where the Solr distribution should be installed - see [Dev Setup](DevSetup.md)
        * `server`
            * `solr`
                * `{mls}_{class}` - Solr core directory.  The directory directory is created & `conf/` filled by the `tools/gen-solr-configs` script
    * `lib` - IcedCoffeeScript classes for generating the Solr `schema.xml` and `solr-data-import.xml` files
    * `templates`
        * `field-types.xml` - the field types used for each Solr core.  This is inserted into the `schema.xml` file created by `tools/gen-solr-configs`
    * `tools`
        * `gen-solr-configs` - IcedCoffeeScript script that creates the Solr core directories & config files

* `tools/`
    * `solr-sync` - utility script for performing a differential import into Solr from MySQL, optimizing Solr, etc.

## Setup

Follow the steps in the [IDX Setup](IDXSetup.md) guide for details on setting up Solr for development.

## Configuration

The `tools/gen-solr-configs` takes care of all the dynamic configuration.  It does the following:

* reads in the MLSes from the YAML configuration
* creates the Solr core for each MLS inside `dist/server/solr/`
* copies the files from the `configs/` directory into the `conf` directory for each core
* reads the MySQL property table definition, RETS fields & lookup details
* generates the following files for each core:
    * `schema.xml`
    * `solr-data-import.xml`

### schema.xml

The `schema.xml` file defines the [field names & types](https://cwiki.apache.org/confluence/display/solr/Overview+of+Documents%2C+Fields%2C+and+Schema+Design) for the Solr core.

### solr-data-import.xml

The `solr-data-import.xml` file tells Solr how to use the [Data Import Handler](https://cwiki.apache.org/confluence/display/solr/Uploading+Structured+Data+Store+Data+with+the+Data+Import+Handler) to import data directly from MySQL into Solr.

## Starting & Stopping

To start/stop/restart Solr:

    dist/bin/solr {start,stop,restart}

## Inserting Data

Solr provides a [DataImportHandler](https://wiki.apache.org/solr/DataImportHandler) for feeding data into Solr from another source, such as MySQL.

## Spatial Search

There are currently two uses cases for [Spatial Search](https://cwiki.apache.org/confluence/display/solr/Spatial+Search) with Solr for the IDX search:

* Map polygon searches
* "Nearby Radius" searches

### Polygon Searches

The polygon search uses a `IsWithin` [Well-Known Text](https://en.wikipedia.org/wiki/Well-known_text) query on the `location` field.

See the `server/lib/PropSearch.iced` file for details.

### Nearby Radius searches

The nearby radius search uses the `pt`, `d`, `sfield`, and `fq` parameters, as well as the Solr `geofilt()` function.

See the `server/lib/PropSearch.iced` file for details.

## Logging

* TODO: log4j file size

## Updating the Solr index

There are two ways the Solr index is updated in production:

* via the PHP API inside of `RETSHelper.php`, during the [RETS Sync](RETS-Sync.md)
    * ARMLS - residential, rentals, land
* Running a `full-import` via the `solr-import` target in the main `Makefile`
    * all other MLSes

The tables MLSes other than ARMLS are small enough, that a full data import is quick enough that it doesn't take a lot of CPU, or affect query performance.  These MLSes are also synced by [RetsCloud](RetsCloud.md) and are not using the `RETSHelper.php` code.

In addition, a differential sync can be run using the `tools/solr-sync` script.

    tools/solr-sync --mls armls --mls_class res --delete

Run `tools/solr-sync -h` for more options.

## Query Statistics

Solr includes a [Stats Component](https://cwiki.apache.org/confluence/display/solr/The+Stats+Component) that lets you get back the **mean**, **minimum**, **maxium**, **stdev**, etc. for a list of fields, for the full set of query results.

This is used provide [Aggregate Statistics for IDX searches](https://taskbump.com/case-details/55b15b4dd0b9383821000004/1326-aggregate-statistics-for-idx-searches).

## Field Types

This section provides some useful information on the field types used in Solr.

See the `templates/field-types.xml` file for technical details.

* [Field Types Included with Solr - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Field+Types+Included+with+Solr)
* [Field Type Definitions and Properties - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Field+Type+Definitions+and+Properties)

### String

A custom `string` type is used for fields that will be matched exactly by value, but should be matched using a case-insensitive comparison.

### Multilookup

A custom `multilookup` field type is used for fields that may have more than one lookup value.  When the data is received from RETS, and stored in MySQL, the values will be comma-separated:

Example value for "Kitchen Features":

    Dishwasher,Pantry

The `multilookup` field type splits the field by `,` when it is indexed, so it can be searched properly.

### Latitude / Longitude

We use special spatial types for these fields.

See: [Spatial Search - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Spatial+Search)

### Tries

We use the Solr `tdate`, `tint`, `tdouble`, etc. fields for numercial & data values, to allow faster range queries.

* [Trie - Wikipedia, the free encyclopedia](https://en.wikipedia.org/wiki/Trie)

## Disk Usage

The Solr cores currently (as of 8/24/2015) take about about **1.3 GB** of disk space in production.

```
ubuntu@ip-10-255-64-32:~/prod/solr/dist/server/solr [master] $ du -shc */
27M	armls_land/
304M	armls_rentals/
683M	armls_res/
75M	glvarnv_res/
36M	paaraz_land/
13M	paaraz_rentals/
18M	paaraz_res/
304K	sdcrca_res/
264K	sndmls_res/
17M	tarmlsaz_land/
7.2M	tarmlsaz_rentals/
58M	tarmlsaz_res/
296K	trendmls_res/
1.3G	total
```

## npm modules

* [lbdremy/solr-node-client](https://github.com/lbdremy/solr-node-client) - Solr client library
* [oozcitak/xmlbuilder-js](https://github.com/oozcitak/xmlbuilder-js) - for generating the Solr configs
* [jprichardson/node-fs-extra](https://github.com/jprichardson/node-fs-extra#copy) - also for Solr configs
* [felixge/node-mysql](https://github.com/felixge/node-mysql/) - for accessing MySQL

## Manual Installation

The [IDX Setup](IDXSetup.md) guide provides details on setting up Solr using a pre-packaged archive that is stored on Amazon S3.  

If you wish to set up Solr from scratch, then follow the steps here.

Download:

1. Download an archive from the mirrors linked to from the [Solr Downloads](http://lucene.apache.org/solr/downloads.html) page
2. Download the [JTS Topology Suite](http://sourceforge.net/projects/jts-topo-suite/)
3. Download the [JDBC Connector for MySQL](http://dev.mysql.com/downloads/connector/j/)

Configure:

1.  Unpack the Solr archive
2.  Configure JTS:
    1. locate the `solr.war`
    2. unzip it
    3. find the `jts-1.13.jar` file inside the `lib` directory of unzipped JTS download
    4. copy the `jts-1.13.jar` to the `WEB-INF/lib` folder inside the unzipped Solr archive
    5. zip it again as solr.war
3.  Install the JDBC connector
    1. create `lib` directory under `contrib/dataimporthandler` in the folder where Solr was installed
	2. copy `mysql-connector-java-\*.jar` files from the downloaded JDBC archive to the `contrib/dataimporthandler/lib` directory 

## Links

Wikipedia

* [Well-known text - Wikipedia, the free encyclopedia](https://en.wikipedia.org/wiki/Well-known_text)

Query Syntax

* [Apache Lucene - Query Parser Syntax](https://lucene.apache.org/core/2_9_4/queryparsersyntax.html#Range Searches)

Documentation

* [Spatial Search - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Spatial+Search)
* [Uploading Structured Data Store Data with the Data Import Handler - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Uploading+Structured+Data+Store+Data+with+the+Data+Import+Handler)
* [Pagination of Results - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Pagination+of+Results)
* [Common Query Parameters - Apache Solr Reference Guide - Apache Software Foundation](https://cwiki.apache.org/confluence/display/solr/Common+Query+Parameters)

Misc

* [Solr geolocation searches using WKT – latitude or longitude first?](http://www.flax.co.uk/blog/2014/09/12/solr-geolocation-searches-using-wkt-latitude-or-longitude-first/)

## TaskBump cases

See the [IDX Search Cases](IDXSearchCases.md) for links to related TaskBump cases.
