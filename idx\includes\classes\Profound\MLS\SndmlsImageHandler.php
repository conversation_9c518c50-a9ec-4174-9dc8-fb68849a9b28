<?php

namespace Profound\MLS;

class SndmlsImageHandler extends ImageHandler {
	static $mlsname = "sndmls";

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$img_array = array();
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Preferred` DESC, `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		foreach ($results as $key => $result) {
			if ($this->getPropAPI()->getVersion() == "v1.00") {
				$img_array[$key]['img_url'] = $result["Location"];
			} else {
				$img_array[$key]['normal_url'] = $result['Location'];
				$img_array[$key]['thumbnail_url'] = null;
				$img_array[$key]['highres_url'] = null;
				$img_array[$key]['description'] = $result['Content-Description'];
			}
		}
		return $img_array;
	}
}
