/**
 * @summary Polygons Map.
 *
 * @since 1.1.2
 */

jQuery( document ).ready( function( $ ) {

	function initMap() {

		var map;
		var polygons = new Array();

		var styles = [
			{ featureType: "all",
				stylers: [
					{ saturation: 0 }
				]
			},{ featureType: "road.arterial",
				elementType: "geometry",
				stylers: [
					{ hue: "#ff9009" },
					{ saturation: 20 }
				]
			}
		];


		var mapOptions = {
			zoom: force_zoom || polygons_map.geo.zoom,
			center: force_center || { lat: polygons_map.geo.center_lat, lng: polygons_map.geo.center_lng },
			styles: styles,
			...window.iFoundGlobal.sharedGoogleMapBaseOptions,
		};

		map = new google.maps.Map(document.getElementById( 'polygons-map' ), mapOptions );

		google.maps.event.addListenerOnce(map, 'bounds_changed', function(event) {
        	if (force_zoom){
            	map.setZoom(force_zoom);
        	}
        	if (force_center){
        	 	map.setCenter( force_center );
        	}
        });

		setPolygons();

		function fitPolygons() {

			var bounds = new google.maps.LatLngBounds();

			$.each( polygons, function() {
				var paths = this.getPaths();
				paths.forEach(function(path){
               		var ar = path.getArray();
               		for(var i=0, l = ar.length; i <l; i++){
                  		bounds.extend(ar[i]);
                	}
            	});
			});

			map.fitBounds(bounds);

		}

		function setPolygons(){

			var colors = polygons_map.colors;
			var i = 0;
			var l = colors.length;

			$.each( polygons_data.polygons, function() {

				if( i > l ) i = 0;
				var color = colors[i];
				i++;

				var polygon = new google.maps.Polygon({
					paths: this.paths,
					strokeColor: color,
					strokeOpacity: 0.5,
					strokeWeight: 0.5,
					fillColor: color,
					fillOpacity: 0.5
				});

				var slug = this.slug;
				var title = this.title;
				var href = polygons_map.site_url + slug;

				if(screen.width > 800){
					polygon.addListener('click', function() {
						window.location.href = href;
					});
				}

				google.maps.event.addListener(polygon,"mouseover",function(){
					this.setOptions({strokeColor: color, fillColor: color, fillOpacity: 0.3});
				});

				google.maps.event.addListener(polygon,"mouseout",function(){
					this.setOptions({strokeColor: color, fillColor: color, fillOpacity: 0.5});
				});

				polygon.addListener('mouseover', function() {
					if(screen.width > 800){
						$('.polygon-title').html( '<b>' + title + '</b>' );
					} else {
						var html = '<a class="map-label-iw" href="' + href + '">Search ' + title + '</a>';
						$('.polygon-title').html( html );
					}
				});

				if(screen.width > 800 && this.paths.length > 2){
					polygon.addListener('mouseout', function() {
						$('.polygon-title').html( 'Search Areas' );
					});
					var labelW = new google.maps.InfoWindow();
					labelW.setContent('<a class="map-label-iw" href="' + href + '">' + this.title + '</a>');
					labelW.setPosition(getLabelPosition(this.paths, polygon));
					labelW.open(map);
				}

				polygon.setMap(map);

				polygons.push(polygon);

			});

			fitPolygons();

		}

		function getLabelPosition(paths, polygon) {
			var coord = getCentralCoord(paths);
			while(!isWithinPoly(coord, paths)) {
				coord = getWithinPoly(paths);
			}

			return {
				lat: coord.lat,
				lng: coord.lng
			}
		}

		// Ray-casting algorithm implementation
		// Find if the point lies withing the polygon
		function isWithinPoly(coord, paths) {
			var count = 0;
			for(var i = 0; i < paths.length; i++) {
				var v1 = paths[i];
				var v2 = paths[(i+1) % paths.length];
				if(westof(v1,v2,coord))
					++count;
			}
			return count %2;

			function westof(v1, v2, coord) {
				if(v1.lat <= v2.lat) {
					if(coord.lat <= v1.lat || coord.lat > v2.lat || coord.lng >= v1.lng && coord.lng >= v2.lng)
						return false;
					else if(coord.lng < v1.lng && coord.lng < v2.lng)
						return true;
					else
						return (coord.lat - v1.lat) / (coord.lng - v1.lng) > (v2.lat - v1.lat) / (v2.lng - v1.lng);
				} else {
					return westof(v2, v1, coord);
				}
			}
		}

		function getCentralCoord(paths) {
			var highestLat = paths[0].lat;
			var lowestLat = paths[0].lat;
			var highestLng = paths[1].lng;
			var lowestLng = paths[1].lng;
			for(var i = 1; i < paths.length; i++) {
				if(highestLat < paths[i].lat)
					highestLat = paths[i].lat;
				else if(lowestLat > paths[i].lat)
					lowestLat = paths[i].lat;

				if(highestLng < paths[i].lng)
					highestLng = paths[i].lng;
				else if(lowestLng > paths[i].lng)
					lowestLng = paths[i].lng;
			}

			var lat = lowestLat + (highestLat - lowestLat) / 2;
			var lng = lowestLng + (highestLng - lowestLng) / 2;
			return {
				lat: lat,
				lng: lng,
			}
		}

		// Generate the cooridnate within the polygon square area
		function getWithinPoly(paths) {
			var lat = 0;
			var lng = 0;
			var hLat = paths[0].lat;
			var lLat = paths[0].lat;
			var hLng = paths[0].lng;
			var lLng = paths[0].lng;

			for(var i = 1; i < paths.length; i++) {
				if(hLat < paths[i].lat)
					hLat = paths[i].lat;
				else if(lLat > paths[i].lat)
					lLat = paths[i].lat;

				if(hLng < paths[i].lng)
					hLng = paths[i].lng;
				else if(lLng > paths[i].lng)
					lLng = paths[i].lng;
			}

			var spaths = [
				{ lat: lLat, lng: lLng },
				{ lat: hLat, lng: lLng },
				{ lat: hLat, lng: hLng },
				{ lat: lLat, lng: hLng }
			]

			var rand = null;
			while(1) {
				rand = {
					lat: lLat + ((hLat - lLat) * Math.random()),
					lng: lLng + ((hLng - lLng) * Math.random()),
				}
				// FIXME
				if(isWithinPoly(rand, spaths))
					break;
			}

			return rand;
		}

	}
	window.iFoundGlobal.loadGoogleMaps().then(function() {
		initMap();
	});
});
