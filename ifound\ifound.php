<?php
/*
Plugin Name: iFound
Plugin URI:  https://ifoundagent.com
Description: The Best Real Estate IDX
Version:     5.49.0
Author:      iFoundAgent
Author URI:  https://ifoundagent.com
License:     GPL2
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ifound
*/

defined( 'ABSPATH' ) or die( 'You do not have access!' );

// https://www.php.net/manual/en/function.str-starts-with.php#125913
if (!function_exists('str_starts_with')) {
	function str_starts_with($haystack, $needle) {
		return (string)$needle !== '' && strncmp($haystack, $needle, strlen($needle)) === 0;
	}
}

// As we upgrade PHP from 7.3 to 8.1, what once was a notice has been changed to a warning, this warning about
// undefined array indexes. I prefer to use syntax like
//   if ($_GET['mykey'])
// rather than alternatives like
//   1. if (isset($_GET['mykey']))
//   2. if ($_GET['mykey'] ?? null)
// I could perhaps do the null coalescing operator if I were starting a codebase from scratch, but we have too many
// uses to change now.
// Oh, and same with using $myvar->some_property.
set_error_handler(function($errno, $errstr, $errfile, $errline) {
	if ($errno === 2) {
		// Ignore these errors by returning not false.
		if (str_starts_with($errstr, 'Undefined array key')) {
			return true;
		} else if (str_starts_with($errstr, 'Undefined property')) {
			return true;
		}
	}
	// The current error being handled will be handled normally by returning false.
	return false;
});

require __DIR__ . '/vendor/autoload.php';

// Keep track of a unique ID, such that when we record activity later, we can determine if the activity is from the same
// page load sequence, or if it's a separate web request.
// I'm using a range of 1000 just because I don't expect to be collecting lots of data for this and that should be fine.
// See: https://ifoundagent.teamwork.com/#/tasks/36600725
$IFOUND_PAGE_LOAD_ID = rand(1, 1000);

require_once('traits/UtilTrait.php');

/** Let's Define the plugin version.*/
if ( ! defined( 'iFOUND_PLUGIN_VERSION' ) ){
	define( 'iFOUND_PLUGIN_VERSION', '5.49.0' );
}

/**
 * iFound Class
 *
 * @since 1.0.0
 */

class iFound {
	use NewHooklessTrait;
	use UtilTrait;

	// We sometimes use a context with things like _x(). I don't think the person who did that that really understood
	// or used it correctly. So it's just confusing and a waste of brain cycles. Let's replace with this everywhere.
	public static $default_context = 'ifound_default_context';

	private $config = null;

	/**
	 * init iFound class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('wp', [$this, 'wp_hook']);
			add_filter('ifound_config', array($this, 'get_config'));
			add_action('init', array($this, 'check_version'));
			add_action('wp_footer', array($this, 'ifound_footer'));
			add_action('init', array($this, 'push_website_update'));
			add_action('ifound_push_website_update', array($this, 'push_website_update'), 10, 1);
			add_filter('ifound_sanitize', array($this, 'sanitize'));
			add_filter('ifound_obj', array($this, 'obj'));
			add_filter('ifound_current_time', array($this, 'current_time'));
			add_action('wp_footer', array($this, 'while_we_wait'));
			add_action('admin_footer', array($this, 'while_we_wait'));
			add_filter('user_has_cap', [$this, 'user_has_cap'], 10, 4);

			new iFoundUpdateHelper(['enable_hooks' => true]);
		}
	}

	// If the content contains shortcode, we assume that it's dynamic content that comes from the IDX server and
	// shouldn't be cached. This is a simplistic approach at first for the sake of proof-of-concept with our web host.
	// We need to set the header based on the post content, so we can't use the wp_headers filter or the send_headers
	// action hook because the "queried object" is not yet known. The wp hook is the first place where we can know the
	// "queried object" (and get the post content), but still early enough to send headers.
	// Useful link: https://developer.mozilla.org/en-US/docs/Web/HTTP/Caching
	public function wp_hook() {
		$object = get_queried_object();
		if ($object && get_class($object) === WP_Post::class) {
			$post = $object;
			$content = $post->post_content;
			if (strpos($content, '[ifound') !== false) {
				header('Cache-Control: no-cache');
			}
		}
	}

	private function load_config() {
		$armls_import_mls_contacts_config = [
			'client_id' => 'yjgdm1dnm5uv0gkl6uvkgdu6',
			'authorization_endpoint' => 'https://sparkplatform.com/openid/authorize',
			'redirect_endpoint' => 'https://api.ifoundagent.com/spark/callback',
			'token_endpoint' => 'https://api.ifoundagent.com/spark/token',
			'refresh_token_endpoint' => 'https://api.ifoundagent.com/spark/refresh_token',
			'contacts_endpoint' => 'https://replication.sparkapi.com/v1/contacts',
			'revoke_endpoint' => 'https://sparkplatform.com/openid/revoke',
		];
		$defaults = [
			'php_env'                              => 'production',
			'admin_origin'                         => 'https://ifoundadmin.com',
			'build_admin_origin'                   => 'https://build.ifoundadmin.com',
			'facebook_app_id'                      => '2752820648267381',
			'google_client_id'                     => '805270820167-56cj1p6j33gsm0nq59d7vo0metgof6pl.apps.googleusercontent.com',
			'mls_associations_origin'              => 'https://mls-associations.ifoundadmin.com/wp-json/ifound-admin/1.0.0/mls-association/',
			'api_base_url'                         => 'https://api.profoundidx.com',
			'idx_url'                              => 'https://get.profoundidx.com',
			// Comma separated
			'reso_web_api_mlss'                    => 'recolorado,recolorado_mlg,crmls,realtracs,armls_spark,naar_spark',
			'record_user_agent_for_domains'        => '',
			// Comma separated
			'monetization_domains'                 => 'tempearizonahouses.com',
			// Comma separated
			'social_login_sites'                   => 'tempearizonahouses.com,scottggroup.com,scottsdale.com',

			// Here are keys whose values we don't want in git. I'm listing them to document that they exist.
			// This key is the IFA key, distinct from the key that the agent can configure in the admin.
			'ifa_google_maps_api_key'              => null,
			'wise_agent_auth_token'                => null,

			// Allowed features is a special case. If it is specified, it will override what is in the database. So we
			// don't have a default. I'm putting it here, commented out, because this space serves as a list of all
			// possible config values.
			// 'allowed_features'        => '',

			'mls_contacts' => [
				'mls' => [
					'armls'       => $armls_import_mls_contacts_config,
					'armls_spark' => $armls_import_mls_contacts_config,
				],
			],
		];

		$config = [];
		$inifile = plugin_dir_path( __FILE__ ) . 'config.ini';
		if( file_exists( $inifile ) ) {
			$config = parse_ini_file( $inifile );
		}

		$this->config = wp_parse_args($config, $defaults);
	}

	public function get_config($config = []) {
	    if (!$this->config) {
	        $this->load_config();
		}
		$temp = wp_parse_args($config, $this->config);
		return $temp;
	}

	public function get_config_array_val($name) {
		$config_val = $this->get_config()[$name];
		$config_val_as_array = $this->util()->split_on_comma($config_val);
		return $config_val_as_array;
	}

	public function does_config_array_val_include($name, $value) {
		$config_val_as_array = $this->get_config_array_val($name);
		return in_array($value, $config_val_as_array);
	}

	public function is_php_env_production() {
		$config = $this->get_config();
		return $config['php_env'] === 'production';
	}

	/**
	 * iFound Footer
	 *
	 * MLS disclosure and Agent login.
	 *
	 * @since 1.0.0
	 *
	 * @param object $results The ebtire results object.
	 */

	public function ifound_footer() { ?>

		<div class="ifound_required_footer">

			<div class="ifound-wrap"><?

				do_action( 'ifound_required_footer' );
				do_action( 'ifound_login_footer' ); ?>

			</div>

		</div><?

	}

	/**
	 * Current URL
	 *
	 * @since 2.4.9
	 *
	 * @return string $current_url The full URL of the current page.
	 */

	public static function current_url() {
		$protocol = 'http' . ((isset( $_SERVER['HTTPS'] ) && $_SERVER['HTTPS']) ? 's' : '');
		return $protocol . '://' . "{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
	}

	/**
	 * Current Time
	 *
	 * This id the current time for this blog.
	 *
	 * @since 1.2.31
	 *
	 * @param  int $time         The imt value of time. Defaults php time().
	 * @return int $current_time The time considering the gmt offest for this blog.
	 */

	public function current_time( $time = false ) {
		$time = $time ? $time : time();
		return $time + $this->gmt();
	}

	/**
	 * GMT
	 *
	 * @since 2.5.3
	 *
	 * @return int $gmt The GMT offset.
	 */

	public function gmt() {
		return get_option( 'gmt_offset' ) * 3600;
	}

	/**
	 * Get Meta By ID
	 *
	 * @since 1.0.0
	 *
	 * @param  int    $meta_id The database ID of the meta.
	 * @return object $meta An object of meta.
	 */

	public function get_meta_by_id( $meta_id ) {

		global $wpdb;
  		$meta = $wpdb->get_var(
			$wpdb->prepare(
				"
				SELECT meta_value
				FROM $wpdb->postmeta
				WHERE meta_id = %d
				",
				intval( $meta_id )
			)
		);
  		return unserialize( $meta );

	}

	public function get_post_id_from_meta_id($meta_id) {
		global $wpdb;
		$post_id = $wpdb->get_var(
			$wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_id = %d", intval($meta_id))
		);
		return $post_id;
	}

	/**
	 * Most Recent Meta
	 *
	 * @since 1.0.0
	 *
	 * @param  int    $post_id The database ID of the post.
	 * @param  string $key     The meta key.
	 * @param  int    $limit   The number of rows to query.
	 * @return array  $meta    An array of meta data.
	 */

	public function most_recent_meta( $post_id, $key, $limit = 1 ) {

		global $wpdb;

		$meta_array = $wpdb->get_results(
			"
			SELECT *
			FROM " . $wpdb->postmeta . "
			WHERE post_id = " . $post_id . "
			AND meta_key LIKE '" . esc_sql( $key ) . "'
			ORDER BY meta_id DESC
			LIMIT " . $limit . "
			"
		);

		if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

			return $meta_array;

		}

		return false;

	}

	/**
	 * Update Meta By ID
	 *
	 * @since 1.0.0
	 *
	 * @param int    $meta_id  The database ID of the meta.
	 * @param object $new_meta The meta to be saved in the database.
	 */

	public function update_meta_by_id( $meta_id, $new_meta ) {

		global $wpdb;

		$wpdb->update(
			$wpdb->postmeta,
			array( 'meta_value' => serialize( $new_meta ) ),
			array( 'meta_id' => $meta_id ),
			array( '%s'),
			array( '%d' )
		);

	}

	public function get_meta( $object_id, $meta_key = '', $single = false ) {

 		global $wpdb;

    	$object_id = absint( $object_id );

    	if ( ! $object_id ) {
        	return false;
    	}

    	if ( ! isset( $this->table ) ) {
        	return false;
    	}

    	$table = $wpdb->prefix . $this->table;

    	$results = $wpdb->get_results( $wpdb->prepare( "SELECT * FROM $table WHERE meta_key = %s AND object_id = %d", $meta_key, $object_id ) );

    	if ( count( $results ) ) {

    		if ( $single ) {

    			$result = $results[0];

    			$value = maybe_unserialize( $result->meta_value );
    			return $value[0];

    		} else {

    			foreach ( $results as $key => $value ) {

    				$results[$key]->meta_value = maybe_unserialize( $value->meta_value );

    			}

    			return $results;

    		}

    	}

    	if ( $single )
        	return '';
    	else
        	return array();

	}

	public function get_meta_single( $meta_id ) {

 		global $wpdb;

    	$meta_id = absint( $meta_id );

    	if ( ! $meta_id ) {
        	return false;
    	}

    	if ( ! isset( $this->table ) ) {
        	return false;
    	}

    	$table = $wpdb->prefix . $this->table;

    	$results = $wpdb->get_results( $wpdb->prepare( "SELECT * FROM $table WHERE meta_id = %d", $meta_id ) );

    	if ( count( $results ) ) {

    		$result = $results[0];

    		return maybe_unserialize( $result->meta_value );

    	}

    	return false;

	}

	public function add_meta( $object_id, $meta_key, $meta_value ) {

    	global $wpdb;

    	$object_id = absint( $object_id );

    	if ( ! $object_id ) {
        	return false;
    	}

    	if ( ! isset( $this->table ) ) {
        	return false;
    	}

    	$table = $wpdb->prefix . $this->table;

    	$meta_value = wp_unslash( $meta_value );
    	$meta_value = $this->sanitize( $meta_value );
    	$meta_value = maybe_serialize( $meta_value );

        $result = $wpdb->insert( $table, array(
        	'object_id' 	=> $object_id,
        	'meta_key' 		=> $meta_key,
        	'meta_value'	=> $meta_value
    	) );

    	if ( ! $result )
        	return false;

    	return (int) $wpdb->insert_id;

	}

	public function update_meta( $meta_id, $meta_key, $meta_value ) {

    	global $wpdb;

    	$meta_id = absint( $meta_id );

    	if ( ! $meta_id ) {
        	return false;
    	}

    	if ( ! isset( $this->table ) ) {
        	return false;
    	}

    	$table = $wpdb->prefix . $this->table;

    	$meta_value = wp_unslash( $meta_value );
    	$meta_value = $this->sanitize( $meta_value );
    	$meta_value = maybe_serialize( $meta_value );

    	$data  = compact( 'meta_value' );
    	$where = array( 'meta_id' => $meta_id );

    	$result = $wpdb->update( $table, $data, $where );

    	if ( ! $result )
        	return false;

    	return true;

	}

	public function delete_meta( $meta_id ) {

    	global $wpdb;

    	$meta_id = absint( $meta_id );

    	if ( ! $meta_id ) {
        	return false;
    	}

    	if ( ! isset( $this->table ) ) {
        	return false;
    	}

    	$table = $wpdb->prefix . $this->table;

        $query = "DELETE FROM $table WHERE meta_id = $meta_id;";

    	$count = $wpdb->query( $query );

    	if ( ! $count )
        	return false;

    	return true;

	}

	/**
	 * Is Admin
	 *
	 * Are we in the Admin or doing Ajax?
	 *
	 * @since 2.4.10
	 * @return bool $is_admin True if admin or ajax. False if not.
	 */

	public function is_admin() {
		return ( is_admin() || defined( 'iFOUND_ADMIN_AJAX' ) ) ? true : false;
	}


	/**
	 * Obj
	 *
	 * Convert a multi demisional array to an object.
	 *
	 * @since 1.0.0
	 *
	 * @param  array  $input An array of data.
	 * @return object $obj   The input as an object.
	 */

	public function obj( $input ) {
		return json_decode( json_encode( $input ) );
	}

	/**
	 * Sanitize
	 *
	 * Clean up all input based on type.
	 *
	 * @uses wp_kses_post() This will sanitize to the allowed HTML in a post.
	 *
	 * @since 3.3.0
	 *
	 * @param  mixed $input  The data to be cleaned.
	 * @return mixed $output The cleaned input.
	 */

	public function sanitize( $input ) {

		// Be advised. This switch statement doesn't really work like its author thinks it does. E.g. an $input value of
		// an empty string will match the case of is_float($input) because '' == is_float('').
		// I am scared to change it because I'm not sure if there are dependencies on the bugs.
		switch ( $input ) {

    		case empty( $input ):
        		return $input;

    		case is_float( $input ):
        		return floatval( $input );

        	case is_int( $input ):
        		return intval( $input );

    		case is_numeric( $input ):
        		$output = $input + 0;
        		return is_float( $output ) ? floatval( $output ) : intval( $output );

        	case is_bool( $input ):
        		return boolval( $input );

    		case is_array( $input ):
        		foreach( $input as $var => $val )
					$output[$var] = self::sanitize( $val );
        		return $output;

        	case is_object( $input ):
        		$output = new stdClass();
        		foreach( $input as $var => $val )
					$output->$var = self::sanitize( $val );
        		return $output;

        	case is_email( $input ):
        		$input = trim( $input );
        		return sanitize_email( $input );

        	case strpos( $input, '<' ) === false && strpos( $input, '$' ) === false:
        		$input = trim( $input );
        		return sanitize_text_field( $input );

        	default:
				$input = trim( $input );

        		return wp_kses_post( $input );

		}

	}

	/**
	 * Activation.
	 *
	 * On plugin iFOUND_PLUGIN_VERSION update we check for updates.
	 *
	 * @since 1.0.0
	 * @since 2.4.0 Move updates to iFound_update::run_updates()
	 */

	public function activation() {
		iFoundStartup::init();
		flush_rewrite_rules();
	}

	/**
	 * Check version.
	 *
	 * Check to see if current version is stored in database.
	 *
	 * @since 1.0.0
	 * @since 2.4.0 Init the update functions.
	 */

	public function check_version() {
		if (wp_doing_cron()) {
			return;
		}
		if (isset($_POST['action']) && $_POST['action'] === 'heartbeat') {
			return;
		}

		$currently_installed_version = get_option( 'ifound_current_version' );
		if (version_compare($currently_installed_version, iFOUND_PLUGIN_VERSION, '<')) {
    		$this->activation();
			/** Update current plugin version, only if startup is complete. */
			if( get_option( 'ifound_forms_installed' ) ) {
				// Did we just activate for the first time?
				if (defined('iFOUND_STARTUP')) {
					update_option( 'ifound_current_version', iFOUND_PLUGIN_VERSION );
				} else {
					iFoundUpdate::run();
				}
			}
		}
	}

	/**
	 * Push Website Update
	 *
	 * Update the iFound admin with current website info.
	 *
	 * @since 2.9.0
	 */

	public function push_website_update( $update = false ) {

		if( $update || isset( $_GET['getupdate'] ) ) {

			$api 		= get_option( 'ifound_api_settings' );
			$blog_id 	= is_multisite() ? get_current_blog_id() : 'Stand Alone';
			$dev_url	= is_multisite() ? 'http://' . get_blog_details( $blog_id )->domain : site_url();

			$body = array(
			    'body'	=> array(
				  	'dev_url'				=> $dev_url,
				  	'domain' 				=> site_url(),
				  	'blog_id'				=> $blog_id,
				  	'version'				=> iFOUND_PLUGIN_VERSION,
					'stylesheet' 			=> get_option( 'stylesheet' )
				)
			);

			$headers =  array(
				'blocking'				=> false,
				'headers' => array(
			    	'Accept' 			=> 'application/json',
					'Referer' 			=> site_url(),
					'Authentication'	=> $api['api_secret']
				)
			);

			$admin_origin = apply_filters('ifound_config', [])['admin_origin'];
			$response = wp_remote_post(
				join( '/', array(
					$admin_origin,
					'wp-json',
					'ifound-admin',
					'1.0.0',
					'update-website/'
				)),
				array_merge( $headers, $body )
			);

			if( isset( $_GET['redirect'] ) ) {

				$redirect = urldecode( $_GET['redirect'] );

				wp_redirect( $redirect );

			}

		}

	}

	/**
	 * While We Wait
	 *
	 * @since 1.0.0
	 */

	public function while_we_wait() { ?>

		<div class="while-we-wait pop-drop">
			<div class="while-we-wait pop-msg"><i class="fa fa-cog fa-spin fa-fw"></i> <? _e( 'Searching...', 'ifound' ); ?></div>
		</div><?

	}

	/**
	 * Support
	 *
	 * Allow our support team to view certain items for debugging.
	 *
	 * @since 3.0.0
	 *
	 * @return bool $support True if param is set. False otherwise.
	 */

	public function support() {
		return isset( $_GET['support'] ) ? true : false;
	}

	// To allow a staff member to have certain capabilities, but while potentially logged in as non-admins, we'll allow
	// them to set a cookie in their browser. There is a convenient but secret helper way to do this. To learn how, see:
	// https://ifoundagent.teamwork.com/#tasks/24305946?c=8689881
	public function user_has_cap($allcaps, $caps, $args, $user) {
		$allowed_caps = [
			iFoundAdmin::$manage_ifound_staff_settings_capability_name,
			iFoundHomeownerCampaign::$manage_bulk_campaigns_cap_name,
		];
		if (count($caps) > 0 && in_array($caps[0], $allowed_caps, true)) {
			// For our purposes, a super admin should have all privileges, always. However, for /wp-json routes, we don't
			// know the user's ID, and thus can't be sure if they are a super admin. I'm not sure why WP doesn't know
			// who the user is at this point (or for this /wp-json route). But there seems to be a way around it by
			// using the determine_current_user filter.
			// I assume there's a better way to do this. But I haven't seen it mentioned anywhere (like the WP docs).
			$user_id = get_current_user_id();
			if (!$user_id) {
				$user_id = apply_filters('determine_current_user', null);
			}
			if (is_super_admin($user_id)) {
				foreach ($allowed_caps as $allowed_cap) {
					$allcaps[$allowed_cap] = true;
				}
			} else {
				$cookie = $_COOKIE['ifound_caps'];
				if ($cookie) {
					$cookie_caps = $this->util()->split_on_comma($cookie);
					foreach ($cookie_caps as $cookie_cap) {
						$allcaps[$cookie_cap] = true;
					}
				}
			}
		}
		return $allcaps;
	}
}

$directories = array(
	'util',
	'idx',
	'cmc',
	'crm',
	'includes',
	'includes/startup',
	'vendor/aerial-sphere'
);

foreach( $directories as $directory ) {

	foreach ( glob( plugin_dir_path( __FILE__ ) . $directory . '/*.php' ) as $file ) {
	    require_once $file;
	}

}
add_action( 'plugins_loaded', array( 'iFoundFeatures', 'init' ), 1 );
