<?php

namespace Profound;

use \PHPUnit_Framework_TestCase;

class RETSHelperTest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testSanity () {
		$this->assertTrue(true);
	}

	// public function testGetCrossStreets () {
	// 	$file_path = __DIR__ . "/../../config.ini";
	// 	$mls = 'armls';
	// 	$_GET['mls'] = $mls;
	// 	$rh = new \RETSHelper($file_path);
	// 	// $rh->setupRETSConnection($rh->config[$mls]);
	// 	$rh->viewProperty();
	// }

	public function testViewProperty() {
		$file_path = __DIR__ . "/../../config.ini";
		$mls = 'armls';
		$_GET['mls'] = $mls;
//        $_GET['id'] = "4669444";
//        $_GET['id'] = "4812620";
//        $_GET['id'] = "4882867";

//        +----------+---------------------+
//| LIST_105 | LIST_87             |
//            +----------+---------------------+
//|  4888456 | 2013-03-14 00:38:35 |
//|  4904251 | 2013-03-14 00:38:28 |
//|  4904250 | 2013-03-14 00:36:55 |
//|  4904241 | 2013-03-14 00:36:39 |
//|  4888384 | 2013-03-14 00:36:16 |
//|  4904229 | 2013-03-14 00:35:43 |
//|  4904022 | 2013-03-14 00:34:29 |
//|  4875845 | 2013-03-14 00:32:08 |
//|  4900129 | 2013-03-14 00:31:30 |
//|  4828980 | 2013-03-14 00:30:12 |
//            +----------+---------------------+
//
//        $_GET['id'] = "4888456";
//        $_GET['id'] = "4904251";
//        $_GET['id'] = "4904250";
//        $_GET['id'] = "4904241";
//        $_GET['id'] = "4888384";
//        $_GET['id'] = "4904229";
//        $_GET['id'] = "4904022";
//        $_GET['id'] = "4875845";
//        $_GET['id'] = "4900129";
        $_GET['id'] = "4828980";
        $rh = new \RETSHelper($file_path);
		$rh->viewProperty();
	}

	public function testTrendViewProperty() {
		$file_path = __DIR__ . "/../../config.ini";
		$mls = 'trendmls';
		$_GET['mls'] = $mls;
        $_GET['id'] = "4828980";
        $rh = new \RETSHelper($file_path);
		$rh->viewProperty();
	}
}