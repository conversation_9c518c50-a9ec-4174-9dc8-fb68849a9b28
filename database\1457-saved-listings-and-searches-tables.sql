-- getprofo_wp1

CREATE TABLE IF NOT EXISTS wp_264_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_314_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_316_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_318_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_319_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_320_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_321_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_323_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_325_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_326_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_327_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_328_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_329_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_330_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_332_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_333_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_334_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_335_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_337_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_338_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_340_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_341_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_342_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_343_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_344_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_345_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_346_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_347_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_348_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_351_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_352_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_353_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_354_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_355_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_356_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_357_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_358_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_359_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_360_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_361_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_362_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_363_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_364_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_365_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_366_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_367_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_368_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_369_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_370_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_371_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_372_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_373_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_374_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_375_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_376_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_377_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_378_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_379_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_380_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_381_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE IF NOT EXISTS wp_314_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_316_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_318_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_319_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_320_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_321_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_323_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_325_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_326_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_327_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_328_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_329_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_330_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_332_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_333_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_334_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_335_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_337_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_338_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_340_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_341_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_342_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_343_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_344_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_345_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_346_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_347_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_348_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_351_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_352_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_353_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_354_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_355_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_356_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_357_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_358_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_359_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_360_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_361_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_362_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_363_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_364_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_365_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_366_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_367_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_368_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_369_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_370_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_371_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_372_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_373_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_374_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_375_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_376_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_377_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_378_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_379_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_380_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_381_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;

-- realdeal_wrdp1

CREATE TABLE IF NOT EXISTS wp_237_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE IF NOT EXISTS wp_237_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;


-- tamsms

CREATE TABLE IF NOT EXISTS wp_65_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_66_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_67_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_69_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_70_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_71_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_74_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_75_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_76_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_77_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_78_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_79_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_80_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_81_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_82_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_83_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_84_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_85_pfmls_saved_listings (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,mls_id VARCHAR(11) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_listing_per_user (user_id,mls_id)) ENGINE=InnoDB CHARSET UTF8;

CREATE TABLE IF NOT EXISTS wp_65_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_66_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_67_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_69_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_70_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_71_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_74_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_75_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_76_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_77_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_78_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_79_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_80_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_81_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_82_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_83_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_84_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
CREATE TABLE IF NOT EXISTS wp_85_pfmls_saved_searches (id SERIAL,user_id BIGINT(20) UNSIGNED NOT NULL,url VARCHAR(171) NOT NULL,search_name VARCHAR(1024) NOT NULL,created TIMESTAMP NOT NULL,UNIQUE KEY saved_search_per_user (user_id,url)) ENGINE=InnoDB CHARSET UTF8;
