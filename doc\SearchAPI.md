# Profound IDX API #

Profound provides an API to access listings data. The API is currently limited to read-only querying.

## Basics ##

You will need an API key to access the data. Your API key should be provided to you by email. When you construct a URL to fetch data, it should always include `apikey=<APIKEY>`, where `<APIKEY>` is replaced by your api key.

URL endpoint base: `http://api.profoundidx.com/q/search`

The API returns JSON.

Search *values* are case-insensitive. E.g. `city=scottsdale` works the same as `city=Scottsdale`. Search *keys* are case-sensitive, e.g. `CITY=scottsdale` will have undefined results. Always use lowercase.

## Listings Parameters ##

To be able to query for listings, you'll need knowledge of what you're looking for and its mapping name in the MLS RETS system. For example, for the ARMLS MLS, price is `LIST_22` and city is `LIST_39`. Let us know if you need help determining the search keys, but we recommend using the 'easy names' as explained below.

### Easy names ###

For convenience, we offer a list of 'easy names', that take care of the mapping for you. In such case, you can use the easy name without having to remember the mapping. For example, instead of using `LIST_39=scottsdale`, you could use `city=scottsdale`.

Here is a sample list of easy names:

* price
* city
* zip
* subdivision
* bedrooms
* bathrooms
* living_sqft
* mls_id

See the [Easy Names](EasyNames.md) page if you are constructing an API call manually, and wish to use these for your `words` query.

### Building a search query ###

After you've determined your search terms, you'll build a search query using the following pattern:

    http://api.profoundidx.com/q/search?words=<SEARCH PARAMETERS><API PARAMETERS>

Note the use of the term `words`. Search terms are separated with a semi-colon, percent encoded to `%3B`. Other parameters are separated by an ampersand (`&`) as in a normal GET call.

So, if you wanted to specify city and zip, you'd use

    http://api.profoundidx.com/q/search?words=city=scottsdale%3Bzip=85255&apikey=<YOUR API KEY>

### More query notes ###

You can use less-than, less-than-or-equal-to, greater-than, and greater-than-or-equal-to operators if you percent encode them. For example, for price greater than 999,999 dollars, use `price%3E999999`.

## API Parameters ##

All parameters are optional unless otherwise specified.

### apikey (required) ###

Don't forget your API key as explained in the **Basics** section.

### all ###

If you want more data per listing, add `&all=1` to your query. Without it, you'll get about 20 fields. With it, it's more like 50.

### img_count ###

By default, only one image is returned per listing. If you'd like to have more than one image, use `&img_count=N`, where `N` is the number of images you want. If you'd like all images, use the number 999.

### sorting ###

To sort, set `sort` equal to a field name, followed by a comma, followed by either "ASC" for ascending or "DESC" for descending. For example, to sort by ListDate descending, use `&sort=ListDate,DESC`.

### Paging ###

By default, 25 results are returned per page. To request a specific page, use `pp`, e.g. `pp=2`. If you'd like to change the results per page, set `max_results` to another number, e.g. `max_results=15` or `max_results=50`.

You can inspect the response's `paging`'s `parseable` object to see the total number of items available (`total_items`), the number of items per page (`items_per_page`), and the current page number (`current_page`).

### mls_class ###

By default it's assumed you want residential listings. You can specify another MLS class, such as rentals, with the `mls_class` parameter, e.g. `mls_class=rentals`. Possible options include `res` for residential, `rentals`, and `land`.

Note that not all MLS's are set up for each MLS class. Ask your account manager.

### Extended Data

There is the option of specifying `extended_data`, which allows for things like doing geo-spatial searches, which limits the search to a certain geographical region.

The `extended_data` parameter takes JSON. It looks like this:

```
{
  "map": {
    "polygons": {
      "list": [
        {
          "paths":"(33.**************, -112.**************)(33.**************, -112.**************)(33.**************, -111.**************)(33.**************, -111.*************)"
        }
      ]
    }
  }
}
```

You can supply multiple polygons. Each polygon will have a list of paths.

Note that this JSON must be URL encoded. So if you used the above, your final URL would be:

http://api.profoundidx.com/q/search?extended_data={%20%22map%22:%20{%20%22polygons%22:%20{%20%22list%22:%20[%20{%20%22paths%22:%22(33.**************,%20-112.**************)(33.**************,%20-112.**************)(33.**************,%20-111.**************)(33.**************,%20-111.*************)%22%20}%20]%20}%20}%20}&apikey=<YOUR API KEY>

You can use extended data to further refine your search, meaning that you can use the `extended_data` and `words` in combination.

### Search Parameters

The following query parameters can be used directly in the URL to constrain the search results:

* `pricerange`
* `price_min`, `price_max`
* `latlon` + `nearby_radius`
* `city`
* `zip`, `zipcode`
* `dwelling_type`, `proptype`

The [Quick Search](QuickSearch.md) uses these query parameters:

* 'price', 'sqft', 'bedroom', 'bathroom'

Most other fields are searched using the `words` query parameter.

## Examples ##

Here are some example queries.

* For ARMLS, newer than 2014-02-20, sorted by date, desc:

    ```
    http://api.profoundidx.com/q/search?words=LIST_87%3E2014-02-20&sort=ListDate,DESC&apikey=<YOUR API KEY>
    ```

* Specific client

    ```
    http://api.profoundidx.com/q/search?words=listing_member_shortid=<MEMBER ID>&apikey=<YOUR API KEY>
    ```

* Specific broker

    ```
    http://api.profoundidx.com/q/search?words=listing_office_shortid=<BROKER ID>&apikey=<YOUR API KEY>
    ```
