server {
   listen 80;
   server_name ifaidx.test;

   access_log /var/log/nginx/ifaidx.log;
   error_log /var/log/nginx/ifaidx-error.log;

   location / {
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header Host $http_host;
       proxy_set_header X-NginX-Proxy true;

       set $upstream http://newidx:8155;
       proxy_pass $upstream;
       proxy_redirect off;

       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";

       proxy_pass_request_headers on;
   }
}
