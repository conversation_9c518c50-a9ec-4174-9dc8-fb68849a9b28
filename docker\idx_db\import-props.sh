#!/bin/bash
mysql -h idx_db -u root -proot pfndidx_azdb -e "set session sql_mode='ALLOW_INVALID_DATES'; \
    drop table armls_property_A; \
    drop table armls_images; \
    create table armls_property_A like armls_prop_A_dump; \
    create table armls_images like armls_images_dump; \
    insert into armls_property_A select * from armls_prop_A_dump; \
    insert into armls_images select * from armls_images_dump;"
