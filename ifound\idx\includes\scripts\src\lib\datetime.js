import { utcToZonedTime, format } from 'date-fns-tz';

const formatStringForHumans = 'MMM d, yyyy, h:mm aaa';

export function formatDateTime(datetimeString) {
	const datetime = new Date(datetimeString);
	const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const zonedDate = utcToZonedTime(datetime, timeZone);
	const output = format(zonedDate, formatStringForHumans);
	return output;
}

export function getDateTimeNowAsMysqlStr(datetimeString) {
	return new Date().toISOString().split('.')[0] + 'Z';
}
