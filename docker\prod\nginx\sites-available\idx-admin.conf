upstream railsadmin {
    server railsadmin:3000;
}
upstream node_server {
    server newidx:8155;
}

server {
    listen 80;
    server_name *.ifoundagent.com;

    location / {
        return 301 https://$host$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
}

server {
    listen 443 ssl http2;
    server_name *.ifoundagent.com;

    access_log /var/log/nginx/idx-admin.log;
    error_log /var/log/nginx/idx-admin-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifoundagent.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifoundagent.com/privkey.pem;

    # Ideally this section would be in the newidx.conf. But I told Spark/FBS that the endpoint for app is
    # api.ifoundagent.com, and it's a pain communicating with them. I can't make the change myself. So I'm going to
    # leave it for now. But someday we could tell them to use api.profoundidx.com and put this section in that conf
    # file.
    location /spark/ {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://node_server;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_pass_request_headers on;
    }

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://railsadmin;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_pass_request_headers on;
    }
}

