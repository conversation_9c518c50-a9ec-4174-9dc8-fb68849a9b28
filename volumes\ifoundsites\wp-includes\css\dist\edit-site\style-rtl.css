@charset "UTF-8";
:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:var(--wp-block-synced-color);
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

.dataviews-wrapper{
  box-sizing:border-box;
  container:dataviews-wrapper/inline-size;
  display:flex;
  flex-direction:column;
  font-size:13px;
  height:100%;
  line-height:1.4;
  overflow:auto;
  scroll-padding-bottom:64px;
}

.dataviews-filters__container,.dataviews__view-actions{
  box-sizing:border-box;
  flex-shrink:0;
  padding:16px 48px;
  position:sticky;
  right:0;
}
@media not (prefers-reduced-motion){
  .dataviews-filters__container,.dataviews__view-actions{
    transition:padding .1s ease-out;
  }
}

.dataviews-loading,.dataviews-no-results{
  align-items:center;
  display:flex;
  flex-grow:1;
  justify-content:center;
  padding:0 48px;
}
@media not (prefers-reduced-motion){
  .dataviews-loading,.dataviews-no-results{
    transition:padding .1s ease-out;
  }
}

@container (max-width: 430px){
  .dataviews-filters__container,.dataviews__view-actions{
    padding:12px 24px;
  }
  .dataviews-loading,.dataviews-no-results{
    padding-left:24px;
    padding-right:24px;
  }
}
.dataviews-title-field{
  font-size:13px;
  font-weight:500;
  width:100%;
}
.dataviews-title-field,.dataviews-title-field a{
  color:#2f2f2f;
  text-overflow:ellipsis;
  white-space:nowrap;
}
.dataviews-title-field a{
  display:block;
  flex-grow:0;
  overflow:hidden;
  text-decoration:none;
}
.dataviews-title-field a:hover{
  color:var(--wp-admin-theme-color);
}
.dataviews-title-field a:focus{
  border-radius:2px;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color, #007cba);
  color:var(--wp-admin-theme-color--rgb);
}
.dataviews-title-field button.components-button.is-link{
  color:#1e1e1e;
  display:block;
  font-weight:inherit;
  overflow:hidden;
  text-decoration:none;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:100%;
}
.dataviews-title-field button.components-button.is-link:hover{
  color:var(--wp-admin-theme-color);
}

.dataviews-title-field--clickable{
  color:#2f2f2f;
  cursor:pointer;
}
.dataviews-title-field--clickable:hover{
  color:var(--wp-admin-theme-color);
}
.dataviews-title-field--clickable:focus{
  border-radius:2px;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color, #007cba);
  color:var(--wp-admin-theme-color--rgb);
}

.dataviews-bulk-actions-footer__item-count{
  color:#1e1e1e;
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}

.dataviews-bulk-actions-footer__container{
  margin-left:auto;
  min-height:32px;
}

.dataviews-filters__button{
  position:relative;
}

.dataviews-filters__container{
  padding-top:0;
}

.dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true],.dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true]:hover{
  opacity:0;
}
.dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true]:focus{
  opacity:1;
}

.dataviews-filters__summary-popover{
  font-size:13px;
  line-height:1.4;
}
.dataviews-filters__summary-popover .components-popover__content{
  border-radius:4px;
  width:230px;
}
.dataviews-filters__summary-popover.components-dropdown__content .components-popover__content{
  padding:0;
}

.dataviews-filters__summary-operators-container{
  padding:8px 8px 0;
}
.dataviews-filters__summary-operators-container:has(+.dataviews-filters__search-widget-listbox){
  border-bottom:1px solid #e0e0e0;
  padding-bottom:8px;
}
.dataviews-filters__summary-operators-container:empty{
  display:none;
}
.dataviews-filters__summary-operators-container .dataviews-filters__summary-operators-filter-name{
  color:#757575;
}

.dataviews-filters__summary-chip-container{
  position:relative;
  white-space:pre-wrap;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip{
  align-items:center;
  background:#f0f0f0;
  border:1px solid #0000;
  border-radius:16px;
  box-sizing:border-box;
  color:#2f2f2f;
  cursor:pointer;
  display:flex;
  min-height:32px;
  padding:4px 12px;
  position:relative;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-reset{
  padding-inline-end:28px;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:focus-visible,.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:hover,.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip[aria-expanded=true]{
  background:#e0e0e0;
  color:#1e1e1e;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  color:var(--wp-admin-theme-color);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values:hover,.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values[aria-expanded=true]{
  background:rgba(var(--wp-admin-theme-color--rgb), .12);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:focus-visible{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:none;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip .dataviews-filters-__summary-filter-text-name{
  font-weight:500;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove{
  align-items:center;
  background:#0000;
  border:0;
  border-radius:50%;
  cursor:pointer;
  display:flex;
  height:24px;
  justify-content:center;
  left:4px;
  padding:0;
  position:absolute;
  top:50%;
  transform:translateY(-50%);
  width:24px;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove svg{
  fill:#757575;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus,.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:hover{
  background:#e0e0e0;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus svg,.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:hover svg{
  fill:#1e1e1e;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove.has-values svg{
  fill:var(--wp-admin-theme-color);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove.has-values:hover{
  background:rgba(var(--wp-admin-theme-color--rgb), .08);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus-visible{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:none;
}

.dataviews-filters__search-widget-filter-combobox-list{
  border-top:1px solid #e0e0e0;
  max-height:184px;
  overflow:auto;
  padding:4px;
}
.dataviews-filters__search-widget-filter-combobox-list .dataviews-filters__search-widget-filter-combobox-item-value [data-user-value]{
  font-weight:600;
}

.dataviews-filters__search-widget-listbox{
  overflow:auto;
  padding:4px;
}

.dataviews-filters__search-widget-listitem{
  align-items:center;
  border-radius:2px;
  box-sizing:border-box;
  cursor:default;
  display:flex;
  gap:8px;
  margin-block-end:2px;
  padding:8px 12px;
}
.dataviews-filters__search-widget-listitem:last-child{
  margin-block-end:0;
}
.dataviews-filters__search-widget-listitem:focus,.dataviews-filters__search-widget-listitem:hover,.dataviews-filters__search-widget-listitem[data-active-item]{
  background-color:var(--wp-admin-theme-color);
  color:#fff;
}
.dataviews-filters__search-widget-listitem:focus .dataviews-filters__search-widget-listitem-check,.dataviews-filters__search-widget-listitem:hover .dataviews-filters__search-widget-listitem-check,.dataviews-filters__search-widget-listitem[data-active-item] .dataviews-filters__search-widget-listitem-check{
  fill:#fff;
}
.dataviews-filters__search-widget-listitem:focus .dataviews-filters__search-widget-listitem-description,.dataviews-filters__search-widget-listitem:hover .dataviews-filters__search-widget-listitem-description,.dataviews-filters__search-widget-listitem[data-active-item] .dataviews-filters__search-widget-listitem-description{
  color:#fff;
}
.dataviews-filters__search-widget-listitem .dataviews-filters__search-widget-listitem-check{
  flex-shrink:0;
  height:24px;
  width:24px;
}
.dataviews-filters__search-widget-listitem .dataviews-filters__search-widget-listitem-description{
  color:#757575;
  display:block;
  font-size:12px;
  line-height:16px;
  overflow:hidden;
  text-overflow:ellipsis;
}

.dataviews-filters__search-widget-filter-combobox__wrapper{
  padding:8px;
  position:relative;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input{
  background:#f0f0f0;
  border:none;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  display:block;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  height:32px;
  line-height:normal;
  margin-left:0;
  margin-right:0;
  padding:0 8px 0 32px;
  width:100%;
}
@media not (prefers-reduced-motion){
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input{
    font-size:13px;
    line-height:normal;
  }
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-moz-placeholder{
  color:#1e1e1e9e;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:-ms-input-placeholder{
  color:#1e1e1e9e;
}
@media (min-width:600px){
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input{
    font-size:13px;
  }
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:focus{
  background:#fff;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::placeholder{
  color:#757575;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-cancel-button,.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-decoration,.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-results-button,.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-results-decoration{
  -webkit-appearance:none;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__icon{
  align-items:center;
  display:flex;
  justify-content:center;
  left:12px;
  position:absolute;
  top:50%;
  transform:translateY(-50%);
  width:24px;
}

.dataviews-filters__container-visibility-toggle{
  flex-shrink:0;
  position:relative;
}

.dataviews-filters-toggle__count{
  background:var(--wp-admin-theme-color, #3858e9);
  border-radius:8px;
  box-sizing:border-box;
  color:#fff;
  font-size:11px;
  height:16px;
  left:0;
  line-height:16px;
  min-width:16px;
  outline:var(--wp-admin-border-width-focus) solid #fff;
  padding:0 4px;
  position:absolute;
  text-align:center;
  top:0;
  transform:translate(-50%, -50%);
}

.dataviews-search{
  width:fit-content;
}

.dataviews-footer{
  background-color:#fff;
  border-top:1px solid #f0f0f0;
  bottom:0;
  flex-shrink:0;
  padding:12px 48px;
  position:sticky;
  right:0;
  z-index:2;
}
@media not (prefers-reduced-motion){
  .dataviews-footer{
    transition:padding .1s ease-out;
  }
}

@container (max-width: 430px){
  .dataviews-footer{
    padding:12px 24px;
  }
}
@container (max-width: 560px){
  .dataviews-footer{
    flex-direction:column !important;
  }
  .dataviews-footer .dataviews-bulk-actions-footer__container{
    width:100%;
  }
  .dataviews-footer .dataviews-bulk-actions-footer__item-count{
    flex-grow:1;
  }
  .dataviews-footer .dataviews-pagination{
    justify-content:space-between;
    width:100%;
  }
}
.dataviews-pagination__page-select{
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}
@media (min-width:600px){
  .dataviews-pagination__page-select .components-select-control__input{
    font-size:11px !important;
    font-weight:500;
  }
}

.dataviews-action-modal{
  z-index:1000001;
}

.dataviews-selection-checkbox{
  --checkbox-input-size:24px;
  flex-shrink:0;
  line-height:0;
}
@media (min-width:600px){
  .dataviews-selection-checkbox{
    --checkbox-input-size:16px;
  }
}
.dataviews-selection-checkbox .components-checkbox-control__input-container{
  margin:0;
}

.dataviews-view-config{
  container-type:inline-size;
  font-size:13px;
  line-height:1.4;
  width:320px;
}

.dataviews-config__popover.is-expanded .dataviews-config__popover-content-wrapper{
  height:100%;
  overflow-y:scroll;
}
.dataviews-config__popover.is-expanded .dataviews-config__popover-content-wrapper .dataviews-view-config{
  width:auto;
}

.dataviews-view-config__sort-direction .components-toggle-group-control-option-base{
  text-transform:uppercase;
}

.dataviews-settings-section__title.dataviews-settings-section__title{
  font-size:15px;
  line-height:24px;
}

.dataviews-settings-section__sidebar{
  grid-column:span 4;
}

.dataviews-settings-section__content,.dataviews-settings-section__content>*{
  grid-column:span 8;
}

.dataviews-settings-section__content .is-divided-in-two{
  display:contents;
}
.dataviews-settings-section__content .is-divided-in-two>*{
  grid-column:span 4;
}

.dataviews-settings-section:has(.dataviews-settings-section__content:empty){
  display:none;
}

@container (max-width: 500px){
  .dataviews-settings-section.dataviews-settings-section{
    grid-template-columns:repeat(2, 1fr);
  }
  .dataviews-settings-section.dataviews-settings-section .dataviews-settings-section__content,.dataviews-settings-section.dataviews-settings-section .dataviews-settings-section__sidebar{
    grid-column:span 2;
  }
}
.dataviews-field-control__field{
  height:32px;
}

.dataviews-field-control__actions{
  position:absolute;
  top:-9999em;
}

.dataviews-field-control__actions.dataviews-field-control__actions{
  gap:4px;
}

.dataviews-field-control__field.is-interacting .dataviews-field-control__actions,.dataviews-field-control__field:focus-within .dataviews-field-control__actions,.dataviews-field-control__field:hover .dataviews-field-control__actions{
  position:unset;
  top:unset;
}

.dataviews-field-control__icon{
  display:flex;
  width:24px;
}

.dataviews-field-control__label-sub-label-container{
  flex-grow:1;
}

.dataviews-field-control__label{
  display:block;
}

.dataviews-field-control__sub-label{
  color:#757575;
  font-size:11px;
  font-style:normal;
  margin-bottom:0;
  margin-top:8px;
}

.dataviews-view-grid{
  container-type:inline-size;
  grid-template-rows:max-content;
  margin-bottom:auto;
  padding:0 48px 24px;
}
@media not (prefers-reduced-motion){
  .dataviews-view-grid{
    transition:padding .1s ease-out;
  }
}
.dataviews-view-grid .dataviews-view-grid__card{
  height:100%;
  justify-content:flex-start;
  position:relative;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-actions{
  padding:8px 0 4px;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-field{
  align-items:center;
  display:flex;
  min-height:24px;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-field--clickable{
  width:fit-content;
}
.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-value{
  color:#1e1e1e;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__media:focus:after,.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__media:after{
  background-color:rgba(var(--wp-admin-theme-color--rgb), .08);
}
.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__media:after{
  box-shadow:inset 0 0 0 1px var(--wp-admin-theme-color);
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__media:focus:after{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.dataviews-view-grid .dataviews-view-grid__media{
  aspect-ratio:1/1;
  background-color:#f0f0f0;
  border-radius:4px;
  min-height:200px;
  position:relative;
  width:100%;
}
.dataviews-view-grid .dataviews-view-grid__media img{
  height:100%;
  object-fit:cover;
  width:100%;
}
.dataviews-view-grid .dataviews-view-grid__media:after{
  border-radius:4px;
  box-shadow:inset 0 0 0 1px #0000001a;
  content:"";
  height:100%;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}
.dataviews-view-grid .dataviews-view-grid__fields{
  font-size:12px;
  line-height:16px;
  position:relative;
}
.dataviews-view-grid .dataviews-view-grid__fields:not(:empty){
  padding:0 0 12px;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field-value:not(:empty){
  line-height:20px;
  min-height:24px;
  padding-top:2px;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field{
  align-items:center;
  min-height:24px;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-name{
  color:#757575;
  width:35%;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-value{
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:65%;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field:not(:has(.dataviews-view-grid__field-value:not(:empty))){
  display:none;
}
.dataviews-view-grid .dataviews-view-grid__badge-fields:not(:empty){
  padding-bottom:12px;
}
@container (max-width: 480px){
  .dataviews-view-grid.dataviews-view-grid{
    grid-template-columns:repeat(1, minmax(0, 1fr));
    padding-left:24px;
    padding-right:24px;
  }
}
@container (min-width: 480px){
  .dataviews-view-grid.dataviews-view-grid{
    grid-template-columns:repeat(2, minmax(0, 1fr));
  }
}
@container (min-width: 780px){
  .dataviews-view-grid.dataviews-view-grid{
    grid-template-columns:repeat(3, minmax(0, 1fr));
  }
}
@container (min-width: 1140px){
  .dataviews-view-grid.dataviews-view-grid{
    grid-template-columns:repeat(4, minmax(0, 1fr));
  }
}
@container (min-width: 1520px){
  .dataviews-view-grid.dataviews-view-grid{
    grid-template-columns:repeat(5, minmax(0, 1fr));
  }
}

.dataviews-view-grid__field-value:empty,.dataviews-view-grid__field:empty{
  display:none;
}

.dataviews-view-grid__card .dataviews-selection-checkbox{
  position:absolute;
  right:8px;
  top:-9999em;
  z-index:1;
}

.dataviews-view-grid__card.is-selected .dataviews-selection-checkbox,.dataviews-view-grid__card:focus-within .dataviews-selection-checkbox,.dataviews-view-grid__card:hover .dataviews-selection-checkbox{
  top:8px;
}

.dataviews-view-grid__media--clickable{
  cursor:pointer;
}

div.dataviews-view-list{
  list-style-type:none;
}

.dataviews-view-list{
  margin:0 0 auto;
}
.dataviews-view-list div[role=row]{
  border-top:1px solid #f0f0f0;
  margin:0;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-wrapper{
  box-sizing:border-box;
  padding:16px 24px;
  position:relative;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions{
  flex:0;
  overflow:hidden;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions>div{
  height:24px;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions .components-button{
  opacity:0;
  position:relative;
  z-index:1;
}
.dataviews-view-list div[role=row]:where(.is-selected,.is-hovered,:focus-within) .dataviews-view-list__item-actions{
  flex-basis:min-content;
  overflow:unset;
  padding-inline-end:4px;
}
.dataviews-view-list div[role=row]:where(.is-selected,.is-hovered,:focus-within) .dataviews-view-list__item-actions .components-button{
  opacity:1;
}
.dataviews-view-list div[role=row].is-selected.is-selected,.dataviews-view-list div[role=row].is-selected.is-selected+div[role=row]{
  border-top:1px solid rgba(var(--wp-admin-theme-color--rgb), .12);
}
.dataviews-view-list div[role=row]:not(.is-selected) .dataviews-view-list__title-field{
  color:#1e1e1e;
}
.dataviews-view-list div[role=row]:not(.is-selected).is-hovered,.dataviews-view-list div[role=row]:not(.is-selected):focus-within,.dataviews-view-list div[role=row]:not(.is-selected):hover{
  background-color:#f8f8f8;
  color:var(--wp-admin-theme-color);
}
.dataviews-view-list div[role=row]:not(.is-selected).is-hovered .dataviews-view-list__fields,.dataviews-view-list div[role=row]:not(.is-selected).is-hovered .dataviews-view-list__title-field,.dataviews-view-list div[role=row]:not(.is-selected):focus-within .dataviews-view-list__fields,.dataviews-view-list div[role=row]:not(.is-selected):focus-within .dataviews-view-list__title-field,.dataviews-view-list div[role=row]:not(.is-selected):hover .dataviews-view-list__fields,.dataviews-view-list div[role=row]:not(.is-selected):hover .dataviews-view-list__title-field{
  color:var(--wp-admin-theme-color);
}
.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper,.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper{
  background-color:rgba(var(--wp-admin-theme-color--rgb), .04);
  color:#1e1e1e;
}
.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper .dataviews-view-list__fields,.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper .dataviews-view-list__title-field,.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper .dataviews-view-list__fields,.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper .dataviews-view-list__title-field{
  color:var(--wp-admin-theme-color);
}
.dataviews-view-list .dataviews-view-list__item{
  appearance:none;
  background:none;
  border:none;
  cursor:pointer;
  inset:0;
  padding:0;
  position:absolute;
  scroll-margin:8px 0;
  z-index:1;
}
.dataviews-view-list .dataviews-view-list__item:focus-visible{
  outline:none;
}
.dataviews-view-list .dataviews-view-list__item:focus-visible:before{
  border-radius:2px;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  content:"";
  inset:var(--wp-admin-border-width-focus);
  outline:2px solid #0000;
  position:absolute;
}
.dataviews-view-list .dataviews-view-list__title-field{
  flex:1;
  line-height:24px;
  min-height:24px;
  overflow:hidden;
}
.dataviews-view-list .dataviews-view-list__title-field:has(a,button){
  z-index:1;
}
.dataviews-view-list .dataviews-view-list__media-wrapper{
  background-color:#f0f0f0;
  border-radius:4px;
  flex-shrink:0;
  height:52px;
  overflow:hidden;
  position:relative;
  width:52px;
}
.dataviews-view-list .dataviews-view-list__media-wrapper img{
  height:100%;
  object-fit:cover;
  width:100%;
}
.dataviews-view-list .dataviews-view-list__media-wrapper:after{
  border-radius:4px;
  box-shadow:inset 0 0 0 1px #0000001a;
  content:"";
  height:100%;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}
.dataviews-view-list .dataviews-view-list__field-wrapper{
  flex-grow:1;
  min-height:52px;
}
.dataviews-view-list .dataviews-view-list__fields{
  color:#757575;
  display:flex;
  flex-wrap:wrap;
  font-size:12px;
  gap:12px;
  row-gap:4px;
}
.dataviews-view-list .dataviews-view-list__fields .dataviews-view-list__field:has(.dataviews-view-list__field-value:empty),.dataviews-view-list .dataviews-view-list__fields:empty{
  display:none;
}
.dataviews-view-list .dataviews-view-list__fields .dataviews-view-list__field-value{
  align-items:center;
  display:flex;
  line-height:20px;
  min-height:24px;
}
.dataviews-view-list+.dataviews-pagination{
  justify-content:space-between;
}

.dataviews-view-table{
  border-collapse:collapse;
  border-color:inherit;
  color:#757575;
  margin-bottom:auto;
  position:relative;
  text-indent:0;
  width:100%;
}
.dataviews-view-table th{
  color:#1e1e1e;
  font-size:13px;
  font-weight:400;
  text-align:right;
}
.dataviews-view-table td,.dataviews-view-table th{
  padding:12px;
  white-space:nowrap;
}
.dataviews-view-table td.dataviews-view-table__actions-column,.dataviews-view-table th.dataviews-view-table__actions-column{
  text-align:left;
}
.dataviews-view-table td.dataviews-view-table__checkbox-column,.dataviews-view-table th.dataviews-view-table__checkbox-column{
  padding-left:0;
  width:1%;
}
.dataviews-view-table tr{
  border-top:1px solid #f0f0f0;
}
.dataviews-view-table tr .dataviews-view-table-header-button{
  gap:4px;
}
.dataviews-view-table tr td:first-child,.dataviews-view-table tr th:first-child{
  padding-right:48px;
}
.dataviews-view-table tr td:first-child .dataviews-view-table-header-button,.dataviews-view-table tr th:first-child .dataviews-view-table-header-button{
  margin-right:-8px;
}
.dataviews-view-table tr td:last-child,.dataviews-view-table tr th:last-child{
  padding-left:48px;
}
.dataviews-view-table tr:last-child{
  border-bottom:0;
}
.dataviews-view-table tr.is-hovered{
  background-color:#f8f8f8;
}
.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input{
  opacity:0;
}
.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:checked,.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:focus,.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:indeterminate{
  opacity:1;
}
.dataviews-view-table tr .dataviews-item-actions .components-button:not(.dataviews-all-actions-button){
  opacity:0;
}
.dataviews-view-table tr.is-hovered .components-checkbox-control__input,.dataviews-view-table tr.is-hovered .dataviews-item-actions .components-button:not(.dataviews-all-actions-button),.dataviews-view-table tr:focus-within .components-checkbox-control__input,.dataviews-view-table tr:focus-within .dataviews-item-actions .components-button:not(.dataviews-all-actions-button),.dataviews-view-table tr:hover .components-checkbox-control__input,.dataviews-view-table tr:hover .dataviews-item-actions .components-button:not(.dataviews-all-actions-button){
  opacity:1;
}
@media (hover:none){
  .dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input,.dataviews-view-table tr .dataviews-item-actions .components-button:not(.dataviews-all-actions-button){
    opacity:1;
  }
}
.dataviews-view-table tr.is-selected{
  background-color:rgba(var(--wp-admin-theme-color--rgb), .04);
  color:#757575;
}
.dataviews-view-table tr.is-selected,.dataviews-view-table tr.is-selected+tr{
  border-top:1px solid rgba(var(--wp-admin-theme-color--rgb), .12);
}
.dataviews-view-table tr.is-selected:hover{
  background-color:rgba(var(--wp-admin-theme-color--rgb), .08);
}
.dataviews-view-table thead{
  inset-block-start:0;
  position:sticky;
  z-index:1;
}
.dataviews-view-table thead tr{
  border:0;
}
.dataviews-view-table thead th{
  background-color:#fff;
  font-size:11px;
  font-weight:500;
  padding-bottom:8px;
  padding-right:12px;
  padding-top:8px;
  text-transform:uppercase;
}
.dataviews-view-table thead th:has(.dataviews-view-table-header-button):not(:first-child){
  padding-right:4px;
}
.dataviews-view-table tbody td{
  vertical-align:top;
}
.dataviews-view-table tbody .dataviews-view-table__cell-content-wrapper{
  align-items:center;
  display:flex;
  min-height:32px;
}
.dataviews-view-table tbody .components-v-stack>.dataviews-view-table__cell-content-wrapper:not(:first-child){
  min-height:0;
}
.dataviews-view-table .dataviews-view-table-header-button{
  font-size:11px;
  font-weight:500;
  padding:4px 8px;
  text-transform:uppercase;
}
.dataviews-view-table .dataviews-view-table-header-button:not(:hover){
  color:#1e1e1e;
}
.dataviews-view-table .dataviews-view-table-header-button span{
  speak:none;
}
.dataviews-view-table .dataviews-view-table-header-button span:empty{
  display:none;
}
.dataviews-view-table .dataviews-view-table-header{
  padding-right:4px;
}
.dataviews-view-table .dataviews-view-table__actions-column{
  width:1%;
}
.dataviews-view-table:has(tr.is-selected) .components-checkbox-control__input{
  opacity:1;
}
.dataviews-view-table.has-compact-density thead th:has(.dataviews-view-table-header-button):not(:first-child){
  padding-right:0;
}
.dataviews-view-table.has-compact-density td,.dataviews-view-table.has-compact-density th{
  padding:4px 8px;
}
.dataviews-view-table.has-comfortable-density td,.dataviews-view-table.has-comfortable-density th{
  padding:16px 12px;
}
.dataviews-view-table.has-comfortable-density td.dataviews-view-table__checkbox-column,.dataviews-view-table.has-comfortable-density th.dataviews-view-table__checkbox-column,.dataviews-view-table.has-compact-density td.dataviews-view-table__checkbox-column,.dataviews-view-table.has-compact-density th.dataviews-view-table__checkbox-column{
  padding-left:0;
}

@container (max-width: 430px){
  .dataviews-view-table tr td:first-child,.dataviews-view-table tr th:first-child{
    padding-right:24px;
  }
  .dataviews-view-table tr td:last-child,.dataviews-view-table tr th:last-child{
    padding-left:24px;
  }
}
.dataviews-view-table-selection-checkbox{
  --checkbox-input-size:24px;
}
@media (min-width:600px){
  .dataviews-view-table-selection-checkbox{
    --checkbox-input-size:16px;
  }
}

.dataviews-column-primary__media{
  max-width:60px;
}

.dataviews-controls__datetime{
  border:none;
  padding:0;
}

.dataforms-layouts-panel__field{
  align-items:flex-start !important;
  justify-content:flex-start !important;
  min-height:32px;
  width:100%;
}

.dataforms-layouts-panel__field-label{
  align-items:center;
  display:flex;
  flex-shrink:0;
  hyphens:auto;
  line-height:20px;
  min-height:32px;
  padding:6px 0;
  width:38%;
}

.dataforms-layouts-panel__field-control{
  align-items:center;
  display:flex;
  flex-grow:1;
  min-height:32px;
}
.dataforms-layouts-panel__field-control .components-button{
  max-width:100%;
  min-height:32px;
  text-align:right;
  text-wrap:balance;
  text-wrap:pretty;
  white-space:normal;
}
.dataforms-layouts-panel__field-control .components-dropdown{
  max-width:100%;
}

.dataforms-layouts-panel__field-dropdown .components-popover__content{
  min-width:320px;
  padding:16px;
}

.dataforms-layouts-panel__dropdown-header{
  margin-bottom:16px;
}

.components-popover.components-dropdown__content.dataforms-layouts-panel__field-dropdown{
  z-index:159990;
}

.dataforms-layouts-regular__field{
  align-items:flex-start !important;
  justify-content:flex-start !important;
  min-height:32px;
  width:100%;
}

.dataforms-layouts-regular__field .components-base-control__label{
  font-size:inherit;
  font-weight:400;
  text-transform:none;
}

.dataforms-layouts-regular__field-label{
  align-items:center;
  display:flex;
  flex-shrink:0;
  hyphens:auto;
  line-height:20px;
  min-height:32px;
  padding:6px 0;
  width:38%;
}

.dataforms-layouts-regular__field-control{
  align-items:center;
  display:flex;
  flex-grow:1;
  min-height:32px;
}

.fields-create-template-part-modal{
  z-index:1000001;
}

.fields-create-template-part-modal__area-radio-group{
  border:1px solid #949494;
  border-radius:2px;
}

.fields-create-template-part-modal__area-radio-wrapper{
  align-items:center;
  display:grid;
  grid-template-columns:min-content 1fr min-content;
  padding:12px;
  position:relative;
  grid-gap:4px 8px;
  color:#1e1e1e;
}
.fields-create-template-part-modal__area-radio-wrapper+.fields-create-template-part-modal__area-radio-wrapper{
  border-top:1px solid #949494;
}
.fields-create-template-part-modal__area-radio-wrapper input[type=radio]{
  opacity:0;
  position:absolute;
}
.fields-create-template-part-modal__area-radio-wrapper:has(input[type=radio]:checked){
  z-index:1;
}
.fields-create-template-part-modal__area-radio-wrapper:has(input[type=radio]:not(:checked)):hover{
  color:var(--wp-admin-theme-color);
}
.fields-create-template-part-modal__area-radio-wrapper>:not(.fields-create-template-part-modal__area-radio-label){
  pointer-events:none;
}

.fields-create-template-part-modal__area-radio-label:before{
  content:"";
  inset:0;
  position:absolute;
}
input[type=radio]:not(:checked)~.fields-create-template-part-modal__area-radio-label:before{
  cursor:pointer;
}
input[type=radio]:focus-visible~.fields-create-template-part-modal__area-radio-label:before{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:4px solid #0000;
}

.fields-create-template-part-modal__area-radio-checkmark,.fields-create-template-part-modal__area-radio-icon{
  fill:currentColor;
}

input[type=radio]:not(:checked)~.fields-create-template-part-modal__area-radio-checkmark{
  opacity:0;
}

.fields-create-template-part-modal__area-radio-description{
  color:#757575;
  font-size:12px;
  grid-column:2 /  3;
  line-height:normal;
  margin:0;
  text-wrap:pretty;
}
input[type=radio]:not(:checked):hover~.fields-create-template-part-modal__area-radio-description{
  color:inherit;
}

.fields-controls__slug .fields-controls__slug-external-icon{
  margin-right:5ch;
}
.fields-controls__slug .fields-controls__slug-input input.components-input-control__input{
  padding-inline-start:0 !important;
}
.fields-controls__slug .fields-controls__slug-help-link{
  word-break:break-word;
}
.fields-controls__slug .fields-controls__slug-help{
  display:flex;
  flex-direction:column;
}
.fields-controls__slug .fields-controls__slug-help .fields-controls__slug-help-slug{
  font-weight:600;
}

.fields-controls__featured-image-placeholder{
  background:#fff linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
  border-radius:2px;
  box-shadow:inset 0 0 0 1px #0003;
  display:inline-block;
  padding:0;
}

.fields-controls__featured-image-title{
  color:#1e1e1e;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:100%;
}

.fields-controls__featured-image-image{
  align-self:center;
  border-radius:2px;
  height:100%;
  width:100%;
}

.fields-controls__featured-image-container .fields-controls__featured-image-placeholder{
  margin:0;
}
.fields-controls__featured-image-container span{
  margin-left:auto;
}

fieldset.fields-controls__featured-image .fields-controls__featured-image-container{
  border:1px solid #ddd;
  border-radius:2px;
  cursor:pointer;
  padding:8px 12px;
}
fieldset.fields-controls__featured-image .fields-controls__featured-image-container:hover{
  background-color:#f0f0f0;
}
fieldset.fields-controls__featured-image .fields-controls__featured-image-placeholder{
  height:24px;
  width:24px;
}
fieldset.fields-controls__featured-image span{
  align-self:center;
  text-align:start;
  white-space:nowrap;
}
fieldset.fields-controls__featured-image .fields-controls__featured-image-upload-button{
  height:fit-content;
  padding:0;
}
fieldset.fields-controls__featured-image .fields-controls__featured-image-upload-button:focus,fieldset.fields-controls__featured-image .fields-controls__featured-image-upload-button:hover{
  border:0;
  color:unset;
}
fieldset.fields-controls__featured-image .fields-controls__featured-image-remove-button{
  place-self:end;
}
.dataforms-layouts-panel__field-control .fields-controls__featured-image-image,.dataforms-layouts-panel__field-control .fields-controls__featured-image-placeholder{
  height:16px;
  width:16px;
}

.dataviews-view-table__cell-content-wrapper .fields-controls__featured-image-image,.dataviews-view-table__cell-content-wrapper .fields-controls__featured-image-placeholder{
  display:block;
  height:32px;
  width:32px;
}

.fields-controls__template-modal{
  z-index:1000001;
}

.fields-controls__template-content .block-editor-block-patterns-list{
  column-count:2;
  column-gap:24px;
  padding-top:2px;
}
@media (min-width:782px){
  .fields-controls__template-content .block-editor-block-patterns-list{
    column-count:3;
  }
}
@media (min-width:1280px){
  .fields-controls__template-content .block-editor-block-patterns-list{
    column-count:4;
  }
}
.fields-controls__template-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  break-inside:avoid-column;
}

.fields-field__title span:first-child{
  display:block;
  flex-grow:0;
  overflow:hidden;
  text-decoration:none;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.fields-field__pattern-title span:first-child{
  flex:1;
}

.edit-site-custom-template-modal__contents-wrapper{
  height:100%;
  justify-content:flex-start !important;
}
.edit-site-custom-template-modal__contents-wrapper>*{
  width:100%;
}
.edit-site-custom-template-modal__contents-wrapper__suggestions_list{
  margin-left:-12px;
  margin-right:-12px;
  width:calc(100% + 24px);
}
.edit-site-custom-template-modal__contents>.components-button{
  height:auto;
  justify-content:center;
}
@media (min-width:782px){
  .edit-site-custom-template-modal{
    width:456px;
  }
}
@media (min-width:600px){
  .edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list{
    max-height:224px;
    overflow-y:auto;
  }
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item{
  display:block;
  height:auto;
  overflow-wrap:break-word;
  padding:8px 12px;
  text-align:right;
  white-space:pre-wrap;
  width:100%;
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item mark{
  background:none;
  font-weight:700;
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item:hover{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item:hover *,.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item:hover mark{
  color:var(--wp-admin-theme-color);
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item:focus{
  background-color:#f0f0f0;
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item:focus:not(:disabled){
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color) inset;
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item__info,.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item__title{
  display:block;
  overflow:hidden;
  text-overflow:ellipsis;
}
.edit-site-custom-template-modal .edit-site-custom-template-modal__suggestions_list__list-item__info{
  color:#757575;
  word-break:break-all;
}

.edit-site-custom-template-modal__no-results{
  border:1px solid #ccc;
  border-radius:2px;
  padding:16px;
}

.edit-site-custom-generic-template__modal .components-modal__header{
  border-bottom:none;
}
.edit-site-custom-generic-template__modal .components-modal__content:before{
  margin-bottom:4px;
}

@media (min-width:960px){
  .edit-site-add-new-template__modal{
    margin-top:64px;
    max-height:calc(100% - 128px);
    max-width:832px;
    width:calc(100% - 128px);
  }
}
.edit-site-add-new-template__modal .edit-site-add-new-template__custom-template-button svg,.edit-site-add-new-template__modal .edit-site-add-new-template__template-button svg{
  fill:var(--wp-admin-theme-color);
}
.edit-site-add-new-template__modal .edit-site-add-new-template__custom-template-button .edit-site-add-new-template__template-name{
  align-items:flex-start;
  flex-grow:1;
}
.edit-site-add-new-template__modal .edit-site-add-new-template__template-icon{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  border-radius:100%;
  max-height:40px;
  max-width:40px;
  padding:8px;
}

.edit-site-add-new-template__template-list__contents>.components-button,.edit-site-custom-template-modal__contents>.components-button{
  border:1px solid #ddd;
  display:flex;
  flex-direction:column;
  justify-content:center;
  outline:1px solid #0000;
  padding:32px;
}
.edit-site-add-new-template__template-list__contents>.components-button span:first-child,.edit-site-custom-template-modal__contents>.components-button span:first-child{
  color:#1e1e1e;
}
.edit-site-add-new-template__template-list__contents>.components-button span,.edit-site-custom-template-modal__contents>.components-button span{
  color:#757575;
}
.edit-site-add-new-template__template-list__contents>.components-button:hover,.edit-site-custom-template-modal__contents>.components-button:hover{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  border-color:#0000;
  color:var(--wp-admin-theme-color-darker-10);
}
.edit-site-add-new-template__template-list__contents>.components-button:hover span,.edit-site-custom-template-modal__contents>.components-button:hover span{
  color:var(--wp-admin-theme-color);
}
.edit-site-add-new-template__template-list__contents>.components-button:focus,.edit-site-custom-template-modal__contents>.components-button:focus{
  border-color:#0000;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:3px solid #0000;
}
.edit-site-add-new-template__template-list__contents>.components-button:focus span:first-child,.edit-site-custom-template-modal__contents>.components-button:focus span:first-child{
  color:var(--wp-admin-theme-color);
}
.edit-site-add-new-template__template-list__contents .edit-site-add-new-template__custom-template-button,.edit-site-add-new-template__template-list__contents .edit-site-add-new-template__template-list__prompt,.edit-site-custom-template-modal__contents .edit-site-add-new-template__custom-template-button,.edit-site-custom-template-modal__contents .edit-site-add-new-template__template-list__prompt{
  grid-column:1 /  -1;
}

.edit-site-add-new-template__template-list__contents>.components-button{
  align-items:flex-start;
  height:100%;
  text-align:start;
}

.edit-site-visual-editor__editor-canvas.is-focused{
  outline:calc(var(--wp-admin-border-width-focus)*2) solid var(--wp-admin-theme-color);
  outline-offset:calc(var(--wp-admin-border-width-focus)*-2);
}

.edit-site-canvas-loader{
  align-items:center;
  display:flex;
  height:100%;
  justify-content:center;
  opacity:0;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}
@media not (prefers-reduced-motion){
  .edit-site-canvas-loader{
    animation:edit-site-canvas-loader__fade-in-animation .5s ease .2s;
    animation-fill-mode:forwards;
  }
}
.edit-site-canvas-loader>div{
  width:160px;
}

@keyframes edit-site-canvas-loader__fade-in-animation{
  0%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
.edit-site-global-styles-preview{
  align-items:center;
  cursor:pointer;
  display:flex;
  justify-content:center;
  line-height:1;
}

.edit-site-global-styles-preview__wrapper{
  display:block;
  max-width:100%;
  width:100%;
}

.edit-site-typography-preview{
  align-items:center;
  background:#f0f0f0;
  border-radius:2px;
  display:flex;
  justify-content:center;
  margin-bottom:16px;
  min-height:100px;
  overflow:hidden;
}

.edit-site-font-size__item{
  line-break:anywhere;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.edit-site-font-size__item-value{
  color:#757575;
}

.edit-site-global-styles-screen{
  margin:12px 16px 16px;
}

.edit-site-global-styles-screen-typography__indicator{
  align-items:center;
  border-radius:1px;
  display:flex !important;
  font-size:14px;
  height:24px;
  justify-content:center;
  width:24px;
}

.edit-site-global-styles-screen-typography__font-variants-count{
  color:#757575;
}

.edit-site-global-styles-font-families__manage-fonts{
  justify-content:center;
}

.edit-site-global-styles-screen .color-block-support-panel{
  border-top:none;
  padding-left:0;
  padding-right:0;
  padding-top:0;
  row-gap:12px;
}

.edit-site-global-styles-header__description{
  padding:0 16px;
}

.edit-site-block-types-search{
  margin-bottom:8px;
  padding:0 16px;
}

.edit-site-global-styles-header{
  margin-bottom:0 !important;
}

.edit-site-global-styles-subtitle{
  font-size:11px !important;
  font-weight:500 !important;
  margin-bottom:0 !important;
  text-transform:uppercase;
}

.edit-site-global-styles-section-title{
  color:#2f2f2f;
  font-weight:600;
  line-height:1.2;
  margin:0;
  padding:16px 16px 0;
}

.edit-site-global-styles-icon-with-current-color{
  fill:currentColor;
}

.edit-site-global-styles__color-indicator-wrapper{
  flex-shrink:0;
  height:24px;
}

.edit-site-global-styles__shadows-panel__options-container,.edit-site-global-styles__shadows-panel__title{
  height:24px;
}

.edit-site-global-styles__block-preview-panel{
  border:1px solid #e0e0e0;
  border-radius:4px;
  overflow:hidden;
  position:relative;
  width:100%;
}

.edit-site-global-styles__shadow-preview-panel{
  background-image:repeating-linear-gradient(-45deg, #f5f5f5 25%, #0000 0, #0000 75%, #f5f5f5 0, #f5f5f5), repeating-linear-gradient(-45deg, #f5f5f5 25%, #0000 0, #0000 75%, #f5f5f5 0, #f5f5f5);
  background-position:100% 0, right 8px top 8px;
  background-size:16px 16px;
  border:1px solid #e0e0e0;
  border-radius:4px;
  height:144px;
  overflow:auto;
}
.edit-site-global-styles__shadow-preview-panel .edit-site-global-styles__shadow-preview-block{
  background-color:#fff;
  border:1px solid #e0e0e0;
  border-radius:2px;
  height:60px;
  width:60%;
}

.edit-site-global-styles__shadow-editor__dropdown-content{
  width:280px;
}

.edit-site-global-styles__shadow-editor-panel{
  margin-bottom:4px;
}

.edit-site-global-styles__shadow-editor__dropdown{
  position:relative;
  width:100%;
}

.edit-site-global-styles__shadow-editor__dropdown-toggle{
  border-radius:inherit;
  height:auto;
  padding-bottom:8px;
  padding-top:8px;
  text-align:right;
  width:100%;
}
.edit-site-global-styles__shadow-editor__dropdown-toggle.is-open{
  background:#f0f0f0;
  color:var(--wp-admin-theme-color);
}

.edit-site-global-styles__shadow-editor__remove-button{
  left:8px;
  opacity:0;
  position:absolute;
  top:8px;
}
.edit-site-global-styles__shadow-editor__remove-button.edit-site-global-styles__shadow-editor__remove-button{
  border:none;
}
.edit-site-global-styles__shadow-editor__dropdown-toggle:hover+.edit-site-global-styles__shadow-editor__remove-button,.edit-site-global-styles__shadow-editor__remove-button:focus,.edit-site-global-styles__shadow-editor__remove-button:hover{
  opacity:1;
}
@media (hover:none){
  .edit-site-global-styles__shadow-editor__remove-button{
    opacity:1;
  }
}

.edit-site-global-styles-screen-css{
  display:flex;
  flex:1 1 auto;
  flex-direction:column;
  margin:16px;
}
.edit-site-global-styles-screen-css .components-v-stack{
  flex:1 1 auto;
}
.edit-site-global-styles-screen-css .components-v-stack .block-editor-global-styles-advanced-panel__custom-css-input,.edit-site-global-styles-screen-css .components-v-stack .block-editor-global-styles-advanced-panel__custom-css-input .components-base-control__field{
  display:flex;
  flex:1 1 auto;
  flex-direction:column;
}
.edit-site-global-styles-screen-css .components-v-stack .block-editor-global-styles-advanced-panel__custom-css-input .components-base-control__field .components-textarea-control__input{
  direction:ltr;
  flex:1 1 auto;
}

.edit-site-global-styles-screen-css-help-link{
  display:inline-block;
  margin-top:8px;
}

.edit-site-global-styles-screen-variations{
  border-top:1px solid #ddd;
  margin-top:16px;
}
.edit-site-global-styles-screen-variations>*{
  margin:24px 16px;
}

.edit-site-global-styles-sidebar__navigator-provider{
  height:100%;
}

.edit-site-global-styles-sidebar__navigator-screen{
  display:flex;
  flex-direction:column;
  height:100%;
}

.edit-site-global-styles-sidebar__navigator-screen .single-column{
  grid-column:span 1;
}

.edit-site-global-styles-screen-root.edit-site-global-styles-screen-root,.edit-site-global-styles-screen-style-variations.edit-site-global-styles-screen-style-variations{
  background:unset;
  color:inherit;
}

.edit-site-global-styles-sidebar__panel .block-editor-block-icon svg{
  fill:currentColor;
}

.edit-site-global-styles-screen-root__active-style-tile.edit-site-global-styles-screen-root__active-style-tile,.edit-site-global-styles-screen-root__active-style-tile.edit-site-global-styles-screen-root__active-style-tile .edit-site-global-styles-screen-root__active-style-tile-preview{
  border-radius:2px;
}

.edit-site-global-styles-screen-revisions__revisions-list{
  flex-grow:1;
  list-style:none;
  margin:0 16px 16px;
}
.edit-site-global-styles-screen-revisions__revisions-list li{
  margin-bottom:0;
}

.edit-site-global-styles-screen-revisions__revision-item{
  cursor:pointer;
  display:flex;
  flex-direction:column;
  position:relative;
}
.edit-site-global-styles-screen-revisions__revision-item[role=option]:active,.edit-site-global-styles-screen-revisions__revision-item[role=option]:focus{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.edit-site-global-styles-screen-revisions__revision-item:hover{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
}
.edit-site-global-styles-screen-revisions__revision-item:hover .edit-site-global-styles-screen-revisions__date{
  color:var(--wp-admin-theme-color);
}
.edit-site-global-styles-screen-revisions__revision-item:after,.edit-site-global-styles-screen-revisions__revision-item:before{
  content:"\a";
  display:block;
  position:absolute;
}
.edit-site-global-styles-screen-revisions__revision-item:before{
  background:#ddd;
  border:4px solid #0000;
  border-radius:50%;
  height:8px;
  right:17px;
  top:18px;
  transform:translate(50%, -50%);
  width:8px;
  z-index:1;
}
.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true]{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  border-radius:2px;
  color:var(--wp-admin-theme-color);
  outline:3px solid #0000;
  outline-offset:-2px;
}
.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true] .edit-site-global-styles-screen-revisions__date{
  color:var(--wp-admin-theme-color);
}
.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true]:before{
  background:var(--wp-admin-theme-color);
}
.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true] .edit-site-global-styles-screen-revisions__applied-text,.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true] .edit-site-global-styles-screen-revisions__changes>li,.edit-site-global-styles-screen-revisions__revision-item[aria-selected=true] .edit-site-global-styles-screen-revisions__meta{
  color:#1e1e1e;
}
.edit-site-global-styles-screen-revisions__revision-item:after{
  border:.5px solid #ddd;
  height:100%;
  right:16px;
  top:0;
  width:0;
}
.edit-site-global-styles-screen-revisions__revision-item:first-child:after{
  top:18px;
}
.edit-site-global-styles-screen-revisions__revision-item:last-child:after{
  height:18px;
}

.edit-site-global-styles-screen-revisions__revision-item-wrapper{
  display:block;
  padding:12px 40px 4px 12px;
}

.edit-site-global-styles-screen-revisions__applied-text,.edit-site-global-styles-screen-revisions__apply-button.is-primary{
  align-self:flex-start;
  margin:4px 40px 12px 12px;
}

.edit-site-global-styles-screen-revisions__applied-text,.edit-site-global-styles-screen-revisions__changes,.edit-site-global-styles-screen-revisions__meta{
  color:#757575;
  font-size:12px;
}

.edit-site-global-styles-screen-revisions__description{
  align-items:flex-start;
  display:flex;
  flex-direction:column;
  gap:8px;
}
.edit-site-global-styles-screen-revisions__description .edit-site-global-styles-screen-revisions__date{
  font-size:12px;
  font-weight:600;
  text-transform:uppercase;
}

.edit-site-global-styles-screen-revisions__meta{
  align-items:flex-start;
  display:flex;
  justify-content:start;
  margin-bottom:4px;
  text-align:right;
  width:100%;
}
.edit-site-global-styles-screen-revisions__meta img{
  border-radius:100%;
  height:16px;
  margin-left:8px;
  width:16px;
}

.edit-site-global-styles-screen-revisions__loading{
  margin:24px auto !important;
}

.edit-site-global-styles-screen-revisions__changes{
  line-height:1.4;
  list-style:disc;
  margin-right:12px;
  text-align:right;
}
.edit-site-global-styles-screen-revisions__changes li{
  margin-bottom:4px;
}

.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination{
  gap:2px;
  justify-content:space-between;
}
.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination .edit-site-pagination__total{
  height:1px;
  margin:-1px;
  overflow:hidden;
  position:absolute;
  right:-1000px;
}
.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination .components-text{
  font-size:12px;
  will-change:opacity;
}
.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination .components-button.is-tertiary{
  color:#1e1e1e;
}
.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination .components-button.is-tertiary:disabled,.edit-site-global-styles-screen-revisions__pagination.edit-site-global-styles-screen-revisions__pagination .components-button.is-tertiary[aria-disabled=true]{
  color:#949494;
}

.edit-site-global-styles-screen-revisions__footer{
  background:#fff;
  border-top:1px solid #ddd;
  bottom:0;
  height:56px;
  min-width:100%;
  padding:12px;
  position:sticky;
  z-index:1;
}

.editor-sidebar{
  width:280px;
}
.editor-sidebar>.components-panel{
  border-left:0;
  border-right:0;
  margin-bottom:-1px;
  margin-top:-1px;
}
.editor-sidebar>.components-panel>.components-panel__header{
  background:#f0f0f0;
}
.editor-sidebar .block-editor-block-inspector__card{
  margin:0;
}

.edit-site-global-styles-sidebar{
  display:flex;
  flex-direction:column;
  min-height:100%;
}
.edit-site-global-styles-sidebar__panel{
  flex:1;
}

.edit-site-global-styles-sidebar .edit-site-global-styles-sidebar__header-title{
  margin:0;
}

.edit-site-global-styles-sidebar .edit-site-global-styles-sidebar__header-actions{
  flex:1;
}

.edit-site-global-styles-sidebar .components-navigation__menu-title-heading{
  font-size:15.6px;
  font-weight:500;
}

.edit-site-global-styles-sidebar .components-navigation__item>button span{
  font-weight:500;
}

.edit-site-global-styles-sidebar .block-editor-panel-color-gradient-settings{
  border:0;
}

.edit-site-global-styles-sidebar .single-column{
  grid-column:span 1;
}

.edit-site-global-styles-sidebar .components-tools-panel .span-columns{
  grid-column:1 /  -1;
}

.edit-site-global-styles-sidebar__blocks-group{
  border-top:1px solid #e0e0e0;
  padding-top:24px;
}

.edit-site-global-styles-sidebar__blocks-group-help{
  padding:0 16px;
}

.edit-site-global-styles-color-palette-panel,.edit-site-global-styles-gradient-palette-panel{
  padding:16px;
}

.edit-site-global-styles-sidebar hr{
  margin:0;
}

.show-icon-labels .edit-site-global-styles-sidebar__header .components-button.has-icon{
  width:auto;
}
.show-icon-labels .edit-site-global-styles-sidebar__header .components-button.has-icon svg{
  display:none;
}
.show-icon-labels .edit-site-global-styles-sidebar__header .components-button.has-icon:after{
  content:attr(aria-label);
  font-size:12px;
}

.edit-site-page{
  background:#fff;
  color:#2f2f2f;
  container:edit-site-page/inline-size;
  height:100%;
}
@media not (prefers-reduced-motion){
  .edit-site-page{
    transition:width .2s ease-out;
  }
}

.edit-site-page-header{
  background:#fff;
  border-bottom:1px solid #f0f0f0;
  padding:16px 48px;
  position:sticky;
  top:0;
  z-index:2;
}
@media not (prefers-reduced-motion){
  .edit-site-page-header{
    transition:padding .1s ease-out;
  }
}
.edit-site-page-header .components-heading{
  color:#1e1e1e;
}
.edit-site-page-header .edit-site-page-header__page-title{
  min-height:40px;
}
.edit-site-page-header .edit-site-page-header__page-title .components-heading{
  flex-basis:0;
  flex-grow:1;
  white-space:nowrap;
}
.edit-site-page-header .edit-site-page-header__sub-title{
  margin-bottom:8px;
}

@container (max-width: 430px){
  .edit-site-page-header{
    padding:16px 24px;
  }
}
.edit-site-page-content{
  display:flex;
  flex-flow:column;
  height:100%;
  position:relative;
  z-index:1;
}

.edit-site-patterns__delete-modal{
  width:384px;
}

.page-patterns-preview-field{
  align-items:center;
  border-radius:4px;
  display:flex;
  flex-direction:column;
  height:100%;
  justify-content:center;
}
.dataviews-view-grid .page-patterns-preview-field .block-editor-block-preview__container{
  height:100%;
}
.dataviews-view-table .page-patterns-preview-field{
  flex-grow:0;
  text-wrap:balance;
  text-wrap:pretty;
  width:96px;
}

.edit-site-patterns__pattern-icon{
  fill:var(--wp-block-synced-color);
  flex-shrink:0;
}

.edit-site-patterns__section-header{
  border-bottom:1px solid #f0f0f0;
  flex-shrink:0;
  min-height:40px;
  padding:16px 48px;
  position:sticky;
  top:0;
  z-index:2;
}
@media not (prefers-reduced-motion){
  .edit-site-patterns__section-header{
    transition:padding .1s ease-out;
  }
}
.edit-site-patterns__section-header .edit-site-patterns__title{
  min-height:40px;
}
.edit-site-patterns__section-header .edit-site-patterns__title .components-heading{
  flex-basis:0;
  flex-grow:1;
  white-space:nowrap;
}
.edit-site-patterns__section-header .edit-site-patterns__sub-title{
  margin-bottom:8px;
}
.edit-site-patterns__section-header .screen-reader-shortcut:focus{
  top:0;
}

.edit-site-page-patterns-dataviews .dataviews-view-grid__badge-fields .dataviews-view-grid__field-value:has(.edit-site-patterns__field-sync-status-fully){
  background:rgba(var(--wp-block-synced-color--rgb), .04);
  color:var(--wp-block-synced-color);
}

.dataviews-action-modal__duplicate-pattern [role=dialog]>[role=document]{
  width:350px;
}
.dataviews-action-modal__duplicate-pattern .patterns-menu-items__convert-modal-categories{
  position:relative;
}
.dataviews-action-modal__duplicate-pattern .components-form-token-field__suggestions-list:not(:empty){
  background-color:#fff;
  border:1px solid var(--wp-admin-theme-color);
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
  box-shadow:0 0 .5px .5px var(--wp-admin-theme-color);
  box-sizing:border-box;
  max-height:96px;
  min-width:auto;
  position:absolute;
  right:-1px;
  width:calc(100% + 2px);
  z-index:1;
}

@media (min-width:600px){
  .dataviews-action-modal__duplicate-template-part .components-modal__frame{
    max-width:500px;
  }
}

@container (max-width: 430px){
  .edit-site-page-patterns-dataviews .edit-site-patterns__section-header{
    padding-left:24px;
    padding-right:24px;
  }
}
.page-templates-preview-field{
  align-items:center;
  border-radius:4px;
  display:flex;
  flex-direction:column;
  height:100%;
  justify-content:center;
  width:100%;
}
.dataviews-view-list .page-templates-preview-field .block-editor-block-preview__container{
  height:120px;
}
.dataviews-view-grid .page-templates-preview-field .block-editor-block-preview__container{
  height:100%;
}
.dataviews-view-table .page-templates-preview-field{
  max-height:160px;
  position:relative;
  text-wrap:balance;
  text-wrap:pretty;
  width:120px;
}
.dataviews-view-table .page-templates-preview-field:after{
  border-radius:4px;
  box-shadow:inset 0 0 0 1px #0000001a;
  content:"";
  height:100%;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}

.page-templates-description{
  max-width:50em;
  text-wrap:balance;
  text-wrap:pretty;
}
.dataviews-view-table .page-templates-description{
  display:block;
  margin-bottom:8px;
}

.edit-site-page-templates .dataviews-pagination{
  z-index:2;
}

.page-templates-author-field__avatar{
  align-items:center;
  display:flex;
  flex-shrink:0;
  height:24px;
  justify-content:right;
  overflow:hidden;
  width:24px;
}
.page-templates-author-field__avatar img{
  border-radius:100%;
  height:16px;
  object-fit:cover;
  opacity:0;
  width:16px;
}
@media not (prefers-reduced-motion){
  .page-templates-author-field__avatar img{
    transition:opacity .1s linear;
  }
}
.page-templates-author-field__avatar.is-loaded img{
  opacity:1;
}

.page-templates-author-field__icon{
  display:flex;
  flex-shrink:0;
  height:24px;
  width:24px;
}
.page-templates-author-field__icon svg{
  margin-right:-4px;
  fill:currentColor;
}

.page-templates-author-field__name{
  overflow:hidden;
  text-overflow:ellipsis;
}

.edit-site-list__rename-modal{
  z-index:1000001;
}
@media (min-width:782px){
  .edit-site-list__rename-modal .components-base-control{
    width:320px;
  }
}

.edit-site-editor__editor-interface{
  opacity:1;
}
@media not (prefers-reduced-motion){
  .edit-site-editor__editor-interface{
    transition:opacity .1s ease-out;
  }
}
.edit-site-editor__editor-interface.is-loading{
  opacity:0;
}

.edit-site-editor__toggle-save-panel{
  background-color:#fff;
  border:1px dotted #ddd;
  box-sizing:border-box;
  display:flex;
  justify-content:center;
  padding:24px;
  width:280px;
}

.edit-site-editor__view-mode-toggle{
  view-transition-name:toggle;
  height:60px;
  right:0;
  top:0;
  width:60px;
  z-index:100;
}
.edit-site-editor__view-mode-toggle .components-button{
  align-items:center;
  border-radius:0;
  color:#fff;
  display:flex;
  height:100%;
  justify-content:center;
  overflow:hidden;
  padding:0;
  width:100%;
}
.edit-site-editor__view-mode-toggle .components-button:active,.edit-site-editor__view-mode-toggle .components-button:hover{
  color:#fff;
}
.edit-site-editor__view-mode-toggle .components-button:focus{
  box-shadow:none;
}
.edit-site-editor__view-mode-toggle .edit-site-editor__view-mode-toggle-icon img,.edit-site-editor__view-mode-toggle .edit-site-editor__view-mode-toggle-icon svg{
  background:#1e1e1e;
  display:block;
}

.edit-site-editor__back-icon{
  align-items:center;
  background-color:#ccc;
  display:flex;
  height:60px;
  justify-content:center;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
  width:60px;
}
.edit-site-editor__back-icon svg{
  fill:currentColor;
}
.edit-site-editor__back-icon.has-site-icon{
  -webkit-backdrop-filter:saturate(180%) blur(15px);
  backdrop-filter:saturate(180%) blur(15px);
  background-color:#fff9;
}

.edit-site-welcome-guide{
  width:312px;
}
.edit-site-welcome-guide.guide-editor .edit-site-welcome-guide__image,.edit-site-welcome-guide.guide-styles .edit-site-welcome-guide__image{
  background:#00a0d2;
}
.edit-site-welcome-guide.guide-page .edit-site-welcome-guide__video{
  border-left:16px solid #3858e9;
  border-top:16px solid #3858e9;
}
.edit-site-welcome-guide.guide-template .edit-site-welcome-guide__video{
  border-right:16px solid #3858e9;
  border-top:16px solid #3858e9;
}
.edit-site-welcome-guide__image{
  margin:0 0 16px;
}
.edit-site-welcome-guide__image>img{
  display:block;
  max-width:100%;
  object-fit:cover;
}
.edit-site-welcome-guide__heading{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:24px;
  line-height:1.4;
  margin:16px 0;
  padding:0 32px;
}
.edit-site-welcome-guide__text{
  font-size:13px;
  line-height:1.4;
  margin:0 0 16px;
  padding:0 32px;
}
.edit-site-welcome-guide__text img{
  vertical-align:bottom;
}
.edit-site-welcome-guide__inserter-icon{
  margin:0 4px;
  vertical-align:text-top;
}

.edit-site-layout{
  color:#ccc;
  display:flex;
  flex-direction:column;
  height:100%;
}
.edit-site-layout,.edit-site-layout:not(.is-full-canvas) .editor-visual-editor{
  background:#1e1e1e;
}

.edit-site-layout__content{
  display:flex;
  flex-grow:1;
  height:100%;
}

.edit-site-layout__sidebar-region{
  flex-shrink:0;
  width:100vw;
  z-index:1;
}
@media (min-width:782px){
  .edit-site-layout__sidebar-region{
    width:300px;
  }
}
.edit-site-layout.is-full-canvas .edit-site-layout__sidebar-region{
  height:100vh;
  position:fixed !important;
  right:0;
  top:0;
}
.edit-site-layout__sidebar-region .edit-site-layout__sidebar{
  display:flex;
  flex-direction:column;
  height:100%;
}
.edit-site-layout__sidebar-region .resizable-editor__drag-handle{
  left:0;
}

.edit-site-layout__main{
  display:flex;
  flex-direction:column;
  flex-grow:1;
  overflow:hidden;
}

.edit-site-layout__mobile{
  display:flex;
  flex-direction:column;
  position:relative;
  width:100%;
  z-index:2;
}
.edit-site-layout__mobile .edit-site-sidebar__screen-wrapper{
  padding:0;
}
.edit-site-layout__mobile .edit-site-sidebar-navigation-screen__main{
  padding:0 12px;
}

.edit-site-layout__canvas-container{
  flex-grow:1;
  overflow:visible;
  position:relative;
  z-index:2;
}
.edit-site-layout__canvas-container.is-resizing:after{
  bottom:0;
  content:"";
  left:0;
  position:absolute;
  right:0;
  top:0;
  z-index:100;
}

.edit-site-layout__canvas{
  align-items:center;
  bottom:0;
  display:flex;
  justify-content:center;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}
.edit-site-layout__canvas.is-right-aligned{
  justify-content:flex-end;
}
.edit-site-layout__canvas .edit-site-resizable-frame__inner{
  color:#1e1e1e;
}
@media (min-width:782px){
  .edit-site-layout__canvas{
    bottom:16px;
    top:16px;
    width:calc(100% - 16px);
  }
  .edit-site-layout__canvas .edit-site-resizable-frame__inner-content{
    box-shadow:0 1px 1px #00000008,0 1px 2px #00000005,0 3px 3px #00000005,0 4px 4px #00000003;
    overflow:hidden;
  }
}
@media (min-width:782px) and (not (prefers-reduced-motion)){
  .edit-site-layout__canvas .edit-site-resizable-frame__inner-content{
    transition:border-radius,box-shadow .4s;
  }
}
@media (min-width:782px){
  .edit-site-layout:not(.is-full-canvas) .edit-site-layout__canvas .edit-site-resizable-frame__inner-content{
    border-radius:8px;
  }
  .edit-site-layout__canvas .edit-site-resizable-frame__inner-content:hover{
    box-shadow:0 5px 15px #00000014,0 15px 27px #00000012,0 30px 36px #0000000a,0 50px 43px #00000005;
  }
}
.edit-site-layout.is-full-canvas .edit-site-layout__canvas{
  bottom:0;
  top:0;
  width:100%;
}

.edit-site-layout__canvas .interface-interface-skeleton,.edit-site-layout__mobile .interface-interface-skeleton,.edit-site-template-pages-preview .interface-interface-skeleton{
  min-height:100% !important;
  position:relative !important;
}

.edit-site-template-pages-preview{
  height:100%;
}
html.canvas-mode-edit-transition::view-transition-group(toggle){
  animation-delay:255ms;
}

@media (prefers-reduced-motion){
  ::view-transition-group(*),::view-transition-new(*),::view-transition-old(*){
    animation:none !important;
  }
}
.edit-site-layout.is-full-canvas .edit-site-layout__sidebar-region .edit-site-layout__view-mode-toggle{
  display:none;
}

.edit-site-layout__view-mode-toggle.components-button{
  view-transition-name:toggle;
  align-items:center;
  background:#1e1e1e;
  border-radius:0;
  color:#fff;
  display:flex;
  height:60px;
  justify-content:center;
  overflow:hidden;
  padding:0;
  position:relative;
  width:60px;
}
.edit-site-layout__view-mode-toggle.components-button:active,.edit-site-layout__view-mode-toggle.components-button:hover{
  color:#fff;
}
.edit-site-layout__view-mode-toggle.components-button:focus,.edit-site-layout__view-mode-toggle.components-button:focus-visible{
  box-shadow:0 0 0 3px #1e1e1e, 0 0 0 6px var(--wp-admin-theme-color);
  outline:4px solid #0000;
  outline-offset:4px;
}
.edit-site-layout__view-mode-toggle.components-button:before{
  border-radius:4px;
  bottom:9px;
  box-shadow:none;
  content:"";
  display:block;
  left:9px;
  position:absolute;
  right:9px;
  top:9px;
}
@media not (prefers-reduced-motion){
  .edit-site-layout__view-mode-toggle.components-button:before{
    transition:box-shadow .1s ease;
  }
}
.edit-site-layout__view-mode-toggle.components-button .edit-site-layout__view-mode-toggle-icon{
  align-items:center;
  display:flex;
  height:60px;
  justify-content:center;
  width:60px;
}

.edit-site-layout__actions{
  background:#fff;
  bottom:auto;
  color:#1e1e1e;
  left:0;
  position:fixed !important;
  right:auto;
  top:-9999em;
  width:280px;
  z-index:100000;
}
.edit-site-layout__actions:focus,.edit-site-layout__actions:focus-within{
  bottom:0;
  top:auto;
}
.edit-site-layout__actions.is-entity-save-view-open:focus,.edit-site-layout__actions.is-entity-save-view-open:focus-within{
  top:0;
}
@media (min-width:782px){
  .edit-site-layout__actions{
    border-right:1px solid #ddd;
  }
}

.edit-site-layout__area{
  box-shadow:0 1px 1px #00000008,0 1px 2px #00000005,0 3px 3px #00000005,0 4px 4px #00000003;
  flex-grow:1;
  margin:0;
  overflow:hidden;
}
@media (min-width:782px){
  .edit-site-layout__area{
    border-radius:8px;
    margin:16px 0 16px 16px;
  }
}

.edit-site .components-editor-notices__snackbar{
  bottom:16px;
  left:0;
  padding-left:16px;
  padding-right:16px;
  position:fixed;
}

.edit-site-save-hub{
  border-top:1px solid #2f2f2f;
  color:#949494;
  flex-shrink:0;
  margin:0;
  padding:16px;
}

.edit-site-save-hub__button{
  color:inherit;
  justify-content:center;
  width:100%;
}
.edit-site-save-hub__button[aria-disabled=true]{
  opacity:1;
}
.edit-site-save-hub__button[aria-disabled=true]:hover{
  color:inherit;
}
.edit-site-save-hub__button:not(.is-primary).is-busy,.edit-site-save-hub__button:not(.is-primary).is-busy[aria-disabled=true]:hover{
  color:#1e1e1e;
}

@media (min-width:600px){
  .edit-site-save-panel__modal{
    width:600px;
  }
}

.edit-site-sidebar__content{
  contain:content;
  flex-grow:1;
  overflow-x:hidden;
  overflow-y:auto;
}

@keyframes _x51ri_slide-from-right{
  0%{
    opacity:0;
    transform:translateX(-50px);
  }
  to{
    opacity:1;
    transform:none;
  }
}
@keyframes _x51ri_slide-from-left{
  0%{
    opacity:0;
    transform:translateX(50px);
  }
  to{
    opacity:1;
    transform:none;
  }
}
.edit-site-sidebar__screen-wrapper{
  animation-duration:.14s;
  animation-timing-function:ease-in-out;
  display:flex;
  flex-direction:column;
  height:100%;
  max-height:100%;
  overflow-x:auto;
  padding:0 12px;
  scrollbar-color:#0000 #0000;
  scrollbar-gutter:stable both-edges;
  scrollbar-gutter:stable;
  scrollbar-width:thin;
  will-change:transform;
  will-change:transform, opacity;
}
.edit-site-sidebar__screen-wrapper::-webkit-scrollbar{
  height:12px;
  width:12px;
}
.edit-site-sidebar__screen-wrapper::-webkit-scrollbar-track{
  background-color:initial;
}
.edit-site-sidebar__screen-wrapper::-webkit-scrollbar-thumb{
  background-clip:padding-box;
  background-color:initial;
  border:3px solid #0000;
  border-radius:8px;
}
.edit-site-sidebar__screen-wrapper:focus-within::-webkit-scrollbar-thumb,.edit-site-sidebar__screen-wrapper:focus::-webkit-scrollbar-thumb,.edit-site-sidebar__screen-wrapper:hover::-webkit-scrollbar-thumb{
  background-color:#757575;
}
.edit-site-sidebar__screen-wrapper:focus,.edit-site-sidebar__screen-wrapper:focus-within,.edit-site-sidebar__screen-wrapper:hover{
  scrollbar-color:#757575 #0000;
}
@media (hover:none){
  .edit-site-sidebar__screen-wrapper{
    scrollbar-color:#757575 #0000;
  }
}
@media (prefers-reduced-motion:reduce){
  .edit-site-sidebar__screen-wrapper{
    animation-duration:0s;
  }
}
.edit-site-sidebar__screen-wrapper.slide-from-left{
  animation-name:_x51ri_slide-from-left;
}
.edit-site-sidebar__screen-wrapper.slide-from-right{
  animation-name:_x51ri_slide-from-right;
}

.edit-site-sidebar-button{
  color:#e0e0e0;
  flex-shrink:0;
}
.edit-site-sidebar-button:focus:not(:disabled){
  box-shadow:none;
  outline:none;
}
.edit-site-sidebar-button:focus-visible:not(:disabled){
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:3px solid #0000;
}
.edit-site-sidebar-button:focus,.edit-site-sidebar-button:focus-visible,.edit-site-sidebar-button:hover:not(:disabled,[aria-disabled=true]),.edit-site-sidebar-button:not(:disabled,[aria-disabled=true]):active,.edit-site-sidebar-button[aria-expanded=true]{
  color:#f0f0f0;
}

.edit-site-sidebar-navigation-item.components-item{
  border:none;
  color:#949494;
  min-height:40px;
  padding:8px 16px 8px 6px;
}
.edit-site-sidebar-navigation-item.components-item:focus,.edit-site-sidebar-navigation-item.components-item:hover,.edit-site-sidebar-navigation-item.components-item[aria-current=true]{
  color:#e0e0e0;
}
.edit-site-sidebar-navigation-item.components-item:focus .edit-site-sidebar-navigation-item__drilldown-indicator,.edit-site-sidebar-navigation-item.components-item:hover .edit-site-sidebar-navigation-item__drilldown-indicator,.edit-site-sidebar-navigation-item.components-item[aria-current=true] .edit-site-sidebar-navigation-item__drilldown-indicator{
  fill:#e0e0e0;
}
.edit-site-sidebar-navigation-item.components-item[aria-current=true]{
  background:#2f2f2f;
  color:#fff;
  font-weight:500;
}
.edit-site-sidebar-navigation-item.components-item:focus-visible{
  transform:translateZ(0);
}
.edit-site-sidebar-navigation-item.components-item .edit-site-sidebar-navigation-item__drilldown-indicator{
  fill:#949494;
}
.edit-site-sidebar-navigation-item.components-item.with-suffix{
  padding-left:16px;
}

.edit-site-sidebar-navigation-screen__content .block-editor-list-view-block-select-button{
  cursor:grab;
  padding:8px 0 8px 8px;
}

.edit-site-sidebar-navigation-screen{
  display:flex;
  flex-direction:column;
  overflow-x:unset !important;
  position:relative;
}

.edit-site-sidebar-navigation-screen__main{
  flex-grow:1;
  margin-bottom:16px;
}
.edit-site-sidebar-navigation-screen__main.has-footer{
  margin-bottom:0;
}

.edit-site-sidebar-navigation-screen__content{
  padding:0 16px;
}
.edit-site-sidebar-navigation-screen__content .components-text{
  color:#ccc;
}
.edit-site-sidebar-navigation-screen__content .components-heading{
  margin-bottom:8px;
}

.edit-site-sidebar-navigation-screen__title-icon{
  background:#1e1e1e;
  margin-bottom:8px;
  padding-bottom:8px;
  padding-top:48px;
  position:sticky;
  top:0;
  z-index:1;
}

.edit-site-sidebar-navigation-screen__title{
  flex-grow:1;
  overflow-wrap:break-word;
}
.edit-site-sidebar-navigation-screen__title.edit-site-sidebar-navigation-screen__title,.edit-site-sidebar-navigation-screen__title.edit-site-sidebar-navigation-screen__title .edit-site-sidebar-navigation-screen__title{
  line-height:32px;
}

.edit-site-sidebar-navigation-screen__actions{
  display:flex;
  flex-shrink:0;
}

@media (min-width:782px){
  .edit-site-sidebar-navigation-screen__content .edit-site-global-styles-variation-container{
    max-width:292px;
  }
}

.edit-site-global-styles-variation-title{
  color:#ddd;
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}

.edit-site-sidebar-navigation-screen__content .edit-site-global-styles-variations_item .edit-site-global-styles-variations_item-preview{
  outline-color:#ffffff0d;
}
.edit-site-sidebar-navigation-screen__content .edit-site-global-styles-variations_item:not(.is-active):hover .edit-site-global-styles-variations_item-preview{
  outline-color:#ffffff26;
}
.edit-site-sidebar-navigation-screen__content .edit-site-global-styles-variations_item.is-active .edit-site-global-styles-variations_item-preview{
  outline-color:#fff;
}
.edit-site-sidebar-navigation-screen__content .edit-site-global-styles-variations_item:focus-visible .edit-site-global-styles-variations_item-preview{
  outline-color:var(--wp-admin-theme-color);
}

.edit-site-sidebar-navigation-screen__footer{
  background-color:#1e1e1e;
  border-top:1px solid #2f2f2f;
  bottom:0;
  gap:0;
  margin:16px 0 0;
  padding:8px 16px;
  position:sticky;
}
.edit-site-sidebar-navigation-screen__footer .edit-site-sidebar-navigation-screen-details-footer{
  margin-left:-16px;
  margin-right:-16px;
}
.edit-site-sidebar-navigation-screen__input-control{
  width:100%;
}
.edit-site-sidebar-navigation-screen__input-control .components-input-control__container{
  background:#2f2f2f;
}
.edit-site-sidebar-navigation-screen__input-control .components-input-control__container .components-button{
  color:#e0e0e0 !important;
}
.edit-site-sidebar-navigation-screen__input-control .components-input-control__input{
  background:#2f2f2f !important;
  color:#e0e0e0 !important;
}
.edit-site-sidebar-navigation-screen__input-control .components-input-control__backdrop{
  border:4px !important;
}
.edit-site-sidebar-navigation-screen__input-control .components-base-control__help{
  color:#949494;
}

.edit-site-sidebar-navigation-screen-details-footer div.edit-site-sidebar-navigation-item.components-item:focus,.edit-site-sidebar-navigation-screen-details-footer div.edit-site-sidebar-navigation-item.components-item:hover,.edit-site-sidebar-navigation-screen-details-footer div.edit-site-sidebar-navigation-item.components-item[aria-current]{
  background:none;
}

.sidebar-navigation__more-menu .components-button{
  color:#e0e0e0;
}
.sidebar-navigation__more-menu .components-button:focus,.sidebar-navigation__more-menu .components-button:hover,.sidebar-navigation__more-menu .components-button[aria-current]{
  color:#f0f0f0;
}

.edit-site-sidebar-navigation-screen-patterns__group{
  margin-bottom:24px;
  margin-left:-16px;
  margin-right:-16px;
}
.edit-site-sidebar-navigation-screen-patterns__group:last-of-type{
  border-bottom:0;
  margin-bottom:0;
  padding-bottom:0;
}

.edit-site-sidebar-navigation-screen-patterns__group-header{
  margin-top:16px;
}
.edit-site-sidebar-navigation-screen-patterns__group-header p{
  color:#949494;
}
.edit-site-sidebar-navigation-screen-patterns__group-header h2{
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}

.edit-site-sidebar-navigation-screen-patterns__divider{
  border-top:1px solid #2f2f2f;
  margin:16px 0;
}

.edit-site-sidebar-navigation-screen__description{
  margin:0 0 32px;
}

.edit-site-sidebar-navigation-screen-navigation-menus{
  margin-left:-16px;
  margin-right:-16px;
}

.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-leaf .block-editor-list-view-block__contents-cell{
  width:100%;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-leaf .block-editor-list-view-block-contents{
  white-space:normal;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block__title{
  margin-top:3px;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block__menu-cell{
  padding-left:0;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block-select-button{
  color:#949494;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block-select-button:focus,.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block-select-button:hover,.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block-select-button[aria-current]{
  color:#fff;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block__menu{
  color:#949494;
}
.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block__menu:focus,.edit-site-sidebar-navigation-screen-navigation-menus__content .block-editor-list-view-block__menu:hover{
  color:#fff;
}

.edit-site-sidebar-navigation-screen-navigation-menus__loading.components-spinner{
  display:block;
  margin-left:auto;
  margin-right:auto;
}

.edit-site-sidebar-navigation-screen-navigation-menus__helper-block-editor{
  display:none;
}

.edit-site-sidebar-navigation-screen-main,.edit-site-sidebar-navigation-screen-templates-browse{
  margin-left:-16px;
  margin-right:-16px;
}

.edit-site-sidebar-navigation-screen-dataviews__group-header{
  margin-top:32px;
}
.edit-site-sidebar-navigation-screen-dataviews__group-header h2{
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}

.edit-site-sidebar-dataviews{
  margin-left:-16px;
  margin-right:-16px;
}

.edit-site-sidebar-navigation-screen-dataviews__custom-items .edit-site-sidebar-dataviews-dataview-item{
  padding-left:8px;
}

.edit-site-sidebar-dataviews-dataview-item{
  border-radius:2px;
}
.edit-site-sidebar-dataviews-dataview-item .edit-site-sidebar-dataviews-dataview-item__dropdown-menu{
  min-width:auto;
}
.edit-site-sidebar-dataviews-dataview-item:focus,.edit-site-sidebar-dataviews-dataview-item:hover,.edit-site-sidebar-dataviews-dataview-item[aria-current]{
  color:#e0e0e0;
}
.edit-site-sidebar-dataviews-dataview-item.is-selected{
  background:#2f2f2f;
  color:#fff;
  font-weight:500;
}

.edit-site-site-hub{
  align-items:center;
  display:flex;
  gap:8px;
  height:60px;
  justify-content:space-between;
  margin-left:12px;
}

.edit-site-site-hub__actions{
  flex-shrink:0;
}

.edit-site-site-hub__view-mode-toggle-container{
  flex-shrink:0;
  height:60px;
  width:60px;
}
.edit-site-site-hub__view-mode-toggle-container.has-transparent-background .edit-site-layout__view-mode-toggle-icon{
  background:#0000;
}

.edit-site-site-hub__title .components-button{
  color:#e0e0e0;
  display:block;
  flex-grow:1;
  font-size:15px;
  font-weight:500;
  margin-right:-4px;
  overflow:hidden;
  padding-left:16px;
  position:relative;
  text-decoration:none;
  text-overflow:ellipsis;
  white-space:nowrap;
}
.edit-site-site-hub__title .components-button:active,.edit-site-site-hub__title .components-button:focus,.edit-site-site-hub__title .components-button:hover{
  color:#e0e0e0;
}
.edit-site-site-hub__title .components-button:focus{
  box-shadow:none;
  outline:none;
}
.edit-site-site-hub__title .components-button:focus-visible{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #1e1e1e, 0 0 0 calc(var(--wp-admin-border-width-focus)*2) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:2px;
}
.edit-site-site-hub__title .components-button:after{
  content:"↗";
  font-weight:400;
  left:0;
  opacity:0;
  position:absolute;
}
@media not (prefers-reduced-motion){
  .edit-site-site-hub__title .components-button:after{
    transition:opacity .1s linear;
  }
}
.edit-site-site-hub__title .components-button:active:after,.edit-site-site-hub__title .components-button:focus:after,.edit-site-site-hub__title .components-button:hover:after{
  opacity:1;
}

.edit-site-site-hub_toggle-command-center{
  color:#e0e0e0;
}
.edit-site-site-hub_toggle-command-center:active svg,.edit-site-site-hub_toggle-command-center:hover svg{
  fill:#f0f0f0;
}

.edit-site-site-icon__icon{
  fill:currentColor;
  height:100%;
  width:100%;
}
.edit-site-layout.is-full-canvas .edit-site-site-icon__icon{
  padding:12px;
}

.edit-site-site-icon__image{
  aspect-ratio:1/1;
  background:#333;
  height:100%;
  object-fit:cover;
  width:100%;
}
.edit-site-layout.is-full-canvas .edit-site-site-icon__image{
  border-radius:0;
}

.edit-site-editor__view-mode-toggle button:focus{
  position:relative;
}
.edit-site-editor__view-mode-toggle button:focus:before{
  bottom:0;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color), inset 0 0 0 calc(1px + var(--wp-admin-border-width-focus)) #fff;
  content:"";
  display:block;
  left:0;
  position:absolute;
  right:0;
  top:0;
  z-index:1;
}

.edit-site-style-book{
  align-items:stretch;
  display:flex;
  flex-direction:column;
  height:100%;
}
.edit-site-style-book.is-button{
  border-radius:8px;
}

.edit-site-style-book__iframe{
  display:block;
  height:100%;
  width:100%;
}
.edit-site-style-book__iframe.is-button{
  border-radius:8px;
}
.edit-site-style-book__iframe.is-focused{
  outline:calc(var(--wp-admin-border-width-focus)*2) solid var(--wp-admin-theme-color);
  outline-offset:calc(var(--wp-admin-border-width-focus)*-2);
}

.edit-site-style-book__tablist-container{
  background:#fff;
  display:flex;
  flex:none;
  padding-left:56px;
  width:100%;
}

.edit-site-style-book__tabpanel{
  flex:1 0 auto;
  overflow:auto;
}

.edit-site-editor-canvas-container{
  background-color:#ddd;
  height:100%;
}
.edit-site-editor-canvas-container iframe{
  display:block;
  height:100%;
  width:100%;
}
.edit-site-layout.is-full-canvas .edit-site-editor-canvas-container{
  padding:24px 24px 0;
}

.edit-site-editor-canvas-container__section{
  background:#fff;
  border-radius:8px;
  bottom:0;
  left:0;
  overflow:hidden;
  position:absolute;
  right:0;
  top:0;
}
@media not (prefers-reduced-motion){
  .edit-site-editor-canvas-container__section{
    transition:all .3s;
  }
}

.edit-site-editor-canvas-container__close-button{
  background:#fff;
  left:8px;
  position:absolute;
  top:8px;
  z-index:2;
}

.edit-site-post-edit{
  padding:24px;
}
.edit-site-post-edit.is-empty .edit-site-page-content{
  align-items:center;
  display:flex;
  justify-content:center;
}

.dataforms-layouts-panel__field-dropdown .fields-controls__password{
  border-top:1px solid #e0e0e0;
  padding-top:16px;
}

.edit-site-post-list__featured-image{
  height:100%;
  object-fit:cover;
  width:100%;
}

.edit-site-post-list__featured-image-wrapper{
  border-radius:4px;
  height:100%;
  width:100%;
}
.edit-site-post-list__featured-image-wrapper.is-layout-table .edit-site-post-list__featured-image-button,.edit-site-post-list__featured-image-wrapper.is-layout-table:not(:has(.edit-site-post-list__featured-image-button)){
  background-color:#f0f0f0;
  border-radius:4px;
  display:block;
  flex-grow:0 !important;
  height:32px;
  overflow:hidden;
  position:relative;
  width:32px;
}
.edit-site-post-list__featured-image-wrapper.is-layout-table .edit-site-post-list__featured-image-button:after,.edit-site-post-list__featured-image-wrapper.is-layout-table:not(:has(.edit-site-post-list__featured-image-button)):after{
  border-radius:4px;
  box-shadow:inset 0 0 0 1px #0000001a;
  content:"";
  height:100%;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}

.edit-site-post-list__featured-image-button{
  background-color:unset;
  border:none;
  border-radius:4px;
  box-shadow:none;
  box-sizing:border-box;
  cursor:pointer;
  height:100%;
  overflow:hidden;
  padding:0;
  width:100%;
}
.edit-site-post-list__featured-image-button:focus-visible{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}

.dataviews-view-grid__card.is-selected .edit-site-post-list__featured-image-button:after{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}

.edit-site-post-list__status-icon{
  height:24px;
  width:24px;
}
.edit-site-post-list__status-icon svg{
  fill:currentColor;
  margin-right:-4px;
}

.edit-site-resizable-frame__inner{
  position:relative;
}
body:has(.edit-site-resizable-frame__inner.is-resizing){
  cursor:col-resize;
  user-select:none;
  -webkit-user-select:none;
}

.edit-site-resizable-frame__inner.is-resizing:before{
  content:"";
  inset:0;
  position:absolute;
  z-index:1;
}

.edit-site-resizable-frame__inner-content{
  inset:0;
  position:absolute;
  z-index:0;
}

.edit-site-resizable-frame__handle{
  align-items:center;
  background-color:#75757566;
  border:0;
  border-radius:4px;
  cursor:col-resize;
  display:flex;
  height:64px;
  justify-content:flex-end;
  padding:0;
  position:absolute;
  top:calc(50% - 32px);
  width:4px;
  z-index:100;
}
.edit-site-resizable-frame__handle:before{
  content:"";
  height:100%;
  position:absolute;
  right:100%;
  width:32px;
}
.edit-site-resizable-frame__handle:after{
  content:"";
  height:100%;
  left:100%;
  position:absolute;
  width:32px;
}
.edit-site-resizable-frame__handle:focus-visible{
  outline:2px solid #0000;
}
.edit-site-resizable-frame__handle.is-resizing,.edit-site-resizable-frame__handle:focus,.edit-site-resizable-frame__handle:hover{
  background-color:var(--wp-admin-theme-color);
}

.edit-site-push-changes-to-global-styles-control .components-button{
  justify-content:center;
  width:100%;
}

@media (min-width:782px){
  .font-library-modal.font-library-modal{
    width:65vw;
  }
}
.font-library-modal .components-modal__header{
  border-bottom:none;
}
.font-library-modal .components-modal__content{
  margin-bottom:70px;
  padding-top:0;
}
.font-library-modal .font-library-modal__subtitle{
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}
.font-library-modal .components-navigator-screen{
  padding:3px;
}

.font-library-modal__tabpanel-layout{
  margin-top:32px;
}
.font-library-modal__tabpanel-layout .font-library-modal__loading{
  align-items:center;
  display:flex;
  height:100%;
  justify-content:center;
  padding-top:120px;
  position:absolute;
  right:0;
  top:0;
  width:100%;
}

.font-library-modal__footer{
  background-color:#fff;
  border-top:1px solid #ddd;
  bottom:32px;
  height:70px;
  margin:0 -32px -32px;
  padding:16px 32px;
  position:absolute;
  width:100%;
}

.font-library-modal__page-selection{
  font-size:11px;
  font-weight:500;
  text-transform:uppercase;
}
@media (min-width:600px){
  .font-library-modal__page-selection .components-select-control__input{
    font-size:11px !important;
    font-weight:500;
  }
}

.font-library-modal__tabpanel-layout .components-base-control__field{
  margin-bottom:0;
}

.font-library-modal__fonts-title{
  font-size:11px;
  font-weight:600;
  text-transform:uppercase;
}

.font-library-modal__fonts-list,.font-library-modal__fonts-title{
  margin-bottom:0;
  margin-top:0;
}

.font-library-modal__fonts-list-item{
  margin-bottom:0;
}

.font-library-modal__font-card{
  border:1px solid #e0e0e0;
  height:auto !important;
  margin-top:-1px;
  padding:16px;
  width:100%;
}
.font-library-modal__font-card:hover{
  background-color:#f0f0f0;
}
.font-library-modal__font-card .font-library-modal__font-card__name{
  font-weight:700;
}
.font-library-modal__font-card .font-library-modal__font-card__count{
  color:#757575;
}
.font-library-modal__font-card .font-library-modal__font-variant_demo-image{
  display:block;
  height:24px;
  width:auto;
}
.font-library-modal__font-card .font-library-modal__font-variant_demo-text{
  flex-shrink:0;
  white-space:nowrap;
}
@media not (prefers-reduced-motion){
  .font-library-modal__font-card .font-library-modal__font-variant_demo-text{
    transition:opacity .3s ease-in-out;
  }
}

.font-library-modal__font-variant{
  border-bottom:1px solid #e0e0e0;
  padding-bottom:16px;
}

.font-library-modal__tablist-container{
  background:#fff;
  border-bottom:1px solid #ddd;
  margin:0 -32px;
  padding:0 16px;
  position:sticky;
  top:0;
  z-index:1;
}
.font-library-modal__tablist-container [role=tablist]{
  margin-bottom:-1px;
}

.font-library-modal__upload-area{
  align-items:center;
  display:flex;
  height:256px !important;
  justify-content:center;
  width:100%;
}

button.font-library-modal__upload-area{
  background-color:#f0f0f0;
}

.font-library-modal__local-fonts{
  margin:0 auto;
  width:80%;
}
.font-library-modal__local-fonts .font-library-modal__upload-area__text{
  color:#757575;
}

.font-library__google-fonts-confirm{
  align-items:center;
  display:flex;
  justify-content:center;
  margin-top:64px;
}
.font-library__google-fonts-confirm p{
  line-height:1.4;
}
.font-library__google-fonts-confirm h2{
  font-size:1.2rem;
  font-weight:400;
}
.font-library__google-fonts-confirm .components-card{
  padding:16px;
  width:400px;
}
.font-library__google-fonts-confirm .components-button{
  justify-content:center;
  width:100%;
}

.font-library-modal__select-all{
  padding:16px 17px 16px 16px;
}
.font-library-modal__select-all .components-checkbox-control__label{
  padding-right:16px;
}

.edit-site-pagination .components-button.is-tertiary{
  height:32px;
  justify-content:center;
  width:32px;
}

.edit-site-global-styles-variations_item{
  box-sizing:border-box;
  cursor:pointer;
}
.edit-site-global-styles-variations_item .edit-site-global-styles-variations_item-preview{
  border-radius:2px;
  outline:1px solid #0000001a;
  outline-offset:-1px;
  overflow:hidden;
  position:relative;
}
@media not (prefers-reduced-motion){
  .edit-site-global-styles-variations_item .edit-site-global-styles-variations_item-preview{
    transition:outline .1s linear;
  }
}
.edit-site-global-styles-variations_item .edit-site-global-styles-variations_item-preview.is-pill{
  height:32px;
}
.edit-site-global-styles-variations_item .edit-site-global-styles-variations_item-preview.is-pill .block-editor-iframe__scale-container{
  overflow:hidden;
}
.edit-site-global-styles-variations_item:not(.is-active):hover .edit-site-global-styles-variations_item-preview{
  outline-color:#0000004d;
}
.edit-site-global-styles-variations_item.is-active .edit-site-global-styles-variations_item-preview,.edit-site-global-styles-variations_item:focus-visible .edit-site-global-styles-variations_item-preview{
  outline-color:#1e1e1e;
  outline-offset:1px;
  outline-width:var(--wp-admin-border-width-focus);
}
.edit-site-global-styles-variations_item:focus-visible .edit-site-global-styles-variations_item-preview{
  outline-color:var(--wp-admin-theme-color);
}

.edit-site-styles .edit-site-page-content .edit-site-global-styles-screen-root{
  box-shadow:none;
}
.edit-site-styles .edit-site-page-content .edit-site-global-styles-screen-root>div>hr{
  display:none;
}
.edit-site-styles .edit-site-page-content .edit-site-global-styles-sidebar__navigator-provider{
  overflow-y:auto;
  padding-left:0;
  padding-right:0;
}
.edit-site-styles .edit-site-page-content .edit-site-global-styles-sidebar__navigator-provider .components-tools-panel{
  border-top:none;
}
.edit-site-styles .edit-site-page-content .edit-site-global-styles-sidebar__navigator-provider .edit-site-global-styles-sidebar__navigator-screen{
  outline:none;
  padding:12px;
}
.edit-site-styles .edit-site-page-content .edit-site-page-header{
  padding-left:48px;
  padding-right:48px;
}
@container (max-width: 430px){
  .edit-site-styles .edit-site-page-content .edit-site-page-header{
    padding-left:24px;
    padding-right:24px;
  }
}
.edit-site-styles .edit-site-page-content .edit-site-sidebar-button{
  color:#1e1e1e;
}

.show-icon-labels .edit-site-styles .edit-site-page-content .edit-site-page-header__actions .components-button.has-icon{
  padding:0 8px;
  width:auto;
}
.show-icon-labels .edit-site-styles .edit-site-page-content .edit-site-page-header__actions .components-button.has-icon svg{
  display:none;
}
.show-icon-labels .edit-site-styles .edit-site-page-content .edit-site-page-header__actions .components-button.has-icon:after{
  content:attr(aria-label);
  font-size:12px;
}
::view-transition-image-pair(root){
  isolation:auto;
}

::view-transition-new(root),::view-transition-old(root){
  animation:none;
  display:block;
  mix-blend-mode:normal;
}
body.js #wpadminbar{
  display:none;
}

body.js #wpbody{
  padding-top:0;
}

body.js.appearance_page_gutenberg-template-parts,body.js.site-editor-php{
  background:#fff;
}
body.js.appearance_page_gutenberg-template-parts #wpcontent,body.js.site-editor-php #wpcontent{
  padding-right:0;
}
body.js.appearance_page_gutenberg-template-parts #wpbody-content,body.js.site-editor-php #wpbody-content{
  padding-bottom:0;
}
body.js.appearance_page_gutenberg-template-parts #wpbody-content>div:not(.edit-site):not(#screen-meta),body.js.appearance_page_gutenberg-template-parts #wpfooter,body.js.site-editor-php #wpbody-content>div:not(.edit-site):not(#screen-meta),body.js.site-editor-php #wpfooter{
  display:none;
}
body.js.appearance_page_gutenberg-template-parts .a11y-speak-region,body.js.site-editor-php .a11y-speak-region{
  right:-1px;
  top:-1px;
}
body.js.appearance_page_gutenberg-template-parts ul#adminmenu a.wp-has-current-submenu:after,body.js.appearance_page_gutenberg-template-parts ul#adminmenu>li.current>a.current:after,body.js.site-editor-php ul#adminmenu a.wp-has-current-submenu:after,body.js.site-editor-php ul#adminmenu>li.current>a.current:after{
  border-left-color:#fff;
}
body.js.appearance_page_gutenberg-template-parts .media-frame select.attachment-filters:last-of-type,body.js.site-editor-php .media-frame select.attachment-filters:last-of-type{
  max-width:100%;
  width:auto;
}

body.js.site-editor-php{
  background:#1e1e1e;
}

.edit-site{
  box-sizing:border-box;
  height:100vh;
}
.edit-site *,.edit-site :after,.edit-site :before{
  box-sizing:inherit;
}
@media (min-width:600px){
  .edit-site{
    bottom:0;
    left:0;
    min-height:100vh;
    position:fixed;
    right:0;
    top:0;
  }
}
.no-js .edit-site{
  min-height:0;
  position:static;
}
.edit-site .interface-interface-skeleton{
  top:0;
}

body.admin-color-light{
  --wp-admin-theme-color:#0085ba;
  --wp-admin-theme-color--rgb:0, 133, 186;
  --wp-admin-theme-color-darker-10:#0073a1;
  --wp-admin-theme-color-darker-10--rgb:0, 115, 161;
  --wp-admin-theme-color-darker-20:#006187;
  --wp-admin-theme-color-darker-20--rgb:0, 97, 135;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-light{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-modern{
  --wp-admin-theme-color:#3858e9;
  --wp-admin-theme-color--rgb:56, 88, 233;
  --wp-admin-theme-color-darker-10:#2145e6;
  --wp-admin-theme-color-darker-10--rgb:33, 69, 230;
  --wp-admin-theme-color-darker-20:#183ad6;
  --wp-admin-theme-color-darker-20--rgb:24, 58, 214;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-modern{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-blue{
  --wp-admin-theme-color:#096484;
  --wp-admin-theme-color--rgb:9, 100, 132;
  --wp-admin-theme-color-darker-10:#07526c;
  --wp-admin-theme-color-darker-10--rgb:7, 82, 108;
  --wp-admin-theme-color-darker-20:#064054;
  --wp-admin-theme-color-darker-20--rgb:6, 64, 84;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-blue{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-coffee{
  --wp-admin-theme-color:#46403c;
  --wp-admin-theme-color--rgb:70, 64, 60;
  --wp-admin-theme-color-darker-10:#383330;
  --wp-admin-theme-color-darker-10--rgb:56, 51, 48;
  --wp-admin-theme-color-darker-20:#2b2724;
  --wp-admin-theme-color-darker-20--rgb:43, 39, 36;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-coffee{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ectoplasm{
  --wp-admin-theme-color:#523f6d;
  --wp-admin-theme-color--rgb:82, 63, 109;
  --wp-admin-theme-color-darker-10:#46365d;
  --wp-admin-theme-color-darker-10--rgb:70, 54, 93;
  --wp-admin-theme-color-darker-20:#3a2c4d;
  --wp-admin-theme-color-darker-20--rgb:58, 44, 77;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ectoplasm{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-midnight{
  --wp-admin-theme-color:#e14d43;
  --wp-admin-theme-color--rgb:225, 77, 67;
  --wp-admin-theme-color-darker-10:#dd382d;
  --wp-admin-theme-color-darker-10--rgb:221, 56, 45;
  --wp-admin-theme-color-darker-20:#d02c21;
  --wp-admin-theme-color-darker-20--rgb:208, 44, 33;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-midnight{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ocean{
  --wp-admin-theme-color:#627c83;
  --wp-admin-theme-color--rgb:98, 124, 131;
  --wp-admin-theme-color-darker-10:#576e74;
  --wp-admin-theme-color-darker-10--rgb:87, 110, 116;
  --wp-admin-theme-color-darker-20:#4c6066;
  --wp-admin-theme-color-darker-20--rgb:76, 96, 102;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ocean{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-sunrise{
  --wp-admin-theme-color:#dd823b;
  --wp-admin-theme-color--rgb:221, 130, 59;
  --wp-admin-theme-color-darker-10:#d97426;
  --wp-admin-theme-color-darker-10--rgb:217, 116, 38;
  --wp-admin-theme-color-darker-20:#c36922;
  --wp-admin-theme-color-darker-20--rgb:195, 105, 34;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-sunrise{
    --wp-admin-border-width-focus:1.5px;
  }
}