import { Cancel } from 'axios';

export function getFetchErrorMessage(error) {
	if (error.response && error.response.data) {
		if (error.response.data === -1) {
			return 'The action you performed used a security code that has expired. Refresh the page and try again.'
		}
		if (error.response.data.data && error.response.data.data.message) {
			// An example of when this would occur is when using wp_send_json_error().
			return error.response.data.data.message;
		}
		return error.response.data.message;
	}
	return error.message;
}

export function showToastForFetchError(addErrorToast) {
	return function(error) {
		const errorMessage = getFetchErrorMessage(error);
		addErrorToast(errorMessage);
	};
}

export function isAbortError(error) {
	return error instanceof DOMException || error instanceof Cancel;
}
