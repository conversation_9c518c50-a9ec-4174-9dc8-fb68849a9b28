// Reminder: This page (https://cloud.google.com/blog/products/maps-platform/more-control-loading-maps-javascript-api)
// recommends using their new dynamic loader. I haven't yet done that. I'm loading it myself here. But it's something
// to consider in the future.

window.iFoundGlobal = window.iFoundGlobal || {};
(function(document, tag) {
	var loadGoogleMapsPromise = null;
	var librariesAlreadyLoaded = [];
	// The idea here is we can load Google maps only when needed, and we can even load extra libraries on demand. I
	// might not have written this perfectly, it might try to load libraries multiple times based on race conditions,
	// but I'm not going to worry about that because the Google Maps library itself won't actually make the network
	// call again if it has already loaded the library.
	window.iFoundGlobal.loadGoogleMaps = function(librariesDemanded) {
		if (!librariesDemanded) {
			librariesDemanded = [];
		}
		if (!loadGoogleMapsPromise) {
			loadGoogleMapsPromise = new Promise(function(resolve) {
				var scriptTag = document.createElement(tag);
				scriptTag.onload = resolve;
				var firstScriptTag = document.getElementsByTagName(tag)[0];
				var url = new URL(ifound_load_map.url);
				url.searchParams.set('key', ifound_load_map.key);
				if (librariesDemanded.length) {
					url.searchParams.set('libraries', librariesDemanded.join(','));
				}
				librariesAlreadyLoaded = librariesAlreadyLoaded.concat(librariesDemanded);
				scriptTag.src = url.toString()
				firstScriptTag.parentNode.insertBefore(scriptTag, firstScriptTag);
			});
		} else {
			var librariesStillToLoad = librariesDemanded.filter(function(x) {
				return !librariesAlreadyLoaded.includes(x);
			});
			if (librariesStillToLoad.length) {
				loadGoogleMapsPromise = loadGoogleMapsPromise.then(function() {
					var p = Promise.all(librariesStillToLoad.map(function(x) {
						return google.maps.importLibrary(x);
					}));
					librariesAlreadyLoaded = librariesAlreadyLoaded.concat(librariesStillToLoad);
					return p;
				})
			}
		}
		return loadGoogleMapsPromise;
	};
	window.iFoundGlobal.getLoadGoogleMapsPromise = function() {
		var isGoogleMapsLoadingOrLoaded = loadGoogleMapsPromise !== null;
		return loadGoogleMapsPromise || Promise.resolve(isGoogleMapsLoadingOrLoaded);
	}
	window.iFoundGlobal.sharedGoogleMapBaseOptions = {
		// Reminder: this isn't needed in older versions of Google Maps API. However, in the beta version,
		// (which will become the default soon enough), they're switching to the "camera control" as a
		// default, and no zoom buttons. We decided the zoom buttons are nice, so we'll have them also.
		zoomControl: true,
		// Hide the full screen control because in that view, clicking on the listing markers would only
		// affect things behind the map, meaning the user wouldn't see the effect, meaning it's useless
		// aor/or confusing.
		fullscreenControl: false,
	};
})(document, 'script');
