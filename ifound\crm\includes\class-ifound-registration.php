<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );


/**
 * iFoundRegistration Class
 *
 * @since 1.0.7
 */

class iFoundRegistration extends iFoundCrm {

	private $is_registered;
	private $under_limit;

	/**
	 * init iFoundRegistration class.
	 *
	 * @since 1.0.7
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.0.7
	 * @since 1.2.47 Big fix wp_footer hook for registration_form max priority 10.
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'cookie'), 1);
			add_action('init', array($this, 'register_contact_with_click'), 2);
			add_action('init', array($this, 'registration'), 9);
			add_action('wp_enqueue_scripts', array($this, 'registration_scripts'));
			add_action('wp_footer', array($this, 'registration_check'), 4);
			add_action('wp_footer', array($this, 'registration_cookie_script'), 5);

			add_action('ifound_account_already_form', array($this, 'account_already_form'));

			add_action('wp_ajax_account_already', array($this, 'account_already'));
			add_action('wp_ajax_nopriv_account_already', array($this, 'account_already'));
			add_action('wp_ajax_social_login', array($this, 'social_login'));
			add_action('wp_ajax_nopriv_social_login', array($this, 'social_login'));

			add_action('wp_footer', array($this, 'registration_form'));
		}
	}

	/**
	 * Registration Scripts
	 *
	 * @since 1.0.0
	 */

	public function registration_scripts() {

		wp_register_script( 'registration_js', plugins_url( 'js/registration.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		if (iFound::new_hookless()->does_config_array_val_include('social_login_sites', $this->util()->get_host())) {
			wp_register_script('sign_in_with_google_js', 'https://accounts.google.com/gsi/client', ['jquery', 'registration_js'], null, ['strategy' => 'async']);
		}
		wp_localize_script( 'registration_js', 'registration', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'registration_secure_me' ),
		));

		wp_register_script( 'registration_close_js', plugins_url( 'js/registration-close.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );

	}

	/**
	 * Registration
	 *
	 * Determine if user is a contact.
	 *
	 * @since 1.0.0
	 * @since 1.2.6 Bug Fix - Add param contact_id to conditions to stop registration popup. @see iFOUND_registration::register_contact_with_click().
	 */

	public function registration() {

		$track_code = intval( $_COOKIE[$this->cookie] );

		/** If true, we have this contact, so do not display registration. */
		if( $this->get_post_id_by_key_value( 'track_code', $track_code ) || isset( $_GET['contact_id'] ) ) {

			$this->is_registered = true;

		} else {

			$this->is_registered = false;

		}

	}

	/**
	 * Registration Check
	 *
	 * Check to see if user is over set view limits.
	 *
	 * @since 1.0.0
	 * @since 1.2.45 Renamed form registration_cookies to registration_check.
	 */

	public function registration_check() {

		/** We cab skip this method if we are already registered. */
		if( $this->is_registered ) return;

		$settings = (object) get_option( 'ifound_registration_settings' );

		$this->cookie_name = $this->cookie_name();

		$old_cookie_value = isset( $_COOKIE[$this->cookie_name] ) ? $_COOKIE[$this->cookie_name] : false;

		$this->cookie_value = $old_cookie_value ? $old_cookie_value + 1 : 1;

		/** We will set this true as default. If it becomes false then we can show the pop-form. */
		$this->under_limit = true;

		/** If check the limits on all 3 of our settings. */
		if(
			$this->cookie_name == 'ifound_page_views_details'  && $settings->registration_limit_details  < $this->cookie_value
			||
			$this->cookie_name == 'ifound_page_views_results'  && $settings->registration_limit_results  < $this->cookie_value
			||
			$this->cookie_name == 'ifound_page_views_advanced' && $settings->registration_limit_advanced < $this->cookie_value
			||
			$this->cookie_name == 'ifound_page_views' 		  && $settings->registration_limit_all  	 < $this->cookie_value
		) {

			/** If we go over the limit, our condition is false. */
			$this->under_limit = false;

		}

	}

	/**
	 * Cookie Name
	 *
	 * Return the registration cookie name for this page.
	 *
	 * @since 1.2.45
	 *
	 * @return string cookie_name The name of the cookie.
	 */

	public function cookie_name() {

		if( 	defined( 'DOING_DETAILS'   ) ) $type = '_details';
		elseif( defined( 'DOING_RESULTS'   ) ) $type = '_results';
		elseif( defined( 'DOING_SHORTCODE' ) ) $type = '_results';
		elseif( defined( 'DOING_ADVANCED'  ) ) $type = '_advanced';
		else 								   $type = '';
		return 'ifound_page_views' . $type;

	}

	/**
	 * Registration Cookie Script
	 *
	 * Set the refistration cookie script.
	 *
	 * @since 1.2.45
	 */

	public function registration_cookie_script() {

		if( $this->is_registered ) return; ?>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
				document.cookie = "<? echo $this->cookie_name; ?>=<? echo $this->cookie_value; ?>; expires=Thu, 29 Dec 2033 12:00:00 UTC; path=/";
			});
		</script><?

	}

	private function should_site_and_page_force_registration() {
		$host = $this->util()->get_host();
		$uri = $_SERVER['REQUEST_URI'];

		if (iFoundLeadGeneration::new_hookless()->should_site_and_page_force_registration($host, $uri)) {
			return true;
		}

		// We're hard-coding this list for now because we aren't sure if we should spend the time to generalize it.
		if ($host === 'metgrouprealestate.com') {
			if (
				preg_match('#scottsdale-area-homes-for-sale#', $uri)
				|| preg_match('#5-down-homes-in-scottsdale#', $uri)
			) {
				return true;
			}
		}
		return false;
	}

	/**
	 * Registration Form
	 *
	 * This is the registration pop up.
	 *
	 * @since 1.0.0
	 * @since 1.3.4 Add registration_disabled functionality.
	 * @since 3.4.4 Check if delay is numeric, multipy outside of intval function.
	 *
	 * @uses iFOUND_registration::registration_form_body()
	 */

	public function registration_form() {

		/** We can skip this method if we are already registered, under the limit. */
		if( $this->is_registered || is_admin() ) return;

		$settings = (object) get_option( 'ifound_registration_settings' );

		$registration_disabled = is_array( $settings->registration_disabled ) ? $settings->registration_disabled : array();

		$delay 	  = is_numeric( $settings->registration_delay_before )  ? ( intval( $settings->registration_delay_before )  * 1000 ) : 0;
		$between  = is_numeric( $settings->registration_delay_between ) ? ( intval( $settings->registration_delay_between ) * 60 )   : 86400;
		$forced   = in_array( 'checked', (array) $settings->registration_force );
		$forced = $forced || $this->should_site_and_page_force_registration();

		$required = ( $this->under_limit || in_array( 'checked', $registration_disabled ) ) ? false : true;
		$array    = array( 'required' => $required, 'delay' => $delay, 'between' => $between, 'forced' => $forced ); ?>

		<script>var register = <? echo json_encode( $array ); ?> </script>

		<?php
		wp_enqueue_script( 'cookie_js' );
		wp_enqueue_script( 'registration_js' );
		wp_enqueue_script('sign_in_with_google_js');
		?>

		<?php if (strpos($_SERVER['REQUEST_URI'], 'client-profile') === false): ?>
			<div class="ifound-popup-backdrop ifound-popup-close"></div>
			<div class="ifound-popup">
				<?
					$this->registration_form_body();
				?>
			</div>
		<? endif; ?>
		<?php

	}

	/**
	 * Registration Form Body
	 *
	 * We use this for the registration pop up and the client profile.
	 *
	 * @since 1.0.23
	 *
	 * @param bool $client_profile True if doing the client prohile. Otherwise false.
	 */

	public static function registration_form_body( $client_profile = false ) {


		$settings = (object) get_option( 'ifound_registration_settings' );
		$show_close_button = !$settings->registration_force && !$client_profile;
		if (static::new_hookless()->should_site_and_page_force_registration()) {
			$show_close_button = false;
		}
		?>

		<div class="ifound-wrap"><?

			if ($show_close_button) {

				wp_enqueue_script( 'registration_close_js' ); ?>

				<i class="fal fa-times ifound-popup-close ifound-close-button" aria-hidden="true"></i><?

			}

			$data = (object) get_option( 'ifound_agent' );

			$content = apply_filters( 'ifound_filter_merge_tags', $settings->registration_body, $data );

			$form_id = iFoundLeadGeneration::new_hookless()->maybe_get_registration_form_id()
				?? $settings->registration_form_id;
			$form = gravity_form( $form_id, false, false, false, null, true, 10, false ); ?>

			<div class="registration-account-already-form">

				<? do_action( 'ifound_account_already_form' ); ?>

			</div><?

			echo str_replace( '{RegistrationForm}', $form, $content ); ?>

		</div><?

	}

	/**
	 * Account Already Form
	 *
	 * This displays a form for previously registered users to reactivate their ifound-registered token
	 * It is located at the bottom of the registration form.
	 *
	 * @since 1.0.0
	 */

	public function account_already_form() {
		$google_client_id = $this->get_config()['google_client_id'];
		?>
		<a class="account-already"><? _e( 'If you already have an account, please enter your email address below or create an account using the form below, or sign in with Google', 'ifound' ); ?></a>

		<div class="account-already-form">

			<div class="account-already-input">

				<input type="text" id="registered-email" placeholder="Type Registered Email Address"/>

			</div>

			<div class="account-already-button">

				<a class="account-already-login button">
					<i class="fal fa-sign-in fa-account-already" aria-hidden="true"></i>
					<? _e( 'Login', 'ifound' ); ?>
				</a>

			</div>

		</div>

		<div class="social-logins">
			<!-- This can go anywhere, but I prefer it outside of the social-login-actions because it affects the div that
				 is display: flex -->
			<div id="g_id_onload"
				 data-client_id="<?= $google_client_id ?>"
				 data-callback="iFound_SignInWithGoogle_callback"
				 data-auto_prompt="true"
			>
			</div>
			<div class="social-login-actions">
				<div class="g_id_signin"
					 data-type="standard"
					 data-size="large"
					 data-theme="filled_blue"
					 data-text="sign_in_with"
					 data-shape="rectangular"
					 data-logo_alignment="left"
				>
				</div>
			</div>
			<div class="login-error"></div>
		</div>
		<?
	}

	/**
	 * Account Already - Ajax
	 *
	 * This resets a previously registered users ifound-registered token.
	 * If this contact does not exist we echo an "action" to validate the form input.
	 * Currently the input would turn red and the registration pop up would remain.
	 * If the contact does exsist. The ifound-registered code is reset and the pop up would be removed from display.
	 *
	 * @since 1.0.0
	 *
	 * @param string $_REQUEST['input'] The email address submitted from this form via Ajax.
	 */

	public function account_already() {

		check_ajax_referer( 'registration_secure_me', 'registration_nonce' );

		$email = sanitize_email( $_REQUEST['input'] );

		$contact_id = $this->get_post_id_by_key_value( 'email', $email );

		if( $contact_id && is_email( $email ) ) {

			/** Set the new cookie to the contact */
			add_post_meta( $contact_id, 'track_code' , intval( $_COOKIE[$this->cookie] ) );

			$response = array(
				'class' 		=> 'fa-sign-in',
				'action'		=> false,
				'contact_id' 	=> $contact_id
			);

		} else {

			$response = array(
				'class' 	=> 'fa-exclamation-triangle',
				'action'	=> 'empty-input'
			);

		}

		echo json_encode( $response );

		die();

	}

	public function social_login() {
		// I'm commenting out this call to check_ajax_referer() for now. When users are on a non-admin page like
		// /client-profile, the nonce generation function will consider them not logged in. Then, when they try to log
		// in, it uses admin-ajax.php, which is an admin page and the wordpress_sec_xxx cookie is sent and the nonce
		// verification function considers them logged in (if they are indeed logged into wordpress), and the nonces
		// won't match.
		// Now that I think about, nonces seem pretty silly for a non-admin context. Every (guest) user would have the
		// same nonce. So perhaps it's worthless to worry about it.
		// check_ajax_referer('registration_secure_me', 'registration_nonce');

		$social_login_type = $_REQUEST['social_login_type'];
		$user_id_meta_key = $social_login_type . '_user_id';
		$social_auth_token = $_REQUEST['social_auth_token'];

		$email = null;
		$social_user_id = null;
		$payload = null;
		if ($social_login_type === 'google') {
			$google_client_id = $this->get_config()['google_client_id'];
			$client = new Google_Client(['client_id' => $google_client_id]);
			$payload = $client->verifyIdToken($social_auth_token);
			if (!$payload) {
				wp_die('Invalid ID token', 400);
				return;
			}
			$social_user_id = $payload['sub'];
			$email = $payload['email'];
		} else {
			wp_die('Invalid social login type: ' . $social_login_type, 400);
			return;
		}

		// Google recommends to only look up users by their Google ID, and not by email address. However, we can't allow
		// duplicate email addresses in our system. So what we'll do is first look them up by their Google ID. If we
		// find it, we'll log them in as that user, even if their email address doesn't match (because they must have
		// changed it in Google at some point). If we don't find it, we'll look them up by their email address. And
		// finally we'll create a new contact if we don't find it.
		if ($contact_id = $this->get_post_id_by_key_value($user_id_meta_key, $social_user_id)) {
			// Note: why add_post_meta and not update_post_meta for 'track_code'?
			// Each separate user agent (as in, browser, per device) could have a different track code.
			// If we update a track_code, we hose the client that was using it. So it is possible one user
			// has multiple track codes.
			add_post_meta($contact_id, 'track_code', intval($_COOKIE[$this->cookie]));
		} else if ($contact_id = $this->get_post_id_by_key_value('email', $email)) {
			add_post_meta($contact_id, 'track_code', intval($_COOKIE[$this->cookie]));
			add_post_meta($contact_id, $user_id_meta_key, $social_user_id);
		} else {
			$contact_data = [
				'name'                 => $payload['name'],
				'email'                => $email,
				'social_login_type'    => $social_login_type,
				'social_login_user_id' => $social_user_id,
			];
			if ($social_login_type === 'google') {
				$contact_data['fname'] = $payload['given_name'];
				$contact_data['lname'] = $payload['family_name'];
			}
			$contact_id = iFoundContacts::new_hookless()->add_contact($contact_data);
		}
		$response = array(
			'class'      => 'fa-sign-in',
			'action'     => false,
			'contact_id' => $contact_id,
		);
		echo json_encode($response);
		wp_die();
	}

	/**
		 * Register With Click
	 *
	 * We want to register a contact with an email button click.
	 * If the contact is added via import or manually added. We do not have a contact_id/cookie match.
	 * This will do it for us via a URL param added to the button URL when an email is sent out.
	 *
	 * Note: This is designed to allow mutiple contacts on the same device.
	 *
	 * This is dependant on a track_code cookie to be set. We run iFOUND_contacts::cookie() with a priority of 1. Then use a priority of 2 here.
	 * We added a condition to iFOUND_registration::registration() to stop the registration popup.
	 *
	 * @uses get_post_status()
	 * @link https://codex.wordpress.org/Function_Reference/get_post_status
	 *
	 * @since 1.0.32
	 * @since 1.2.6 Bug Fix - Do not create a cookie/track_code if we already have one.
	 */

	public function register_contact_with_click() {

		$should_register = strpos($_SERVER['REQUEST_URI'], '/' . $this->save_this . '/') === 0
			|| strpos($_SERVER['REQUEST_URI'], '/' . $this->detail . '/') === 0;
		// Only do this if it's a campaign results/PDP page and the user is not logged into Wordpress. If they're logged
		// into Wordpress, we assume they are an admin or team member and we don't want to mix the user's tracking code
		// in with the contact.
		// Reminder to self only because I know this is going to bite me later: when first testing this, the function
		// is_user_logged_in was returning false when I was logged in and I'd visit a save-this page. Turns out it was
		// because it wasn't sending the wordpress_logged_in_[hash] cookie, which is because that cookie was set for
		// .ifoundsites.test, and I was visiting the page using the domain multiagent.test. When I changed to visit the
		// page using multiagent.ifoundsites.test, it sent the cookie and considered me logged in, as expected. This
		// shouldn't ever be a problem in production though.
		if( isset( $_GET['contact_id'] ) && $should_register && !is_user_logged_in() ) :

			$contact_id = intval( $_GET['contact_id'] );

			if( is_int( $contact_id ) && get_post_status( $contact_id ) ) {

				$track_code = intval( $_COOKIE[$this->cookie] );

				$track_codes = get_post_meta( $contact_id, 'track_code' );

				/** If the track code and contact do not match, we make a new track code. */
				if( ! in_array( $track_code, $track_codes ) ) {

					// This cookie exists for another contact. Let's give a new one.
					if (defined('iFOUND_CONTACT_ID')) {
						$track_code = $this->create_cookie();
					}
					// Reminder: we do add_post_meta() instead of update_post_meta() because the user could be using
					// multiple devices.
					// Another reminder: The following is something I'll note but I don't want to deal with right now.
					// This code will run multiple times if there is a redirect. For example, in my dev setup right now,
					// I have links in my emails that look like http://multiagent.ifoundsites.test/..., and that site
					// forwards to http://multiagent.test/... In that case, it will run this, write a track_code to the
					// database, then forward/redirect, and then will run on the forwarded page too. So basically there
					// is a wasted track code in the database.
					add_post_meta( $contact_id, 'track_code' , $track_code );

				}


				/** We define contact ID. */
				define( 'iFOUND_CONTACT_ID', $contact_id );

			}

		endif;

	}

	/**
	 * Cookie
	 *
	 * We create and/or validate the cookie. If we have a contact/cookie match, we define the contact ID.
	 *
	 * @since 1.0.0
	 */

	public function cookie() {

		if( ! isset( $_COOKIE[$this->cookie] )) {

			$track_code = $this->create_cookie();

		} elseif( is_int( intval( $_COOKIE[$this->cookie] ) ) ) {

			$track_code = intval( $_COOKIE[$this->cookie] );

			if( $contact_id = $this->get_post_id_by_key_value( 'track_code', $track_code ) ) {

				define( 'iFOUND_CONTACT_ID', $contact_id );

			}
		}

	}

	/**
	 * Create Cookie
	 *
	 * Create the cookie.
	 *
	 * @since 1.0.32
	 *
	 * @return int $track_code The value of the cookie.
	 */

	public function create_cookie() {

		$track_code = rand( 100000, 999999 ) . time();

		/** Set a 50 year cookie */
		setcookie( $this->cookie, $track_code, time() + ( 86400 * 365 * 50 ), '/' );
		// Some of our code expects this cookie value to exist (like iFoundContacts::add_contact_from_form).
		$_COOKIE[$this->cookie] = $track_code;

		return $track_code;

	}

}
