FROM ifoundagent:base

RUN rm -f /etc/apt/sources.list
RUN echo "deb https://archive.debian.org/debian stretch main" >> /etc/apt/sources.list
    RUN echo "deb-src https://archive.debian.org/debian stretch main" >> /etc/apt/sources.list
    RUN echo "deb https://archive.debian.org/debian stretch-backports main" >> /etc/apt/sources.list
    RUN echo  "deb https://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list
    RUN echo  "deb-src https://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list
RUN apt update

# Install some basic, useful stuff
RUN apt-get install -y --no-install-recommends --fix-missing \
    openjdk-8-jdk \
    procps
### ^^^^^^ #####
# Add the "ps" utility needed by the solr binary
# Since the utility is missing from the Debian Stretch docker image
# Move this line to the base image build if "ps" is needed in
# containers whose images are inhereited from the base image

# We'll install Solr here - could be anywhere, but I picked this location
WORKDIR /var/lib/solr

# Copy & install the Solr distribution
COPY solr-dist.tar.gz /var/lib/solr/
RUN tar xf solr-dist.tar.gz
RUN rm solr-dist.tar.gz

# Declare a volume, for where all the different cores will be stored
VOLUME /var/lib/solr/dist/server/solr

# Create a symbolic link for the solr binary
# So we can simply use the solr command to execute
# CL queries when running the container's shell
RUN ln -s /var/lib/solr/dist/bin/solr /bin/solr

# I'm installing node and iced-coffee-script just for the sake of running our tools/gen-solr-configs script.
# node.js - this runs apt-get update
RUN curl -sL https://deb.nodesource.com/setup_10.x | bash -
# Install node.js
RUN apt-get install -y --no-install-recommends --fix-missing \
	nodejs
# Install IcedCoffeeScript
RUN npm install -g iced-coffee-script coffee-script

# Command for running Solr
CMD ["/bin/solr", "start", "-f"]
