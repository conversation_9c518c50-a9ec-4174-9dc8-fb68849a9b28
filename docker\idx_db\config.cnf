[mysqld]
character-set-server=latin1
collation-server=latin1_swedish_ci
innodb_log_file_size=1GB
innodb_page_size=32KB
innodb_file_per_table=true
sql_mode=NO_ENGINE_SUBSTITUTION

# Update: we now have over a million records, and it's taking longer. I'm bumping it up from 30 minutes to 45, and
# changing the cron from every 30 minutes to only once per hour.
# If we use the default wait_timeout of 600, I get errors like this when syncing ARMLS Residential occasionally.
# Like if it's supposed to sync every 30 minutes, I might see the bug every time for a few times, or it might be a few
# hours in between. This seems to fix it. It starts due to a call to beginTransaction() in PHP.
#
#   There is no active transaction
#
#   Previous number of properties in DB: 871483
#   Properties left to update: 0
#
#   PHP Fatal error:  Uncaught PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in /www/vendor/zendframework/zendframework1/library/Zend/Db/Statement/Pdo.php:228
#   Stack trace:
#   #0 /www/vendor/zendframework/zendframework1/library/Zend/Db/Statement/Pdo.php(228): PDOStatement->execute(Array)
#   #1 /www/vendor/zendframework/zendframework1/library/Zend/Db/Statement.php(303): Zend_Db_Statement_Pdo->_execute(Array)
#   #2 /www/vendor/zendframework/zendframework1/library/Zend/Db/Adapter/Abstract.php(480): Zend_Db_Statement->execute(Array)
#   #3 /www/vendor/zendframework/zendframework1/library/Zend/Db/Adapter/Pdo/Abstract.php(238): Zend_Db_Adapter_Abstract->query('UPDATE `action_...', Array)
#   #4 /www/vendor/zendframework/zendframework1/library/Zend/Db/Adapter/Abstract.php(635): Zend_Db_Adapter_Pdo_Abstract->query('UPDATE `action_...', Array)
#   #5 /www/includes/classes/RETSHelper.php(444): Zend_Db_Adapter_Abstract->update('action_log', Array, '(action_id = 12...')
#   #6 /www/includes/classes/RETSHelper.php(838): RETSHelper->updat in /www/vendor/zendframework/zendframework1/library/Zend/Db/Statement/Pdo.php on line 235
wait_timeout=2700
