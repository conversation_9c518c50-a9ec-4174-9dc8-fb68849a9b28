<?
/**
 * iFound_quick_search class
 *
 * Display quick search in widget areas.
 *
 * @package iFound
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFound_quick_search extends WP_Widget {
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Quick Search', 
			array(
			'description' => 'Add Quick Search to any widget area.'
		));
		
	}
	
	/**
	 * Widget
	 *
	 * @since 1.0.0
	 */
	
	public function widget( $args, $instance ) {
		
		echo $args['before_widget'];
		
		$qs = iFoundQuickSearch::init();

		$qs->body();
		
		echo $args['after_widget'];
		
	}
	
}
