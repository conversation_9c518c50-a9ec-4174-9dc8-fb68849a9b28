<?xml version="1.0"?>
<!DOCTYPE Configure PUBLIC "-//Jetty//Configure//EN" "http://www.eclipse.org/jetty/configure_9_0.dtd">

<!-- =============================================================== -->
<!-- Configure the Jetty Server                                      -->
<!--                                                                 -->
<!-- Documentation of this file format can be found at:              -->
<!-- http://wiki.eclipse.org/Jetty/Reference/jetty.xml_syntax        -->
<!--                                                                 -->
<!-- =============================================================== -->


<Configure id="Server" class="org.eclipse.jetty.server.Server">

    <!-- =========================================================== -->
    <!-- Configure the Server Thread Pool.                           -->
    <!-- The server holds a common thread pool which is used by      -->
    <!-- default as the executor used by all connectors and servlet  -->
    <!-- dispatches.                                                 -->
    <!--                                                             -->
    <!-- Configuring a fixed thread pool is vital to controlling the -->
    <!-- maximal memory footprint of the server and is a key tuning  -->
    <!-- parameter for tuning.  In an application that rarely blocks -->
    <!-- then maximal threads may be close to the number of 5*CPUs.  -->
    <!-- In an application that frequently blocks, then maximal      -->
    <!-- threads should be set as high as possible given the memory  -->
    <!-- available.                                                  -->
    <!--                                                             -->
    <!-- Consult the javadoc of o.e.j.util.thread.QueuedThreadPool   -->
    <!-- for all configuration that may be set here.                 -->
    <!-- =========================================================== -->
    <!-- uncomment to change type of threadpool
    <Arg name="threadpool"><New id="threadpool" class="org.eclipse.jetty.util.thread.QueuedThreadPool"/></Arg>
    -->
    <Get name="ThreadPool">
        <Set name="minThreads" type="int"><Property name="solr.jetty.threads.min" default="10"/></Set>
        <Set name="maxThreads" type="int"><Property name="solr.jetty.threads.max" default="10000"/></Set>
        <Set name="idleTimeout" type="int"><Property name="solr.jetty.threads.idle.timeout" default="5000"/></Set>
        <Set name="stopTimeout" type="int"><Property name="solr.jetty.threads.stop.timeout" default="60000"/></Set>
        <Set name="detailedDump">false</Set>
    </Get>


    <!-- =========================================================== -->
    <!-- Http Configuration.                                         -->
    <!-- This is a common configuration instance used by all         -->
    <!-- connectors that can carry HTTP semantics (HTTP, HTTPS, SPDY)-->
    <!-- It configures the non wire protocol aspects of the HTTP     -->
    <!-- semantic.                                                   -->
    <!--                                                             -->
    <!-- This configuration is only defined here and is used by      -->
    <!-- reference from the jetty-http.xml, jetty-https.xml and      -->
    <!-- jetty-spdy.xml configuration files which instantiate the    -->
    <!-- connectors.                                                 -->
    <!--                                                             -->
    <!-- Consult the javadoc of o.e.j.server.HttpConfiguration       -->
    <!-- for all configuration that may be set here.                 -->
    <!-- =========================================================== -->
    <New id="httpConfig" class="org.eclipse.jetty.server.HttpConfiguration">
        <Set name="secureScheme">https</Set>
        <Set name="securePort"><Property name="solr.jetty.secure.port" default="8443" /></Set>
        <Set name="outputBufferSize"><Property name="solr.jetty.output.buffer.size" default="32768" /></Set>
        <Set name="outputAggregationSize"><Property name="solr.jetty.output.aggregation.size" default="8192" /></Set>
        <Set name="requestHeaderSize"><Property name="solr.jetty.request.header.size" default="262143" /></Set>
        <Set name="responseHeaderSize"><Property name="solr.jetty.response.header.size" default="262143" /></Set>
        <Set name="sendServerVersion"><Property name="solr.jetty.send.server.version" default="false" /></Set>
        <Set name="sendDateHeader"><Property name="solr.jetty.send.date.header" default="false" /></Set>
        <Set name="headerCacheSize"><Property name="solr.jetty.header.cache.size" default="512" /></Set>
        <Set name="delayDispatchUntilContent"><Property name="solr.jetty.delayDispatchUntilContent" default="false"/></Set>
        <!-- Uncomment to enable handling of X-Forwarded- style headers
        <Call name="addCustomizer">
          <Arg><New class="org.eclipse.jetty.server.ForwardedRequestCustomizer"/></Arg>
        </Call>
        -->
    </New>

    <!-- =========================================================== -->
    <!-- Set handler Collection Structure                            -->
    <!-- =========================================================== -->
    <New id="RewriteHandler" class="org.eclipse.jetty.rewrite.handler.RewriteHandler">
        <Set name="rewriteRequestURI">true</Set>
        <Set name="rewritePathInfo">false</Set>
        <Set name="originalPathAttribute">requestedPath</Set>

        <Call name="addRule">
            <Arg>
                <New class="org.eclipse.jetty.rewrite.handler.RedirectRegexRule">
                    <Set name="regex">^/$</Set>
                    <Set name="replacement">/solr/</Set>
                </New>
            </Arg>
        </Call>
    </New>

    <!-- =========================================================== -->
    <!-- Set handler Collection Structure                            -->
    <!-- =========================================================== -->
    <Set name="handler">
        <New id="Handlers" class="org.eclipse.jetty.server.handler.HandlerCollection">
            <Set name="handlers">
                <Array type="org.eclipse.jetty.server.Handler">
                    <Item>
                        <Ref id="RewriteHandler"/>
                    </Item>
                    <Item>
                        <New id="Contexts" class="org.eclipse.jetty.server.handler.ContextHandlerCollection"/>
                    </Item>
                    <Item>
                        <New id="DefaultHandler" class="org.eclipse.jetty.server.handler.DefaultHandler"/>
                    </Item>
                    <Item>
                        <New id="RequestLog" class="org.eclipse.jetty.server.handler.RequestLogHandler"/>
                    </Item>
                </Array>
            </Set>
        </New>
    </Set>

    <!-- =========================================================== -->
    <!-- Configure Request Log                                       -->
    <!-- =========================================================== -->
    <!--
    <Ref id="Handlers">
      <Call name="addHandler">
        <Arg>
          <New id="RequestLog" class="org.eclipse.jetty.server.handler.RequestLogHandler">
            <Set name="requestLog">
              <New id="RequestLogImpl" class="org.eclipse.jetty.server.NCSARequestLog">
                <Set name="filename">
                   logs/request.yyyy_mm_dd.log
                </Set>
                <Set name="filenameDateFormat">yyyy_MM_dd</Set>
                <Set name="retainDays">90</Set>
                <Set name="append">true</Set>
                <Set name="extended">false</Set>
                <Set name="logCookies">false</Set>
                <Set name="LogTimeZone">UTC</Set>
              </New>
            </Set>
          </New>
        </Arg>
      </Call>
    </Ref>
    -->

    <!-- =========================================================== -->
    <!-- extra options                                               -->
    <!-- =========================================================== -->
    <Set name="stopAtShutdown">true</Set>
    <Set name="dumpAfterStart">false</Set>
    <Set name="dumpBeforeStop">false</Set>

    <Call name="addBean">
        <Arg>
            <New id="DeploymentManager" class="org.eclipse.jetty.deploy.DeploymentManager">
                <Set name="contexts">
                    <Ref refid="Contexts" />
                </Set>

                <Call name="addAppProvider">
                    <Arg>
                        <New class="org.eclipse.jetty.deploy.providers.WebAppProvider">
                            <Set name="monitoredDirName"><Property name="jetty.base" default="."/>/contexts</Set>
                            <Set name="scanInterval">0</Set>
                        </New>
                    </Arg>
                </Call>

                <!-- Add a customize step to the deployment lifecycle -->
                <!-- uncomment and replace DebugBinding with your extended AppLifeCycle.Binding class
                <Call name="insertLifeCycleNode">
                  <Arg>deployed</Arg>
                  <Arg>starting</Arg>
                  <Arg>customise</Arg>
                </Call>
                <Call name="addLifeCycleBinding">
                  <Arg>
                    <New class="org.eclipse.jetty.deploy.bindings.DebugBinding">
                      <Arg>customise</Arg>
                    </New>
                  </Arg>
                </Call>
                -->

            </New>
        </Arg>
    </Call>

</Configure>
