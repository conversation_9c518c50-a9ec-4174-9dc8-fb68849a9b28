<?
/**
 * The template for displaying IFound Details
 *
 * @since 1.0
 * @version 1.0
 */
do_action('prep_cmc_process_from_listing', (array)$results);

get_header(); ?>

<div id="primary" class="content-area details <? echo $results->mls_class; ?>">

	<main id="main" class="site-main" role="main">

		<article id="listing-details" class="listing-details">

			<header class="entry-header">

				<h1 class="entry-title"><?
					do_action( 'ifound_seo_h1' ); ?>
				</h1>

			</header><!-- .entry-header -->

			<div class="entry-content">

				<div class="ifound-details">

					<div class="ifound-wrap"><?

						do_action( 'ifound_detail_header' );

						/**
						 * Move these Hooks into any order you like.
						 *
						 * @since 1.0.0
						 */

						?><!-- Start Slider Section --><?
						do_action( 'ifound_before_slider', $results );
						if ( is_active_sidebar( 'details-before-slider' ) ) :
							dynamic_sidebar( 'details-before-slider' );
	 					endif;
						do_action( 'ifound_display_slider' );
						if ( is_active_sidebar( 'details-after-slider' ) ) :
							dynamic_sidebar( 'details-after-slider' );
	 					endif;
						do_action( 'ifound_after_slider', $results );
						?><!-- End Slider Section --><?

						?><!-- Start Details Section --><?
						do_action( 'aerial_sphere_pdp', $results );
						do_action( 'aerial_sphere_map_html' );
						do_action( 'ifound_before_details', $results );
						do_action( 'ifound_display_details', $extra );
						do_action( 'ifound_after_details', $results );
						if ( is_active_sidebar( 'after-details' ) ) :
							dynamic_sidebar( 'after-details' );
	 					endif;
						?><!-- End Details Section --><?

						do_action( 'ifound_whats_nearby' );
						?><!-- End Whats Nearby Section -->


						<? if (!$this->util()->is_user_agent_bot()): ?>
						<div class="pdp-heading cmc">
							<div class="ifound-wrap">
								<h2 class="pdp-h2 cmc">Current Market Comparison</h2>
								<div class="php-content cmc">
									<?php
									do_action('cmc_report');
									?>
								</div>
							</div>
						</div>
						<? endif; ?>
					</div>

				</div>

			</div><!-- .entry-content -->

		</article><!-- #post-## -->

	</main><!-- #main -->

</div><!-- #primary --><?

get_footer();

/** Required Hooks **DO NOT REMOVE** */
do_action( 'ifound_registration', $results );
do_action( 'ifound_tracking', $results, 'detail_view' );

/**
 * This is an action to track Properies viewed by a contact.
 * We send this data to their external CRM.
 *
 * @since 1.0.20
 */
do_action( 'ifound_external_crm_property_view', $results );

apply_filters( 'ifound_footer', $results );
