select min(LIST_75) from property where LIST_75 > 0;

select min(LIST_75 / LIST_22) as tax_to_list_price_ratio from property where LIST_75 > 0;

describe property;
select * from property limit 10;
select LIST_105 from property;
select count(*) from property;
select * from field_mapping;
-- MLS ID LIST_105
-- CrossStreets LIST_130
-- Directions LIST_82
select * from property where LIST_130 = '';
select * from property where LIST_82 = '';

select * from access;
select * from field_mapping order by MapName;
select * from field_mapping order by armls;

select * from field_mapping where length(armls) < 4 or substring(armls, 1, 4) != 'LIST' order by MapName;
select * from field_mapping where armls <> '' and MapName <> 'Certifications' and MapName <> 'Design' and MapName <> 'MasterBathFeatures' and MapName <> 'ModificationTimestamp' order by MapName;

select LIST_131, count(LIST_131) from property group by LIST_131 having count(LIST_131) > 1 order by LIST_131;

select * from property where LIST_39 = 'chandler' order by LIST_87 DESC;
select * from property where 1 = 1 order by LIST_87 DESC;

delimiter $$

CREATE TABLE `access_log` (
  `LogID` int(11) NOT NULL AUTO_INCREMENT,
  `access_id` int(11) NOT NULL,
  `ClientIP` varchar(15) NOT NULL,
  `BrowserIP` varchar(15) NOT NULL,
  `QueryType` enum('FeatList','Search','Details','Options','RSS') NOT NULL,
  `PropID` int(11) NOT NULL,
  `UserAgent` varchar(255) NOT NULL,
  `Referer` varchar(255) NOT NULL,
  `QueryTime` double NOT NULL,
  `NumQueries` int(11) NOT NULL,
  `TotalTime` double NOT NULL,
  `Created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Features` varchar(255) NOT NULL,
  `Words` varchar(255) NOT NULL,
  `Hostname` varchar(255) NOT NULL,
  `Category` varchar(255) NOT NULL,
  `City` varchar(255) NOT NULL,
  `RequestURL` varchar(255) NOT NULL,
  `Page` smallint(5) unsigned NOT NULL,
  `SortOrder` varchar(255) NOT NULL,
  `Price` varchar(20) NOT NULL,
  `Sqft` varchar(20) NOT NULL,
  `Bedroom` varchar(20) NOT NULL,
  `Bathroom` varchar(20) NOT NULL,
  `ZipCode` varchar(20) NOT NULL,
  `PropType` varchar(20) NOT NULL,
  `NumResults` int(10) unsigned NOT NULL,
  PRIMARY KEY (`LogID`),
  KEY `IX_Customer` (`access_id`),
  KEY `IX_ClientIP` (`ClientIP`),
  KEY `IX_BrowserIP` (`BrowserIP`),
  KEY `IX_Type` (`QueryType`),
  KEY `IX_Created` (`Created`)
) ENGINE=MyISAM AUTO_INCREMENT=448 DEFAULT CHARSET=latin1$$

create table property_timestamp_backup like property;

insert into property_timestamp_backup (property_id, LIST_87) select property_id, LIST_87 from property;

select * from property_timestamp_backup;

update property_timestamp_backup set LIST_87 = '1980-01-01';

select UNBRANDEDIDXVIRTUALTOUR from property where UNBRANDEDIDXVIRTUALTOUR <> '';
select LIST_1 from property order by LIST_22 desc limit 1;

select count(*) from property_images;
select * from property_images where Location <> '' limit 10;
select count(*) from property_images where Location <> '';

select count(*) from property p left join property_images pi on p.LIST_1 = pi.`Content-ID` where pi.`Content-ID` is null;
select p.LIST_1, count(*) from property p left join property_images pi on p.LIST_1 = pi.`Content-ID` where pi.`Content-ID` is not null group by p.LIST_1 limit 100;
select LIST_105 from property where LIST_1 = '20090204165240703491000000';

select * from trendmls_images limit 1000;
select count(*) from property_images;
select count(*) from armls_images;

select count(*) from property p left join armls_images ai on p.LIST_1 = ai.`Content-ID` where ai.`Content-ID` is null;
select * from trendmls_images limit 10;
select * from armls_images limit 10;
ALTER TABLE `armls_images` ADD COLUMN `Location` text;
describe armls_images;
select * from property where LIST_105 = '4640803';
-- 20110902194022415979000000

update armls_images a join pfndidx_azdb_backup.armls_images b on a.`image_id` = b.`image_id` set a.`Content-ID` = b.`Content-ID`;

update property p left join armls_images ai on p.LIST_1 = ai.`Content-ID` set LIST_87 = '1980-01-01' where ai.Preferred = 1 && ai.Location is null;

select count(*) from property p left join armls_images ai on p.LIST_1 = ai.`Content-ID` where ai.Preferred = 1 && ai.Location is null;

select `Content-ID`, count(*) from armls_images ai group by `Content-ID`;
select count(distinct `Content-ID`) from armls_images ai where Location is not null;

select `Content-ID` from armls_images ai where Location is null limit 1;
select `Content-ID` from armls_images ai limit 1;
select `Content-ID` from armls_images ai where Location <> '' limit 1;
update armls_images set Location = null where Location = '';
update armls_images ai set Location = null where `Content-ID` = '20071213200504804468000000';
select * from armls_images where `Content-ID` = '20071213200504804468000000';
update armls_images ai set Location = null;
select Location from armls_images ai limit 1;
describe armls_images;
select @CI := `Content-ID` from armls_images ai where Location is null limit 1;
set @CI = 5;
select @CI;
select count(*) from armls_images;
select count(*) from armls_images where Location = '';
select count(*) from armls_images where Location is null;
select * from armls_images limit 10;
update armls_images set Location = 'http://cdn.photos.flexmls.com/az/20121101193320639039000000.jpg';

set PROFILING=1;
delete from armls_images where `Content-ID` = '20071213200504804468000000';
show profile for QUERY 1;
set PROFILING=0;

SHOW GLOBAL STATUS LIKE 'Key%';

select * from action_log limit 1;
select * from action_log order by action_id desc limit 1;

explain select * from armls_images where `Content-ID` = '20071213200504804468000000';
show processlist;
describe armls_images;

select count(*) from property p join armls_images ai on p.LIST_1 = ai.`Content-ID` where ai.Preferred = 1 and ai.Location is not null;
select ai.`Content-ID`, count(ai.`Content-ID`) from armls_images ai where ai.`Object-ID` = 1 group by ai.`Content-ID` having count(ai.`Content-ID`) > 1 order by count(ai.`Content-ID`) desc;
select ai.`Content-ID`, count(ai.`Content-ID`) from armls_images ai where ai.`Object-ID` = 1 and ai.Location is not null group by ai.`Content-ID` having count(ai.`Content-ID`) > 1 order by count(ai.`Content-ID`) desc;
select ai.`Content-ID`, count(ai.`Content-ID`) from armls_images ai where ai.`Object-ID` = 1 and ai.Location <> '' group by ai.`Content-ID` having count(ai.`Content-ID`) > 1 order by count(ai.`Content-ID`) desc;
select * from armls_images ai where ai.`Content-ID` = '20080417203554709493000000';
select count(*) from armls_images ai where ai.`Content-ID` = '20100503235701509529000000';

select * from videos;

select count(*) from armls_images where Location not like BINARY '%.jpg';

describe trendmls_images;
select * from trendmls_images limit 10; 

select * from armls_property_A;
select count(*) from property;
describe property_a_fields;
select * from property_a_fields;

select * from access;
select * from access where access_account_id = '';
delete from access where access_account_id = '' and access_apikey = '';

describe trendmls_images;
select * from trendmls_images where PropMediaURLThumb <> '' limit 10;

select LIST_1 from property where LIST_105 = '4899643';
select * from armls_images where `Content-ID` = '20130305182127896238000000';