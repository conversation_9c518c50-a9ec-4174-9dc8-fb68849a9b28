<?php

namespace Profound;

class Config {
	private static $config;

	public static function getConfig($options = array()) {
		if (!isset(self::$config)) {
			$filename = @$options['filename'] ?: __DIR__."/../../../config.ini";
			$filename = realpath($filename);
			$defaults = [
				'log' => [
					'debug_log_path' => null
				],
				'crm' => [
					'api_base_url' => null,
				]
			];
			self::$config = array_merge($defaults, parse_ini_file($filename, true));
		}
		return self::$config;
	}

	public static function getDebugLogPath() {
		$config = self::getConfig();
		return $config['log']['debug_log_path'];
	}

	public static function getCrmApiBaseUrl() {
		$config = self::getConfig();
		return $config['crm']['api_base_url'];
	}
}
