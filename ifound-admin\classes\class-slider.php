<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

 
class <PERSON>lider extends iFoundAdmin {

	private $post_type		= 'sliders';
	private $label_name 	= 'Slider';
	private	$label_names	= 'Sliders';
	protected $blog_id		= 5;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	 
	public function __construct() {
		
		add_action( 'init', array( $this, 'sliders_post_type' ) );
		add_action( 'admin_menu', array( $this, 'add_menu_items' ) );

		add_filter( 'manage_sliders_posts_columns' , array( $this, 'add_sliders_columns' ) );
		add_action( 'manage_sliders_posts_custom_column' , array( $this, 'sliders_column'), 10, 2 );

		add_action('edit_form_after_title', [$this, 'edit_form_after_title']);
	}

	public function sliders_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'register_meta_box_cb'	=> array( $this, 'add_metaboxes' )
			)
	  	);
		
	}

	public function add_sliders_columns( $columns ) {
		
		return array_merge( 
			array( 'cb' 						=> __( 'checkall', 'ifound' ) ),
			array( 'featured_image' 			=> __( 'Slider', 'ifound' ) ),
			array( 'title' 						=> __( 'Image URL', 'ifound' ) ),
			array( 'taxonomy-mls_name'			=> __( 'MLS Name', 'ifound' ) ),
			array( 'taxonomy-sub_section'		=> __( 'Sub Section', 'ifound' ) ),
			array( 'taxonomy-stylesheet' 		=> __( 'Style Sheet', 'ifound' ) ),
			array( 'taxonomy-slider_type' 		=> __( 'Slider Type', 'ifound' ) ),
		);
	
	}

	public function sliders_column( $column, $id ) { 

		if ( $column == 'featured_image' ) {
	
			ob_start(); ?>

			<div><? 

				$title 	= get_the_title( $id ); ?>

				<div><image src="<? echo $title; ?>" width="400"/></div>

			</div><?

			echo ob_get_clean();

		}
		
	}


	public function add_metaboxes() {

		add_meta_box(
			'featured_reminder',
			'Reminder',
			array( $this, 'reminder_metabox'),
			$this->post_type,
			'ifound_context',
			'high'
		);

		add_meta_box(
			'featured_image_metabox',
			__( '<i class="far fa-image"></i> Sliders', 'ifound' ),
			array( $this, 'sliders_metabox'),
			$this->post_type,
			'ifound_context',
			'high'
		);

	}

	// Update: I don't know why, but this doesn't seem to be needed, and in fact, it causes the metaboxes to show up
	// twice, so I commented out the internals. It seems to work as expected in class-content.php though.
	//
	// Put the image preview above the content editor.
	// I got this from https://wordpress.stackexchange.com/a/158485/27896
	// When we use 'ifound_context', it can be anything other than the built-in context names.
	public function edit_form_after_title() {
	    // Get the globals:
	    global $post, $wp_meta_boxes;

	    // Output the "advanced" meta boxes:
	    // do_meta_boxes( get_current_screen(), 'ifound_context', $post );

	    // Remove the initial "advanced" meta boxes:
	    // unset($wp_meta_boxes[$this->post_type]['ifound_context']);
	}

	public function sliders_metabox() { ?>

		<table class="form-table">
					
			<tbody>

				<tr>
								
					<td><? 

						$title 	= get_the_title( get_the_ID() ); ?>

						<div><image src="<? echo $title; ?>"/></div>
					
					</td>
										
				</tr
							
			</tbody>
					
		</table><?

	}

	public function reminder_metabox() {
		?>
		<ul>
			<li>These are called Sliders, but they are probably better named Slides. For iFound, there is only one
				slider, so each of these are a slide. For Slider Revolution, there could be multiple sliders, and each
				slider can have multiple of these slides.</li>
			<li>The post title will be the URL used for the image.</li>
			<li>The Slider Revolution version is set to 6.4.0. If the version being used is much different than this,
				our code needs to be changed (including this line).</li>
		</ul>
		<?php
	}

	public function add_menu_items() {
		
		if( get_current_blog_id() == $this->blog_id ) {

			add_menu_page(
	        	__( 'Import Sliders', 'ifound' ),
				__( 'Import Sliders', 'ifound' ),
	        	'manage_options',
				'import-sliders',
	        	array( $this, 'import_sliders_page' ) 
			);

		}

	}

	public function import_sliders_page () { 

		if( isset( $_POST['ifound-sliders-import'] ) ) {

			$this->import_sliders();

		} 

		$stylesheets 	= get_terms ( array(
			'taxonomy'   	=> 'stylesheet',
			'fields'	    => 'names',
			'hide_empty' 	=> false
		) );

		$mls_names 		= get_terms ( array(
			'taxonomy'  	=> 'mls_name',
			'fields'	    => 'names',
			'hide_empty'   	=> false
		) );

		$sub_sections 		= get_terms ( array(
			'taxonomy'  	=> 'sub_section',
			'fields'	    => 'names',
			'hide_empty'   	=> false
		) );

		$slider_types 		= get_terms ( array(
			'taxonomy'  	=> 'slider_type',
			'fields'	    => 'names',
			'hide_empty'   	=> false
		) );
		?>

		<h1>Import Images</h1>

		<ol>
			<li>Images must be .jpg</li>
			<li>FTP the images to wp-content/uploads/staging/ </li>
			<li>Check all the categories that apply for this set of images </li>
			<li>Click Import Now to upload images into the system. </li>
			<li>The images at wp-content/uploads/staging/ will be removed automactically.</li>
		</ol>

		<form method="post">

			<h2>Choose MLS</h2>

			<table class="form-table">

				<tbody><?

					foreach( $mls_names as $mls_name )
						$this->checkbox( 'mls_name', $mls_name ); ?>

				</tbody>

			</table>

			<h2>Choose Sub Sections(optional)</h2>

			<table class="form-table">

				<tbody><?

					foreach( $sub_sections as $sub_section )
						$this->checkbox( 'sub_section', $sub_section ); ?>

				</tbody>

			</table>

			<h2>Choose Stylesheets</h2>

			<table class="form-table">

				<tbody><?

					foreach( $stylesheets as $stylesheet )
						$this->checkbox( 'stylesheet', $stylesheet ); ?>

				</tbody>

			</table>

		<h2>Choose Type</h2>

		<table class="form-table">

			<tbody><?

			foreach( $slider_types as $slider_type)
				$this->checkbox( 'slider_type', $slider_type ); ?>

			</tbody>

		</table>

			<div class="clear"></div>

			<input type="submit" value="Import Now" class="button button-primary">
			<input type="hidden" name="ifound-sliders-import" value="import-now">

		</form><?

	}

	public function checkbox( $name, $value ) { ?>

		<tr>
								
			<th scope="row"><label for="<? echo $name; ?>"><? _e( $value, 'ifound' ); ?></label></th>

			<td><input type="checkbox" name="<? echo $name; ?>[]" id="<? echo $name; ?>" value="<? echo $value; ?>"></td>

		</tr><?
		
	}

	private function import_sliders() {

		$the_path 	= ABSPATH . 'wp-content/uploads/staging/';
		$new_path 	= ABSPATH . 'wp-content/uploads/sliders/';
		$url 		= $this->get_config()['sliders_origin'] . '/wp-content/uploads/sliders/';

		$handler = opendir( $the_path );
			
		while ( $photo = readdir( $handler ) ) {
				
			if( exif_imagetype( $the_path . '/' . $photo ) == IMAGETYPE_JPEG ) {
						
				$new_photo = 'slider-' . time() . rand( 1, 99999999 ) . '.jpg';

				copy( $the_path . '/' . $photo, $new_path .'/' . $new_photo );
				
				unlink( $the_path . '/' . $photo );

				$post_data = array(
    				'post_title' 	=> $url . $new_photo,
    				'post_type' 	=> $this->post_type,
    				'post_status'   => 'publish'
				);
				
				$object_id = wp_insert_post( $post_data );
				
				wp_set_object_terms( $object_id, $_POST['mls_name'], 'mls_name', true );
				wp_set_object_terms( $object_id, $_POST['stylesheet'], 'stylesheet', true );
				wp_set_object_terms( $object_id, $_POST['sub_section'], 'sub_section', true );
				wp_set_object_terms( $object_id, $_POST['slider_type'], 'slider_type', true );

			}

		}
		
		closedir( $handler );

	}

}
