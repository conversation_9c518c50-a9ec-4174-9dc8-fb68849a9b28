const defaultConfig = require('@wordpress/scripts/config/webpack.config');

module.exports = {
	...defaultConfig,
	optimization: {
		...defaultConfig.optimization,
		splitChunks: {
			...defaultConfig.optimization.splitChunks,
			chunks: 'all',
			cacheGroups: {
				...defaultConfig.optimization.splitChunks.cacheGroups,
				// Restore webpack's default vendors cacheGroup to split out vendor chunks.
				vendors: {
					test: /[\\/]node_modules[\\/]/,
					priority: -10
				},
			},
		},
	},
};