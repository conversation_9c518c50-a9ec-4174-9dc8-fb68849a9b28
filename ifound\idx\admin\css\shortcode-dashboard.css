@charset "UTF-8";
/* CSS Document */

/* Shortcode */
i.ifound_shortcode { 
	background-image: url(/wp-content/plugins/ifound/idx/admin/images/ifound-shortcode.png); 
}

i.ifound_polygon_drawing {
  background-image: url(/wp-content/plugins/ifound/idx/admin/images/polygon-drawing-icon.png);
}

.shortcode-close-wrapper{
	float: right ;
}

.shortcode-heading{
	font-size: 28px;
}

.pop-up{
	display: none;
}

.pop-up.active.pop-drop{
	display: block;
	position: fixed;
	top:0;
	bottom: 0;
	right: 0;
	left: 0;
	z-index: 9999;
	background-color: rgba(0, 0, 0, .8);
}

.pop-up.active.pop-box{
	display: block;
	position: fixed;
	margin: 0 auto;
	top: 40px;
	left: 2%;
	right: 2%;
	bottom: 40px;
	padding: 20px 10px 10px;
	background: #fff;
	overflow: scroll;
	z-index: 9999;
}

.shortcode-button-wrapper{
	padding: 10px 50px;
}

.backup-wrapper {
	margin-bottom: 20px;
}

/*
  This is the selector for the Google Maps Places Autocomplete container. We need the z-index to be at least as high
  as our pop-up or it won't show.
 */
.pac-container {
    z-index: 9999 !important;
}
