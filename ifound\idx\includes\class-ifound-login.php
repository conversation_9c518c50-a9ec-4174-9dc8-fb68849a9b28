<?
/**
 * iFoundLogin class
 *
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundLogin {
	
	/**
	 * init iFOUND_login class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}
	
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	 
	public function __construct() {
		
		add_action( 'wp_footer', array( $this, 'body' ), 999 );
		add_action( 'ifound_login_footer', array( $this, 'button' ) );

		add_filter( 'login_redirect', array( $this, 'login_redirect' ), 10, 3 );

		add_action( 'wp_ajax_pop_logout', array( $this, 'pop_logout' ) );
		add_action( 'wp_ajax_nopriv_pop_logout', array( $this, 'pop_logout' ) );
		
	}
	
	public function pop_logout(){
		
		check_ajax_referer( 'login_secure_me', 'ifound_login_nonce' );
		
		wp_logout();
		
		die();
		
	}
	
	/** NOTE: This loads with MLS disclosure iFOUND::footer */
	public function button(){
		
		?><div class="footer-login-body">
		
			<div class="ifound-wrap"><?
			
				if( is_user_logged_in() ) { ?>
				
					<div id="ifound-footer-logout">
						<a class="footer-login"><? _e( 'AGENT LOGOUT', 'ifound' ); ?></a>
					</div>
					
					<div id="ifound-footer-logout">
						<a class="footer-login" href="<? echo admin_url( '/' ); ?>"><? _e( 'ADMIN', 'ifound' ); ?></a>
					</div><?

					do_action( 'ifound_footer_links' );

				} else {

					?><div id="ifound-footer-login"><a class="footer-login login-pop"><? _e( 'AGENT LOGIN', 'ifound' ); ?></a></div><?

				} ?>
				
			</div>
	
		</div><?

	}
	
	public function body(){ ?>
		
		<a class="ifound-login-backdrop login ifound-login-close" id="login-pop-overlay"></a>
		
		<div class="ifound-login-popup login" id="login-popup">
			
			<div class="ifound-wrap">
				
				<div class="center">
				
					<i class="fal fa-times ifound-login-close" aria-hidden="true"></i>
					
					<h2 class="login-heading"><? _e( 'Agent Login', 'ifound' ); ?></h2>
					
					<? 
					$args = array( 'remember' => false, 'redirect' => ( is_ssl() ? 'https://' : 'http://' ) . $_SERVER['HTTP_HOST'] . '/wp-admin/', );
					wp_login_form( $args );
					?>
					
					<a rel="nofollow" href="<? echo wp_lostpassword_url( get_permalink() ); ?>" title="Lost Password"><? _e( 'Lost Password', 'ifound' ); ?></a>
					
				
				</div>
			
			</div>
		
		</div><?	
			
	}
	
	public function login_redirect( $redirect_to, $request, $user ) {
	
		if ( isset( $user->roles ) && is_array( $user->roles ) ) {
	
			if ( in_array( 'administrator', $user->roles ) ) {
		
				return $redirect_to;
		
			} else {
			
				return admin_url( 'index.php' );
			}
	
		} else {

			return $redirect_to;

		}

	}
	
}
