<?
/**
 * iFound_Property_Video class
 *
 * Display a property video in widget areas.
 *
 * @since 3.6.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


class iFound_Property_Video extends WP_Widget {
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Property Video', 
			array(
			'description' => 'Add a YouTube Video to your Property Details Pages.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {

		if( $video_code = apply_filters( 'ifound_video_code', false ) ) {

			echo $args['before_widget']; ?>

			<div class="ifound-property-video">

				<div class="widgets-wrap">

					<div class="video-responsive">

						<iframe width="100%" height="100%" src="https://www.youtube.com/embed/<? echo $video_code; ?>" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>	

					</div>

				</div>

			</div><?

			echo $args['after_widget'];

		}
		
	}
	
}
