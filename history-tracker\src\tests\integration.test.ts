import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { promises as fs } from 'fs'
import path from 'path'
import { processJsonFiles } from '../fileProcessor.js'
import { processListingRecords } from '../historyTracker.js'
import {
	getConnection,
	closeConnection,
	getLatestPropertyHistory,
	insertPropertyHistory,
	upsertPropertyChanges,
} from '../database.js'
import config from '../config.js'

// These tests require a test database to be available
// Skip by default unless TEST_DATABASE is set
const shouldRunIntegrationTests = process.env.TEST_DATABASE === 'true'

const testDataDir = path.join(process.cwd(), 'data', 'test', 'residential')

describe.skipIf(!shouldRunIntegrationTests)('Integration Tests', () => {
	beforeAll(async () => {
		// Load configuration
		await config.load()

		// Create test data directory
		await fs.mkdir(testDataDir, { recursive: true })

		// Setup test database tables with updated schema
		const conn = await getConnection()

		// Drop and recreate tables with correct Price columns
		await conn.execute('DROP TABLE IF EXISTS test_residential_property_changes')
		await conn.execute(`
			CREATE TABLE test_residential_property_changes (
				ListingId VARCHAR(255) PRIMARY KEY,
				StandardStatus VARCHAR(255),
				StandardStatus_ModificationTimestamp DATETIME(3) NULL COMMENT 'ModificationTimestamp when StandardStatus change was detected',
				StandardStatus_updated_at DATETIME(3) NULL COMMENT 'When StandardStatus was updated by HistoryTracker',
				Price DECIMAL(14,2),
				Price_ModificationTimestamp DATETIME(3) NULL COMMENT 'ModificationTimestamp when Price change was detected',
				Price_updated_at DATETIME(3) NULL COMMENT 'When Price was updated by HistoryTracker',
				created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
				updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
				INDEX StandardStatus (StandardStatus),
				INDEX StandardStatus_ModificationTimestamp (StandardStatus_ModificationTimestamp)
			)
		`)

		await conn.execute('DROP TABLE IF EXISTS test_residential_property_history')
		await conn.execute(`
			CREATE TABLE test_residential_property_history (
				id INT AUTO_INCREMENT PRIMARY KEY,
				ListingId VARCHAR(255) NOT NULL,
				StandardStatus VARCHAR(255),
				Price DECIMAL(14,2),
				ModificationTimestamp DATETIME(3),
				created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
				INDEX idx_listing_id (ListingId),
				INDEX idx_modification_timestamp (ModificationTimestamp),
				INDEX idx_created_at (created_at)
			)
		`)
	})

	afterAll(async () => {
		// Clean up test data
		try {
			const conn = await getConnection()
			await conn.execute(
				'DELETE FROM test_residential_property_history WHERE ListingId LIKE "TEST%"'
			)
			await conn.execute(
				'DELETE FROM test_residential_property_changes WHERE ListingId LIKE "TEST%"'
			)
		} catch (error) {
			console.warn('Failed to clean up test data:', error)
		}

		// Clean up test data directory
		try {
			await fs.rm(testDataDir, { recursive: true })
		} catch (error) {
			console.warn('Failed to clean up test data directory:', error)
		}

		await closeConnection()
	})

	beforeEach(async () => {
		// Clean up any test JSON files
		try {
			const files = await fs.readdir(testDataDir)
			for (const file of files) {
				if (file.endsWith('.json')) {
					await fs.unlink(path.join(testDataDir, file))
				}
			}
		} catch (error) {
			// Directory might not exist yet
		}

		// Clean up history tables for fresh test state
		const conn = await getConnection()
		await conn.execute('DELETE FROM test_residential_property_history WHERE ListingId LIKE "TEST%"')
		await conn.execute('DELETE FROM test_residential_property_changes WHERE ListingId LIKE "TEST%"')
	})

	describe('Database Operations', () => {

		it('should insert and retrieve property history', async () => {
			const historyData = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z'),
			}

			await insertPropertyHistory(historyData, 'test_residential_property_history')

			const retrieved = await getLatestPropertyHistory(
				'TEST123',
				'test_residential_property_history'
			)
			expect(retrieved).toBeTruthy()
			expect(retrieved?.ListingId).toBe('TEST123')
			expect(retrieved?.StandardStatus).toBe('Active')
			expect(retrieved?.Price).toBe(500000)
		})

		it('should upsert property changes', async () => {
			const now = new Date()
			const changesData = {
				ListingId: 'TEST123',
				StandardStatus: 'Sold',
				Price: 550000,
				StandardStatus_ModificationTimestamp: new Date('2024-01-01T12:00:00Z'),
				StandardStatus_updated_at: now,
				Price_ModificationTimestamp: new Date('2024-01-01T12:00:00Z'),
				Price_updated_at: now,
			}

			await upsertPropertyChanges(changesData, 'test_residential_property_changes')

			const conn = await getConnection()
			const [rows] = await conn.execute(
				'SELECT * FROM test_residential_property_changes WHERE ListingId = ?',
				['TEST123']
			)

			const results = rows as any[]
			expect(results).toHaveLength(1)
			expect(results[0].StandardStatus).toBe('Sold')
			expect(parseFloat(results[0].Price)).toBe(550000)
		})
	})

	describe('End-to-End File Processing', () => {
		it('should process JSON files and track first-time listings', async () => {
			// Create test JSON file with new format
			const testRecords = {
				value: [
					{
						ListingId: 'TEST123',
						StandardStatus: 'Active',
						ListPrice: 500000,
						ModificationTimestamp: '2024-01-01T10:00:00Z'
					},
					{
						ListingId: 'TEST456',
						StandardStatus: 'Pending',
						ListPrice: 600000,
						ModificationTimestamp: '2024-01-01T11:00:00Z'
					}
				]
			}
			
			const jsonFile1 = path.join(testDataDir, '2024-01-01-sync.json')
			await fs.writeFile(jsonFile1, JSON.stringify(testRecords))

			let processedRecords: any[] = []
			const mockProcessor = async (records: any[]) => {
				processedRecords = records
				await processListingRecords(records, 'test', 'residential')
			}

			const results = await processJsonFiles(testDataDir, mockProcessor)

			expect(results).toHaveLength(1)
			expect(results[0].processed).toBe(true)
			expect(results[0].records).toHaveLength(2)
			expect(results[0].records[0].ListingId).toBe('TEST123')
			expect(results[0].records[1].ListingId).toBe('TEST456')
			expect(processedRecords).toHaveLength(2)

			// Verify history was created
			const history123 = await getLatestPropertyHistory(
				'TEST123',
				'test_residential_property_history'
			)
			const history456 = await getLatestPropertyHistory(
				'TEST456',
				'test_residential_property_history'
			)

			expect(history123).toBeTruthy()
			expect(history456).toBeTruthy()

			// Verify no changes entries were created (first time)
			const conn = await getConnection()
			const [changeRows] = await conn.execute(
				'SELECT COUNT(*) as count FROM test_residential_property_changes WHERE ListingId IN ("TEST123", "TEST456")'
			)
			expect((changeRows as any)[0].count).toBe(0)

			// Verify file was deleted
			await expect(fs.access(jsonFile1)).rejects.toThrow()
		})

		it('should detect and track changes on subsequent runs', async () => {
			// First, establish baseline with initial record
			const initialRecord = {
				ListingId: 'TEST123',
				StandardStatus: 'Active',
				Price: 500000,
				ModificationTimestamp: new Date('2024-01-01T10:00:00Z')
			}
			await processListingRecords([initialRecord], 'test', 'residential')

			// Create another JSON file with changed data
			const updatedRecord = {
				value: [{
					ListingId: 'TEST123',
					StandardStatus: 'Sold',
					ListPrice: 550000,
					ModificationTimestamp: '2024-01-01T13:00:00Z'
				}]
			}
			
			const jsonFile2 = path.join(testDataDir, '2024-01-01-sync2.json')
			await fs.writeFile(jsonFile2, JSON.stringify(updatedRecord))

			const results = await processJsonFiles(testDataDir, records =>
				processListingRecords(records, 'test', 'residential')
			)

			expect(results).toHaveLength(1)
			expect(results[0].processed).toBe(true)

			// Verify history was updated
			const conn2 = await getConnection()
			const [historyRows] = await conn2.execute(
				'SELECT COUNT(*) as count FROM test_residential_property_history WHERE ListingId = "TEST123"'
			)
			expect((historyRows as any)[0].count).toBe(2) // Original + updated

			// Verify changes were tracked
			const [changeRows] = await conn2.execute(
				'SELECT * FROM test_residential_property_changes WHERE ListingId = "TEST123"'
			)
			const changes = changeRows as any[]
			expect(changes).toHaveLength(1)
			expect(changes[0].StandardStatus).toBe('Sold')
			expect(parseFloat(changes[0].Price)).toBe(550000)
			expect(changes[0].StandardStatus_ModificationTimestamp).toBeTruthy()
			expect(changes[0].Price_ModificationTimestamp).toBeTruthy()
		})

		it('should handle Closed listings using ClosePrice', async () => {
			// Create test JSON file with a closed listing
			const testRecords = {
				value: [
					{
						ListingId: 'TEST789',
						StandardStatus: 'Closed',
						ListPrice: 450000,
						ClosePrice: 435000,
						ModificationTimestamp: '2024-01-01T15:00:00Z'
					}
				]
			}
			
			const jsonFile = path.join(testDataDir, '2024-01-01-closed.json')
			await fs.writeFile(jsonFile, JSON.stringify(testRecords))

			const results = await processJsonFiles(testDataDir, records =>
				processListingRecords(records, 'test', 'residential')
			)

			expect(results).toHaveLength(1)
			expect(results[0].processed).toBe(true)
			expect(results[0].records).toHaveLength(1)
			
			// Verify the record has the ClosePrice as Price (not ListPrice)
			expect(results[0].records[0].ListingId).toBe('TEST789')
			expect(results[0].records[0].StandardStatus).toBe('Closed')
			expect(results[0].records[0].Price).toBe(435000) // Should be ClosePrice, not ListPrice

			// Verify history was created with ClosePrice
			const history = await getLatestPropertyHistory(
				'TEST789',
				'test_residential_property_history'
			)
			expect(history).toBeTruthy()
			expect(history?.Price).toBe(435000) // ClosePrice should be stored as Price
			expect(history?.StandardStatus).toBe('Closed')

			// Verify file was deleted
			await expect(fs.access(jsonFile)).rejects.toThrow()
		})

		it('should throw error immediately upon invalid JSON', async () => {
			// Create an invalid JSON file
			const invalidJsonFile = path.join(testDataDir, '2024-01-01-invalid.json')
			await fs.writeFile(invalidJsonFile, '{ invalid json }')

			// Should throw immediately instead of returning error results
			await expect(processJsonFiles(testDataDir, records =>
				processListingRecords(records, 'test', 'residential')
			)).rejects.toThrow('Failed to process file 2024-01-01-invalid.json: Invalid JSON format')

			// Verify file was NOT deleted due to error
			await expect(fs.access(invalidJsonFile)).resolves.toBeUndefined()
		})

		it('should rollback transaction when database error occurs', async () => {
			// Create a JSON file with records that will start processing but fail partway through
			const testRecords = {
				value: [
					{
						ListingId: 'ROLLBACK_TEST_1',
						StandardStatus: 'Active',
						ListPrice: 500000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					},
					{
						ListingId: 'ROLLBACK_TEST_2', 
						StandardStatus: 'Active',
						ListPrice: 600000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			}
			
			const jsonFile = path.join(testDataDir, '2024-01-01-rollback.json')
			await fs.writeFile(jsonFile, JSON.stringify(testRecords))

			// Count records before processing
			const conn = await getConnection()
			const [beforeRows] = await conn.execute(
				'SELECT COUNT(*) as count FROM test_residential_property_history WHERE ListingId LIKE "ROLLBACK_TEST_%"'
			)
			const beforeCount = (beforeRows as any)[0].count

			// Create a JSON file with a malformed timestamp that will cause an error
			const badTestRecords = {
				value: [
					{
						ListingId: 'ROLLBACK_TEST_3',
						StandardStatus: 'Active',
						ListPrice: 500000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					},
					{
						ListingId: 'ROLLBACK_TEST_4',
						StandardStatus: 'Active', 
						ListPrice: 600000,
						ModificationTimestamp: 'invalid-date-format' // This will cause an error
					}
				]
			}

			const badJsonFile = path.join(testDataDir, '2024-01-01-bad.json')
			await fs.writeFile(badJsonFile, JSON.stringify(badTestRecords))

			// This should fail due to invalid date format
			await expect(processJsonFiles(testDataDir, records =>
				processListingRecords(records, 'test', 'residential')
			)).rejects.toThrow()

			// Verify that NO records were committed due to transaction rollback
			const [afterRows] = await conn.execute(
				'SELECT COUNT(*) as count FROM test_residential_property_history WHERE ListingId LIKE "ROLLBACK_TEST_%"'  
			)
			const afterCount = (afterRows as any)[0].count

			// Count should be the same (no partial commits)
			expect(afterCount).toBe(beforeCount)

			// Verify files were not deleted due to processing failure
			await expect(fs.access(badJsonFile)).resolves.toBeUndefined()
		})

		it('should process multiple files in chronological order', async () => {
			// Create multiple JSON files with different timestamps
			const files = [
				{ 
					name: '2024-01-03-sync.json', 
					data: { value: [{ ListingId: 'TEST123', StandardStatus: 'Active', ListPrice: 500000, ModificationTimestamp: '2024-01-03T10:00:00Z' }] }
				},
				{ 
					name: '2024-01-01-sync.json', 
					data: { value: [{ ListingId: 'TEST456', StandardStatus: 'Pending', ListPrice: 600000, ModificationTimestamp: '2024-01-01T10:00:00Z' }] }
				},
				{ 
					name: '2024-01-02-sync.json', 
					data: { 
						value: [
							{ ListingId: 'TEST123', StandardStatus: 'Active', ListPrice: 500000, ModificationTimestamp: '2024-01-02T10:00:00Z' },
							{ ListingId: 'TEST456', StandardStatus: 'Pending', ListPrice: 600000, ModificationTimestamp: '2024-01-02T11:00:00Z' }
						] 
					}
				},
			]

			for (const file of files) {
				await fs.writeFile(path.join(testDataDir, file.name), JSON.stringify(file.data))
			}

			let processOrder: string[] = []
			const trackingProcessor = async (records: any[]) => {
				processOrder.push(records.map(r => r.ListingId).join(','))
				await processListingRecords(records, 'test', 'residential')
			}

			const results = await processJsonFiles(testDataDir, trackingProcessor)

			expect(results).toHaveLength(3)
			expect(results.every(r => r.processed)).toBe(true)

			// Verify files were processed in chronological order (alphabetical)
			expect(processOrder).toEqual(['TEST456', 'TEST123,TEST456', 'TEST123'])
		})
	})
})
