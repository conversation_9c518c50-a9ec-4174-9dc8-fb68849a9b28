{"name": "ifound_scripts", "version": "0.1.0", "scripts": {"build:custom": "wp-scripts build --output-path=build-without-react src/non-admin.js src/admin.js && wp-scripts build --webpack-no-externals --output-path=build-with-react src/non-admin.js src/admin.js", "start": "wp-scripts start --webpack-no-externals --output-path=build-with-react", "start:custom": "wp-scripts start --webpack-no-externals --output-path=build-with-react src/non-admin.js src/admin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@wordpress/scripts": "^15.0.1"}, "dependencies": {"@dnd-kit/core": "^4.0.1", "@dnd-kit/sortable": "^5.0.0", "@react-google-maps/api": "^2.7.0", "axios": "^0.26.0", "classnames": "^2.2.6", "date-fns": "^2.19.0", "date-fns-tz": "^1.1.3", "immer": "^9.0.6", "prop-types": "^15.7.2", "react-select": "^5.3.2", "react-tabs": "^3.2.0", "react-toast-notifications": "^2.4.3", "react-tooltip": "^4.2.21", "slugify": "^1.4.7", "url-join": "^4.0.1", "use-debounce": "^9.0.4"}}