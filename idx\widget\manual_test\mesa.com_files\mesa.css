body
{
	margin:0%;
	margin-bottom:30px;
	background-color:#202538;
	background-image:url('../images/mesa_bg.gif');
	background-repeat:repeat-x;
	background-position:top left;
}

.city_logo
{
	margin-bottom:8px;
}

a
{
	color:#bA6336;
	text-decoration:underline;
}

td,tr
{
	font-size:12px;
	font-family:arial;
	color:#737573;
}

h1
{
font-size:18px;
margin:0px;
padding:0px;
}

legend
{
	color:#636563;
	font-size:14px;
	font-weight:bold;
}

.top_header_table
{
	padding-top:10px;
	padding-left:25px;
	padding-right:30px;
	height:66px;
	vertical-align:top;
}

.text1
{
	color:#fff;
	font-size:11px;
	font-family:arial;
}

.text2
{
	color:#B6592C;
	font-size:10px;
	font-family:arial;
}

.text3
{
	color:#C4BCA7;
	font-size:26px;
	font-family:helvetica,arial;
}

.text4
{
	color:#fff;
	font-size:13px;
	font-family:helvetica,arial;
}

.text5
{
	color:#6077C7;
	font-size:15px;
	font-family:helvetica,arial;
}

.text6
{
	color:#757575;
	font-size:11px;
	font-family:arial;
	line-height:14px;
}

.text7
{
	color:#bA6336;
	font-size:12px;
	font-family:arial;
}

.text8
{
	color:#757575;
	font-size:11px;
	font-family:arial;
}

.text9
{
	color:#6077C7;
	font-size:17px;
	font-family:helvetica,arial;
}

.text10
{
	color:#bA6336;
	font-size:11px;
	font-family:arial;
}

.text11
{
	color:#757575;
	font-size:10px;
	font-family:arial;
}

.text12
{
	color:#fff;
	font-size:10px;
	font-family:arial;
}

.text13
{
	font-family:helvetica,arial;
	font-size:20px;
	color:#6076c7;
}

.text14
{
	font-family:helvetica,arial;
	font-size:26px;
	color:#c4bca7;
}

.text15
{
	font-family:helvetica,arial;
	font-size:17px;
	color:#6076c7;
}

.text16
{
	font-family:helvetica,arial;
	font-size:11px;
	color:#868686;
}

.text17
{
	color:#B6592C;
	font-size:17px;
	font-family:helvetica,arial;
}

.text18
{
	color:#b0adad;
	font-size:11px;
	font-family:helvetica,arial;
}

.link1
{
	color:#fff;
	text-decoration:underline;
}

.link2
{
	color:#fff;
	text-decoration:none;
}

.link3,.link3:active,link3:visited
{
	color:#bA6336;
	text-decoration:none;
}

.link3:hover
{
	color:#bA6336;
	text-decoration:underline;
}

.link4
{
	color:#6077C7;
	text-decoration:none;
}

.link5,.link5:active,link5:visited
{
	color:#0C5F8F;
	text-decoration:none;
}

.link5:hover
{
	color:#0C5F8F;
	text-decoration:underline;
}

.fld1
{
	background-color:#fff;
	color:#757575;
	font-size:11px;
	font-family:arial;
	height:22px;
	padding-top:3px;
}

.fld2
{
	background-color:#fff;
	border:1px #757575 solid;
	color:#757575;
	font-size:10px;
	font-family:arial;
}

.main_header
{
	position:relative;
	width:960px;
	min-height:339px;
	overflow:hidden;
	clear:both;
}

* html .main_header
{
	height:339px;
	overflow:visible;
}

.main_header a i {
	visibility: hidden;
}
.main_header a.header_link1 {
	position: absolute;
	top: 48px;
	left: 640px;
	width: 290px;
	min-height: 15px;
	text-decoration: none;
	font-size: 10px;
	color:#fff;
	text-decoration:none;
	line-height:12px;
	padding-top:4px;
}
* html .main_header a.header_link1 {
	height: 15px;
	overflow: visible;
}
.main_header a.header_link2 {
	position: absolute;
	top: 140px;
	left: 640px;
	width: 290px;
	min-height: 15px;
	text-decoration: none;
	font-size: 10px;
	color:#fff;
	text-decoration:none;
	line-height:12px;
	padding-top:4px;
}
* html .main_header a.header_link2 {
	height: 15px;
	overflow: visible;
}
.main_header a.header_link3 {
	position: absolute;
	top: 230px;
	left: 640px;
	width: 290px;
	min-height: 15px;
	text-decoration: none;
	font-size: 10px;
	color:#fff;
	text-decoration:none;
	line-height:12px;
	padding-top:4px;
}
* html .main_header a.header_link3 {
	height: 15px;
	overflow: visible;
}

.main_area
{
	height:100px;
	background-color:#fff;
	vertical-align:top;
	padding-left:7px;
	padding-right:7px;
}

.box_brown
{
	height:10px;
	background-image:url('../images/box_brown_bg.gif');
	background-repeat:repeat-y;
	background-position:top left;
	vertical-align:top;
	padding-left:16px;
	padding-right:16px;
	padding-bottom:5px;
}

.box_brown_sub
{
	height:100px;
	background-color:#F3F1EB;
	vertical-align:top;
	padding:10px;
}

.box_brown_small
{
	background-image:url('../images/box_brown_bg_small.gif');
	background-repeat:no-repeat;
	background-position:top left;
	height:239px;
	padding:15px;
	vertical-align:top;
}

.box_brown_small2
{
	background-image:url('../images/box_brown_bg_small2.gif');
	background-repeat:repeat-y;
	background-position:top left;
	height:100px;
	padding:15px;
	padding-top:0px;
	vertical-align:top;
}

.box_brown_fade
{
	height:187px;
	background-image:url('../images/box_brown_fadebg.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	padding-top:17px;
	padding-right:4px;
}

.box_brown_fade_small
{
	height:136px;
	background-image:url('../images/box_brown_small_fadebg.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	padding:25px;
	padding-top:17px;
	padding-right:10px;
}

.menu_header_purple
{
	height:26px;
	background-image:url('../images/menu_purple_header.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	color:#fff;
	font-size:13px;
	font-family:helvetica,arial;
	padding-left:10px;
	padding-top:4px;
}

.tab_long_purple
{
	height:25px;
	width:192px;
	background-image:url('../images/tab_purple_long.gif');
	background-repeat:no-repeat;
	background-position:top left;
}

.box_white
{
	height:100px;
	background-color:#fff;
	vertical-align:top;
	padding:11px;
}

.box_white2
{
	height:70px;
	background-color:#fff;
	vertical-align:top;
	padding:11px;
}

.box_white_sub
{
	height:100px;
	background-color:#fff;
	vertical-align:top;
	padding:20px;
	padding-left:26px;
	padding-right:26px;
}

.box_white_sub2
{
	height:100px;
	background-color:#fff;
	vertical-align:top;
	padding:10px;
	padding-left:12px;
	padding-right:12px;
}

.box_white_location1
{
	height:85px;
	background-color:#fff;
	vertical-align:top;
	padding:11px;
	padding-left:15px;
	border-bottom:4px #F2EFE8 solid;
	border-right:4px #F2EFE8 solid;
}

.box_white_location2
{
	height:85px;
	background-color:#fff;
	vertical-align:top;
	padding:11px;
	border-bottom:4px #F2EFE8 solid;
}

.box_white_shadow
{
	height:50px;
	background-image:url('../images/box_white_shadow.gif');
	background-repeat:repeat-y;
	background-position:top left;
	vertical-align:top;
	padding-left:16px;
	padding-right:16px;
	padding-bottom:5px;
}

.box1
{
	height:50px;
	padding:10px;
	background-color:#f8f7f6;
	vertical-align:top;
}

.box2
{
	height:50px;
	padding:10px;
	background-color:#f3f1eb;
	vertical-align:top;
}

.search_box_brown
{
	height:195px;
	background-image:url('../images/search_box_brown.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	padding:15px;
}

.search_box_brown_long
{
	height:111px;
	background-image:url('../images/search_box_brown_long.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	padding:15px;
}

.search_box_brown_search
{
	height:195px;
	background-image:url('../images/search_box_brown2.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
	padding:15px;
}

.cal_icon
{
	height:142px;
	background-image:url('../images/cal_icon.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:top;
}

.cal_date
{
	font-family:helvetica,arial;
	font-size:72px;
	color:#000;
}

.box_open
{
	padding-left:25px;
	padding-right:20px;
	height:250px;
	background-color:#fff;
	vertical-align:top;
}

.form_title
{
	font-size:16px;
	font-weight:bold;
	color:#A54421;
}

/*Story Page CSS Starts*/
#story_details h1
{
	font-size:21px;
	margin:0%;
	padding:0%;
	color:#A54421;
}

#story_details h2
{
	font-size:14px;
	margin:0%;
	padding:0%;
	color:#A54421;
}

#story_details .text
{
	line-height:16px;
	font-size:12px;
	color:#000;
}

#story_details .box_small
{
	height:20px;
	background-color:#F7F7F7;
	vertical-align:middle;
	border:1px #CCCCCC solid;
	padding:4px;
}

#story_details .box
{
	height:100px;
	background-color:#F5F5F5;
	vertical-align:top;
	padding:4px;
	border:1px #DDDDDD solid;
}

#story_details .box_title
{
	color:#000;
	font-size:14px;
	font-weight:bold;
}
/*Story Page CSS Ends*/

/*Story Archives CSS Starts*/
#story_archive h1
{
	font-size:21px;
	margin:0%;
	padding:0%;
	color:#A54421;
}
/*Story Archives CSS Ends*/

/*Event Page CSS Starts*/
#event_details .main_title
{
	font-size:27px;
	margin:0%;
	padding:0%;
	color:#c4bca7;
	font-weight:none;
}
/*Event Page CSS Ends*/

/*Listing Page CSS Starts*/
#listing_details .main_title
{
	font-size:27px;
	margin:0%;
	padding:0%;
	color:#c4bca7;
	font-weight:none;
}
/*Listing Page CSS Ends*/

/*Account Section Starts*/
.account_header
{
	background-color:#363E53;
	height:20px;
	vertical-align:middle;
	color:#fff;
	font-weight:bold;
	padding:4px;
}

.account_box
{
	height:120px;
	background-color:#F7F7F7;
	vertical-align:top;
	border:1px #CCCCCC solid;
	border-top:0px solid;
	padding:6px;
}

.account_row
{
	height:20px;
	background-color:#F7F7F7;
	vertical-align:middle;
	padding:4px;
}
/*Account Section Ends*/

/*Tab Main Story Starts*/


.indentmenu{
font:12px arial;
width:100%;
}

.indentmenu ul{
margin: 0;
padding: 0;
float: left;
/* width: 80%; width of menu*/
}

.indentmenu ul li{
display: inline;
}

.indentmenu ul li a{
text-decoration: none;
float:left;
z-index: 1;
padding: 3px 7px;
margin-right: 3px;
border: 2px solid #fff;
color: #fff;
border-right: 1px solid #fff;
background-color:#6077C7;
}

.indentmenu ul li a:visited{
color: white;
}

.indentmenu ul li a.selected{
color: white !important;
background-color:#B5BDE2;
}


.tabcontentstyle{ /*style of tab content oontainer*/
border: 1px solid gray;
width: 450px;
margin-bottom: 1em;
padding: 10px;
}

.tabcontent{
display:none;
}

@media print {
.tabcontent {
display:block !important;
}
}

/*Tab Main Story Ends*/

.box_field
{
	height:30px;
	background-color:#EEEEEE;
	vertical-align:middle;
	font-size:11px;
	font-weight:bold;
	padding:4px;
}

.fld_photo
{
	height:50px;
	background-color:#EFEFEF;
	border:1px #cccccc solid;
	vertical-align:top;
	padding:6px;
}

.site_footer
{
	height:10px;
	border:1px #ccc solid;
	vertical-align:top;
	padding:6px;
	background-color:#fff;
}

p.acities
{
	margin:0px;
	padding:0px;
	line-height:20px;
}

.main_footer_link
{
	color:#909090;
	text-decoration:none;
}

.main_footer
{
	width:960px;
	height:50px;
	background-image:url('../images/footer_bg.gif');
	background-repeat:no-repeat;
	background-position:top left;
	vertical-align:middle;
	padding-left:26px;
	font-family:arial;
	font-size:10px;
	color:#909090;
}

/*Site Forum Starts*/
.forum_box
{
	min-height:10px;
	overflow:hidden;
	background:url('../images/box_brown_bg.gif') repeat-y top left;
	padding:0px 5px 0px 5px;
	width:658px;
}

* html .forum_box
{
	height:10px;
	overflow:visible;
}

.forum_arrow
{
	float:left;
	margin:0px 5px 0px 5px;
}

table.forum_table
{
	border-width:0px;
	border-spacing:0px;
	border-style:solid;
	border-collapse:collapse;
}

table.forum_table a
{
	color:#d47547;
	text-decoration:underline;
}

table.forum_table th
{
	border:0px solid;
	padding:4px;
	background:url('../images/sites/mesa/forum_header.gif') repeat-x top left;
	height:26px;
	line-height:18px;
	color:#fff;
	font-weight:bold;
	font-family:arial;
	text-align:center;
}

table.forum_table td
{
	border-width:1px;
	border-style:solid;
	padding:4px;
	border-color:#ccc;
	background-color:white;
}

h1.forum_title
{
	margin:0px 0px 5px 0px;
	padding:0px;
	font-size:16px;
	line-height:14px;
	color:#d47547;
}

/*Site Forum Ends*/

.site_weather
{
	margin:0px 10px 0px 0px;
	float:left;
	width:75px;
	min-height:1px;
	overflow:hidden;
}

* html .site_weather
{
	height:1px;
	overflow:visible;
}

.site_weather_end
{
	margin:0px;
	float:left;
	width:75px;
	min-height:1px;
	overflow:hidden;
}

* html .site_weather_end
{
	height:1px;
	overflow:visible;
}

.site_weather_header
{
	clear:both;
	background:#6077C7;
	border:1px #fff solid;
	line-height:20px;
	color:#fff;
	text-align:center;
	font-weight:bold;
}

.site_weather_info
{
	clear:both;
	background:#fff;
	color:#333;
	padding:5px;
	font-size:10px;
	font-family:verdana;
	min-height:100px;
	overflow:hidden;
	text-align:center;
}

* html .site_weather_info
{
	height:100px;
	overflow:visible;
}

.ad_header {
min-height:20px;
line-height:20px;
overflow:hidden;
background:#202538;
font-weight:bold;
color:#fff;
padding:0px 4px 0px 4px
}

.ad_content {
border:1px #ccc solid;
background:#efefef;
padding:2px 4px 4px 4px;
line-height:16px;
margin-bottom:15px
}

.archive_area {
clear:both;
margin-bottom:20px;
padding-bottom:20px;
min-height:10px;
overflow:hidden;
border-bottom:1px #d6d6d6 dashed;
}

* html .archive_area {
height:10px;
overflow:visible;
}

.archive_area .photo {
float:left;
margin-right:10px;
}

.archive_area .photo img {
border:1px #333 solid;
}

.archive_area .content {
min-height:10px;
overflow:hidden;
}

* html .archive_area .content {
height:10px;
overflow:visible;
}

.archive_area h2 {
margin:0px;
padding:0px;
font-size:13px;
}

.archive_area p {
margin:0px;
padding:0px;
line-height:16px;
font-size:12px;
}