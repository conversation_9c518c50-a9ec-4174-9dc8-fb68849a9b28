import React, { useState } from 'react';
import merge from 'lodash/merge'
import cloneDeep from 'lodash/cloneDeep'
import get from 'lodash/get'
import set from 'lodash/set'

const possibleFields = [{
	name: 'list_price',
	display: 'Sale Price',
}, {
	name: 'close_price',
	display: 'Close/Sold Price',
}, {
	name: 'price_sqft',
	display: 'Price / Sqft',
}, {
	name: 'computed_days_on_market',
	display: 'Days on Market',
}];
const possibleFacets = [{
	name: 'median',
	display: 'Median',
}, {
	name: 'mean',
	display: 'Average',
}, {
	name: 'min',
	display: 'Low',
}, {
	name: 'max',
	display: 'High',
}];
function getFacets(field) {
	if (field.name === 'computed_days_on_market') {
		return [{
			name: 'mean',
			display: 'Average',
		}];
	}
	return possibleFacets;
}

function ShortcodeCreatorStats(props) {
	const initial = merge({}, {
		show: 'default',
		fields: {},
	}, props)
	const [show, setShow] = useState(initial.show);
	const [fields, setFields] = useState(initial.fields);

	const statsData = {
		show,
		fields,
	}

	function toggleField(fieldName, facetName) {
		const clonedFields = cloneDeep(fields);
		const val = get(clonedFields, [fieldName, facetName], false);
		set(clonedFields, [fieldName, facetName], !val);
		setFields(clonedFields);
	}

	return <div style={{ marginTop: '10px' }}>
		{
			// Instead of using real fields, we use a hidden field. By this I mean we leave off the "name"
			// attribute of the form elements.
			// This makes it easy to parse the values on the server from strings into real types in one fell swoop.
			// Otherwise, we'd have to coerce strings into their types like booleans, numbers, etc.
		}
		<input type="hidden" name="stats_data" value={JSON.stringify(statsData)} />

		<div>
			<label htmlFor="stats_show_default" style={{ marginRight: '10px' }}>
				<input id="stats_show_default" type="radio" value="default"
					   checked={show === 'default'} onChange={() => setShow('default')} /> Default stats
			</label>
			<label htmlFor="stats_show_choose">
				<input id="stats_show_choose" type="radio" value="choose"
					   checked={show === 'choose'} onChange={() => setShow('choose')} /> Choose stats
			</label>
		</div>

		{show === 'default' && <div style={{marginTop: '10px' }}>
			<div style={{ color: 'gray' }}>
				By default we show Sale Price (median), Close Price (median), Price / Sqft (median), and Days on Market (average)
			</div>
		</div>}

		{show === 'choose' && <div style={{ marginTop: '10px' }}>
			<div>Show these stats</div>
			<div style={{ marginTop: '4px', display: 'flex' }}>
				{possibleFields.map(field => {
					const id = 'ifound_stats_form_' + field.name
					return <div key={id} style={{ marginRight: '10px' }}>
						<div>{field.display}</div>
						<div style={{ marginLeft: '8px' }}>
							<div>
								{getFacets(field).map(facet => {
									const facetId = `${id}_${facet.name}`
									return <div key={facetId}>
										<label htmlFor={facetId}>
											<input
												id={facetId}
												type="checkbox"
												value={true}
												checked={
													fields[field.name]
														? fields[field.name][facet.name]
															? fields[field.name][facet.name]
															: false
														: false
												}
												onChange={() => toggleField(field.name, facet.name)}
											/> {facet.display}
										</label>
									</div>
								})}
							</div>
						</div>
					</div>
				})}
			</div>
		</div>}
	</div>
}

export default ShortcodeCreatorStats;
