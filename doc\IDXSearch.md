# IDX Search Functionality

[TOC]

## Search origination

Here's where searches come from and how they're used:

### API

[IDX Search API](SearchAPI.md)

This is the same API our Wordpress plugin uses, but we have occasionally given it out to consumers, such as <PERSON>. I don't think anyone currently uses it. However, it's useful for describing the capabilities available, and I reference it later in this document.

### Algorithm

See the [Old Search](OldSearch.md) page for details on the old PHP-based property search algorithm.

### Quick search

The quick search functionality involves a short list of fields to be search, like bedroom and bathrooms. For many values, we provide a dropdown to allow the user to select a value which we map later. For example, for price, the user might technically pick a price RANGE we call 'price02', and we later translate that to $100-159k.

Fields in quick search that involve a mapping/range:

* price - named
    * There is also the pricerange value, which is a comma split value of low and high
* square footage
* bathrooms
* bedrooms

Fields in quick search that don't really involve a mapping:

* zip
* city
* for search nearby (distance):
    * latlon
    * nearby_radius
* prince min
* price max
* square feet min
* square feet max
* property type

Quick search fields that I don't think we use anywhere:

* year built
* garages
* price per square foot

### Categories

I don't think anyone uses our old search categories. I've actually removed the Wordpress admin page where they are managed. But it might be worth checking with Dale.

### Advanced search

Customers can do direct 'advanced searches'. We have gotten rid of our old advanced search, and replaced it with a slightly modified version of our "Shortcode GUI" (explained in next section).

### Shortcode GUI in the Wordpress admin

Our clients can use our admin tool to create Wordpress shortcode, which is then interpreted into search parameters which are sent to the IDX server. Much of what those parameters look like is covered in the **API** section mentioned above. Here are notes about how it works internally.

The request to the IDX server includes this data:

* API key. This is how we look up which MLS to use.
* MLS class. We default to "es" for residential, but others are allowed, like rentals and land.

** I am stopping here to focus on Matt's remaining questions **

> my questions are about SEO metadata (still used, for what?), geocoding, RetsCloud

The SEO metadata hasn't been changed from how it used to work.

Goecoding is now optional, and we don't do it for RetsCloud-backed MLS's, because we pay to have RetsCloud do it for us. Otherwise, we still use Matt's old code which uses Google Maps' API.

I think Matt has already investigated the current spatial searches algorithm.

We have MLS-specific "property limiters", which handle each MLS in a special way as necessary. See e.g. `idx/includes/classes/Profound/MLS/ArmlsPropertyLimiter`, as well as its superclass.

RetsCloud doesn't change how we do things much. They add a few fields to the table for their own use. They add their own fields for latitude and longitude.

## Where searches DON'T come from

The Wordpress plugin communicates with the IDX server in many ways. Here I'll document a few of them that might be thought to be relevant here, but I think they're not.

* Looking up a single listing by ID, aka Property Details Page
* Featured listings
 * A client can choose zero or more featured listings. Technically, we have 'small' and 'large' featured listings from a code perspective. For each of these, the plugin fetches a handful of explicit listings by ID.

## Search capabilities desired

Here I'll distill what we want out of case 1024/1026/1122:

* Full text search on e.g. address, features
* Spatial search (search by polygon(s)). We already have this, but it's rudimentary and could be improved in speed.
* Handle more listings. Right now, we have approx 27,000 in ARMLS, but case 1122 bumps this to more like 270,000 which has proved too slow in MySQL (improved indexing might fix all our woes; I'm not sure).

## Things to document

* `words` parameter
* field mappings
* multiple MLS's
* MLS classes
* RetsCloud
* existing spatial search
* paging
* sorting
* aggregates