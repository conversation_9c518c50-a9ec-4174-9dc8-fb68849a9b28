/*
* jQuery UI CSS Framework
* Copyright (c) 2009 AUTHORS.txt (http://jqueryui.com/about)
* Dual licensed under the MIT (MIT-LICENSE.txt) and GPL (GPL-LICENSE.txt) licenses.
* To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Trebuchet%20MS,Verdana,Arial,sans-serif&fwDefault=normal&fsDefault=1em&cornerRadius=4px&bgColorHeader=464646&bgTextureHeader=01_flat.png&bgImgOpacityHeader=100&borderColorHeader=ffffff&fcHeader=ffffff&iconColorHeader=222222&bgColorContent=ffffff&bgTextureContent=01_flat.png&bgImgOpacityContent=75&borderColorContent=ffffff&fcContent=222222&iconColorContent=222222&bgColorDefault=444444&bgTextureDefault=03_highlight_soft.png&bgImgOpacityDefault=60&borderColorDefault=666666&fcDefault=ffffff&iconColorDefault=888888&bgColorHover=555555&bgTextureHover=03_highlight_soft.png&bgImgOpacityHover=75&borderColorHover=666666&fcHover=ffffff&iconColorHover=454545&bgColorActive=ffffff&bgTextureActive=01_flat.png&bgImgOpacityActive=65&borderColorActive=666666&fcActive=F6921E&iconColorActive=454545&bgColorHighlight=fbf9ee&bgTextureHighlight=02_glass.png&bgImgOpacityHighlight=55&borderColorHighlight=fcefa1&fcHighlight=363636&iconColorHighlight=2e83ff&bgColorError=fef1ec&bgTextureError=05_inset_soft.png&bgImgOpacityError=95&borderColorError=cd0a0a&fcError=cd0a0a&iconColorError=cd0a0a&bgColorOverlay=aaaaaa&bgTextureOverlay=01_flat.png&bgImgOpacityOverlay=0&opacityOverlay=30&bgColorShadow=aaaaaa&bgTextureShadow=01_flat.png&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=8px&offsetTopShadow=-8px&offsetLeftShadow=-8px&cornerRadiusShadow=8px
*/


/* MODIFIED TO BE SCOPED TO THE DEMOS PAGE - this prevents the documentation tabs & dialog from changing styles when the Themeroller bookmark is used to reskin demos */


/* Component containers
----------------------------------*/
#widget-docs.ui-widget, 
#demo-dialog.ui-widget { font-family: Trebuchet MS,Verdana,Arial,sans-serif; font-size: 1em; }

#widget-docs.ui-widget input, 
#widget-docs.ui-widget select, 
#widget-docs.ui-widget textarea, 
#widget-docs.ui-widget button,
#demo-dialog.ui-widget input, 
#demo-dialog.ui-widget select, 
#demo-dialog.ui-widget textarea, 
#demo-dialog.ui-widget button { font-family: Trebuchet MS,Verdana,Arial,sans-serif; font-size: 1em; }

#widget-docs .ui-widget-header, 
#demo-dialog .ui-widget-header { border: 1px solid #ffffff; background: #464646 url(images/464646_40x100_textures_01_flat_100.png) 50% 50% repeat-x; color: #ffffff; font-weight: bold; }

#widget-docs .ui-widget-header a,
#demo-dialog .ui-widget-header a { color: #ffffff; }

#widget-docs .ui-widget-content,
#demo-dialog .ui-widget-content { border: 1px solid #ffffff; background: #ffffff url(images/ffffff_40x100_textures_01_flat_75.png) 50% 50% repeat-x; color: #222222; }

#widget-docs .ui-widget-content a,
#demo-dialog .ui-widget-content a { color: #222222; }


/* Interaction states
----------------------------------*/
#widget-docs .ui-state-default, 
#widget-docs .ui-widget-content .ui-state-default,
#demo-dialog .ui-state-default, 
#demo-dialog .ui-widget-content .ui-state-default { border: 1px solid #666666; background: #555555 url(images/555555_40x100_textures_03_highlight_soft_75.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff; outline: none; }

#widget-docs .ui-state-default a, 
#demo-dialog .ui-state-default a { color: #ffffff; text-decoration: none; outline: none; }

#widget-docs .ui-state-hover, 
#widget-docs .ui-widget-content .ui-state-hover, 
#widget-docs .ui-state-focus, 
#widget-docs .ui-widget-content .ui-state-focus,
#demo-dialog .ui-state-hover, 
#demo-dialog .ui-widget-content .ui-state-hover, 
#demo-dialog .ui-state-focus, 
#demo-dialog .ui-widget-content .ui-state-focus { border: 1px solid #666666; background: #444444 url(images/444444_40x100_textures_03_highlight_soft_60.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff; outline: none; }

#widget-docs .ui-state-hover a,
#demo-dialog .ui-state-hover a { color: #ffffff; text-decoration: none; outline: none; }

#widget-docs .ui-state-active, 
#widget-docs .ui-widget-content .ui-state-active,
#demo-dialog .ui-state-active, 
#demo-dialog .ui-widget-content .ui-state-active { border: 1px solid #666666; background: #ffffff url(images/ffffff_40x100_textures_01_flat_65.png) 50% 50% repeat-x; font-weight: normal; color: #F6921E; outline: none; }

#widget-docs .ui-state-active a,
#demo-dialog .ui-state-active a { color: #F6921E; outline: none; text-decoration: none; }


/* Interaction Cues
----------------------------------*/
#widget-docs .ui-state-highlight, 
#widget-docs .ui-widget-content .ui-state-highlight,
#demo-dialog .ui-state-highlight, 
#demo-dialog .ui-widget-content .ui-state-highlight {border: 1px solid #fcefa1; background: #fbf9ee url(images/fbf9ee_40x100_textures_02_glass_55.png) 50% 50% repeat-x; color: #363636; }

#widget-docs .ui-state-error, 
#widget-docs .ui-widget-content .ui-state-error, 
#demo-dialog .ui-state-error, 
#demo-dialog .ui-widget-content .ui-state-error {border: 1px solid #cd0a0a; background: #fef1ec url(images/fef1ec_40x100_textures_05_inset_soft_95.png) 50% bottom repeat-x; color: #cd0a0a; }

#widget-docs .ui-state-error-text, 
#widget-docs .ui-widget-content .ui-state-error-text,
#demo-dialog .ui-state-error-text, 
#demo-dialog .ui-widget-content .ui-state-error-text { color: #cd0a0a; }

#widget-docs .ui-state-disabled, 
#widget-docs .ui-widget-content .ui-state-disabled,
#demo-dialog .ui-state-disabled, 
#demo-dialog .ui-widget-content .ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }

#widget-docs .ui-priority-primary, 
#widget-docs .ui-widget-content .ui-priority-primary,
#demo-dialog .ui-priority-primary, 
#demo-dialog .ui-widget-content .ui-priority-primary { font-weight: bold; }

#widget-docs .ui-priority-secondary, 
#widget-docs .ui-widget-content .ui-priority-secondary,
#demo-dialog .ui-priority-secondary, 
#demo-dialog .ui-widget-content .ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }


/* Icons
----------------------------------*/

/* states and images */
#demo-frame-wrapper .ui-icon, 
#widget-docs .ui-icon,
#demo-dialog .ui-icon { width: 16px; height: 16px; background-image: url(images/222222_256x240_icons_icons.png); }

#widget-docs .ui-widget-content .ui-icon, 
#demo-dialog .ui-widget-content .ui-icon {background-image: url(images/222222_256x240_icons_icons.png); }

#widget-docs .ui-widget-header .ui-icon {background-image: url(images/222222_256x240_icons_icons.png); }
#demo-dialog .ui-widget-header .ui-icon {background-image: url(images/ui-icons_aaaaaa_256x240.png); }

#widget-docs .ui-state-default .ui-icon,
#demo-dialog .ui-state-default .ui-icon { background-image: url(images/888888_256x240_icons_icons.png); }

#widget-docs .ui-state-hover .ui-icon, 
#widget-docs .ui-state-focus .ui-icon,
#demo-dialog .ui-state-hover .ui-icon, 
#demo-dialog .ui-state-focus .ui-icon {background-image: url(images/454545_256x240_icons_icons.png); }

#demo-dialog .ui-widget-header .ui-state-hover .ui-icon, 
#demo-dialog .ui-widget-header .ui-state-focus .ui-icon {background-image: url(images/ui-icons_aaaaaa_256x240.png); }

#widget-docs .ui-state-active .ui-icon,
#demo-dialog .ui-state-active .ui-icon {background-image: url(images/454545_256x240_icons_icons.png); }

#widget-docs .ui-state-highlight .ui-icon,
#demo-dialog .ui-state-highlight .ui-icon {background-image: url(images/2e83ff_256x240_icons_icons.png); }

#widget-docs .ui-state-error .ui-icon, 
#widget-docs .ui-state-error-text .ui-icon,
#demo-dialog .ui-state-error .ui-icon, 
#demo-dialog .ui-state-error-text .ui-icon {background-image: url(images/cd0a0a_256x240_icons_icons.png); }


/* Misc visuals
----------------------------------*/

/* Corner radius */
#widget-docs .ui-corner-tl,
#demo-dialog .ui-corner-tl { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; }

#widget-docs .ui-corner-tr,
#demo-dialog .ui-corner-tr { -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; }

#widget-docs .ui-corner-bl,
#demo-dialog .ui-corner-bl { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; }

#widget-docs .ui-corner-br,
#demo-dialog .ui-corner-br { -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }

#widget-docs .ui-corner-top,
#demo-dialog .ui-corner-top { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; }

#widget-docs .ui-corner-bottom,
#demo-dialog .ui-corner-bottom { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }

#widget-docs .ui-corner-right,
#demo-dialog .ui-corner-right {  -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }

#widget-docs .ui-corner-left,
#demo-dialog .ui-corner-left { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; }

#widget-docs .ui-corner-all,
#demo-dialog .ui-corner-all { -moz-border-radius: 4px; -webkit-border-radius: 4px; }


/* Overlays */
#widget-docs .ui-widget-overlay,
#demo-dialog .ui-widget-overlay { background: #aaaaaa url(images/aaaaaa_40x100_textures_01_flat_0.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); }

#widget-docs .ui-widget-shadow,
#demo-dialog .ui-widget-shadow { margin: -8px 0 0 -8px; padding: 8px; background: #aaaaaa url(images/aaaaaa_40x100_textures_01_flat_0.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); -moz-border-radius: 8px; -webkit-border-radius: 8px; }