<?php





// I don't think any of our clients use Wordpress Gutenberg blocks. Because the blocks were causing me some headache
// during deploying to SiteGround, and no one uses them, I'm going to remove them. Rather than delete the files, I'll
// leave them for posterity so it's easier to add blocks in the future if we ever return to using them.





/**
 * Registers all block assets so that they can be enqueued through the block editor
 * in the corresponding context.
 *
 * @see https://developer.wordpress.org/block-editor/tutorials/block-tutorial/applying-styles-with-stylesheets/
 */
function ifound_blocks_init() {
	$dir = dirname( __FILE__ );

	$script_asset_path = "$dir/build/index.asset.php";
	if ( ! file_exists( $script_asset_path ) ) {
		throw new Error(
			'You need to run `npm start` or `npm run build` for the "ifound/listings-search" block first.'
		);
	}
	$index_js     = 'build/index.js';
	$script_asset = require( $script_asset_path );
	wp_register_script(
		'ifound-block-editor-blocks',
		plugins_url( $index_js, __FILE__ ),
		$script_asset['dependencies'],
		$script_asset['version']
	);
	wp_set_script_translations( 'ifound-block-editor-blocks', 'ifound' );

	$editor_css = 'build/index.css';
	wp_register_style(
		'ifound-block-editor-blocks-editor',
		plugins_url( $editor_css, __FILE__ ),
		array(),
		filemtime( "$dir/$editor_css" )
	);

	$style_css = 'build/style-index.css';
	wp_register_style(
		'ifound-block-editor-blocks',
		plugins_url( $style_css, __FILE__ ),
		array(),
		filemtime( "$dir/$style_css" )
	);

	register_block_type( 'ifound/listings-search', array(
		'editor_script' => 'ifound-block-editor-blocks',
		'editor_style'  => 'ifound-block-editor-blocks-editor',
		'style'         => 'ifound-block-editor-blocks',
		'render_callback' => function($block_attributes, $content) {
			return apply_filters('ifound_listings_search', $block_attributes);
		},
	) );

	register_block_type( 'ifound/map-maker', array(
		'editor_script' => 'ifound-block-editor-blocks',
		'editor_style'  => 'ifound-block-editor-blocks-editor',
		'style'         => 'ifound-block-editor-blocks',
		'render_callback' => function($block_attributes, $content) {
			return apply_filters('ifound_polygon_maker', $block_attributes);
		},
	) );
}

if (version_compare($GLOBALS['wp_version'], '5.0-beta', '>')) {
	add_action( 'init', 'ifound_blocks_init' );
}
