<?
/**
 * iFound_save_this_search
 *
 * Save this Search Button.
 *
 * @package iFOUND
 * @since 1.0.9
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );
 
class iFound_save_this_search extends WP_Widget {
	
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Save This Search Button', 
			array(
			'description' => 'Add a Save This Search Button to the search results pages.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {
		
		echo $args['before_widget'];

		?>

		<div class="ifound-save-this-search-button">

			<div class="widgets-wrap">

				<? do_action( 'ifound_save_this_button', 'search-update' ); ?>

			</div>

		</div>

		<?

		echo $args['after_widget'];

	}
	
}
