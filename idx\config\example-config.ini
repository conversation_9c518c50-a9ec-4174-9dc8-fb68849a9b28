[database]

protocol = mysql
username = root
password =
host = 127.0.0.1
dbname = rets_db

[notify]

member_status =
action_log =

[history_tracker]
data_root_dir=
# Optional files user owner.
chown=
# Optional files group owner.
chgrp=

; RETS Connection information - each MLS should have a section, and they key should be listed in 'rets.sections'

[armls]
login_url = http://rets.profoundidx.com/rets2_0/Login
username = az.rets.profound
password = spret-zoon52

rets_version = 1.7.2
offset_support = true

prop_table = Property
db_table = property
listing_id = LIST_1
status_field = LIST_15
active_status = OV61GOJ13C0
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134

[sndmls]
login_url = http://stagerets01.sandicor.com/Login.asmx/Login
username = 500561
password = P?P_jAA3

rets_version = 1.5
offset_support = false

prop_table = Property
listing_id = sysid
status_field = Status_f102
prop_img_class = Photo
lastmod = LastTransDate_f141
picture_mod = LastImgTransDate_f1385
