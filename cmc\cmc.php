<?
/*
Plugin Name: Current Market Comparison
Plugin URI:  https://ifoundagent.com
Description: Display a Current Market Comparison report.
Version:     1.1.1
Author:      reblogdog
Author URI:  https://ifoundagent.com
License:     GPL2
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ifound
*/

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

if ( ! defined( 'CMC_PLUGIN_VERSION' ) )
    define( 'CMC_PLUGIN_VERSION', '1.1.1' );

/**
 * Require if admin.
 */
if ( is_admin() ) {

	require_once( plugin_dir_path( __FILE__ ) . 'admin/admin.php' );

}

/**
 * CMC Class
 *
 * @since 1.0.0
 */

class CMC {

	protected $google_key = 'AIzaSyAiLbh0tkYmcx-d3lZ-NnoCSQcmWd5TTzY';
	private $address;
	private $lat;
	private $lng;
	private $ptype;
	private $beds;
	private $baths;
	private $sqft;
	private $built;
	private $stories;
	private $map_data;
	private	$closed;
	private $active;

	/**
	 * init CMC class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct() {

		/**
		 * Acivate plugin defaults
		 *
		 * @since 1.0.0
		 */

		register_activation_hook( __FILE__, array( $this, 'activation' ) );

		/**
		 * Check current version.
		 *
		 * @since 1.0.0
		 */

		add_action( 'init', array( $this, 'check_version' ) );

		/**
		 * CMC Process.
		 *
		 * @since 1.0.0
		 */

		add_action( 'init', array( $this, 'cmc_process' ) );

		 /**
		 * CMC Form.
		 *
		 * @since 1.0.0
		 */

		add_shortcode( 'cmc', array( $this, 'cmc_form' ) );

		 /**
		 * CMC Report.
		 *
		 * @since 1.0.0
		 */

		add_shortcode( 'cmc_report', array( $this, 'cmc_report' ) );

		 /**
		 * CMC Scripts.
		 *
		 * @since 1.0.0
		 */

		add_action( 'wp_enqueue_scripts', array( $this, 'cmc_scripts' ) );

		 /**
		 * CMC Pop.
		 *
		 * @since 1.0.0
		 */

		add_action( 'wp_footer', array( $this, 'cmc_pop' ) );


		 /**
		 * Require CMC Widget.
		 *
		 * @since 1.0.0
		 */

		require_once( plugin_dir_path( __FILE__ ) . 'widgets/cmc.php');

		 /**
		 * CMC Widget.
		 *
		 * @since 1.0.0
		 */

		add_action( 'widgets_init', array( $this, 'init_widgets' ) );

		/**
		 * Gform Confirmaation
		 *
		 * Remove the G Form confirmation for the incomplete submission form.
		 *
		 * @since 1.0.0
		 * @see https://www.gravityhelp.com/documentation/article/gform_confirmation_anchor/
		 */

		add_filter( 'gform_confirmation_anchor_' . get_option( 'cmc_address_capture_form_id' ), '__return_false' );

	}

	/**
	 * CMC Scripts
	 *
	 * @since 1.0.0
	 */

	public function cmc_scripts(){

		if( ! get_option( 'cmc_activate' ) ) return;

		 /**
		 * Enqueue CMC Styles.
		 *
		 * @since 1.0.0
		 */

		wp_enqueue_style( 'cmc_styles', plugins_url( 'css/cmc.css', __FILE__ ), array(), CMC_PLUGIN_VERSION );

		/**
		 * Enqueue jQuery.
		 *
		 * @since 1.0.0
		 */

		wp_enqueue_script( 'jquery' );

		/**
		 * Font Awesome
		 *
		 * @since 1.0.0
		 */

		wp_enqueue_script( 'font_awesome', '//use.fontawesome.com/d3de6c724e.js' );

		/**
		 * Register CMC Form/Pop Script.
		 *
		 * @since 1.0.0
		 */

		wp_register_script( 'cmc_js', plugins_url( 'js/cmc.js', __FILE__ ), array( 'jquery' ), CMC_PLUGIN_VERSION );

		/**
		 * Localize CMC Script Data.
		 *
		 * @since 1.0.0
		 */

		wp_localize_script( 'cmc_js', 'cmc_data', array(
			'key'		=> $this->google_key,
			'url'		=> iFoundMap::$GOOGLE_MAPS_URL . '&key=',
			'places'	=> '&libraries=places',
			'settings'	=> get_option( 'cmc_admin_map_settings' ),
			'icon'		=> plugins_url( '/images/form-marker.png', __FILE__ ),
			'form_id'	=> get_option( 'cmc_form_id' ),
			'form2_id'	=> get_option( 'cmc_address_capture_form_id' )
		));

		/** Check to see if CMC Report Page */
		if( is_page( get_option( 'cmc_page_id' ) ) ) {

			/**
		     * Enqueue jQuery UI Core.
		     *
		     * @since 1.0.0
		     */

			wp_enqueue_script( 'jquery-ui-core' );


			/**
		     * Enqueue CMC Map Script.
		     *
		     * @since 1.0.0
		     */

			wp_enqueue_script(
				'cmc_map_js',
				plugins_url( 'js/cmc_map.js', __FILE__ ),
				array( 'jquery', 'jquery-ui-core' ),
				CMC_PLUGIN_VERSION
			);

			/**
		 	 * Localize CMC Script Map Data.
		 	 *
		     * @since 1.0.0
		     */

			wp_localize_script( 'cmc_map_js', 'cmc_map', array(
				'image_url'		=> plugins_url( '/images/', __FILE__ ),
				'url'			=> iFoundMap::$GOOGLE_MAPS_URL . '&key=',
				'key'			=> $this->google_key,
				'map_data'		=> $this->map_data
			));

		}

	}

	/**
	 * Init Widgets
	 *
	 * @since 1.0.0
	 *
	 * @see Class CMC_widget
	 */

	public function init_widgets(){

		if( ! get_option( 'cmc_activate' ) ) return;

		register_widget( 'CMC_widget' );

	}

	/**
	 * CMC Process
	 *
	 * Process GET and build API query string
	 *
	 * @since 1.0.0
	 */

	public function cmc_process() {

		if( ! get_option( 'cmc_activate' ) ) return;

		/** If Lat or Lng are not set end script */
		if( ! isset( $_GET['Latitude'] ) || ! isset( $_GET['Longitude'] ) ) return;

		/** Clean up GET query string values */
		$this->address	= sanitize_text_field( $_GET['Address'] );
		$this->lat 		= floatval( $_GET['Latitude'] );
		$this->lng 		= floatval( $_GET['Longitude'] );
		$this->ptype 	= sanitize_text_field( $_GET['PropType'] );
		$this->beds 	= intval( $_GET['Beds'] );
		$this->baths 	= intval( $_GET['Bathrooms'] );
		$this->sqft 	= sanitize_text_field( $_GET['SquareFeet'] );
		$this->built 	= intval( $_GET['YearBuilt'] );
		$this->stories 	= intval( $_GET['IntStories'] );


		if( $this->beds ) {

			$words .= 'bedrooms%3E' . ( $this->beds - 1 );
			$words .= '%3Bbedrooms%3C' . ( $this->beds + 1 );

		}

		if( $this->baths ) {

			$words .= '%3Bbathrooms%3E' . ( $this->baths - 1 );
			$words .= '%3Bbathrooms%3C' . ( $this->baths + 1 );

		}

		if( $this->sqft ) {

			list( $sqft_low, $sqft_high ) = explode( '-', $this->sqft );

			$words .= '%3Bliving_sqft%3E' . intval( $sqft_low * .85 );
			$words .= '%3Bliving_sqft%3C' . intval( $sqft_high * 1.15 );

		}

		if( $this->stories ) {

			$words .= '%3Bint_stories=' . $this->stories;

		}


		/** ACTIVE */
		$this->active = $this->request( '&words=' . ltrim( $words, '%3B' ) );

		/** @since 1.0.6 Relaced with API price stats */
		//$this->active->stats = $this->price_stats( $this->active->listings );

		/** Closed  */
		$words .= '%3Bclose_date%3E=3%20months%20ago';
		$status = '%3Blist_status%20IN%20Closed';
		$sort = '&sort=CloseDate,DESC';
		$this->closed = $this->request( '&words=' . $words .  $status . $sort );

		/** @since 1.0.6 Relaced with API price stats */
		//$this->closed->stats = $this->price_stats( $this->closed->listings );

		/** Subject Map Data */
		$subject_data = array(
			0 => array(
				$this->address,
				$this->lat,
				$this->lng,
				'subject',
				'subject-data'
			)
		);

		/** Active Map Data */
		$active_data = (array) $this->map_data( $this->active->listings, 'active' );

		/** Closed Map Data */
		$closed_data = (array) $this->map_data( $this->closed->listings, 'closed' );

		/** Create the Map Data Object */
		$this->map_data = array_merge_recursive( $subject_data, $active_data, $closed_data );

	}

	/**
	 * Request
	 *
	 * Make API request and return response.
	 *
	 * @since 1.0.0
	 *
	 * @return arrray $response The listing data object as an array.
	 */

	public function request( $params ) {

		/** Api Endpoint */
		$endpoint .= 'https://api.profoundidx.com/q/search?';

		/**
		 * Price Stats
		 *
		 * @since 1.0.0 Request price stats for API
		 * @since 1.0.5 Replaced with CMC::price_stats()
		 * @since 1.0.6 Request price stats for API added back in.
		 */
		$endpoint .= 'stats=price';

		if( $this->lat && $this->lng ) {

			$endpoint .= '&latlon='  . $this->lat . ',' . $this->lng;
			/** Search 2 miles from subject property */
			$endpoint .= '&nearby_radius=2';

		}

		if( $this->ptype ) {

			$endpoint .= '&proptype=' . urlencode( $this->ptype );

		}

		/** @see CMC::cmc_process */
		$endpoint .= $params;

		/** Set Max Results */
		$endpoint .= '&max_results=25';

		/** Request all listing data  */
		$endpoint .= '&all=1';

		/**
		 * Set the API Secret
		 *
		 * @since 1.1.0 urlencode was added to api key to allow for + found in keys.
		 */
		$endpoint .= '&apikey='. urlencode( $this->api_secret() );

		$response = @file_get_contents( $endpoint );

		/** Decode json and covert to associative array */
		return json_decode( $response );

	}

	/**
	 * CMC Report
	 *
	 * Built the CMC report display.
	 *
	 * @since 1.0.0
	 *
	 * @return object $cmc_report The display for the CMC report.
	 */

	public function cmc_report() {

		if( ! get_option( 'cmc_activate' ) ) return;

		/** If Lat or Lng are not set return error */
		if( ! $this->lat || ! $this->lng ) return $this->error();

		ob_start();
		?>

		<div class="cmc-report" id="cmc">

			<div class="wrap">

				<?
				$this->legend();
				$this->subject();
				$this->body( $this->active, 'Active' );
				$this->body( $this->closed, 'Closed' );

				?>

			</div>

		</div>

		<?

		return ob_get_clean();

	}

	/**
	 * CMC Form
	 *
	 * Dispaly the CMC Form.
	 *
	 * @since 1.0.0
	 *
	 * @return object $cmc_form The form for the CMC input.
	 */

	public static function cmc_form() {

		if( ! get_option( 'cmc_activate' ) ) return;

		/**
		 * Enqueue CMC Script.
		 *
		 * @since 1.0.0
		 */

		wp_enqueue_script( 'cmc_js' );

		ob_start();

		?>

		<div id="cmc">

			<div class="cmc" id="cmc-auto-input">

				<div class="wrap">

					<input id="cmc-autocomplete" placeholder="Type Your Address Here"/>


					<div class="button-wrap">
						<button id="cmc-button" class="button">View</button>
					</div>

					<? /** @see https://www.gravityhelp.com/documentation/article/embedding-a-form/ */ ?>
					<? gravity_form( get_option( 'cmc_address_capture_form_id' ), false, false, false, null, true, 99 ); ?>

					<div class="clear"></div>

				</div>

			</div>

		</div>

		<?
		return ob_get_clean();

	}

	/**
	 * CMC Pop
	 *
	 * Display the CMC popup with Gravity Form.
	 *
	 * @since 1.0.0
	 */

	public function cmc_pop() {

		if( ! get_option( 'cmc_activate' ) ) return;

		/**
		 * If CMC Script is enqueued then print CMC Pop to the footer
		 *
		 * @see CMC::cmc_form()
		 */

		if ( wp_script_is( 'cmc_js', 'enqueued' ) ) {

			?>

			<div id="cmc">

				<div id="pop-backdrop" class="pop-backdrop"></div>

				<div id="pop-close" class="pop-close" title="Close">
					<i class="fa fa-window-close fa-2x" aria-hidden="true"></i>
				</div>

				<div id="CMC_form" class="pop-form">

					<div class="wrap">

						<h3 id="form_address" class="center"></h3>

						<div class="one-third first">

							<div class="pop-info">
								<? echo stripslashes( get_option( 'cmc_pop_up_text' ) ); ?>
							</div>

							<div class="cmc-map-container">

								<div class="wrap">

									<div id="pop-map" class="pop-map"></div>

								</div>

							</div>

						</div>

						<div class="two-thirds">

							<?

							/**
							 * Print the CMC Gravity Form to the CMC pop. Allow GForm Ajax is true.
							 *
							 * @link https://www.gravityhelp.com/documentation/article/embedding-a-form/
							 */

							if( $form_id = get_option( 'cmc_form_id' ) ) {

								gravity_form( $form_id, false, false, false, null, true, 99 );

							}

							?>

						</div>

						<div class="clear"></div>

					</div>

				</div>

			</div>

			<?

		}

	}

	/**
	 * Ledgend
	 *
	 * Diplay the CMC ledgend.
	 *
	 * @since 1.0.0
	 */

	public function legend() {

		?>

		<div id="cmc-map" class="cool-wrap"></div>

		<div class="cmc-type legend center">

			<div class="wrap">

				<div class="three-fourths first">

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->subject_map_icon() ?>" alt="Subject" width="20" title="The subject property for this report." class="cmc_tip"/>

						<div class="legend-heading">Subject</div>

					</div>

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->active_map_icon() ?>" alt="Active" width="20" title="Properties that are current active listings." class="cmc_tip"/>

						<div class="legend-heading">Active</div>

					</div>

					<div class="cmc-third pad-top-10">

						<img src="<?= iFoundMap::new_hookless()->closed_map_icon() ?>" alt="closed" width="20" title="Properties that have sold in the last 90 days." class="cmc_tip"/>

						<div class="legend-heading">Closed</div>

					</div>

				</div>

				<div class="one-fourth">

					<div class="distance green-line">Best<span class="right">.5 Mile</span></div>

					<div class="distance yellow-line">Better<span class="right">1 Mile</span></div>

					<div class="distance orange-line">Good<span class="right">1.5 Miles</span></div>

					<div class="distance red-line">Fair<span class="right">2 Miles</span></div>

				</div>

			</div>

		</div>

		<?
	}

	/**
	 * Subject
	 *
	 * Diplay the CMC subject property data.
	 *
	 * @since 1.0.0
	 */

	public function subject() {

		?>
		<div class="subject">

			<div class="wrap">

				<h2>Subject Property</h2>
				<h3><? if( $this->address ) echo $this->address; ?></h3>

				<div class="one-half first">

					<div class="cmc-heading ptype">
						Property Type:
					</div>

					<div class="cmc-value ptype">
						<? if( $this->ptype ) echo $this->ptype; ?>
					</div>

					<div class="cmc-heading">
						Bedrooms:
					</div>

					<div class="cmc-value">
						<? if( $this->beds ) echo $this->beds; ?>
					</div>

					<div class="cmc-heading">
						Bathrooms:
					</div>

					<div class="cmc-value">
						<? if( $this->baths ) echo $this->baths; ?>
					</div>


				</div>

				<div class="one-half">

					<div class="cmc-heading">
						Interior SqFt:
					</div>

					<div class="cmc-value">
						<? if( $this->sqft ) echo $this->sqft; ?>
					</div>

					<div class="cmc-heading">
						Interior Stories:
					</div>

					<div class="cmc-value">
						<? if( $this->stories ) echo $this->stories; ?>
					</div>

					<div class="cmc-heading">
						Year Built:
					</div>

					<div class="cmc-value">
						<? if( $this->built ) echo $this->built; ?>
					</div>

				</div>

			</div>

		</div>
		<?
	}

	/**
	 * Body
	 *
	 * Diplay the CMC active/closed listings data.
	 *
	 * @since 1.0.0
	 */

	public function body( $listings, $title ) {

		$stats = $listings->stats->ListPrice;

		if( empty( $listings->listings[0] ) ) {

			?>

			<div class="listing">

				<div class="wrap">
					There are no comparable <? echo strtolower( $title ); ?> properties available at this time.
				</div>

			</div>

			<?

			return;
		}
		?>

		<div class="listing">

			<div class="wrap">

				<h2><? echo $stats->count; ?> <? echo $title; ?> Properties</h2>

				<div class="compare-wrap">

					<div class="one-third first">
						Lowest Price: $<? echo number_format( $stats->min ); ?>
					</div>

					<div class="one-third">
						Average Price: $<? echo number_format( round( $stats->mean ) ); ?>
					</div>

					<div class="one-third">
						Highest Price: $<? echo number_format( $stats->max ); ?>
					</div>

					<div class="clear"></div>

				</div>

				<div class="listing-wrap cmc-headings">

					<div class="one-half first">
						Address
					</div>

					<div class="one-sixth center">
						Distance/Miles
					</div>

					<div class="one-sixth center">
						<? echo $title; ?> Price
					</div>

					<div class="one-sixth center">
						Details
					</div>

					<div class="clear"></div>

				</div>

				<?

				foreach( $listings->listings as $listing ) {

					/** Check to see if MLS has an image. */
					if( ! empty( $listing->img_url ) ) {

						/**
						 * This is meant to convert images to SSL.
						 *
						 * @since 1.0.2
						 * @see TB Case(1912)
						 */
						$image = str_replace( 'http://', 'https://', $listing->img_url );

						/** For ARMLS, TARMLS, PAAR and others */
						$image = str_replace( 'flexmls', 'sparkplatform', $image );

					} else {

						/**
						 * If no MLS image is available display a NO Image image.
						 *
						 * @since 1.0.3
						 */
						$image = plugins_url( 'images/no-image.png', __FILE__ );

					}


					$distance = $this->distance( $listing->Latitude, $listing->Longitude );

					?>

					<div id="<? echo $listing->ListingID; ?>" class="listing-wrap">

						<div class="one-half first">

							<a href="<? echo site_url( '/' . $listing->uri ); ?>" target="_blank">
								<? echo esc_html( $this->do_address( $listing ) ); ?>
							</a>

						</div>

						<div class="one-sixth cmc-data">
							<span class="miles-total"><? echo $distance; ?> </span>
							<span class="miles"> Miles</span>
						</div>

						<div class="one-sixth cmc-data">

							<?

							if( ! empty( $listing->ClosePrice ) && $listing->ClosePrice != '$0') echo $listing->ClosePrice;
							else echo $listing->ListPrice;

							?>

						</div>

						<div class="one-sixth cmc-data open cmc-more">
							<a>More</a>
						</div>

						<span class="open-box hide">

							<div class="one-third first">
								<img src='<? echo $image; ?>'/>
							</div>


							<div class="one-third">

								<div class="cmc-listing-details">

									<div class="cmc-detail beds">
										Beds: <? echo $listing->Beds; ?>
									</div>

									<div class="cmc-detail baths">
										Baths: <? echo $listing->Bathrooms; ?>
									</div>

									<div class="cmc-detail stories">
										Stories: <? echo $listing->IntStories; ?>
									</div>

									<div class="cmc-detail sqft">
										SqFt: <? echo $listing->SquareFeet; ?>
									</div>

									<div class="cmc-detail price-per">
										Price/SqFt: $<? echo $listing->PriceSqFt; ?>
									</div>

								</div>

							</div>

							<div class="one-third">

								<?

								if( $listing->CloseDate ) {

									?>

									<div class="close-date">
										Closed Date: <? echo date( 'm d, Y', strtotime( $listing->CloseDate ) ); ?>
									</div>

									<?
								}

								?>

								<div class="cmc-detail-button">

									<a href="<? echo site_url( '/' . $listing->uri ); ?>" class="button" target="_blank">
										Full Details
									</a>

								</div>

							</div>

						</span>

						<div class="cmc-data open cmc-mobile-more">
							<a>More</a>
						</div>

						<div class="clear"></div>

					</div>
					<?

				}

				?>

				</div>

			</div>
			<?

	}

	/**
	 * Map Data
	 *
	 * Create the json map data oblect
	 *
	 * @since 1.0.0
	 *
	 * @return object $map_data The map data used to create the CMC map.
	 */

	public function map_data( $listings, $image ) {

		if( ! get_option( 'cmc_activate' ) ) return;

		if( empty ( $listings ) ) return array();

		foreach( $listings as $listing ) {

			$map_data[] = array(
				$this->do_address( $listing ),
				$listing->Latitude,
				$listing->Longitude,
				$image,
				$listing->ListingID
			);

		}

		return $map_data;
	}

	/**
	 * Price Stats
	 *
	 * Calcuate the High, Low, Mean prices of the listings.
	 *
	 * @since 1.0.5
	 * @since 1.0.6 Depricated - Relaced with API price stats.
	 *
	 * @return object $price_stats The High, Low, Mean prices and the Count.
	 */

	public function price_stats( $listings ) {

		foreach( $listings as $listing ) {

			if( ! empty( $listing->ClosePrice ) && $listing->ClosePrice != '$0') $price = $listing->ClosePrice;
			else $price = $listing->ListPrice;

			$replace = array( '$', ',' );

			$prices[] = intval( str_replace( $replace, '', $price ) );

		}

		$stats = array(
			'count'	=> count ( $prices ),
			'low'	=> min ( $prices ),
			'mean'	=> array_sum( $prices ) / count ( $prices ),
			'high'	=> max ( $prices )
		);

		return (object) $stats;

	}

	/**
	 * Do Address
	 *
	 * @since 1.0.4
	 *
	 * #params array  $listing The listing data for the property.
	 * @return string $address A Full formatted address.
	 */

	public function do_address( $listing ) {

		if( ! empty( $listing->StreetNumber ) ) 	$address .= trim( $listing->StreetNumber ). ' ';
		if( ! empty( $listing->StreetDirPrefix ) ) 	$address .= trim( $listing->StreetDirPrefix ) . ' ';
		if( ! empty( $listing->StreetName ) ) 		$address .= trim( $listing->StreetName ) . ' ';
		if( ! empty( $listing->StreetSuffix ) ) 	$address .= trim( $listing->StreetSuffix ) . ' ';
		if( ! empty( $listing->UnitNumber ) ) 		$address .= ' #' . trim( $listing->UnitNumber ) . ', ';
		if( ! empty( $listing->City ) ) 			$address .= trim( $listing->City ) . ', ';
		if( ! empty( $listing->State ) ) 			$address .= trim( $listing->State ) . ' ';
		if( ! empty( $listing->PostalCode ) ) 		$address .= trim( $listing->PostalCode ) . ' ';


		return trim( str_replace( '-', '', $address ) );

	}

	/**
	 * MLS
	 *
	 * @since 1.0.0
	 *
	 * @return string $mls The name of the MLS.
	 */

	public function mls() {

		$settings = get_option( 'ProFoundMLSAdminOptions' );

		$account = json_decode( $settings['account'] );

		return $account->mls;

	}

	/**
	 * API Secret
	 *
	 * @since 1.0.0
	 *
	 * @return string $api_secret The API secret key.
	 */

	public function api_secret() {

		$settings = get_option( 'ProFoundMLSAdminOptions' );

		return $settings['api_secret'];

	}

	/**
	 * Proptypes
	 *
	 * @since 1.0.0
	 *
	 * @return string $ptypes The default proptypes for this site.
	 */

	public function ptypes() {

		$settings =  get_option( 'ProFoundMLSAdminOptions' );

		$form_data = json_decode( $settings['form_data'] );

		return $form_data->proptype;

	}

	/**
	 * Get Distance
	 *
	 * Get the distance of listing from subject property.
	 *
	 * @since 1.0.0
	 *
	 * @params array $listings Listings for the CMC active/closed properties.
	 * @return array $listings Listings for the CMC active/closed properties with distance.
	 */

	public function get_distance( $listings ) {

		$new_listings = array();

		foreach( $listings as $key => $value ) {

			$listings[$key]['distance'] = $this->distance( $value['Latitude'], $value['Longitude'] );

		}

		return $listings;

	}

	/**
	 * Distance
	 *
	 * Calculates the great-circle distance between two points, with
	 * the Vincenty formula.
	 *
	 * @param float $latitudeTo Latitude of target point in [deg decimal]
	 * @param float $longitudeTo Longitude of target point in [deg decimal]
	 * @return float Distance between points in [m] (same as earthRadius)
	 *
	 * @link http://stackoverflow.com/questions/10053358/measuring-the-distance-between-two-coordinates-in-php
	 */

	public function distance( $latitudeTo, $longitudeTo ) {

	  	// convert from degrees to radians
	 	$latFrom	= deg2rad( $this->lat );
	  	$lonFrom 	= deg2rad( $this->lng );
	  	$latTo 		= deg2rad( $latitudeTo );
	  	$lonTo 		= deg2rad( $longitudeTo );

	  	$lonDelta = $lonTo - $lonFrom;
	  	$a = pow( cos( $latTo ) * sin( $lonDelta ), 2 ) + pow( cos( $latFrom ) * sin( $latTo ) - sin( $latFrom ) * cos( $latTo ) * cos( $lonDelta ), 2 );
	  	$b = sin( $latFrom ) * sin( $latTo ) + cos( $latFrom ) * cos( $latTo ) * cos( $lonDelta );

	  	$angle = atan2( sqrt( $a ), $b );
	  	return round( $angle * 3959, 2 ); // 3959 Earths Radius returns Miles
	}

	/**
	 * Error
	 *
	 * @since 1.0.0
	 *
	 * @return string $error The error message if Lat/Lng are not provided.
	 */

	public function error() {

		?>

		<div class="error-message">There was an error processing your request.</div>

		<?

	}

	/**
	 * Activation.
	 *
	 * Activate plugin and check for update.
	 *
	 * @since 1.0.0
	 */

	public function activation() {

		$this->cmc_update();

		/** Update current plugin version */
		update_option( 'cmc_current_version', CMC_PLUGIN_VERSION );

	}

	/**
	 * Check version.
	 *
	 * Check to see if current version is stored in database, if no, call CMC::activation.
	 *
	 * @since 1.0.0
	 */

	public function check_version() {
		if ( CMC_PLUGIN_VERSION !== get_option( 'cmc_current_version' ))
    		$this->activation();
	}

	/**
	 * CMC Update
	 *
	 * Update varies settings required by CMC.
	 *
	 * @since 1.0.0
	 */

	public function cmc_update() {

		/** If CMC page does not exsists we create it and record the page ID in the options table */
		if( ! get_option( 'cmc_page_id' ) ) {
			$this->cmc_page();
		}

		/** If CMC form does not exsists we create it and record the form ID in the options table */
		if( ! get_option( 'cmc_form_id' ) ) {
			$this->cmc_insert_form();
		}

		/** If CMC incomplete form does not exsists we create it and record the form ID in the options table */
		if( ! get_option( 'cmc_address_capture_form_id' ) ) {
			$this->cmc_insert_address_capture_form();
		}

		/** If CMC widget title does not exsists we create it. */
		if( ! get_option( 'cmc_widget_title' ) ) {
			update_option( 'cmc_widget_title', 'What&#39;s My Home Worth?' );
		}

		/**
		 * If No CMC Pop up text. Insert it here.
		 *
		 * @since 1.0.2
		 */
		if( ! get_option( 'cmc_pop_up_text' ) ) {

			$cmc_pop_up_text = 'To get a more accurate representation of what&#39;s selling and what&#39;s sold around your home, please complete the information on the form provided. Thanks.';

			update_option( 'cmc_pop_up_text', trim( $cmc_pop_up_text ) );

		}

		/**
		 * If No CMC Admin Map. Insert defaults it here.
		 *
		 * @since 1.1.0
		 */
		if( ! get_option( 'cmc_admin_map_settings' ) ) {

			/** Default settings include the lower 48 states */
			$map_settings = (object) array(
				'north'			=> 48.764578762482,
				'south'			=> 23.644727798361,
				'east'			=> -65.99809963559,
				'west'			=> -128.97579983091,
				'center_lat'	=> 37.989086530067,
				'center_lng'	=> -93.91133938168,
				'zoom'			=> 3
			);

			update_option( 'cmc_admin_map_settings', $map_settings );

		}

	}

	/**
	 * CMC Page
	 *
	 * Create the CMC report page.
	 *
	 * @since 1.0.0
	 */

	public function cmc_page() {

		$page = array(
			'post_title'    => 'Current Market Comparison',
			'post_content'  => '[cmc_report]',
			'post_type'     => 'page',
			'post_status'   => 'publish'
		);

		$page_id = wp_insert_post( $page );

		if( $page_id && is_int( $page_id ) ) {

			update_option( 'cmc_page_id', $page_id );

			update_post_meta( $page_id, '_genesis_layout', 'full-width-content' );

		}

	}

	/**
	 * CMC Insert Form
	 *
	 * Insert the CMC Gravity form.
	 *
	 * @since 1.0.0
	 */

	public function cmc_insert_form() {

		$json = file_get_contents( plugin_dir_path( __FILE__ ) . 'json/cmc-form.json' );

		/** Replace page id 9999 with 'cmc_page_id' */
		if( $page_id = get_option( 'cmc_page_id' ) ) {

			$json = str_replace( 9999, $page_id, $json );

		}

		$form_object = json_decode( $json, true );

		$form_id = GFAPI::add_form( $form_object[0] );

		if( $form_id && is_int( $form_id ) ) {

			update_option( 'cmc_form_id', $form_id );

			/** If MLS os ARMLS do not update form */
			if( $this->mls() == 'armls' ) return;

			$this->cmc_update_form_ptypes();

		}

	}

	/**
	 * CMC Insert Address Capture Form
	 *
	 * Insert the CMC Gravity form for incomplete entries.
	 *
	 * @since 1.0.1
	 */

	public function cmc_insert_address_capture_form() {

		$json = file_get_contents( plugin_dir_path( __FILE__ ) . 'json/address-capture.json' );

		$form_object = json_decode( $json, true );

		$form_id = GFAPI::add_form( $form_object[0] );

		if( $form_id && is_int( $form_id ) ) {

			update_option( 'cmc_address_capture_form_id', $form_id );

		}

	}

	/**
	 * CMC Update Form PropTypes
	 *
	 * Update property types for MLS in CMC Gravity Form.
	 * Default Property types are for ARMLS.
	 *
	 * @since 1.0.0
	 */

	public function cmc_update_form_ptypes() {


		if( $form_id = get_option( 'cmc_form_id' ) ) {

			$form = GFAPI::get_form( $form_id );

			$ptypes = $this->ptypes();

			foreach( $ptypes as $ptype ) {

				$ptypes_array[] = array(
					'text' 			=> $ptype->Value,
					'value'			=> $ptype->Value,
					'isSelected'	=> false,
					'price'			=> ''
				);
			}

			$select_one[] = array(
				'text' 			=> 'Select One',
				'value'			=> '',
				'isSelected'		=> true,
				'price'			=> ""
			);

			$form['fields'][3]['choices'] = array_merge_recursive( $select_one, $ptypes_array);

			GFAPI::update_form( $form, $form_id );

		}


	}

}

/**
 * @see CMC::init()
 *
 */

add_action( 'plugins_loaded', array( 'CMC', 'init' ) );
?>
