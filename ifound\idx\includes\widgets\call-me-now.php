<?
/**
 * iFound_call_me_now class
 *
 * Display call_me_now in widget areas.
 *
 * @package iFOUND
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


class iFound_call_me_now extends WP_Widget {
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Mobile - Call Me Now', 
			array(
			'description' => 'Add Mobile Call Me Now to any widget area. This will only display on mobile devices.'
		));
		
	}
	
	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	
	public function widget( $args, $instance ) {
		
		if ( wp_is_mobile() ) {
			
			$ifound_agent = get_option( 'ifound_agent' );

			if( empty( $ifound_agent['call_me_phone'] ) ) return;

			$button_text = empty( $ifound_agent['call_me_button'] ) ? 'Call Me' : $ifound_agent['call_me_button'];

			echo $args['before_widget']; ?>

			<div class="ifound-call-me-now">

				<div class="widgets-wrap">

					<div class="call-me-now">

						<a class="phone-me-now-button tel button" href="tel:<? echo $ifound_agent['call_me_phone']; ?>">

							<i class="far fa-phone call-me-now-icon"></i><? echo _e( $button_text, 'ifound' ); ?>
								
						</a>

					</div>

				</div>

			</div><?

			echo $args['after_widget'];
			
		}
		
	}
	
}
