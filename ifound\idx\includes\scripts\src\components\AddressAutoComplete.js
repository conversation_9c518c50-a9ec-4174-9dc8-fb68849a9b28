import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

function AddressAutoComplete(props) {
	const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);
	const addressInput = useRef(null);

	function listenForPlaceChanged() {
		const place = this.getPlace();
		const addr = addressInput.current.value;
		props.onChange(addr);
		props.onAutoComplete(place, addr);
	}

	function listenForGoogleMapsLoad() {
		setIsGoogleMapsLoaded(true);
	}

	useEffect(() => {
		if (props.shouldFocus) {
			addressInput.current.focus();
		}
	}, [props.shouldFocus]);

	useEffect(() => {
		let isComponentMounted = true;

		if (!isGoogleMapsLoaded) {
			props.googleMapsLoadedPromise.then(() => {
				if (isComponentMounted) {
					setIsGoogleMapsLoaded(true);
				}
			});
		}
		return () => {
			isComponentMounted = false;
		}
	}, [isGoogleMapsLoaded]);

	useEffect(() => {
		if (isGoogleMapsLoaded) {
			const options = {
				componentRestrictions: { country: 'us' },
			};
			const autoComplete = new window.google.maps.places.Autocomplete(addressInput.current, options);
			// Only return the geometry fields. This affects billing.
			autoComplete.setFields(['geometry']);
			autoComplete.addListener('place_changed', listenForPlaceChanged);

			return () => {
				autoComplete.unbind('place_changed', listenForPlaceChanged);
			}
		}
	}, [isGoogleMapsLoaded]);

	return <div>
		<input type="text" value={props.address} ref={addressInput} onChange={event => props.onChange(event.target.value)} className="address google-maps-autocomplete" />
	</div>;
}

AddressAutoComplete.propTypes = {
	address: PropTypes.string,
	onChange: PropTypes.func,
	onAutoComplete: PropTypes.func,
	googleMapsLoadedPromise: PropTypes.object,
}

export default AddressAutoComplete;
