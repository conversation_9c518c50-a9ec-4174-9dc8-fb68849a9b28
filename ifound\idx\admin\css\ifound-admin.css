@charset "UTF-8";
/*
 CSS Document

 This is for Global Admin CSS only.
*/

/* Property Video */
.property-video-instructions{
	border: thin solid #ccc;
	padding: 5px;
	background: #fff;
	max-width: 800px;
}

/* Help Buttons */
.help-button-wrapper{
	float: left;
}

.help-button-wrapper{
	margin: .67em 20px;
}

/* Drop Campaigns */
.drip-template-input{
	border-bottom: thin solid #ccc;
}

.trash-drip-template-input{
	color:red;
	cursor: pointer;
}

.drap-drop{
	cursor:grab;
}

/* Client Profile and Saved campaign Meta Boxes. This keeps the page from jumping on status change. */
td.status.next-time{
	min-width: 190px;
}

.update-campaign.fa-toggle-on{
	color: green;
	cursor: pointer;
}

.update-campaign.fa-toggle-off{
	color: #999;
	cursor: pointer;
}

.update-campaign-hint {
	color: #999;
	font-size: small;
}

/* Notes */
.new-note .note-text,
.new-note .note-text-label{
	font-size: 20px;
	color: #3a5795;
}

.new-note li{
	border: thin solid #3a5795;
	border-radius: 7px;
	padding: 2px;
}

.new-note li:before{
	font-family: Font Awesome\ 5 Pro;
	content: "\f058";
	font-size: 20px;
	color:#25A102;
	font-weight: 300;
	padding: 0 14px;
}

#latest_activity_logs .inside,
#activity_log_meta .inside,
#views_meta .inside,
#results_views_meta .inside,
#detail_views_meta .inside,
#ifound_latest_email_activity .inside,
#ifound_latest_website_activity .inside{
	max-height: 300px;
	overflow: scroll;
}

.red-bg{
	background: #ff0000 !important;
}

.admin-contact-name{
	font-size: 16px;
	color: #000;
}

.ifound_no_results {
	color: #f00;
	font-size: 18px;
	font-style: italic;
}

.note-text-label{
	white-space: nowrap;
	line-height: 1.3;
	font-weight: 600;
}

/** Map Controls **/

.ifound-map-control-wrapper{
	width: 50px;
	height: 108px;
}

.ifound-map-control.zoom-out:before{
	content: "\f068";
	display: inline-block;
	font-family: 'Font Awesome 5 Pro';
	padding: 12px
}

.ifound-map-control.zoom-in:before{
	content: "\f067";
	display: inline-block;
	font-family: 'Font Awesome 5 Pro';
	padding: 12px
}

.ifound-map-control{
	padding:12px;
	color:#fff;
	margin: 1px auto;
}

/* Use the close icon to capture closeClick
   event for a InfoWindow instance */
#ifound_polydraw_map .gm-ui-hover-effect {
  width: 100% !important;
  height: 100% !important;
  opacity: 0;
}

#ifound_polydraw_map .gm-style .gm-style-iw-c {
  padding: 0;
}

#ifound_polydraw_map .gm-style-iw-d {
  overflow: auto !important;
}

#ifound_polydraw_map .ifa-label-name {
  background: #e42819;
  color: #fff;
  text-decoration: none;
  padding-left: 10px;
  padding-right: 10px;
}

/** Wordpress admin menus **/
li.toplevel_page_ifound_agent .wp-menu-name,
li.toplevel_page_ifound_agent .wp-submenu a,
li.toplevel_page_contacts .wp-menu-name,
li.toplevel_page_contacts .wp-submenu a,
li.toplevel_page_campaign-builder .wp-menu-name,
li.toplevel_page_campaign-builder .wp-submenu a{
    font-family: 'Raleway', sans-serif;

}
#adminmenu li.menu-top.toplevel_page_ifound_agent,
#adminmenu li.menu-top.menu-icon-contacts,
#adminmenu li.menu-top.toplevel_page_campaign-builder{
    min-height: 40px;
}

li.toplevel_page_ifound_agent .wp-menu-image:before,
li.menu-icon-contacts .wp-menu-image:before,
li.menu-icon-private_contact .wp-menu-image:before,
li.toplevel_page_campaign-builder .wp-menu-image:before{
	content:" ";
}
li.toplevel_page_ifound_agent .wp-menu-image,
li.menu-icon-contacts .wp-menu-image,
li.menu-icon-private_contact .wp-menu-image,
li.toplevel_page_campaign-builder .wp-menu-image{
    background-position: 50%;
    background-repeat: no-repeat;
	background-size:80%;

}
li.toplevel_page_ifound_agent .wp-menu-image{
background-image: url(/wp-content/plugins/ifound/idx/admin/images/idx-settings-icon.png);
}
li.menu-icon-contacts .wp-menu-image, li.menu-icon-private_contact .wp-menu-image{
	background-image: url(/wp-content/plugins/ifound/idx/admin/images/idx-crm-icon.png);
}
li.toplevel_page_campaign-builder .wp-menu-image{
	background-image: url(/wp-content/plugins/ifound/idx/admin/images/idx-campaign-icon.png);
}

.ifound-wrap{
  	margin:0 auto;
    clear: both;
}

.ifound-wrap::after {
    content: "";
    clear: both;
    display: table;
}

#wpbody-content {
	width: 98%;
}

.required-red,
.budget-body .required-red {
	border-color: red !important;
	background-color: #FADBDC !important;
}

/* Sortable Criteria */
.save-criteria-wrapper {
	margin-top: 20px;
}

.choices-wrapper{
	min-height: 600px;
	max-height: 740px;
	overflow: scroll;
	border: thin solid #777;
}

.decisions-wrapper{
	padding: 5px;
	height: 150px;
}

#criteria-choices,
#criteria-main,
#criteria-additional {
	border: 1px solid #eee;
	min-height: 20px;
	list-style-type: none;
	margin: 0;
	padding: 10px 0;
	float: left;
	margin-right: 10px;
	cursor: move;
}

#criteria-choices li,
#criteria-main li,
#criteria-additional li {
	margin: 0 5px 5px 5px;
	padding: 5px 0;
	font-size: 12px;
	width: 170px;
	text-align: center;
}

#criteria-main li,
#criteria-additional li {
	float: left;
}

#criteria-choices,
#criteria-main,
#criteria-additional {
	border: 1px dashed #999;
	list-style-type: none;
	margin: 0;
	padding: 10px;
}

#criteria-main,
#criteria-additional {
	width: 970px;
}

#criteria-choices,
#quick-search-criteria #criteria-main{
	width: 190px;
}

#quick-search-criteria #criteria-choices,
#quick-search-criteria #criteria-main{
	height: 400px;
}

.criteria-options,
form#ifound-sortable-criteria-form,
form#ifound-more-sortable-criteria-form {
	float: left;
}

.choices-wrapper li{
	background: #ba711b;
	color: #fff;
}

.decisions-wrapper #criteria-main li{
	background: #51ba51;
	color: #fff;
}

.decisions-wrapper #criteria-additional li{
	background: #2463ba;
	color: #fff;
}

/* Contact Admin */

.contact_meta{
	text-align: left;
}

.contact_meta input{
	width: 100%;
}

.spouse, .liondesk-heading {
	background: #ddd;
	padding: 10px;
}

/* Shapes Map */

.color-button{
	width: 18px !important;
	height: 18px !important;
}

.ifound-shapes-map{
	width: 100%;
	height: 600px;
}

.color-button {
  	width: 14px;
   	height: 14px;
  	font-size: 0;
	margin: 2px;
   	float: right;
 	cursor: pointer;
}

.map-and-palette-wrapper .legend {
	margin-top: 1em;
	display: flex;
	float: right;
	clear: right;
}

.map-and-palette-wrapper .legend .item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: 1em;
}

/* Search Bar */

.dynamic-heading:after {
   content: "\f107";
   display: inline-block;
   font-family: 'Font Awesome 5 Pro';
   vertical-align: middle;
}

.ifound-dynamic-form-wrapper h3.dynamic-heading:after {
	content: " ";
}

.dynamic-button,
.dynamic-input {
	background: #fff;
	border: #ccc 1px solid;
	border-radius: 4px;
	display: inline-block;
	font-size: 14px;
    height: 26px;
    line-height: 26px;
    margin: 2px 0;
    padding: 0 10px;
    text-align: center;
    vertical-align: middle;
}

.dynamic-input {
	margin: 2px;
}

.dynamic-input-label .polygon-color {
	position: relative;
	margin: 0 10px;
	padding: 0 10px;
}

.default-criteria-heading,
.criteria-heading,
.budget-heading,
.display-stats-criteria-heading,
.display-options-criteria-heading {
	background: #ddd;
	margin: 5px 0;
	padding: 10px 0 10px 10px;
	width: 100%;
}

.search-criteria-wrapper,
.default-criteria-wrapper {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 10px;
}

.more-filters-heading:before {
	content: "\f055";
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    padding-right: 3px;
}

.results-content-main .search-bar {
	position: relative;
}

.more-filters-heading {
	cursor: pointer;
	position: absolute;
    top: 10px;
    right: 10px;
}

.criteria-heading,
.budget-heading,
i.remove-alert {
	cursor: pointer;
}

.campaign-active i.fa-toggle-on.remove-alert {
	color: #008000;
}

i.fa-toggle-off.remove-alert {
	color: #f00;
}

.ifound-dynamic-form,
#ifound-backup-form {
	border: #ddd 1px solid;
	border-radius: 4px;
	display: inline-block;
	overflow: scroll;
	padding: 10px;
	text-align: left;
	width: 100%;
}

.dynamic-button:hover{
	background: #f5f6f7;
}

.dynamic-input-button:hover{
	background: #e6e6e6;
}

.dynamic-input-remove {
	color: #ccc;
	padding: 2px;
	cursor: pointer;
}

.criteria-body .dynamic-input-button,
.search-criteria-body .dynamic-input-button,
.more-filters-wrapper .dynamic-input-button,
.criteria-body .dynamic-button,
.search-criteria-body .dynamic-button,
.more-filters-wrapper .dynamic-button{
	cursor: pointer;
}

.search-criteria-body input,
.more-filters-wrapper input,
.additional-criteria input {
	display: inline-block;
	width: 48%;
}

.budget-add-button-wrapper {
	margin-top: 10px;
}

.more-filters-criteria {
	display: none;
	z-index: 999;
	width: 100%;
	background: #fff;
	border: thin solid #ccc;
	border-radius: 4px;
	padding: 10px;
	margin-top: 10px;
}

.dynamic-input-plus,
.dynamic-input-single-plus,
.dynamic-input-range,
.ifound-close,
.ifound-close-login,
.advanced-close,
.shortcode-close,
.more-filters-criteria i {
	cursor: pointer;
	color:#000;
}

.dynamic-button.multibox-advanced {
	border: 0;
	padding: 0;
}

.ifound-react-select-multibox-advanced-container {
	min-width: 310px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control {
	border-color: #ccc;
	min-height: initial;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control:hover {
	border-color: #ccc;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control--is-focused {
	box-shadow: none;
	border-color: #ccc;
	min-height: initial;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control--is-focused:hover {
	border-color: #ccc;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__value-container {
	padding: 0 8px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__input-container {
	margin: 0;
	padding: 0;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__input {
	min-height: initial;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__placeholder {
	text-align: left;
	white-space: nowrap;
	font-size: 14px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading {
	display: flex;
	text-align: left;
	border-bottom: 1px silver solid;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .map-icon {
	width: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .label {
	margin-left: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .legend-item {
	margin-left: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__option {
	text-align: left;
	font-size: 14px;
	padding: 4px 12px 4px 36px;
	white-space: nowrap;
	overflow-x: hidden;
	display: flex;
	align-items: center;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__option .map-icon {
	width: 10px;
	margin-right: 5px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__menu {
	/* This prevents the width from being narrowed to the width of the input control */
	width: initial;
}

.lookups-body{
	display: none;
	position: absolute;
	text-align: left;
	padding: 10px 20px;
	z-index: 999;
	background: #fff;
	width: auto;
	max-width: 320px;
	min-width: 95px;
	border-radius: 4px;
	margin: 2px 0 0 -10px;
	border: 1px #ddd solid;
}

.lookups-body .ifound-wrap{
	max-height: calc(40vh - 120px);
	overflow: auto;
}

.lookups-body .address-container {
    max-width: 500px;
}

.lookups-body .address{
    width: 100%;
}

select.mls_class_select {
    border: #ccc 1px solid;
    border-radius: 4px;
    cursor: pointer;
    height: auto;
    padding: 2px 5px;
    width: auto;
     -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.mls-class-select-wrap {
	display: inline-block;
}

.mls-class-select-wrap .ifound-wrap {
	position: relative;
}

.sort-select-wrap{
	float: right;
}

.search-criteria-placeholder {
	display: inline-block;
	border: 2px silver dashed;
	padding-left: 4px;
	padding-right: 4px;
	font-size: 14px;
	line-height: 26px;
	border-radius: 4px;
}

.dynamic-input + .search-criteria-placeholder {
	display: none;
}

.criteria-body.active,
.budget-body.active {
	display: block;
}

.results-image,
.results-data {
	float: left;
}

.results-image {
	margin-right: 10px;
}

.results-image img {
	max-width: 100px;
}

.ifound-prop-data img,
.zoom-marker-close,
.broker-name,
.ifound-prop-data .sqft,
.criteria-body,
.budget-body,
#campaign-builder .results-buttons,
.additional-criteria-body,
.create-post {
	display: none;
}

.ifound-results:nth-child(odd) {
	background-color: #f9f9f9;
}

.ifound-price-stats,
.ifound-results {
	background: #fff;
    padding: 5px 0 5px 10px;
    margin-top: 5px;
}

.ifound-price-stats {
	border: 1px solid #ddd;
	padding: 5px 0 10px 10px;
}

.ifound-results h2 {
	font-size: 1em;
	margin: 0 5px 5px 0;
}

.ifound-results h2 a {
	text-decoration: none;
}

.total h2 {
	margin: 0.25em 0 0.5em 0;
}

.wp-core-ui .advanced-button {
	background-color: #0070c9;
    background: -webkit-linear-gradient(#42a1ec, #0070c9);
    background: linear-gradient(#42a1ec, #0070c9);
    border-color: #07c;
    color: #fff;
	margin: 10px 0;
}

.wp-core-ui .advanced-button:hover {
    background-color: #147bcd;
    background: -webkit-linear-gradient(#51a9ee, #147bcd);
    background: linear-gradient(#51a9ee, #147bcd);
    border-color: #1482d0;
    color: #fff;
}

.edit-email-wrapper {
	margin-bottom: 10px;
}

/* Because disabled fields don't get submitted, we don't want to actually disable certain fields, just make them seem
   seem disabled. pointer-events does not actually disable them from keyboard interactivity, but it's probably good
   enough. The idea is to signal that the fields won't have any effect, not that they can't be edited. If you don't
   want a field to be editable, you should actually disable the field instead of using this class.
 */
.ifound-feel-disabled {
	pointer-events: none;
	opacity: 0.5;
}

.sms_template_body_wrapper {
	margin-top: 10px;
}

.contact_sms_opt_out_msg {
	font-size: 14px;
}

.total span {
	color: #f00;
}

.stats-data .heading {
	font-weight: 700;
}

.ifound-pagination {
	margin: 10px 0;
}

/* Contacts Tables */

.form-table.save-this-section {
	border-collapse: separate;
}

.form-table.save-this-section {
	border: 1px solid #ddd;
}

.form-table.save-this-section tr:nth-child(1) {
	background: #efefef;
}

.form-table.save-this-section tr:nth-child(1) th,
.form-table.save-this-section tr:nth-child(1) td {
	padding: 10px;
}

.form-table.save-this-section tr:not(:first-child) th,
.form-table.save-this-section tr:not(:first-child) td {
	padding: 4px 10px;
}

.form-table.views_meta label {
	white-space: nowrap;
}

/* Search Views*/

.form-table.views_meta th,
.form-table.views_meta td {
	padding: 4px 0;
}

/* Search Settings */

table.search-settings th,
table.search-settings td {
	padding: 5px 10px;
}

table.search-settings tr:nth-child(odd) {
	background-color: #f9f9f9;
}

table.search-settings h3 {
	background: #ddd;
    padding: 10px;
    margin-bottom: 0;
}

/* While we wait popup */

.while-we-wait {
	display: none;
}

.while-we-wait.active.pop-drop {
	display: block;
	position: fixed;
	top:0;
	bottom: 0;
	right: 0;
	left: 0;
	z-index: 99999;
	background-color: rgba(0, 0, 0, .8);
}

.while-we-wait.active.pop-msg {
	display: block;
	position: absolute;
	margin: 0 auto;
	width: 100%;
	top: 50%;
	text-align: center;
	font-size: 60px;
	color: #fff;
	z-index: 99999;
}

.intro-title {
	max-width: 500px;
}
.intro-title .warning {
	margin-top: 2px;
	background: #faa732;
	color: white;
	padding: 4px;
	border-radius: 4px;
}
.intro-title .intro-title-preview {
	background: #49afcd;
	color: white;
	padding: 4px;
	border-radius: 4px;
	word-break: break-all;
}

.intro-title .hint {
	color: #aaa;
	font-size: small;
}

/* Column Classes
--------------------------------------------- */

.five-sixths,
.four-sixths,
.one-fourth,
.one-half,
.one-sixth,
.one-third,
.three-fourths,
.three-sixths,
.two-fourths,
.two-sixths,
.two-thirds {
	float: left;
	margin-left: 2.564102564102564%;
}

.one-half,
.three-sixths,
.two-fourths {
	width: 48.717948717948715%;
}

.one-third,
.two-sixths {
	width: 31.623931623931625%;
}

.four-sixths,
.two-thirds {
	width: 65.81196581196582%;
}

.one-fourth {
	width: 23.076923076923077%;
}

.three-fourths {
	width: 74.35897435897436%;
}

.one-sixth {
	width: 14.52991452991453%;
}

.five-sixths {
	width: 82.90598290598291%;
}

.first {
	clear: both;
	margin-left: 0;
}

/* Wise Agent Integration */
#wa-sync {
	margin-top: .7rem;
}

@media all and (max-width: 800px) {

	.ifound-results h2 {
		float: none;
		width: 100%;
	}

	.while-we-wait.active.pop-msg {
		font-size: 30px;
	}
}

@media all and (max-width: 480px) {

	.while-we-wait.active.pop-msg {
		font-size: 30px;
	}

	.sort-select-wrap{
		float: none;
	}

}


@import url('https://fonts.googleapis.com/css?family=Raleway');

/* I looked through this file and have no idea where to put this. Defaulting to tacking it on the end of the file. */
.download_csv_section {
	margin: 10px 0;
}

.sticky-th {
	position: sticky;
	top: 0;
	/* We have to choose some background, otherwise it'll be transparent. */
	/* We don't necessarily need to set it here. We could let the calling place set it. But handy here for now. */
	background: lightgray;
}

td.totals {
	font-weight: bold;
}
