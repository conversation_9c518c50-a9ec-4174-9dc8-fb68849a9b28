/*
 * Hook the Sync Wise Agent Contacts Button
 * To the action inside the iFoundWiseagent class
 */

jQuery(document).ready(function($) {
	var currUrl = window.location.href.split('/').pop();

	if(currUrl !== 'edit.php?post_type=contacts' && currUrl !== 'admin.php?page=campaign-builder') {
		$('#wa-sync').remove();
		return;
	} else if(currUrl.includes('admin.php?page=campaign-builder')) {
		var allButtons = $('.wa-sync-button');
		if(allButtons.length > 1) $(allButtons).first().remove();
	}

	$('#wa-sync').on('click', function() {
		$.ajax({
			url: wa_launch_full_sync.endpoint,
			type: 'post',
			data: {
				action: 'wa_launch_full_sync',
				wa_launch_full_sync_nonce: wa_launch_full_sync.nonce,
			},
			beforeSend: function() {
				$('.while-we-wait').addClass('active');
			},
			success: function(response) {
				$('.while-we-wait').removeClass('active');
			},
			error: function(response) {
				$('.while-we-wait').removeClass('active');
				// This is mainly to help a developer debugging to know the difference between success and failure. From
				// the agent's perspective, they can't tell the difference. I'm not sure what to do about that for now.
				console.error('Error syncing Wise Agent contacts', response);
			},
			dataType: 'html'
		});		
	});
});
