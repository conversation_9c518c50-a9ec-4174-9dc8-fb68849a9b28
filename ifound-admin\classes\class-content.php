<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class Content extends iFoundAdmin {

	private $post_type		= 'content';
	private $query_var		= 'content';
	private $label_name 	= 'Content';
	private	$label_names	= 'Content';
	private $idx_forms 		= 'idx-forms';
	protected	$blog_id	= 2;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	public function __construct() {
		add_action( 'init', array( $this, 'content_post_type' ) );
		add_action( 'save_post', array( $this, 'save_content' ), 10, 1 );	
		add_action( 'rest_api_init', array( $this, 'idx_forms_route' ) );
		add_action('edit_form_after_title', [$this, 'edit_form_after_title']);
	}

	public function content_post_type() {
		
		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'query_var'				=> $this->query_var,
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title', 'editor' ),
				'register_meta_box_cb'	=> array( $this, 'add_metaboxes' )
			)
	  	);
		
	}

	// Put the reminder metabox above the content editor.
	// I got this from https://wordpress.stackexchange.com/a/158485/27896
	// When we use 'ifound_context', it can be anything other than the built-in context names.
	public function edit_form_after_title() {
		// Get the globals:
		global $post, $wp_meta_boxes;

		// Output the "advanced" meta boxes:
		do_meta_boxes( get_current_screen(), 'ifound_context', $post );

		// Remove the initial "advanced" meta boxes:
		unset($wp_meta_boxes[$this->post_type]['ifound_context']);
	}

	public function add_metaboxes($post) {

		if (has_term('global_blox', 'content_type', $post)) {
			add_meta_box(
				'reminder_metabox',
				__('Reminder', 'ifound'),
				array($this, 'reminder_metabox'),
				$this->post_type,
				'ifound_context',
				'high'
			);
		}

		add_meta_box( 
			'page_image_metabox', 
			__( '<i class="far fa-images"></i> Images', 'ifound' ), 
			array( $this, 'page_image_metabox'), 
			$this->post_type,
			'advanced',
			'high'
		);
		
	}

	public function reminder_metabox() {
		?>
		<ul>
			<li>This JSON for Blox is a little different than others. It's not copied over raw/verbatim. It's meant to
			allow for more intelligent control. As of this first writing, such granular control isn't really needed, but
			it could be used in the future.</li>
		</ul>
		<?php
	}

	public function page_image_metabox() {

		wp_enqueue_script( 'featured_image_js' );

		switch_to_blog( 1 );
		$mls_names 	 = get_terms( array( 'taxonomy' => 'mls_name', 'fields' => 'names', 'hide_empty' => false ) );
		restore_current_blog();

		$stylesheets = get_terms( array( 'taxonomy' => 'stylesheet', 'fields' => 'names', 'hide_empty' => false ) ); ?>

		<table class="form-table page-images-table">
					
			<tbody><?

				foreach( $mls_names as $mls_name ) {

					foreach( $stylesheets as $stylesheet ) { 

						$id = 'page_' . $mls_name .'_' . $stylesheet; 

						$image = get_post_meta( get_the_ID(), $id, true );

						$value = empty( $image ) ? '' : $image; 

						$class = empty( $image ) ? 'page_img_button' : 'remove_page_img_button';

						$html = empty( $image ) ? '<i class="far fa-image"></i> Set featured image' : '<i class="far fa-trash-alt"></i> Remove featured image'; ?>

						<tr>
								
							<th scope="row" colspan="4" class="page-image-title"><label for="<? echo $id; ?>"><h3><? _e( $mls_name .'_' . $stylesheet, 'ifound' ); ?></h3></label></th>

						</tr>

						<tr>
								
							<th scope="row"><label for="<? echo $id; ?>"><? _e( 'Page Image', 'ifound' ); ?></label></th>
								
							<td>
								
								<div id="featuredimagediv_<? echo $id; ?>" class="featuredimagediv">

									<img src="<? echo $value; ?>" id="page_img_<? echo $id; ?>" class="clone-parent-page-img"/>

									<div>

										<a 
											title="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>" 
											href="javascript:;" 
											id="<? echo $id; ?>" 
											data-uploader_title="<? echo esc_attr__( 'Choose an image', 'ifound' ); ?>" 
											data-uploader_button_text="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>" 
											class="button button-primary <? echo $class; ?>"
										>
												<? _e( $html, 'ifound' ); ?>
										</a>

										<span class="clone-parent-page-button button button-primary"><? _e( 'Copy to all empty', 'ifound' ); ?></span>

										<input type="hidden" id="page_img_input_<? echo $id; ?>" name="page_images[<? echo $id; ?>]" value="<? echo $value; ?>" class="clone-parent-page-input"/>

									</div>

								</div>
									
							</td><?

							$id = 'featured_' . $mls_name .'_' . $stylesheet; 

							$image = get_post_meta( get_the_ID(), $id, true );

							$value = empty( $image ) ? '' : $image; 

							$class = empty( $image ) ? 'page_img_button' : 'remove_page_img_button';

							$html = empty( $image ) ? '<i class="far fa-image"></i> Set featured image' : '<i class="far fa-trash-alt"></i> Remove featured image'; ?>

							<th scope="row"><label for="<? echo $id; ?>"><? _e( 'Featured Image', 'ifound' ); ?></label></th>
								
							<td>
								
								<div id="featuredimagediv_<? echo $id; ?>" class="featuredimagediv">

									<img src="<? echo $value; ?>" id="page_img_<? echo $id; ?>" class="clone-parent-featured-img"/>

									<div>

										<a 
											title="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>" 
											href="javascript:;" 
											id="<? echo $id; ?>" 
											data-uploader_title="<? echo esc_attr__( 'Choose an image', 'ifound' ); ?>" 
											data-uploader_button_text="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>" 
											class="button button-primary <? echo $class; ?>"
										>
												<? _e( $html, 'ifound' ); ?>
										</a>

										<span class="clone-parent-featured-button button button-primary"><? _e( 'Copy to all empty', 'ifound' ); ?></span>

										<input type="hidden" id="page_img_input_<? echo $id; ?>" name="featured_images[<? echo $id; ?>]" value="<? echo $value; ?>" class="clone-parent-featured-input"/>

									</div>

								</div>
									
							</td>
										
						</tr<?

					}

				} ?>

			</tbody>

		</table><?

	}

	public function save_content( $content_id ){
  
		if ( ! current_user_can( 'edit_posts' ) ) return;
		
		global $post; 
    
		if ( $post->post_type == $this->post_type && isset( $_POST['original_publish'] ) ) {

			$data = array();

			foreach( $_POST['page_images'] as $key => $image_url ) {

				$data = esc_url( $image_url, array( 'http', 'https' ) );

				update_post_meta( $content_id, $key, $data );

			}

			foreach( $_POST['featured_images'] as $key => $image_url ) {

				$data = esc_url( $image_url, array( 'http', 'https' ) );

				update_post_meta( $content_id, $key, $data );

			}

		}

	}

	// https://build.ifoundadmin.com/wp-json/ifound-admin/1.0.0/idx-forms/
	public function idx_forms_route() {

		register_rest_route( 
			'ifound-admin/' . $this->api_version,
			'/idx-forms/', 
			array(
				'methods'  	=> WP_REST_Server::READABLE,
				'callback' 	=> array( $this, 'get_idx_forms' )
			)
		);

	}

	public function get_idx_forms() {

		if ( $mls_name = $this->apikey_to_mls_name() ) {

			$content_types = array(
				'cmc-form',
				'address-capture-form',
				'get-pre-qualified-form',
				'make-offer-form',
				'schedule-showing-form',
				'registration-form'
			);

			$forms = array();

			foreach ( $content_types as $content_type ) {

				$args = array( 
					'post_type' 		=> 'content', 
					'posts_per_page' 	=> -1,
					'tax_query' 		=> array(
						'relation' 		=> 'AND',
						array(
							'taxonomy'	=> 'content_type',
							'field'    	=> 'slug',
							'terms'    	=> array( $content_type )
						),
						array(
							'taxonomy'	=> 'mls_name',
							'field'    	=> 'slug',
							'terms'    	=> array( $mls_name )
						)
					)
				);

				$posts = get_posts( $args );

				$post = $posts[0];

				$forms[$content_type] = json_decode( $post->post_content );

			}

			return $forms;

		}

	}
	
}
