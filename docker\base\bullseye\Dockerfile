# Reminder how to build this. Change directory to this parent dir, then do:
# docker image build -t ifoundagent:base-bullseye -f bullseye/Dockerfile .
FROM debian:11.6-slim

# Fixes some weird terminal issues such as broken clear / CTRL+L
ENV TERM=linux
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update

RUN apt-get install -y --no-install-recommends --fix-missing \
        build-essential \
        apt-utils \
        # Install dig, host, nslookup, etc.
        dnsutils \
        gnupg gnupg2 gnupg1 \
        apt-transport-https \
        curl \
        less \
        ca-certificates \
        wget \
        vim \
        netcat \
        iputils-ping \
        # Install ts
        moreutils

RUN groupadd ubuntu
# Create ubuntu user and make their primary group ubuntu
RUN useradd --create-home --shell /bin/bash -g ubuntu ubuntu
# Add to sudo group
RUN usermod -a -G sudo ubuntu
