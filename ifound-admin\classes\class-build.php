<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class Build extends iFoundAdmin {

	private $body = array();
	private $client_id;
	private $client_info;
	private $development;
	private $api_key;
	private $dev_url;

	public static function init() {
        $class = __CLASS__;
        new $class;
    }


	public function __construct() {
		add_action( 'wp_ajax_build_site', array( $this, 'ajax_build_site' ) );
		add_action('ifound_build_site', array($this, 'build_site'), 10, 2);
	}

	public function ajax_build_site() {
		check_ajax_referer('build_site_secure_me', 'build_site_nonce');

		if (isset($_REQUEST['client_id'])) {
			$client_id = intval($_REQUEST['client_id']);
			$this->build_site($client_id);
		} else {
			wp_die('Missing client ID parameter', 400);
		}

		die();

	}

	public function build_site($client_id, $send_emails = false) {
		$headers = [];
		$headers[] = 'Bcc: ' . $this->get_config()['support_email_address'];

		try {
		    $this->client_id = $client_id;
			$this->get_client();
			$this->init_build();
		} catch (\Throwable $e) {
			if ($send_emails) {
				$client_edit_url = admin_url("post.php?post={$client_id}&action=edit");

				$email_address = get_option('admin_email');
				$message = "An error occurred building a website";
				$message .= "\n\nClient ID: {$client_id}";
				$message .= "\n\nClient info page: {$client_edit_url}";
				if ($this->client_info) {
					$message .= "\n\nClient info:";
					$message .= "\n\n" . json_encode($this->client_info, JSON_PRETTY_PRINT);
				} else {
					$message .= "\n\nUnfortunately we weren't even able to retrieve the client info";
				}
				$message .= "\n\nHere is the error message:";
				$message .= "\n\n" . $e;
				$message .= "\n\nPlease build the site manually.";
				if ($this->client_info) {
					$message .= "\n\nThe client has been notified that you'll be contacting them.";
				} else {
					$message .= "\n\nBecause we don't have client info, we were not able to notify them of the build failure.";
				}
				$message = wp_mail($email_address, 'Error building site', $message);

				if ($this->client_info) {
					$email_address = $this->client_info['email'];
					$message = "There was a problem building your iFoundAgent website";
					$message .= "\n\nOur staff will investigate the issue and contact you. Sorry for the inconvenience.";
					$message .= "\n\n- iFoundAgent team";
					$message = wp_mail($email_address, 'There was a problem building your iFoundAgent website', $message, $headers);
				}
			}
			wp_die($e->__toString(), 500);
		}

		if ($send_emails) {
			$client_edit_url = admin_url("post.php?post={$client_id}&action=edit");
			$wp_admin_url = $this->dev_url;
			if (substr($wp_admin_url, -1) !== '/') {
				$wp_admin_url .= '/';
			}
			$wp_admin_url .= 'wp-admin';

			$email_address = get_option('admin_email');
			$message = "The website for a client was successfully built";
			$message .= "\n\nClient ID: {$client_id}";
			$message .= "\n\nClient info page: {$client_edit_url}";
			$message .= "\n\nClient info:";
			$message .= "\n\n" . json_encode($this->client_info, JSON_PRETTY_PRINT);
			$message .= "\n\nWebsite: {$this->dev_url}";
			$message = wp_mail($email_address, 'Successfully built site', $message);

			$email_address = $this->client_info['email'];
			$message = "Your iFoundAgent real estate agent site is ready!";
			$message .= "\n\nWebsite: {$this->dev_url}";
			$message .= "\n\nThe Wordpress admin site is at: {$wp_admin_url}. Please login with email address {$this->client_info['email']}. For security reasons, we do not provide a password over email, so you will need to use the \"Lost your password?\" link (beneath the login form) to access the admin site for the first time.";
			$message .= "\n\n- iFoundAgent team";
			$message = wp_mail($email_address, 'Your iFoundAgent real estate agent site is ready!', $message, $headers);
		}
	}

	public function get_client() {

		switch_to_blog( 1 );

		$client_id = isset( $this->client_id ) ? $this->client_id : $this->get_client_id();

		if( $client_id ) {

			$this->client_info 	= get_post_meta( $client_id, 'client_info', true );
			$this->billing 		= get_post_meta( $client_id, 'billing', true );
			$this->api_key 		= get_post_meta( $client_id, 'api_key', true );
			$this->website 		= get_post_meta( $client_id, 'website', true );
			$this->dev 			= get_post_meta( $client_id, 'development', true );

			$this->mls_name 	= $this->get_mls_name( $client_id );
			$this->photo_key 	= $this->mls_name . '_' . $this->website['stylesheet'];

			$this->agent_name 	= $this->client_info['fname'] . ' ' . $this->client_info['lname'];

			$this->allowed_features = wp_get_object_terms( $client_id, 'plugin_features', array( 'fields' => 'slugs' ) );
			$this->sub_section = empty( $this->dev['sub_section'] ) ? false : $this->dev['sub_section'];

			restore_current_blog();

			return true;

		}

		restore_current_blog();

		return false;

	}

	private function log() {
		if (true) {
			$vars = func_get_args();
			call_user_func_array('elog', $vars);
		}
	}

	public function init_build() {

		$is = array( 1, 2, 3, 4, 5 );

		foreach( $is as $i ) {

			$body = $this->body( $i );

			if( $i > 1 ) {

				$body['blog_id'] = $this->blog_id;
				$body['user_id'] = $this->user_id;

			}

			$response = $this->request( 'init-build/', $body );

			if( $i == 1 ) {

				$this->process_response( $response );

				$this->dev_url 	= $response['dev_url'];
				$this->blog_id 	= $response['blog_id'];
				$this->user_id 	= $response['user_id'];

			} elseif( $i == 2 ) {

				$this->form_ids = $response['form_ids'];

			} elseif( $i == 3 ) {

				$this->menu_ids = $response['menu_ids'];
				$this->post_ids = $response['post_ids'];

			}

		}

	}

	public function body( $i ) {

	    $body = null;
		if ($i === 1) {
			$body = [
				'create_site'              => $this->create_site(),
				'api_settings'             => $this->api_settings(),
				'start_plugins'            => $this->plugins(),
				'add_gravity_forms_tables' => true,
				'publish_posts'            => $this->get_posts(),
			];
		} else if ($i === 2) {
			$body = [
				'publish_testomonials' => $this->get_testomonials(),
				'insert_views'         => $this->get_views(),
				'add_gravity_forms'    => $this->get_gravity_forms(),
				'add_blox'             => $this->get_blox(),
			];
		} else if ($i === 3) {
			$body = [
				'add_nav_menus'          => $this->get_nav_menus(),
				'publish_pages'          => $this->get_pages(),
				'create_main_nav'        => $this->get_main_nav(),
				'publish_featured_pages' => $this->get_featured_pages(),
			];
		} else if ($i === 4) {
			$body = [
				'set_ifound_sliders'            => $this->get_ifound_sliders(),
				'set_slider_revolution_sliders' => $this->get_slider_revolution_sliders(),
				'set_metaslider_sliders'        => $this->get_metaslider_sliders(),
				'set_backgrounds'               => $this->get_backgrounds(),
				'set_theme_settings'            => $this->get_theme_settings(),
				'set_settings'                  => $this->get_settings(),
			];
		} else if ($i === 5) {
			$body = [
				'update_saved_options'     => $this->get_saved_settings(),
				'responsive_menu_settings' => $this->get_responsive_menu_settings(),
				'set_broker_logo'          => $this->get_broker_logo(),
			];
		}

		$this->log('$body', $i, $body);
		return $body;
	}

	public function create_site() {

		return array(
			'user_name' 	=> strtolower( $this->client_info['fname'] . $this->client_info['lname'] ),
			'user_email'	=> $this->client_info['email'],
			'theme'			=> 'genesis',
			'stylesheet'	=> $this->website['stylesheet'],
			'blog_title'	=> $this->dev['site_title'],
			'user_meta'		=> array(
				'first_name' 		=> $this->client_info['fname'],
				'last_name' 		=> $this->client_info['lname'],
				'display_name' 		=> $this->client_info['fname'] . ' ' . $this->client_info['lname'],
				'admin_bar_front' 	=> false,
				'role'				=> 'administrator'
			),
			'role'					=> 'administrator',
			'domain'				=> $this->website['domain'],
		);

	}

	public function api_settings() {

		return array(
			'ifound_api_settings' 		=> array(
				'api_secret' 			=> $this->api_key,
				'mls_name'	 			=> $this->mls_name
			),
			'ifound_allowed_features' 	=> $this->allowed_features
		);

	}

	public function plugins() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'orderby' 			=> 'date',
			'order'   			=> 'ASC',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'plugin' ),
					'operator'  => 'IN'
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] ),
					'operator'  => 'IN'
				)
			)
		);

		$posts = get_posts( $args );

		foreach( $posts as $post )
			$plugins[] = $post->post_title;

		restore_current_blog();

		return $plugins;

	}

	public function get_gravity_forms() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'forms' )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		$form_object = json_decode( $post->post_content, true );

		restore_current_blog();

		return array(
			'contact' 		=> $form_object[0],
			'home_worth' 	=> $form_object[1],
		);

	}

	public function get_blox() {
		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'global_blox' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
			),
		);

		$posts = get_posts( $args );

		$data = [];
		foreach( $posts as $the_post ) {
			$data[] = [
				// Reminder: the data we're sending is not WP posts. It's custom stuff. As of this writing, it's just
				// a single postmeta entry (a post must be created first).
				'content' => $the_post->post_content,
			];
		}

		restore_current_blog();

		return $data;
	}

	public function get_nav_menus() {

		return array(
			'main' 			=> 'Main Menu',
			'communities'	=> 'Communities'
		);

	}

	public function get_posts() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'post' )
				)
			)
		);

		$posts = get_posts( $args );

		foreach( $posts as $the_post ) {

			/** This was added since some posts has multiple copies. */
			$the_post->post_name 		= sanitize_title( $the_post->post_title );

			$the_post->meta_key 		= false;
			$the_post->post_type 		= 'post';
			$the_post->page_image 		= get_post_meta( $the_post->ID, 'page_' . $this->photo_key, true );
			$the_post->featured_image 	= get_post_meta( $the_post->ID, 'featured_' . $this->photo_key, true );
			$the_post->thumbsize 		= 'full';
			$the_post->post_terms 		= array( 'Real Estate', 'Featured' );
			$the_post->term_tax 		= 'category';
			$complete_posts[] 			= $the_post;

		}

		restore_current_blog();

		return $complete_posts;

	}

	public function get_pages() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'page' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		foreach( $posts as $the_post ) {

			if( $the_post->post_title == 'About' ) {

				$the_post->post_content 	= str_replace( '{AgentBio}', $this->dev['bio'], $the_post->post_content );
				$the_post->page_image 		= empty( $this->dev['headshot'] ) ? get_post_meta( $the_post->ID, 'page_' . $this->photo_key, true ) : $this->dev['headshot'];
				$the_post->featured_image 	= empty( $this->dev['headshot'] ) ? get_post_meta( $the_post->ID, 'featured_' . $this->photo_key, true ) : $this->dev['headshot'];
				$the_post->thumbsize 		= 'medium';

			} elseif( $the_post->post_title == 'Contact' && $this->website['stylesheet'] == 'pinnacle' ) {

				$the_post->page_image 		= fasle;
				$the_post->featured_image 	= empty( $this->dev['headshot'] ) ? get_post_meta( $the_post->ID, 'featured_' . $this->photo_key, true ) : $this->dev['headshot'];
				$the_post->thumbsize 		= 'medium';

			} else {

				$the_post->page_image 		= get_post_meta( $the_post->ID, 'page_' . $this->photo_key, true );
				$the_post->featured_image 	= get_post_meta( $the_post->ID, 'featured_' . $this->photo_key, true );
				$the_post->thumbsize 		= 'full';
				$the_post->post_content 	= str_replace( '{FormID}', $this->form_ids['contact'], $the_post->post_content );

			}

			/** This was added since some pages has multiple copies. */
			$the_post->post_name 	= sanitize_title( $the_post->post_title );
			if ($the_post->post_name === 'advanced-search') {
				$the_post->post_name = 'listing-search';
			}

			$the_post->meta_key 	= false;
			$the_post->post_type 	= 'page';
			$the_post->post_terms 	= false;
			$the_post->term_tax 	= false;
			$complete_posts[] 		= $the_post;

		}

		restore_current_blog();

		return $complete_posts;

	}

	public function get_testomonials() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'testimonial' )
				)
			)
		);

		$posts = get_posts( $args );

		foreach( $posts as $the_post ) {

			list( $title, $name ) = explode( '|', $the_post->post_title );

			$the_post->meta_key 			= 'client_name';
			$the_post->meta_value 			= $name;
			$the_post->post_title 			= $title;
			$the_post->post_content 		= str_replace( '{Fname}', $this->client_info['fname'], $the_post->post_content );
			$the_post->page_image 			= false;
			$the_post->featured_image 		= false;
			$the_post->thumbsize 			= false;
			$the_post->post_type 			= 'wpm-testimonial';
			$the_post->post_terms 			= array( 'Home' );
			$the_post->term_tax 			= 'wpm-testimonial-category';
			$the_post->register_taxonomy 	= true;
			$complete_posts[] 				= $the_post;

		}

		restore_current_blog();

		return $complete_posts;

	}

	public function get_featured_pages() {

		$featured_images = $this->get_featured_images();

		switch_to_blog( 2 );

		$i = 1;

		while( $i <= 6 ) {

			$this->location[$i]		= $this->dev['featured_' . $i];
			$value					= preg_split('/,\s*/', $this->location[$i]);
			$title   				= $this->location[$i] . ' Homes for Sale';
			$name  					= sanitize_title( $title );
			$this->page_slug[$i]  	= $name;
			$type 					= $this->dev['featured_type_' . $i];

			$the_post = array(
				'meta_key' 			=> 'save_this_shortcode',
				'meta_value' 		=> array( 'query' => array( $type => $value ) ),
				'post_title' 		=> $title,
				'post_name'			=> $name,
				'post_content'		=> '[ifound id={meta_id}]',
				'page_image' 		=> false,
				'featured_image' 	=> $featured_images[$i],
				'featured_page' 	=> true,
				'thumbsize' 		=> 'full',
				'post_type' 		=> 'page',
				'post_terms' 		=> false,
				'term_tax' 			=> false
			);

			$complete_posts[] = $the_post;

			$i++;

		}

		restore_current_blog();

		return $complete_posts;

	}

	public function get_featured_images() {

		switch_to_blog( 4 );

		$args = array(
			'post_type' 		=> 'featured_images',
			'posts_per_page' 	=> 6,
			'orderby'        	=> 'rand',
			'fields'			=> 'titles',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		$i = 1;
		$featured_images = array();

		foreach( $posts as $the_post ) {

			$featured_images[$i] = $the_post->post_title;

			$i++;

		}

		restore_current_blog();

		return $featured_images;

	}

	public function get_main_nav() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'nav_menu' )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		restore_current_blog();

		return $post->post_content;

	}

	public function get_views() {

		$views = array(
			'home' => array(
				'name'  => 'Home',
				'value'	=> array(
				 	'all' 				=> '1',
				 	'background' 		=> array(
					    'color' 				=> '',
					    'type' 					=> '',
					    'preset' 				=> '',
					    'gradient1' 			=> '',
					    'gradient2' 			=> '',
					    'example-font-color'	=> 'dark',
					),
					'category' 			=> 0,
					'class' 			=> '',
					'client_section' 	=> array (
					 	0 => array (
					 		'field' 			=> 'client_name',
					      	'type' 				=> 'text',
					      	'before' 			=> '',
					      	'class' 			=> 'testimonial-name',
					    ),
					    1 => array (
					      	'field' 			=> 'company_name',
					      	'type' 				=> 'link',
					      	'before' 			=> '',
					      	'class' 			=> 'testimonial-company',
					      	'url' 				=> 'company_website',
					      	'link_text' 		=> 'value',
					      	'link_text_custom' 	=> '',
					      	'new_tab' 			=> 1,
					    ),
					),
					'column_count' 		=> '2',
					'content' 			=> 'excerpt',
					'count' 			=> 1,
					'divi_builder' 		=> 0,
					'excerpt_length'	=> 55,
					'form_ajax' 		=> 0,
					'form_id' 			=> '1',
					'gravatar' 			=> 'no',
					'id' 				=> 0,
					'layout' 			=> '',
					'lightbox' 			=> 0,
					'mode' 				=> 'slideshow',
					'more_full_post'	=> '0',
					'more_page_hook'	=> 'wpmtst_view_footer',
					'more_page_text' 	=> 'Read more testimonials',
					'more_post' 		=> 1,
					'more_post_ellipsis' => 1,
					'more_post_text' 	=> 'Read more',
					'nav' 				=> 'after',
					'order' 			=> 'newest',
					'pagination' 		=> 0,
					'pagination_type' 	=> 'simple',
					'per_page' 			=> 5,
					'slideshow_settings' => array (
						'adapt_height' 			=> 1,
						'adapt_height_speed' 	=> 0.5,
						'auto_hover' 			=> 1,
						'auto_start' 			=> 1,
						'controls_style' 		=> 'buttons',
						'controls_type' 		=> 'none',
						'effect' 				=> 'horizontal',
						'nav_position' 			=> 'inside',
						'pager_style' 			=> 'buttons',
						'pager_type' 			=> 'full',
						'pause' 				=> 5.0,
						'speed' 				=> 1.5,
						'stop_auto_on_click' 	=> 1,
						'stretch' 				=> 0,
					),
					'template' 			=> 'large:widget',
					'thumbnail' 		=> 0,
					'thumbnail_height' 	=> '',
					'thumbnail_size' 	=> 'thumbnail',
					'thumbnail_width' 	=> '',
					'title' 			=> 0,
					'title_link' 		=> 0,
					'use_default_length'=> '1',
					'use_default_more' 	=> '0',
				)
			),
			'page' =>  array(
				'name'  => 'Testimonials Page',
				'value'	=> array(
				  	'all' 				=> '1',
				  	'background' 		=> array(
				    	'color' 				=> '',
				    	'type' 					=> '',
				    	'preset' 				=> '',
				    	'gradient1' 			=> '',
				    	'gradient2' 			=> '',
				    	'example-font-color' 	=> 'dark',
				  	),
				  	'category' 			=> 'all',
				  	'class' 			=> 'testimonial-name',
				  	'client_section' 	=> array (
				    	0 				=> array (
				      		'field' 			=> 'client_name',
				      		'type' 				=> 'text',
				      		'before' 			=> '',
				      		'class' 			=> 'testimonial-name',
				    	),
				    	1 				=> array (
				      		'field' 			=> 'company_name',
				      		'type' 				=> 'link',
				      		'before' 			=> '',
				      		'class' 			=> 'testimonial-company',
				      		'url' 				=> 'company_website',
				      		'link_text' 		=> 'value',
				      		'link_text_custom' 	=> '',
				      		'new_tab' 			=> 1,
				    	),
				  	),
				  	'column_count' 		=> '2',
				  	'content' 			=> 'entire',
				  	'count' 			=> 1,
				  	'divi_builder' 		=> 0,
				  	'excerpt_length' 	=> 55,
				  	'form_ajax' 		=> 0,
				  	'form_id' 			=> '1',
				  	'gravatar' 			=> 'no',
				  	'id' 				=> 0,
				  	'layout' 			=> '',
				  	'lightbox' 			=> 0,
				  	'mode' 				=> 'display',
				  	'more_full_post' 	=> '0',
				  	'more_page_hook' 	=> 'wpmtst_view_footer',
				  	'more_page_text' 	=> 'Read more testimonials',
				  	'more_post' 		=> 0,
				  	'more_post_ellipsis'=> 1,
				  	'more_post_text' 	=> 'Read more',
				  	'nav' 				=> 'after',
				  	'order' 			=> 'newest',
				  	'pagination' 		=> 0,
				  	'pagination_type' 	=> 'simple',
				  	'per_page' 			=> 5,
				  	'slideshow_settings'=> array (
				    	'adapt_height' 				=> 0,
				    	'adapt_height_speed' 		=> 0.5,
				    	'auto_hover' 				=> 1,
				    	'auto_start' 				=> 1,
				    	'controls_style' 			=> 'buttons',
				    	'controls_type' 			=> 'none',
				    	'effect' 					=> 'fade',
				    	'nav_position' 				=> 'inside',
				    	'pager_style' 				=> 'buttons',
				    	'pager_type' 				=> 'none',
				    	'pause' 					=> 8.0,
				    	'speed' 					=> 1,
				    	'stop_auto_on_click' 		=> 1,
				    	'stretch' 					=> 0,
				  	),
				  	'template' 				=> 'default:content',
				  	'thumbnail' 			=> 0,
				  	'thumbnail_height'		=> '',
				  	'thumbnail_size' 		=> 'thumbnail',
				  	'thumbnail_width' 		=> '',
				  	'title' 				=> 1,
				  	'title_link' 			=> 0,
				  	'use_default_length' 	=> '1',
				  	'use_default_more' 		=> '0'
				)
			)
		);

		if (in_array($this->website['stylesheet'], ['olympus', 'palisades', 'victoria'], true)) {
			$stylesheet = $this->website['stylesheet'];
			$json_string = file_get_contents(__DIR__ . "/../master_data/strong_testimonials/{$stylesheet}/home.json");
			$value = json_decode($json_string, true);
			$views['home']['value'] = $value;
		}

		return $views;
	}

	public function get_theme_settings() {

		include( plugin_dir_path( __DIR__ ) . 'options/' . $this->website['stylesheet'] . '.php' );

		return $theme_settings;

	}

	public function get_settings() {

		return array(
			'blogdescription' 				=> $this->dev['tagline'],
	  		'admin_email' 					=> $this->client_info['email'],
    		'permalink_structure'			=> '/%postname%/',
    		'blog_public' 					=> 1,
    		'default_link_category' 		=> 2,
    		'posts_per_page'				=> 10,
    		'show_on_front' 				=> 'posts',
    		'timezone_string' 				=> $this->get_timezone(),
    		'medium_size_h'					=> 300,
    		'medium_size_w'					=> 300,
    		'wp-to-buffer-pro_licenseKey'	=> '6fae18a09b1cf136a2b997fb20deb64e',
    		'ifound_agent' 					=> array(
        		'agent_name'     			=> $this->agent_name,
		    	'agent_id'					=> $this->client_info['agent_id'],
		    	'team_name'					=> $this->client_info['team_name'],
		    	'broker_name'				=> $this->client_info['broker_name'],
		    	'broker_id'					=> $this->client_info['broker_id'],
		    	'street_address'			=> $this->billing['billing_address'],
		    	'city'						=> $this->billing['billing_city'],
		    	'state'						=> $this->billing['billing_state'],
		    	'zip'						=> $this->billing['billing_zip'],
		    	'office_phone'				=> '',
		    	'mobile_phone'				=> $this->client_info['phone'],
				'call_me_phone'				=> $this->client_info['phone'],
		    	'text_me_phone'				=> $this->client_info['phone'],
		    	'fax'						=> '',
		    	'email'						=> $this->client_info['email'],
		    	'bcc_email'					=> ''
    		),
    		'ifound_sociaL_media_networks' 	=> array(
		    	'button_type' 				=> 'floating',
		    	'networks'					=> $this->get_social_networks()
		    ),
			'ifound_map_settings' 			=> array(
				'state'						=> $this->get_state(),
				'city'						=> $this->get_cities()
			),
			'cmc_admin_map_settings'		=> $this->get_cmc_map_settings(),
			'ifound_quick_search_criteria'	=> $this->get_quick_search_criteria()
    	);

	}

	/**
	 * Quick Search Criteria
	 *
	 * The deafult criteria options for the quick search.
	 *
	 * @uses iFoundAdmin::explode()
	 * @uses $blog_id 8
	 *
	 * @since 1.1.1
	 *
	 * @return array $quick_search_criteria An array of QS criteria defaults.
	 */

	public function get_quick_search_criteria() {

		switch_to_blog( 8 );

		$args = array(
			'post_type' 		=> 'mls_association',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'quick_search_defaults' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		$array = $this->explode( $post->post_content );

		restore_current_blog();

		return array_keys( $array );

	}

	public function get_cmc_map_settings() {

		switch_to_blog( 8 );

		$args = array(
			'post_type' 		=> 'mls_association',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'cmc_map_settings' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		$settings = $posts[0]->post_content;

		restore_current_blog();

		list( $n, $s, $e, $w, $c_lat, $c_lng, $z ) = explode( '|', $settings );

		return (object) array(
			'north' 		=> floatval( $n ),
			'south' 		=> floatval( $s ),
			'east' 			=> floatval( $e ),
			'west' 			=> floatval( $w ),
			'center_lat' 	=> floatval( $c_lat ),
			'center_lng'	=> floatval( $c_lng ),
			'zoom' 			=> intval( $z )
		);

	}

	public function get_featured_listings_script() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'featured_listings_script' )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		restore_current_blog();

		return $post->post_content;

	}

	// This updates already saved settings
	public function get_saved_settings() {

		return array(
			'ifound_crm_settings' 		=> array(
				'from_name'					=> $this->agent_name,
				'from_email'				=> $this->client_info['email']
			),
			'ifound_featured_settings' 	=> array(
				'featured_slider_script' 	=> stripcslashes( $this->get_featured_listings_script() )
			)
		);

	}

	public function get_social_networks() {

		$networks =  array(
            'facebook' 			=> array(
				'url'				=> $this->dev['facebook'],
				'name'				=> 'facebook',
				'class'				=> 'facebook-f'
			),
			'twitter' 			=> array(
				'url'				=> $this->dev['twitter'],
				 'name'				=> 'twitter',
				'class'				=> 'twitter'
			),
			'googleplus'		=> array(
				'url'				=> $this->dev['google'],
				'name'				=> 'googleplus',
				'class'				=> 'google-plus-g'
			),
			'linkedin' 			=> array(
				'url'				=> $this->dev['linkedin'],
				'name'				=> 'linkedin',
				'class'				=> 'linkedin-in'
			),
			'pinterest' 		=> array(
				'url'				=> $this->dev['pinterest'],
				'name'				=> 'pinterest',
				'class'				=> 'pinterest-p'
			),
			'youtube' 			=> array(
				'url'				=> $this->dev['youtube'],
				'name'				=> 'youtube',
				'class'				=> 'youtube'
			),
			'vimeo' 			=> array(
				'url'				=> $this->dev['vimeo'],
				'name'				=> 'vimeo',
				'class'				=> 'vimeo'
			),
			'instagram' 		=> array(
				'url'				=> $this->dev['instagram'],
				'name'				=> 'instagram',
				'class'				=> 'instagram'
			),
			'tumblr'			=> array(
				'url'				=> $this->dev['tumblr'],
				'name'				=> 'tumblr',
				'class'				=> 'tumblr'
			),
            'tiktok'			=> array(
	            'url'				=> $this->dev['tiktok'],
	            'name'				=> 'tiktok',
	            'class'				=> 'tiktok'
            ),
		);

		$values = array();

		foreach( $networks as $key => $value ) {

			if( empty( $value['url'] ) ) continue;

			$values[$key] = $value;

		}

		return $values;

	}

	public function get_state() {

		switch_to_blog( 8 );

		$args = array(
			'post_type' 		=> 'mls_association',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'state' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		restore_current_blog();

		return $post->post_title;

	}

	public function get_cities() {

		switch_to_blog( 8 );

		$args = array(
			'post_type' 		=> 'mls_association',
			'posts_per_page' 	=> -1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'map_cities' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		restore_current_blog();

		return explode( '|', $post->post_content );

	}

	public function get_timezone() {

		switch_to_blog( 8 );

		$args = array(
			'post_type' 		=> 'mls_association',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'timezone' )
				),
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		restore_current_blog();

		return $post->post_title;

	}

	public function get_ifound_sliders() {

		switch_to_blog( 5 );

		$args = array(
			'post_type' 		=> 'sliders',
			'posts_per_page' 	=> 3,
			'orderby'        	=> 'rand',
			'fields'			=> 'titles',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
				array(
					'taxonomy'	=> 'slider_type',
					'field'    	=> 'slug',
					'terms'    	=> array('ifound')
				),
			)
		);

		if( $this->sub_section ) {

			$sub_args = array(
				'taxonomy'	=> 'sub_section',
				'field'    	=> 'slug',
				'terms'    	=> array( $this->sub_section )
			);

			$args['tax_query'] = array_merge( $args['tax_query'], $sub_args );

		}

		$posts = get_posts( $args );

		$sliders = array();

		foreach( $posts as $the_post ) {

			$sliders[] = $the_post->post_title;

		}

		restore_current_blog();

		return $sliders;

	}

	// This is a little bit different than the iFound Sliders plugin. We'll get both the sliders and the slides.
	public function get_slider_revolution_sliders() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 3,
			'orderby'			=> 'rand',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'slider-revolution-slider' )
				)
			)
		);

		$sliders = get_posts( $args );
		$sliders = array_map(function($x) {
			return [
				'post_title' => $x->post_title,
				'post_content' => $x->post_content,
			];
		}, $sliders);

		restore_current_blog();

		switch_to_blog( 5 );

		$args = array(
			'post_type' 		=> 'sliders',
			'posts_per_page' 	=> 3,
			'orderby'        	=> 'rand',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
				array(
					'taxonomy'	=> 'slider_type',
					'field'    	=> 'slug',
					'terms'    	=> array('slider-revolution')
				),
			)
		);

		if( $this->sub_section ) {

			$sub_args = array(
				'taxonomy'	=> 'sub_section',
				'field'    	=> 'slug',
				'terms'    	=> array( $this->sub_section )
			);

			$args['tax_query'] = array_merge( $args['tax_query'], $sub_args );

		}

		$slides = get_posts( $args );
		$slides = array_map(function($x) {
			return [
				'post_title' => $x->post_title,
			];
		}, $slides);

		restore_current_blog();

		return [
			'sliders' => $sliders,
			'slides' => $slides,
		];

	}

	// This is a little bit different than the iFound Sliders plugin. We'll get both the sliders and the slides.
	public function get_metaslider_sliders() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 3,
			'orderby'			=> 'rand',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'metaslider-slider' )
				)
			)
		);

		$sliders = get_posts( $args );
		$sliders = array_map(function($x) {
			return [
				'post_title' => $x->post_title,
				'post_content' => $x->post_content,
			];
		}, $sliders);

		restore_current_blog();

		switch_to_blog( 5 );

		$args = array(
			'post_type' 		=> 'sliders',
			'posts_per_page' 	=> 3,
			'orderby'        	=> 'rand',
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'mls_name',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->mls_name )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				),
				array(
					'taxonomy'	=> 'slider_type',
					'field'    	=> 'slug',
					'terms'    	=> array('metaslider')
				),
			)
		);

		if( $this->sub_section ) {

			$sub_args = array(
				'taxonomy'	=> 'sub_section',
				'field'    	=> 'slug',
				'terms'    	=> array( $this->sub_section )
			);

			$args['tax_query'] = array_merge( $args['tax_query'], $sub_args );

		}

		$slides = get_posts( $args );
		$slides = array_map(function($x) {
			return [
				'post_title' => $x->post_title,
			];
		}, $slides);

		restore_current_blog();

		return [
			'sliders' => $sliders,
			'slides' => $slides,
		];

	}

	public function get_backgrounds() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> -1,
			'orderby'			=> 'titles',
			'order'				=> 'ASC',
			'tax_query' 		=> array(
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'background_image' )
				)
			)
		);

		$posts = get_posts( $args );

		$backgrounds = array();
		$background_key = $this->get_background_key();
		$i = 1;

		foreach( $posts as $the_post ) {

			$key = str_replace( '{i}', $i, $background_key );

			$image = get_post_meta( $the_post->ID, 'page_' . $this->photo_key, true );

			$backgrounds[$key] = $image ? $image : '';

			$i++;

		}

		restore_current_blog();

		return $backgrounds;

	}

	public function get_background_key() {

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'background_key' )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		$post = $posts[0];

		return $post->post_title;

	}

	public function get_broker_logo() {
		return empty( $this->dev['broker_logo'] ) ? 'https://ifoundadmin.com/wp-content/uploads/broker-logos/broker-logo.png' : $this->dev['broker_logo'];
	}

	public function get_responsive_menu_settings() {

		switch_to_blog( 2 );

		$args = array(
			'post_type' 		=> 'content',
			'posts_per_page' 	=> 1,
			'tax_query' 		=> array(
				'relation' 		=> 'AND',
				array(
					'taxonomy'	=> 'content_type',
					'field'    	=> 'slug',
					'terms'    	=> array( 'responsive_menu_settings' )
				),
				array(
					'taxonomy'	=> 'stylesheet',
					'field'    	=> 'slug',
					'terms'    	=> array( $this->website['stylesheet'] )
				)
			)
		);

		$posts = get_posts( $args );

		$rows = explode( PHP_EOL, $posts[0]->post_content );

		restore_current_blog();

		return empty( $rows ) ? false : $rows;

	}

	private function headers() {

		// FIXME: Fix the Authentication token
		return array(
			'timeout' => 2400,
			'headers' => array(
				'Content-Type'		=> 'application/json',
				'Accept' 			=> 'application/json',
				'Referer' 			=> site_url(),
				'Authentication'	=> 'xxPQm654PFyrDJ09'
			)
		);

	}

	private function api_url( $endpoint ) {
		$terms = get_the_terms($this->client_id, 'multi_site');
		$build_origin = null;
		if ($terms) {
			$build_origin = $terms[0]->name;
		}
		if (!$build_origin) {
			$build_origin = $this->get_config()['build_origin'];
		}
		return join( '/', array(
			$build_origin,
			'wp-json',
			'ifound-builder',
			'1.0.0',
			$endpoint
		));
	}

	private function request( $endpoint, $body ) {

		$url = $this->api_url( $endpoint );
		$body = json_encode($body);
		$more = array_merge( $this->headers(), array( 'body' => $body ) );
		$this->log('Posting to ' . $url);
		$response = wp_remote_post($url, $more);
		if (is_array($response) && isset($response['response']) && $response['response']['code']) {
			$this->log('Received response code: ' . $response['response']['code']);
		} else {
			$this->log('Did not receive a response code');
		}
		if (is_wp_error($response)) {
			$this->log('class-build.php request error', $response);
		} else if (isset($response['response']) && $response['response']['code'] >= 400) {
			$body = json_decode($response['body'], true);
			$message = $body['message'];
			throw new \Exception($message);
		}

		return json_decode( wp_remote_retrieve_body( $response ), true );

	}

	private function process_response( $response ) {

		$website = get_post_meta( $this->client_id, 'website', true ) ?: array();
		update_post_meta( $this->client_id, 'website', array_merge( $website, $response ) );

	}

	/**


		Below this line are misc function that might be replaced with content.




	*/

	public function get_agent_name() {
		return $this->agent_name;
	}

	public function get_agent_info() {
		return $this->client_info;
	}

	public function contact_info() {

		ob_start(); ?>

		<div class="footer-contact">
		<div class="ifound-wrap">
		<div class="footer-contact-name"><b><? echo $this->agent_name; ?></b></div>
		<div class="footer-contact-phone"><a href="tel:<? echo $this->client_info['phone']; ?>"><? echo $this->client_info['phone']; ?></a></div>
		<div class="footer-contact-email"><a href="mailto:<? echo $this->client_info['email']; ?>"><? echo $this->client_info['email']; ?></a></div>
		<div class="footer-contact-broker"><? echo $this->client_info['broker']; ?></div><?

		if( $this->mls_name == 'crmls' ) { ?>

			<div class="footer-contact-calbre">CalBRE#<? echo $this->client_info['state_id']; ?></div><?

		} ?>

		</div>
		</div><?

		return ob_get_clean();

	}

	public function header_phone() {

		ob_start(); ?>

		<div class="header-contact"><div class="ifound-wrap"><a href="tel:<?= $this->client_info['phone'] ?>" class="phone"><?= $this->client_info['phone'] ?></a></div></div><?

		return ob_get_clean();

	}

	public function home_worth_slug() {
		return 'whats-my-home-worth';
	}

	public function call_or_text() {

		ob_start(); ?>

		<div class="call-or-text-wrapper">

			<div class="ifound-wrap">

				<div class="call-or-text-phone">Call or Text Me at <a href="tel:<? echo $this->client_info['phone']; ?>"><? echo $this->client_info['phone']; ?></a></div>

				<div class="call-or-text-contact"><a class="button taglinebutton" href="/contact/">Contact Me</a></div>

			</div>

		</div><?

		return ob_get_clean();

	}

	public function my_account_link(){
		return '<a class="account" href="/client-profile/">My Account</a>';
	}

	public function home_worth_tagline() {

		ob_start(); ?>

		<div class="home-worth-tagline-wrapper">
			<div class="ifound-wrap">
				<h1 class="tagline">Do you know what your home is worth?<a href="/<? echo $this->home_worth_slug(); ?>/" class="button alignright">Find Out <span class="dashicons dashicons-admin-network"></span></a></h1>
			</div>
		</div><?

		return ob_get_clean();

	}

	public function find_home() {

		ob_start(); ?>

		<div class="find-home">

			<a href="#findhome">Find a Home</a>

		</div>

		<div class="home-worth">

			<a href="/<? echo $this->home_worth_slug(); ?>/">What's My Home's Value?</a>

		</div><?

		return ob_get_clean();

	}

}
