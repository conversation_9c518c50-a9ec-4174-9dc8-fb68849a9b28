@charset "UTF-8";
/* CSS Document */


/* Shapes Map */

.color-button{
	width: 18px !important;
	height: 18px !important;
}

.ifound-shapes-map{
	width: 100%;
	height: 600px;
}

.color-button {
  	width: 14px;
   	height: 14px;
  	font-size: 0;
	margin: 2px;
   	float: right;
 	cursor: pointer;
}

.map-and-palette-wrapper .legend {
	margin-top: 1em;
	display: flex;
	float: right;
	clear: right;
}

.map-and-palette-wrapper .legend .item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: 1em;
}

.subject-info {
	background: #f5f5f5;
	margin-bottom: 40px;
	padding: 10px 20px;
	border: 1px #ddd solid;
}

.subject-info .radius-hint {
	font-style: italic;
	margin-bottom: 10px;
}

.subject-info .label {
	display: inline-block;
	width: 40%;
    white-space: nowrap;
}

.advanced-section-1 .radio-section,
.advanced-section-1 .checkbox-section,
.wp-admin .radio-section,
.wp-admin .checkbox-section,
.advanced-section-2,
.advanced-section-3{
	width: 49%;
	margin: 0 auto;
	padding: 2px;
	border: thin solid #ccc;
	text-align: center;
	float: left;
}

.ifound-advanced-body .select-wrap{
	width: 50%;
	float: left;
	padding: 0 20px 0;
}

.advanced-button-wrapper {
	display: inline-block;
	margin-bottom: 20px;
}

.advanced-button-wrapper .modify-button,
.advanced-button-wrapper.active .advanced-button{
	display: none;
}

.advanced-button-wrapper.active .modify-button,
.advanced-button-wrapper .advanced-button{
	display:block;
}

.ifound-dynamic-form-wrapper {
	margin-top: 20px;
}

.ifound-dynamic-form-wrapper h3.dynamic-heading {
	background: #f5f5f5;
	margin-bottom: 5px;
	padding: 10px 20px;
	width: 100%;
}

.ifound-dynamic-form-wrapper h3.dynamic-heading:after {
	content: " ";
}

.additional-criteria {
	margin-top: 5px;
}

.advanced-criteria-section {
	margin-bottom: 20px;
	display: none;
}

.advanced-criteria-section .button {
	display: inline-block;
	margin-bottom: 10px;
}

.advanced-criteria-section-heading{
	display: none;
	padding: 10px 5px;
	cursor: pointer;
}

.button-wrapper{
	padding: 4px 0;
}

.budget-body{
	padding: 10px;
}

.budget-add-button-wrapper {
	margin-top: 10px;
}

.criteria-body,
.budget-body {
	display: none;
}

.criteria-body {
	padding-bottom: 5px;
}

.criteria-body.active,
.budget-body.active {
	display: block;
}

.button-wrapper .save-this-wrapper,
.show-map-button-wrapper {
	display: inline-block;
}
@media only screen and (max-width: 750px) {

	.show-map-button-wrapper {
		margin-top: 20px;
	}

}

@media all and (max-width: 480px) {
	
	.advanced-criteria-section .button {
		text-align: center;
		width: 100%;
	}
	
}
