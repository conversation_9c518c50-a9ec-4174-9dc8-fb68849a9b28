(function() {
	var o = {
		modules: {},
		apps: {
			shortcode_creator_stats: {},
			owned_listings: {},
			radius: {},
			drip_templates: {},
			bulk_campaign_automator: {},
			multibox_quicksearch: {},
			multibox_advanced: {},
			campaign_status_toggle: {},
			broker_compensation: {},
			intro_title: {},
		},
		render_app: function(app_name, dom_container, props) {
			// React won't be available if the classic editor plugin is activated. So we must grab it from where we've
			// stashed it.
			var React = o.modules.React;
			var ReactDOM = o.modules.ReactDOM;
			var component = o.apps[app_name].component;
			ReactDOM.render(React.createElement(component, props), dom_container);
		},
	};
	// spa stands for Single Page App
	window.ifound_spa = o;
})();
