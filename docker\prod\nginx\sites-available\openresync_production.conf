upstream openresync {
    server **********:4000;
}

server {
    listen 443 ssl http2;
    server_name openresync.ifoundagent.com;

    access_log off;
    error_log /var/log/nginx/openresync-error.log;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ssl_certificate /etc/letsencrypt/live/ifoundagent.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ifoundagent.com/privkey.pem;

    include /etc/nginx/snippets/authelia-location.conf;

    location / {
        include /etc/nginx/snippets/proxy.conf;
        include /etc/nginx/snippets/authelia-authrequest.conf;

        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://openresync;

        proxy_pass_request_headers on;
    }
}
