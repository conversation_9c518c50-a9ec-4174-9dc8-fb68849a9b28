{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "noEmit": true, "isolatedModules": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "dist", "rootDir": "src"}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist"]}