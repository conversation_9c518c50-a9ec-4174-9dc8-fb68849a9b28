<?
/**
 * iFoundSeo Class
 *
 * @since 4.1.0
 */

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

class iFoundSeo {

	private $type;
	private	$listing;
	private	$settings;

	/**
	 * init iFoundSeo class.
	 *
	 * @since 4.1.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 4.1.0
	 */

	public function __construct() {

		add_action( 'init', array( $this, 'settings' ) );
		add_action( 'ifound_seo', array( $this, 'seo' ), 10, 2 );
		add_action( 'wp_footer', array( $this, 'structured_data_business' ), 99 );
		add_action( 'ifound_page_h1', array( $this, 'page_h1' ), 10, 1 );
		add_action( 'ifound_seo_h1', array( $this, 'seo_h1' ) );

		add_filter( 'ifound_results_h2', array( $this, 'results_h2' ), 10, 2 );
		add_filter( 'ifound_detail_url', array( $this, 'detail_url' ), 10, 2 );
		add_filter( 'ifound_get_detail_url', array( $this, 'get_detail_url' ), 10, 2 );
		add_filter( 'ifound_address', array( $this, 'address' ), 10, 2 );

	}

	public function settings() {
		$this->settings = get_option( 'ifound_seo_settings' );
	}

	/**
	 * SEO
	 *
	 * @since 1.0.0
	 * @since 2.5.8 Consolidate do_seo().
	 *
	 * @param object $results The API response results.
	 */

	public function seo( $type, $listing ) {
		$this->type 	= $type;
		$this->listing 	= $listing;

		$this->remove_other_seo();

		add_action( 'wp_head', array( $this, 'meta_title' ), 1 );
		add_action( 'wp_head', array( $this, 'meta_desc' ), 1 );
		add_action( 'wp_head', array( $this, 'canonical' ), 1 );
		// This is just a reminder to self about how this would work for base Wordpress.
		// add_filter('pre_get_document_title', [$this, 'pre_get_document_title'], 10, 1);
	}

	// public function pre_get_document_title($title) {
	// 	return $this->get_seo_title();
	// }

	private function get_seo_title() {
		$template = $this->settings[$this->type . '_meta_title'];
		$title = $this->filter( $template );
		return $title;
	}

	private function get_seo_description() {
		$template = $this->settings[$this->type . '_meta_description'];
		$desc = $this->filter( $template );
		return $desc;
	}

	public function remove_other_seo() {

		// Genesis's SEO is outputting a meta description with the site's tag, which is too generic for our IDX
		// pages. The canonical link is just wrong, so remove it too.
		if ( function_exists( 'genesis_disable_seo' ) ) {
			genesis_disable_seo();
		}

		// Disable Yoast. We do our own SEO stuff for our IDX pages.
		// I got this from https://developer.yoast.com/customization/yoast-seo/disabling-yoast-seo/
		$yoastClassName = 'Yoast\WP\SEO\Integrations\Front_End_Integration';
		if (class_exists($yoastClassName)) {
			$front_end = YoastSEO()->classes->get($yoastClassName);
			remove_action('wpseo_head', [$front_end, 'present_head'], -9999);
		}

		// Disable All In One SEO. We do our own SEO stuff for our IDX pages.
		// I got this from https://aioseo.com/docs/aioseo_disable/
		add_filter('aioseo_disable', '__return_true');

		// We output some head meta tags ourselves, so disable WP's built-in functionality to do that.
		remove_action( 'wp_head', '_wp_render_title_tag', 1 );
	}

	/**
	 * Meta Title
	 *
	 * @since 1.0.0
	 *
	 * @return string $meta_title The meta title fot the current page.
	 */

	public function meta_title() {
		$title = $this->get_seo_title();
		?>
		<!-- iFound SEO -->
		<title><? _e( $title, 'ifound' ); ?></title>
		<?
	}

	/**
	 * Meta Desc
	 *
	 * @since 1.0.0
	 *
	 * @return string $meta_desc The meta desc fot the current page.
	 */

	public function meta_desc() {
		$desc = $this->get_seo_description();
		?>
		<meta name="description" content="<? echo substr( $desc, 0, 160 ); ?>" />
		<?
	}

	/**
	 * Canonical
	 *
	 * @since 1.0.0
	 */

	public function canonical(){ ?>
		<link rel="canonical" href="<? echo iFound::current_url(); ?>" />
		<!-- End iFound SEO -->
		<?
	}

	public function seo_h1() {

		$template = $this->settings[$this->type . '_h1'];

		$h1 = $this->filter( $template );

		_e( $h1, 'ifound' );

	}

	public function results_h2( $content, $listing ) {

		$this->listing = $listing;

		$template = $this->settings['results_h2'];

		$h2 = $this->filter( $template );

		return str_replace( '{results_h2}', $h2, $content );

	}

	/**
	 * Detail URL
	 *
	 * The URL to a PDP page.
	 *
	 * @since 1.0.0
	 */

	public function detail_url( $content, $listing ) {

		$this->listing = $listing;

		$template = str_replace( '-', ' ', $this->settings['detail_url'] );

		$template = $this->filter( $template );

		$template = empty( $template ) ? 'property' : $template;

		$mls_id = $this->filter( '/{ListingID}/' );

		$url = site_url( '/listing-details/' . sanitize_title( $template ) . $mls_id );

		return str_replace( '{DetailsURL}', $url, $content );

	}

	public function get_detail_url( $address, $mls_id ) {
		return site_url( '/listing-details/' . sanitize_title( $address ) . '/' . $mls_id . '/' );
	}

	public function address( $listing ) {

		$template = $this->settings['address'];

		return $this->filter( $template, $listing );

	}

	public function filter( $template, $listing = null ) {

		global $mls_associations;

		$field_list = $mls_associations->field_list->res;

		foreach ( $field_list as $key => $value ) {

			$value = isset($listing)
				? $listing->$key
				: (isset( $this->listing->$key )
					? $this->listing->$key
					: '');

			$value = $this->field_cleaner( $key, $value );

			$template = str_replace( '{' . $key . '}', $value, $template );

		}

		// Remove double whitespace.
		$template = preg_replace( '/\s+/', ' ', $template );

		return $template;

	}

	public function field_cleaner( $key, $value ) {

		switch ( $key ) {

			case 'StreetDirPrefix':
				$value = strtoupper( $value );
				break;

			case 'StreetName':
				$value = ucwords( $value );
				break;

			case 'StreetSuffix':
				$value = ucwords( $value );
				break;

			case 'City':
				$value = ucwords( $value );
				break;

			case 'State':
				$value = strtoupper( $value );
				break;

		}

		return trim( $value );

	}

	/**
	 * Page H1
	 *
	 * Get the H1 for an IDX page.
	 *
	 * @since 1.0.0
	 *
	 * @param  object $results The API response results.
	 * @return string $h1      The H1 fot the current page.
	 */

	public function page_h1( $results ) {

		if( isset( $results->save_this_id ) && $results->save_this_id !== 0 && defined( 'DOING_SAVED_SEARCH' ) ) {
			$h1 = get_the_title( $results->save_this_id );
			$contact_id = get_post_meta($results->save_this_id, 'contact_id', true);
			$h1 = iFoundMergeTags::new_hookless()->replace_content($h1, ['contact_id' => $contact_id]);
		} elseif( defined( 'DOING_ADVANCED' ) ) {
			$h1 = 'Advanced Search';
		} elseif( isset( $results->input_obj['polygons'] ) && defined( 'DOING_POLYGONS' ) ) {
			foreach( $results->input_obj['polygons'] as $polygon )
				$title = $polygon->title;
			$h1 = $title . ' Homes for Sale';
		} else {
			$h1 = 'Real Estate Website';
		}

		_e( $h1, 'ifound' );

	}

	/**
	 * Structured Data for Business
	 *
	 * Provide a json object with agent data for search engines.
	 *
	 * @since 1.2.1
	 */

	public function structured_data_business() {

		if( $agent = get_option( 'ifound_agent' ) ) {

			$social = get_option( 'ifound_sociaL_media_networks' );

			$profiles = [];
			if( ! empty( $social['networks'] ) ) {

				foreach( $social['networks'] as $network )
					$profiles[] = $network['url'];

			}

			$data = array(
			  	'@context' 		=> 'http://schema.org',
			  	'@type'			=> 'LocalBusiness',
			  	'address' 		=> array(
					'@type'				=> 'PostalAddress',
					'addressLocality'	=> $agent['city'],
					'addressRegion'		=> $agent['state'],
					'streetAddress'		=> $agent['street_address'],
					'postalCode'		=> $agent['zip']
			  	),
			  	'description' 			=> get_option( 'blogdescription' ),
			  	'name'					=> ! empty( $agent['team_name'] ) ? $agent['team_name'] : $agent['agent_name'],
			  	'telephone'				=> $agent['mobile_phone'],
			  	'faxNumber'				=> $agent['fax'],
			  	'email'					=> $agent['email'],
				'url'					=> site_url(),
			  	'logo'					=> $agent['business_logo'],
			  	'photo'					=> $agent['business_photo'],
			  	'image'					=> $agent['business_photo'],
			  	'priceRange'			=> $agent['price_range'],
			  	'sameAs' 				=> $profiles
			); ?>

			<!-- Local Business Rich Card -->
			<script type="application/ld+json">
				<? echo json_encode( array_filter( $data ) ); ?>
			</script><?

		}

	}

}
