<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
* iFoundBlogdog class
*
* The methods in this class may not be the exact methods from the blogdog plugin.
* Rather, these are methods used for convertion to the iFound plugin. They may all be removed at some point. 
*
* @since 1.0.8
*/

class iFoundBlogdog extends iFoundIdx {
	
	/**
	 * init iFoundBlogdog class.
	 *
	 * @since 1.0.8
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}
	
	public function __construct(){
		add_shortcode( 'rebd_listings', array( $this, 'blogdog_to_ifound' ) );
	}
	
	public function blogdog_to_ifound( $atts ) {
		
		$input = shortcode_atts(array(
			'ptype' 	=> '',
			'city' 		=> '',
			'zip' 		=> '',
			'bed' 		=> '',
			'bath'		=> '',
			'min' 		=> '',
			'max' 		=> '',
			'sub'		=> '',
			'commf' 	=> '',
			'propf' 	=> '',
			'status'	=> '',
			'green'		=> false,
			'solar'		=> false
		), $atts, 'rebd_listings');
			
		$type = array(
			1 => 'Single Family - Detached',
			2 => 'Apartment Style',
			3 => 'Patio Home',
			4 => 'Townhouse',
			5 => 'Loft Style'
		);
		
		$p = array(
			'prop_type' 			=> empty( $type[$atts['ptype']] ) 	? false : $type[$atts['ptype']],
			'city' 					=> empty( $atts['city'] ) 			? false : $atts['city'],
			'zip' 					=> empty( $atts['zip'] ) 			? false : $atts['zip'],
			'subdivision'			=> empty( $atts['sub'] ) 			? false : $atts['sub'],
			'energy_green_feature'	=> empty( $atts['solar'] ) 			? false : $atts['solar'],
			'green_energy_cert'		=> empty( $atts['green'] ) 			? false : $atts['green'],
			'list_status'			=> $atts['status'] == 'sold' 		? array(  'Closed' ) 			: false,
			'comm_features' 		=> $atts['commf'] == 'gated' 		? array( 'Gated Community' ) 	: false,
			'comm_features' 		=> $atts['commf'] == 'golf'  		? array( 'Golf Course' )    	: false,
			'comm_features' 		=> $atts['commf'] == 'cpool' 		? array( 'Community Pool' )  	: false,
			'horses' 				=> $atts['propf'] == 'horse' 		? array( 'Y' )				 	: false,
			'pool_private' 			=> $atts['propf'] == 'ppool' 		? array( 'Pool - Private' )		: false,
			'garage_spaces' 		=> $atts['propf'] == '2car'  		? array( 'min' => 2 )			: false,
			'garage_spaces' 		=> $atts['propf'] == '3car'  		? array( 'min' => 3 )			: false,
			'living_sqft'			=> empty( $atts['sqft'] )			? false : array( 'min' => $atts['sqft'] ),
			'bedrooms' 				=> empty( $atts['bed'] )			? false : array( 'min' => $atts['bed'] ),
			'bathrooms'				=> empty( $atts['bath'] )			? false : array( 'min' => $atts['bath'] ),
			'list_price' 			=> empty( $atts['min'] )			? false : array( 'min' => $atts['min'] ),
			'list_price' 			=> empty( $atts['max'] )			? false : array( 'max' => $atts['max'] )
		);
			
		$x['query'] = array_filter( $p );
		
		$meta_id = add_post_meta( get_the_ID(), 'save_this_shortcode', $x );
		
		$shortcode = '[ifound id=' . $meta_id . ']';
	
		$this->blogdog_to_ifound_replace( $shortcode );
		
		return do_shortcode( $shortcode );
		
	}
	
	public function blogdog_to_ifound_replace( $shortcode ) {
		
		global $post;
		$find = 'rebd_listings';
		
		$pattern = get_shortcode_regex();
		if ( 
			preg_match_all( '/'. $pattern .'/s', $post->post_content, $matches )
        	&& 
			array_key_exists( 2, $matches )
        	&& 
			in_array( $find, $matches[2] ) 
		) {
        	foreach( $matches[0] as $match ) {
				
				if( strpos( $match, $find ) !== false ) {
					
					$new_content = str_replace( $match, $shortcode, $post->post_content );
					
					$new_post = array(
      					'ID'           => get_the_ID(),
      					'post_content' => $new_content
 					 );
					
					wp_update_post( $new_post );
					
					/** Break in case there is more tnan 1 shortcode. */
					break;
					
				}	
				
			}
    	}
	}
	
}
