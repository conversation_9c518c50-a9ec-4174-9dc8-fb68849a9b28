version: '3'
services:
    nginx:
        image: nginx:1.19.10-alpine
        restart: unless-stopped
        hostname: nginx
        ports:
            - "80:80"
            - "443:443"
        depends_on:
            - newidx
            - solr
        volumes:
            - ./nginx/sites-available:/etc/nginx/sites-available
            - ../nginx/nginx.conf:/etc/nginx/nginx.conf
            - ../nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
            - ../nginx/global:/etc/nginx/global
            - ./data/certbot/conf:/etc/letsencrypt
            - ./data/certbot/www:/var/www/certbot
            - ../nginx/logs:/var/log/nginx
            - ./nginx/snippets/:/etc/nginx/snippets/
    # How to use certbot with nginx and docker:
    # https://pentacent.medium.com/nginx-and-lets-encrypt-with-docker-in-less-than-5-minutes-b4b8a60d3a71
    certbot:
        image: certbot/certbot
        volumes:
            - ./data/certbot/conf:/etc/letsencrypt
            - ./data/certbot/www:/var/www/certbot
        profiles:
            - certbot
        # See https://ifoundagent.teamwork.com/#/tasks/22665231?c=8076474
        # Since I plan to do things manually, I'll just leave this entrypoint permanently.
        entrypoint: "/usr/bin/tail -f /dev/null"
    idx_db:
        image: mariadb:10.3.28-focal
        restart: unless-stopped
        hostname: idx_db
        ports:
            - "33066:3306"
        volumes:
            - ./idx_db/config.cnf:/etc/mysql/conf.d/config.cnf
            - ../../volumes/idx_db:/var/lib/mysql
            - ~/tmp/:/var/backups
        # environment:
        #     # Just showing these as a reminder of what might be needed/useful when standing up a server.
        #     MYSQL_ROOT_PASSWORD:
        #     MYSQL_DATABASE:
        #     MYSQL_USER:
        #     MYSQL_PASSWORD:
    solr:
        build: ../solr
        restart: unless-stopped
        hostname: solr
        volumes:
            - ../../volumes/solr:/var/lib/solr/dist/server/solr
            - ../../solr/configs:/var/lib/solr/configs
            - ../../solr/configs/solr.xml:/var/lib/solr/dist/server/solr/solr.xml
            - ../solr/resources/log4j.properties:/var/lib/solr/dist/server/resources/log4j.properties
            - ../solr/etc/jetty.xml:/var/lib/solr/dist/server/etc/jetty.xml
        command: "/bin/solr start -f -a -Xmx12288m"
    newidx:
        build:
            context: ../newidx/
            dockerfile: Dockerfile
        restart: unless-stopped
        hostname: newidx
        depends_on:
            - idx_db
            - solr
        expose:
            - 8155
        volumes:
            - ../../server:/www
        user: node
        environment:
            NODE_ENV: production
    railsadmin:
        build: ../rails_admin
        image: ifoundagent:railsadmin
        restart: unless-stopped
        hostname: railsadmin
        depends_on:
            - idx_db
            - redis
        volumes:
            - ../../idx2:/www
            - ../../volumes/rubygems:/var/lib/gems
            - ../../volumes/binstubs:/usr/local/bin
        environment:
            RAILS_ENV: production
            REDIS_URL: redis://redis/0
        # This command can be useful when standing up a server, to allow the server to "run idle" as long as you need
        # while you docker-compose exec into it to set things up.
        # command: tail -f /dev/null
    sidekiq:
        build: ../rails_admin
        image: ifoundagent:railsadmin
        restart: unless-stopped
        depends_on:
            - redis
        volumes:
            - ../../idx2:/www
            - ../../volumes/rubygems:/var/lib/gems
            - ../../volumes/binstubs:/usr/local/bin
        environment:
            RAILS_ENV: production
            REDIS_URL: redis://redis/0
        command: /usr/local/bin/bundle exec sidekiq
    idx:
        build: ../php/idx
        image: ifoundagent:idx-bullseye
        restart: unless-stopped
        hostname: idx
        depends_on:
            - idx_db
        volumes:
            - ../../idx:/www
            - ../../history-tracker/data/:/www/history_tracker_data/
            - ../../volumes/idx_vendor:/www/vendor
            - ../../volumes/composer_cache:/var/www/.composer/cache/
            - ../php/idx/config/solr.ini:/etc/php/8.1/cli/conf.d/20-solr.ini
            - ../php/idx/config/solr.ini:/etc/php/8.1/fpm/conf.d/20-solr.ini
            - ../php/idx/config/fpm/pool.d/www.conf:/etc/php/8.1/fpm/pool.d/www.conf
            - ../php/idx/config/php.ini:/etc/php/8.1/cli/conf.d/php-ifoundagent-idx.ini
            - ../php/idx/config/php.ini:/etc/php/8.1/fpm/conf.d/php-ifoundagent-idx.ini
            - ../user/.bashrc:/root/.bashrc
            - ../user/.bash_aliases:/root/.bash_aliases
            - ../user/.bash_aliases:/home/<USER>/.bash_aliases
    redis:
        image: redis:5.0.4-alpine
        restart: unless-stopped
        hostname: redis
        volumes:
            - ../../volumes/redis/:/var/redis/
    authelia:
        image: authelia/authelia:4.37.5
        volumes:
            - ./authelia/config/:/config/
            - ./authelia/secrets/:/secrets/
        expose:
            - 9091
        user: "1000:1000"
        # See: https://www.authelia.com/integration/deployment/docker/#using-a-secrets-volume
        environment:
            AUTHELIA_JWT_SECRET_FILE: /secrets/JWT_SECRET
            AUTHELIA_SESSION_SECRET_FILE: /secrets/SESSION_SECRET
            AUTHELIA_STORAGE_ENCRYPTION_KEY_FILE: /secrets/STORAGE_ENCRYPTION_KEY
