<?php

namespace Profound\MLS;

require_once 'vendor/autoload.php';
require_once __DIR__ . '/../TestHelper.php';

use \PHPUnit_Framework_TestCase;
use \Profound\TestHelper;

class TrendmlsImageHandlerTest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testgetImagePathsList() {
		$factory = Mls::create('trendmls', array('db' => TestHelper::getDb()));
		$image_handler = $factory->getImageHandler();
		// Obviously this test is dependent on my current database's values.
		$list = $image_handler->getImagePathsList("70082855219");
		$item = array(
			'normal_url' => "http://csmedia.mris.com/platinum/getmedia?ID=70082855264&LOOT=50038672093",
			"thumbnail_url" => "http://images.prd.mris.com/image/V1/1/70b93caf36261565ccf2f5db3e545f3aa826c890844a7c294b282b767460e0ae1c6b595fbffa5e2666a6a2994da6870a9fdaaf81efe96d71de2ef9615e71c2b4273cecd89f3f4ee35d294aaa48fc84ae.jpg",
			"highres_url" => null,
			"description" => ""
		);
		$this->assertEquals($item, $list[0]);
	}
}