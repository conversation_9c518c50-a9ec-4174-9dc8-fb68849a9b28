explain SELECT
				t1.EasyName, t1.Interpretation, plo.ShortValue
			FROM
				(
					SELECT DISTINCT
						fm.EasyName, ft.Interpretation, ft.LookupName
					FROM
						armls_fields_A ft
						JOIN field_mapping fm ON fm.armls <> '' AND fm.EasyName <> '' AND fm.armls = ft.SystemName
				) t1
				JOIN property_lookup_opts plo USING (LookupName)
			ORDER BY
				EasyName, ShortValue;
explain SELECT
				t1.EasyName, t1.Interpretation, plo.ShortValue
			FROM
				(
					SELECT DISTINCT
						fm.EasyName, ft.Interpretation, ft.LookupName
					FROM
						armls_fields_A ft
						JOIN field_mapping fm ON fm.armls <> '' AND fm.EasyName <> '' AND fm.armls = ft.SystemName AND fm.mls_class = 'res'
				) t1
				JOIN property_lookup_opts plo ON plo.mls = 'armls' AND plo.LookupName = t1.LookupName
			ORDER BY
				EasyName, ShortValue;

show indexes from armls_fields_A;
show indexes from field_mapping;
show indexes from property_lookup_opts;

select * from armls_fields_A limit 10;
select * from armls_fields_B limit 10;
select * from armls_fields_C limit 10;
select * from glvarnv_fields_1 limit 10;
select * from glvarnv_fields_9 limit 10;
select * from armls_fields_A where SystemName = 'LIST_1' limit 10;
select * from property_lookup_opts limit 10;
select mls from property_lookup_opts group by mls;
select count(*) from armls_fields_A;
select count(*) from armls_fields_B;
select count(*) from paaraz_fields_A;
select count(*) from tarmlsaz_fields_A;
select count(*) from glvarnv_fields_1;
select distinct fields_enterdate, fields_status, LookupName, SystemName, StandardName, ShortName, DBName, Interpretation from armls_fields_A;
select distinct fields_enterdate, fields_status, LookupName, SystemName, StandardName, ShortName, DBName, Interpretation from armls_fields_A where SystemName = 'LIST_1';
select count(distinct mls, LookupName, MetadataEntryID, Value, ShortValue, LongValue) from property_lookup_opts;
SHOW TABLE STATUS WHERE Name = 'armls_fields_A';

-- show session variables like 'old_alter_table';
-- set session old_alter_table=1;
alter ignore table armls_fields_A add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table armls_fields_B add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table armls_fields_C add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table glvarnv_fields_1 add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table glvarnv_fields_9 add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table paaraz_fields_A add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table paaraz_fields_B add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table paaraz_fields_F add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table tarmlsaz_fields_A add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table tarmlsaz_fields_B add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);
alter ignore table tarmlsaz_fields_E add unique (fields_enterdate, fields_status, LookupName, SystemName, StandardName(100), ShortName(100), DBName(100), Interpretation);

alter ignore table property_lookup_opts add unique (mls, LookupName, MetadataEntryID(100), Value(100), ShortValue(100), LongValue(150));
-- set session old_alter_table=0;

select count(*) from property_lookup_opts;
select count(*) from property_lookup_opts where mls = 'armls';
select count(*) from property_lookup_opts where mls = 'trendmls';
select count(*) from property_lookup_opts where mls = 'sdcrca';
select max(length(MetadataEntryID)) from property_lookup_opts;
select max(length(Value)) from property_lookup_opts;
select max(length(ShortValue)) from property_lookup_opts;
select max(length(LongValue)) from property_lookup_opts;

create index case809 on armls_fields_A (Interpretation, LookupName);
create index case809 on property_lookup_opts (mls, LookupName);

explain SELECT DISTINCT
						fm.EasyName, ft.Interpretation, ft.LookupName
					FROM
						armls_fields_A ft
						JOIN field_mapping fm ON fm.armls <> '' AND fm.EasyName <> '' AND fm.armls = ft.SystemName;

select `field_243`, `field_244` from glvarnv_property_1 limit 10;

select * from access where mls = 'glvarnv';
select * from access_meta am join access a using (access_id) where mls = 'glvarnv';

explain SELECT
				t1.EasyName, t1.Interpretation, plo.ShortValue
			FROM
				(
					SELECT DISTINCT
						fm.EasyName, ft.Interpretation, ft.LookupName
					FROM
						glvarnv_fields_1 ft
						JOIN field_mapping fm ON fm.glvarnv <> '' AND fm.EasyName <> '' AND replace(fm.glvarnv, 'field_', '') = ft.SystemName
				) t1
				JOIN property_lookup_opts plo USING (LookupName)
			ORDER BY
				EasyName, ShortValue;
