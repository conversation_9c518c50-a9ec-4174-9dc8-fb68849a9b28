jQuery( document ).ready( function( $ ) {
	// TODO: All this pagination stuff assumes there is only one result set on the page. I believe that is always the
	// case for pages that use this code. But if we ever merge this code with the code that handles pagination
	// for shortcodes (results-map.js) where there can technically be multiple result sets on one page, we'll need
	// to not make that assumption.

	function makeUrlWithResultsPageNum(pageNum) {
		var urlParams = new URLSearchParams(window.location.search);
		urlParams.set('results_page_num', pageNum);
		var loc = window.location;
		var url = loc.origin + loc.pathname + '?' + urlParams;
		return url;
	}

	history.replaceState({
		pfmls: true,
		eventName: 'pagination',
		pageNum: (typeof input_obj !== 'undefined' && input_obj[0].pp) || 1,
	}, '', window.location.href);

	function updateResults(pageNum, doPushState) {
		changeMapButton( 'Show' );
		$( '.map-button' ).unbind(); // ?????
		input_obj[input_obj.length -1].pp = pageNum;
		var input = $.param( input_obj[input_obj.length -1] );
		// Hide the map on the next page.
		$( '.results-map' ).css( 'display', 'none' );
		$( '.map-and-palette-wrapper' ).css( 'display', 'none' );

		var url = makeUrlWithResultsPageNum(pageNum);
		if (doPushState) {
			history.pushState({
				pfmls: true,
				eventName: 'pagination',
				pageNum: pageNum,
			}, '', url);
		}

		if(typeof ifound_map !== 'undefined' && ifound_map.advanced){
			$.getResults(input);
		} else {
			getResults(input);
		}
	}

	window.addEventListener('popstate', function(event) {
		if (event.state && event.state.pfmls) {
			if (event.state.eventName === 'pagination') {
				var doPushState = false;
				updateResults(event.state.pageNum, false);
			}
		}
	});

	$( document ).on( 'click', '.next-page', function() {
		var pageNum = parseInt($( this ).attr( 'new_page' ), 10);
		updateResults(pageNum, true);
	});

	function getResults(input) {

		$('.while-we-wait').addClass('active');

		if(ifound_paging.has_map){
			$.clearMarkers();
		}

		$('html, body').animate({scrollTop: 0}, 1600);

		var url = ifound_paging.site_url + ifound_paging.endpoint + '?' + input;

		$.getJSON( url, function( data ) {
			var pageLoaded = false;

			$( '.map-button' ).click(function() {
				if( !pageLoaded ) {
					drawMap(data);
					pageLoaded = true;
				} else displayMap();
			});

			$('.replace-results-here').html(data.body).fadeIn('slow');
			$('.while-we-wait').removeClass('active');

		});

	}

	function displayMap() {
		let map;
		if( $( '.results-map' ).length > 0 ) map = $( '.results-map' );
		else map = $( '.map-and-palette-wrapper' );

		if( $( map ).css( 'display' ) != 'none' ) {
			$( map ).toggle( false );
			changeMapButton( 'Show' );
		} else {
			if( $( '#draw-map-body > div' ).hasClass( 'map-and-palette-wrapper' ) ) {
				$( map ).remove().css( 'display', 'block' ).insertAfter( '.button-wrapper' );
			} else {
				$( map ).toggle();
				changeMapButton( 'Hide' );
			}
		}
	}

	function changeMapButton( actionString ) {
		let icon = $( '.map-button > i' ).prop( 'outerHTML' );
		$( '.map-button' ).html( icon + ' ' + actionString + ' Map' );
	}

	function drawMap(data) {
		displayMap();
		if( ifound_paging.has_map) {
			p = data.map_data.pins;
			$.setMarkers(false, p);
		}
	}
});
