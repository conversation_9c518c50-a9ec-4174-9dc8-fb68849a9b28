import './shared';

import './shared-styles/index.scss';

import ShortcodeCreatorStats from './components/ShortcodeCreatorStats';
import OwnedListings from './components/OwnedListings';
import Radius from './components/Radius';
import DripTemplates from './components/DripTemplates';
import BulkCampaignAutomator from './components/crm/BulkCampaignAutomator';
import MultiboxQuickSearch from './components/MultiboxQuickSearch';
import MultiboxAdvanced from './components/MultiboxAdvanced';
import CampaignStatusToggle from './components/CampaignStatusToggle';
import BrokerCompensation from './components/BrokerCompensation';
import IntroTitle from './components/IntroTitle';
import provideToast from './hocs/provideToast';

window.ifound_spa.apps.shortcode_creator_stats.component = ShortcodeCreatorStats;
window.ifound_spa.apps.owned_listings.component = provideToast(OwnedListings);
window.ifound_spa.apps.radius.component = Radius;
window.ifound_spa.apps.drip_templates.component = provideToast(DripTemplates);
window.ifound_spa.apps.bulk_campaign_automator.component = provideToast(BulkCampaignAutomator);
window.ifound_spa.apps.multibox_quicksearch.component = MultiboxQuickSearch;
window.ifound_spa.apps.multibox_advanced.component = MultiboxAdvanced;
window.ifound_spa.apps.broker_compensation.component = provideToast(BrokerCompensation);
window.ifound_spa.apps.campaign_status_toggle.component = provideToast(CampaignStatusToggle);
window.ifound_spa.apps.intro_title.component = provideToast(IntroTitle);
