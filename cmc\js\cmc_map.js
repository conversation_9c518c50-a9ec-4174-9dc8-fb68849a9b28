jQuery( document ).ready( function( $ ) {
	
	$('.open').on('click touchstart', function(e) {
		$(this).text($(this).text() == 'Close' ? 'More' : 'Close');
		$(this).parents('.listing-wrap').children('.open-box').slideToggle('slow');
	});
					
	function ZoomControl(controlDiv, map) {
	  
		controlDiv.style.padding = '15px';
				
		var controlWrapper = document.createElement('div');
		controlWrapper.style.cursor = 'pointer';
		controlWrapper.style.textAlign = 'center';
		controlWrapper.style.width = '50px'; 
		controlWrapper.style.height = '108px';
		controlDiv.appendChild(controlWrapper);
				  
		var zoomInButton = document.createElement('div');
		zoomInButton.style.width = '50px'; 
		zoomInButton.style.height = '50px';
		zoomInButton.style.border = "thin solid #000000";
		zoomInButton.style.backgroundColor = 'black';
		zoomInButton.style.borderRadius = "7px";
		zoomInButton.style.marginBottom = "8px";
		zoomInButton.style.boxShadow = "0 .188em .625em #494949";
		zoomInButton.style.backgroundImage = 'url("' + cmc_map.image_url + 'plus.png")';
		controlWrapper.appendChild(zoomInButton);
					
		var zoomOutButton = document.createElement('div');
		zoomOutButton.style.width = '50px'; 
		zoomOutButton.style.height = '50px';
		zoomOutButton.style.backgroundColor = 'black';
		zoomOutButton.style.border = "thin solid #000000";
		zoomOutButton.style.borderRadius = "7px";
		zoomOutButton.style.padding = "4px";
		zoomOutButton.style.boxShadow = "0 .188em .625em #494949";
		zoomOutButton.style.backgroundImage = 'url("' + cmc_map.image_url + 'minus.png")';
		controlWrapper.appendChild(zoomOutButton);
				
		google.maps.event.addDomListener(zoomInButton, 'click', function() {
			map.setZoom(map.getZoom() + 1);
		});	
					
		google.maps.event.addDomListener(zoomOutButton, 'click', function() {
			map.setZoom(map.getZoom() - 1);
		 });  
					
	}
			 
	var areas = {
		area_1: {
		 	radius: 804,
		 	color: 'green'
		},
		area_2: {
			radius: 1609, /** 1 mile = 1609.34 meters */
		 	color: 'yellow'
		},
		area_3: {
		 	radius: 2414,
		 	color: 'orange'
		},
		area_4: {
			radius: 3218,
			color: 'red'
		}
	};

 	function initMap() {
		
		var styles = [
			{ featureType: "all",
				stylers: [
					{ saturation: -80 }
				]
			},{ featureType: "road.arterial",
				elementType: "geometry",
				stylers: [
					{ hue: "#ff9009" },
					{ saturation: 20 }
				]
			}
		];
				
 		// Create the map.
 		var map = new google.maps.Map(document.getElementById('cmc-map'), {
			zoom: 14,
			center: { lat: cmc_map.map_data[0][1], lng: cmc_map.map_data[0][2] },
			scrollwheel: false,
			styles: styles,
			disableDefaultUI: true
 		});
		
		
		// Construct the circle for each value in citymap.
		// Note: We scale the area of the circle based on the population.
		for (var area in areas) {
			// Add the circle for this city to the map.
			var areaCircle = new google.maps.Circle({
				strokeColor: areas[area].color,
				strokeOpacity: 0.8,
				strokeWeight: 4,
				fillOpacity: 0,
				map: map,
				center: { lat: cmc_map.map_data[0][1], lng: cmc_map.map_data[0][2] },
				radius: areas[area].radius
			});
		}
	
		if(screen.width > 800){
			var zoomControlDiv = document.createElement('div');
			var zoomControl = new ZoomControl(zoomControlDiv, map);
	
			zoomControlDiv.index = 1;
			map.controls[google.maps.ControlPosition.RIGHT_CENTER].push(zoomControlDiv);
		}
					
		google.maps.event.addListener(map, 'zoom_changed', function() {
			if(document.contains(document.getElementById('zoom'))){
				var zoom = map.getZoom();
				document.getElementById('zoom').value = zoom;
			}
		});
				
		setMarkers(map);
		
		map.fitBounds(areaCircle.getBounds());
 	}
		
		
	function setMarkers(map) {
	  
		var image = {
			subject : cmc_map.image_url + 'subject.png', 
			active : cmc_map.image_url + 'active.png',
			closed : cmc_map.image_url + 'closed.png'
		};
		
		z = 100;
		
        for (var i = 0; i < cmc_map.map_data.length; i++) {
          	var property = cmc_map.map_data[i];
         	var marker = new google.maps.Marker({
            	position: {lat: property[1], lng: property[2]},
            	map: map,
            	icon: image[property[3]],
            	title: property[0],
            	zIndex: z
          	});
			z++;
			
			attachLink(marker, property[4]);
        }
		
		function attachLink( marker, anchorID ){
			marker.addListener( 'click', function() {
				$( '.highlight' ).removeClass( 'highlight' );
    			$('html, body').animate({
        			scrollTop: $( '#' + anchorID ).offset().top - ( $( window ).height() - $( '#' + anchorID ).outerHeight( true ) ) / 2
    			}, 2000);
				$( '#' + anchorID ).addClass( 'highlight' );
  			});
		}
	}
	
	$.getScript( cmc_map.url + cmc_map.key )
		.done(function( script, textStatus ) {
			initMap();
	});
	
});