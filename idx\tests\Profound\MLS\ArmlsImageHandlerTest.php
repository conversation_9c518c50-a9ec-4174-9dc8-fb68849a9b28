<?php

namespace Profound\MLS;

require_once 'vendor/autoload.php';
require_once __DIR__ . '/../TestHelper.php';

use \PHPUnit_Framework_TestCase;
use \Profound\TestHelper;

class ArmlsImageHandlerTest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testgetImagePathsList() {
		$factory = Mls::create('armls', array('db' => TestHelper::getDb()));
		$image_handler = $factory->getImageHandler();
		// Obviously this test is dependent on my current database's values.
		$list = $image_handler->getImagePathsList("20130305182127896238000000");
		$item = array(
			'normal_url' => "http://cdn.photos.flexmls.com/az/20130305191512589363000000.jpg",
			"thumbnail_url" => "http://cdn.photos.flexmls.com/az/20130305191512589363000000-t.jpg",
			"highres_url" => "http://cdn.photos.flexmls.com/az/20130305191512589363000000-o.jpg",
			"description" => "IMG_3080"
		);
		$this->assertEquals($item, $list[0]);
	}
}