<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

/**
 * iFoundPrivateContact Class
 *
 * @since 3.0.0
 */

class iFoundPrivateContact extends iFoundCrm{
    public static $the_post_type  = 'private_contact';

	protected $post_type;
	private $query_var   = 'private-contact';
	private $taxonomy    = 'contacts_status';
	protected $taxonomys = ['contacts_status'];


	/**
	 * init iFoundPrivateContact class.
	 *
	 * @since 3.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 3.0.0
	 */

	public function __construct() {
	    $this->post_type = static::$the_post_type;

		add_action( 'init', array( $this, 'private_contacts_post_type' ) );

		// Reminder: map_meta_cap, at the least, prevents other team members from editing others' private contacts.
		add_filter( 'map_meta_cap', array( $this, 'map_meta_cap' ), 10, 4 );
		// I don't know what these do so I don't think they are needed. Delete these lines and the functions later.
		// add_filter( 'parse_query', array( $this, 'sort_query' ) );
		// add_action( 'pre_get_posts', array( $this, 'sortable_columns_query'), 10, 1 );

		add_action( 'restrict_manage_posts', array( $this, 'filter_dropdown' ), 2 );
		add_action( 'pre_get_posts', array( $this, 'private_contacts_query'), 10, 1 );

		add_action('add_meta_boxes_private_contact', [iFoundContactsAdmin::new_hookless(), 'contacts_meta_box']);

		// If the user is an administrator (not super admin), we will not let them edit a private
		// contact. This is only needed in multisite.
		// Note: I'm thinking there's got to be a better way than using this filter, but I can't figure it out. I tried
		// setting the administrator's role's capability of edit_others_private_contacts explicitly to false (I also
		// tried 'do_not_allow' and null), but that didn't work. I'd like to see how the user's 'allcaps' are computed
		// in wordpress core's class-wp-user, in get_role_caps(), but my debugger is borked right now and is showing me
		// integers for $this->allcaps' keys instead of strings. But if capability's value is null/false, in the
		// has_cap() method, it should return false.
		if (is_multisite()) {
			add_filter('user_has_cap', [$this, 'user_has_cap'], 10, 4);
		}
		add_filter('default_hidden_columns', [$this, 'default_hidden_columns'], 10, 2);
	}

	public function private_contacts_post_type() {

		register_post_type( 'private_contact',
			array(
				'labels' => array(
					'name' 			=> __( 'Contacts' ),
					'singular_name' => __( 'Private Contact' ),
					'add_new'		=> __( 'Add New Private Contact' ),
					'add_new_item'	=> __( 'Add New Private Contact' ),
					'edit_item'		=> __( 'Edit Private Contact' ),
					'new_item'		=> __( 'New Private Contact' ),
					'view_item'		=> __( 'View Private Contact' ),
					'view_items'	=> __( 'View Private Contacts' ),
					'search_items'	=> __( 'Search Private Contacts' ),
					'all_items'		=> __( 'Contacts' ),
					'attributes'	=> __( 'Private Contacts Attributes' ),
					'menu_name'		=> __( 'iFound CRM' ),
					'not_found'     => __( 'No Private Contacts found' ),
				),
				'query_var'				=> 'private-contact',
				'show_ui'				=> true,
				'show_in_menu'			=> iFoundTeams::new_hookless()->user_has_team_member_role(),
				'menu_position'			=> 3,
				'public' 				=> false,
				'has_archive' 			=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'capability_type'		=> 'private_contact',
				'capabilities' 			=> array(
					'edit_post'          	=> 'edit_private_contact',
					'read_post'          	=> 'read_private_contact',
					'delete_post'        	=> 'delete_private_contact',
					'edit_posts'         	=> 'edit_private_contacts',
					'edit_others_posts'  	=> 'edit_others_private_contacts',
					'delete_posts'        	=> 'delete_private_contacts',
					'publish_posts'      	=> 'publish_private_contacts',
					'create_posts'       	=> 'create_private_contacts'
				)
			)
	  	);

	}

	public function private_contacts_query( $query ) {

		if ( ! is_admin() || $this->support() )	return;

		global $typenow;

		if ( $typenow === static::$the_post_type && ! isset( $_GET['page'] ) && ! isset( $_GET['post'] ) ) {
			// Setting the post_type to an array seems to work most of the time, but there are a few spots in WP that
			// don't seem to expect an array. One spot that I just traced through is from this message:
			// PHP Warning:  Illegal offset type in /www/wp-admin/edit.php on line 412
			// Because that code (from WP 5.5) deals with "bulk messages" and it's just a warning, I think the best
			// thing to do is ignore it.
			// Update: it was a warning in PHP 7. I think in PHP it's now a fatal warning so this is more important. The
			// only situation that's affected me so far is deleting private contacts. Ugh. Actually, this prevents the
			// fatal warning, but when the (Private) Contacts list page loads, it will not show (non-private) contacts,
			// which could be confusing. The inelegant workaround is to refresh that page.
			if (strpos($this->current_url(), 'deleted=1') === false) {
				$query->set( 'post_type', array( iFoundContacts::$the_post_type, static::$the_post_type ) );
			}
			$query->set( 'author', get_current_user_id() );
		}
	}

	public function user_has_cap($allcaps, $caps, $args, $user) {
		if (count($caps) > 0
			&& $caps[0] === 'edit_others_private_contacts'
			&& in_array('administrator', $user->roles, true)
		) {
			$allcaps['edit_others_private_contacts'] = false;
		}
		return $allcaps;
	}

	// public function sortable_columns_query( $query ) {
	//
  	// 	if ( ! is_admin() )	return;
	//
  	// 	$orderby = $query->get( 'orderby');
	//
  	// 	$options = array(
  	// 		'title'			=> 'fname',
	// 		'lname'			=> 'lname'
	// 	);
	//
  	// 	if ( array_key_exists( $orderby, $options ) ) {
    // 		$query->set( 'meta_key', $options[$orderby] );
    // 		$query->set( 'orderby', 'meta_value' );
  	// 	}
	//
	// }
	//
	// /**
	//  *	Sort Query
	//  *
	//  *	Get the item selected in the dropdown.
	//  *
	//  *	@since 3.0.0
	//  */
	//
	// public function sort_query( $query ) {
	//
	// 	global $pagenow;
	//
	// 	$q_vars    = &$query->query_vars;
	//
	// 	if (
	// 		$pagenow == 'edit.php'
	// 		&&
	// 		isset( $q_vars['post_type'] )
	// 		&&
	// 		$q_vars['post_type'] == $this->post_type
	// 		&&
	// 		isset( $q_vars[ $this->taxonomy ] )
	// 		&&
	// 		is_numeric( $q_vars[ $this->taxonomy ] )
	// 		&&
	// 		$q_vars[ $this->taxonomy ] != 0 )
	// 	{
	// 		$term = get_term_by( 'id', $q_vars[ $this->taxonomy ], $this->taxonomy );
	// 		$q_vars[ $this->taxonomy ] = $term->slug;
	// 	}
	//
	// }

	// Hide these columns by default.
	public function default_hidden_columns($hidden, $screen) {
		if ($screen->id === 'edit-' . static::$the_post_type) {
			return ['mls_contact', 'wiseagent'];
		}
		return $hidden;
	}
}
