<?php

namespace Profound;

// This class is responsible for testing conveniences.

require_once 'Zend/Db/Adapter/Pdo/Mysql.php';

class TestHelper {
	static $config;

	static function init() {
		$CONFIGFILE = __DIR__ . "/../config.ini";

		self::$config = parse_ini_file($CONFIGFILE, true);
	}

	public static function getDb() {
		return new \Zend_Db_Adapter_Pdo_Mysql(self::$config['database']);
	}
}

TestHelper::init();
