jQuery(document).ready(function($) {
	// Even though we show multiple checkboxes, the only value we actually submit in the form is a single input
	// (separate from the checkboxes) named to_email. This function is responsible for taking the multiple checkboxes
	// and updating the single field.
	function toggleEmailInEmailTo(emailAddress, shouldAdd) {
		var $to_email = $('#to_email');
		var emails = [];
		if ($to_email.val() && $to_email.val() !== '') {
			emails = $to_email.val().split(',');
		}
		if (shouldAdd) {
			emails.push(emailAddress);
		} else {
			emails = emails.filter(function (x) {
				return x !== emailAddress;
			});
		}
		var new_emails = emails.join(',');
		$to_email.val(new_emails);
	}

	function outputEmailCheckbox(emailAddress, emailDisplay, id, isChecked, appendToEl) {
		var checkedString = isChecked ? 'checked="checked"' : '';
		var div = $(`<div>
				<label for="${id}" class="email-validate">
					<input class="email-validate" type="checkbox" ${checkedString} value="${emailAddress}" id="${id}">
					${emailDisplay}
				</label>
			</div>`);
		div.find('input').on('change', function() {
			// Are any checkboxes checked?
			if (!appendToEl.find('input').is(':checked')) {
				$('.no_email_addresses_checked_warning').show();
				$(this).prop('checked', true);
				return;
			}
			$('.no_email_addresses_checked_warning').hide();
			toggleEmailInEmailTo($(this).val(), $(this).is(':checked'));
		});
		appendToEl.append(div);
	}

	function populateEmailsTo(emails_map, options) {
		options = options || {};

		var $to_email = $('#to_email');
		$to_email.val('');
		var $to_emails = $('.to_emails')
		$to_emails.children().remove();
		if (emails_map === null) {
			$('.no_contact_selected_warning').show();
			return;
		}

		$('.no_contact_selected_warning').hide();
		var selected_emails = options && options.selected_emails;
		if (selected_emails) {
			// Update: Now I just think the '0' was a bug. I'm also seeing empty string, which we should handle the
			// same.
			// There is a weird situation where campaigns (in the past?) would set the
			// to_email field to "0". Presumably this was intentional and meant "use the contact's main email address".
			// But that means it would show up here as "0" as well, which is confusing to see. So remove it and
			// replace with the main email address.
			if (selected_emails.length === 1 && ['', '0'].includes(selected_emails[0])) {
				selected_emails = [emails_map.email];
			}
			$to_email.val(selected_emails.join(','));
			// Show a checkbox for all emails on the campaign but not currently listed on the contact.
			// This scenario would happen if the contact's email address(es) were updated after the campaign was
			// created, back before we updated all their campaigns when an email address was changed.
			selected_emails.forEach(function(selected_email, index) {
				if (!Object.values(emails_map).includes(selected_email)) {
					outputEmailCheckbox(selected_email, selected_email, 'selected_email_index_' + index, true, $to_emails);
				}
			});
		} else {
			// Use main email address.
			$to_email.val(emails_map.email);
		}
		for (var key in emails_map) {
			var emailAddress = emails_map[key];
			if (!emailAddress) {
				continue;
			}
			var emailDisplay = emailAddress + (key.endsWith('_spouse') ? ' (Spouse)' : '');
			var isChecked = key === 'email';
			if (selected_emails) {
				isChecked = selected_emails.includes(emailAddress);
			}
			var id = 'email_checkbox_' + key;
			outputEmailCheckbox(emailAddress, emailDisplay, id, isChecked, $to_emails);
		}
	}

	function unpopulateEmailsTo() {
		var $to_email = $('#to_email');
		$to_email.val('');
		var $to_emails = $('.to_emails')
		$to_emails.children().remove();
	}

	// Even though we show multiple checkboxes, the only value we actually submit in the form is a single input
	// (separate from the checkboxes) named to_sms. This function is responsible for taking the multiple checkboxes
	// and updating the single field.
	function toggleMphoneInMphoneTo(mphoneAddress, shouldAdd) {
		var $to_sms = $('#to_sms');
		var mphones = [];
		if ($to_sms.val() && $to_sms.val() !== '') {
			mphones = $to_sms.val().split(',');
		}
		if (shouldAdd) {
			mphones.push(mphoneAddress);
		} else {
			mphones = mphones.filter(function (x) {
				return x !== mphoneAddress;
			});
		}
		var new_mphones = mphones.join(',');
		$to_sms.val(new_mphones);
	}

	function outputToSmsCheckbox(mphoneNumber, mphoneDisplay, id, isChecked, appendToEl) {
		var checkedString = isChecked ? 'checked="checked"' : '';
		var div = $(`<div>
				<label for="${id}" class="mphone-validate">
					<input class="mphone-validate" type="checkbox" ${checkedString} value="${mphoneNumber}" id="${id}">
					${mphoneDisplay}
				</label>
			</div>`);
		div.find('input').on('change', function () {
			// Are any checkboxes checked?
			if (!appendToEl.find('input').is(':checked')) {
				$('.no_to_sms_checked_warning').show();
				$(this).prop('checked', true);
				return;
			}
			$('.no_to_sms_checked_warning').hide();
			toggleMphoneInMphoneTo($(this).val(), $(this).is(':checked'));
		});
		appendToEl.append(div);
	}

	function populateToSmss(mphones_map, options) {
		options = options || {};

		var $to_sms = $('#to_sms');
		$to_sms.val('');
		var $to_smss = $('.to_smss')
		$to_smss.children().remove();

		if (!mphones_map['mphone']) {
			$('.no_mphone_warning').show();
			return;
		}

		$('.no_mphone_warning').hide();
		var selected_mphones = options && options.selected_mphones;
		// If the campaign was created with do_sms=0, and the contact had no mphone, but now does, we want to_sms to
		// become that phone number.
		if (selected_mphones && selected_mphones.length) {
			$to_sms.val(selected_mphones.join(','));
		} else {
			// Use main mobile phone
			$to_sms.val(mphones_map.mphone);
		}
		var mphones_map_keys = Object.keys(mphones_map);
		for (var i = 0; i < mphones_map_keys.length; i++) {
			var key = mphones_map_keys[i];
			var mphoneNumber = mphones_map[key];
			if (!mphoneNumber) {
				continue;
			}
			var formattedMphone = formatPhoneNumber(mphoneNumber);
			var mphoneDisplay = formattedMphone + (key.endsWith('_spouse') ? ' (Spouse)' : '');
			var isChecked = key === 'mphone';
			if (selected_mphones) {
				isChecked = selected_mphones.includes(mphoneNumber);
			}
			if (i === 0 && (!selected_mphones || !selected_mphones.length)) {
				// We always need at least one checked.
				isChecked = true;
			}
			var id = 'mphone_checkbox_' + key;
			outputToSmsCheckbox(mphoneNumber, mphoneDisplay, id, isChecked, $to_smss);
		}
		if (!$('#do_sms').is(':checked')) {
			$('.mphone-validate').addClass('ifound-feel-disabled');
		}
	}

	function unpopulateToSmss() {
		var $to_sms = $('#to_sms');
		$to_sms.val('');
		var $to_smss = $('.to_smss')
		$to_smss.children().remove();
	}


	// I got this originally from: https://stackoverflow.com/a/8358141/135101
	function formatPhoneNumber(phoneNumberString) {
		var cleaned = ('' + phoneNumberString).replace(/\D/g, '');
		var match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
		if (match) {
			return '(' + match[1] + ') ' + match[2] + '-' + match[3];
		}
		return phoneNumberString;
	}

	function addConfirmationToDeleteLinks() {
		// This is for the links on the "Edit" page for the posts.
		$('.submitdelete').on('click', function() {
			return window.confirm('Are you sure you wish to delete this?');
		});

		// This is for the bulk action "Apply" button when listing posts.
		$('#doaction').on('click', function() {
			var $bulk_action_select = $('#bulk-action-selector-top');
			if ($bulk_action_select.val() === 'delete') {
				var $checkboxes = $("input[type=checkbox][name='post[]']:checked");
				if ($checkboxes.length) {
					var str = $checkboxes.length === 1 ? 'this' : 'these';
					return window.confirm('Are you sure you wish to delete ' + str + '?');
				}
			}
			return true;
		});
	}
	addConfirmationToDeleteLinks();

	function handleMayText(vals) {
		var partialMessage = null;
		var link = null;
		if (!vals.hasPreviousRelationship) {
			partialMessage = 'you have not set their contact status with Previous Relationship';
			link = vals.contactLink;
		} else if (!vals.hasAtLeastOneMobilePhoneNumber) {
			partialMessage = 'they do not have at least one mobile phone number';
			link = vals.contactLink;
		} else if (!vals.hasAgentSetUpIntroTitle) {
			partialMessage = 'you have not set up your Intro Title in iFound CRM Settings';
			link = vals.crmSettingsLink;
		} else if (vals.hasOptedOut) {
			partialMessage = 'the contact has opted out of text messages';
		}
		if (partialMessage) {
			var aHrefLink = `<a target="_blank" href="${link}">this link</a>`;
			var message = `You may not text this contact because ${partialMessage}`;
			if (link) {
				message += `. You may fix it with ${aHrefLink}. Then reload this page.`;
			}
			var $mayNotTextMessage = $('.may_not_text_message');
			$mayNotTextMessage.html(message);
			$('#do_sms').prop('checked', false);
			$('#do_sms').addClass('ifound-feel-disabled');
			$('label[for="do_sms"]').addClass('ifound-feel-disabled');
		} else {
			$('#do_sms').removeClass('ifound-feel-disabled');
			$('label[for="do_sms"]').removeClass('ifound-feel-disabled');
		}
	}

	function checkUncheckAll($buttonSelector, $checkboxesSelector) {
		$buttonSelector.on('click', () => {
			var shouldCheck = !!$buttonSelector.data('shouldCheck');
			$checkboxesSelector.prop('checked', shouldCheck);
			$buttonSelector.data('shouldCheck', !shouldCheck);
		});
	}

	// Prefix with 'ifound' to provide a global namespace.
	window.ifound_populateEmailsTo = populateEmailsTo;
	window.ifound_unpopulateEmailsTo = unpopulateEmailsTo;
	window.ifound_populateToSmss = populateToSmss;
	window.ifound_unpopulateToSmss = unpopulateToSmss;
	window.ifound_handleMayText = handleMayText;
	window.ifound_checkUncheckAll = checkUncheckAll;
});
