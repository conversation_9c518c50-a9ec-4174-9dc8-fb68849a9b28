/* Admin Styles */

.bg-green{
	background: rgb(34, 163, 47) !important;
	border-color: #ccc !important;
	box-shadow: none !important;
}

.bg-red{
	background: rgb(224, 0, 0) !important;
	border-color: #ccc !important;
	box-shadow: none !important;
}

.bg-green:hover{
	background: rgb(27, 128, 37) !important;
}

.bg-red:hover{
	background: rgb(128, 42, 64) !important;
}

#ifound-log-wrapper{
	max-height: 150px;
	overflow: scroll;
}

.log-text{
	font-size: 16px;
	color: #000;
}

.new-log .log-text, 
.new-log .log-text-label{
	font-size: 20px;
	color: #3a5795;
}

.new-log li{
	border: thin solid #3a5795;
	border-radius: 7px;
	padding: 2px;
}

.new-log li:before{
	font-family: Font Awesome\ 5 Pro;
	content: "\f058";
	font-size: 20px;
	color: #25A102;
	font-weight: 300;
	padding: 0 14px;
}

.broker-logo{
	background: #ccc;
}

.page-images-table tr{
	border-bottom: thin solid #ccc;
}

.page-images-table h3{
	border-bottom: font-size: 1.7em;
}

.page-image-title{
	background: #f6f6f6;
}

.clone-parent-page-img,
.clone-parent-featured-img{
	width:300px;
	height:auto;
	border:0;
	background:#ccc;
}

table.history {
	width: 100%;
	border: 1px silver solid;
	border-collapse: collapse;
}

table.history th, table.history td {
	border: 1px silver solid;
	padding: 10px;
}
