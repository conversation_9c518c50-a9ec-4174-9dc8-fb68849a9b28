@charset "UTF-8";
/* CSS Document */

#cmc .left{
	float:left;
}
			
#cmc .right{
	float:right;
}
			
#cmc .center{
	text-align:center;
}

#cmc .pad-top-10{
	padding-top:10px;
}

#cmc .pad-top-20{
	padding-top:20px;
}
			
#cmc .green-line{
	border-bottom:green medium solid;
}
			
#cmc .yellow-line{
	border-bottom:yellow medium solid;
}
			
#cmc .orange-line{
	border-bottom:orange medium solid;
}
			
#cmc .red-line{
	border-bottom:red medium solid;
}

#cmc .distance{
	width:100%;
	font-size: 13px !important;
	text-align:left;
}

#cmc .legend-heading{
	font-size:30px;
}

#cmc .text-14{
	font-size:14px;
}
			
#cmc-map {
	height: 820px;
}
			
#cmc .cmc{
	max-width:800px;
	margin: 0 auto;
	text-align:center;
}

#cmc .listing,
#cmc .subject{
	border:#ccc thin solid;
	padding:20px;
	margin:20px 0;
	max-width: 100%;
}

#cmc .listing-wrap,
#cmc .compare-wrap{
	border-bottom:#ccc thin solid;
}

#cmc .compare-wrap{
	margin-bottom:40px;
}

#cmc .cmc-third{
	width:33.33333333%;
	float:left;
}

#cmc .cmc-heading{
	width:35%;
	float:left;
	clear:left;
}

#cmc .cmc-value{
	width:65%;
	float:left;
	clear:right;
}

#cmc .miles{
	display:none;
}

#cmc .cmc-data{
	text-align:center;
}

#cmc .legend{
	margin:20px 0;
	border:#ccc thin solid;
	padding:20px;
}

#cmc .highlight{
	box-shadow: 0 .188em .625em #494949;
	border-radius: 4px;
	padding:2px 5px;
}

#cmc .close-date{
	margin-bottom:14px;
}

#cmc .error-message{
	min-height:500px;
	color:red;
	font-size:32px;
}

/* CMC Form */

#cmc-auto-input #address {
	border: 1px solid #000090;
 	background-color: #f0f0ff;
 	width: 480px;
	padding-right: 2px;
}

#cmc-auto-input #address td {
	font-size: 10pt;
}

#cmc-auto-input .field {
  	width: 99%;
}

#cmc-auto-input .slimField {
	width: 80px;
}

#cmc-auto-input .wideField {
 	width: 200px;
}
	  			
#cmc-auto-input input{
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 0;
	box-shadow: none;
	color: #000;
	font-size: 24px;
	font-weight: 200;
	padding: 12px;
	width:80%;
	float:left;
}
			
#cmc-auto-input .button-wrap{
	width:20%;
	float:left;
}
			
#cmc-auto-input button,
#cmc-auto-input .button{
	border-radius: 0;
	cursor: pointer;
	font-size: 24px;
	font-weight: 200;
	letter-spacing: normal;
	padding: 19px 0 !important;
	text-transform: uppercase;
	-webkit-font-smoothing: antialiased;
	white-space: normal;
	width:100%;
	box-shadow:none;
	margin-top: 0;
	border:none;
	margin-left: 0;
	margin-right: 0;
}

#cmc-auto-input .required{
	border:red thin solid;
	background:#F8DCDD;
}

#cmc-auto-input input{
	line-height: 1.5;
}

/* Pop */
#cmc .hide{
	display:none;
}

#cmc .open-box{
	width: 100%;
	padding:20px;
}

#cmc .open,
#cmc .pop-close{
	 cursor:pointer;
}

#cmc .pop-form {
	width: 100%;
	max-width: 960px;
	padding:30px;
	margin: 100px auto 0;
	background-color: transparent;
	opacity: 0;
	position: absolute;
	left: 50%;
  	transform: translate(-50%);
	display:none;
	z-index: 9999;
}

#cmc .pop-backdrop {
	background-color: rgba(0, 0, 0, 0.8);
    bottom: 0;
    cursor: default;
    left: 0;
    opacity: 0;
    position: fixed;
    right: 0;
    top: 0;
    display:none;
    z-index: 9999;
    -webkit-transition: opacity .5s;
    -moz-transition: opacity .5s;
    -ms-transition: opacity .5s;
    -o-transition: opacity .5s;
    transition: opacity .5s;
}

#cmc .pop-form.active,
#cmc .pop-backdrop.active {
    opacity: 1;
    display:block;
}

#cmc .cmc-map-container{
	padding:16px 5px 0 0;
}

#pop-map {
	height: 200px;
}

#cmc .pop-info{
	margin-top:30px;
}

#cmc .pop-close{
	display:none;
}

#cmc .pop-close.active{
	display:block;
	position:fixed;
	top:160px;
	right:60px;
	z-index:99999;
	color:#777;
}

/* Gravity Form */

#CMC_form {
	color:#fff;
}

#CMC_form .gfield_label {
	color:#fff;
	font-weight:300;
}

#CMC_form .name_first label,
#CMC_form .name_last label{
	display:none;
}

#CMC_form .gfield {
	width: 50%;
	float: left;
	clear:none;
}

#CMC_form .cmc_name{
	width:100%;
}

#CMC_form .gform_wrapper ul li.gfield,
#CMC_form .gform_wrapper .top_label div.ginput_container{
	margin-top: 1px;
}

#CMC_form .validation_error{
	display:none;
}

#CMC_form .gfield_error {
	background-color:transparent;
	margin-bottom: 0px !important;
	border-top:none;
	border-bottom: none;
	padding-bottom: 0px;
	padding-top: 0px;
}

#CMC_form li.gfield_error input:not([type=radio]):not([type=checkbox]):not([type=submit]):not([type=button]):not([type=image]):not([type=file]) {
	border: 1px solid red;
	background-color:#FFEEEE;
}
	

#CMC_form .validation_message {
	color: red;
	font-weight: 300;
	font-size: 12px;
	background: #f5f5f5;
	padding: 1px;
	display:none;
}

#CMC_form .field_sublabel_above .gfield_description {
	margin-top:0px;
}

#CMC_form .gform_wrapper li.gfield.gfield_error.gfield_contains_required label.gfield_label {
	margin-top: 2px;
}

#CMC_form input[type="text"] {
	height: 50px;
}

#CMC_form select {
	height: 40px;
}
	
#CMC_form .gform_wrapper .gfield_required{
	color: #fff;
}

#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=email], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=number], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=password], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=tel], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=text], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error input[type=url], 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error select, 
#CMC_form .gform_wrapper.gf_browser_chrome .gfield_error textarea, 
#CMC_form .gform_wrapper.gf_browser_chrome li.gfield_error.field_sublabel_above .ginput_complex input[type=text] {
   margin-bottom: 0;
}

#CMC_form .gform_wrapper li.gfield.email.gfield_error.gfield_contains_required {
   padding-right: 16px;
}

.cmc-hidden-address_wrapper{
	display:none;
}

/* CMC Widget */

.cmc-widget{
	background-color: rgba(0,0,0,.6);
	padding:17px 0 50px;
	max-width:800px;
	margin:0 auto;
}

.cmc-widget-heading{
	color:#fff;
	font-size:32px;
}

#cmc .cmc-mobile-more{
	display:none;
}

/* Responsive */

#cmc .five-sixths,
#cmc .four-sixths,
#cmc .one-fourth,
#cmc .one-half,
#cmc .one-sixth,
#cmc .one-third,
#cmc .three-fourths,
#cmc .three-sixths,
#cmc .two-fourths,
#cmc .two-sixths,
#cmc .two-thirds {
	float: left;
	margin-left: 2.564102564102564%;
}

#cmc .one-half,
#cmc .three-sixths,
#cmc .two-fourths {
	width: 48.717948717948715%;
}

#cmc .one-third,
#cmc .two-sixths {
	width: 31.623931623931625%;
}

#cmc .four-sixths,
#cmc .two-thirds {
	width: 65.81196581196582%;
}

#cmc .one-fourth {
	width: 23.076923076923077%;
}

#cmc .three-fourths {
	width: 74.35897435897436%;
}

#cmc .one-sixth {
	width: 14.52991452991453%;
}

#cmc .five-sixths {
	width: 82.90598290598291%;
}

#cmc .first {
	clear: both;
	margin-left: 0;
}

@media only screen and (max-width: 1023px){
	#cmc .cmc-headings{
		display:none;
	}
	
	#cmc .miles-total{
		float:left;
		padding-right:7px;
	}
	
	#cmc .miles{
		display:block;
		float:left;
	}
	
	#cmc .cmc-data{
		text-align:left;
	}
	
	#cmc-map {
		height: 420px;
	}
	
	#cmc .cmc-mobile-more{
		display:block;
	}
	
	#cmc .cmc-more{
		display:none;
	}
	
	#cmc .five-sixths,
	#cmc .four-sixths,
	#cmc .one-fourth,
	#cmc .one-half,
	#cmc .one-sixth,
	#cmc .one-third,
	#cmc .three-fourths,
	#cmc .three-sixths,
	#cmc .two-fourths,
	#cmc .two-sixths,
	#cmc .two-thirds {
		margin: 0;
		width: 100%;
	}
}

@media only screen and (max-width: 600px){
	#cmc-auto-input input{
		width:100%;
	}
				
	#cmc-auto-input .button-wrap{
		width:100%;
		float:none;
	}
	
	#CMC_form .gfield {
		width: 100%;
		float: none;
		clear:both;
	}
	
	#cmc .cmc-map-container{
		padding:16px 5px;
	}
	
	#cmc .legend-heading{
		font-size:24px;
	}
	
	#CMC_form .gform_wrapper li.gfield.email.gfield_error.gfield_contains_required {
   		padding-right:0;
	}
	
	#cmc-auto-input button,
	#cmc-auto-input .button{
		margin-top: 10px;
	}
	
	#cmc .pop-close.active{
		top:20px;
		right:20px;
	}
				
}

@media only screen and (max-width: 480px){
	#cmc .cmc-heading.ptype,
	#cmc .cmc-value.ptype{
		width:100%;
		float:none;
		clear:both;
	}
	
	#cmc .legend-heading{
		font-size:18px;
	}
	
	#cmc .pop-close.active{
		top:10px;
		right:10px;
	}
	
}