<?php
require_once("classes/dbroot.php");

class sql_query extends dbroot {



public function process_sql( $sql_strg ){

	$sql = $sql_strg;

	return $this->process('update', $sql);

} // End process_sql Function



public function get_process_sql( $sql_strg ){

	$sql = $sql_strg;

	return $this->process('get_assoc', $sql);

} // End get_process_sql Function




public function put_process_sql( $table, $sdl_fields_strg, $sdl_value_strg, $nextid=false ){

	$sql = "INSERT INTO `$table` ($sdl_fields_strg) VALUES ($sdl_value_strg);";

	if($nextid){
		$result_id = $this->connect->nextid($table);
		return $this->process('put', $sql, $result_id);
	}else{
		return $this->process('put', $sql);
	}

} // End put_process_sql Function




public function update_process_sql( $table, $sql_strg ){

	$sql = "UPDATE `$table` SET $sql_strg";

	return $this->process('update', $sql);

} // End update_process_sql Function




public function del_property($listing_id){

	$sql = "DELETE FROM `property` WHERE `LIST_1`='$listing_id'";

	return $this->process('delete', $sql);

} //  End delete property function




public function del_properties($ageddate){

	$sql = "DELETE FROM `property` WHERE `property_enterdate` <= '$ageddate' ";

	return $this->process('delete', $sql);

} // End delete properties function




public function del_prop_image($listing_id){

	$sql = "DELETE FROM `property_images` WHERE `Content-ID`='$listing_id'";

	return $this->process('delete', $sql);

} //  End delete property image function




public function del_prop_images($ageddate){

	$sql = "DELETE FROM `property_images` WHERE `image_enterdate` <= '$ageddate' ";

	return $this->process('delete', $sql);

} // End delete property images function




public function check_fields($table, $data_fields, $insert=true){

	$column_array = $this->process('get_assoc', "SHOW COLUMNS FROM `$table`");
	//print "<pre>"; print_r($column_array); print "</pre>";

	// Build the DB column array
	$db_fields = array();
	if(is_array($column_array)){
		foreach($column_array as $key=>$result){
			$db_fields[] = $key;
		}
	}

	// Compair the array and get the difference
	if(is_array($data_fields) && is_array($db_fields)){
		$missing = array_diff($data_fields, $db_fields);
	}

	// If there is a missing field, create it
	if($insert && is_array($missing)){
		foreach($missing as $key=>$result){
			//$this->process('update', "ALTER TABLE  `$table` ADD `$result` VARCHAR( 255 ) NOT NULL");
			$this->process('update', "ALTER TABLE  `$table` ADD `$result` TEXT NOT NULL");
		}
	}

} // End check_fields function




} //end class

?>