{"background": {"color": "#161616", "type": "single", "preset": "light-gray-gradient", "gradient1": "", "gradient2": ""}, "category": "4", "class": "", "client_section": [{"field": "client_name", "type": "text", "before": "", "class": "testimonial-name"}, {"field": "company_name", "type": "link", "before": "", "class": "testimonial-company", "url": "company_website", "link_text": "value", "link_text_custom": "", "new_tab": 1}], "column_count": "2", "container_class": "", "container_data": "", "content": "excerpt", "count": -1, "divi_builder": 0, "excerpt_length": 55, "font-color": {"type": "", "color": ""}, "form_ajax": 0, "form_id": "1", "gravatar": "no", "id": 0, "initials_bg_color": "#ffffff", "initials_font_color": "#000000", "initials_font_size": "42", "layout": "", "less_post": false, "less_post_text": "Show less", "lightbox": 0, "lightbox_class": "", "mode": "slideshow", "more_full_post": "0", "more_page": false, "more_page_hook": "wpmtst_view_footer", "more_page_id": 0, "more_page_text": "Read more testimonials", "more_post": 1, "more_post_ellipsis": "1", "more_post_in_place": "0", "more_post_text": "Read more", "note": "", "order": "newest", "page": "", "pagination": 0, "pagination_settings": {"type": "simple", "nav": "after", "per_page": 5, "show_all": false, "end_size": 1, "mid_size": 2, "prev_next": true, "prev_text": "&laquo; Previous", "next_text": "Next &raquo;", "before_page_number": "", "after_page_number": ""}, "slideshow_settings": {"adapt_height": 0, "adapt_height_speed": 0.5, "auto_hover": 1, "auto_start": 1, "breakpoints": {"desktop": {"width": 1200, "max_slides": 3, "move_slides": 1, "margin": 20}, "large": {"width": 1024, "max_slides": 2, "move_slides": 1, "margin": 20}, "medium": {"width": 640, "max_slides": 1, "move_slides": 1, "margin": 10}, "small": {"width": 480, "max_slides": 1, "move_slides": 1, "margin": 1}}, "continuous_sliding": 0, "controls_style": "buttons", "controls_type": "none", "effect": "horizontal", "nav_position": "outside", "pager_style": "buttons", "pager_type": "full", "pause": 5, "show_single": {"max_slides": 1, "move_slides": 1, "margin": 1}, "speed": 1.5, "stop_auto_on_click": 1, "stretch": 0, "type": "show_multiple"}, "template": "default", "template_settings": {"default": {"image_position": "left", "theme": "dark", "quotes": "on"}, "small-widget": {"image_position": "left"}, "default-form": {"theme": "light"}}, "thumbnail": 0, "thumbnail_height": 0, "thumbnail_size": "thumbnail", "thumbnail_width": 0, "title": "hidden", "title_link": "none", "use_default_length": "1", "use_default_more": "0", "view": "", "html_content": 0}