<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../traits/NewHooklessTrait.php');

class iFoundUpdateHelper {
	use UtilTrait;
	use NewHooklessTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('ifound_v5_45_4_update', [$this, 'v5_45_4_update']);
			add_action('ifound_v5_45_3_update', [$this, 'v5_45_3_update']);
			add_action('ifound_v5_45_2_update', [$this, 'v5_45_2_update']);
			add_action('ifound_v5_45_1_update', [$this, 'v5_45_1_update']);
			add_action('ifound_v5_40_0_update', [$this, 'v5_40_0_update']);
			add_action('ifound_v5_35_2_update', [$this, 'v5_35_1_update']);
			add_action('ifound_v5_35_1_update', [$this, 'v5_35_1_update']);
			add_action('ifound_v5_35_0_update', [$this, 'v5_35_0_update']);
			add_action('ifound_v5_35_0_update_for_user_id', [$this, 'v5_35_0_update_for_user_id']);
			add_action('ifound_v5_28_0_update', [$this, 'v5_28_0_update']);
			add_action('ifound_v5_28_0_update_for_team_member', [$this, 'v5_28_0_update_for_team_member'], 10, 1);
		}
	}

	// For all campaigns that have to_email postmeta, put do_email=yes.
	public function v5_45_4_update() {
		global $wpdb;

		$sql = <<<EOT
			SELECT pm.post_id
			FROM {$wpdb->postmeta} pm
			JOIN {$wpdb->posts} p ON pm.post_id = p.ID
			WHERE 1=1
			AND p.post_type = %s
			AND meta_key = 'to_email'
			AND meta_value is not null
			AND meta_value != ''
		EOT;
		$results = $wpdb->get_results($wpdb->prepare($sql, iFoundSaveThis::$the_post_type), ARRAY_A);
		foreach ($results as $result) {
			$campaign_id = $result['post_id'];
			$sql = <<<EOT
				INSERT INTO {$wpdb->postmeta} (post_id, meta_key, meta_value)
				values (%d, 'do_email', 'yes')
			EOT;
			$wpdb->query($wpdb->prepare($sql, $campaign_id));
		}
	}

	public function v5_45_3_update() {
		iFoundSms::new_hookless()->create_sms_messages_table();
	}

	public function v5_45_2_update() {
		iFoundSms::new_hookless()->create_sms_intros_table();
	}

	public function v5_45_1_update() {
		iFoundSms::new_hookless()->create_opt_ins_table();
	}

	// For ARMLS Spark sites, which have all been migrated from ARMLS, handle the fact that there is no field
	// specifically for contingent anymore.
	// Also, in ARMLS, for featured listings images, we'd often use JavaScript to replace .jpg with -o.jpg. We don't
	// need to do that with armls_spark.
	public function v5_40_0_update() {
		if (iFoundIdx::mls_name() !== 'armls_spark') {
			return;
		}

		// I've already handled this site for this setting.
		if ($this->util()->get_host() !== 'tempearizonahouses.com') {
			// Reminder: this didn't actually work due to a bug. See the change in the update code for 5.43.0.
			// $option_name = 'ifound_search_settings';
			// $search_settings = get_option($option_name);
			// $has_search_settings_change = false;
			// if ($search_settings['contingent']['UCB'] ?? null) {
			// 	$search_settings['list_status'][] = 'UCB';
			// 	$has_search_settings_change = true;
			// }
			// if ($search_settings['contingent']['CCBS'] ?? null) {
			// 	$search_settings['list_status'][] = 'CCBS';
			// 	$has_search_settings_change = true;
			// }
			// if ($has_search_settings_change) {
			// 	update_option($option_name, $search_settings);
			// }
		}

		$args = array(
			'role' => iFoundTeams::$role_name,
			'fields' => 'ID',
		);
		$users = get_users( $args );
		$crm_ids = $users;
		// Add the CRM ID of the primary admin, which is empty.
		array_unshift($crm_ids, '');
		foreach ($crm_ids as $crm_id) {
			$adv_search_option_names = ['ifound_adv_search_criteria', 'ifound_more_adv_search_criteria'];
			foreach ($adv_search_option_names as $option_name) {
				$opt_name_with_crm_id = $option_name . $crm_id;
				$opt = get_option($opt_name_with_crm_id);
				if (isset($opt['contingent'])) {
					unset($opt['contingent']);
					update_option($opt_name_with_crm_id, $opt);
				}
			}
		}

		$featured_settings = get_option('ifound_featured_settings');
		$original_value = $featured_settings['featured_slider_script'];
		$new_value = str_replace("this.src.replace('.jpg', '-o.jpg')", 'this.src', $original_value);
		$featured_settings['featured_slider_script'] = $new_value;
		update_option('ifound_featured_settings', $featured_settings);
		update_option('ifound_armls_rets_to_armls_spark',
			['replaced_in_featured_slider_script' => $new_value !== $original_value]
		);
	}

	// For all contacts who are unsubscribed, disable all their campaigns, again.
	public function v5_35_1_update() {
		$args = ['role__in' => [iFoundTeams::$role_name, 'administrator']];
		$users = get_users($args);
		foreach ($users as $user) {
			as_enqueue_async_action('ifound_v5_35_0_update_for_user_id', [[$user->ID]], 'ifound');
		}
	}

	// For all contacts who are unsubscribed, disable all their campaigns.
	public function v5_35_0_update() {
		// CAREFUL!: Note that $args is not instantiated. So be careful if you copy this code.
		$users = get_users($args);
		foreach ($users as $user) {
			as_enqueue_async_action('ifound_v5_35_0_update_for_user_id', [[$user->ID]], 'ifound');
		}
	}

	public function v5_35_0_update_for_user_id($user_id) {
		$contact_ids = iFoundJointContact::new_hookless()->get_all_contacts($user_id, true);
		foreach ($contact_ids as $contact_id) {
			$is_unsubscribed = has_term('unsubscribed', iFoundContacts::$the_taxonomy, $contact_id);
			if ($is_unsubscribed) {
				iFoundContacts::new_hookless()->disable_all_campaigns($contact_id);
			}
		}
	}

	// For the primary admin and all team members:
	// - Create an email template for Website visitor follow-up.
	// - Create the setting for sending a website visitor follow-up email, defaulted to off.
	public function v5_28_0_update() {
		// Do we need these?
		// iFoundEmail::new_hookless()->post_type();
		// iFoundEmail::new_hookless()->taxonomy();

		wp_insert_term(iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_LABEL, 'ifound_email_type',
			['slug' => iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_SLUG]);
		$this->add_website_visit_follow_up_email_template(iFoundCrm::$crm_settings_option_name, 1);

		$args = ['role' => iFoundTeams::$role_name];
		$users = get_users($args);
		foreach ($users as $user) {
			as_enqueue_async_action('ifound_v5_28_0_update_for_team_member', [[$user->ID]], 'ifound');
		}
	}

	private function add_website_visit_follow_up_email_template($option_name, $post_author) {
		$email_templates = iFoundEmail::new_hookless()->email_templates();
		$template = $this->util()->array_find($email_templates, function($x) {
			return $x['email_type'] === iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_SLUG;
		});
		$template_subset = $this->util()->pick($template, ['post_title', 'post_content', 'subject', 'email_type']);
		$post_id = iFoundEmail::new_hookless()->create_email_template($template_subset, $post_author);

		$crm_settings = get_option($option_name);
		$crm_settings[$template['default_key']] = $post_id;
		// Fix a bug where the 'default_key' had been set to 'false' instead of false.
		unset($crm_settings['false']);
		update_option($option_name, $crm_settings);
	}

	public function v5_28_0_update_for_team_member($args) {
		$user_id = $args[0];
		$option_name_for_user = iFoundCrm::$crm_settings_option_name . $user_id;
		$this->add_website_visit_follow_up_email_template($option_name_for_user, $user_id);
	}
}
