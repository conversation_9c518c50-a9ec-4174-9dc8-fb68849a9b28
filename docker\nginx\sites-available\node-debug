# This is meant for helping with remote debugging. I can't do port forwarding on Site District. But because my PhpStorm
# debugger only allows ports > 1024, and ngrok only allows ports 80 and 443 when tunneling HTTP traffic, I forward a
# local port (e.g. 39229) to the remote server. But, of course at that point, the HOST header coming from PhpStorm says
# localhost, and ngrok returns a 404. So I need a reverse proxy that can rewrite the HOST header to be the ngrok URL.
# In the example below, the ngrok domain is 9fbc-54-190-253-24.ngrok.io, but it will be different next time, unless you
# use a paid ngrok account and set a custom domain.

upstream node_debug {
    server 9fbc-54-190-253-24.ngrok.io:443;
}

server {
    listen 39229;
    server_name localhost;

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host 9fbc-54-190-253-24.ngrok.io;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://node_debug;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_pass_request_headers on;
    }
}
