// TODO: this function definition is duplicated for the sake of a quick fix. We should de-dupe it by moving it to a shared file.
// This function comes from https://github.com/WordPress/gutenberg/blob/5cd71f42499173e6d6f10a20a4b066f3aab77e34/packages/dom-ready/src/index.js
// I am using it instead of jQuery.ready(callback) because the callback I was passing it is not being run in a certain scenario.
// The scenario is when using the block editor, WP's JS will transform shortcode into its content via an XHR call,
// (and in our code, it enqueues this JS file)
// and then the ready function callback is not being called. The confusing thing was that this file was being executed
// before the XHR. If I put console.log statements in OTHER JS file jQuery.ready callbacks, they'd fire, but not this one.
// I assume there is something that tells Wordpress not to run the callback if it comes from XHR.
// Another scenario occurs in Wordpress 5.6 (not 5.5 or before): the Create Shortcode TinyMCE button doesn't appear,
// even though the iFound Map Maker button does. The fix is to use this domReady function instead of jQuery.ready.
function domReady( callback ) {
  if (
    document.readyState === 'complete' || // DOMContentLoaded + Images/Styles/etc loaded, so we call directly.
    document.readyState === 'interactive' // DOMContentLoaded fires at this point, so we call directly.
  ) {
    return void callback();
  }

  // DOMContentLoaded has not fired yet, delay callback until then.
  document.addEventListener( 'DOMContentLoaded', callback );
}

domReady(function() {
  var $ = jQuery;

  var blockEditorCallback;
  $(window).on('ifound:show-map-maker', function(event, attributes, callback) {
    blockEditorCallback = callback;
    initMapForm({ attributes });
  });

  // Let people draw
  function initMapForm(options) {
    if (!options) {
      options = {};
    }
    if(typeof google === "undefined" || typeof ifound_map === 'undefined') {
      console.error('Something wrong with Google API Key. Please contact iFoundAgent Support');
      return;
    }
    var map = getMap();
    // Use to allocate control DOM objects om the map
    map._addControl_ = function(position, controlDiv) {
      this.controls[google.maps.ControlPosition[position]].push(controlDiv);
    };

    // Initialize Drawing Manager and list/label managment panel
    var drawingManager = getDrawingManager(map);
    var managerPanel = getManagerPanel();

    // Add the list panel and the color palette
    map._addControl_('LEFT_CENTER', managerPanel);
    map._addControl_('BOTTOM_CENTER', getColorPalette(drawingManager));

    var bounds =  new google.maps.LatLngBounds();
    var shortcodeContent = isBlockEditor() ? getPolygonsAndLabels(options.attributes) : getShortcodeContent();
    // If a shortcode already exists,
    // recreate it back on the map
    if(shortcodeContent && Object.entries(shortcodeContent).length > 0) {
      if(typeof shortcodeContent.polygons !== 'undefined' &&
          shortcodeContent.polygons.length > 0) {
        for(var polygonMeta of shortcodeContent.polygons) {
          var path = createPathArray(polygonMeta.polypath);
          // Temp hack
          if(typeof polygonMeta.polycolor !== 'undefined') {
            polygonMeta.polycolor = polygonMeta.polycolor.replace(/"/g, '');
          }
          var polygon = new google.maps.Polygon({
            'strokeColor': (typeof polygonMeta.bordercolor === 'undefined'
              ? polygonMeta.polycolor
              : polygonMeta.bordercolor),
            'strokeOpacity': 0.8,
            'strokeWeight': 2,
            'fillOpacity': 0.35,
            'fillColor': polygonMeta.polycolor,
            'editable': true,
            'draggable': false,
            'paths': path,
            'map': map
          });

          polygon.polyName = polygonMeta.polyname;
          polygon.polyLink = polygonMeta.polylink;
          polygon.labelCoord = polygonMeta.labelcoord;
          polygon.polyLabel = getPolyLabel(polygon);
          polygon.polyColor = polygonMeta.polycolor;
          polygon.borderColor = polygonMeta.bordercolor;
          var ctlDiv = getPolyControllerDiv(polygon);
          polygon.polyLabel = makeDraggableLabel(ctlDiv, polygon);
          managerPanel.querySelector('.ifound-gcontrol-polygons').appendChild(ctlDiv);
          attachColorListener(ctlDiv);
          attachChangedListener(ctlDiv);

          polygon.getPath().forEach(function(coord) {
            bounds.extend(coord);
          });
      }
    }

    if(typeof shortcodeContent.labels !== 'undefined' &&
      shortcodeContent.labels.length > 0) {
        for(var labelMeta of shortcodeContent.labels) {
          // Temp hack
          if(typeof labelMeta.labelcolor !== 'undefined') {
            labelMeta.labelcolor = labelMeta.labelcolor.replace(/"/g, '');
          }
          var label = getLabel(labelMeta);
          label.open(map);
          label.labelLink = labelMeta.labellink;
          label.labelName = labelMeta.labelname;
          label.labelColor = labelMeta.labelcolor;
          var ctlDiv = getLabelControllerDiv(label);
          managerPanel.querySelector('.ifound-gcontrol-labels').appendChild(ctlDiv);
          label = makeDraggableLabel(ctlDiv, null, label, map);
          bounds.extend(label.getPosition());
        }
      }
    }

    void(!bounds.isEmpty() && map.fitBounds(bounds));
    // Once the polygon is drawn, popup a prompt, collect name, [link] for the
    // polygon/label
    google.maps.event.addListener(drawingManager, 'polygoncomplete', function(polygon) {
      // Unfocus the polygon tool
      this.setDrawingMode(null);
      getPolyName(polygon, function(polyName, link) {
        if(!polyName) {
          polygon.setMap(null);
          return;
        }

        polygon.polyName = polyName;
        polygon.polyLink = link || '';
        polygon.polyLabel = getPolyLabel(polygon);
        polygon.polyColor = (typeof window.polyCurrColor === 'undefined' ? '#eee' :
                              window.polyCurrColor);
        polygon.borderColor = polygon.polyColor;
        polygon.setOptions({
          'strokeColor': window.polyCurrColor,
          'fillColor': window.polyCurrColor,
        });
        polygon.setEditable(true);
        // Tie the polygon to the polygon controller div on the manager panel
        var ctlDiv = getPolyControllerDiv(polygon);
        polygon.polyLabel = makeDraggableLabel(ctlDiv, polygon);
        managerPanel.querySelector('.ifound-gcontrol-polygons').appendChild(ctlDiv);
        attachColorListener(ctlDiv);
        attachChangedListener(ctlDiv);
      });
    });

    // Quite similar to the listener above but in regards to  to markers(labels ugh sorry)
    google.maps.event.addListener(drawingManager, 'markercomplete', function(marker) {
      // Unfocus the marker button
      this.setDrawingMode(null);
      getPolyName(marker, function(markerName, link) {
        if(!markerName) {
          marker.setMap(null);
          return;
        }
        marker.labelName = markerName;
        marker.labelLink = link || '';
        marker.labelColor = (typeof window.polyCurrColor === 'undefined' ? '#eee' :
                              window.polyCurrColor);

        var label = getMarkerLabel(marker);
        marker.setMap(null);
        var ctlDiv = getLabelControllerDiv(label);
        managerPanel.querySelector('.ifound-gcontrol-labels').appendChild(ctlDiv);
        label = makeDraggableLabel(ctlDiv, null, label, map);
      });
    });
  }

  // For now only one shortcode
  // but should add support for multiple on the next release
  function getShortcodeContent() {
    var shortcode = null;
    for(var editor in tinyMCE.editors) {
      if(tinyMCE.editors[editor].getContent) {
        var content = tinyMCE.editors[editor].getContent().toString();
        if(content.includes('[ifound_polygon_maker ')) {
          var shRegex = /\[ifound_polygon_maker .*?\]/g;
          shortcode = content.match(shRegex)[0];
          window._orig_shortcode_ = shortcode;
          break;
        }
      }
    }
    if(!shortcode) return false;
    return disassembleShortcode(shortcode);
  }

  // Create a google maps object with zoom
  // controls and such
  function getMap() {
    var mapOpts = getMapOptions();
    var div = injectPopupDiv();

    var map = new google.maps.Map(div, mapOpts);
    return map;
  }

  // Inject the map div
  // And... return it
  function injectPopupDiv(hmtl, zIndex) {
    if(typeof html === 'undefined') {
      html = '';
      zIndex = '9997';
    } else if(typeof zIndex === 'undefined')
      zIndex = '9997';

    // Freeze everything else besides the overlay
    document.body.style.overflow = 'hidden';
    var overlay = document.body.appendChild(getBlockingOverlay(zIndex));

    var div = document.createElement('div');
    div.id = 'ifound_polydraw_map';
    div.style.width = '100%';
    div.style.height = '100%';

    var popup = document.createElement('div');
    // Specify the index here for now, move it to
    // css later when more existing features can be tested
    popup.style.zIndex = '9998';

    popup.classList = 'pop-up pop-box active';
    popup.innerHTML += (html + '<i class="fa fa-times-circle pop-up-close">');

    var icon = popup.querySelector('.pop-up-close');
    icon.style.fontSize = '1.5rem';
    icon.style.cursor = 'pointer';
    icon.style.float = 'right';
    icon.addEventListener('click', function() {
      popup.remove();
      overlay.remove();
      document.body.style.overflow = 'initial';
    });

    popup.appendChild(div);
    // test only
    document.body.appendChild(popup);
    return div;
  }

  // styles, zoom, center
  // go here
  function getMapOptions() {
      /******
        VAR STYLES
          ******/
      var styles = [
        {
          'featureType': 'all',
          'stylers': [
              {
                'saturation': 0,
              }
           ]
        },
        {
          'featureType': 'road.arterial',
          'elementType': 'geometry',
          'stylers': [
            {
              'hue': '#c0c0c0'
            },
            {
              'saturation': 20
            }
          ]
        }
      ];
      //\\//\\///\\//\\//\\//\\
     //|||||| End STYLES ||||||\
    //\\//\\//\\//\\//\\//\\//\\\

    return {
      // TODO: This about this one
      'center': new google.maps.LatLng(ifound_map.geo.center_lat, ifound_map.geo.center_lng),
      'styles': styles,
      'zoom': ifound_map.geo.zoom,
      ...window.iFoundGlobal.sharedGoogleMapBaseOptions,
    };
  }
  //// <!> End get Map options <!> ///

  // TODO: Change class names from legacy
  // But together with CSS changes
  //

  // Well let's get that drawing manager
  function getDrawingManager(map) {
    window.polyCurrColor = '#030227';
    var opts = {
      'strokeColor': window.polyCurrColor,
      'strokeOpacity': 0.8,
      'strokeWeight': 2,
      'fillOpacity': 0.35,
      'editable': true,
      'draggable': false,
      'fillColor': window.polyCurrColor,
      'zIndex': 0
    };

    return new google.maps.drawing.DrawingManager({
      'drawingControlOptions': {
        'position': google.maps.ControlPosition.BOTTOM_RIGHT,
        'drawingModes': ['polygon', 'marker'/*,'circle', 'rectangle' */]
      },
      'markerOptions': {
        'draggable': false
      },
      'polylineOptions': {
        'editable': true,
        'draggable': false
      },
      'rectangleOptions': opts,
      'circleOptions': opts,
      'polygonOprions': opts,
      'map': map
    });
  }

  // Let's do it
  // We barely need the google's position integer here lol
  function getManagerPanel() {
    var saveButton = document.createElement('div');
    saveButton.classList = 'polygon-create-button button button-large wp-core-ui button-primary';
    // These changes are questionable (my style is questionable)
    saveButton.style.width = '100%';
    saveButton.style.textAlign = 'center';
    saveButton.innerHTML = 'Save Map';

    var polyPanel = document.createElement('div');
    polyPanel.classList = 'ifound-gcontrol-polygons';
    polyPanel.style.height = '45%';
    polyPanel.style.overflowY = 'scroll';
    polyPanel.innerHTML = '<h2 style="margin-top: 0">Polygons</h2>';

    var labelPanel = document.createElement('div');
    labelPanel.classList = 'ifound-gcontrol-labels';
    labelPanel.style.height = '45%';
    labelPanel.style.overflowY = 'scroll';
    labelPanel.innerHTML = '<h2 style="margin-top: 0">Points of interest</h2>';

    var saveButtonPanel = document.createElement('div');
    saveButtonPanel.classList = 'ifa-button-panel ifa-col-01';
    saveButtonPanel.style.height = '10%';
    saveButtonPanel.appendChild(saveButton);

    var panel = document.createElement('div');
    panel.classList = 'ifound-gcontrol-panel';
    panel.style.height = '100%';
    panel.style.width = '12rem';
    panel.style.backgroundColor = '#fafafa';
    panel.style.bottom = '0';

    panel.appendChild(polyPanel);
    panel.appendChild(labelPanel);
    panel.appendChild(saveButtonPanel);

    saveButton.addEventListener('click', function() {
      serializeAndQuit(panel);
    });
    return panel;
  }

  // Get the color palette so we can paint
  // the polygons! \/\/ o \/\/
  function getColorPalette(drawingManager) {
    var colors = ['#1E90FF', '#FF1493', '#eef848',
                  '#32CD32', '#FF8C00', '#4B0082',
                  '#030237'];

    var palette = document.createElement('div');
    palette.classList = 'ifound-color-palette';
    palette.style.padding = '3px';
    palette.style.backgroundColor = '#fafafa';

    var cellSide = 15;
    var cellDistance = 2;
    var height = colors.length * cellSide
               + colors.length * cellDistance;

    palette.style.height = height + 'px';
    palette.style.transform = 'rotate(90deg)'; // Yup!
    palette.style.marginBottom = -(height/3) + 'px';

    function getColorDiv(color) {
      var div = document.createElement('div');
      div.style.backgroundColor = color;
      div.style.width = cellSide + 'px';
      div.style.height = cellSide + 'px';
      div.style.marginBottom = cellDistance + 'px';

      div.addEventListener('click', function() {
        window.polyCurrColor = color;
      });
      return div;
    }

    colors.forEach(function(color) {
      palette.appendChild(getColorDiv(color));
    });

    return palette;
  }

  // Creates a text field associated with the polygon id
  function getPolyControllerDiv(polygon) {
    var div = document.createElement('div');
    div.innerHTML = polygon.polyName
      + ' <i class="fa fa-times-circle dynamic-input-remove">';
    div.style.padding = "5px";
    div.style.margin = '5px';
    div.style.display = 'inline-block';
    div.style.border = '2px solid ' + polygon.polyColor;
    div.style.borderRaduius = '5px';
    div.style.color = '#fff';
    div.style.backgroundColor = polygon.polyColor;
    div.style.wordBreak = 'break-all';
    div.style.cursor = 'pointer';
    div.classList = 'ifound-gcontrol-item';
    div.polygon = polygon;

    div.setAttribute('polyname', polygon.polyName);
    div.setAttribute('polylink', polygon.polyLink);
    div.setAttribute('polypath', polygon.getPath().getArray());
    div.setAttribute('polycolor', polygon.polyColor);
    div.setAttribute('bordercolor',
      (typeof polygon.borderColor === 'undefined' ? polygon.polyColor : polygon.borderColor));
    div.setAttribute('labelcoord', polygon.labelCoord);

    div.addEventListener('click', function() {
      // Check if the exit was clicked instead
      if(!this.polygon.map) return;

      getPolyName(polygon, function(newName, newLink) {
        if(!newName) return;
        polygon.polyName = newName;
        polygon.polyLink = newLink;
        div.innerHTML = polygon.polyName
          + ' <i class="fa fa-times-circle dynamic-input-remove">';

        div.setAttribute('polyname', polygon.polyName);
        div.setAttribute('polylink', polygon.polyLink);
      }, polygon.polyName, polygon.polyLink);
    });

    // Delete the polygon from the map once it's clicked
    // Bye
    div.querySelector('.dynamic-input-remove').addEventListener('click', function() {
      div.polygon.setMap(null);
      div.polygon.polyLabel.close();
      // Ohh rhe wonders of garbage collecting!
      div.remove();
    });

    return div;
  }

  // Thought about utilizing the first controller function
  // but... this is cleaner
  // if one more item added, then do modularization
  function getLabelControllerDiv(label) {
    var div = document.createElement('div');
    div.innerHTML = label.labelName
      + ' <i class="fa fa-times-circle dynamic-input-remove">';
    div.style.padding = "5px";
    div.style.margin = '5px';
    div.style.display = 'inline-block';
    div.style.border = '2px solid ' + label.labelColor;
    div.style.borderRaduius = '5px';
    div.style.color = '#fff';
    div.style.backgroundColor = label.labelColor;
    div.style.wordBreak = 'break-all';
    div.style.cursor = 'pointer';

    div.classList = 'ifound-gcontrol-item';
    div.label = label;

    div.setAttribute('labelname', label.labelName);
    div.setAttribute('labellink', label.labelLink);
    div.setAttribute('labelcoord', label.getPosition());
    div.setAttribute('labelcolor', label.labelColor);

    div.addEventListener('click', function() {
     // Same as with polygons
      if(!this.label.map) return;

      getPolyName(label, function(newName, newLink) {
        if(!newName) return;
        label.labelName = newName;
        label.labelLink = newLink;
        div.innerHTML = label.labelName
          + ' <i class="fa fa-times-circle dynamic-input-remove">';

        div.setAttribute('labelname', label.labelName);
        div.setAttribute('labellink', label.labelLink);
      }, label.labelName, label.labelLink);
    });

    div.querySelector('.dynamic-input-remove').addEventListener('click', function() {
      div.label.setMap(null);
      // Ohh rhe wonders of garbage collecting!
      div.remove();
    });

    return div;
  }

  // Getting the name of the polygon
  // by showing this prompt
  // @param {object}. IFA inhanced Google Polygon obj or the Marker object
  // @param {callaback} cb. Function to call once the popup is gone
  // @param {string=} nVal. Value for the name
  // @param {string=} lVal. Value for the link
  function getPolyName(item, cb, nVal, lVal) {
    if(typeof nVal === 'undefined') {
      nVal = false;
      lVal = false;
    } else if(typeof lVal === 'undefined')
      lVal = false;

    // Name input
    var nInput = document.createElement('input');
    nInput.style.border = '1px solid #eee';
    nInput.style.borderRadius = '5px';
    if(nVal)
      nInput.value = nVal;
    else
      nInput.setAttribute('placeholder', 'Polygon Name');

    // Link input
    var lInput = document.createElement('input');
    lInput.style.border = '1px solid #eee';
    lInput.style.borderRadius = '5px';
    lInput.style.marginTop = '5px';
    lInput.style.width = '100%';
    if(nVal)
      lInput.value = lVal;
    else
      lInput.setAttribute('placeholder', 'Link To');

    // Browse post/pages button
    var dropdown = document.createElement('select');
    dropdown.style.width = '100%';
    dropdown.style.marginLeft = '5px';
    dropdown.style.marginTop = '5px';
    dropdown.id = 'polygon-link-selection';
    dropdown.innerHTML = '<option value="">Posts/Page</option>';
    dropdown.onchange = function() {
      lInput.value = document.getElementById('polygon-link-selection').value;
    };

    $.ajax({
      'url': create_polygon_endpoint,
      'type': 'post',
      'data': {
        'action': 'get_posts_',
      },
      'success': function(data) {
        try {
          var posts = JSON.parse(data);
        } catch(e) {
          console.error('Error parsing response from the server');
        }

        posts.forEach(function(post) {
          var option = document.createElement('option');
          option.innerHTML = post.title;
          var proto = location.protocol != 'https' ? 'http://' : 'https://';
          option.value = proto + window.location.hostname + '/' + post.link;
          dropdown.appendChild(option);
        });
      }
    });

    // Save button
    var sButton = document.createElement('div');
    sButton.style.marginLeft = '25%';
    sButton.style.marginTop = '5px';
    sButton.classList = 'button';
    sButton.innerHTML = 'Save';

    var pane = document.createElement('div');
    pane.innerHTML = '<i style="float:right" class="fa fa-times-circle input-exit">';
    pane.style.width = '300px';
    pane.style.padding = '20px';
    pane.style.margin = '35% auto';
    pane.style.background = '#fff';

    pane.appendChild(nInput);
    pane.appendChild(lInput);
    pane.appendChild(dropdown);
    pane.appendChild(sButton);

    var blockingOverlay = getBlockingOverlay();
    blockingOverlay.style.display = 'none';
    blockingOverlay.appendChild(pane);

    var initBodyOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    document.body.appendChild(blockingOverlay);

    sButton.addEventListener('click', function() {
      var name = nInput.value.replace(/\W/g, '');
      if(name == '') {
        nInput.placeholder = 'The name cannot be an empty word';
        return;
      }

      var link = lInput.value;
      // clean up bro
      blockingOverlay.remove();
      document.body.style.overflow = initBodyOverflow;

      cb(name, link);
    });

    pane.querySelector('.input-exit').addEventListener('click', function() {
      blockingOverlay.remove();
      document.body.style.overflow = initBodyOverflow;
      cb(false);
    });

    $(blockingOverlay).fadeIn(1500);
  }
  // End getPolyName

  // We use it a lot so we need it, ok?
  // Move something like that to a special IFA util file
  function getBlockingOverlay(zIndex) {
    var div = document.createElement('div');
    div.classList = 'to-close-overlay';

    div.style.position = 'absolute';
    div.style.width = '100%';
    div.style.height = '100%';
    div.style.backgroundColor = '#000';
    div.style.opacity = '.9';
    div.style.position = 'absolute';
    div.style.top = window.scrollY + 'px';
    div.style.zIndex = (typeof zIndex === 'undefined' ? '9999' : zIndex);

    return div;
  }

  // TODO: Here
  // Also, maybe move LabelOverlay to some maps util js file
  function getPolyLabel(polygon) {
    var label = new google.maps.InfoWindow();
    label.setContent('<div class="ifa-label-name">' + polygon.polyName + '</div>');
    if(typeof polygon.labelCoord === 'undefined' || polygon.labelCoord.includes('undefined')) {
      var position = getPolyCenter(getFattestRect(polygon));
    } else {
      var coords = getCoords(polygon.labelCoord);
      var position = {
        'lat': coords.lat,
        'lng': coords.lng
      };
    }

    label.setPosition(position);
    label.open(polygon.map);
    label.setZIndex(2);
    return label;
  }

  // TODO: move
  function getMarkerLabel(marker) {
    var label = new google.maps.InfoWindow();

    label.setContent('<div class="ifa-label-name">' + marker.labelName + '</div>');
    label.setPosition({
      'lat': marker.getPosition().lat(),
      'lng': marker.getPosition().lng()
    });
    label.labelName = marker.labelName;
    label.labelLink = marker.labelLink;
    label.labelColor = marker.labelColor;
    label.open(marker.map);
    return label;
  }

  // TODO: Move
  function getLabel(labelObj) {
    var label = new google.maps.InfoWindow();
    label.setContent('<div class="ifa-label-name">' + labelObj.labelname + '</div>');
    label.setPosition(getCoords(labelObj.labelcoord));
    return label;
  }

  function getFattestRect(polygon) {
    var mvcPaths = polygon.getPath().getArray();
    var paths = [];
    // Weird API ugh
    mvcPaths.forEach(function(point) {
      paths.push({
        'lat': point.lat(),
        'lng': point.lng()
      });
    });

    // find north east
    // Use slice() so not to modify the paths array
    var latArray = paths.map(function(path) {
      return path.lat;
    });
    var lngArray = paths.map(function(path) {
      return path.lng;
    });

    var n = Math.max.apply(null, latArray);
    var w = Math.max.apply(null, lngArray);
    var s = Math.min.apply(null, latArray);
    var e = Math.min.apply(null, lngArray);

    // Starting position: North West
    var points = {
      'nw': {
        'lat': n,
        'lng': w
      },
      'ne': {
        'lat': n,
        'lng': e
      },
      'se': {
        'lat': s,
        'lng': e
      },
      'sw': {
         'lat': s,
         'lng': w
      }
    };

    return scanAndFind(points, paths);
  }

  function scanAndFind(points, paths) {
    // The lower the rate, the more accurate the estimation is
    // but the slower the calculation rate.

    //TODO: change rate mechanism
    var scanRate = .01;

    var rows = [];
    for(var i = points.nw.lat; i > points.sw.lat; i-=scanRate) {
      var row = [];
      for(var j = points.nw.lng; j > points.ne.lng; j-=scanRate) {
        var lIndex = row.push({
          'lat': i,
          'lng': j,
          'isWithin': isWithinPolygon({'lat': i, 'lng': j}, paths)
        }) - 1;
      }
      rows.push(row);
    }

    return findBiggestRect(rows);
  }

  function isWithinPolygon(point, paths) {
    var numOfCrossings = 0;
    for(var i = 0; i < paths.length; i++) {
      var vertex1 = paths[i];
      var vertex2 = paths[(i+1) % paths.length];
      if(westOf(vertex1, vertex2, point))
        ++numOfCrossings;
    }

    function westOf(v1, v2, p) {
      if(v1.lat <= v2.lat) {
        if(p.lat <= v1.lat || p.lat > v2.lat || p.lng >= v1.lng && p.lng >= v2.lng)
          return false;
        else if(p.lng < v1.lng && p.lng < v2.lng)
          return true;
        else
          return (p.lat - v1.lat) / (p.lng - v1.lng) > (v2.lat - v1.lat) / (v2.lng - v1.lng);
      } else return westOf(v2, v1, p);
    }

    return numOfCrossings % 2;
  }

  function findBiggestRect(rows) {
    var maxRatio = 5/2;

    var rects = [];
    for(var i = 0; i < rows.length - 1; i++) {
      for(var j = 0; j < rows[i].length - 1; j++) {
        var rect = {
          'nw': rows[i][j],
          'ne': rows[i][j],
          'sw': rows[i][j],
          'se': rows[i][j]
        }
        while(typeof rows[i][++j] !== 'undefined' && rows[i][j].isWithin) {
          rect.ne = rows[i][j];
          var index = i;
          while(typeof rows[++index] !== 'undefined' &&  rows[index][j].isWithin) {
            rect.sw = rows[index][j];
          }
        }
        rects.push(rect);
      }
    }

    return rects.reduce(function(max, rect) {
      var width = Math.abs(rect.nw.lng - rect.ne.lng);
      var len = Math.abs(rect.nw.lat - rect.sw.lat);
      var area = len * width;
      if(area > max.area) {
        max = {
          'rect': rect,
          'area': area
        };
      }
      return max;
     }, {'rect': rects[0], 'area': 0}).rect;
  }

  function getPolyCenter(polygon) {
    var vert = polygon.ne.lng + Math.abs((polygon.nw.lng - polygon.ne.lng) / 2);
    var hor = polygon.sw.lat + Math.abs((polygon.nw.lat - polygon.sw.lat) / 2);
    return { 'lng': vert, 'lat': hor };
  }

  function isBlockEditor() {
    return typeof ifound_polygon_drawing !== 'undefined' && ifound_polygon_drawing.is_block_editor;
  }

    // create the shortcode and append it to the tinymce editor
  function serializeAndQuit(panel) {
    /**
     * Init the payload
     *  |  |
     * \|  |/
     *  \  /
     *   \/
     * Collect controller DOMs, extract and package their meta
     */
    var polygonDivs = panel.querySelector('.ifound-gcontrol-polygons')
                            .querySelectorAll('.ifound-gcontrol-item');
    var polyAttrs = ['polyname', 'polylink', 'polypath', 'polycolor', 'labelcoord', 'bordercolor'];
    var polymeta = collectMeta(polygonDivs, polyAttrs);

    var labelDivs = panel.querySelector('.ifound-gcontrol-labels')
                         .querySelectorAll('.ifound-gcontrol-item');
    var labelAttrs = ['labelname', 'labellink', 'labelcoord', 'labelcolor'];
    var labelmeta = collectMeta(labelDivs, labelAttrs);

    var shortcodePayload = {
      'polygons': polymeta,
      'labels': labelmeta
    }
    // End Init Payload

    if (isBlockEditor()) {
      var atts = $.extend({}, shortcodePayload);
      atts.polygons = doCustomSerialization(atts.polygons);
      atts.labels = doCustomSerialization(atts.labels);
      blockEditorCallback(atts);
    } else {
      // Assemble the shortcode and pass it tot he rich text editor
      var shortcode = assembleShortcode(shortcodePayload);

      // Delete the old shortcode
      if(typeof window._orig_shortcode_ !== 'undefined')
        tinyMCE.activeEditor.setContent(tinyMCE.activeEditor.getContent().replace(window._orig_shortcode_, shortcode));
      else
        tinymce.activeEditor.execCommand('mceInsertContent', false, shortcode);
    }

    // Over and out
    document.querySelector('.to-close-overlay').remove();
    document.querySelector('.pop-box.active').remove();
    document.body.style.overflow = 'initial';
  }

  /**
   * Collect meta from s
    console.log(window._orig_shortocode_);pecific elements types
   *
   * @param {Object[]} elemsOfType Array of elements DOM elements
   * @param {string[]} metaList
   * @returns {Object[]} An array of meta objects
   */
  function collectMeta(elemsOfType, metaList) {
    var meta = [];

    for(var i = 0; i < elemsOfType.length; i++) {
      var tempObj = {};
      var div = elemsOfType[i];
      for(var j = 0; j < metaList.length; j++) {
        tempObj[metaList[j]] = div.getAttribute([metaList[j]]);
      }
      meta.push(tempObj);
    }
    return meta;
  }

  /**
   * shortcodePayload = { polygons, lables }
   */
  function assembleShortcode(shortcodePayload) {
    var shortcodeTokens = ['[ifound_polygon_maker'];
    var mapSettings = ['height', 'zoom', 'center'];

    // Restore height/zoom if in orig shortcode
    if(typeof window._orig_shortcode_ !== 'undefined') {
      if(window._orig_shortcode_.includes('height')) {
        var regex = /height="(.*?)"/;
        var matches = window._orig_shortcode_.match(regex);
        if(matches)
          shortcodeTokens.push('height="' + matches[1] + '"');
        else
          shortcodeTokens.push('height=""');
      }

      if(window._orig_shortcode_.includes('zoom')) {
        var regex = /zoom="(.*?)"/;
        var matches = window._orig_shortcode_.match(regex);
        if(matches)
          shortcodeTokens.push('zoom="' + matches[1] + '"');
        else
          shortcodeTokens.push('zoom=""');
      }

      if(window._orig_shortcode_.includes('center')) {
        var regex = /center="(.*?)"/;
        var matches = window._orig_shortcode_.match(regex);
        if(matches)
          shortcodeTokens.push('center="' + matches[1] + '"');
        else
          shortcodeTokens.push('center=""');
      }
    } else {
       shortcodeTokens.push('height="" zoom="" center=""');
    }
    // :)
    for(var k in shortcodePayload) {
      if(shortcodePayload.hasOwnProperty(k)) {
        var token = k + '="';
        token += doCustomSerialization(shortcodePayload[k]);
        shortcodeTokens.push(token + ';"');
      }
    }
    shortcodeTokens.push(']');
    return shortcodeTokens.join(' ').replace(/;"/g, '"');
  }

  function doCustomSerialization(shortcodePayloadItem) {
    var token = '';
    for(var kok of shortcodePayloadItem) {
      token += 'object:';
      for(var obj in kok) {
        token+=obj + '::' + kok[obj].replace(/"/g, '') + ";";
      }
    }
    return token;
  }

  // Opposite of assembleShortcode()
  /**
   * Incoming format:
   * [ifound_polygon_marker polygons="object:polyname::fooname;polylink::foolink;object:... etc
   */
  function disassembleShortcode(shortcode) {
    var fieldList = ['polygons', 'labels'];
    var obj = {};
    for(var field of fieldList) {
      var regex = new RegExp(field + '="(object.*?)"', 'g');
      var fieldVal = shortcode.match(regex);
      if(!fieldVal) continue;
      var objs = parseShortcodeAttribute(fieldVal[0])
      obj[field] = objs;
    }
    return obj;
  }

  function getPolygonsAndLabels(attributes) {
    if (!Object.keys(attributes).length) {
      return false;
    }
    return {
      polygons: parseShortcodeAttribute(attributes.polygons),
      labels: parseShortcodeAttribute(attributes.labels),
    };
  }

  function parseShortcodeAttribute(string) {
    var items = string.split('object:');
    var objs = [];
    for(var i = 1; i < items.length; i++) {
      var keyVals = items[i].split(';').reduce(function(kvals, item) {
        if(item.includes('::')) {
          var temp = item.split('::');
          kvals.push.apply(kvals, temp);
        }
        return kvals;
      }, []);
      var tempObj = {};
      for(var j = 0; j < keyVals.length - 1; j+=2) {
        tempObj[keyVals[j]] = (typeof keyVals[j+1] === 'undefined' ? '' : keyVals[j+1]);
      }
      objs.push(tempObj);
    }
    return objs;
  }

  // Move to that utils file too
  function createPathArray(coordStr) {
    var coords = coordStr.match(/(\(.*?\))/g);
    var path = [];
    for(var coord of coords) {
      path.push(getCoords(coord));
    }
    return path;
  }

  function getCoords(coord) {
      var regArray = coord.match(/(-|)(\d+\.\d*)/g);
      return {
          'lat': Number(regArray[0]),
          'lng': Number(regArray[1])
      };
  }

  function attachColorListener(div) {
    google.maps.event.addListener(div.polygon, 'click', function() {
      void(window.draggableLabel || void(
        this.setOptions({
          'fillColor': window.polyCurrColor,
          'stokeColor': window.polyCurrColor,
        }),
        div.setAttribute('polycolor', window.polyCurrColor),
        div.style.borderColor = window.polyCurrColor,
        div.style.backgroundColor = window.polyCurrColor
        ));
    });
  }

  function attachChangedListener(div) {
    google.maps.event.addListener(div.polygon.getPath(), 'insert_at', function() {
      div.setAttribute('polypath', div.polygon.getPath().getArray());
      div.polygon.polyLabel.setPosition(getPolyCenter(getFattestRect(div.polygon)));
    });
    google.maps.event.addListener(div.polygon.getPath(), 'set_at', function() {
      div.setAttribute('polypath', div.polygon.getPath().getArray());
      div.polygon.polyLabel.setPosition(getPolyCenter(getFattestRect(div.polygon)));
    });
  }

  // Since InfoWindow object doesn't have a draggable interface
  // AFAIK (feel free to prove me wrong).
  // Let's emulate the drag
  function makeDraggableLabel(ctlDiv, polygon, label, map) {
    if(typeof polygon === 'undefined' || !polygon) {
      var label = label;
      var map = map;
    } else {
      var label = polygon.polyLabel;
      var map = polygon.map;
    }

    google.maps.event.addListener(label, 'closeclick', function(e) {
      window.draggableLabel = label;
      label.open(map);

      var self = this;

      if(typeof polygon !== 'undefined' && polygon) {
        this.polyZoneClick = google.maps.event.addListener(polygon, 'click', function(_e) {
          dropLabel(_e);
        });

        this.polyZoneMouseover = google.maps.event.addListener(polygon, 'mousemove', function(_e) {
          hoverWith(_e);
        });
      }

      this.listener = google.maps.event.addListenerOnce(map, 'click', function(_e) {
        dropLabel(_e);
      });

      this.listener_ = google.maps.event.addListener(map, 'mousemove', function(_e) {
        hoverWith(_e);
      });

      function dropLabel(e) {
        label.setPosition({'lat': e.latLng.lat(), 'lng': e.latLng.lng() });
        google.maps.event.removeListener(self.listener_);
        google.maps.event.removeListener(self.polyZoneController);
        window.draggableLabel = null;
        google.maps.event.removeListener(self.listener);
        ctlDiv.setAttribute('labelcoord', label.getPosition());
      }

      function hoverWith(e) {
        if(!window.draggableLabel) return;
        label.setPosition({'lat': e.latLng.lat(), 'lng': e.latLng.lng() });
      }
    });
    return label;
  }

   //\ My least favortie part -> the rest of the file/\\
  /*|*\                                             /*|*\

*/
  (function(initMapForm) {
    // Let's plug this map in
    tinymce.PluginManager.add('ifound_polygon_drawing', function(editor, url) {
      editor.addButton('ifound_polygon_drawing', {
        'title': 'iFound Map Maker',
        'icon': 'icon ifound_polygon_drawing',
        'onclick': initMapForm
      });
    });
  })(initMapForm)
});
