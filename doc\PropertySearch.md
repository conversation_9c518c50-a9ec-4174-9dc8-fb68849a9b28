# Property Search

This page provides details about the Property Search functionality of the [IDX Server](IDXServer.md).

[TOC]

## See also

* [Search API](SearchAPI.md) - documentation of the IDX search API
* [IDX Search](IDXSearch.md) - more details about the IDX search
* [Solr](Solr.md) - details about Solr, which is used by the new IDX server for search
* [Old Search](OldSearch.md) - outline created to assist with case 1026

## Results JSON

**TODO**: This should be merged with the [Search API](SearchAPI.md) doc ...

### Debug JSON

If you specify `debug=2` or `debug=3` as a query parameter in the IDX search URL, the results JSON will contain extra information.

Here is a screenshot of the `debug` property for [this IDX query](http://api.profoundidx.com/q/search/?words=listing_office_shortid%3Asnde01&max_results=15&pp=1&sort=ModificationTimestamp%2CDESC&mls_class=rentals&extended_data=%5B%5D&apikey=la9DMf%2BihzoAz7e&debug=2):

![](images/idx-search-debug-json.png)

## New Relic

A New Relic Insights dashboard has been created that provides some performance & query statistics data about IDX searches.

* [Insights: IDX Searches](https://insights.newrelic.com/accounts/815479/dashboards/72999)

See the [IDX Server](IDXServer.md) page for more details on custom parameters sent to New Relic.

## Technical Details

This section contains some technical details about how the code inside `server/lib/PropSearch.iced` works.

### Images

Images for properties that are returned from Solr are pulled from MySQL, using additional queries to the MySQL DB.

**Note**: These could also be indexed & stored in Solr if desired for performance.

### SEO string

The [Property Details]() URL, and the title, H2, and search results HTML content are configurable via "SEO" fields inside the `access_meta` MySQL database table, on a per-site/customer basis.

These fields contain templates with field names in the format `{FieldName}` that are filled in by the IDX search and added to each property object in the `listings` array of the results JSON.

### Field Cleaning

The data stored in the MySQL database and Solr isn't necessarily consistent or clean.  To keep capitalization & the format of other fields consistent & clean, and to pre-format some fields in the JSON, the `FieldCleaner` class is used.

### Field Mapping

The `field_mapping` table is used to map MLS-specific field names to more human-readable `MapName` values, which are used in the results JSON.

Each IDX search request reads the mappings for that MLS + Class.

### Database field type checks

Once the IDX server starts up, it reads the MySQL table schema for each MLS + Class (Solr core) into memory the first time a request is made for that MLS.  This is done only once per MLS + class (Solr core).

The field types are used for some type checks, which print `WARNING` message to the log file.

## TaskBump cases

See [IDX Search Cases](IDXSearchCases.md) for more details on the TaskBump cases & issues related to this functionality.

## Notes

* probably not going to be sorting on "Score" very often, if at all
