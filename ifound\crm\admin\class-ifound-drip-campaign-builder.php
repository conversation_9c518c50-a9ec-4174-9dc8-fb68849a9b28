<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundDripCampaignBuilder extends iFoundCrm {
	public static $menu_slug = 'create-drip-campaign';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('admin_menu', [$this, 'settings_menu'], 5);
		add_action('wp_ajax_drip_campaign_ajax', [$this, 'drip_campaign_ajax']);
	}

	public function settings_menu() {
		add_submenu_page(
			'campaign-builder',
			'New Drip Campaign',
			'Drip Campaign',
			'create_campaigns',
			static::$menu_slug,
			[$this, 'create_drip_campaign_page']
		);
	}

	public function create_drip_campaign_page() {
		wp_register_script('ifound_drip_campaign_js', plugins_url( 'js/drip-campaign.js', __FILE__ ),
			['jquery', 'ifound_drip_campaign_shared_js'], iFOUND_PLUGIN_VERSION);
		$template_steps_and_titles = iFoundDripCampaign::new_hookless()->get_template_steps_and_titles();
		wp_localize_script('ifound_drip_campaign_js', 'ifound_drip_campaign', [
			'endpoint'                           => admin_url('admin-ajax.php'),
			'nonce'                              => wp_create_nonce('drip_campaign_secure_me'),
			'drip_template_steps_by_template_id' => $template_steps_and_titles['drip_template_steps_by_template_id'],
			'email_template_titles_by_id'        => $template_steps_and_titles['email_template_titles_by_id'],
		]);

		wp_enqueue_script('ifound_drip_campaign_js');
		wp_enqueue_script('email_editor_js');
		wp_enqueue_script('jquery-ui-datepicker');

		wp_enqueue_style('campaign_builder_css');

		$drip_templates = iFoundDripCampaign::new_hookless()->get_drip_templates();
		$crm = $this->crm();
		$contact_id = $_GET['contact_id'];
		$add_new_contact_url = admin_url('post-new.php');
		$add_new_contact_url = add_query_arg(
			['post_type' => iFoundJointContact::new_hookless()->get_new_contact_type()], $add_new_contact_url);
		?>

		<h1 class="ifound-admin-h1"><? _e( 'Drip Campaign and Task Reminder Builder', 'ifound' ); ?></h1>

        <div class="ifound-wrap">
            <div class="default-criteria-heading"><i class="fal fa-user"></i> <? _e( 'Contact', 'ifound' ); ?></div>
            <table class="campaign-builder-table form-table">
                <tbody>
                <tr>
                    <th scope="row"><label for=""><? _e( 'Contact:', 'ifound' ); ?></label></th>
                    <td>
                        <input type="text" id="contact_autocomplete" placeholder="Type Here" class="regular-text ifound-validate">
                        <div style="margin-top: 4px;">
                            Or, start by <a href="<?= $add_new_contact_url ?>">adding a new contact</a>
                        </div>
                    </td>
                    <input type="hidden" id="contact_id" name="contact_id" value="<?= $contact_id ?>">
                    <? do_action('ifound_wa_sync_button'); ?>
                </tr>
                </tbody>
            </table>

            <form id="ifound-drip-campaign-form">
                <table class="form-table">
                    <tbody>
                    <tr>
                        <th scope="row"><label for="to_email"><? _e( 'To Email(s):', 'ifound' ); ?></label></th>
                        <td>
                            <input type="hidden" name="to_email" id="to_email">
                            <div class="to_emails_spinner hidden"><i class="fa fa-redo fa-spin"></i></div>
                            <strong class="no_contact_selected_warning">No contact selected</strong>
                            <div class="to_emails"></div>
                            <strong class="no_email_addresses_checked_warning hidden">You must check at least one email address</strong>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="default-criteria-heading">
                    <i class="fal fa-clone" aria-hidden="true"></i> <? _e('Template', 'ifound'); ?>
                </div>
                <table class="form-table">
                    <tbody>
                    <tr>
                        <th scope="row"><label for="drip_template_id"><? _e('Drip / Task Template:', 'ifound'); ?></label>
                        </th>
                        <td>
                            <select name="drip_template_id" class="drip_template_id ifound-validate">
                                <option value="" selected="selected" disabled="disabled">(Select)</option>
						        <?
						        foreach ($drip_templates as $title => $id) {
							        ?>
                                    <option value="<?= $id ?>"><?= $title ?></option>
							        <?php
						        }
						        ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="upcoming_step_index"><? _e('Start Step:', 'ifound'); ?></label>
                        </th>
                        <td>
                            <select class="upcoming_step_index" name="upcoming_step_index" class="ifound-validate">
                                <!-- Options will be filled in by JavaScript -->
                            </select>
                            <div>
                                <button type="button" class="show_hide_possible_steps" style="margin-top: 10px;">Show possible steps</button>
                                <div class="possible_steps hidden" style="margin-top: 10px; opacity: 50%;">
                                    <table class="striped widefat">
                                        <thead>
                                        <tr>
                                            <th>Step Number</th>
                                            <th>Email Template for Customer</th>
                                            <th>Reminder to Agent</th>
                                            <th>Interval</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td colspan="4">Select a Drip Template ID above to show the steps</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="title-heading default-criteria-heading"><i class="fal fa-heading"
                                                                       aria-hidden="true"></i> <? _e('Title', 'ifound'); ?>
                </div>
                <table class="form-table">
                    <tbody>
                    <tr>
                        <th scope="row"><label for="campaign_title"><? _e('Campaign Title:', 'ifound'); ?></label></th>
                        <td><input type="text" name="campaign_title" id="campaign_title"
                                   placeholder="e.g. Monthly Homeowner Tips"
                                   class="search-title-input regular-text ifound-validate"></td>
                    </tr>
                    </tbody>
                </table>

                <div class="default-criteria-heading"><i class="fal fa-calendar-alt"
                                                         aria-hidden="true"></i> <? _e('Schedule', 'ifound'); ?></div>
                <table class="form-table">
                    <tbody>
                    <tr>
                        <th scope="row"><label><? _e( 'Start Date:', 'ifound' ); ?></label></th>
                        <td><input class="start-date ifound-validate" type="text" name="start_date"/></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="time_of_day"><? _e('Time of Day:', 'ifound'); ?></label></th>
                        <td>
					        <? do_action('ifound_time_of_day_select', 'now', array('now' => 'Now')); ?>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="default-criteria-heading"><i
                        class="fal fa-puzzle-piece"></i> <? _e('Branding', 'ifound'); ?></div>
                <table class="form-table">
                    <tbody>
                    <tr>
                        <th scope="row"><label for="template_id"><? _e('Header:', 'ifound'); ?></label></th>
                        <td><? do_action('ifound_email_dropdown', 'header', 'header', $crm->header); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="template_id"><? _e('Signature:', 'ifound'); ?></label></th>
                        <td><? do_action('ifound_email_dropdown', 'signature', 'signature', $crm->signature); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="template_id"><? _e('Footer:', 'ifound'); ?></label></th>
                        <td><? do_action('ifound_email_dropdown', 'footer', 'footer', $crm->footer); ?></td>
                    </tr>
                    </tbody>
                </table>

                <table class="form-table">
                    <tbody>
                    <tr>
                        <td>
                            <div class="button save-campaign button-primary">
                                <i class="fal fa-plus-square" id="save-campaign-spinner" aria-hidden="true"></i>
						        <? _e('Save Campaign', 'ifound'); ?>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div class="success-msg">
                                <? _e('Campaign Processed Successfully', 'ifound'); ?>
                            </div>
                            <div class="success-sub-msg"></div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div
                                class="failed-msg"><? _e('There was an error processing your campaign. ', 'ifound'); ?></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </form>
        </div>
		<?php
		iFoundContactsAdmin::new_hookless()->enqueue_contact_autosuggest();
	}

	public function drip_campaign_ajax() {
		check_ajax_referer( 'drip_campaign_secure_me', 'drip_campaign_nonce' );

		$input = $_REQUEST['input'];
		$contact_id = intval( $input['contact_id'] );

		/** Make sure we have a contact to send this to. */
		if( is_int( $contact_id ) && get_post_status( $contact_id ) ) {
			$data = new stdClass();
			parse_str($input['form'], $form);
			$data = $this->obj($form);
			$data->contact_id = $contact_id;
			if( $drip_campaign_id = iFoundDripCampaign::new_hookless()->create_drip_campaign( $data ) ) {
				$response = array(
					'class'              => 'fa-plus-square',
					'drip_campaign_id'   => $drip_campaign_id,
					'drip_campaign_link' => admin_url('/post.php?action=edit&post=' . $drip_campaign_id),
				);
				echo json_encode( $response );
				die();
			}
		}

		$response = array(
			'class' 	    	=> 'fa-exclamation-triangle',
			'drip_campaign_id' 	=> 0,
		);

		/** We didn't have success, so let's go ahead and error. */
		echo json_encode( $response );
		die();
	}
}
