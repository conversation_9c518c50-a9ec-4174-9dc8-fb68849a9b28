# iFound IDX Plugin #

* Install the plugin at wp-content/plugins/. 
* Activate Gravity Forms plugin in WP-Admin > Plugins (if not already active).
* Activate iFound plugin in WP-Admin > Plugins.
* A valid iFoundAgent API Key is required.
* An MLS name is required.
* Navigate to WP Admin > iFound Settings > API Settings
* Add the API Key and MLS Name into the text inputs.
* Click Save Changes
* Allow 1-2 minutes for the settings to install your build.

## Status: Beta Testing ##

* This plugin is currently in beta testing.

## Features ##

* Quick Search - Property Search
* CRM - Contact Admin
* Save This - Saves Searches and Properties
* Campaign Builder - Create Searches and Email Campaigns
* Advanced Map - Draw Shapes on a Map
* Featured Listings - Highlight Listings in a Widget
* Search Nearby - Radius Search from Current Location
* Text Me Now - Text Button for Mobile Phones
* What's My Payment - Mortgage Payment Calulator with Lead Capture
* Sortable Criteria - Admin to select and Sort Search Criteia Options
* Current Market Comparison - Comprehensive Market Comparison with Lead Capture
* Compare Prices - Compares Prices to the Current Property
* Millenial Capture - Tools Designed to Capture the Newest Generation of Buyers
* Budget Search - Search by Budget Criteria
* API Integrations - Connect to External CRMs

## The purpose of this project is: ##

* Display real estate listings.
* Provide a UI to manage contacts.
* Provide a UI to create email campaigns..

## Gravity Forms ##

When creating forms, insure the field names match the list below. This is used to input contacts into the CRM and CRM integrations. 

The forms will be inserted at activation.

### Forms Included ###

* Address Capture
* Current Market Comparison
* Get Pre-Qualified
* Make Offer
* Registration Form
* Schedule Showing

### Field - Field Name ###

To use the CRM and CRM integrations, these input names are required.

* First Name - input_1.3
* Last Name - input_1.6
* Email - input_2
* Phone - input_3
* Comments - input_4
* Page URL - input_5 - Hidden

### Create New Forms ###

* Duplicate the Registration form
* Add or remove fields as needed

## Email Templates: ##

All the email templates are included in the automated setup process. 

New email templates can be created. Make sure to select an email type. This is required for your email templates to appear in the proper settings dropdown menu.

## Debugging Params ##

* ?idxurls - View the URLs used to build the page.
* ?ifound_refresh - Delete the transients that store the lookups, filed_mappings, and lookups_to_criteria.
* ?debug - Display the save this ID in the contact record or client profile for each entry.
* ?dump - This is used with ifound shortcodes to dump the search params saved in post meta.
	

