# History Tracker

I want you to build an app called History Tracker (or history_tracker when you need a computerized version like a URL slug).

The purpose of History Tracker is to track the history of real estate listings. I have another project that has access real estate listings from an external source using the RESO Web API (using the Property resource), and downloads them to a MySQL database. The problem is that each record only provides the current data, and I don't have access to previous values. For example, I might know that some listing's StandardStatus is Active, but I don't know WHEN it became Active.

## What the app needs to do

Here's how we'll track the history.

First, assume in MySQL we have a table named property. It has at least these fields (it really has others, but these are the relevant ones):
1. ListingId (primary key) - string
1. StandardStatus - string
1. ListPrice - decimal(14,2)
1. ModificationTimestamp - datetime(3)

I'm calling StandardStatus and ListPrice the "watched" fields. Those are the ones I am interested in tracking when they changed (and their values when they changed).

Further assume that table is usually updated approximately every 15 minutes. The external sync tool is reliable but not perfect so it might not run exactly every 15 minutes.

The History Tracker tool will be launched by the sync process when it's done syncing after its batch every 15 minutes. For the sake of communicating to the History Tracker what listings to check, the sync tool will output a JSON file with an array of ListingId strings. It's possible that the History Tracker tool might not run for a while, so there might be multiple JSON files. The JSON files will be in the data/ dir and should be processed alphabetically (the files will be ordered by a Y-m-d style timestamp, and processing them alphabetically will effectively process them chronologically). For each file, the History Tracker must process it as described below, then delete it.

The History Tracker should track the history using these 2 tables. I'll describe them here; you write SQL statements for me to execute to create the tables.

**Table property_changes**

- ListingId - this is a PRIMARY key - there is only one record per listing
- StandardStatus
- StandardStatus_ModificationTimestamp - null if StandardStatus has never CHANGED
	- This means what was the ModificationTimestamp of the record when we noticed the change
- StandardStatus_updated_at - null if it's never CHANGED
	- This means what time was the record updated via our HistoryTracker process
- ListPrice
- ListPrice_ModificationTimestamp - null if it's never changed
- ListPrice_updated_at - null if it's never changed
- created_at - datetime when this record was inserted
- updated_at - datetime when this record was updated

**Table property_history**

- ListingId
- StandardStatus
- ListPrice
- ModificationTimestamp
- created_at

When the History Tracker launches, it will look for these JSON files in the data/ dir and process them. To process them, it should look at the JSON, which is an array of ListingId strings. For each ListingId, it should look up the listing by ListingId in the property table and grab the watched values, as well as the ModificationTimestamp. Add a record to this table when any of the watched fields changes, but only when at least one watched field changes. Fill in the watched field values regardless of whether they changed. If there is no entry in the property_history table yet, add one, but not to the property_changes table. If there is one in property_history, if one of the watched fields has changed, add another record, and add one to the property_changes table. Actually, let's have it be an upsert into the property_changes table.

If there is a problem while processing a file, send an email to an admin. Assume there is a .env file that contains an array of admin email addresses to email, delimited by comma and optionally a space. Also send a text message using Twilio. Again, assume in the .env that there is an admin's SMS number.

## Technical parameters of what to build

- The project should be built with the latest version of node. Use vite if desired.
- Assume 10.3.28-MariaDB-1:10.3.28+maria~focal
- Write tests. Use vitest. Please write unit and integration tests as needed.
