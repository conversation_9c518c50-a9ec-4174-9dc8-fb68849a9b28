{"0": {"title": "Current Market Comparison", "description": "", "labelPlacement": "top_label", "descriptionPlacement": "below", "button": {"type": "text", "text": "Submit", "imageUrl": ""}, "fields": [{"type": "name", "id": 1, "label": "Name", "adminLabel": "", "isRequired": true, "size": "medium", "errorMessage": "", "nameFormat": "advanced", "inputs": [{"id": "1.2", "label": "Prefix", "name": "", "choices": [{"text": "Mr.", "value": "Mr.", "isSelected": false, "price": ""}, {"text": "Mrs.", "value": "Mrs.", "isSelected": false, "price": ""}, {"text": "Miss", "value": "Miss", "isSelected": false, "price": ""}, {"text": "Ms.", "value": "Ms.", "isSelected": false, "price": ""}, {"text": "Dr.", "value": "Dr.", "isSelected": false, "price": ""}, {"text": "Prof.", "value": "Prof.", "isSelected": false, "price": ""}, {"text": "Rev.", "value": "Rev.", "isSelected": false, "price": ""}], "isHidden": true, "inputType": "radio"}, {"id": "1.3", "label": "First", "name": "", "placeholder": "First"}, {"id": "1.4", "label": "Middle", "name": "", "isHidden": true}, {"id": "1.6", "label": "Last", "name": "", "placeholder": "Last"}, {"id": "1.8", "label": "Suffix", "name": "", "isHidden": true}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "above", "subLabelPlacement": "above", "placeholder": "", "cssClass": "cmc_name", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "displayOnly": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false}, {"type": "email", "id": 2, "label": "Email", "adminLabel": "", "isRequired": true, "size": "large", "errorMessage": "", "inputs": null, "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "email", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "emailConfirmEnabled": "", "displayOnly": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false}, {"type": "phone", "id": 3, "label": "Phone", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "phoneFormat": "standard", "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "phone", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "form_id": "", "productField": "", "displayOnly": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false}, {"type": "select", "id": 7, "label": "Property Type", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": true, "price": ""}, {"text": "Single Family - Detached", "value": "Single Family - Detached", "isSelected": false, "price": ""}, {"text": "Patio Home", "value": "Patio Home", "isSelected": false, "price": ""}, {"text": "Townhouse", "value": "Townhouse", "isSelected": false, "price": ""}, {"text": "Apartment Style/Flat", "value": "Apartment Style/Flat", "isSelected": false, "price": ""}, {"text": "Gemini/Twin Home", "value": "Gemini/Twin Home", "isSelected": false, "price": ""}, {"text": "Mfg/Mobile Housing", "value": "Mfg/Mobile Housing", "isSelected": false, "price": ""}, {"text": "Modular/Pre-Fab", "value": "Modular/Pre-Fab", "isSelected": false, "price": ""}, {"text": "Loft Style", "value": "Loft Style", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "proptype", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "select", "id": 10, "label": "Interior SqFt", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": true, "price": ""}, {"text": "Less than 400", "value": "0-400", "isSelected": false, "price": ""}, {"text": "401-500", "value": "401-500", "isSelected": false, "price": ""}, {"text": "501-600", "value": "501-600", "isSelected": false, "price": ""}, {"text": "601-700", "value": "601-700", "isSelected": false, "price": ""}, {"text": "701-800", "value": "701-800", "isSelected": false, "price": ""}, {"text": "801-900", "value": "801-900", "isSelected": false, "price": ""}, {"text": "901-1000", "value": "901-1000", "isSelected": false, "price": ""}, {"text": "1001-1200", "value": "1001-1200", "isSelected": false, "price": ""}, {"text": "1201-1400", "value": "1201-1400", "isSelected": false, "price": ""}, {"text": "1401-1600", "value": "1401-1600", "isSelected": false, "price": ""}, {"text": "1601-1800", "value": "1601-1800", "isSelected": false, "price": ""}, {"text": "1801-2000", "value": "1801-2000", "isSelected": false, "price": ""}, {"text": "2001-2200", "value": "2001-2200", "isSelected": false, "price": ""}, {"text": "2201-2400", "value": "2201-2400", "isSelected": false, "price": ""}, {"text": "2401-2600", "value": "2401-2600", "isSelected": false, "price": ""}, {"text": "2601-2800", "value": "2601-2800", "isSelected": false, "price": ""}, {"text": "2801-3000", "value": "2801-3000", "isSelected": false, "price": ""}, {"text": "3001-3500", "value": "3001-3500", "isSelected": false, "price": ""}, {"text": "3501-4000", "value": "3501-4000", "isSelected": false, "price": ""}, {"text": "4001-4500", "value": "4001-4500", "isSelected": false, "price": ""}, {"text": "4501-5000", "value": "4501-5000", "isSelected": false, "price": ""}, {"text": "5001-5500", "value": "5001-5500", "isSelected": false, "price": ""}, {"text": "5501-6000", "value": "5501-6000", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "sqft", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "select", "id": 8, "label": "Bedrooms", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": true, "price": ""}, {"text": "1", "value": "1", "isSelected": false, "price": ""}, {"text": "2", "value": "2", "isSelected": false, "price": ""}, {"text": "3", "value": "3", "isSelected": false, "price": ""}, {"text": "4", "value": "4", "isSelected": false, "price": ""}, {"text": "5", "value": "5", "isSelected": false, "price": ""}, {"text": "6", "value": "6", "isSelected": false, "price": ""}, {"text": "7", "value": "7", "isSelected": false, "price": ""}, {"text": "8", "value": "8", "isSelected": false, "price": ""}, {"text": "9", "value": "9", "isSelected": false, "price": ""}, {"text": "10", "value": "10", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "beds", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "select", "id": 9, "label": "Bathrooms", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": true, "price": ""}, {"text": "1", "value": "1", "isSelected": false, "price": ""}, {"text": "2", "value": "2", "isSelected": false, "price": ""}, {"text": "3", "value": "3", "isSelected": false, "price": ""}, {"text": "4", "value": "4", "isSelected": false, "price": ""}, {"text": "5", "value": "5", "isSelected": false, "price": ""}, {"text": "6", "value": "6", "isSelected": false, "price": ""}, {"text": "7", "value": "7", "isSelected": false, "price": ""}, {"text": "8", "value": "8", "isSelected": false, "price": ""}, {"text": "9", "value": "9", "isSelected": false, "price": ""}, {"text": "10", "value": "10", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "baths", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "select", "id": 11, "label": "Interior Stories", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": true, "price": ""}, {"text": "1", "value": "1", "isSelected": false, "price": ""}, {"text": "2", "value": "2", "isSelected": false, "price": ""}, {"text": "3", "value": "3", "isSelected": false, "price": ""}, {"text": "4", "value": "4", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "stories", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "select", "id": 12, "label": "Year Built", "adminLabel": "", "isRequired": false, "size": "large", "errorMessage": "", "inputs": null, "choices": [{"text": "Select One", "value": "", "isSelected": false, "price": ""}, {"text": "1940", "value": "1940", "isSelected": false, "price": ""}, {"text": "1941", "value": "1941", "isSelected": false, "price": ""}, {"text": "1942", "value": "1942", "isSelected": false, "price": ""}, {"text": "1943", "value": "1943", "isSelected": false, "price": ""}, {"text": "1944", "value": "1944", "isSelected": false, "price": ""}, {"text": "1945", "value": "1945", "isSelected": false, "price": ""}, {"text": "1946", "value": "1946", "isSelected": false, "price": ""}, {"text": "1947", "value": "1947", "isSelected": false, "price": ""}, {"text": "1948", "value": "1948", "isSelected": false, "price": ""}, {"text": "1949", "value": "1949", "isSelected": false, "price": ""}, {"text": "1950", "value": "1950", "isSelected": false, "price": ""}, {"text": "1951", "value": "1951", "isSelected": false, "price": ""}, {"text": "1952", "value": "1952", "isSelected": false, "price": ""}, {"text": "1953", "value": "1953", "isSelected": false, "price": ""}, {"text": "1954", "value": "1954", "isSelected": false, "price": ""}, {"text": "1955", "value": "1955", "isSelected": false, "price": ""}, {"text": "1956", "value": "1956", "isSelected": false, "price": ""}, {"text": "1957", "value": "1957", "isSelected": false, "price": ""}, {"text": "1958", "value": "1958", "isSelected": false, "price": ""}, {"text": "1959", "value": "1959", "isSelected": false, "price": ""}, {"text": "1960", "value": "1960", "isSelected": false, "price": ""}, {"text": "1961", "value": "1961", "isSelected": false, "price": ""}, {"text": "1962", "value": "1962", "isSelected": false, "price": ""}, {"text": "1963", "value": "1963", "isSelected": false, "price": ""}, {"text": "1964", "value": "1964", "isSelected": false, "price": ""}, {"text": "1965", "value": "1965", "isSelected": false, "price": ""}, {"text": "1966", "value": "1966", "isSelected": false, "price": ""}, {"text": "1967", "value": "1967", "isSelected": false, "price": ""}, {"text": "1968", "value": "1968", "isSelected": false, "price": ""}, {"text": "1969", "value": "1969", "isSelected": false, "price": ""}, {"text": "1970", "value": "1970", "isSelected": false, "price": ""}, {"text": "1971", "value": "1971", "isSelected": false, "price": ""}, {"text": "1972", "value": "1972", "isSelected": false, "price": ""}, {"text": "1973", "value": "1973", "isSelected": false, "price": ""}, {"text": "1974", "value": "1974", "isSelected": false, "price": ""}, {"text": "1975", "value": "1975", "isSelected": false, "price": ""}, {"text": "1976", "value": "1976", "isSelected": false, "price": ""}, {"text": "1977", "value": "1977", "isSelected": false, "price": ""}, {"text": "1978", "value": "1978", "isSelected": false, "price": ""}, {"text": "1979", "value": "1979", "isSelected": false, "price": ""}, {"text": "1980", "value": "1980", "isSelected": false, "price": ""}, {"text": "1981", "value": "1981", "isSelected": false, "price": ""}, {"text": "1982", "value": "1982", "isSelected": false, "price": ""}, {"text": "1983", "value": "1983", "isSelected": false, "price": ""}, {"text": "1984", "value": "1984", "isSelected": false, "price": ""}, {"text": "1985", "value": "1985", "isSelected": false, "price": ""}, {"text": "1986", "value": "1986", "isSelected": false, "price": ""}, {"text": "1987", "value": "1987", "isSelected": false, "price": ""}, {"text": "1988", "value": "1988", "isSelected": false, "price": ""}, {"text": "1989", "value": "1989", "isSelected": false, "price": ""}, {"text": "1990", "value": "1990", "isSelected": false, "price": ""}, {"text": "1991", "value": "1991", "isSelected": false, "price": ""}, {"text": "1992", "value": "1992", "isSelected": false, "price": ""}, {"text": "1993", "value": "1993", "isSelected": false, "price": ""}, {"text": "1994", "value": "1994", "isSelected": false, "price": ""}, {"text": "1995", "value": "1995", "isSelected": false, "price": ""}, {"text": "1996", "value": "1996", "isSelected": false, "price": ""}, {"text": "1997", "value": "1997", "isSelected": false, "price": ""}, {"text": "1998", "value": "1998", "isSelected": false, "price": ""}, {"text": "1999", "value": "1999", "isSelected": false, "price": ""}, {"text": "2000", "value": "2000", "isSelected": false, "price": ""}, {"text": "2001", "value": "2001", "isSelected": false, "price": ""}, {"text": "2002", "value": "2002", "isSelected": false, "price": ""}, {"text": "2003", "value": "2003", "isSelected": false, "price": ""}, {"text": "2004", "value": "2004", "isSelected": false, "price": ""}, {"text": "2005", "value": "2005", "isSelected": false, "price": ""}, {"text": "2006", "value": "2006", "isSelected": false, "price": ""}, {"text": "2007", "value": "2007", "isSelected": false, "price": ""}, {"text": "2008", "value": "2008", "isSelected": false, "price": ""}, {"text": "2009", "value": "2009", "isSelected": false, "price": ""}, {"text": "2010", "value": "2010", "isSelected": false, "price": ""}, {"text": "2011", "value": "2011", "isSelected": false, "price": ""}, {"text": "2012", "value": "2012", "isSelected": false, "price": ""}, {"text": "2013", "value": "2013", "isSelected": false, "price": ""}, {"text": "2014", "value": "2014", "isSelected": false, "price": ""}, {"text": "2015", "value": "2015", "isSelected": false, "price": ""}, {"text": "2016", "value": "2016", "isSelected": false, "price": ""}, {"text": "2017", "value": "2017", "isSelected": false, "price": ""}, {"text": "2018", "value": "2018", "isSelected": false, "price": ""}, {"text": "2019", "value": "2019", "isSelected": false, "price": ""}, {"text": "2020", "value": "2020", "isSelected": false, "price": ""}], "formId": "1", "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "built", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "conditionalLogic": "", "productField": "", "enablePrice": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "enableChoiceValue": true, "displayOnly": ""}, {"type": "hidden", "id": 4, "label": "Lat", "adminLabel": "", "isRequired": false, "size": "medium", "errorMessage": "", "inputs": null, "formId": "1", "description": "", "allowsPrepopulate": true, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "", "inputName": "Latitude", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "displayOnly": ""}, {"type": "hidden", "id": 5, "label": "Lng", "adminLabel": "", "isRequired": false, "size": "medium", "errorMessage": "", "inputs": null, "formId": "1", "description": "", "allowsPrepopulate": true, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "", "inputName": "Longitude", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "displayOnly": ""}, {"type": "hidden", "id": 6, "label": "Address", "adminLabel": "", "isRequired": false, "size": "medium", "errorMessage": "", "inputs": null, "formId": "1", "description": "", "allowsPrepopulate": true, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "", "inputName": "Address", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "displayOnly": ""}], "version": "2.1.3", "id": "1", "useCurrentUserAsAuthor": true, "postContentTemplateEnabled": false, "postTitleTemplateEnabled": false, "postTitleTemplate": "", "postContentTemplate": "", "lastPageButton": null, "pagination": null, "firstPageCssClass": null, "subLabelPlacement": "below", "cssClass": "", "enableHoneypot": false, "enableAnimation": false, "save": {"enabled": false, "button": {"type": "link", "text": "Save and Continue Later"}}, "limitEntries": false, "limitEntriesCount": "", "limitEntriesPeriod": "", "limitEntriesMessage": "", "scheduleForm": false, "scheduleStart": "", "scheduleStartHour": "", "scheduleStartMinute": "", "scheduleStartAmpm": "", "scheduleEnd": "", "scheduleEndHour": "", "scheduleEndMinute": "", "scheduleEndAmpm": "", "schedulePendingMessage": "", "scheduleMessage": "", "requireLogin": false, "requireLoginMessage": "", "is_active": "1", "date_created": "2017-03-25 16:33:04", "is_trash": "0", "confirmations": [{"id": "58d69bc00f5a8", "name": "Default Confirmation", "isDefault": true, "type": "page", "message": "", "url": "", "pageId": 9999, "queryString": "PropType={Property Type:7:value}&Beds={Bedrooms:8:value}&Bathrooms={Bathrooms:9:value}&SquareFeet={Interior SqFt:10:value}&IntStories={Interior Stories:11:value}&Latitude={Lat:4:value}&Longitude={Lng:5}&Address={Address:6:value}&YearBuilt={Year Built:12:value}", "disableAutoformat": false, "conditionalLogic": []}], "notifications": [{"isActive": true, "id": "58d69bc00caf6", "name": "Admin Notification", "service": "wordpress", "event": "form_submission", "to": "{admin_email}", "toType": "email", "bcc": "", "subject": "New submission from {embed_url}", "message": "Someone has just completed your Current Market Comparison lead form. To see the report provided to this lead, click the link below.\r\n\r\n<a href=\"{embed_url}current-market-comparison/?PropType={Property Type:7:value}&Beds={Bedrooms:8:value}&Bathrooms={Bathrooms:9:value}&SquareFeet={Interior SqFt:10:value}&IntStories={Interior Stories:11:value}&Latitude={Lat:4:value}&Longitude={Lng:5}&Address={Address:6:value}&YearBuilt={Year Built:12:value}\">View Your Current Market Comparison</a>\r\n\r\n{all_fields}", "from": "{admin_email}", "fromName": "", "replyTo": "", "routing": null, "conditionalLogic": null, "disableAutoformat": false}, {"isActive": true, "id": "58f0ef6f4dd32", "name": "User Notification", "service": "wordpress", "event": "form_submission", "to": "2", "toType": "field", "bcc": "", "subject": "Thanks for Your Interest in {embed_url}", "message": "Thanks for checking your home’s market comparison. If you save the link provided in this email and come back to it in the future you will get updated results.\r\n\r\n<a href=\"{embed_url}current-market-comparison/?PropType={Property Type:7:value}&Beds={Bedrooms:8:value}&Bathrooms={Bathrooms:9:value}&SquareFeet={Interior SqFt:10:value}&IntStories={Interior Stories:11:value}&Latitude={Lat:4:value}&Longitude={Lng:5}&Address={Address:6:value}&YearBuilt={Year Built:12:value}\">View Your Current Market Comparison</a>\r\n\r\n{all_fields}\r\n\r\nThanks", "from": "{admin_email}", "fromName": "", "replyTo": "{admin_email}", "routing": null, "conditionalLogic": null, "disableAutoformat": false}]}, "version": "*******"}