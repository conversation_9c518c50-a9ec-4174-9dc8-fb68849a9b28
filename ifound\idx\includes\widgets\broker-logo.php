<?
/**
 * iFoundBrokerLogo class
 *
 * Display Broker Logo in widget areas.
 *
 * @package iFOUND
 * @since 3.1.4
 *
 * @link https://stackoverflow.com/questions/6480462/how-to-pre-populate-the-sms-body-text-via-an-html-link
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFound_Broker_Logo extends WP_Widget {
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Broker Logo', 
			array( 'description' => 'Add a broker logo to any widget area.' )
		);
		
	}

	public static function get_broker_logo($options = []) {
		$options = wp_parse_args($options, [
			'ensure_href_is_url' => false,
			'agent_id'           => null,
		]);

		// If this is a teams website, the agent might be allowed to set their own logo. If so, we show it here.
		// Otherwise, we use the single agent broker logo farther below.
		$already_looked_up_broker_logo = false;
		if (apply_filters('ifound_has_feature', 'teams')) {
			if (iFoundTeams::new_hookless()->allow_override_broker_logo()) {
				$agent_id = $options['agent_id'] ?: apply_filters('ifound_get_agent_id', null);
				if ($agent_id) {
					$broker_logo = get_user_meta($agent_id, 'ifa-broker-logo', true);
					if ($broker_logo) {
						$already_looked_up_broker_logo = true;
					}
				}
			}
		}
		if (!$already_looked_up_broker_logo) {
			$ifound_agent = get_option( 'ifound_agent' );

			if( ! empty( $ifound_agent['broker_logo'] ) ) {
				$broker_logo = $ifound_agent['broker_logo'];
			}
		}
		if ($options['ensure_href_is_url']) {
			if ($broker_logo) {
				$broker_logo = iFoundUtil::new_hookless()->ensure_href_is_url($broker_logo);
			}
		}
		return $broker_logo;
	}

	/**
	 * Front-end display of widget.
	 *
	 * @see WP_Widget::widget()
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */

	public function widget( $args, $instance ) {
	    $broker_logo = static::get_broker_logo();

		if ($broker_logo) {

			echo $args['before_widget']; ?>

			<div class="ifound-broker-logo">

				<div class="widgets-wrap">

					<div class="header-broker">

						<div class="ifound-wrap">

							<img class="broker-logo" src="<?= esc_url( $broker_logo ) ?>" alt="broker-logo"/>

						</div>

					</div>

				</div>

			</div><?

			echo $args['after_widget'];
			
		}
		
	}
	
}
