FROM ifoundagent:wordpress

# Generate the wp-config.php file
RUN su www-data -s /bin/bash -c "wp core config --skip-check --dbhost=wpdb --dbuser=wordpress --dbpass=wordpress --dbname=profoundwp"

# Requred third-party plugins
ADD profoundwp/install_files/use-shortcodes-in-sidebar-widgets /www/wp-content/plugins/use-shortcodes-in-sidebar-widgets/
ADD profoundwp/install_files/widget-shortcode /www/wp-content/plugins/widget-shortcode/
ADD profoundwp/install_files/login-with-ajax /www/wp-content/plugins/login-with-ajax/

ADD profoundwp/install_files/jquery-ui-1.12.1 /www/

# Upload images that are hard-coded for houseschandler.com
ADD profoundwp/install_files/uploads /www/wp-content/uploads/

# Fix ownership
RUN chown -R www-data:www-data /www/wp-content

# Now that we're done adding stuff to www/, declare as a Docker volume, so we can access from nginx
VOLUME /www
