/* From: https://codepen.io/RobotsPlay/pres/pyNLdL */

.dropdown {
	position: relative;
	font-size: 14px;
	color: #333;
	float: left;
	min-width: 150px;
	margin-right: 6px;

	.dropdown-list {
		padding: 12px;
		background: #fff;
		position: absolute;
		top: 30px;
		left: 2px;
		right: 2px;
		box-shadow: 0 1px 2px 1px rgba(0, 0, 0, .15);
		transform-origin: 50% 0;
		transform: scale(1, 0);
		transition: transform .15s ease-in-out .15s;
		max-height: 66vh;
		overflow-y: scroll;
		min-width: 100%;
	}

	.dropdown-option {
		display: block;
		padding: 4px 0;
		opacity: 0;
		transition: opacity .15s ease-in-out;
		white-space: nowrap;
	}

	.dropdown-label {
		display: block;
		/*height: 30px;*/
		background: #fff;
		border: 1px solid #ccc;
		padding: 0 12px;
		line-height: 2;
		cursor: pointer;
		border-color: #8c8f94;
		border-radius: 3px;

		&:before {
			content: '▼';
			float: right;
		}
	}

	&.on {
		.dropdown-list {
			transform: scale(1, 1);
			transition-delay: 0s;

			.dropdown-option {
				opacity: 1;
				transition-delay: .2s;
			}
		}

		.dropdown-label:before {
			content: '▲';
		}
	}

	[type="checkbox"] {
		position: relative;
		top: -1px;
		margin-right: 4px;
	}

	.check-all-button {
		background: transparent;
		color: blue;
		text-decoration: underline;
		border: none;
		cursor: pointer;
		margin: 4px 0;
		padding-left: 0;
	}

	.operator {
		margin: 4px 0;
	}
}
