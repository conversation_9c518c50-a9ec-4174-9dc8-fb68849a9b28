import { promises as fs } from 'fs'
import path from 'path'

export interface Config {
	database: {
		host: string
		port: number
		user: string
		password: string
		database: string
	}
	testDatabase: {
		host: string
		port: number
		user: string
		password: string
		database: string
	}
	adminEmails: string[]
	sendgrid: {
		apiKey: string
		fromEmail: string
		fromName: string
	}
	twilio: {
		accountSid: string
		apiKey: string
		apiSecret: string
		fromNumber: string
		adminSmsNumber: string
	}
	mls_systems: {
		[key: string]: {
			mls_classes: {
				[key: string]: {
					property_changes_table_name: string
					property_history_table_name: string
					data_directory: string
				}
			}
		}
	}
}

let cachedConfig: Config | null = null

export async function loadConfig(configPath?: string): Promise<Config> {
	if (cachedConfig) {
		return cachedConfig
	}

	const configFile = configPath || path.join(process.cwd(), 'config.js')

	try {
		// Check if config file exists
		await fs.access(configFile)

		// Dynamic import for ES modules
		const configModule = await import(configFile)
		const config = configModule.default as Config

		// Validate required fields
		validateConfig(config)

		cachedConfig = config
		return config
	} catch (error) {
		if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
			throw new Error(
				`Config file not found: ${configFile}. Please copy config.example.js to config.js and update with your settings.`
			)
		}
		throw new Error(
			`Failed to load config: ${error instanceof Error ? error.message : 'Unknown error'}`
		)
	}
}

function validateConfig(config: any): asserts config is Config {
	const errors: string[] = []

	if (!config.database) {
		errors.push('database configuration is required')
	} else {
		if (!config.database.host) errors.push('database.host is required')
		if (!config.database.port) errors.push('database.port is required')
		if (!config.database.user) errors.push('database.user is required')
		if (!config.database.password) errors.push('database.password is required')
		if (!config.database.database) errors.push('database.database is required')
	}

	// testDatabase is optional for non-test environments
	if (config.testDatabase) {
		if (!config.testDatabase.host)
			errors.push('testDatabase.host is required when testDatabase is provided')
		if (!config.testDatabase.port)
			errors.push('testDatabase.port is required when testDatabase is provided')
		if (!config.testDatabase.user)
			errors.push('testDatabase.user is required when testDatabase is provided')
		if (!config.testDatabase.password)
			errors.push('testDatabase.password is required when testDatabase is provided')
		if (!config.testDatabase.database)
			errors.push('testDatabase.database is required when testDatabase is provided')
	}

	if (!Array.isArray(config.adminEmails) || config.adminEmails.length === 0) {
		errors.push('adminEmails must be a non-empty array')
	}

	if (!config.sendgrid) {
		errors.push('sendgrid configuration is required')
	} else {
		if (!config.sendgrid.apiKey) errors.push('sendgrid.apiKey is required')
		if (!config.sendgrid.fromEmail) errors.push('sendgrid.fromEmail is required')
		if (!config.sendgrid.fromName) errors.push('sendgrid.fromName is required')
	}

	if (!config.twilio) {
		errors.push('twilio configuration is required')
	} else {
		if (!config.twilio.accountSid) errors.push('twilio.accountSid is required')
		if (!config.twilio.apiKey) errors.push('twilio.apiKey is required')
		if (!config.twilio.apiSecret) errors.push('twilio.apiSecret is required')
		if (!config.twilio.fromNumber) errors.push('twilio.fromNumber is required')
		if (!config.twilio.adminSmsNumber) errors.push('twilio.adminSmsNumber is required')
	}

	if (!config.mls_systems || typeof config.mls_systems !== 'object') {
		errors.push('mls_systems configuration is required')
	} else {
		for (const [mlsKey, mlsConfig] of Object.entries(config.mls_systems)) {
			if (!mlsConfig.mls_classes || typeof mlsConfig.mls_classes !== 'object') {
				errors.push(`mls_systems.${mlsKey}.mls_classes is required`)
			} else {
				for (const [classKey, classConfig] of Object.entries(mlsConfig.mls_classes)) {
					if (!classConfig.property_changes_table_name)
						errors.push(
							`mls_systems.${mlsKey}.mls_classes.${classKey}.property_changes_table_name is required`
						)
					if (!classConfig.property_history_table_name)
						errors.push(
							`mls_systems.${mlsKey}.mls_classes.${classKey}.property_history_table_name is required`
						)
					if (!classConfig.data_directory)
						errors.push(`mls_systems.${mlsKey}.mls_classes.${classKey}.data_directory is required`)
				}
			}
		}
	}

	if (errors.length > 0) {
		throw new Error(`Invalid configuration:\n${errors.map(e => `  - ${e}`).join('\n')}`)
	}
}

// Export a default instance for convenience
let defaultConfig: Config | null = null

export default {
	async load(configPath?: string): Promise<Config> {
		if (!defaultConfig) {
			defaultConfig = await loadConfig(configPath)
		}
		return defaultConfig
	},

	get database() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.database
	},

	get adminEmails() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.adminEmails
	},

	get sendgrid() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.sendgrid
	},

	get twilio() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.twilio
	},

	get testDatabase() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.testDatabase
	},

	get mls_systems() {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		return defaultConfig.mls_systems
	},

	getMlsSystem(mlsKey: string) {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		const mlsSystem = defaultConfig.mls_systems[mlsKey]
		if (!mlsSystem) {
			throw new Error(
				`MLS system '${mlsKey}' not found in configuration. Available systems: ${Object.keys(defaultConfig.mls_systems).join(', ')}`
			)
		}
		return mlsSystem
	},

	getMlsClass(mlsKey: string, classKey: string) {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		const mlsSystem = defaultConfig.mls_systems[mlsKey]
		if (!mlsSystem) {
			throw new Error(
				`MLS system '${mlsKey}' not found in configuration. Available systems: ${Object.keys(defaultConfig.mls_systems).join(', ')}`
			)
		}
		const mlsClass = mlsSystem.mls_classes[classKey]
		if (!mlsClass) {
			throw new Error(
				`MLS class '${classKey}' not found in system '${mlsKey}'. Available classes: ${Object.keys(mlsSystem.mls_classes).join(', ')}`
			)
		}
		return mlsClass
	},

	listMlsClasses(mlsKey: string) {
		if (!defaultConfig) throw new Error('Config not loaded. Call load() first.')
		const mlsSystem = defaultConfig.mls_systems[mlsKey]
		if (!mlsSystem) {
			throw new Error(
				`MLS system '${mlsKey}' not found in configuration. Available systems: ${Object.keys(defaultConfig.mls_systems).join(', ')}`
			)
		}
		return Object.keys(mlsSystem.mls_classes)
	},
}
