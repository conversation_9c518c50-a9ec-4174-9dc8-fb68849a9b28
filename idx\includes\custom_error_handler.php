<?php

// https://www.php.net/manual/en/function.str-starts-with.php#125913
if (!function_exists('str_starts_with')) {
	function str_starts_with($haystack, $needle) {
		return (string)$needle !== '' && strncmp($haystack, $needle, strlen($needle)) === 0;
	}
}

// As we upgrade PHP from 7.3 to 8.1, what once was a notice has been changed to a warning, this warning about
// undefined array indexes. I prefer to use syntax like
//   if ($_GET['mykey'])
// rather than alternatives like
//   1. if (isset($_GET['mykey']))
//   2. if ($_GET['mykey'] ?? null)
// I could perhaps do the null coalescing operator if I were starting a codebase from scratch, but we have too many
// uses to change now.
// Oh, and same with using $myvar->some_property.
set_error_handler(function ($errno, $errstr, $errfile, $errline) {
	if ($errno === 2) {
		// Ignore these errors by returning not false.
		if (str_starts_with($errstr, 'Undefined array key')) {
			return true;
		} else if (str_starts_with($errstr, 'Undefined property')) {
			return true;
		}
	}
	// The current error being handled will be handled normally by returning false.
	return false;
});
