<?php

namespace Profound\MLS;

class MlsClass {
	const RES = 'res';
	const RENTALS = 'rentals';
	const LAND = 'land';

	public static function getGenericNames() {
		return array(self::RES, self::RENTALS, self::LAND);
	}

	protected static function getMlsClassInfo() {
		$res = 'res';
		$rentals = 'rentals';
		$land = 'land';
		// Only MLS classes that we use should be listed here, so some are
		// commented out as reminders for the future when we do use them.
		$mapping = array(
			'armls' => array(
				'A' => $res,
				'B' => $rentals,
				'C' => $land,
			),
			'armls_spark' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
			'glvarnv' => array(
				'1' => $res,
				// '9' => $rentals,
				// '5' => $land,
			),
			'mredil' => array(
				'ResidentialProperty' => $res,
				'RentalHome' => $rentals,
				'LotsAndLand' => $land,
			),
			'paaraz' => array(
				'A' => $res,
				'F' => $rentals,
				'B' => $land,
			),
			'paaraz_mlg' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
			'sdcrca' => array(
				'RE_1' => $res,
				'RT_4' => $rentals,
				'LN_3' => $land,
			),
			'sndmls' => array(
				'9' => $res,
				'18' => $rentals,
				'12' => $land,
			),
			'tarmlsaz' => array(
				'A' => $res,
				'E' => $rentals,
				'B' => $land,
			),
			'trendmls' => array(
				'RES' => $res,
				 'RNT' => $rentals,
				 'LOT' => $land,
			),
			'brightmls' => array(
				'RESI' => $res,
				'RLSE' => $rentals,
				'LAND' => $land,
			),
			'naar' => array(
				'A' => $res,
				'B' => $rentals,
				'C' => $land,
			),
			'naar_spark' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
			'cabor' => array(
				'A' => $res,
				'B' => $rentals,
				'C' => $land,
			),
			'recolorado' => array(
				'RESI' => $res,
				'INCOME' => $rentals,
				'LAND' => $land,
			),
			'recolorado_mlg' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
			'wardex' => array(
				'ResidentialProperty' => $res,
				'Rental' => $rentals,
				'LotsAndLand' => $land,
			),
			'wmar' => array(
				'A' => $res,
				'D' => $rentals,
				'B' => $land,
			),
			'crmls' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
			'realtracs' => array(
				'res' => $res,
				'rentals' => $rentals,
				'land' => $land,
			),
		);
		return $mapping;
	}

	public static function getGenericMlsClassName($mls, $mlsClass) {
		$mapping = self::getMlsClassInfo();
		if(isset($mapping[$mls][$mlsClass]))
			$generic = $mapping[$mls][$mlsClass];
		else
			$generic = $mlsClass;

		return $generic;
	}

	public static function getGenericMlsClassesForMls($mls) {
		$mapping = self::getMlsClassInfo();
		return array_values($mapping[$mls]);
	}
}
