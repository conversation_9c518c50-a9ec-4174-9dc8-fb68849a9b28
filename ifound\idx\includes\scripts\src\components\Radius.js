// This is for radius searches, aka nearby.

import React, { useState, useEffect } from 'react';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import ReactTooltip from 'react-tooltip';
import PropTypes from 'prop-types';
import urljoin from 'url-join';
import DistanceSelect from './DistanceSelect';
import AddressAutoComplete from './AddressAutoComplete';
import AddOwnedListing from './AddOwnedListing';
import { isSpaceOnlyString } from '../lib/utils';

import 'react-tabs/style/react-tabs.css';

const hintStyles ={ fontStyle: 'italic', fontSize: '0.9em' };

function Radius(props) {
	const [address, setAddress] = useState('');
	// We make sure the address is valid, we make the user select from the Google auto complete choices.
	const [chosenAddressFields, setChosenAddressFields] = useState(null);
	const [radius, setRadius] = useState(0.5);
	const [ownedListing, setOwnedListing] = useState(null);
	const [shouldFocus, setShouldFocus] = useState(false);
	const [ownedListingMode, setOwnedListingMode] = useState('select');

	useEffect(() => {
		if (shouldFocus) {
			setShouldFocus(false);
		}
	}, [shouldFocus]);

	function fillAddressFromContact() {
		setAddress(props.contact.address);
		setShouldFocus(true);
	}

	function onAddressAutoComplete(place, addr) {
		const lat = place.geometry.location.lat();
		const lng = place.geometry.location.lng();
		setChosenAddressFields({
			address: addr,
			lat,
			lng,
		});
	}

	function addRadius() {
		const { address: addr, lat, lng } = chosenAddressFields;
		props.addRadius(radius, lat, lng, addr);
		setAddress('');
		setChosenAddressFields(null);
		props.onDone();
	}

	function useOwnedListingRadius() {
		props.addRadius(radius, ownedListing.lat, ownedListing.lng, ownedListing.address, ownedListing.mls_id);
		props.onDone();
	}

	function showDistanceAndAddButton(onAddCallback) {
		return <div style={{ marginTop: '10px' }}>
			<DistanceSelect distance={radius} onChange={setRadius} />
			<div className="radius-add-button" onClick={onAddCallback}>
				<i className="fal fa-plus-circle" aria-hidden="true"></i>
				{' '}
				Add
			</div>
		</div>
	}

	// We don't actually do anything with radius search criteria. We send a signal that we just want to show the map
	// and show a pin on the map.
	function dropPin() {
		props.dropPin(chosenAddressFields);
	}

	function showAddressInput() {
		return <div className="address-container">
			{props.contact && !isSpaceOnlyString(props.contact.address) &&
				<div>
					<button className="button button-small" onClick={fillAddressFromContact}>Fill with contact address</button>
				</div>
			}
			<div>Address:</div>
			{chosenAddressFields
				? <div style={{...hintStyles}}>
					<div>{address}</div>
					<div>
						<ReactTooltip />
						<button className="button button-small" onClick={() => setChosenAddressFields(null)}>Change</button>
						{props.allowDropPin &&
							<>
								<button
									className="button button-small button-secondary"
									style={{ marginLeft: '10px' }}
									onClick={dropPin}
								>Drop Pin</button>
								{' '}
								<span style={{ fontSize: 'smaller' }}><i className="fa fa-question-circle" data-tip="Show this location on the map now"></i></span>
							</>
						}
					</div>
				</div>
				: <AddressAutoComplete
					googleMapsLoadedPromise={props.googleMapsLoadedPromise}
					address={address}
					onChange={setAddress}
					onAutoComplete={onAddressAutoComplete}
					shouldFocus={shouldFocus}
				/>
			}
			{!chosenAddressFields && <div style={{...hintStyles}}>
				<em>Type an address and select from the choice(s)</em>
			</div>}
			{chosenAddressFields && showDistanceAndAddButton(addRadius)}
		</div>
	}

	function chooseOwnedListing(event) {
		const mlsIdString = event.target.value;
		const ownedListing = props.contact.owned_listings.find(x => x.mls_id === parseInt(mlsIdString, 10));
		setOwnedListing(ownedListing);
	}

	function addOwnedListing(ownedListing, paredOwnedListing) {
		props.addOwnedListingToContact(props.contact, paredOwnedListing);
		// After the components re-render, select the new contact in the dropdown.
		setTimeout(() => {
			setOwnedListingMode('select');
			const foundOwnedListing = props.contact.owned_listings.find(x => x.mls_id === paredOwnedListing.mls_id);
			setOwnedListing(foundOwnedListing);
		}, 0);
	}

	function showOwnedListings() {
		const endpoint = urljoin(props.addOwnedListingEndpointBase, props.contact.id.toString(), 'owned_listings');

		return <div>
			<div style={{ display: 'flex', marginBottom: '10px' }}>
				<div style={{ marginRight: '10px' }}>
					<label htmlFor="owned_listing_mode_select">
						<input style={{ width: 'initial' }} type="radio" checked={ownedListingMode === 'select'} onChange={() => setOwnedListingMode('select')} id="owned_listing_mode_select" /> Select existing
					</label>
				</div>
				<div>
					<label htmlFor="owned_listing_mode_add">
						<input style={{ width: 'initial' }} type="radio" checked={ownedListingMode === 'add'} onChange={() => setOwnedListingMode('add')} id="owned_listing_mode_add" /> Add new
					</label>
				</div>
			</div>
			{ownedListingMode === 'select'
				? <>
					{!!props.contact.owned_listings.length && <select value={ownedListing ? ownedListing.mls_id : 'DEFAULT'} onChange={chooseOwnedListing}>
						<option value="DEFAULT" disabled="disabled">Select...</option>
						{props.contact.owned_listings.map(x => <option value={x.mls_id} key={x.mls_id}>{x.address} ({x.mls_id})</option>)}
					</select>}
					{!props.contact.owned_listings.length && <>No owned listings</>}
					{ownedListing && showDistanceAndAddButton(useOwnedListingRadius)}
				</>
				: <>
					<AddOwnedListing
						endpoint={endpoint}
						nonce={props.nonce}
						onAdd={addOwnedListing}
					/>
				</>
			}
		</div>
	}

	return <div>
		{props.isAdmin && !props.contact && <div style={{...hintStyles, visibility: address ? 'hidden' : 'visible'}}>
			<em>Tip: choose a contact to auto-populate</em>
		</div>}
		{props.contact && <Tabs>
			<TabList>
				<Tab>Type address</Tab>
				<Tab>Owned Listings</Tab>
			</TabList>

			<TabPanel>
				{showAddressInput()}
			</TabPanel>
			<TabPanel>
				{showOwnedListings()}
			</TabPanel>
		</Tabs>}
		{!props.contact && showAddressInput(false)}
	</div>
}

Radius.propTypes = {
	contact: PropTypes.object,
	isAdmin: PropTypes.bool,
	googleMapsLoadedPromise: PropTypes.object,
	onDone: PropTypes.func,
	addOwnedListingEdnpointBase: PropTypes.string,
	nonce: PropTypes.string,
	addOwnedListingToContact: PropTypes.func,
};

export default Radius;
