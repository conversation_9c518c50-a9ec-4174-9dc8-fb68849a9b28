// This is for "multibox" searches, which is a single text box where the user can type a single search term and get back
// potential results related to address, school, city, zip, subdivision (neighborhood), and MLS ID.

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import AsyncSelect from 'react-select/async';
import { components } from 'react-select';
import {
	debouncedLoadOptionsCallback,
	buildLabelMap,
	getGroupIcon,
	noOptionsMessage,
	HiddenComponent,
	GroupHeadingMaker,
	OptionMaker,
} from '../lib/multibox';

const mlsClass = 'Residential';
const bucketsThatGoToPdp = ['mls_id', 'address'];
const labelMap = buildLabelMap('quick');

function MultiboxQuickSearch({ endpoint, icons, isMultipleStateMls }) {
	const [inputValue, setInputValue] = useState('');
	const [selectedChoice, setSelectedChoice] = useState(null);
	const [isSearchable, setIsSearchable] = useState(true);

	function onChange(selections, actionType) {
		// I'm not sure what pop-value means. It occurs at least when the text box is empty and the user hits backspace.
		if (['clear', 'remove-value', 'pop-value'].includes(actionType.action)) {
			setSelectedChoice(null);
			setIsSearchable(true);
		} else {
			const selection = selections[0];
			if (bucketsThatGoToPdp.includes(selection.type)) {
				window.open(selection.value.url, '_blank').focus();
				return;
			}
			setSelectedChoice(selection);
			setIsSearchable(false);
		}
	}

	const components = {
		// We want it to look like a text search box, so we intentionally make it look not like a dropdown. Remove the
		// multi-value-remove button to try to make it clear that there can only be one selection at a time.
		MultiValueRemove: HiddenComponent,
		DropdownIndicator: HiddenComponent,
		IndicatorSeparator: HiddenComponent,
		GroupHeading: GroupHeadingMaker(icons),
		Option: OptionMaker(bucketsThatGoToPdp, mlsClass, icons),
	};
	if (!inputValue) {
		components.Menu = HiddenComponent;
	}

	let hiddenInputs = null
	if (selectedChoice) {
		if (isMultipleStateMls && ['city', 'school_district'].includes(selectedChoice.type)) {
			const stringValue = selectedChoice.type === 'school_district'
				? selectedChoice.value.value
				: selectedChoice.value;
			const [itemValue, stateOrProvince] = stringValue.split(/, ?/)
			hiddenInputs = <>
				<input type="hidden" name={selectedChoice.type} value={itemValue} />
				<input type="hidden" name="state" value={stateOrProvince} />
			</>
		} else {
			hiddenInputs = <input
				type="hidden"
				name={selectedChoice.type === 'school' ? selectedChoice.value.type : selectedChoice.type}
				value={selectedChoice.type === 'school' ? selectedChoice.value.value : selectedChoice.value}
			/>
		}
	}

	return <>
		<AsyncSelect
			isClearable={true}
			isMulti
			defaultOptions={[]}
			// We use the callback form of this prop, as opposed to returning a promise, just for the sake of being able to
			// debounce it.
			loadOptions={(inputValue, cb) => debouncedLoadOptionsCallback(
				inputValue,
				endpoint,
				labelMap,
				mlsClass,
				cb
			)}
			placeholder={'City, zip, school, neighborhood, address, MLS ID'}
			inputValue={inputValue}
			onInputChange={setInputValue}
			onChange={onChange}
			isSearchable={isSearchable}
			value={selectedChoice}
			components={components}
			noOptionsMessage={noOptionsMessage}
			className="ifound-react-select-multibox-quicksearch-container"
			classNamePrefix="ifound-react-select-multibox-quicksearch"
		/>
		{hiddenInputs}
	</>
}

MultiboxQuickSearch.propTypes = {
	endpoint: PropTypes.string.isRequired,
	icons: PropTypes.shape({
		active: PropTypes.string.isRequired,
		closed: PropTypes.string.isRequired,
		subject: PropTypes.string.isRequired,
	}),
	isMultipleStateMls: PropTypes.bool,
};

export default MultiboxQuickSearch;
