jQuery( document ).ready( function( $ ) {

	$( '.save-campaign' ).on( 'click', function( e ) {

		if( ! validate() ) return;

		tinyMCE.triggerSave();

		var input = new Object();
		input.form 		= $( '#ifound-save-alert-form' ).serialize();
		input.params 			= $( '#ifound-dynamic-form' ).serialize();
		input.stats 			= $( '#ifound-stats-form' ).serialize();
		input.contact_id 		= $( '#contact_id' ).val();
		input.extra_map_data	= $( '#extra-map-data' ).val();

		var toEmailVal = $('#to_email').val();
		if(toEmailVal && toEmailVal !== 'none') input.to_email = toEmailVal;

		if(toEmailVal.includes('(Spouse)')) {
			$('.ifound_email_editor').html().replace('{ContactFirstName}', '{SpouseFirstName}');
		}

		jQuery.ajax ( {
			url : save_alert.endpoint,
			type : 'post',
			data : {
				action : 'save_alert_ajax',
				input : input,
				save_alert_nonce : save_alert.nonce,
			},
			beforeSend: function() {
				$('.while-we-wait').addClass('active');
				$( '#save-campaign-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
				$( '.success-msg' ).fadeOut( 100 );
				$( '.success-sub-msg' ).html('');
				$( '.failed-msg' ).fadeOut( 100 );
			},
			success: function( response ) {
				$( '#save-campaign-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response.class );
				$('.while-we-wait').removeClass('active');
				if( response.save_this_id > 0 ) {
					save_this_id = response.save_this_id;
					$( '.liondesk-button' ).attr( 'disabled', false );
					$( '.success-msg' ).fadeIn( 100 );
					$( '.success-sub-msg' ).html('You can view the campaign by <a href="' + response.campaign_link + '">clicking here</a>');
				} else {
					$( '.failed-msg' ).fadeIn( 100 );
				}
			},
			dataType:'json'
		});
	});

	function validate(){
		/** Clean up all the empty inputs from previos attempt to save. */
		$( '.empty-input' ).removeClass( 'empty-input' );
		if( $( '#contact_id' ).val().length < 1 ) {
			$( '#contact_autocomplete' ).addClass( 'empty-input' ).focus();
			alert( 'You must select a contact to save this campaign.' );
			return false;
		}
		var valid = true;
		$( '.ifound-validate' ).each( function(){
			if( $( this ).val().length < 1 && ! $(this).hasClass('ifound-feel-disabled') ) {
				$( this ).addClass( 'empty-input' ).focus();
				valid = false;
			}
		});
		if (!valid) {
			alert( 'Complete REQUIRED fields to save this campaign.' );
			return false;
		}
		if ($('#do_sms').is(':checked') && !$('#to_sms').val()) {
			alert('You must first add a mobile phone number to this contact (and then reload this page) before checking the Include text message(s) checkbox');
			return false;
		}
		return true;
	}

    var dateFormat = "yy-mm-dd",
    	from = $( "#start_date" )
   	.datepicker({
       	defaultDate: "+1w",
       	changeMonth: true,
      	changeYear: true,
      	numberOfMonths: 1,
		dateFormat: dateFormat
  	})
   	.on( "change", function() {
      	to.datepicker( "option", "minDate", getDate( this ) );
		to.datepicker( "option", "dateFormat", dateFormat );
   	}),
   	to = $( "#end_date" ).datepicker({
		defaultDate: "+1w",
		changeMonth: true,
		changeYear: true,
        numberOfMonths: 1,
		dateFormat: dateFormat
  	})
	.on( "change", function() {
      	from.datepicker( "option", "maxDate", getDate( this ) );
		from.datepicker( "option", "dateFormat", dateFormat );
	});

    function getDate( element ) {
      	var date;
      	try {
			date = $.datepicker.parseDate( dateFormat, element.value );
      	} catch( error ) {
			date = null;
      	}

      	return date;
    }

	$( '.recurring' ).on( 'change', function(){
		$( '.set-disable' ).attr( 'disabled', false );
		if( $( this ).val() == 'no' ){
			$( '.set-disable' ).attr( 'disabled', true );
		}
	});

	$( '#do_post' ).on( 'change', function(){
		$( '.create-post' ).slideToggle();
	});

	var $doEmail = $('#do_email');
	$doEmail.on( 'change', function(){
		$('.email-validate').toggleClass('ifound-feel-disabled');
		$('.ifound-wp-editor-wrapper').toggleClass('ifound-feel-disabled');
	});
	if (!$doEmail.is(':checked')) {
		$('.email-validate').toggleClass('ifound-feel-disabled');
		$('.ifound-wp-editor-wrapper').toggleClass('ifound-feel-disabled');
	}
	var $doSms = $('#do_sms');
	$doSms.on( 'change', function(){
		$('.mphone-validate').toggleClass('ifound-feel-disabled');
	});
	$('#sms_template_id').on('change', function(event) {
		var template = save_alert.sms_campaign_templates.find(function(t) {
			return t.id === event.target.value;
		});
		if (template) {
			$('.sms_template_body').text(template.body);
		}
	});

	$( '.liondesk-button' ).on( 'click', function() {
		if( save_this_id == false ) return;
		tinyMCE.triggerSave();
		var link = email_editor.link;
		var body = $( '#ifound_email_editor' ).val();
		var s = 'subjecttext=' + $( '#email-subject' ).val();
		var b = '&bodytext=' + encodeURIComponent( body.replace( '{AlertLink}', link.replace( 'save_this_id', save_this_id ) ) );
		var url = email_editor.liondesk + s + b;
		window.open(url);
	});

});
