[PHP]

zend_extension=xdebug.so

[xdebug]

; Enable connecting to debug client
; This is xdebug 2
;xdebug.remote_enable = on
; This is xdebug 3
xdebug.mode = debug

; Should every request try to hit debug client, or wait until manually triggered via URL parameter ?XDEBUG_SESSION_START=DOCKER_XDEBUG
; This is xdebug 2
;xdebug.remote_autostart = on
; This is xdebug 3
xdebug.start_with_request = yes

; remote_connect_back tries to guess IP address. We will hardcode via remote_host
xdebug.remote_connect_back = off

; Docker host machine
; This is xdebug 2
;xdebug.remote_host = **********
; This is xdebug 3
xdebug.client_host = ${PHP_XDEBUG_HOST}
; This is xdebug 2
;xdebug.remote_port = 9001
; This is xdebug 3
xdebug.client_port = ${PHP_XDEBUG_PORT}

;xdebug.idekey = "DOCKER_XDEBUG"

; If you're having trouble connecting, uncomment to see xdebug connection attempts in logs folder
; This is xdebug 2
;xdebug.remote_log = /var/log/xdebug.log
; This is xdebug 3, see https://xdebug.org/docs/upgrade_guide
; REMINDER! Make sure this path can be written to, i.e. www-data owns it.
; By default, in the Docker images I'm using, it's not writeable.
;xdebug.log = /var/log/xdebug.log
; 10 is a crazy amount of logging. You probably want 5 or 7.
; See: https://xdebug.org/docs/all_settings#log_level
;xdebug.log_level = 10

; profiling
; This is xdebug 2
;xdebug.profiler_enable = ${PHP_XDEBUG_PROFILER_ENABLE}
; This is xdebug 3, probably comment out the xdebug.mode = debug above
;xdebug.mode = profile
;xdebug.profiler_output_dir = /srv/storage/logs/xdebug_profiler

; Timeout the server will wait to see if xdebug is running
; This is xdebug 2
;xdebug.remote_timeout=200
; This is xdebug 3
xdebug.connect_timeout_ms=200
