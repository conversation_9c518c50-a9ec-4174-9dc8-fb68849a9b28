<?php

function guess_city_from_referrer() {
	if (isset($_SERVER['HTTP_REFERER'])) {
		$host = get_host();
		switch ($host) {
			case "www.scottsdale.com":
				return "scottsdale";
			case "www.tempe.com":
				return "tempe";
		}
	}
	return "mesa";
}

function get_host() {
	return parse_url($_SERVER["HTTP_REFERER"], PHP_URL_HOST);
}

function get_forward_subdir() {
	switch (get_host()) {
		// Looks like we need to support both www and no subdomain, at least for
		// mesa.com.
		case "www.mesa.com":
		case "mesa.com":
		case "www.scottsdale.com":
		case "www.tempe.com":
			return "realestate";
		default:
			return "";
	}
}

?>

<html>
<head>
	<script src="http://code.jquery.com/jquery-1.9.0.min.js"></script>
	<script src="budget-search-widget.js" type="text/javascript"></script>
	<link rel="stylesheet" href="budget-search-widget.css" type="text/css"></link>
	<title>Search by budget widget</title>
</head>
<body>
	<div class='budget-search-widget' data-host="<?= get_host() ?>" data-forward-subdir="<?= get_forward_subdir() ?>" data-city="<?= guess_city_from_referrer() ?>">
		<div class='header'>Search homes by budget</div>
		<div class='one-line'>
			<div class='pfp-label'>Preferred monthly payment:</div>
			<div class='input-prepend'>
				<span class='add-on'>$</span>
				<input class="pfp" type="text" placeholder="1500" value="1500"></input>
			</div>
			<div><input class='search btn' type="submit" value="Search!"></input></div>
		</div>
	</div>
</body>
</html>