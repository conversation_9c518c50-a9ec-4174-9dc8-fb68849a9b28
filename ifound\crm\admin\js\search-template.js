jQuery(document).ready(function($) {
	
	var templates = search_template.json;

	$('.do-search-template').on('click', function() {
		
		$('#search-template').removeClass('required-red');

		var action = $(this).attr('id');
		var removeClasses = 'fa-search-plus fa-save fa-edit fa-trash-alt fa-exclamation-triangle';
		var spinDiv = $(this).find( '.far' );
		var spinner = 'fa-spinner fa-spin';
		
		var input = new Object();
		 	input.meta_id = $('#meta-id').val();
		 	input.search_template = $('#search-template').val();
			input.search_criteria = $('#ifound-dynamic-form').serialize();

		if( ! input.search_template.length ) {
			$('#search-template').addClass('required-red').focus();
			return false;
		}

		if( ! input.meta_id.length && action !== 'add_search_template' ) {
			alert('You must click on a selection from the list of suggestions to perform this action.');
			$('#search-template').val('').focus();
			return false;
		}
		
		$.ajax ( {
			url : search_template.endpoint,
			type : 'post', 
			data : {
				action : action,
				input : input,
				search_template_nonce : search_template.nonce,
			},
			beforeSend: function() {
				$('.while-we-wait').addClass('active');
				$('#load_search_template').removeClass('required-red');
				$(spinDiv).removeClass(removeClasses).addClass(spinner);
				if(action == 'load_search_template' || action == 'delete_search_template'){
					$.clearPolygons();
				}
			},
			success: function( response ) {
				$('.while-we-wait').removeClass('active');
				$( spinDiv ).removeClass( spinner ).addClass( response.class );
				if(response.success) {
					if(action == 'add_search_template'){
						templates.push(response.item);
					}
					if(action == 'delete_search_template'){
						// clear the map and form
						templates = $.grep(templates, function(e){ 
     						return e.key != input.meta_id; 
						});
						$('#search-template').val('');
						$('#meta-id').val('');
						$('.dynamic-input').remove();
					}
					if(action == 'load_search_template'){
						// Fill the form and add polygons
						$.setDynamicForm(response.data,'#ifound-dynamic-form');
					}		
				}
			},
			dataType:'json'
		});
	});

	$('#search-template').on('click', function(){
		if( templates.length  ){
		    $(this).autocomplete({
		      	minLength: 0,
		      	source: templates,
		      	select: function( event, ui ) {
		        	$( "#search-template" ).val( ui.item.value );
					$( "#meta-id" ).val( ui.item.key );
		        	return false;
				} 
		    });
		}
	});
	
});