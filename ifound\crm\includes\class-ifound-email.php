<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

use <PERSON><PERSON>\Url\Url;

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

/**
 * iFoundEmail Class
 *
 * @since 1.0.0
 * @since 2.5.1 Major update - Revamp the entire email sending system.
 */

class iFoundEmail extends iFoundCrm{
	use UtilTrait;
	use NewHooklessTrait;

	public static $the_post_type = 'ifound_email';
	public static $the_taxonomy = 'ifound_email_type';
	public static $success_message_base = 'Successfully Sent to: ';
	public static $CAMPAIGN_ALERT = 'campaign_alert';
	public static $INSTANT_UPDATE = 'instant_update';
	public static $INSTANT_UPDATE_RECENTLY_CLOSED = 'instant_update_recently_closed';
    public static $INSTANT_UPDATE_TYPES;
	public static $WEBSITE_VISIT_FOLLOW_UP_ENABLED_SETTING_KEY = 'website_visit_follow_up_enabled';
	public static $WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE = 'website_visit_follow_up';
	public static $WEBSITE_VISIT_FOLLOW_UP_SLUG = 'website-visit-follow-up';
	public static $WEBSITE_VISIT_FOLLOW_UP_LABEL = 'Website Visit Follow-Up';

    // This regex finds URLs in a string, meant for href=, and excludes src=.
	// Referring to this part at the beginning: (?<!src=[\'"])
	// That's a negative look-behind that says "If the string contains xyz, then ignore it", where the xyz
	// represents the src="..." part of <img> tag, that has a single or double quote.
	// This is based on the regex I used in tests/ifound/crm/includes/ClassIfoundEmailTest.php
    private static $url_not_images = '#(?<!src=[\'"])\b(https?://[^,\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|/)))#';

	public $post_type;
	private $query_var		= 'ifound-email';
	private $label_name 	= 'Email Template';
	private	$label_names	= 'Email Templates';
	private $taxonomy;
	private	$taxonomy_title	= ' Type';
	protected $taxonomys 	= array( 'ifound_email_type' );
	private $id_ext 		= '';
	protected $merge_tags;
	private $from_name;
	private $from_email;
	private $contact_id;
	private $log_type;
	private $bcc_email;
    // $this->post_id could be a campaign ID or contact ID
	private $post_id;
	private $to_name;
	private $to_email;
	private $content;
	private $subject;
	private $email_styles;
	// If default_templates is true, the agent's default header, footer, and signature will be used. Otherwise, the
	// templates to use will be pulled from the campaign, unless there is no campaign set ($this->post_id), in which
	// case there will be no header, footer, or signature used.
	private $default_templates;
	private $requires_unsubscribe_header;
	private $reply_to;

    public static function static_init() {
	    static::$INSTANT_UPDATE_TYPES = [static::$INSTANT_UPDATE, static::$INSTANT_UPDATE_RECENTLY_CLOSED];
    }

	/**
	 * init iFoundEmail class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$this->post_type = static::$the_post_type;
		$this->taxonomy = static::$the_taxonomy;

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'post_type'));
			add_action('init', array($this, 'taxonomy'));

			add_filter('parse_query', array($this, 'filter_query'));
			add_action('restrict_manage_posts', array($this, 'filter_dropdown'), 20, 1);
			add_action('edit_form_after_title', array($this, 'email_subject_input'));
			add_action('save_post_ifound_email', array($this, 'save_post'));
			add_filter('map_meta_cap', array($this, 'map_meta_cap'), 10, 4);
			add_filter('manage_contacts_posts_columns', array($this, 'add_email_columns'));

			add_action('ifound_email', array($this, 'email'), 10, 4);
			add_action('ifound_property_alert_email', array($this, 'property_alert_email'));
			add_action('ifound_agent_notification_email', array($this, 'agent_notification_email'));
			add_action('ifound_campaign_alert_email', array($this, 'campaign_alert_email'));
			add_action('ifound_instant_update_email', array($this, 'instant_update_email'), 10, 2);
			add_action('ifound_contact_visit_email', array($this, 'contact_visit_email'), 10, 2);
			add_action('ifound_activity_report_email', array($this, 'activity_report_email'), 10, 1);
			add_action('ifound_upcoming_reminders_email', array($this, 'upcoming_reminders_email'), 10, 1);
			add_action('ifound_team_lead_email', array($this, 'team_lead_email'), 10, 2);
			add_action('ifound_drip_campaign_customer_email', array($this, 'drip_campaign_customer_email'), 10, 1);
			add_action('ifound_website_visit_follow_up_email', array($this, 'website_visit_follow_up'), 10, 1);
			add_action('ifound_prepare_email', array($this, 'prepare_email'), 10, 1);

			add_action('ifound_email_editor', array($this, 'email_editor'), 10, 1);
			add_action('ifound_email_dropdown', array($this, 'email_dropdown'), 10, 6);

			add_action('wp_mail_failed', array($this, 'log_mailer_errors'), 10, 1);

			add_filter('ifound_default_email_tags', array($this, 'default_email_tags'));
			add_action('ifound_merge_tag_select', array($this, 'merge_tag_select'), 10, 1);

			add_action('ifound_bulk_email', array($this, 'bulk_email'), 10, 6);

			add_action( 'pre_get_posts', array( $this, 'private_email_templates_query'), 10, 1 );

			// before_delete_post has 2 parameters as of WP 5.5, but our code will still work with 1.
			add_action('before_delete_post', [$this, 'before_delete_post']);

			add_action('ifound_create_email_template', [$this, 'create_email_template']);
			add_filter('ifound_default_email_templates', [$this, 'email_templates'], 10, 0);
		}
	}

	/**
	 *	Post Type
	 *
	 *	@since 1.0.0
	 *
	 *	@link https://codex.wordpress.org/Function_Reference/register_post_type
	 */

	public function post_type() {

		$capabilities = $this->is_site_admin() ? array() : $this->team_email_capabilities();

		$vals = [
			'labels' => [
				'name' 			=> __( $this->label_names ),
				'singular_name' => __( $this->label_name ),
				'add_new'		=> __( 'Add New ' . $this->label_name ),
				'add_new_item'	=> __( 'Add New ' . $this->label_name ),
				'edit_item'		=> __( 'Edit ' . $this->label_name ),
				'new_item'		=> __( 'New ' . $this->label_name ),
				'view_item'		=> __( 'View ' . $this->label_name ),
				'view_items'	=> __( 'View ' . $this->label_names ),
				'search_items'	=> __( 'Search ' . $this->label_names ),
				'all_items'		=> __( $this->label_names ),
				'attributes'	=> __(  $this->label_name . ' Attributes' ),
				'menu_name'     => __('iFound Email'),
			],
			'query_var'				=> $this->query_var,
			'show_ui'				=> true,
			'show_in_nav_menus'		=> false,
			'show_in_admin_bar'		=> false,
			'public' 				=> false,
			'has_archive' 			=> false,
			'hierarchical' 			=> true,
			'supports'				=> array( 'title', 'editor', 'author' )
		];
		// Update 2: I wrote the previous stuff before I knew about our EZ-Admin plugin. It might (or might not) be
		// responsible for all the headaches I was dealing with. Just noting it for whoever might need to investigate in
		// the future.
		//
		// Update: a problem with having the line commented out, in other words, not having the 'show_in_menu' key
		// is that we end up with a separate, disjointed navigation menu for Email Templates, which is confusing for the
		// user. So, we'll put the Email Templates menu item within the 'Contact Manager' menu, and only create a separate
		// iFound Email top level menu when the user is actually creating a new email template (as in, on the
		// post-new.php page). So, it might be slightly confusing when there is a new, different menu when creating a
		// new email template. But this is a rare occurrence.
		//
		// I'm adding this comment because I spent hours debugging this.
		// I could not get the "Add new" functionality working when the following line was not commented out.
		// When I'd visit http://ifoundteam.test/wp-admin/post-new.php?post_type=ifound_email,
		// I'd get a 403 and see: Sorry, you are not allowed to access this page.
		// I could go to http://ifoundteam.test/wp-admin/post-new.php?post_type=private_contact and it would
		// work as expected. It was hard to find the difference.
		// The following line makes sense to me; it seems like it's trying to put this custom post type as a
		// menu underneath our 'Contact Manager' menu. However, the exact problem comes when Wordpress internally
		// calls user_can_access_admin_page(). (The following specifics are for Wordpress 5.5)
		// In the method user_can_access_admin_page(), there is this:
		//
		// if ( empty( $parent ) ) {
		//   ...
		//   foreach ( array_keys( $_wp_submenu_nopriv ) as $key ) {
		// 	   if ( isset( $_wp_submenu_nopriv[ $key ][ $pagenow ] ) ) {
		// 	     return false;
		//     }
		// 	   ...
		//   }
		// }
		// I don't really understand that logic. It seems that there is some list it keeps of pages that
		// shouldn't be accessed if they don't have a parent, which I guess makes sense, but I thought we
		// were basically able to use 'show_in_menu' to specify that we wanted some other menu to be the parent.
		// I suppose it actually works as expected when the user is not adding a new email template; for example
		// when they are viewing the list of email templates, the 'Email Templates' menu item would be under the
		// Contact Manager menu as desired. So it's just the 'Add New' concept that has this problem.
		// So, with this line commented out, it will end up defaulting to true. A consequence is that we will
		// have a separate menu for email templates.
		// I also realize that the 'Saved Campaigns' menu item has the same issue, or would regarding the 'Add
		// New' ability, except the 'create_posts' capability is set to 'do_not_allow' in our code. So I don't
		// see a way around this.
		if (strpos($this->current_url(), 'post-new.php?post_type=' . $this->post_type) !== false) {
			// Do nothing, as explained above.
		} else {
			$vals['show_in_menu'] = $this->crm_menu();
		}

		register_post_type( $this->post_type, array_merge( $vals, $capabilities ) );

	}

	private function team_email_capabilities() {

		return array(
			'capability_type'		=> 'ifound_email',
			'capabilities' 			=> array(
				'edit_post'          	=> 'edit_ifound_email',
				'read_post'          	=> 'read_ifound_email',
				'delete_post'        	=> 'delete_ifound_email',
				'edit_posts'         	=> 'edit_ifound_emails',
				'edit_others_posts'  	=> 'edit_others_ifound_emails',
				'delete_posts'        	=> 'delete_ifound_emails',
				'publish_posts'      	=> 'publish_ifound_emails',
				'create_posts'       	=> 'create_ifound_emails',
			)
		);

	}

	/**
	 *	Taxonomy
	 *
	 *	@since 1.0.0
	 *
	 *	@link https://codex.wordpress.org/Function_Reference/register_taxonomy
	 */

	public function taxonomy() {

		// Add new taxonomy, make it hierarchical (like categories)
		$labels = array(
			'name'              => _x( $this->label_name . $this->taxonomy_title, iFound::$default_context, 'ifound' ),
			'singular_name'     => _x( $this->label_name . $this->taxonomy_title, iFound::$default_context, 'ifound' ),
			'search_items'      => __( 'Search ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'all_items'         => __( 'All ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'parent_item'       => __( 'Parent ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'parent_item_colon' => __( 'Parent ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'edit_item'         => __( 'Edit ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'update_item'       => __( 'Update ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'add_new'           => __( 'Add New ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'add_new_item'      => __( 'Add New ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'new_item_name'     => __( 'New ' . $this->label_name . $this->taxonomy_title, 'ifound' ),
			'menu_name'         => __( $this->label_name . $this->taxonomy_title, 'ifound' ),
		);

		$args = array(
			'hierarchical'      => true,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_in_menu'		=> false,
			'show_admin_column' => true,
			'show_in_nav_menus'	=> false,
			'query_var'         => true,
			'public' 			=> false,
			'show_tagcloud'		=> false,
			'rewrite' 			=> false,
		);
		if (!$this->is_site_admin()) {
			$args['capabilities'] = [
				'assign_terms' => 'edit_ifound_emails',
				// These are commented out for now because I don't think we want users creating new terms.
				// It might be fine if they create terms that have a parent type of one of our types, but I'm not sure
				// so we won't risk it.
				// 'edit_terms'   => 'edit_ifound_emails',
				// 'manage_terms' => 'edit_ifound_emails',
			];
		}

		register_taxonomy( $this->taxonomy, array( $this->post_type ), $args );

	}

	public function add_email_columns( $columns ) {

		$columns['author'] = __( 'Author', 'ifound' );

		return $columns;

	}

	// Don't show a user email templates that belong to other users.
	public function private_email_templates_query( $query ) {
		global $pagenow;
		if ($pagenow !== 'edit.php' || $this->support()) {
			return $query;
		}

		global $typenow;
		if( current_user_can( 'edit_others_posts' ) && $typenow !== $this->post_type ) {
			return $query;
		}

		if ($typenow === $this->post_type) {
			$query->set('author__in', iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids());
		}

		return $query;
	}

	/**
	 * Save Post
	 *
	 * This publishes and updates email templates.
	 *
	 * @since 1.0.0
	 * @since 2.4.2 Add condition for original_publish.
	 *
	 * @param int $post_id The ID of the post being saved.
	 */

	public function save_post( $post_id ){

		global $post;

		if ( isset( $_POST['original_publish'] ) ) {
			update_post_meta( $post_id, 'subject', sanitize_text_field( $_POST['email_subject'] ) );

			// We want all admins to share the same email templates. They should all be owned by the super admin, with
			// user ID 1.
			if ($this->util()->user_has_admin_role(get_current_user_id())) {
			    // Avoid infinite update loop. Unhook, then re-hook after.
				remove_action('save_post_ifound_email', [$this, 'save_post']);
				wp_update_post([
					'ID' => $post_id,
					'post_author' => 1,
				]);
				add_action('save_post_ifound_email', [$this, 'save_post']);
			}
		}

	}

	/**
	 * Email Subject input
	 *
	 * The input in the email edit screen for the subject.
	 *
	 * @since 1.0.0
	 * @since 2.5.2 Rename method email_subject_input
	 */

	public function email_subject_input() {

		global $post;

		if( $post->post_type !== $this->post_type ) return; ?>

		<style>
			#subjectdiv{
				position: relative;
			}

			#poststuff #subjectwrap{
				border: 0;
				padding: 0;
			}

			#poststuff .email_subject{
				padding: 3px 8px;
				font-size: 1.7em;
				line-height: 100%;
				height: 1.7em;
				width: 100%;
				outline: 0;
				margin: 0 0 3px;
				background-color: #fff;
			}

		</style>

		<table class="form-table">

			<tbody>

				<tr>

					<th scope="row"><? _e( 'Email Subject', 'ifound' ); ?></th>

					<td>

						<input
							type="text"
							class="regular-text"
							name="email_subject"
							id="subject"
							value="<? echo get_post_meta( get_the_ID(), 'subject', true ); ?>"
							spellcheck="true"
							autocomplete="off"
						/>
                        <div style="font-size: small; color: gray;">You may use merge tags here</div>

					</td>

				</tr>

				<tr>

					<th<? _e( '>Merge Tags', 'ifound' ); ?></th>

					<td><? $this->merge_tag_select( $this->default_email_tags() ); ?></td>

				</tr>

			</tbody>

		</table><?

	}

	public function activity_tracking_link_input() {
		$max = 100;
		$args = [
			'numberposts' => $max,
			'post_status' => ['publish'],
		];
		$relevant_values_mapper = function($p) {
			return [
				'title' => $p->post_title,
				'value' => htmlspecialchars(json_encode([
					'title' => $p->post_title,
					'link' => get_permalink($p),
				])),
			];
		};
		$pages = wp_get_recent_posts(wp_parse_args([
			'post_type' => 'page',
		], $args), OBJECT);
		$mappedPages = array_map($relevant_values_mapper, $pages);
		$posts = wp_get_recent_posts(wp_parse_args([
			'post_type' => 'post',
		], $args), OBJECT);
		$mappedPosts = array_map($relevant_values_mapper, $posts);
		?>
		<select name="activity_tracking_link" class="activity-tracking-link-select email-validate">
			<option selected="selected" disabled="disabled" value="">Select one</option>
			<option value="ifound_activity_tracking_link">Custom link</option>
			<optgroup label="Recent pages">
				<?php
				foreach($mappedPages as $p) {
					echo "<option value=\"{$p['value']}\">{$p['title']}</option>\n";
				}
				?>
			</optgroup>
			<optgroup label="Recent posts">
				<?php
				foreach($mappedPosts as $p) {
					echo "<option value=\"{$p['value']}\">{$p['title']}</option>\n";
				}
				?>
			</optgroup>
		</select>
		<?php
	}

	/**
	 * Merge Tag select
	 *
	 * The select with all the email merge tags.
	 *
	 * @since 1.0.0
	 *
	 * @param array $options An array of default email tags.
	 */

	public function merge_tag_select( $options ) {

		wp_enqueue_script( 'email_editor_js' ); ?>

		<select name="merge-tags" class="merge-tag-select email-validate" id="merge_tag">

			<option value=""><? _e( 'Select One', 'ifound' ); ?></option><?

			foreach( $options as $key => $value ) { ?>

			<option value="{<? echo $key; ?>}"><? _e( $key, 'ifound' ); ?></option><?

			} ?>

		</select><?

	}

	/**
	 * Email Dropdown
	 *
	 * @since 1.0.0
	 *
	 * @param string $category The category of emails to include.
	 * @param array  $include  The terms to include.
	 * @param string $selected The option to mark as selected.
	 * @param array  $exclude  The IDs to exclude.
	 * @param string $name     The name of the select menu.
	 */

	public function email_dropdown($category, $include = false, $selected = false, $exclude = array(), $name = false,
		$show_option_none = 'Select One'
	) {

        $include_ids = [];
		if( $include ) {
		    $author_ids_str = iFoundadmin::new_hookless()->get_this_user_ids_or_primary_admin_ids_str();

			$include_args = array(
        		'posts_per_page' 	=> -1,
				'author'            => $author_ids_str,
				'post_type' 	 	=> $this->post_type,
		        'post_status'       => 'publish',
		        'fields'			=> 'ids',
        		'tax_query' 	 	=> array(
            		array(
                		'taxonomy' 	=> $this->taxonomy,
                		'field' 	=> 'slug',
                		'terms' 	=> $include,
                		'operator'  => 'IN'
            		)
        		)
    		);

			$include_args 	= array_merge( $include_args, $exclude );
			$include_ids 	= get_posts( $include_args );
            // If no posts were found, the resulting call to wp_dropdown_pages() will not perform a filter, so it will
            // show posts we dont' want it to. This is the only trick I know to fix that. It ends up not even showing
            // a dropdown, which could confuse the user, but should be safer than allowing the wrong item to be
            // selected.
			if (count($include_ids) === 0) {
				$include_ids = [-1];
			}
		}

		$name = $name ? $name : $category;

		$args = array(
			'selected'              => $selected,
			'echo'                  => 1,
			'name'                  => $name,
			'id'                    => 'template_id',
			'class'                 => 'template_id email-validate',
			'show_option_none'      => 'Select One',
            'include'               => $include_ids,
			'post_type' 			=> $this->post_type,
            'authors'               => $author_ids_str,
            'post_status'           => 'publish',
			'show_option_none'		=> $show_option_none,
		);
		wp_dropdown_pages( $args );

	}

	/**
	 * BCC Email
	 *
	 * Email address to bcc.
	 *
	 * @since 3.0.0
	 *
	 * @return string $email The senders email address if email post type. Or default email address if not.
	 */

	public function bcc_email() {
		return empty( $this->agent->bcc_email ) ? false : $this->agent->bcc_email;
	}

	/**
	 * Email Styles
	 *
	 * The CSS styles for Emails.
	 *
	 * @since 2.5.1
	 */

	public function email_styles() {
	    ob_start();
	    ?>

		<style type="text/css">
			body{
				font-size: 18px;
				font-weight: 200;
				line-height: 1.5;
				max-width:600px
			}
			h2{
				font-size: 24px;
				font-weight: 200;
				line-height: 1.5;
			}
			h3{
				font-size: 20px;
				font-weight: 200;
				line-height: 1.5;
			}
			.email_body{
				padding:10px;
			}
			.email_body .wrap{
				padding:10px;
				border:#ccc thin solid;
				border-radius:10px;
			}
			.img-wrap{
				padding:10px;
			}
			.img-wrap img{
				max-height:160px;
				margin:0 auto;
			}
			.underline{
				border:#ccc solid thin;
				width:100%;
			}
			.clear{
				clear:both;
			}
			.heading{
				width:80%;
				float:left;
			}
			.price{
				width:20%;
				float:right;
			}
			.pad-15{
				padding:15px;
			}
			.button{
				padding:7px 15px;
				background:#15B658;
				color:#fff;
				text-decoration:none;
			}
			.button:hover{
				background:#ccc;
				color:#000;
			}
			.decline{
				background:#BF3C3E;
			}
			.center{
				text-align:center;
			}
			.content,
			.signature,
			.email-footer{
				padding-top: 40px;
				clear: both;
			}
			.footer-text,
			.footer-text a{
				font-size:10px !important;
				text-align:center;
			}

			@media only screen and (max-width: 500px){
				body{
					font-size: 16px;
				}
				h3{
					font-size:18px;
				}
				.heading,
				.price{
					width:100%;
					float:none;
				}
				.footer-text,
				.footer-text a{
					font-size:6px !important;
				}
			}
		</style>
        <?

        return ob_get_clean();
	}

	/**
	 * Email Headers
	 *
	 * The Headers for Emails.
	 *
	 * @since 2.5.1
	 *
	 * @return array $headers An array of email headers.
	 */

	public function email_headers($options = []) {
		$options = wp_parse_args($options, [
			'is_rich_text' => true,
		]);

		$headers[] = 'MIME-Version: 1.0';
		if ($options['is_rich_text']) {
			$headers[] = 'Content-type: text/html; charset=UTF-8';
		} else {
			$headers[] = 'Content-type: text/plain; charset=UTF-8';
		}
		$headers[] = 'From: ' . $this->from_name . ' <' . $this->from_email . '>';
		if ($this->reply_to) {
			$headers[] = "Reply-To: {$this->reply_to}";
		}

		if( $this->bcc_email ) {
			$headers[] = 'Bcc: ' . $this->bcc_email;
		}
		// Add one-click unsubscribe due to new Google/Yahoo requirements.
		if ($options['unsubscribe_url'] ?? $this->requires_unsubscribe_header) {
			$unsubscribe_url = $options['unsubscribe_url'] ?? $this->unsubscribe_url();
			$headers[] = 'List-Unsubscribe-Post: List-Unsubscribe=One-Click';
			$headers[] = "List-Unsubscribe: <{$unsubscribe_url}>";
		}

		return $headers;

	}

	/**
	 * Email Merge Tags
	 *
	 * Replace merge tags from content templates.
	 *
	 * @since 1.0.0
	 *
	 * @param  string $body Unfiltered content tehplate.
	 * @return string $body Filtered content.
	 */

	public function filter_tags( $body ) {
		return $this->do_replacements($body, $this->default_email_tags());
	}

	/**
	 * Default Email Tags
	 *
	 * The email merge tags
	 *
	 * @since 2.5.1
	 *
	 * @return array $email_tags An array of email merge tags.
	 */

	public function default_email_tags($options = []) {
		$email_alert_button_options = $this->util()->pick($options, ['email_type', 'results']);
		$default_email_tags = array(
			'ContactFirstName'		=> get_post_meta( $this->contact_id, 'fname', true ),
			'ContactLastName'		=> get_post_meta( $this->contact_id, 'lname', true ),
			'SpouseFirstName'		=> get_post_meta( $this->contact_id, 'fname_spouse', true ),
			'SpouseLastName'		=> get_post_meta( $this->contact_id, 'lname_spouse', true ),
			'ContactEmail'			=> get_post_meta( $this->contact_id, 'email', true ),
			'ContactHomePhone'		=> get_post_meta( $this->contact_id, 'hphone', true ),
			'ContactWorkPhone'		=> get_post_meta( $this->contact_id, 'wphone', true ),
			'ContactMobilePhone'	=> get_post_meta( $this->contact_id, 'mphone', true ),
			// Don't put a value for ContactStreetName here. The value is grabbed only when needed in do_replacements().
			'ContactStreetName'     => '',
			'ContactLink'			=> $this->contact_button(),
			'AlertLink'				=> $this->email_alert_button($email_alert_button_options),
			'Year' 					=> date( 'Y' ),
			'Unsubscribe'			=> $this->unsubscribe_link(),
			// A value for listings wouldn't make sense here. It isn't needed anyway, as it will be used just to show in
			// the list of possible merge tags.
			'Listings'              => '',
		);
		if ($this->agent) {
			$default_email_tags['AgentName']		= $this->agent->agent_name;
			$default_email_tags['HeadShot'] 		= $this->agent->head_shot;
			$default_email_tags['BrokerName']		= $this->agent->broker_name;
			$default_email_tags['BrokerLogo']		= $this->agent->broker_logo;
			$default_email_tags['StreetAddress']		= $this->agent->street_address;
			$default_email_tags['City']			= $this->agent->city;
			$default_email_tags['State']			= $this->agent->state;
			$default_email_tags['Zip']			= $this->agent->zip;
			$default_email_tags['OfficePhone']		= $this->agent->office_phone;
			$default_email_tags['MobilePhone']		= $this->agent->mobile_phone;
			$default_email_tags['Fax']			= $this->agent->fax;
			$default_email_tags['Email']			= $this->agent->email;
			$default_email_tags['SignatureGraphic']		= $this->agent->signature_graphic;
		}
		if (!wp_doing_cron() && !iFoundTeams::new_hookless()->user_has_team_member_role()) {
			unset($default_email_tags['HeadShot']);
			unset($default_email_tags['BrokerLogo']);
			unset($default_email_tags['SignatureGraphic']);
		}

		return $default_email_tags;

	}

	private function unsubscribe_url($options = []) {
		$url = site_url( '/unsubscribe/' . $this->contact_id );
		$query_args = $options['query_args'] ?: [];
		foreach ($query_args as $key => $query_arg) {
			$query_args[$key] = urlencode($query_arg);
		}
		$url = add_query_arg($query_args, $url);
		return $url;
	}

	/**
	 * Unsunscribe Link
	 *
	 * The <a> for the unsubscribe link.
	 *
	 * @since 2.5.1
	 */

	public function unsubscribe_link($options = []) {
		$url = $this->unsubscribe_url($options);
		return '<a href="' . $url . '">Unsubscribe</a>';
	}

	/**
	 * Contact Button
	 *
	 * The <a> link button in the email to the contact record.
	 *
	 * @since 2.5.30
	 */

	public function contact_button($contact_id = FALSE) {

		return '<a href="' . $this->make_contact_profile_url($contact_id) . '" class="button">View Contact Profile</a>';

	}

	private function make_contact_profile_url($contact_id = FALSE) {
		$uri = '/post.php?action=edit&post=' . ($contact_id ? $contact_id : $this->contact_id);
		return admin_url($uri);
	}

	/**
	 * Email Alert Button
	 *
	 * The <a> link button in the email with the contact_id param.
	 *
	 * @since 1.2.34
	 */

	public function email_alert_button($options = []) {
		$url_string = $this->alert_link() . $this->id_ext;
		$url = Url::fromString($url_string);
		$view_or_review_word = 'view';
		if ($options['email_type'] ?? null) {
			$email_type = $options['email_type'];
			$url = $url->withQueryParameter('et', $email_type);
			if (strpos($email_type, 'instant_update') === 0) {
				$view_or_review_word = 'review';
			}
		}
		$results = $options['results'] ?? null;
		$num = iFoundSaveThis::new_hookless()->get_updated_campaign_listings_results_count_str($results);
		return '<a href="' . $url . '" class="button">View Now - ' . $num . ' properties to '
			. $view_or_review_word . '</a>';
	}

	/**
	 * Alert Link
	 *
	 * The alert link for emails.
	 *
	 * @since 2.5.1
	 * @since 4.1.7 Add default value for detail URL.
	 *
	 * @return string $alert_link A URL for the alert button.
	 */

	public function alert_link() {

		if( has_term( 'property-alert', 'save_type', $this->post_id ) ) {

			$params  = get_post_meta( $this->post_id, 'params', true );

			return apply_filters( 'ifound_get_detail_url', 'saved-property', $params['mls_id'] );

		} else

			return $this->util()->build_post_href($this->post_id, 'view', ['ensure_href_is_url' => true]);

	}

	/**
	 * Header
	 *
	 * The email header template.
	 *
	 * @since 2.5.1
	 * @since 2.5.45 Add condition for default templates.
	 *
	 * @return string $header The HTML template.
	 */

	public function header() {

		if( isset( $this->default_templates ) )
			$post_id = $this->crm->header;

		else
			$post_id = get_post_meta( $this->post_id, 'header', true );

		return $post_id ? get_post($post_id)->post_content : '';

	}

	/**
	 * Footer
	 *
	 * The email footer template.
	 *
	 * @since 2.5.1
	 * @since 2.5.45 Add condition for default templates.
	 *
	 * @return string $footer The HTML template.
	 */

	public function footer() {

		if( isset( $this->default_templates ) )
			$post_id = $this->crm->footer;

		else
			$post_id = get_post_meta( $this->post_id, 'footer', true );

		return $post_id ? get_post($post_id)->post_content : '';

	}

	/**
	 * Signature
	 *
	 * The email signature template.
	 *
	 * @since 2.5.1
	 * @since 2.5.45 Add condition for default templates.
	 *
	 * @return string $signature The HTML template.
	 */

	public function signature() {

		if( isset( $this->default_templates ) )
			$post_id = $this->crm->signature;

		else
			$post_id = get_post_meta( $this->post_id, 'signature', true );

		return $post_id ? get_post($post_id)->post_content : '';

	}

	private function contact_full_name() {
		$fname 	= get_post_meta( $this->contact_id, 'fname', true );
		$lname 	= get_post_meta( $this->contact_id, 'lname', true );
		return iFoundJointContact::new_hookless()->make_full_name([$fname, $lname]);
	}

	private function get_info_from_email_address($contact_id, $email_address) {
        $post_meta = $this->util()->get_single_metas($contact_id, [
            'fname', 'lname', 'email', 'email2',
            'fname_spouse', 'lname_spouse', 'email_spouse', 'email2_spouse',
        ]);
		$fname = '';
		$lname = '';
		$type = 'unknown';
		if (in_array($email_address, [$post_meta['email_spouse'], $post_meta['email2_spouse']])) {
			$fname = $post_meta['fname_spouse'];
			$lname = $post_meta['lname_spouse'];
			$type = 'spouse';
		} else {
			// We use the name of the contact, even if the email doesn't belong explicitly. We do it so that we have
			// values to show in the emails when the replacements are done. Otherwise they'd show up blank.
			$fname = $post_meta['fname'];
			$lname = $post_meta['lname'];
			if (in_array($email_address, [$post_meta['email'], $post_meta['email2']])) {
				$type = 'contact';
			}
		}
		$name = iFoundJointContact::new_hookless()->make_full_name([$fname, $lname]);
		return [
			'fname'    => $fname,
			'lname'    => $lname,
			'name'     => $name,
			'to_email' => "$name <$email_address>",
			'type'     => $type,
		];
	}

	// Ensure all links have the agent ID in them. Only considers links that are on the given domain (host).
	// Currently, considering matching, the port is ignored.
	// Reminder: I have a unit test for this at tests/ifound/crm/includes/ClassIfoundEmailTest.php.
	private function ensure_team_member_links_include_agent_id($string, $host_to_match, $user_id) {
        return $this->ensure_urls_have_query_param($string, $host_to_match, 'aid', $user_id);
	}

	private function ensure_links_include_tracking_pixel($string, $host_to_match, $tpx) {
        return $this->ensure_urls_have_query_param($string, $host_to_match, 'tpx', $tpx);
	}

    private function ensure_urls_have_query_param($string, $host_to_match, $query_param_name, $value) {
	    $change_query_string_fn = function($query_str_parts) use ($query_param_name, $value) {
		    if (isset($query_str_parts[$query_param_name])) {
			    return false;
		    }
		    $query_str_parts[$query_param_name] = $value;
		    return $query_str_parts;
	    };
	    $s = $this->change_links($string, static::$url_not_images, $host_to_match, $change_query_string_fn);
	    return $s;
    }

	/**
	 * Email
	 *
	 * Set up for an email
	 *
	 * @since 1.0.0
	 * @since 1.2.30 Add condition for Bcc Email.
	 * @since 2.0.3 Add comprehensive $log_type for activity logs.
	 * @since 2.1.0 Move function to save last_time here.
	 * @since 2.5.1 Revamp the entire email system.
	 * @since 2.5.44 Check if contact has unsubscribed.
	 * @since 2.5.60 Evaluate bool value returned from wp_mail.
	 * @since 3.0.0  Update to multi-user CRM.
	 *
	 * @param int/bool $save_this_id ID of the email campaign. Or false.
	 * @param string   $email_type 	 The type of email we are sending.
	 */

	public function email( $post_id, $email_type, $extra = NULL, $options = [] ) {
        $options = wp_parse_args($options, [
            'post_id_type' => 'campaign',
        ]);

		if( $options['post_id_type'] === 'contact' ) {
			$this->contact_id = $post_id;
			$this->to_email = get_post_meta($post_id, 'email', true);
		} else  {
			$this->contact_id	= intval( get_post_meta( $post_id, 'contact_id', true ) );
			$this->to_email = get_post_meta($post_id, 'to_email', true);
		}

		if( $this->contact_id && has_term( 'unsubscribed', 'contacts_status', $this->contact_id ) )
			return;

		$user_id 		= $this->crm_post_author( $this->contact_id );
		$is_admin       = $this->util()->user_has_admin_or_super_role($user_id);
		$crm_id 		= $is_admin ? '' : $user_id;

		$this->crm 		= $this->crm( $crm_id );
		$this->agent 	= $this->agent( $crm_id );

		$this->post_id = $post_id;
		$action_name = 'ifound_' . $email_type . '_email';
		$results = null;
		if ($extra !== null) {
			if ($email_type === static::$CAMPAIGN_ALERT) {
				$results = $extra;
				do_action($action_name, $results);
			} else if (in_array($email_type, static::$INSTANT_UPDATE_TYPES)) {
				$results = $extra;
				do_action('ifound_instant_update_email', $results, $email_type);
			} else if ($email_type === 'drip_campaign_customer') {
				$options = $extra;
				do_action($action_name, $options);
			}
		} else {
			do_action($action_name);
		}

		$to_emails = explode(',', $this->to_email);
		$default_email_tags = $this->default_email_tags([
			'email_type' => $email_type,
			'results' => $results,
		]);
		$emails_sent_count = 0;
		foreach ($to_emails as $email_address) {
			$contact_info = $this->get_info_from_email_address($this->contact_id, $email_address);
			$unsub_options = [];
			if ($contact_info['type'] !== 'contact') {
				$unsub_options['query_args'] = [
					'email' => $email_address,
					'post_id' => $post_id,
				];
			}
			$replacements = wp_parse_args([
				'ContactFirstName' => $contact_info['fname'],
				'ContactLastName'  => $contact_info['lname'],
				'Unsubscribe'      => $this->unsubscribe_link($unsub_options),
			], $default_email_tags);
			$build_email_body_options = [
				'replacements' => $replacements,
			];
			$host = $this->util()->get_host();
			if ($crm_id && iFoundTeams::new_hookless()->user_has_team_member_role(['id' => $crm_id])) {
				$build_email_body_options['link_changer'] = function($string) use ($host, $crm_id) {
				    return $this->ensure_team_member_links_include_agent_id($string, $host, $crm_id);
				};
			}
            $tracking_pixel_id = null;
			$should_include_tracking_pixel = !!$this->log_type;
            if ($should_include_tracking_pixel) {
	            $tracking_pixel_id = bin2hex(random_bytes(16));
	            $prev_link_changer = $build_email_body_options['link_changer'];
	            $build_email_body_options['link_changer'] = function($string) use ($host, $tracking_pixel_id,
                    $prev_link_changer
                ) {
		            $v = $this->ensure_links_include_tracking_pixel($string, $host, $tracking_pixel_id);
		            if ($prev_link_changer) {
			            $v = $prev_link_changer($v);
		            }
		            return $v;
	            };
            }
			$body = $this->build_email_body($build_email_body_options);
			$send_email_options = [
				'to'            => $contact_info['to_email'],
				'subject'       => $this->do_replacements($this->subject, $replacements),
				'body'          => $body,
				'email_headers' => $this->email_headers(['unsubscribe_url' => $this->unsubscribe_url($unsub_options)]),
			];
			if ($should_include_tracking_pixel) {
			    $send_email_options['tracking_pixel_id'] = $tracking_pixel_id;
			}
			if ($this->send_email($send_email_options)) {
				$emails_sent_count++;
				if ($should_include_tracking_pixel) {
					$log_type = $this->log_type . ($contact_info['type'] === 'spouse' ? ' (Spouse)' : '');
					[
						'meta_id'      => $meta_id,
						'current_time' => $date_sent,
					] = iFoundActivity::new_hookless()->activity_log(
						$post_id,
						$log_type,
						static::$success_message_base . $email_address,
						[
							iFoundActivity::$email_tracking_meta_key => $send_email_options['tracking_pixel_id']
						]
					);
					iFoundEmailTrackingPixel::new_hookless()->create([
						'meta_id'        => $meta_id,
						'tracking_pixel' => $send_email_options['tracking_pixel_id'],
						'to_email'       => $email_address,
						'date_sent'      => $date_sent,
                        'type'           => $email_type,
					]);
				}
				if ($emails_sent_count === 1 && $options['post_id_type'] === 'campaign') {
					// If the email was the instant update type, send the notification to the agent
					if (in_array($email_type, static::$INSTANT_UPDATE_TYPES, true)) {
						$listings_content = $this->build_listings_content($results);
						$this->notify_agent($post_id, $listings_content, $email_type, $crm_id);
					}
				}
			}
		}

	}

	// The only thing this function does is a regex replacement of the status with a contingent-aware status.
	// I describe WHY we are doing this regex replacement of the status in server/lib/EmailBuilder.iced.
	// I gave the function a generic name because we make other changes as necessary.
	private function handle_rich_email_formatting($results) {
		$html = $results->email_content->body;
		foreach ($results->listings as $listing) {
			$status = apply_filters('ifound_check_status', $listing, ['mls_class' => $results->meta->mls_class]);
			// This regex basically says a <span> open tag, followed by the status inside the span, followed by the
			// span close.
			$regex = '#(<span class="list-status list-status-' . $listing->ListingID . '"[^>]*?>)([^<]*?)(</span>)#';
			$status_replacement = '${1}' . $status . '${3}';
			$html = preg_replace($regex, $status_replacement, $html);
		}
		return $html;
	}

	private function build_listings_content($results) {
		if ($results && isset($results->email_content) && isset($results->email_content->body)) {
			return $this->handle_rich_email_formatting($results);
		}

		// We have this fallback just in case we can't get email content from the server.
		return $this->build_fallback_links($results);
	}

	private function build_fallback_links($results) {
		$links = [];
		$max_links = 5;
		foreach(array_slice($results->listings, 0, $max_links) as $listing) {
			$fields = array(
				$listing->StreetNumber,
				$listing->StreetDirPrefix,
				$listing->StreetName,
				$listing->StreetSuffix,
				$listing->City,
				$listing->PostalCode
			);

			$address = [];
			foreach($fields as $field) {
				if($field !== '')
					$address[] = str_replace(' ', '-', $field);
			}

			$uri = site_url('/listing-details/' . implode('-', $address) . '/' . $listing->ListingID . '/');
			$uri .= $this->id_ext;

			$links[] = '<a href="' . $uri . '">' . implode(' ', $fields) . '</a>';
		}
		$content = implode('<br>', $links);
		return $content;
	}

	public function make_to_address($email_address, $name = null) {
		$to_address = $email_address;
		if ($name) {
			$to_address = "{$name} <{$email_address}>";
		}
		return $to_address;
	}

	private function set_from($crm, $requires_unsubscribe_header) {
		$this->from_name = $crm->from_name;
		// If WP Mail SMTP is set up, meaning the agent has set up their own (e.g.) Sendgrid account, then we'll respect
		// their From email address specified in iFound CRM Settings. Otherwise, we'll send it from our domain.
		if ($this->has_separate_email_service_provider()) {
			$this->from_email = $crm->from_email;
			$this->reply_to = null;
		} else {
			$from_name = $this->crm()->from_name;
			$this->from_email = $this->make_from_email_address($from_name);
			$this->reply_to = "{$crm->from_name} <{$crm->from_email}>";
		}
		$this->requires_unsubscribe_header = $requires_unsubscribe_header;
	}

	// For now, the only way we'll assume that someone has set up to use an Email Service Provider (ESP) than the one
	// our host uses by default is if they use the WP Mail SMTP plugin. There are of course other ways to do it, and we
	// can check for them here if we ever allow other mechanisms.
	public function has_separate_email_service_provider() {
		$does_class_exist = class_exists('\WPMailSMTP\Options');
		if ($does_class_exist) {
			$mailer = \WPMailSMTP\Options::init()->get('mail', 'mailer');
			$has_separate = $mailer !== 'mail';
			return $has_separate;
		}
		return false;
	}

















	// *****************************************************************************************************************
    // Below here are "prep" methods used by $this->email(). I'm putting them here because they are hard to distinguish
    // from other methods in this class which aren't prep methods. These methods don't actually send the mail; they
    // don't call wp_mail() or $this->send_email(), they main set member variables that will be used by
    // $this->send_email() later in $this->email().
    //
    // $this->email() dynamically builds a hook name by doing 'ifound_' + hook name + '_email', which hook to here.
	//
	// vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv

	public function property_alert_email() {
		$this->set_from($this->crm, true);
		$this->to_name		= $this->contact_full_name();
		$this->to_email 	= get_post_meta( $this->contact_id, 'email', true );
		$this->bcc_email 	= $this->bcc_email();
		$this->id_ext 		= $this->build_id_ext();

		$content_id  		= $this->crm->prop_alert;
		$this->subject 		= get_post_meta( $content_id, 'subject', true );
		$this->content 		= get_post( $content_id )->post_content;

		$this->default_templates = true;

		$this->log_type		= 'Send Property Update Email';

		do_action( 'ifound_activity_log', $this->contact_id, $this->log_type, get_the_title( $this->post_id ) );
	}

	public function agent_notification_email() {
		$option_name		= has_term( 'property-alert', 'save_type', $this->post_id ) ? 'prop_notice' : 'search_notice';
		$content_id			= $this->crm->$option_name;

		$this->set_from($this->crm, false);
		$this->to_name		= $this->crm->from_name;
		$this->to_email 	= $this->crm->from_email;
		$this->bcc_email 	= false;

		$this->log_type		= 'Send Agent Email';

		$this->subject 		= get_post_meta( $content_id, 'subject', true );
		$this->content 		= get_post( $content_id )->post_content;

		$this->default_templates = true;
	}

	public function campaign_alert_email($results) {
		$camp_to_email = get_post_meta( $this->post_id, 'to_email', true);

		$this->set_from($this->crm, true);
		$this->to_name		= $this->contact_full_name();
		$this->to_email 	= ($camp_to_email ? $camp_to_email : get_post_meta( $this->contact_id, 'email', true ));
		$this->bcc_email 	= $this->bcc_email();
		$this->id_ext 		= $this->build_id_ext();

		$this->log_type		= 'Send Client Email';

		$this->subject 		= get_post_meta( $this->post_id, 'custom_subject', true );
		$this->content 		= get_post_meta( $this->post_id, 'custom_content', true );

		$listings_content = '(No listings)';
		if ($results) {
			$listings_content = $this->build_listings_content($results);
			$this->content = $this->do_replacements($this->content, [
				'Listings' => $listings_content,
			]);
			$this->email_styles = $this->email_styles() . "\n" . $results->email_content->head;
		}
	}

	public function instant_update_email($results, $type) {
		$camp_to_email = get_post_meta( $this->post_id, 'to_email', true);

		$this->set_from($this->crm, true);
		$this->to_name		= $this->contact_full_name();
		$this->to_email 	= ($camp_to_email ? $camp_to_email : get_post_meta( $this->contact_id, 'email', true ));
		$this->bcc_email 	= $this->bcc_email();
		$this->id_ext 		= $this->build_id_ext();

		$typeString = $type === static::$INSTANT_UPDATE_RECENTLY_CLOSED
			? 'Instant Update Recently Closed' : 'Instant Update';
		$this->log_type		= "Send $typeString Email";

		$content_id  		= $this->crm->$type;
		$default_subject	= $type === static::$INSTANT_UPDATE_RECENTLY_CLOSED
			? '{ContactFirstName}, A Home Recently Closed Near Your Home!'
			: '{ContactFirstName}, There are new homes for sale near you!';
		$this->subject 		= get_post_meta( $content_id, 'subject', true ) ?: $default_subject;
		// We might not find the content, and the email would have a mostly blank body (aside from header, footer, signature).
		// But oh well. Apparently no one has ever complained of this situation before.
		if ($content_id) {
			$instant_update_post = get_post($content_id);
			if ($instant_update_post) {
				$listings_content = $this->build_listings_content($results);
				$content = $instant_update_post->post_content;
				$this->content 	= $this->do_replacements($content, [
					'Listings' => $listings_content,
				]);
				$this->email_styles = $this->email_styles() . "\n" . $results->email_content->head;
			}
		}

		do_action('ifound_activity_log', $this->contact_id, $this->log_type, get_the_title($this->post_id));
	}

	public function drip_campaign_customer_email($options) {
		$this->set_from($this->crm, true);
		$this->bcc_email 	= $this->bcc_email();
		$this->id_ext 		= $this->build_id_ext();

		$email_template_id = $options['email_template_id'];
		$this->subject = get_post_meta($email_template_id, 'subject', true);
		$this->content = get_post($email_template_id)->post_content;

		$this->log_type		= 'Send Drip Campaign Customer Email';
		do_action( 'ifound_activity_log', $this->contact_id, $this->log_type, get_the_title( $this->post_id ) );
	}

	public function website_visit_follow_up($contact_id) {
		$this->set_from($this->crm, true);
		$this->bcc_email 	= $this->bcc_email();
		$this->id_ext 		= $this->build_id_ext();

		$email_template_id = $this->crm->{iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE};
		$this->subject = get_post_meta($email_template_id, 'subject', true);
		$this->content = get_post($email_template_id)->post_content;

		$this->default_templates = true;
		$this->log_type		= 'Send Website Visit Follow-Up';
	}

    // ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    //
	// Above here are "prep" methods used by $this->email().
	// *****************************************************************************************************************


















	// *****************************************************************************************************************
	// Below here are methods that send email directly, either calling wp_mail() or $this->send_email(). I'm putting
    // them here to distinguish them from the other section of "prep" methods.
	//
	// vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv

	public function bulk_email($contact_id, $subject, $content, $header_template_id, $signature_template_id,
		$footer_template_id
	) {
		$this->contact_id  	= intval( $contact_id );
		$this->crm 			= $this->crm();
		$this->agent 		= $this->agent(['contact_id' => $contact_id]);

		$this->set_from($this->crm, true);
		$this->to_name		= $this->contact_full_name();
		$this->to_email 	= get_post_meta( $this->contact_id, 'email', true );
		$this->bcc_email 	= $this->bcc_email();

		$content = $this->filter_tags($content);

		$header_content = $header_template_id ? $this->filter_tags(get_post($header_template_id)->post_content) : '';
		$signature_content = $signature_template_id
			? $this->filter_tags(get_post($signature_template_id)->post_content) : '';
		$footer_content = $footer_template_id ? $this->filter_tags(get_post($footer_template_id)->post_content) : '';
		$body = $this->build_email_body([
			'header'    => $header_content,
			'content'   => $content,
			'signature' => $signature_content,
			'footer'    => $footer_content,
		]);
		$subject = $this->filter_tags($subject);
		$headers = $this->email_headers();
		$to_address = $this->make_to_address($this->to_email, $this->to_name);
		$response = wp_mail($to_address, $subject, $body, $headers);
		if (!is_wp_error($response)) {
			// Store the sent email. Capture the meta ID returned so we can link it in the activity log.
			$meta_id = add_post_meta($contact_id, 'ifound_email_sent', [
				'date'    => new \DateTime("now", new \DateTimeZone("UTC")),
				'to'      => $this->to_email,
				'subject' => $subject,
				'body'    => $body,
				'headers' => $headers,
			]);
			do_action('ifound_activity_log', $contact_id, 'Send Email', $subject, ['ifound_email_sent_meta_id' => $meta_id]);
		}
		return $response;
	}

	// Send an email to the agent.
	public function agent_email($subject, $content, $options = []) {
		$options = wp_parse_args($options, [
			'to_email'     => null,
			'crm_id'       => null,
			'is_rich_text' => false,
		]);
		$this->crm 			= $this->crm($options['crm_id']);
		$this->agent 		= $this->agent($options['crm_id']);

		$this->bcc_email    = false;
		$this->set_from($this->crm, false);
		$to_email_address   = $options['to_email'] ?: $this->crm->from_email;
		$to_address         = $this->make_to_address($to_email_address, $this->crm->from_name);
		$headers            = $this->email_headers(['is_rich_text' => $options['is_rich_text']]);

		wp_mail($to_address, $subject, $content, $headers);
	}

	// This function is not currently used. We can remove it later if we're sure we won't be using it. Or we can leave
	// it knowing we'll be asked to use it someday.
	//
	// public function drip_campaign_reminder_email($email_template_id, $contact_id, $drip_campaign_id) {
	// 	$this->contact_id	= $contact_id;
	// 	$crm_id             = $this->crm_id(['contact_id' => $contact_id]);
	// 	$this->crm 			= $this->crm($crm_id);
	// 	$this->agent 		= $this->agent($crm_id);
	//
	// 	$email_template = get_post($email_template_id);
	// 	$subject = $this->filter_tags(get_post_meta($email_template_id, 'subject', true));
	// 	$content = $this->filter_tags($email_template->post_content);
	// 	$this->agent_email($subject, $content, [
	// 		'to_email' => $this->crm->sms_carrier_email,
	// 		'crm_id' => $crm_id,
	// 	]);
	//
	// 	$log_type = 'Send Drip Campaign Reminder Email';
	// 	do_action('ifound_activity_log', $drip_campaign_id, $log_type, $email_template->post_title);
	// }

	public function upcoming_reminders_email($user_id) {
		$crm_id             = $this->crm_id_from_user_id($user_id);
		$this->crm 			= $this->crm( $crm_id );
		$this->agent 		= $this->agent( $crm_id );

		$this->set_from($this->crm, false);
		$this->to_name		= $this->crm->from_name;
		$this->to_email 	= $this->crm->from_email;
		$this->bcc_email 	= false;

		$this->log_type		= false;

		$this->subject 		= 'Upcoming Task Reminders from your website powered by iFoundAgent';
		$this->content      = iFoundDripCampaign::new_hookless()->get_upcoming_reminders_email_content($user_id);

		$email_headers = $this->email_headers();
		$email_headers = $this->maybe_add_cc_header_for_setting($crm_id, 'notification_cc_activity_report', $email_headers);
		$this->send_email(['email_headers' => $email_headers]);
	}

	public function activity_report_email($user_id) {
		$crm_id             = $this->crm_id_from_user_id($user_id);
		$this->crm 			= $this->crm( $crm_id );
		$this->agent 		= $this->agent( $crm_id );

		$this->set_from($this->crm, false);
		$this->to_name		= $this->crm->from_name;
		$this->to_email 	= $this->crm->from_email;
		$this->bcc_email 	= false;

		$this->log_type		= false;

		$this->subject 		= 'Daily Activity Report from your website powered by iFoundAgent';
		$this->content      = iFoundContactsAdmin::new_hookless()
			->get_latest_web_and_email_activity_slim_content(
				$this->crm->qty_logs_to_report,
				iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids($user_id),
				$user_id
			);

		$email_headers = $this->email_headers();
		$email_headers = $this->maybe_add_cc_header_for_setting($crm_id, 'notification_cc_activity_report', $email_headers);
		$this->send_email(['email_headers' => $email_headers]);
	}

	public function contact_visit_email( $contact_id, $options = [] ) {
		$options = wp_parse_args($options, [
			'subject' => '{ContactFirstName} {ContactLastName} has visited your website',
			'content' => '<p>{ContactFirstName} {ContactLastName} has visited your website.</p>',
		]);

		$this->contact_id  	= intval( $contact_id );

		$crm_id             = $this->crm_id(['contact_id' => $contact_id]);
		$this->crm 			= $this->crm($crm_id);
		$this->agent 		= $this->agent(['contact_id' => $contact_id]);

		$this->set_from($this->crm, false);
		$this->to_name		= $this->crm->from_name;
		$this->to_email 	= $this->crm->from_email;
		$this->bcc_email 	= $this->bcc_email();

		$this->log_type		= false;
		$hPhoneNum          = get_post_meta($contact_id, 'hphone', true);
		// Prevent the URL encoding of the space char
		$hPhoneNum = str_replace(' ', '-', $hPhoneNum);
		$mPhoneNum          = get_post_meta($contact_id, 'mphone', true);
		$mPhoneNum = str_replace(' ', '-', $mPhoneNum);

		$this->subject 		= $options['subject'];
		$this->content 		= $options['content'];
		$this->content 		.= '<p>{ContactLink}</p>'
			. '<style>.email-link{display:inline-block; padding: 10px 10px; margin-right: 20px; background: #15b658; text-decoration: none !important;}</style>'
			. '<a class="email-link button" href="mailto:{ContactEmail}">Email {ContactFirstName}</a>';
		if($hPhoneNum) {
			$this->content .= '<a class="email-link button" href="tel:' . $hPhoneNum . '">Call {ContactFirstName} (Home)</a>';
		}
		if ($mPhoneNum) {
			$this->content .= '<a class="email-link button" href="tel:' . $mPhoneNum . '">Call {ContactFirstName} (Mobile)</a>'
				. '<a class="email-link button" href="sms:' . $mPhoneNum . '">Text {ContactFirstName} (Mobile)</a>';
		}
		$this->content = $this->remove_contact_info_from_links($this->content, $this->util()->get_host());
		$email_headers = $this->email_headers();
		$email_headers = $this->maybe_add_cc_header_for_setting($crm_id, 'notification_cc_instant_activity',
			$email_headers);

		$this->send_email(['email_headers' => $email_headers]);
	}

	public function team_lead_email( $contact_id, $content ) {
		$this->crm			= $this->crm( '' );

		$this->agent 		= $this->agent('');

		$this->set_from($this->crm, false);
		$this->to_name		= $this->crm->from_name;
		$this->to_email 	= $this->crm->from_email;
		$this->bcc_email 	= $this->bcc_email();
		$this->contact_id   = $contact_id;

		$this->log_type		= false;

		$this->subject 		= 'Assign Team Lead: {ContactFirstName} {ContactLastName}';
		$this->content 		= $content;

		$this->default_templates = true;

		$this->send_email();
	}

	/**
	 * Notify the agent when the instant update sent to a client
	 *
	 * @since 5.4.3
	 * @param int $save_this_id. Id of the campaign post
	 * @param string $listings_content. An HTML string with links to the new listings
	 * @return bool. Status of wpmail
	 */
	private function notify_agent($save_this_id, $listings_content, $type, $crm_id) {
		if ($this->crm($crm_id)->instant_updates_notification !== 'enabled') {
			return;
		}

		$contact_id = intval(get_post_meta($save_this_id, 'contact_id', true));
		if(!$contact_id) return;
		$contact_meta = array(
			'email'  => get_post_meta($contact_id, 'email', true),
			'hphone' => get_post_meta($contact_id, 'hphone', true)
		);

		// Get the campaign name
		$campaign = get_the_title($save_this_id);

		// Get the email body
		$listing_type_string = $type === static::$INSTANT_UPDATE_RECENTLY_CLOSED ? 'Recently Closed' : 'New';
		$content = "$listing_type_string Listings were sent to {ContactFirstName} {ContactLastName} for their campaign "
			. $campaign . ':<br>'
			. $listings_content . '<br><br>' . $this->contact_button($contact_id);

		$content .= '<style>.email-link{display:inline-block; padding: 10px 10px;'
			. 'margin-right: 20px; margin-top:20px; background: #15b658; text-decoration: none !important;}</style>'
			. '<br><br><a class="email-link button" href="mailto:' . $contact_meta['email'] . '">Email {ContactFirstName}</a>';

		if($contact_meta['hphone'])
			$content .= '<a class="email-link button" href="tel:' . $contact_meta['hphone'] . '">Call {ContactFirstName}</a>'
				.  '<a class="email-link button" href="sms:' . $contact_meta['hphone'] . '">Text {ContactFirstName}</a>';

		$this->content = $content;
		$host = $this->util()->get_host();
		$body = $this->build_email_body([
			'link_changer' => function($string) use ($host) {
				return $this->remove_contact_info_from_links($string, $host);
			}
		]);

		$type_string = $type === static::$INSTANT_UPDATE_RECENTLY_CLOSED ? 'Recently Closed' : '';
		$subject = "Instant Update $type_string sent to client";
		$to_address = $this->make_to_address($this->from_email, $this->from_name);

		// Record these existing values, so that we can restore them after we send this notification to the agent. This
		// is hacky but is a consequence of this class using member variables, and I'm not willing to risk the
		// consequences of trying to change such uses right now. To make it even more explicit what's going on, as of
		// now, this method is called from within a for loop of email(), and so it'll use the member variables that are
		// meant for the emails to be sent to the agent's customers, not the agent.
		$existing_requires_unsubscribe_header = $this->requires_unsubscribe_header;
		$existing_reply_to = $this->reply_to;

		$this->set_from($this->crm, false);
		$this->reply_to = null;
		$email_headers = $this->email_headers();

		$this->requires_unsubscribe_header = $existing_requires_unsubscribe_header;
		$this->reply_to = $existing_reply_to;

		return wp_mail($to_address, $subject, $body, $email_headers);
	}

	public function email_ifoundagent_staff($subject, $body) {
		$url = $this->base_url(true) . '/admin/email_staff';
		$headers = $this->headers();
		$args = &$headers;
		$body = [
			'apikey' => $this->api_secret(),
			'subject' => $subject,
			'body' => $body,
		];
		$args['body'] = $body;
		$response = wp_remote_post($url, $args);
		// TODO: check if response is code 200 (body will be empty). I'm not checking just because the only place I'm
		// currently using it doesn't care.
	}

	public function email_ifoundagent_adhoc($to_address, $from_address, $subject, $body) {
		$headers = [
			'From: ' . $from_address,
			// Note that it's plain text!
			'Content-type: text/plain; charset=UTF-8',
		];
		return wp_mail($to_address, $subject, $body, $headers);
	}

	// ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
	//
	// Above here are email sending methods which are NOT "prep" methods used by $this->email().
	// *****************************************************************************************************************



































	private function do_replacements($content, $replacements = []) {
		foreach( $replacements as $key => $value ) {
            if (strtoupper($key) === 'CONTACTSTREETNAME' && $this->contact_id) {
                $content = iFoundMergeTags::new_hookless()->replace_contact_street_name($content, $this->contact_id);
            } else {
	            $content = str_ireplace( '{' . $key . '}', $value, $content );
            }
		}
		return html_entity_decode( $content, ENT_QUOTES );
	}

	private function build_id_ext() {
		$aid_query_term     = '';
		if ($this->crm_id(['contact_id' => $this->contact_id])) {
			$aid_query_term = '&aid=' . $this->crm_id(['contact_id' => $this->contact_id]);
		}
		$id_ext = '?contact_id=' . $this->contact_id . $aid_query_term;
		return $id_ext;
	}

	// I don't love putting this code here. But I don't feel like refactoring all the $this->filter_tags() stuff out of
	// this class.
	public function get_drip_reminder_content($email_template_id, $contact_id) {
		$this->contact_id	= $contact_id;
		$crm_id             = $this->crm_id(['contact_id' => $contact_id]);
		$this->crm 			= $this->crm($crm_id);
		$this->agent 		= $this->agent($crm_id);

		$email_template = get_post($email_template_id);
		$content = $this->filter_tags($email_template->post_content);
		return $content;
	}

	public function get_contact_visit_sms_content($contact_id) {
		$crm_id             = $this->crm_id(['contact_id' => $contact_id]);
	    $this->crm 			= $this->crm($crm_id);
	    $this->agent 		= $this->agent(['contact_id' => $contact_id]);
		$this->contact_id  	= intval( $contact_id );

	    $content = '{ContactFirstName} {ContactLastName} visited your website. View contact/activity: ';
	    $content = $this->do_replacements($content, $this->default_email_tags());
	    $content = $content . $this->make_contact_profile_url($contact_id);

		return $content;
	}

	private function send_email($options = []) {
		$email_tags = wp_parse_args($options['replacements'], $this->default_email_tags());
		$options = wp_parse_args($options, [
			'to'            => $this->make_to_address($this->to_email, $this->to_name),
			'subject'       => $this->do_replacements($this->subject, $email_tags),
			'email_headers' => $this->email_headers(),
		]);
		// I'm doing this outside the above wp_parse_args() just so it doesn't necessarily have to call
		// build_email_body().
		if (!isset($options['body'])) {
			$options['body'] = $this->build_email_body($options['body_options'], $email_tags);
		}
		if ($options['tracking_pixel_id']) {
		    $tracking_pixel_url = site_url('/wp-json/ifound/tpxl/' . $options['tracking_pixel_id'] . '.png');
		    $options['body'] .= "\n<img src=\"$tracking_pixel_url\" width=\"1\" height=\"1\">";
		}
		return wp_mail($options['to'], $options['subject'], $options['body'], $options['email_headers']);
	}

	private function maybe_change_links($options) {
		if (isset($options['link_changer'])) {
			$email_sections_to_maybe_change = [
				'header',
				'content',
				'signature',
				'footer',
			];
			foreach ($email_sections_to_maybe_change as $section_name) {
				$options[$section_name] = $options['link_changer']($options[$section_name]);
			}
		}
		return $options;
	}

	private function build_email_body($options = []) {
		$fillers = [
			'subject'      => function() { return $this->subject; },
			'email_styles' => function() { return $this->email_styles ?: $this->email_styles(); },
			'header'       => function() { return $this->header(); },
			'content'      => function() { return $this->content; },
			'signature'    => function() { return $this->signature(); },
			'footer'       => function() { return $this->footer(); },
		];
		$email_tags = wp_parse_args($options['replacements'] ?? null, $this->default_email_tags());
		foreach ($fillers as $key => $fn) {
			if (!isset($options[$key])) {
				$options[$key] = $this->do_replacements($fn(), $email_tags);
			}
		}

		$options = $this->maybe_change_links($options);

		// Regarding emails that embed listings:
		// What I did was put an HTML comment
		// at the beginning of the listings content, as well as the end. Now we're able to use regex to look for that
		// content, pull it out, make sure we don't use wpautop() on it, but use wpautop() on the rest of the content.
		// Be sure to keep it up to date with server/lib/EmailBuilderWorker.iced's buildTemplates()!
		$content = $options['content'];
		$pattern = '#(.*)(?:\s*<p>\s*)?(<!-- start mj-body -->.*<!-- end mj-body -->)(?:\s*</p>\s*)?(.*)#s';
		if (preg_match($pattern, $content, $matches)) {
			$content_before = wpautop($matches[1]);
			$content_between = $matches[2];
			$content_after = wpautop($matches[3]);
			$content = $content_before . $content_between . $content_after;
		} else {
			$content = wpautop($content);
		}

		ob_start();
		?>

		<html lang="en-US">
			<head>
				<meta name="viewport" content="width=device-width, initial-scale=1">
				<title><?= $options['subject'] ?></title>
				<?= $options['email_styles'] ?>
			</head>
			<body>
			<div class="header">
				<div class="ifound-wrap">
					<?= wpautop( $options['header'], true ) ?>
				</div>
			</div>
			<div class="content">
				<div class="ifound-wrap">
					<?= $content ?>
				</div>
			</div>
			<div class="signature">
				<div class="ifound-wrap">
					<?= wpautop( $options['signature'], true ) ?>
				</div>
			</div>
			<footer class="email-footer">
				<div class="footer-text">
					<?= wpautop( $options['footer'], true ) ?>
				</div>
			</footer>
			</div>
			</body>
		</html>
		<?

		return ob_get_clean();
	}

	/**
	 * Log Mailer Errors
	 *
	 * Log errors if any occur.
	 *
	 * @since 2.5.60
	 *
	 * @param object $wp_error The email object.
	 */

	public function log_mailer_errors( $wp_error ) {
 		if( isset( $this->post_id ) )
 			do_action( 'ifound_activity_log', $this->post_id, 'Email Error', print_r( $wp_error->errors['wp_mail_failed'][0], true ) );

 		if( isset( $this->contact_id ) )
 			do_action( 'ifound_activity_log', $this->contact_id, 'Email Error', print_r( $wp_error->errors['wp_mail_failed'][0], true ) );

	}


	// Remove contact_id and aid query string parameters. The idea is that for emails being sent to agents, we want to
	// prevent the possibility that they'd follow the links when not logged in, because in that scenario the website
	// would consider them that contact, and they'd get visit notifications, etc.
	private function remove_contact_info_from_links($string, $host_to_match) {
        $link_regex = $this->util()->get_link_regex();
		$change_query_string_fn = function($query_str_parts) {
			// This is where we actually remove the query string parameters we care about. At this time, I know about
			// these parameters. Add more as necesary.
			unset($query_str_parts['contact_id']);
			unset($query_str_parts['aid']);
			unset($query_str_parts['et']);
			unset($query_str_parts['tpx']);
			return $query_str_parts;
		};
		$s = $this->change_links($string, $link_regex, $host_to_match, $change_query_string_fn);
		return $s;
	}

	private function change_links($string, $regex, $host_to_match, $change_query_string_fn) {
		return preg_replace_callback($regex, function($matches) use ($host_to_match, $change_query_string_fn) {
			$url_parts = parse_url($matches[0]);
			if ($url_parts === false) {
				return $matches[0];
			}
			if ($url_parts['host'] !== $host_to_match) {
				return $matches[0];
			}
			$query_str_parts = [];
			if (isset($url_parts['query'])) {
				parse_str($url_parts['query'], $query_str_parts);
			}

			$result = $change_query_string_fn($query_str_parts);
			if ($result === false) {
				return $matches[0];
			}
			$query_str_parts = $result;

			// Build URL back up
			$port_str = isset($url_parts['port']) ? ':' . $url_parts['port'] : '';
			$origin = $url_parts['host'] . $port_str;
			$path_str = isset($url_parts['path']) ? $url_parts['path'] : '/';
			$replacement_url = "{$url_parts['scheme']}://{$origin}{$path_str}";
			// Don't try to build a query string if there are no parameters because it will just be a question mark.
			if ($query_str_parts) {
				$new_query_str = http_build_query($query_str_parts);
				$replacement_url .= '?' . $new_query_str;
			}
			return $replacement_url;
		}, $string);
	}

	private function maybe_add_cc_header_for_setting($crm_id, $setting_name, $email_headers) {
		$crm = $this->crm($crm_id);
		if ($crm->{$setting_name} === 'enabled' && $crm->notification_cc_email_addresses) {
			$email_headers[] = 'CC: ' . $crm->notification_cc_email_addresses;
		}
		return $email_headers;
	}

	public function delete_email_templates($user_id) {
		$args = [
			'numberposts' => -1,
			'post_type'    => 'ifound_email',
			'author'       => $user_id,
		];
		$email_templates = get_posts($args);
		foreach ($email_templates as $email_template) {
			delete_post($email_template->ID);
		}
	}

	// Wordpress doesn't have a good way to prevent trashing/deleting. You can use the pre_trash_post hook, but you can
	// only return true/false, without an explanation to the user. So we circumvent their intended use to do our own
	// thing.
	public function before_delete_post($post_id) {
		if (get_post_type($post_id) === $this->post_type) {
			if (iFoundDripTemplate::new_hookless()->is_email_template_in_use($post_id)) {
				$message = 'This email template is in use by at least one drip template with at least one drip'
				    . ' campaign that has not ended';
				$title = 'Unable to remove this post';
				wp_die($message, $title, ['code' => 400]);
			}
		}
	}

	public function create_email_template($template, $post_author = 1) {
		$email_post = array(
			'post_title'    => $template['post_title'],
			'post_content'  => $template['post_content'],
			'post_status'   => 'publish',
			'post_type'		=> 'ifound_email',
			'post_author' 	=> $post_author,
		);

		// Insert the post into the database
		$post_id = wp_insert_post( $email_post );

		wp_set_object_terms( $post_id, $template['email_type'], 'ifound_email_type' );

		if( $template['subject'] ) {

			add_post_meta( $post_id, 'subject', $template['subject'] );

		}

		return $post_id;

	}

	/**
	 * Email Templates
	 *
	 * These are the defaults for the custom post type ifound_email.
	 * @see ifound_email::post_type()
	 *
	 * Some of these templates are set as defaults for the CRM.
	 * @see iFOUND_contacts::crm_settings()
	 *
	 * default_key 			The options DB key for this template.
	 * email_type 			The name for the email_type
	 * subject 				The email subject
	 * post_title 			The title for the email Template
	 * post_content			The HTML for the email body.
	 *
	 * @since 1.2.0
	 */

	public function email_templates() {

		return array(
			1 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> 'You just received a new testimonial',
				'post_title'		=> 'Testimonial',
				'post_content'		=> $this->util()->get_html( 'email/', 'testimonial' )
			),
			2 => array(
				'default_key'		=> 'prop_alert',
				'email_type'		=> 'content',
				'subject'			=> 'Great News from {AgentName} - Property Alert',
				'post_title'		=> 'Property Alert',
				'post_content'		=> $this->util()->get_html( 'email/', 'property-alert' )
			),
			3 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} Here is the home search you wanted',
				'post_title'		=> 'Buyer Search Template',
				'post_content'		=> $this->util()->get_html( 'email/', 'buyer-search' )
			),
			4 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} Here is your neighborhood market update',
				'post_title'		=> 'Market Update - Past Client',
				'post_content'		=> $this->util()->get_html( 'email/', 'market-update-past-client' )
			),
			5 => array(
				'default_key'		=> 'prop_notice',
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} {ContactLastName} Saved Property Notification',
				'post_title'		=> 'Saved Property Notification',
				'post_content'		=> $this->util()->get_html( 'email/', 'saved-property-notice' )
			),
			6 => array(
				'default_key'		=> 'search_notice',
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} {ContactLastName} Saved Search Notification',
				'post_title'		=> 'Saved Search Notification',
				'post_content'		=> $this->util()->get_html( 'email/', 'saved-search-notice' )
			),
			7 => array(
				'default_key'		=> iFoundEmail::$INSTANT_UPDATE,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} There are new homes for sale near you',
				'post_title'		=> 'Instant Update Notification',
				'post_content'		=> $this->util()->get_html( 'email/', 'instant-update-notice' )
			),
			8 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} Here is your neighborhood market update',
				'post_title'		=> 'Market Update - Home Owner',
				'post_content'		=> $this->util()->get_html( 'email/', 'market-update-home-owner' )
			),
			9 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> 'Happy Holidays from {AgentName}!',
				'post_title'		=> 'Happy Holidays',
				'post_content'		=> $this->util()->get_html( 'email/', 'happy-holidays' )
			),
			10 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{SpouseFirstName} - Happy Birthday from {AgentName}',
				'post_title'		=> 'Spouse Birthday',
				'post_content'		=> $this->util()->get_html( 'email/', 'spouse-birthday' )
			),
			11 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} - Happy Birthday from {AgentName}',
				'post_title'		=> 'Birthday',
				'post_content'		=> $this->util()->get_html( 'email/', 'birthday' )
			),
			12 => array(
				'default_key'		=> 'header',
				'email_type'		=> 'header',
				'subject'			=> false,
				'post_title'		=> 'Default Header',
				'post_content'		=> $this->util()->get_html( 'email/', 'default-header' )
			),
			13 => array(
				'default_key'		=> 'signature',
				'email_type'		=> 'signature',
				'subject'			=> false,
				'post_title'		=> 'Default Signature',
				'post_content'		=> $this->util()->get_html( 'email/', 'default-signature' )
			),
			14 => array(
				'default_key'		=> 'footer',
				'email_type'		=> 'footer',
				'subject'			=> false,
				'post_title'		=> 'Default Footer',
				'post_content'		=> $this->util()->get_html( 'email/', 'default-footer' )
			),
			15 => array(
				'default_key'		=> false,
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} Here is your neighborhood market update from {AgentName}',
				'post_title'		=> 'Homeowner Campaign',
				'post_content'		=> $this->util()->get_html( 'email/', 'homeowner-campaign' )
			),
			16 => array(
				'default_key'		=> 'instant_update_recently_closed',
				'email_type'		=> 'content',
				'subject'			=> '{ContactFirstName} These homes recently closed near you',
				'post_title'		=> 'Instant Update Recently Closed Notification',
				'post_content'		=> $this->util()->get_html( 'email/', 'instant-update-recently-closed-notice' )
			),
			17 => array(
				'default_key'		=> iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE,
				'email_type'		=> iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_SLUG,
				'subject'			=> '{ContactFirstName} Thanks for visiting my website',
				'post_title'		=> iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_LABEL,
				'post_content'		=> $this->util()->get_html( 'email/', 'website-visit-follow-up' )
			),
		);
	}

	public function make_from_email_address($name, $from_domain = null) {
		$name = strtolower($name);
		$concentrated_name = preg_replace('/[^a-z]/', '', $name);
		$home = get_option('home');
		$domain = parse_url($home)['host'];
		$domain = 'www.' . $domain;
		if (strpos($domain, 'www.') === 0) {
			$domain = substr($domain, strlen('www.'));
		}
		if (!$from_domain) {
			$from_domain = iFoundCrm::$from_email_domain;
		}
		$email_address = "{$concentrated_name}_{$domain}@{$from_domain}";
		return $email_address;
	}
}

iFoundEmail::static_init();
