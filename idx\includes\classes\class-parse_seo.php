<?php
include_once('class-cleanfield.php');


/**
 *
 */
class SEO_Parse extends Cleaner
{

	/**
	 * While implementing this as a class
	 * get all of the customer access
	 * information so that these map
	 * functions are returned as well.
	 * then use them if needed.
	 *
	 * this should allow the use of a class
	 * that cleans the field data
	 * otherwise, this should be done.
	 *
	 */

	public function parse_seomap($data_array, $strg)
	{
		if (is_array($data_array)) {
			foreach ($data_array as $key => $value) {

				//Correct any issues with the data
				$value = $this->cleanField($key, $value);

				# TODO: this is a hack to skip over images on property.
				if (is_array($value)) continue;

				$strg = str_replace('{' . $key . '}', $value, $strg);

			}
		}
		return $strg;
	}

	/**
	 * URL Structure of cleaned the data as it's being put into the map
	 * by using the dynamic function ability in this class
	 *
	 */
	public function parse_urlmap($data_array, $strg)
	{
		$url_strg = strtolower($this->parse_seomap($data_array, $strg));
		return preg_replace('/[^A-Za-z0-9_-]+/', '-', $url_strg);
	}

}

?>