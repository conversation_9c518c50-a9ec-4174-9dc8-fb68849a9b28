/**
 * @summary Area Map.
 *
 * @since 1.1.2
 */

jQuery( document ).ready( function( $ ) {

	function initMap() {

		var map;
		var markers = new Array();

		var styles = [
			{ featureType: "all",
				stylers: [
					{ saturation: 0 }
				]
			},{ featureType: "road.arterial",
				elementType: "geometry",
				stylers: [
					{ hue: "#ff9009" },
					{ saturation: 20 }
				]
			}
		];


		var mapOptions = {
			zoom: area_map.geo.zoom,
			center: { lat: area_map.geo.center_lat, lng: area_map.geo.center_lng },
			styles: styles,
			...window.iFoundGlobal.sharedGoogleMapBaseOptions,
		};

		map = new google.maps.Map(document.getElementById( 'area-map' ), mapOptions );

		setMarkers();

		function setMarkers() {

			var bounds = new google.maps.LatLngBounds();

			z = 100;

			$.each( map_data.markers, function() {

				var content = '<div class="ifound-info-window">'
					content += '<div class="ifound-wrap">'
					content += '<a href="' + this.url + '" class="ifound-info-window-button button">' + this.label + '</a>';
					content += '</div>';
					content += '</div>';

				var marker = new google.maps.InfoWindow({
					position: { lat: this.lat, lng: this.lng },
					map: map,
					content: content,
					maxHeight: 0,
					zIndex: z
				});
				z++;

				markers.push(marker);

				bounds.extend( marker.getPosition() );

				google.maps.event.addListener(marker, 'DOMContentLoaded', function() {
					// @link https://codepen.io/Marnoto/pen/xboPmG
		    		// Reference to the DIV that wraps the bottom of infowindow
		    		var iwOuter = $('.gm-style-iw');

				    /* Since this div is in a position prior to .gm-div style-iw.
				     * We use jQuery and create a iwBackground variable,
				     * and took advantage of the existing reference .gm-style-iw for the previous div with .prev().
				    */
				    var iwBackground = iwOuter.prev();

				    // Removes background shadow DIV
				    iwBackground.children(':nth-child(2)').css({'display' : 'none'});

				    // Removes white background DIV
				    iwBackground.children(':nth-child(4)').css({'display' : 'none'});

				    // Moves the infowindow 40px down.
				    iwOuter.parent().parent().css({top: '40px'});

				    // Moves the shadow of the arrow 76px to the left margin.
				    //iwBackground.children(':nth-child(1)').attr('style', function(i,s){ return s + 'top: 20px !important;'});
				    iwBackground.children(':nth-child(1)').css({'display' : 'none'});

				    // Moves the arrow 30px up.
				    //iwBackground.children(':nth-child(3)').attr('style', function(i,s){ return s + 'top: 15px !important;'});
				    iwBackground.children(':nth-child(3)').css({'display' : 'none'});

				    // Changes the desired tail shadow color.
				    //iwBackground.children(':nth-child(3)').find('div').children().css({'box-shadow': 'rgba(72, 181, 233, 0.1) 0px 1px 3px'});
				    iwBackground.children(':nth-child(3)').find('div').children().css({'display' : 'none'});

				    // If the content of infowindow not exceed the set maximum height, then the gradient is removed.
				    if($('.iw-content').height() < 140){
				      $('.iw-bottom-gradient').css({display: 'none'});
				    }

				});


			});

			map.fitBounds(bounds);
		}
	}

	window.iFoundGlobal.loadGoogleMaps().then(function() {
		initMap();
	});
});
