--
-- Table structure for table `property_coordinates`
--

DROP TABLE IF EXISTS `property_coordinates`;
CREATE TABLE `property_coordinates` (
  `Latitude` decimal(17,12) COLLATE utf8_unicode_ci NOT NULL,
  `Longitude` decimal(17,12) COLLATE utf8_unicode_ci NOT NULL,
  `ListingKey` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`ListingKey`),
  KEY `ADDRESS` (`address`)
) ENGINE=MyISAM AUTO_INCREMENT=2284 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

