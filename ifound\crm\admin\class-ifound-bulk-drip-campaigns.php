<?php

class iFoundBulkDripCampaigns extends iFoundCrm {
	public static $snake_slug = 'create_bulk_drip_campaigns';
	public static $ifound_snake_slug;

	public static function static_init() {
		static::$ifound_snake_slug = 'ifound_' . static::$snake_slug;
	}

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_notices', array($this, 'my_bulk_action_admin_notice'));
			add_action(static::$ifound_snake_slug . '_page', array($this, static::$snake_slug . '_page'));
			foreach (iFoundJointContact::$all_contact_post_types as $type) {
				add_filter("handle_bulk_actions-edit-$type", array($this, 'handle_bulk_actions_edit_contacts'), 10, 3);
			}
			add_action('admin_post_'. static::$ifound_snake_slug,
				array($this, 'admin_post_' . static::$ifound_snake_slug));
		}
	}

	public function handle_bulk_actions_edit_contacts($redirect, $doaction, $object_ids) {
		if ($doaction === static::$ifound_snake_slug) {
			$redirect = menu_page_url(static::$snake_slug);
			$redirect = add_query_arg('contact_ids', $object_ids, $redirect);
		}
		return $redirect;
	}

	public function my_bulk_action_admin_notice() {
		if (!($_GET['page'] === static::$snake_slug && $_GET['done'] === 'true')) {
			return;
		}

		?>
		<div class="notice notice-success is-dismissible pad-me">Successfully created drip campaigns</div>
		<?php
	}

	public function create_bulk_drip_campaigns_page() {
		$css_handle = static::$ifound_snake_slug . '_css';
		wp_register_style($css_handle, plugins_url('css/create-bulk-drip-campaigns.css', __FILE__), array(), iFOUND_PLUGIN_VERSION);
		wp_enqueue_style($css_handle);
		?>
		<div class="ifound_wrap create-bulk-drip-campaigns-page">
			<h1>Create Bulk Drip Campaigns</h1>
			<?
			if ($_GET['page'] === static::$snake_slug && $_GET['done'] === 'true') {
				$this->create_bulk_drip_campaigns_page_done();
			} else {
				$this->create_bulk_drip_campaigns_page_start();
			} ?>
		</div>
		<?php
	}

	private function create_bulk_drip_campaigns_page_done() {
		$crm_page = iFoundCrm::new_hookless()->crm_menu();
		?>
		<div>Done creating bulk drip campaigns.</div>
		<div>
			Return to the <a href="<?= $crm_page ?>">contacts</a> page.
		</div>
		<?php
	}

	private function create_bulk_drip_campaigns_page_start() {
		if (!apply_filters('ifound_has_feature', 'drip-campaigns')) {
			do_action('ifound_warn_feature', 'drip-campaigns');
			return;
		}

		$js_handle = static::$ifound_snake_slug . '_js';
		wp_register_script($js_handle,
			plugins_url('js/create-bulk-drip-campaigns.js', __FILE__),
			['jquery', 'ifound_drip_campaign_shared_js'], iFOUND_PLUGIN_VERSION);
		$template_steps_and_titles = iFoundDripCampaign::new_hookless()->get_template_steps_and_titles();
		wp_localize_script($js_handle, 'ifound_drip_campaign', [
			'drip_template_steps_by_template_id' => $template_steps_and_titles['drip_template_steps_by_template_id'],
			'email_template_titles_by_id'        => $template_steps_and_titles['email_template_titles_by_id'],
		]);

		wp_enqueue_script($js_handle);
		wp_enqueue_script( 'email_editor_js' );
		wp_enqueue_script('jquery-ui-datepicker');
		$lookups = apply_filters('ifound_lookups_to_criteria', null);
		$contacts = apply_filters('ifound_get_contacts_from_request', $_GET['contact_ids']);
		$drip_templates = iFoundDripCampaign::new_hookless()->get_drip_templates();
		$crm = iFoundCrm::new_hookless()->crm();
		?>
		<div>You are creating drip campaigns for the following <?= count($contacts) ?> contacts.</div>
		<div class="contacts">
			<table class="striped" style="border: 1px silver solid;">
				<thead>
				<tr>
					<th>Name</th>
					<th>Email</th>
					<th>Address</th>
				</tr>
				</thead>
				<tbody>
				<?php foreach ($contacts as $contact) : ?>
					<tr>
						<td><?= $contact['name'] ?></td>
						<td><?= $contact['email'] ?></td>
						<td><?= $contact['address'] ?></td>
					</tr>
				<?php endforeach ?>
				</tbody>
			</table>
		</div>
		<form id="ifound-bulk-drip-campaign-form" action="<?= admin_url('admin-post.php') ?>" method="POST">
			<input type="hidden" name="action" value="<?= static::$ifound_snake_slug ?>">
			<?php foreach ($contacts as $contact) : ?>
				<input type="hidden" name="contact_ids[]" value="<?= $contact['id'] ?>">
			<?php endforeach ?>
			<div class="default-criteria-heading">
				<i class="fal fa-clone" aria-hidden="true"></i> <? _e('Template', 'ifound'); ?>
			</div>
			<table class="form-table">
				<tbody>
				<tr>
					<th scope="row"><label for="drip_template_id"><? _e('Drip / Task Template:', 'ifound'); ?></label>
					</th>
					<td>
						<select name="drip_template_id" class="drip_template_id ifound-validate" required="required">
							<option value="" selected="selected" disabled="disabled">(Select)</option>
							<?
							foreach ($drip_templates as $title => $id) {
								?>
								<option value="<?= $id ?>"><?= $title ?></option>
								<?php
							}
							?>
						</select>
					</td>
				</tr>
				<tr>
					<th scope="row"><label for="upcoming_step_index"><? _e('Start Step:', 'ifound'); ?></label>
					</th>
					<td>
						<select class="upcoming_step_index" name="upcoming_step_index" class="ifound-validate"
								required="required"
						>
							<!-- Options will be filled in by JavaScript -->
						</select>
						<div>
							<button type="button" class="show_hide_possible_steps" style="margin-top: 10px;">Show possible steps</button>
							<div class="possible_steps hidden" style="margin-top: 10px; opacity: 50%;">
								<table class="striped widefat">
									<thead>
									<tr>
										<th>Step Number</th>
										<th>Email Template for Customer</th>
										<th>Reminder to Agent</th>
										<th>Interval</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td colspan="4">Select a Drip Template ID above to show the steps</td>
									</tr>
									</tbody>
								</table>
							</div>
						</div>
					</td>
				</tr>
				</tbody>
			</table>

			<div class="title-heading default-criteria-heading"><i class="fal fa-heading"
																   aria-hidden="true"></i> <? _e('Title', 'ifound'); ?>
			</div>
			<table class="form-table">
				<tbody>
				<tr>
					<th scope="row"><label for="campaign_title"><? _e('Campaign Title:', 'ifound'); ?></label></th>
					<td><input type="text" name="campaign_title" id="campaign_title"
							   placeholder="e.g. Monthly Homeowner Tips"
							   class="search-title-input regular-text ifound-validate" required="required"></td>
				</tr>
				</tbody>
			</table>

			<div class="default-criteria-heading"><i class="fal fa-calendar-alt"
													 aria-hidden="true"></i> <? _e('Schedule', 'ifound'); ?></div>
			<table class="form-table">
				<tbody>
				<tr>
					<th scope="row"><label><? _e( 'Start Date:', 'ifound' ); ?></label></th>
					<td>
						<input class="start-date ifound-validate" type="text" name="start_date"
								pattern="[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]"
								title="Please enter a date in YYYY-MM-DD format"
								aria-label="Date in YYYY-MM-DD format"
						/>
					</td>
				</tr>
				<tr>
					<th scope="row"><label for="time_of_day"><? _e('Time of Day:', 'ifound'); ?></label></th>
					<td>
						<? do_action('ifound_time_of_day_select', 'now', array('now' => 'Now')); ?>
					</td>
				</tr>
				</tbody>
			</table>

			<div class="default-criteria-heading"><i
					class="fal fa-puzzle-piece"></i> <? _e('Branding', 'ifound'); ?></div>
			<table class="form-table">
				<tbody>
				<tr>
					<th scope="row"><label for="template_id"><? _e('Header:', 'ifound'); ?></label></th>
					<td><? do_action('ifound_email_dropdown', 'header', 'header', $crm->header); ?></td>
				</tr>
				<tr>
					<th scope="row"><label for="template_id"><? _e('Signature:', 'ifound'); ?></label></th>
					<td><? do_action('ifound_email_dropdown', 'signature', 'signature', $crm->signature); ?></td>
				</tr>
				<tr>
					<th scope="row"><label for="template_id"><? _e('Footer:', 'ifound'); ?></label></th>
					<td><? do_action('ifound_email_dropdown', 'footer', 'footer', $crm->footer); ?></td>
				</tr>
				</tbody>
			</table>

			<button type="submit" class="button button-primary" value="<?= static::$snake_slug ?>">
				Schedule Campaigns
			</button>
		</form>
		<?php
	}

	// This is a hook of type admin_post_{$action}, https://developer.wordpress.org/reference/hooks/admin_post_action/
	public function admin_post_ifound_create_bulk_drip_campaigns() {
		$contacts = apply_filters('ifound_get_contacts_from_request', $_POST['contact_ids']);
		$campaign_ids = [];
		$campaign_title = sanitize_text_field($_POST['campaign_title']);
		foreach ($contacts as $contact) {
			$data = new stdClass();
			$data->contact_id = $contact['id'];
			$data->campaign_title = $campaign_title;
			$data->to_email = $contact['email'];
			$data->drip_template_id = $_POST['drip_template_id'];
			$data->header = $_POST['header'];
			$data->signature = $_POST['signature'];
			$data->footer = $_POST['footer'];
			$data->upcoming_step_index = $_POST['upcoming_step_index'];
			$data->start_date = $_POST['start_date'];
			$data->time_of_day = $_POST['time_of_day'];
			$campaign_id = iFoundDripCampaign::new_hookless()->create_drip_campaign($data);
			// This meta value doesn't do anything yet. But I want to keep track of which campaigns are bulk campaigns, so
			// we can watch their behavior over time and see if it differs from non-bulk campaigns.
			update_post_meta($campaign_id, 'ifound_is_bulk_campaign', 'yes');
		}

		$redirect_url = admin_url('admin.php?page=' . static::$snake_slug);
		$redirect_url = add_query_arg('done', 'true', $redirect_url);
		wp_redirect($redirect_url);
	}
}

iFoundBulkDripCampaigns::static_init();
