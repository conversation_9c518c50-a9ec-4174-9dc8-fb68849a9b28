<?php
require_once("classes/class-auth.php");
require_once("classes/class-query_db.php");
require_once("classes/PropQuery.php");
require_once("classes/class-sanitizer.php");



//Create an array of the form information
while (list($key,$val)=each($_REQUEST)){ // HTTP_GET_VARS
	if(isset($query_info[$key])){
		$query_info[$key] = NULL;
	}
	$query_info[$key] = $val;
}

// Process The Form Cleaner (you can't trust anyone these days)
$sanitizer_obj = & new sanitizer;
$query_info = $sanitizer_obj->cleandata( $query_info ); //1.7



$auth_db = new Auth();
$db_obj = new sql_query();
$opt_obj = new PropQuery();



// Get authorization or die
$access_array = $auth_db->authenticate($query_info);


// Try the DBName with the query
$sql = "SELECT o.lookup_id, o.ShortValue, o.<PERSON>, o.Value
		FROM property_a_fields as f, property_lookup_opts as o
		WHERE f.LookupName = o.LookupName
		AND f.DBName = '".$query_info['q']."'";
$opt_array = $db_obj->get_process_sql($sql);
//print "<pre>"; print_r($opt_array); print "</pre>";



// if the DBName didn't work to get any results, then try the LookupNmae directly
if(!is_array($opt_array)){
	$sql = "SELECT lookup_id, ShortValue, LongValue, Value
			FROM `property_lookup_opts`
			WHERE `LookupName` = '".$query_info['q']."'";
	$opt_array = $db_obj->get_process_sql($sql);

}
//print "<pre>"; print_r($opt_array); print "</pre>";



// if the DBName or the LookupNmae didn't work try the opts class
/**
 * @TODO: Create the query to find options from the static options
 */
if(!is_array($opt_array)){

}


if(is_array($opt_array)){
	echo json_encode($opt_array);
}else{
	echo json_encode('oops! - Error');
}
?>