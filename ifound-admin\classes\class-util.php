<?php

class Util {
	public function current_url() {
		return 'http' . ( isset( $_SERVER['HTTPS'] ) ? 's' : '') . '://' . "{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
	}

	// This is array_map() for associative arrays, allowing you to modify the key and the value. The callback should
	// return an array (tuple) of [new key, new value] (note: not [new key => new value]).
	public function array_map_modify_both($fn, $array) {
		$mapped = [];
		foreach ($array as $key => $value) {
			[$new_key, $new_value] = $fn($key, $value);
			$mapped[$new_key] = $new_value;
		}
		return $mapped;
	}
}
