[database]
protocol = mysql
profiler = true
username = root
password = root
host = idx_db
dbname = pfndidx_azdb

[retsdb]
protocol = mysql
username = root
password = root
host = idx_db
dbname = pfndidx_azdb

[log]

log_access = false

[notify]

;member_status =
;action_log =

[armls]
login_url = http://retsgw.flexmls.com/rets2_3/Login
username = az.rets.profound
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = B
mls_class_land = C
db_table_prefix = armls_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = OV61GOJ13C0
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = ActiveAgent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = armls_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[armls_spark]
prop_table = Property
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = armls_spark_property_
listing_id = ListingKey
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
img_table = Media
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = armls_spark_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingKey
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Phoenix

[naar_spark]
prop_table = Property
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = naar_spark_property_
listing_id = ListingKey
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
img_table = Media
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = naar_spark_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingKey
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Phoenix

[glvarnv]
login_url = http://rets.las.mlsmatrix.com/rets/Login.ashx
username = profound
rets_version = 1.7.2
dont_fetch_img_location = true
s3_bucket = "glvarnv-images"
offset_support = true
prop_table = Property
mls_class_all = Listing
mls_class_res = 1
mls_class_rentals = 9
mls_class_land = 5
db_table_prefix = glvarnv_property_
listing_id = Matrix_Unique_ID
status_field = Status
active_status = A
img_table = Media
prop_img_class = LargePhoto
prop_thumbnail_class = Photo
lastmod = MatrixModifiedDT
picture_mod = PhotoModificationTimestamp
image_location_field_name = Location

[mredil]
login_url = http://connectmls-rets.mredllc.com/rets/server/login
username = RETS_O_86528_15
rets_version = 1.7.2
offset_support = true
s3_bucket = "mredil-images2"
prop_table = Property
mls_class_res = ResidentialProperty
mls_class_rentals = RentalHome
mls_class_land = LotsAndLand
db_table_prefix = mredil_property_
listing_id = LN
mls_listing_id_name = LN
status_field = ST
active_status = ACTV
img_table = Media
prop_img_class = Photo
prop_thumbnail_class = ThNail
prop_highres_class = HrPhoto
lastmod = RECORDMODDATE
picture_mod = PHOTODATE
user_agent = ""
image_location_field_name = Location

[tarmlsaz]
login_url = http://retsgw.flexmls.com/rets2_3/Login
username = tar.rets.profoundmktg
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = E
mls_class_land = B
db_table_prefix = tarmlsaz_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = UD87EN8M6PQ
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
user_agent = ""
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = Agent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = tarmlsaz_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[paaraz]
login_url = http://retsgw.flexmls.com/rets2_3/Login
username = paz.rets.ifoundagent
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = F
mls_class_land = B
db_table_prefix = paaraz_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = 15614ASF9O39
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
user_agent = ""
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = Agent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = paaraz_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[paaraz_mlg]
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = paaraz_mlg_property_
listing_id = ListingId
mls_listing_id_name = ListingId
status_field = StandardStatus
active_status = Active
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = paaraz_mlg_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingId
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Denver

[sdcrca]
login_url = https://sdmls-rets.connectmls.com:443/server/login
username = 500561
; user_agent = "RETSMD/1.0"
rets_version = 1.5
offset_support = true
mls_class_res = RE_1
mls_class_rentals = RT_4
mls_class_land = LN_3
db_table_prefix = sdcrca_property_
prop_table = Property
listing_id = L_ListingID
status_field = L_Status
prop_img_class = Photo
lastmod = L_UpdateDate
picture_mod = L_Last_Photo_updt
mls_listing_id_name = L_ListingID

[sndmls]
login_url = http://sdmls-spn-rets.paragonrels.com/rets/fnisrets.aspx/SPN/login
username = 500561
; user_agent = "RETSMD/1.0"
rets_version = 1.7.2
offset_support = false
mls_class_res = 9
mls_class_rentals = 18
mls_class_land = 12
db_table_prefix = sndmls_property_
prop_table = Property
listing_id = sysid
status_field = Status_f102
prop_img_class = Photo
lastmod = LastTransDate_f141
picture_mod = LastImgTransDate_f1385

[trendmls]
login_url = http://trend.trendrets.com:6103/platinum/login
username = TRNIFNDPLS
rets_version = 1.7.2
user_agent = "MRIS Conduit/2.2"
; login_url = http://csrets.mris.com:6103/platinum/login
; username = 3237212
; rets_version = 1.8
; user_agent = "Bright RETS Application/1.0"
offset_support = true
prop_table = Property
mls_class_res = RES
mls_class_rentals = RNT
mls_class_land = LOT
db_table_prefix = trendmls_property_
listing_id = ListingKey
status_field = LocaleListingStatus
active_status = ACTIVE
lastmod = ModificationTimestamp
media_res = Media
prop_img_class = PROP_MEDIA
prop_media_key = PropObjectKey
prop_media_type_key = PropMediaType
prop_media_type = Photo
prop_media_order = PropItemNumber
prop_media_caption = PropMediaCaption
picture_mod = MaxPhotoTms
image_location_field_name = PropMediaURL

[brightmls]
login_url = http://bright-rets.brightmls.com:6103/cornerstone/login
username = 3237212
rets_version = 1.8
user_agent = "Bright RETS Application/1.0"
offset_support = true
prop_table = Property
mls_class_res = RESI
mls_class_rentals = RLSE
mls_class_land = LAND
db_table_prefix = brightmls_property_
listing_id = ListingKey
status_field = MlsStatus
active_status = ACTIVE
lastmod = ModificationTimestamp
media_res = Media
prop_img_class = PROP_MEDIA
prop_media_fields_whitelist = MediaURLFull,MediaURLHD,MediaURLHiRes,MediaURLMedium,MediaURLThumb
prop_media_preferred = PreferredPhotoYN
prop_media_key = ResourceRecordKey
prop_media_type_key = MediaType
prop_media_type = Photo
prop_media_order = MediaItemNumber
prop_media_caption = MediaShortDescription
picture_mod = PhotosChangeTimestamp
image_location_field_name = MediaURL
office_resource = Office
office_table_infix = office
office_id = OfficeKey
office_lastmod = ModificationTimestamp
office_attribution_contact = OfficeBrokerLeadPhoneNumber
agent_resource = ActiveAgent
agent_table_infix = agent
agent_id = MemberKey
agent_lastmod = ModificationTimestamp
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = brightmls_open_house_OpenHouse
open_house_id = OpenHouseKey
open_house_lastmod = OpenHouseModificationTimestamp
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = OpenHouseListingKey
open_house_php_format = Y-m-d H:i:s

[naar]
login_url = http://retsgw.flexmls.com/rets2_3/Login
username = naz.rets.ifoundagent
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = B
mls_class_land = C
db_table_prefix = naar_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = 12KFKCZKM878
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
user_agent = ""
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = Agent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = naar_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[crmls]
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = crmls_property_
listing_id = ListingKey
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
daysago = 1095
is_reso_web_api = true
open_house_table = crmls_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingKey
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Los_Angeles

[cabor]
login_url = http://retsgw.flexmls.com:80/rets2_3/Login
;username = caz.rets.ch0230
username = caz.rets.bi583-2
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = B
mls_class_land = C
db_table_prefix = cabor_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = 12KFKCZKM878
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
user_agent = ""
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = Agent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = cabor_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[wmar]
login_url = http://retsgw.flexmls.com:80/rets2_3/Login
username = wmar.rets.owusa
rets_version = 1.7.2
offset_support = true
prop_table = Property
mls_class_res = A
mls_class_rentals = B
mls_class_land = D
db_table_prefix = wmar_property_
listing_id = LIST_1
mls_listing_id_name = LIST_105
status_field = LIST_15
active_status = 12KFKCZKM878
img_table = Media
prop_img_class = Photo
lastmod = LIST_87
picture_mod = LIST_134
user_agent = ""
image_location_field_name = Location
office_resource = Office
office_table_infix = office
office_id = OFFICE_0
office_lastmod = TIMESTAMP
office_attribution_contact = OFFICE_33
office_short_id = OFFICE_15
agent_resource = Agent
agent_table_infix = agent
agent_id = MEMBER_0
agent_lastmod = TIMESTAMP
agent_attribution_contact = MEMBER_38
agent_short_id = MEMBER_17
open_house_resource = OpenHouse
open_house_table_infix = open_house
open_house_table = wmar_open_house_OpenHouse
open_house_id = EVENT0
open_house_lastmod = EVENT6
open_house_event_start = EVENT100
open_house_event_end = EVENT200
open_house_listing_id = LIST1
open_house_php_format = Y-m-d H:i:s

[recolorado]
mls_class_res = RESI
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = recolorado_property_
listing_id = ListingKey
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
dont_fetch_img_location = true
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = recolorado_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingKey
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Denver

[recolorado_mlg]
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = recolorado_mlg_property_
listing_id = ListingId
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
dont_fetch_img_location = true
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = recolorado_mlg_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingKey
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Denver

[realtracs]
mls_class_res = res
mls_class_rentals = rentals
mls_class_land = land
db_table_prefix = realtracs_property_
listing_id = ListingId
mls_listing_id_name = ListingId
status_field = MlsStatus
active_status = Active
dont_fetch_img_location = true
lastmod = ModificationTimestamp
picture_mod = PhotosChangeTimestamp
is_reso_web_api = true
open_house_table = realtracs_open_house
open_house_event_start = OpenHouseStartTime
open_house_event_end = OpenHouseEndTime
open_house_listing_id = ListingId
open_house_php_format = Y-m-d H:i:s.v
open_house_convert_to_timezone = America/Chicago

[crm]
api_base_url =

[google]
api_key =

[solr]
host = solr
port = 80
