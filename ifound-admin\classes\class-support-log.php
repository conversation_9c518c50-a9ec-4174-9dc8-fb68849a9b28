<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * SupportLog Class
 *
 * @since 1.0.6
 */

class SupportLog{
	
	/**
	 * init SupportLog class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }
	
	/**
	 * Constructor
	 *
	 * @since 1.0.6
	 */
	 
	public function __construct() {
		
		add_action( 'add_meta_boxes', array( $this, 'add_log_metabox' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'log_scripts' ) );	
		add_action( 'wp_ajax_new_log_ajax', array( $this, 'new_log_ajax' ) );

	}

	/**
	 * Log Scripts
	 *
	 * @since 1.0.6
	 */
	 
	public function log_scripts(){
		
		wp_register_script( 'log_js', plugins_url( 'js/support-log.js', __DIR__ ), array( 'jquery' ), iFOUND_ADMIN_VERSION );	
		wp_localize_script( 'log_js', 'log', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'log_secure_me' )
		));

	}

	/** 
	 *	Add Log Metabox
	 *
	 *	Add metaboxes.
	 *
	 *	@since 1.0.6
	 *	
	 *	@link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */
	public function add_log_metabox() {
		
		add_meta_box( 
			'log_meta', 
			__( '<i class="far fa-book"></i> Support Log', 'ifound' ), 
			array( $this, 'log_metabox'), 
			array( 'clients', 'feature_log', 'template', 'sliders', 'mls_association', 'featured_images', 'content', 'broker_logo' ),
			'advanced',
            'default'
		);
		
	}

	/** 
	 *	Log Body
	 *
	 *  The body of the log in log metabox.
	 *
	 *	@since 1.2.39
	 *  @since 2.4.26 Add pretty date.
	 */

	public function log_body( $date, $text ) { 

		ob_start(); ?>

		<li>
			<span class="log-text-label"><? _e( apply_filters( 'pretty_date',  $date ), 'ifound' ) ; ?></span>
			<span class="log-text"><? _e( $text, 'ifound' ) ; ?></span>
		</li><?

		return ob_get_clean(); 

	}

	/** 
	 *	New log Ajax
	 *
	 *  Stores the new log in the db. Returns a respose to jQuery.
	 *
	 *	@since 1.2.39
	 *  @since 2.4.26 Strup slashes from text in response.
	 */

	public function new_log_ajax() {
		
		check_ajax_referer( 'log_secure_me', 'log_nonce' );
		
		$input = $_REQUEST['input'];

		$text = sanitize_text_field( $input['text'] );

		$client_id = intval( $input['client_id'] );
		
		if( is_int( $client_id ) && ! empty( $text ) ) {

			$date = $this->add_support_log( $client_id, $text );

			$response = array(
				'class' 	=> 'fa-check-circle',
				'log'		=> '<span class="new-log">' . stripslashes( $this->log_body( $date, $text ) ) . '</span>',
				'success'	=> true
			);

		} else {
			
			$response = array(
				'class' 	=> 'fa-exclamation-triangle',
				'log'		=> false,
				'success'	=> false
			);
			
		}
		
		echo json_encode( $response );

		die();
		
	}

	/** 
	 *	Add Support Log
	 *
	 *  Stores the new log in the db.
	 *
	 *	@since 1.3.0
	 */

	public static function add_support_log( $client_id, $text ) {

		$date = current_time( 'mysql' );

		$log = (object) array(
			'date' 	=> $date,
			'text'	=> $text
		);

		add_post_meta( $client_id, 'log', $log );

		return $date;
		
	}

	/** 
	 *	log Metabox
	 *
	 *	@since 1.2.39
	 */

	public function log_metabox() { 

		wp_enqueue_script( 'log_js' ); 

		$client_id = get_the_ID(); ?>

		<ul id="ifound-log-wrapper"><?

			if( $log = get_post_meta( $client_id, 'log' ) ) { 

				krsort( $log ); 
				
				foreach( $log as $log ) { 

					echo $this->log_body( $log->date, $log->text );

				}

			} ?>

		</ul>

		<table class="form-table log_meta views_meta">
			
			<tbody>

				<tr>
					<th colspan="4" scope="row"><label for="new-log-text"><? _e( 'New log:', 'ifound' ) ; ?></label></th>
				</tr>

				<tr>
					<td colspan="4"><textarea id="new-log-text" class="large-text"></textarea></td>
				</tr>

				<tr>
					<td colspan="4">
						<div class="button button-primary add-log">
							<i class="fal fa-plus-square new-log-spinner"></i> 
							<? _e( 'Save log', 'ifound' ) ; ?>
						</div>
					</td>
				</tr>
				
			</tbody>

		</table>

		<input type="hidden" id="client_id" value="<? echo $client_id; ?>"><?

	}

}
