<?php

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundActionScheduler {
	use NewHooklessTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			// None yet
		}
	}

	// I don't see any public API for this in Action Scheduler. This is for Action Scheduler 3.3, and of course it could
	// change at any point, which is why I'm putting it in this central function.
	public function get_action_scheduler_job_status($action_id) {
		global $wpdb;
		$sql = "select status from {$wpdb->prefix}actionscheduler_actions where action_id = %d";
		$prepared_sql = $wpdb->prepare($sql, $action_id);
		$results = $wpdb->get_results($prepared_sql, ARRAY_A);
		if (count($results)) {
			return $results[0]['status'];
		}
		return null;
	}

	// UPDATE: Looking through the Action Scheduler source, it looks like as_has_scheduled_action()'s $args and $group
	// params are optional. I did not test this function I wrote.
	// I want to know if there are any actions yet to finish processing. I don't see a way to do this through the
	// official API, so we do it ourselves. There is as_has_scheduled_action, but it requires $args and $group.
	// public function is_any_action_unfinished_for_hook($hook) {
	// 	return as_has_scheduled_action($hook, null, 'ifound');
	// 	global $wpdb;
	// 	$sql = "select status from {$wpdb->prefix}actionscheduler_actions where hook = %s";
	// 	$prepared_sql = $wpdb->prepare($sql, $hook, $group);
	// 	$results = $wpdb->get_results($prepared_sql, ARRAY_A);
	// 	return count($results) !== 0;
	// }
}
