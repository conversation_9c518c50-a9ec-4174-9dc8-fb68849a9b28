/**
 * @summary Dnamic.
 *
 * Adds and removes inputs from a dynamic form.
 *
 * @since 1.0.0
 * @since 2.4.14 Remove depricated dynamic-input-single input.
 * @since 2.5.62 Add clickEvent for click on desktop or touchstart on mobile
 */

jQuery( document ).ready( function( $ ) {
	function runSearch() {
		jQuery(window).trigger('ifound:search');
	}

	var clickEvent = dynamic.clickEvent;
	var potentiallyUnfinishedSearchCriteria = {};
	var isMultipleStateMls = dynamic.isMultipleStateMls;

	var useUserMlsClassPreference = $( '.mls_class_select' ).data('pfmls-use-user-mls-class-preference');
	if (useUserMlsClassPreference) {
		var pref = readLocalStorage()['userMlsClassPreference'];
		// Since the default is residential, we don't want to run an extra XHR request here (where we block the user's
		// interaction with the app using the while-we-wait spinner.
		if (pref && pref !== 'Residential') {
			$('.mls_class_select').val(pref);
			updateSearchCriteria('mls_class_select', pref);
		}
	}

	$( '.budget-heading' ).on( 'click', function() {
  		$('.budget-body' ).slideToggle( 'slow' );
	});

	$( '.display-stats-criteria-heading' ).on( 'click', function() {
		$('.display-stats' ).slideToggle( 'slow' );
	});

	$( document ).on(clickEvent, '.dynamic-input-remove', function() {
		$(this).closest('.dynamic-input').remove();
		runSearch();
	});

	function addInputToForm(input) {
		// Ensure the search criteria is added before the placeholder. The placeholder disappears when an item added.
		// It disappears due to CSS.
		$( '.ifound-dynamic-form' ).find('.search-criteria-placeholder').before(input);
		runSearch();
	}

	function showOrHideMoreFilters() {
		// Hide "More Filters" button if the criteria is not visible
		if( $( '.search-criteria-wrapper' ).css( 'display' ) === 'none' )
			$( '.more-filters-heading' ).css( 'display', 'none' );
		else
			$( '.more-filters-heading' ).css( 'display', 'flex' );
	}
	showOrHideMoreFilters();
	var resizeObserver = new ResizeObserver(showOrHideMoreFilters);
	var searchCriteriaHeading = document.querySelector('.search-criteria-heading');
	if (searchCriteriaHeading) {
		resizeObserver.observe(searchCriteriaHeading);
	}

	/** Add an input for a single selection. Input is an array. */
	$( document ).on( clickEvent, '.dynamic-input-array', function() {

		$( this ).addClass( 'active' );

		var label = $( this ).attr( 'label' );
		var name = $( this ).attr( 'name' );
		var value = $( this ).attr( 'value' );
		var display = $( this ).attr( 'display' );

		var mls_id_input = '';
		if( name == 'mls_id' ) {
			mls_id_input = 'mls-id-input';
		}

		var input = '<div class="dynamic-input dynamic-input-' + value + '">'
		input +=	'<div class="dynamic-input-label">';
		input +=	label + ' - ' + display;
		input +=	'<i class="fa fa-times-circle dynamic-input-remove data-id-remove ' + mls_id_input + '" data_id="' + value + '"></i>';
		input +=	'</div>';
		input +=	'<input type="hidden" name="' + name + '[]" value="' + value + '" class="dynamic-value">';
		input +=	'</div>';

		addInputToForm(input);

	});

	function addSearchCriteria(fields) {
		var label = fields.label;
		var name = fields.name;
		var value = fields.value;
		var display = fields.display;

		var input = '<div class="dynamic-input">'
		input +=	'<div class="dynamic-input-label">';
		input +=	label + ' - ' + display;
		input +=	'<i class="fa fa-times-circle dynamic-input-remove"></i>';
		input +=	'</div>';
		input +=	'<input type="hidden" name="' + name + '[]" value="' + value + '" class="dynamic-value">';
		input +=	'</div>';

		addInputToForm(input);
	}

	/** Add an input for a single text input. */
	$( document ).on( clickEvent, '.dynamic-input-plus', function() {
		var label = $( this ).attr( 'label' );
		var name = $( this ).attr( 'name' );
		var value = $( this ).siblings( '.dynamic-input-text' ).val();
		var display = $( this ).siblings( '.dynamic-input-text' ).val();

		if (value) {
			addSearchCriteria({
				label: label,
				name: name,
				value: value,
				display: display,
			});
			$(this).parents('.lookups-body' ).slideUp();
			$(this).parents('.lookups-body').find('.dynamic-input-listen').val('');
		}
	});

	/** Add an input for a text input with range.  */
	$( document ).on( clickEvent, '.dynamic-input-range', function() {

		var label = $( this ).attr( 'label' );
		var name = $( this ).attr( 'name' );
		var min = $( this ).siblings( '.dynamic-input-min' ).val();
		var max = $( this ).siblings( '.dynamic-input-max' ).val();

		if(min.length){
			var input = '<div class="dynamic-input">'
			input +=	'<div class="dynamic-input-label">';
			input +=	min + ' - Min ' + label;
			input +=	'<i class="fa fa-times-circle dynamic-input-remove"></i>';
			input +=	'</div>';
			input +=	'<input type="hidden" name="' + name + '[min]" value="' + min + '" class="dynamic-value">';
			input +=	'</div>';

			addInputToForm(input);

		}

		if(max.length){
			var input = '<div class="dynamic-input">'
			input +=	'<div class="dynamic-input-label">';
			input +=	max + ' - Max ' + label;
			input +=	'<i class="fa fa-times-circle dynamic-input-remove"></i>';
			input +=	'</div>';
			input +=	'<input type="hidden" name="' + name + '[max]" value="' + max + '" class="dynamic-value">';
			input +=	'</div>';

			addInputToForm(input);

		}

		if (min || max) {
			$(this).parents('.lookups-body' ).slideUp();
			$(this).parents('.lookups-body').find('.dynamic-input-listen').val('');
		}
	});

	/*
         * City autofill section
	 */
	$( document ).on( clickEvent, '.validate-autofill', function() {
		var label = $( this ).attr( 'label' );
		var name = $( this ).attr( 'name' );
		var value = $( this ).parent().find( '.dynamic-input-autofill').val();
		if (!value) {
			return;
		}
		var display = $( this ).siblings( '.dynamic-input-text' ).val();
		if (isMultipleStateMls) {
			var parts = value.split(',');
			var mainValue = parts[0];
			var stateValue = parts[1].replace(' ', '');

			createInput(label, name, mainValue, display);
			createInput('State', 'state', stateValue, display);
		} else {
			createInput(label, name, value, display);
		}
		$(this).parents('.lookups-body' ).slideUp();
		$(this).parents('.lookups-body').find('.dynamic-input-listen').val('');
	});

	function createInput(label, name, value, display) {
		var input = '<div class="dynamic-input">'
		input +=	'<div class="dynamic-input-label">';
		input +=	label + ' - ' + value;
		input +=	'<i class="fa fa-times-circle dynamic-input-remove"></i>';
		input +=	'</div>';
		input +=	'<input type="hidden" name="' + name + '[]" value="' + value + '" class="dynamic-value">';
		input +=	'</div>';
		addInputToForm(input);
	}

	var overlay = $('.dynamic-input-autofill:first').clone();
	overlay.css({
		'position': 'absolute',
		'top': 0,
		'left': 0,
		'color': '#777',
		'width': '100%',
	}).addClass('overlay').attr({
		'placeholder': '',
		'id': '',
	});

	$('.dynamic-input-autofill').css({
		'position': 'relative',
		'z-index': '9999',
		'background': 'transparent',
		'width': '100%',
		}).parent().append(overlay);

	$(document).on('input', '.dynamic-input-autofill', function() {
		autofill($(this));
	});

	function doesPrefixExist(values) {
		let vParts = values.split(' ');
		for(let vPart of vParts) {
			if(!isNaN(vPart)) return true;
		}
		return false;
	}

	var isPrefixDeleted = false;
	function autofill(inputField) {
		var nameParts = $(inputField).val().split(' ');
		for(let i = 0; i < nameParts.length; i++) {
			nameParts[i] = nameParts[i].toLowerCase();
			let isStatePart = isMultipleStateMls && nameParts.length > 1 && i === nameParts.length -1 && nameParts[i-1].includes(',');
			if(isStatePart) {
				nameParts[i] = nameParts[i].toUpperCase();
			} else {
				nameParts[i] = nameParts[i].replace(/[a-z]/, nameParts[i].charAt(0).toUpperCase());
			}
		}

		$(inputField).val(nameParts.join(' '));
		var currVal = $(inputField).val();
		var input = $(inputField);
		var overlay = $(inputField).next('.overlay');

		if(currVal === '') {
			$(overlay).val('');
			$('.autofill-hint').remove();
			return;
		}

		var targetList = null;
		switch($(inputField).attr('placeholder')) {
			case 'City': targetList = cities;
			break;
			// The list is originally not sorted properly
			case 'School District':
				if(!isPrefixDeleted) {
					for(let i = 0; i < sDistricts.length; i++) {
						if( doesPrefixExist(sDistricts[i])) sDistricts[i] = sDistricts[i].substr(sDistricts[i].indexOf('- ') + '- '.length);
					}
					sDistrcits = sDistricts.sort();
					isPrefixDeleted = true;
				}
				targetList = sDistricts;
			break;
			case 'Zip': targetList = zips;
		}

		for(let i = 0; i < targetList.length; i++) {
			if(targetList[i].startsWith(currVal)) {
				$(overlay).val(currVal + targetList[i].replace(currVal, ''));
				var matchArray = [targetList[i]];
				while( i !== targetList.length -1 && targetList[++i].startsWith(currVal) && matchArray.length < 11) {
					matchArray.push(targetList[i]);
				}
				$.autofillDropdown(inputField, matchArray);
				$(inputField).parent().on('keydown',function(e) {
					if(e.which == 39 || e.which == 9) {
						$(input).val($(overlay).val());
					}
				});
				return;
			}
			if(i === targetList.length -1) overlay.val('');
		}
	}
	$.autofill = autofill;
	function autofillDropdown(mainInput, cities) {
		$('.autofill-dropdown').remove();

		var dropdownBeg = '<div class="autofill-dropdown">';
		var dropdownEnd = '</div>';

		var inputs = '';
		for(let city of cities) {
			inputs += '<div class="autofill-hint" value="' + city + '">' + city + '</div>';
		}
		$('.autofill-hint').css({
			'cursor': 'pointer',
			'width': '100%',
		});
		$(document).on('click', '.autofill-hint', function() {
			$(mainInput).val($(this).attr('value'));
			$(mainInput).next('.overlay').val($(this).attr('value'));
			$(this).parent().slideUp('slow').remove();

		});
		$(mainInput).parent().append(dropdownBeg + inputs + dropdownEnd);
	}
	$.autofillDropdown = autofillDropdown;
	/*=======================================================AUTOFILL SECTION END==========================*/

	//Do not use this. It is here to let us know that this is elsewhere.
	//@see results-map.js
	//@see advanced.js
	//$( document ).on( 'click', '.dynamic-input-remove', function() {
		//$( this ).parents( '.dynamic-input' ).remove();
	//});

	$( document ).on( 'keydown', '.dynamic-input-listen', function() {
		// This first one is special for the city autofill
		$(this).parents('.lookups-body').find('.dynamic-input-validate' ).addClass( 'active' );
		$(this).parent('.ifound-wrap').find('.dynamic-input-validate' ).addClass( 'active' );
	});

	$( document ).on( clickEvent, '.dynamic-heading', function() {
		$(this).parents('.dynamic-button').children('.lookups-body' ).slideToggle( 'slow' );
		// This first one is special for the city autofill
		$(this).siblings('.lookups-body').find('.dynamic-input-listen:first').focus();
		$(this).siblings('.lookups-body').children('.ifound-wrap').children('.dynamic-input-text').focus();
		$(this).siblings('.lookups-body').children('.ifound-wrap').children('.dynamic-input-min').focus();
	});

	$( '.display-options-criteria-heading' ).on( clickEvent, function() {
		$(this).siblings('.display-options' ).slideToggle( 'slow' );
	});

	$( '.criteria-heading' ).on( clickEvent, function() {
  		$( this ).siblings('.criteria-body' ).slideToggle( 'slow' );
	});

	$( '.more-filters-heading' ).on( clickEvent, function() {
		$( '.more-filters-criteria' ).slideToggle( 'slow' );
	});

	// Here's what we're doing. If the user types values, but doesn't press the Add button, we're going to assume that
	// they meant to press Add when they later press Search. To do this, we need to record which controls they're
	// touching and which Add button to click. I thought this would be much more difficult to track everything, but
	// it turns out, based on how things work, we can keep it simple. We basically just always call the add button,
	// which will validate the data entered, and not doing anything if the data isn't valid. Thus, for example, if the
	// user DOES push the Add button themselves, that'll clear out the inputs. Later, when we call Add for them anyway
	// (because we don't bother removing anything from the list of Add functions), at that point it'll do nothing.
	$('.dynamic-input-listen').on('change', function() {
		var addButton = $(this).parents('.lookups-body').find('.dynamic-input-validate');
		var fieldName = addButton.attr('name');
		potentiallyUnfinishedSearchCriteria[fieldName] = addButton.click.bind(addButton);
	});
	window.iFoundAddPotentiallyUnfinishedSearchCriteria = function() {
		for (var fn of Object.values(potentiallyUnfinishedSearchCriteria)) {
			fn();
		}
	};

	$( '.search-bar .mobile-show-criteria' ).on( clickEvent, function() {
		$( '.search-criteria-wrapper, .ifound-dynamic-form' ).slideToggle( 'slow' );
		$( '.mobile-show-criteria' ).toggleClass( 'active' );

		if( $( '.more-filters-heading' ).css( 'display' ) === 'none' )
			$( '.more-filters-heading' ).css( 'display', 'flex' );
		else
			$( '.more-filters-heading' ).css( 'display', 'none' );
	});

	$( function() {
    	var dateFormat = "yy-mm-dd",
      	from = $( "#ifound-to" )
        .datepicker({
         	defaultDate: "+1w",
          	changeMonth: true,
      		changeYear: true,
          	numberOfMonths: 1,
			dateFormat: dateFormat
        })
        .on( "change", function() {
          	to.datepicker( "option", "minDate", getDate( this ) );
			to.datepicker( "option", "dateFormat", dateFormat );
        }),
      	to = $( "#ifound-from" ).datepicker({
			defaultDate: "+1w",
			changeMonth: true,
			changeYear: true,
        	numberOfMonths: 1,
			dateFormat: dateFormat
      	})
		.on( "change", function() {
        	from.datepicker( "option", "maxDate", getDate( this ) );
			from.datepicker( "option", "dateFormat", dateFormat );
     });

    function getDate( element ) {
      	var date;
      	try {
        	date = $.datepicker.parseDate( dateFormat, element.value );
      	} catch( error ) {
        	date = null;
      	}

      	return date;
    	}
  	});

	function updateSearchCriteria(id, mlsClass) {
		$('.while-we-wait').addClass( 'active' );
		var url = dynamic.endpoint + id + '?mls_class=' + mlsClass + '&more_criteria_class=' + moreCriteriaClassName;
		$.getJSON( url, function( data ) {
			// We replace the entirety of the additional criteria. Then we loop over all the additional criteria fields,
			// and if the field matches one of the criteriaFields list, we show it in the search criteria section,
			// otherwise we hide it in the search criteria section.
			$( '.' + moreCriteriaClassName ).replaceWith( data );
			let searchCriteria = [];

			if (mlsClass !== 'Residential') {
				let aHeadings = $('.' + moreCriteriaClassName + ' .lookups-body .dynamic-input-criteria');
				let names = [];
				for(let aHeading of aHeadings) {
					names.push($(aHeading).attr('name'));
				}
				let uNames = [names[0]];
				for(let i = 1; i < names.length; i++) {
					let found = false;
					for(let uName of uNames) {
						if(uName.includes(names[i])) {
							found = true;
							break;
						}
					}
					if(!found) uNames.push(names[i]);
				}
				for(let uName of uNames) {
					for(let criteriaField of criteriaFields) {
						if(criteriaField.includes(uName)) {
							searchCriteria.push(uName);
							break;
						}
					}
				}
			} else searchCriteria = criteriaFields;

			let currSCriteria = $('.' + criteriaClassName + ' .dynamic-input-criteria');
			for(let i = 0; i < currSCriteria.length; i++) {
				let found = false;
				for(let searchCriterion of searchCriteria) {
					if(searchCriterion === $(currSCriteria[i]).attr('name')) {
						found = true;
						break;
					}
				}
				let parentButton = $(currSCriteria[i]).closest('.dynamic-button');
				let skippedChildren = $(parentButton).find('.dynamic-input-criteria');
				i += skippedChildren.length - 1;
				if(!found) parentButton.css('display', 'none');
				else if(found && parentButton.css('display') === 'none') parentButton.css('display', 'inline-block');
			}
			$('.while-we-wait').removeClass('active');
		});
	}

	$(document).on('change', '.mls_class_select, .mls_class_select_admin', function() {
		var mlsClass = $(this).val();
		$(window).trigger('ifound:mls-class-select', [mlsClass]);
		setLocalStorageValue('userMlsClassPreference', mlsClass);
		var id = this.id;
		if (!id.includes('campaign-builder')) {
			id = this.className.includes('mls_class_select_admin') ? 'mls_class_select_admin' : 'mls_class_select';
		}
		updateSearchCriteria(id, mlsClass);
	});

	/** Choose Data IDs @since 2.5.9 */
	var data_ids = new Array();
	$( '.choose-mls-ids' ).on( 'change', function() {
		$( '.save-alert-form' ).toggleClass('hide-choose-mls-ids');
	});
	$( document ).on( clickEvent, '.data-id-remove', function() {
  		var data_id = $(this).attr('data_id');
  		$('.active').each(function(){
  			if( data_id == $(this).attr('value')){
  				$(this).removeClass('active');
  				if( $(this).hasClass('mls-id-input') ) {
  					$(this).html('Include');
  				}
  			}
  		});
  		data_ids = $.grep(data_ids, function(value) {
  			return value != data_id;
		});
	});
	$( document ).on( clickEvent, '.doing-campaign-button', function() {
  		var data_id = $(this).attr('value');
  		data_ids.push(data_id);
  		$(this).html('Remove');
	});
	$( document ).on( clickEvent, '.doing-campaign-button.active', function() {
  		var data_id = $(this).attr('value');
  		$('.dynamic-input-' + data_id).remove();
  		$(this).removeClass('active');
  		if( $(this).hasClass('mls-id-input') ) {
  			$(this).html('Include');
  		}
  		data_ids = $.grep(data_ids, function(value) {
  			return value != data_id;
		});
	});
	$.reMarkActive = function(){
		$('.doing-campaign-button').each(function(){
			var value = $(this).attr('value');
	  		if( $.inArray(value, data_ids ) > -1 ){
	  			$(this).addClass('active').html('Remove');
	  		}
	  	});
	}
	/** END Choose Data IDs */

	function onAddButtonClicked(radius, lat, lng, address, mls_id) {
		var radiusString = radius + ' mile(s)';

		var input = '';
		input += '<div class="dynamic-input">'
		input += '<div class="dynamic-input-label">';
		input += address + ' - Address, ' + radiusString + ' - Radius';
		input += '<i class="fa fa-times-circle dynamic-input-remove"></i>';
		input += '</div>';
		input += '<input type="hidden" name="nearby[radius]" value="' + radius + '" class="dynamic-value">';
		input += '<input type="hidden" name="nearby[lat]" value="' + lat + '" class="dynamic-value">';
		input += '<input type="hidden" name="nearby[lng]" value="' + lng + '" class="dynamic-value">';
		input += '<input type="hidden" name="nearby[address]" value="' + address + '" class="dynamic-value">';
		if (mls_id) {
			input += '<input type="hidden" name="nearby[mls_id]" value="' + mls_id + '" class="dynamic-value">';
		}
		input += '</div>';

		addInputToForm(input);
	}
	window.ifound_add_radius = onAddButtonClicked;
	window.ifound_add_search_criteria = addSearchCriteria;

	function activateTabs() {
		$('.dynamic-input-tabs').tabs();
	}
	activateTabs();

	$('.criteria-collapse-control').on('click', function () {
		var advSection = $('more-filters-wrapper');
		$(advSection).toggle();
		// $('.advanced-button-wrapper').toggleClass('active');
	});

	// Close all open dropdowns if the user clicks somewhere outside of the dropdown.
	document.body.addEventListener('click', function(event) {
		var isInsideDropdown = false;
		var element = event.target;
		while (element) {
			if (element.className.split(' ').includes('dynamic-button')) {
				isInsideDropdown = true;
				break;
			}
			element = element.parentElement;
		}
		if (!isInsideDropdown) {
			$('.lookups-body').slideUp('fast');
		}
	});
});
