@charset "UTF-8";
/* CSS Document */

/* MLS Specific Rules and MLS Class Options */

/* Don't show these entire sections */
.content-area.details.land .pdp-heading.property-features,
.content-area.details.land .pdp-content.property-features,
.content-area.details.land .pdp-heading.rooms-information,
.content-area.details.land .pdp-content.rooms-information {
	display: none;
}

/* Status Rules */
.Active .close-date,
.ACTIVE .close-date,
.Pending .close-date,
.CAPA .close-date,
.Contingent .close-date,
.Kick .close-date,
.REO .close-date,
.Court .close-date,
.RELO .close-date,
.Short .close-date,
.Contingent .close-date,
.Closed .list-date,
.Sold .list-date,
/* Residential Rules */
.res .lot-size-dimensions,
/* PDP Land Rules */
.land .room-information,
.land .int-sqft,
.land .beds,
.land .baths,
.land .garage-spaces,
.land .year-built,
.land .builder-name,
.land .interior-features,
.land .exterior-features,
.land .kitchen-features,
.land .dining-area,
.land .master-bath-features,
.land .flooring,
.land .laundry,
.land .technology,
.land .fireplace-features,
.land .cooling,
.land .heating,
.land .pool,
.land .spa,
.land .construction-finish,
.land .roofing,
.land .landscaping,
.land .utilities,
.land .bath-features,
.land .appliances,
.land .architectural-style,
.land .levels,
.land .construction-materials,
.land .roof,
.land .other-rooms,
.land .accessibility-features,
.land .basement-type,
.land .structural-style,
.land .community-features,
.land .garage-attached,
.land .carport-attached,
.land .parking-features,
/* Rental PDP Rules */
.rentals .room-information,
.rentals .taxes,
.rentals .tax-year,
.rentals .master-bath-features,
.rentals .hoa-transfer-fee,
.rentals .hoa-paid-freq,
.rentals .lot-size-dimensions,
.rentals .utilities {
	display: none !important;
}
/* MLS Specific Rules and MLS Class Options */

.pdp-before-slider, 
.pdp-after-slider, 
.pdp-after-details,
.details-slider,
.section-1 {
    margin: 40px 0 0;
}

.pdp-before-slider {
	margin-top: 0;
}

.pdp-after-details {
	margin-bottom: 0;
}

.ifound-details h2 {
	border-bottom: 1px solid #ccc;
}

.ifound-details .featured-listing h2{
	border-bottom: none;
}

.ifound-details .pdp-content {
	padding: 10px 0 20px;
}

.ifound-details .pdp-content .pdp-data-wrapper {
	display: table-row;
}

.ifound-details .pdp-content .pdp-label,
.ifound-details .pdp-content .pdp-data,
.main-information .pdp-main-label,
.main-information .pdp-main-data {
	display: table-cell;
}

.ifound-details .pdp-content .pdp-label,
.main-information .pdp-main-label {
	font-weight: bold;
	padding-right: 5px;
}

.ifound-details .ifound-open-houses-wrapper .ifound-open-house {
	display: flex;
	align-items: center;
}

.ifound-details .ifound-open-houses-wrapper .ifound-open-house .ifound-open-house-range {
	margin-left: 10px;
}

/* Nearby Places */
.ifound-nearby-places {
	width: 100%;
	margin: 40px auto;
}

.nearby-places-map-wrap {
	width: 100%;
}

.nearby-places-choices-wrap {
	width: 100%;
}

.nearby-places-map{
	width: 100%;
	height: 500px;
}

.nearby-places-section {
    float: left;
    font-size: 14px;
    padding: 1px;
    width: 128px;
}

.nearby-places-section .ui-button {
	width: 100%;
}



/* What's My Payment */
.whats-my-payment-body {
	margin-bottom: 10px;
}

.whats-my-payment-form,
.whats-my-payment-prequal,
.whats-my-payment-make-offer,
.whats-my-payment-schedule {
	display: none;
	background: #fff;
	border: 2px solid #ccc;
	box-shadow: 0 .188em .625em #494949;
	position: relative;
	top: 300px;
	width: 700px;
	max-width: 90%;
	margin: 0 auto;
	padding: 20px;
	z-index: 999;
}

.whats-my-payment-price-wrapper {
	float:left;
	text-align: left;
}

.whats-my-payment-payment-wrapper {
	text-align: right;
}

.whats-my-payment-more {
	cursor: pointer;
}

.whats-my-payment-button {
	text-align: center;
	margin-top: 10px;
}

.whats-my-payment-button a {
	display: block;
}

.whats-my-payment-button .button,
.whats-my-payment-form-button .button {
	text-align: center;
	width: 100%;
}

.whats-my-payment-close {
	float: right;
	cursor: pointer;
	color:#ccc;
}

.whats-my-payment-price,
.whats-my-payment-payment {
	font-size: 1.4em;
}

.make-offer-backdrop,
.prequal-backdrop,
.more-backdrop,
.schedule-backdrop {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 99;
}

.whats-my-payment-form .one-half {
	line-height: 1;
	margin-bottom: 10px;
}

.whats-my-payment-form .disc {
	font-weight: 700;
}

.prequal:before,
.make-offer:before,
.schedule-showing:before {
	content: "\f336";
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    padding-right: 8px;
}

.make-offer:before {
	content: "\f31c";
}

.schedule-showing:before {
	content: "\f073";
}

.whats-my-payment-compare a,
.whats-my-payment-more {
	background-color: #fcfcfc;
    border: 1px solid #e6e6e6;
	color: #000;   
	display: inline-block;
	line-height: 1.5;
    padding: 0 10px;
}

.whats-my-payment-compare a:hover,
.whats-my-payment-more:hover {
	background-color: #efefef;
	border-color: #ccc;
	color: #000;
}

/* Top Section */

.top-pdp-data {
	margin-bottom: 20px;
}

.top-pdp-data .pdp-data-wrapper,
.top-pdp-data .pdp-data-wrapper .pdp-data {
	display: inline-block;
}

.top-pdp-data .pdp-data-wrapper {
	border-right: 1px solid #444;
	padding: 0 15px;
}

.top-pdp-data .pdp-data-wrapper.int-sqft {
	border-right: none;
}

.top-pdp-data .pdp-data-wrapper.beds {
	padding-left: 0;
}

.top-pdp-data .pdp-data-wrapper .pdp-label {
	display: none;
}

.top-pdp-data .pdp-data-wrapper .pdp-data.beds:before,
.top-pdp-data .pdp-data-wrapper .pdp-data.baths:before,
.top-pdp-data .pdp-data-wrapper .pdp-data.int-sqft:before {
   content: "\f236";
   display: inline-block;
   font-family: 'Font Awesome 5 Pro';
   padding-right: 8px;
}

.top-pdp-data .pdp-data-wrapper .pdp-data.baths:before {
   content: "\f2cd";
}

.top-pdp-data .pdp-data-wrapper .pdp-data.int-sqft:before {
   content: "\f047";
}

@media all and (max-width: 860px) {
	
	.whats-my-payment-payment-wrapper {
		text-align: left;
	}
	
}

@media all and (max-width: 480px) {

	.ifound-details .pdp-content .pdp-label,
	.ifound-details .pdp-content .pdp-data {
		display: block;
	}
	
	.nearby-places-section {
		width: 50%;
	}
	  
}