@charset "UTF-8";
/* CSS Document */

/* Campaign Builder */

.save-alert-form.hide-choose-mls-ids .doing-campaign-wrapper{
	display: none;
}

.doing-campaign-button:after{
	font-family: 'Font Awesome 5 Pro';
	content: "\f217";
	padding: 0 4px 0 0;
}

.active.doing-campaign-button:after{
	font-family: 'Font Awesome 5 Pro';
	content: "\f1f8";
}

#campaign-builder {
	font-size: 14px;
}

.wp-admin .space-data{
	text-align: center;
	padding: 0 20px;
}

.wp-admin .space-data-right{
	padding: 0 10px 0 0;
}

.wp-admin .admin-scroll{
	height: 300px !important;
	overflow: scroll;
}

.red .remove-alert,
.remove-this{
	cursor: pointer;
}

.hide-results-wrapper {
	display:none;
}

/* Map Effects */

.results-section .ifound-results.zoom-marker {
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 0 10px rgba(0,0,0,.5);
	display: block;
	margin: 0 auto;
	max-width: 300px;
	padding: 4px;
	position: fixed;
	top: 20%;
	width: 300px;
	z-index: 9999;
}

.zoom-marker-close {
	display: none;
	position: relative;
	cursor: pointer;
	text-align: right;
}

.zoom-marker .zoom-marker-close {
	display: block;
}

.dynamic-input-array.active:before {
	font-family: 'Font Awesome 5 Pro';
    content: "\f00c";
    color: #000;
    padding: 0 4px 0 0;
}

.dynamic-input-validate.active {
	background-color: #0070c9;
    background: -webkit-linear-gradient(#42a1ec, #0070c9);
    background: linear-gradient(#42a1ec, #0070c9);
    border-color: #07c;
    border-radius: 3px;
    color: #fff;
    display: inline-block;
    margin-top: 3px;
    padding: 3px 6px;
}

.ifound-dynamic-form-wrapper .dynamic-input {
	background: #6EA804;
    color: #fff;
}

.success-msg,
.failed-msg{
	display: none;
	font-size: 30px;
	padding: 10px;
}

.success-msg{
	border: thin solid #8acc21;
	color: #8acc21;
	padding: 10px;
}

.failed-msg{
	border: thin solid #cc261c;
	color: #cc261c;
}

.empty-input{
	border: red thin solid !important;
	background: #fbbfbf !important;
}

.advanced-button-wrapper .modify-button{
	display: none;
}

.map-and-palette-wrapper {
	display: none;
}

.map-and-palette-wrapper .legend {
	margin-top: 1em;
	display: flex;
	float: right;
	clear: right;
}

.map-and-palette-wrapper .legend .item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: 1em;
}

#map-button {
	cursor: pointer;
}

.display-stats-criteria-heading {
	cursor: pointer;
}
.display-stats {
	display: none;
}

.sms_template_body_wrapper {
	margin-top: 10px;
}
