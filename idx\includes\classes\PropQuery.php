<?php

require_once '../vendor/autoload.php';

# FIXME: extending from the IDX class is a hack to get to the config & $mls properties - don't need the rest
/**
 * Include the file with the base class providing DB access and configuration file handling
 */

require_once 'IDX.php';

use Profound\Utils;
use \Suin\RSSWriter\Feed;
use \Suin\RSSWriter\Channel;
use \Suin\RSSWriter\Item;

/**
 * Class containing methods used to generate SQL for querying the DB for properties
 *
 * These are mainly used to create the search options allowed in the advanced searches.
 * Additionally, the keywords are compiled into a sql string.
 */
class PropQuery extends IDX
{
	/**
	 * Initialize the arrays for the different fields with values and SQL fragments
	 */
	public function initFields()
	{
		$map = $this->getFieldMapping();

		# TODO: wrap these up in to functions & remove redundancy
		//value => array('label', 'sql')

		$this->sqft = array();
		$this->pricesqft = array();

		if (isset($map->SquareFeet)) {
			$this->sqft = array(
				'sqft01' => array('Less Than 1,000', " AND {$map->SquareFeet} < '1000.00'"),
				'sqft02' => array('1,000 - 1,200', " AND {$map->SquareFeet} >= '1000.00' AND {$map->SquareFeet} <= '1200.00'"),
				'sqft03' => array('1,201 - 1,400', " AND {$map->SquareFeet} >= '1201.00' AND {$map->SquareFeet} <= '1400.00'"),
				'sqft04' => array('1,401 - 1,600', " AND {$map->SquareFeet} >= '1401.00' AND {$map->SquareFeet} <= '1600.00'"),
				'sqft05' => array('1,601 - 1,800', " AND {$map->SquareFeet} >= '1601.00' AND {$map->SquareFeet} <= '1800.00'"),
				'sqft06' => array('1,801 - 2,000', " AND {$map->SquareFeet} >= '1801.00' AND {$map->SquareFeet} <= '2000.00'"),
				'sqft07' => array('2,001 - 2,250', " AND {$map->SquareFeet} >= '2001.00' AND {$map->SquareFeet} <= '2250.00'"),
				'sqft08' => array('2,251 - 2,500', " AND {$map->SquareFeet} >= '2251.00' AND {$map->SquareFeet} <= '2500.00'"),
				'sqft09' => array('2,501 - 2,750', " AND {$map->SquareFeet} >= '2501.00' AND {$map->SquareFeet} <= '2750.00'"),
				'sqft10' => array('2,751 - 3,000', " AND {$map->SquareFeet} >= '2751.00' AND {$map->SquareFeet} <= '3000.00'"),
				'sqft11' => array('3,001 - 3,500', " AND {$map->SquareFeet} >= '3001.00' AND {$map->SquareFeet} <= '3500.00'"),
				'sqft12' => array('3,501 - 4,000', " AND {$map->SquareFeet} >= '3501.00' AND {$map->SquareFeet} <= '4000.00'"),
				'sqft13' => array('4,001 - 4,500', " AND {$map->SquareFeet} >= '4001.00' AND {$map->SquareFeet} <= '4500.00'"),
				'sqft14' => array('4,501 - 5,000', " AND {$map->SquareFeet} >= '4501.00' AND {$map->SquareFeet} <= '5000.00'"),
				'sqft99' => array('5,001+', " AND {$map->SquareFeet} > '5000.00'"),
			);

			$ratio = "{$map->ListPrice} / {$map->SquareFeet}";
			$this->pricesqft = array(
				'psqft01' => array('Less than $10 sqft', " AND (( $ratio ) < 10)"),
				'psqft02' => array('$10 - $30 sqft', " AND ( ( ( $ratio ) > 10) AND ( ( $ratio ) < 30) )"),
				'psqft03' => array('$30 - $50 sqft', " AND ( ( ( $ratio ) > 30) AND ( ( $ratio ) < 50) )"),
				'psqft04' => array('$50 - $70 sqft', " AND ( ( ( $ratio ) > 50) AND ( ( $ratio ) < 70) )"),
				'psqft05' => array('$70 - $90 sqft', " AND ( ( ( $ratio ) > 60) AND ( ( $ratio ) < 90) )"),
				'psqft06' => array('$90 - $110 sqft', " AND ( ( ( $ratio ) > 70) AND ( ( $ratio ) < 110) )"),
				'psqft07' => array('$110 - $130 sqft', " AND ( ( ( $ratio ) > 110) AND ( ( $ratio ) < 130) )"),
				'psqft08' => array('More than $130 sqft', " AND (( $ratio ) > 130)"),
			);
		}

		$this->bathroom = array();
		if (isset($map->Bathrooms)) {
			$this->bathroom = array(
				'bath01' => array('1 bath or more', " AND {$map->Bathrooms} >= '1.00'"),
				'bath02' => array('2 baths or more', " AND {$map->Bathrooms} >= '2.00'"),
				'bath03' => array('3 baths or more', " AND {$map->Bathrooms} >= '3.00'"),
				'bath04' => array('4 baths or more', " AND {$map->Bathrooms} >= '4.00'"),
				'bath05' => array('More than 5 baths', " AND {$map->Bathrooms} >= '5.00'"),
			);
		}

		$this->_initPrice();
		$this->_initBeds();
		$this->_initGarage();
		$this->_initYearBuilt();
	}

	/**
	 * Return the array or array element for a given form element
	 *
	 * If a key is passed to the method, then return the value in the array for that key.  Else, return the entire array.
	 * @param $name Method name with a 'get_' prefix
	 * @param $args Method arguments
	 * @return mixed
	 */
	public function __call($name, $args) {
		$varname = str_replace('get_', '', $name);
		// Case 557: Only init cities when necessary.
		if ($varname == 'city' && (!isset($this->city) || $this->city == null)) {
			$this->_initCities();
		}
		$data = $this->$varname;
		return isset($args[1]) && array_key_exists($args[1], $data) ? $data[$args[1]] : $data;
	}

	protected function _initPrice() {
		$map = $this->getFieldMapping();
		// Keep the old prices until all sites have been updated.
		$old_prices = array(
			'price01' => array('Less than $100k', " AND {$map->ListPrice} < '100000.00'"),
			'price02' => array('$100k - $159k', " AND {$map->ListPrice} >= '100000.00' AND {$map->ListPrice} < '160000.00'"),
			'price03' => array('$160k - $189k', " AND {$map->ListPrice} >= '160000.00' AND {$map->ListPrice} < '190000.00'"),
			'price04' => array('$190k - $219k', " AND {$map->ListPrice} >= '190000.00' AND {$map->ListPrice} < '220000.00'"),
			'price05' => array('$220k - $299k', " AND {$map->ListPrice} >= '220000.00' AND {$map->ListPrice} < '300000.00'"),
			'price06' => array('$300k - $399k', " AND {$map->ListPrice} >= '300000.00' AND {$map->ListPrice} < '400000.00'"),
			'price07' => array('$400k - $499k', " AND {$map->ListPrice} >= '400000.00' AND {$map->ListPrice} < '500000.00'"),
			'price08' => array('$500k - $599k', " AND {$map->ListPrice} >= '500000.00' AND {$map->ListPrice} < '600000.00'"),
			'price09' => array('$600k - $799k', " AND {$map->ListPrice} >= '600000.00' AND {$map->ListPrice} < '800000.00'"),
			'price10' => array('Greater than $800k', " AND {$map->ListPrice} >= '800000.00'"),
		);

		$pricePoints = array(
			0 => 100000,
			100000 => 20000,
			500000 => 50000,
			1000000 => 500000,
			2000000 => PHP_INT_MAX,
		);

		$lastElement = end($pricePoints);
		$lastPricePoint = key($pricePoints);
		reset($pricePoints);

		// I'm starting the labeling at 101 because it previously was 1-10, so
		// this should prevent any possible conflicts.
		$labelNumberStart = 101;
		$prices = array();
		$curPrice = 0;
		$curLabelNumber = $labelNumberStart;
		$jump = $pricePoints[$curPrice];
		while ($curPrice <= $lastPricePoint) {
			$nextPricePoint = $curPrice + $jump;
			$curPriceLabel = '$' . number_format($curPrice, 0);
			$nextPricePointLabel = '$' . number_format($nextPricePoint, 0);
			if ($curPrice == $lastPricePoint) {
				$prices["price$curLabelNumber"] = array("$curPriceLabel and up", " AND {$map->ListPrice} >= $curPrice");
			} else {
				$prices["price$curLabelNumber"] = array("$curPriceLabel - $nextPricePointLabel", " AND {$map->ListPrice} >= $curPrice AND {$map->ListPrice} < $nextPricePoint");
			}
			$curPrice = $nextPricePoint;
			$curLabelNumber++;
			if (isset($pricePoints[$curPrice])) {
				$jump = $pricePoints[$curPrice];
			}
		}

		$this->price = array_merge($old_prices, $prices);
	}

	/**
	 * Cities are returned as an array of options
	 */
	private function _initCities() {
		# TEMP hack
		$this->city = ['Phoenix'];
		return;

		$map = $this->getFieldMapping();

		# FIXME: use the Lookup table below instead of querying the property table
		$sql = "SELECT DISTINCT {$map->City} FROM {$this->mls->db_table} WHERE 1 ";
		$this->getMls()->getPropertyLimiter()->limitCities($sql);
		$sql .= " ORDER BY {$map->City}";

		$city_list = $this->db->fetchCol($sql);

		$city_array = array();
		foreach ($city_list as $result) {
			$build_key = strtolower(preg_replace("/[^A-Za-z0-9_-]/", "_", $result));
			$city_array[$build_key] = array(ucwords(strtolower($result)), " AND {$map->City} = '$result'");
		}
		$this->city = $city_array;
	}


	/**
	 * Beds are returned as an array of options
	 */
	private function _initBeds() {
		$map = $this->getFieldMapping();

		$this->bedroom = array();
		if (isset($map->Beds)) {
			for ($i = 1; $i <= 6; $i++) {
				$bed_array['bed0' . $i] = array($i . ' bedrooms or more', " AND {$map->Beds} >= '$i'");
			}
			$bed_array['bed07'] = array('More than 6', " AND {$map->Beds} > '6'");
			$this->bedroom = $bed_array;
		}
	}

	/**
	 * Year Built are returned as an array of options
	 */
	private function _initYearBuilt() {
		$map = $this->getFieldMapping();

		$this->yrbuilt = array();
		if (isset($map->YearBuilt)) {
			$year = date("Y");
			$year_array = array(
				'yrbuilt01' => array('5 years or newer', " AND `{$map->YearBuilt}` > '" . ($year - 5) . "'"),
				'yrbuilt02' => array('5 - 10 years', " AND `{$map->YearBuilt}` > '" . ($year - 10) . "' AND `{$map->YearBuilt}` < '" . ($year - 5) . "'"),
				'yrbuilt03' => array('10 - 20 years', " AND `{$map->YearBuilt}` > '" . ($year - 20) . "' AND `{$map->YearBuilt}` < '" . ($year - 10) . "'"),
				'yrbuilt04' => array('20 - 30 years', " AND `{$map->YearBuilt}` > '" . ($year - 30) . "' AND `{$map->YearBuilt}` < '" . ($year - 20) . "'"),
				'yrbuilt05' => array('30 - 40 years', " AND `{$map->YearBuilt}` > '" . ($year - 40) . "' AND `{$map->YearBuilt}` < '" . ($year - 30) . "'"),
				'yrbuilt06' => array('40 - 50 years', " AND `{$map->YearBuilt}` > '" . ($year - 50) . "' AND `{$map->YearBuilt}` < '" . ($year - 40) . "'"),
				'yrbuilt10' => array('Greater than 50 years', " AND `{$map->YearBuilt}` <= '" . ($year - 50) . "'"),
			);
			$this->yrbuilt = $year_array;
		}
	}

	/**
	 * Garage Spaces are returned as an array of options
	 */
	private function _initGarage() {
		$map = $this->getFieldMapping();

		$this->garage = array();
		if (isset($map->GarageSpaces)) {
			$garage_array['garage01'] = array('1 space', " AND {$map->GarageSpaces} = 1.0");
			for ($i = 2; $i <= 6; $i++) {
				$garage_array['garage0' . $i] = array($i . ' spaces', " AND {$map->GarageSpaces} = $i.0");
			}
			$garage_array['garage99'] = array('Greater than 6', " AND {$map->GarageSpaces} > 6.0");
			$this->garage = $garage_array;
		}
	}

	/**
	 * Get the list of features by name, or the SQL query for a specific feature
	 *
	 * Read the list of features that can be searched from the config file.
	 *
	 * If a key is specified, generate the SQL for searching that feature, by getting the list of database fields
	 * to search and the column values that should match.
	 *
	 * @param string $key Feature name
	 * @return array|string
	 */
	public function get_features($key = '') {
		$search = parse_ini_file(dirname(__FILE__) . '/../../search.ini', true);

		// The INI file doesn't allow nested sections, so I changed things to require
		// each section be prefixed with the MLS system name, to know which it applies to.
		$sections = array();
		foreach ($search as $heading => $value) {
			$pos = strpos($heading, ":");
			$heading_mlsname = substr($heading, 0, $pos);
			if ($heading_mlsname == $this->mlsname) {
				$heading_cleaned = substr($heading, $pos + 2, 999);
				$sections[$heading_cleaned] = $value;
			}
		}
		$search = $sections;

		if ($key) {
			$fstr = array();
			foreach ($search[$key] as $field => $vals) {
				# TODO: Should use the Zend_Db API to construct the SQL statement
				$vals = implode('|', preg_split('/\s*,\s*/', trim($vals)));
				$fstr[] = "$field REGEXP '$vals'";
			}
			$sql_str = " AND (" . implode(' OR ', $fstr) . ")";
			return $sql_str;
		} else {
			$keys = array_keys($search);
			sort($keys);
			return $keys;
		}
	}

	/**
	 * Get the SQL string that used to search for keywords in the fields
	 *
	 * Get Words creates an " AND (something OR something) "
	 * sql string from a comma separated keyword terms
	 * to add to the sql statement.
	 * @param $strg
	 * @param bool $and
	 * @return string
	 */
	public function get_words($strg, $and = true)
	{
		$sql_strg = "";
		$fieldMappings = $this->getFieldMappings($this->mls->mls_class);

		// If the category string is using the advance search syntax, split that up
		if (preg_match_all('/(\w+)\s*(:|=|<=|<|>=|>|IN|LIKE)\s*"?([^;"]+)"?/', $strg, $matches)) {
			for ($i = 0; $i < count($matches[0]); $i++) {
				$field = $matches[1][$i];
				// Get the operator used for comparison:  <, >, =, :
				$op = $matches[2][$i];
				$value = $matches[3][$i];

				// If we find a quick search value, we heed it and ignore all else.
				if ($field == 'quick_search') {
					if ($value == 'all') {
						return '';
					} else if ($value == 'zero') {
						// Return some query that will return zero results,
						// and also give an indication of what's going on to
						// whoever debugs the query string.
						return "AND 'quick_search' = 'zero_listings'";
					}
				}

				$fieldMapping = $fieldMappings->getByEasyName($field);
				if ($fieldMapping) {
					$field = $fieldMapping->mlsFieldName;
				}

				// Search all of the specified fields
				if (strtolower($field) == 'any') $field = null;

				# FIXME: should be using PDO or Zend_Db_Adapter quoting stuff here.  SQL injection vulnerability.
				if ($op == ':') {
					// Search within fields
					$sql_strg .= $this->_getSQLfragment($value, $field, $and);
				} elseif ($op == 'IN') {
					$values = explode(',', $value);
					$values_string_array = array();
					foreach ($values as $val) {
						$values_string_array[] = $this->sql_quote($field, $val);
					}
					$values_string = implode(",", $values_string_array);
					$sql_strg .= " AND $field IN ($values_string)";
				} elseif ($op == 'LIKE') {
					$values = explode(',', $value);
					$values_string_array = array();
					foreach ($values as $val) {
						// Reminder: I'm always quoting here, as it wouldn't
						// make sense to use LIKE except in a text scenario.
						$quoted_percent_val = $this->db->quote("%$val%");
						$values_string_array[] = "$field LIKE $quoted_percent_val";
					}
					$values_string = implode(" OR ", $values_string_array);
					$sql_strg .= " AND ($values_string)";
				} else {
					// Exact matches & comparison (less than/greater than)
					$sql_strg .= ' AND ' . $field . ' ' . $op;
					$sql_strg .= $this->sql_quote($field, $value);
				}
			}
		}
		// Simple search syntax
		else {
			// The way it's worked in the past is a blank query means zero
			// results, so we'll continue that tradition, since that what
			// users of this method expect. But we want to return a query
			// that will return zero results, and also give an indication of
			// what's going on to whoever debugs the query string.
			if ($strg == "") {
				$sql_strg = "AND 'this was' = 'a blank search'";
			} else {
				$sql_strg = $this->_getSQLfragment($strg, null, $and);
			}
		}
		return $sql_strg;
	}

	// Reminder: This is not ideal and still vulnerable to SQL injection.
	private function sql_quote($field, $val) {
		// Fields might be searched on that we don't have mapped. So we first
		// check if we even have such a mapping.
		if ($this->getFieldMappings()->getByMlsFieldName($field)) {
			return $this->getFieldMappings()->getByMlsFieldName($field)->quoteValue($val);
		}
		if (is_numeric($val)) {
			return $val;
		}
		return $this->addQuotes($val);
	}

	protected function addQuotes($val) {
		return "'$val'";
	}

	private function _getSQLfragment($strg, $field = null, $and = true) {

		$map = $this->getFieldMapping();

		// Fields to search for words
		$flist = array(
			'PropDescription',
			'Directions',
			'CrossStreet',
			'Subdivision',
			'ElementarySchool',
			'MiddleSchool',
			'HighSchool',
			'PoolType',
			'RoomList',
			'AdditionalBedroom',
			'Architecture',
			'HOAFeatures',
			'CommunityFeatures',
			'ExteriorFeatures',
			'InteriorFeatures',
			'Remarks',
			'SpecialCondition',
			'Technology'
		);

		$field_array = array();
		foreach ($flist as $mapname) {
			if (isset($map->$mapname)) $field_array[] = $map->$mapname;
		}

		// Only check one field
		if ($field) $field_array = array($field);

		// remove white space, lowercase, and then explode by commas
		$words_array = preg_split('/\s*,\s*/', strtolower($strg));
		$sql_strg = "";

		if (is_array($words_array)) {
			$tests = array();
			foreach ($words_array as $word) {
				$word_tests = array();
				foreach ($field_array as $field) {
					$word_no_minus = preg_replace('/^-/', '', $word);
					$word_tests[] = "(`$field` REGEXP '[[:<:]]" . $word_no_minus . "[[:>:]]')";
				}

				// Check all of the fields for the word
				$new = '(' . implode(' OR ', $word_tests) . ')';

				// Either include or exclude the keyword/phrase, depending on if it was marked with a '-'
				if (preg_match('/^-/', $word)) {
					$exclude[] = $new;
				} else {
					$tests[] = $new;
				}
			}
			$and_or = $and ? 'AND' : 'OR';

			// Check for any/all of the keywords
			if (count($tests)) {
				$sql_strg = " AND (" . implode(" $and_or ", $tests) . ")";
			}

			// If we found "negative" keywords or phrases, exclude them
			if (!empty($exclude)) {
				$sql_strg .= " AND NOT (" . implode(" OR ", $exclude) . ")";
			}
		}
		return $sql_strg;
	}

	// We started using versions of our API to allow clients to have different
	// versions of code than our server, which would allow to not have to immediately
	// update all clients.
	// Version history (I'm not sure of a better place to keep this).
	// 1.00: This is what we're calling anything prior to 1.01. It really means "no version",
	// but is distinctly different than meaning "the latest version".
	// 1.01: We return real JSON for 0 results, rather than a string of "No results".
	// 1.02: We use a proper array structure for results, rather than a weird hybrid
	//   (results were in the form: 0 => some_data, 1 => more_data).
	public function searchProps() {
		require_once("config.php");
		require_once("class-query_db.php");
		require_once("class-parse_seo.php");
		require_once("class-sanitizer.php");
		require_once("class-paging.php");

		$this->query_type = 'Search';

		// Fill in some defaults, to avoid PHP E_NOTICE errors
		// TODO: Add pfmls_cat here? Or does that change functionality?
		$keys = array('pp', 'p_url');
		foreach ($keys as $key) {
			if (!isset($_REQUEST[$key])) $_REQUEST[$key] = null;
		}

		//Create an array of the form information
		while (list($key,$val)=each($_REQUEST)){ // HTTP_GET_VARS
			if(isset($query_info[$key])){
				$query_info[$key] = NULL;
			}
			$query_info[$key] = $val;
		}

		# FIXME: shouldn't be skipping over fields here.  Or rather, move the sanitizing logic somewhere more intelligent.
		// Process The Form Cleaner (you can't trust anyone these days)
		$sanitizer_obj = new sanitizer;
		$sanitizer_obj->price_strg = array('active'=>false); // allow the price variable
		$sanitizer_obj->exclude = array('features', 'p_url', 'words', 'extended_data');
		$query_info = $sanitizer_obj->cleandata( $query_info ); //1.7
		//print "<pre>"; print_r($query_info); print "</pre>";


		$db_obj = new sql_query();
		$seo_obj = new SEO_Parse();

		$this->authenticate();

		$this->initFields();

		$paging_obj = new paging();

		$map = $this->getFieldMapping();

		// Do Something that allows a query
		$search_sql = '';
		$seometa_array = array();
		// Functions and rules go here and build and assemble the query

		$features = @array(
			'price' => array($map->ListPrice, 'Priced %s'),
			'sqft' => array($map->SquareFeet, '%s Sq Foot'),
			'bedroom' => array($map->Beds, '%s'),
			'bathroom' => array($map->Bathrooms, '%s'),
			'city' => array($map->City, 'in %s'),
			'yrbuilt' => array($map->YearBuilt, 'Build %s Ago'),
			'garage' => array($map->GarageSpaces, 'Garage %s')
		);
		// Not all features always exist. Remove those that don't.
		foreach ($features as $key => $value) {
			if ($value[0] == null) {
				unset($features[$key]);
			}
		}

		// Property Types
		if (isset($query_info['proptype'])) {
			$search_sql .= " AND {$map->DwellingType} = '".$query_info['proptype']."'";
			$seometa_array[$map->DwellingType] = $query_info['proptype'];
		}

		foreach ($features as $key => $data) {
			if (isset($query_info[$key])) {
				list($listid, $formatstr) = $data;
				$method = "get_$key";
				$opt = call_user_func(array($this, $method), $listid, $query_info[$key]);

				# FIXME: had to add this hack do to E_NOTICE error - but not sure that this is correct.
				if (!isset($opt[0])) continue;

				$search_sql .=  $opt[1];

				// Don't add city if this was a Category/Neighborhood search
				if (empty($query_info['pfmls_cat']) || $key != 'city') {
					$seometa_array[$listid] = ($query_info[$key] != "${key}00") ? sprintf($formatstr, $opt[0]) : '';
				}
			}
		}

		if (isset($query_info['zipcode']) && trim($query_info['zipcode'])) {
			$search_sql .= " AND {$map->PostalCode} = '".$query_info['zipcode']."'";
			$seometa_array[$map->PostalCode] = 'In Zipcode '.$query_info['zipcode'];
		}

		// Features (used as keywords)
		if (isset($query_info['features'])) {
			$query_info['features'] = unserialize( urldecode($query_info['features']) );
			if(is_array($query_info['features'])){
				foreach($query_info['features'] as $value){
					$search_sql .= $this->get_features($value);

					# TODO: kind of hacky. this whole SEO mapping thing probably needs redone.
					$seometa_array[$value] = $value;
				}
			}
		}

		$lat = null;
		$lon = null;
		if (isset($query_info['latlon']) && isset($query_info['nearby_radius'])) {
			$latlonString = $query_info['latlon'];
			list($lat, $lon) = explode(',', $latlonString);
			$radius = $query_info['nearby_radius'];
			$search_sql .= " AND haversine({$map->Latitude}, {$map->Longitude}, $lat, $lon, 'mi') < $radius";
		}

		// Create the generic keyword search
		if (isset($query_info['words'])) {
			// Require all of the keywords if advanced search; else match at least one keyword
			$and = isset($query_info['pfmls_cat']) && $query_info['pfmls_cat'] == '' ? true : false;

			$search_sql .= $this->get_words($query_info['words'], $and);
			$seometa_array['cat_name'] = isset($query_info['words']) && (isset($query_info['pfmls_cat']) && $query_info['pfmls_cat'] != '') ? $query_info['cat_name'] : '';
		}

		// Price per Sqft
		if (isset($query_info['pricesqft'])) {
			$pricesqft_opt = $this->get_pricesqft($query_info['pricesqft']);
			$search_sql .=  $pricesqft_opt[1];
			$seometa_array['pricesqft_opt'] = ($query_info['pricesqft'] != 'psqft00')? $pricesqft_opt[0].' Per Sq Foot' : '';
		}

		// MLS Number
		if (isset($query_info['mlsnum']) && $query_info['mlsnum']) {
			$search_sql .= $this->db->quoteInto(" AND {$map->ListingID} = ? ", $query_info['mlsnum'], 'INTEGER');
			# FIXME: this is an ugly hack to fix the title & H1 for this kind of search - the whole SEO thing for searches needs redone
			$this->access_array['meta_result_title'] = 'Homes for Sale ';
			$seometa_array = array('cat_name' => 'MLS # ' . $query_info['mlsnum']);
		}

		// Remove AddressNoExport and UCB properties if applicable
		// This is also where we do additional processing like polygon stuff.
		$this->map = $map;
		$extendedData = @$query_info['extended_data'];
		$this->limitProperties(
			$search_sql,
			array(
				'db_table' => $this->mls->db_table,
				'field_mappings' => array(
					'lat' => $map->Latitude,
					'lon' => $map->Longitude,
					'listing_id' => $map->ListingID
				),
				'extended_data' => $extendedData
			)
		);
		$fields = $this->getFieldList();

		# TODO: rather than doing the SQL query twice, use WITH ROLLUP or something similar?
		// Paging settings
		// Set the default for non-existant var
		$query_info['pp'] = ($query_info['pp'])? $query_info['pp'] : 1;
		$max_results = PERPAGE;
		if (isset($query_info['max_results'])) {
			$max = (int)$query_info['max_results'];
			// Safety check
			if ($max < $max_results) {
				$max_results = $max;
			}
		}
		// The 'x' here is a throwaway value.
		$paging_obj->get_paging( "SELECT 'x' FROM {$this->mls->db_table} WHERE 1 $search_sql", $query_info['pp'], $max_results );

		$paging_obj->set_url_vars($query_info['p_url']);
		$paging['total'] = $paging_obj->total_result_count;
		$paging['parseable'] = $paging_obj->get_parseable();

		# Save the number of results for logging purposes
		$this->num_results = $paging_obj->total_result_count;

		// Create the limits
		$limit_start = (($query_info['pp'] - 1) * $max_results); //((($query_info['p'] * $max_results) -1) < 0)? 0 : (($query_info['p'] * $max_results) -1);
		$sql_limit_strg = ' LIMIT '.$limit_start.', '.$max_results.' ';

		// Sort order
		$sort_mods = array(
			'ListPrice,DESC' => "Price, Highest to Lowest",
			'ListPrice,ASC' => "Price, Lowest to Highest",
			'SquareFeet,DESC' => "Home Size, Largest to Smallest",
			'SquareFeet,ASC' => "Home Size, Smallest to Largest",
			'ModificationTimestamp,DESC' => "Latest Listings",
			'Distance,ASC' => "Distance, nearest to farthest",
		);
		$orderstr = "{$map->ModificationTimestamp} DESC";
		$seometa_array['title'] = $sort_mods['ModificationTimestamp,DESC'];
		if (isset($query_info['sort'])) {
			preg_match('/(\w+),(ASC|DESC)/', $query_info['sort'], $match);
			if ($match[1] == 'Distance') {
				$orderstr = "haversine({$map->Latitude}, {$map->Longitude}, $lat, $lon, 'mi') " . $match[2];
				$seometa_array['title'] = $sort_mods['Distance,ASC'];
			} else {
				$orderstr = $map->$match[1] . " " . $match[2];
				foreach($sort_mods as $key => $value) {
					if ($key == $query_info['sort']) {
						$seometa_array['title'] = $value;
						break;
					}
				}
			}
		}

		// Get the properties, order by listing price in descending order
		$sql = "SELECT $fields FROM {$this->mls->db_table} WHERE 1 $search_sql ORDER BY $orderstr $sql_limit_strg ";
		// \D::elog($sql);
		$prop_array = null;
		try {
			$prop_array = $this->db->fetchAll($sql);
		} catch (\Exception $ex) {
			\D::elog("access_id: " . $this->access_array['access_id']);
			\D::elog("Hostname" . isset($_REQUEST['Hostname']) ? $_REQUEST['Hostname'] : "");
			\D::elog("Request URL: " . isset($_REQUEST['RequestURL']) ? $_REQUEST['RequestURL'] : "");
			\D::elog($sql);
			throw $ex;
		}
		$response = array();

		if ($this->searchAPI->getVersion() != "v1.00" || count($prop_array)) {
			$prop_array = $this->reverseMap($prop_array, true);

			// Fill in any missing latitude & longitude
			$prop_array = $this->maybeGeocode($prop_array);

			foreach($prop_array as $key => $var) {
				$prop_array[$key]['img_url'] = $this->getPreferredImage($var['ListingKey']);
				if (isset($_REQUEST['img_count'])) {
					$img_count = $_REQUEST['img_count'];
					$images = $this->getImagePathsList($var['ListingKey']);
					$prop_array[$key]['img_urls'] = array_slice($images, 0, $img_count);
				}

				// Add the property URI based on mapping sent or by default
				$slug = $seo_obj->parse_urlmap($prop_array[$key], $this->access_array['meta_prop_url']) . '-idx';
				$listing_id = $var['ListingID'];
				$prop_array[$key]['uri_parts'] = array(
					'slug' => $slug,
					'listing_id' => $listing_id,
				);
				$prop_array[$key]['uri'] = "$slug-$listing_id";

				if ($this->mls->mls_class == 'land') {
					$prop_array[$key]['prop_h2'] = $seo_obj->parse_seomap($prop_array[$key], "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix}
{City} {PostalCode}, {Subdivision}");
					$prop_array[$key]['prop_content'] = $seo_obj->parse_seomap($prop_array[$key], '<div class="pfmls_prop_data">
	<ul>
		<li class="list_price">{ListPrice}</li>
		<li class="lot_acreage">Lot acreage: {LotAcreage}</li>
		<li class="MLS #">MLS #: {ListingID}</li>
	</ul>
</div>');
				} else {
					// Add the property layout based on the mapping sent or by default
					$prop_array[$key]['prop_h2'] = $seo_obj->parse_seomap($prop_array[$key], $this->access_array['meta_result_prop_h2']);
					$prop_array[$key]['prop_content'] = $seo_obj->parse_seomap($prop_array[$key], $this->access_array['meta_result_prop_content']);
				}

				$prop_content_modified = $prop_array[$key];
				// Don't need the prop content string inside the JSON data.
				unset($prop_content_modified['prop_content']);
				$prop_array[$key]['prop_content_json'] = json_encode($prop_content_modified);
			}

			if ($this->searchAPI->getVersion() == "v1.02" || isset($_GET['v2'])) {
				$response['listings'] = array();
				foreach ($prop_array as $i => $prop) {
					$response['listings'][] = $prop;
				}
			} else {
				$response = $prop_array;
			}
		} else {
			return 'No results';
		}

		$response['last_updated'] = $this->getLastUpdated();

		$response['meta'] = array('mls_class' => $this->mls->mls_class);

		// Insert the paging info
		$response['paging'] = $paging;

		$title_strg = $h1_strg = $description_strg = $keywords_strg = "";

		// Insert the SEO info
		// This is going to need some work to get the details of the search to swap with the field mapping
		if(is_array($seometa_array)){
			$result = implode(', ', array_values($seometa_array));

			$title_strg .= $result;
			$h1_strg .= $result;
			$description_strg .= $result;
			$keywords_strg .= $result;
		}
		$seo_array['title'] 		= $seo_obj->parse_seomap($seometa_array, $this->access_array['meta_result_title']).' '.$title_strg;
		$seo_array['h1'] 			= $seo_obj->parse_seomap($seometa_array, $this->access_array['meta_result_h1']).' '.$h1_strg;
		$seo_array['description'] 	= $seo_obj->parse_seomap($seometa_array, $this->access_array['meta_result_description']).' '.$description_strg;
		$seo_array['keywords'] 		= $seo_obj->parse_seomap($seometa_array, $this->access_array['meta_result_keywords']).' '.$keywords_strg;

		$response['seo'] = $seo_array;
		$response['domain'] = $this->access_array['domain'];

		$optionsJson = @$_REQUEST['options'];
		// Try to get aggregate data.
		try {
			$options = json_decode($optionsJson, true);
			if (@$options['include_aggregates']) {
				$listPrice = $this->getFieldMappings()->getByMapName('ListPrice');
				$sql = "SELECT count(*), sum({$listPrice->mlsFieldName}) as 'sum({$listPrice->easyName})', avg({$listPrice->mlsFieldName}) as 'avg({$listPrice->easyName})', max({$listPrice->mlsFieldName}) as 'max({$listPrice->easyName})', min({$listPrice->mlsFieldName}) as 'min({$listPrice->easyName})' FROM {$this->mls->db_table} WHERE 1 $search_sql";
				$sums = $this->db->fetchAll($sql);
				$sums = $sums[0];
				$response['meta']['aggregates'] = $sums;
			}
		} catch (\Exception $ex) {
			// Bail silently.
		}

		return $response;
	}

	/**
	 * Get the fields to fetch from the DB for a search
	 *
	 * Extract the fields we need to fetch based on the meta data mappings stored via the IDX Admin. We also force add some important fields like listing ID and key.
	 * @return string Field list to use in SQL query
	 */
	private function getFieldList() {
		$map = $this->getFieldMapping();

		// Include all of the mapped fields if 'all' is set - mostly for custom feeds (case 300)
		if (isset($_REQUEST['all'])) {
			$fields = $this->db->fetchCol("SELECT {$this->mlsname} FROM field_mapping WHERE {$this->mlsname} <> '' and mls_class = '{$this->mls->mls_class}'");
		} else {
			if ($this->mls->mls_class == 'land') {
				$fields = array(
					'StreetNumber',
					'StreetDirPrefix',
					'StreetName',
					'StreetSuffix',
					'City',
					'PostalCode',
					'ListPrice',
					'ListingID',
					'LotAcreage',
					'Subdivision',
				);
			} else {
				$fields = array();
				// Get the fields that were specified in the various SEO mappings inside the IDX Admin
				$keys = array('title', 'h1', 'description', 'keywords', 'prop_h2', 'prop_content');
				foreach ($keys as $key) {
					preg_match_all('/{(.*?)}/', $this->access_array['meta_result_' . $key], $matches);
					$fields = array_unique(array_merge($fields, array_values($matches[1])));
				}
			}
			// Include the fields from the property URL as well
			# FIXME: this is duplicated just above
			preg_match_all('/{(.*?)}/', $this->access_array['meta_prop_url'], $matches);
			$fields = array_unique(array_merge($fields, array_values($matches[1])));
			$fields = $this->mapFieldNames($fields);

			$fields[] = $map->ListingID;
			$fields[] = $map->ListingKey;

			// Add the Latitude & Longitude
			$fields[] = $map->Latitude;
			$fields[] = $map->Longitude;

			// Add the Listing Broker office name
			$fields[] = $map->ListOfficeName;

			$fields[] = $map->ModificationTimestamp;
		}
		return implode(', ', $fields);
	}

	/**
	 * Get the Property Types from the lookup table
	 * @return mixed
	 */
	private function get_proptype() {
		$map = $this->getFieldMapping();
		$mls = $this->access_array['mls'];
		$mlsClassGeneric = $this->mls->mls_class;
		$mlsClassLocal = $this->getLocalMlsClassFromGeneric($mls, $mlsClassGeneric);
		$dwellingType = $map->DwellingType;
		$tbl = "{$mls}_fields_{$mlsClassLocal}";

		# TODO: either send the just the list of values, or lookup the Property Type ID when returned to the server
		$sql = "
			SELECT DISTINCT
				o.ShortValue, o.ShortValue AS Value
			FROM
				$tbl as f JOIN property_lookup_opts as o ON f.LookupName = o.LookupName AND o.mls='$mls'
			WHERE
				f.SystemName = '$dwellingType'
		";
		return $this->db->fetchAll($sql);
	}

	/**
	 * Get the search options and account data that the WP plugin needs
	 * @return array Array with indexes for 'account' and 'search_opts'
	 */
	public function getIDXData() {
		$this->authenticate();
		$this->query_type = 'Options';
		$returnValue = array(
			'account' => array(
				'mls' => $this->access_array['mls'],
				'company' => $this->access_array['access_company'],
				'features' => array(
					'shortcode_gui' => $this->access_array['feature_gui'],
					'crm_liondesk' => $this->access_array['crm_liondesk'],
				),
			),
			'search_opts' => $this->getSearchOptions(),
		);

		return $returnValue;
	}

	/**
	 * Get the Search options for the Quick Search widget and Advanced Search form
	 * @return array Options indexed by field name
	 */
	public function getSearchOptions() {

		$this->initFields();

		return array(
			'proptype' => $this->get_proptype(),
			'price' => $this->get_price(),
			'sqft' => $this->get_sqft(),
			'bedroom' => $this->get_bedroom(),
			'bathroom' => $this->get_bathroom(),
			'city' => $this->get_city(),
			'yrbuilt' => $this->get_yrbuilt(),
			'garage' => $this->get_garage(),
			'features' => $this->get_features(),
			'pricesqft' => $this->get_pricesqft(),
		);
	}

	/**
	 * Generate an RSS feed for a property search
	 */
	public function getRSSFeed() {

		# TODO: This is hacky
		$this->authenticate();
		$_REQUEST['pp'] = 1;
		$_REQUEST['pfmls_cat'] = null;
		$domain = $this->access_array['domain'];

		$url = $_REQUEST['p_url'] = 'http://' . $domain;

		# TODO: get query params from base-64 encrypted query parameter?

		$props = $this->searchProps();

		$this->query_type = 'RSS';

		if (isset($_GET['debug'])) {
			echo "<pre>"; print_r($props); echo "</pre>"; exit;
		}
		$feed = new Feed();

		$city = ucwords($_REQUEST['city']);

		$channel = new Channel();
		$channel
			->title($props['seo']['title'])
			->description($props['seo']['description'])
			->url($url)
			->language('en-US')
			->copyright('Copyright ' . date('Y') . ', Pro-Found Marketing, LLC')
			->pubDate(time())
			->lastBuildDate(time())
			->ttl(60)
			->appendTo($feed);

		foreach ($props as $index => $prop) {
			# TODO: fix the shit format that is used to return the property data
			// Skip non-property junk
			if (!is_int($index)) continue;

			$item = new Item();
			$item
				->title($prop['prop_h2'])
				->description(json_encode($prop))
				->url($url . "/" . $prop['uri'])
				->pubDate(time())
				->guid($url, true)
				->appendTo($channel);
		}
		return $feed;
	}
}
