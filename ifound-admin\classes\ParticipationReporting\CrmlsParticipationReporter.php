<?php

require_once(__DIR__ . '/ParticipationReporter.php');

class CrmlsParticipationReporter extends ParticipationReporter {
	protected static $crmls_report_date_format_string = 'Y-m-d';

	public function submit(): string {
		if ($this->client->get_config()['environment'] !== 'production') {
			$dev_success_markup = <<<CONTENT
		    <div class="notice notice-success">
                The <span style="font-weight: bold;">NON-PRODUCTION</span> report was not really submitted.
            </div>
CONTENT;
			return $dev_success_markup;
		}
		$error_markup = <<<CONTENT
		    <div class="notice notice-error">
                An error occurred. See the history.
            </div>
CONTENT;

		$report_data = $this->generate_participation_report();
		$url = 'https://api-trestle.corelogic.com/trestle/report/TpParticipantReport';
		$response = $this->get_access_token();
		if (is_wp_error($response)) {
			$this->capture_report_result($response, true);
			return $error_markup;
		}
		$access_token = $response;
		$args = [
			'body'     => json_encode($report_data),
			'blocking' => true,
			'headers'  => [
				'Authorization' => 'Bearer ' . $access_token,
				'Content-Type'  => 'application/json',
			],
		];
		$response = wp_remote_post($url, $args);
		if (is_wp_error($response)) {
			return $error_markup;
		} else {
			$http_status_code = wp_remote_retrieve_response_code($response);
			if (!$this->is_http_success($http_status_code)) {
				return $error_markup;
			}
			$this->capture_report_result($response, true);
		}

		$success_markup = <<<CONTENT
		    <div class="notice notice-success">
                The report was submitted successfully. See the history.
            </div>
CONTENT;
		return $success_markup;
	}

	public function print_report_preview(): void {
		$report_data = $this->generate_participation_report();
		$json = json_encode($report_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

		?>
		<h1>CRMLS Report Preview</h1>
		<div>Here's a preview of what we'd send if you hit this button right now.</div>
		<div>This report contains <?= count($report_data['Items']) ?> items (clients/agents). They are sorted by first name.</div>
		<textarea cols="100" rows="25" style="font-family: monospace;"><?= $json ?></textarea>
		<form method="POST">
			<input type="hidden" name="mls_name" value="<?= $this->mls_name ?>">
			<input type="submit" class="button button-primary button-large" name="send_participation_report" value="Send this report to the MLS">
		</form>
		<?php
	}

	private function get_access_token() {
		$url = 'https://api-trestle.corelogic.com/trestle/oidc/connect/token';
		$client_id = constant(strtoupper($this->mls_name) . '_CLIENT_ID');
		$client_secret = constant(strtoupper($this->mls_name) . '_CLIENT_SECRET');
		$data = [
			'client_id'     => $client_id,
			'client_secret' => $client_secret,
			'scope'         => 'api',
			'grant_type'    => 'client_credentials',
		];
		$response = wp_remote_post($url, [
			'method'   => 'POST',
			'body'     => $data,
			'blocking' => true,
			'headers'  => [
				'Accept'       => 'application/json',
				'Content-Type' => 'application/x-www-form-urlencoded',
			],
		]);
		if (is_wp_error($response)) {
			return $response;
		} else {
			$response_body = wp_remote_retrieve_body($response);
			$http_status_code = wp_remote_retrieve_response_code($response);
			$json = json_decode($response_body, true);
			if (!$this->is_http_success($http_status_code)) {
				$err_msg = json_last_error() ? $response_body : $json;
				return new WP_Error($http_status_code, $err_msg);
			}
			return $json['access_token'];
		}
	}

	private function generate_participation_report() {
		$posts = $this->get_active_clients();
		$items = array_map(function($post) {
			$client_info = get_post_meta($post->ID, 'client_info', true);
			$website = get_post_meta($post->ID, 'website', true);
			$service_start_date = (new DateTime($post->post_date))->format(static::$crmls_report_date_format_string);
			return [
				'OriginatingSystemName' => 'CRMLS',
				'ParticipantFirstName'  => $client_info['fname'],
				'ParticipantLastName'   => $client_info['lname'],
				'ParticipantType'       => 'Agent',
				'DisplayURLs'           => [$website['domain']],
				'ServiceStartDate'      => $service_start_date,
				'Notes'                 => json_encode(['post_id' => $post->ID]),
			];
		}, $posts);
		// Sort the items to make it easier to compare to the wp-admin where we can filter by mls and visually prove
		// that we have the right clients in the list.
		usort($items, function($a, $b) {
			return $a['ParticipantFirstName'] <=> $b['ParticipantFirstName'];
		});

		$report_data = [
			'ReportDate'      => date(static::$crmls_report_date_format_string),
			'ReportFrequency' => 'Monthly',
			'Items'           => $items,
		];
		return $report_data;
	}

	protected function get_result_from_history_record($record): array {
		return $record['response'];
	}

	protected function get_participation_reporting_history() {
		// This was helpful during initial development. It's an example successful response and an example error
		// response.
		// return [
		//     [
		//         'datetime' => new DateTime(),
		//         'http_status_code' => 200,
		//         'response' => [
		//             "@odata.context"=> "https=>//api-trestle.corelogic.com/trestle/report/metadata#TpParticipantReport/entity",
		//             "TpParticipantReportID"=> 35,
		//             "TpID"=> 196,
		//             "BusinessProductID"=> 47,
		//             "DataFeedID"=> 17,
		//             "ReportDate"=> "2020-02-02",
		//             "ReportFrequency"=> "Daily",
		//             "TpNotes"=> null,
		//             "Recieved"=> "2020-03-13T15=>53=>49.8458511Z",
		//             "ErrorCode"=> "OK",
		//             "ErrorDescription"=> null
		//         ],
		//     ],
		//     [
		// 	    'datetime' => new DateTime(),
		// 	    'http_status_code' => 400,
		// 	    'response' => [
		// 		    "error" => [
		// 			    "code" => "",
		// 			    "message" => "Items[0] =>\nThis is not a URL! is not a well formed URL",
		// 			    "details" => [
		// 				    [
		// 					    "code" => "",
		// 					    "target" => "Items[0]",
		// 					    "message" => "Foo Bar Baz is not a well formed URL"
		// 				    ],
		// 			    ],
		// 		    ],
		// 	    ],
		//     ],
		// ];

		return parent::get_participation_reporting_history();
	}

	protected function is_history_record_success($record): bool {
		return parent::is_http_history_record_success($record);
	}
}
