<?php

namespace Profound\API;

use Profound\API\API;

abstract class APIResponder {
	protected $api;
	protected $api_service_namespace_base = "vnd.profoundmls.idx.";
	private $responseData;
	private $formattedResponse;

	abstract protected function getDefaultAPI();
	abstract protected function computeResponseData();

	protected function prepareVersioning() {
		$accept_header = $_SERVER['HTTP_ACCEPT'];
		preg_match("/application\/$this->api_service_namespace_base(.*)/", $accept_header, $matches);
		if (isset($matches[0])) {
			$this->setAPI(API::from_string($matches[1]));
		} else {
			$this->setAPI($this->getDefaultAPI());
		}
	}

	protected function setAPI($api) {
		$this->api = $api;
	}

	protected function getAPI() {
		return $this->api;
	}

	protected function getConfigFilePath() {
		$path = __DIR__ . '/../../../../config.ini';
		return $path;
	}

	protected function getContentType() {
		// This is a special case for before we versioned our requests to the API from our wordpress plugin.
		if (($this->api->getName() == 'search' || $this->api->getName() == 'prop') && $this->api->getVersion() == 'v1.00') {
			return "application/json";
		} else {
			return "application/$this->api_service_namespace_base{$this->getAPI()->get_api_string()}";	
		}
	}

	protected function setResponseHeaders() {
		header('Content-type: ' . $this->getContentType());
	}

	protected function setResponseData($data) {
		$this->responseData = $data;
	}

	protected function getResponseData() {
		return $this->responseData;
	}

	protected function setFormattedResponse($response) {
		$this->formattedResponse = $response;
	}

	protected function getFormattedResponse() {
		return $this->formattedResponse;
	}

	public function handleRequest() {
		$this->prepareVersioning();
		$this->setResponseHeaders();
		$this->sendResponse();
	}

	protected function formatResponse() {
		return json_encode($this->getResponseData(), JSON_INVALID_UTF8_IGNORE);
	}

	public function sendResponse() {
		$this->setResponseData($this->computeResponseData());
		$this->setFormattedResponse($this->formatResponse());
		echo $this->getFormattedResponse();
	}
}