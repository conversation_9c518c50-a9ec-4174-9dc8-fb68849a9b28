/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var n in i)e.o(i,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:i[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MoveToWidgetArea:()=>X,addWidgetIdToBlock:()=>K,getWidgetIdFromBlock:()=>q,registerLegacyWidgetBlock:()=>ee,registerLegacyWidgetVariations:()=>Y,registerWidgetGroupBlock:()=>te});var i={};e.r(i),e.d(i,{yu:()=>W,UU:()=>A,W0:()=>O});var n={};e.r(n),e.d(n,{yu:()=>$,UU:()=>Q,W0:()=>Z});const s=window.wp.blocks,r=window.wp.primitives,o=window.ReactJSXRuntime,a=(0,o.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(r.Path,{d:"M6 3H8V5H16V3H18V5C19.1046 5 20 5.89543 20 7V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V7C4 5.89543 4.89543 5 6 5V3ZM18 6.5H6C5.72386 6.5 5.5 6.72386 5.5 7V8H18.5V7C18.5 6.72386 18.2761 6.5 18 6.5ZM18.5 9.5H5.5V19C5.5 19.2761 5.72386 19.5 6 19.5H18C18.2761 19.5 18.5 19.2761 18.5 19V9.5ZM11 11H13V13H11V11ZM7 11V13H9V11H7ZM15 13V11H17V13H15Z"})});function c(e){var t,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(i=c(e[t]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i);return n}const l=function(){for(var e,t,i=0,n="",s=arguments.length;i<s;i++)(e=arguments[i])&&(t=c(e))&&(n&&(n+=" "),n+=t);return n},d=window.wp.blockEditor,h=window.wp.components,u=(0,o.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(r.Path,{d:"M4 20h8v-1.5H4V20zM18.9 3.5c-.6-.6-1.5-.6-2.1 0l-7.2 7.2c-.4-.1-.7 0-1.1.1-.5.2-1.5.7-1.9 2.2-.4 1.7-.8 2.2-1.1 2.7-.1.1-.2.3-.3.4l-.6 1.1H6c2 0 3.4-.4 4.7-1.4.8-.6 1.2-1.4 1.3-2.3 0-.3 0-.5-.1-.7L19 5.7c.5-.6.5-1.6-.1-2.2zM9.7 14.7c-.7.5-1.5.8-2.4 1 .2-.5.5-1.2.8-2.3.2-.6.4-1 .8-1.1.5-.1 1 .1 1.3.3.2.2.3.5.2.8 0 .3-.1.9-.7 1.3z"})}),m=window.wp.i18n,w=window.wp.element,g=window.wp.coreData,p=window.wp.data;function f({selectedId:e,onSelect:t}){const i=(0,p.useSelect)((e=>{var t;const i=null!==(t=e(d.store).getSettings()?.widgetTypesToHideFromLegacyWidgetBlock)&&void 0!==t?t:[];return e(g.store).getWidgetTypes({per_page:-1})?.filter((e=>!i.includes(e.id)))}),[]);return i?0===i.length?(0,m.__)("There are no widgets available."):(0,o.jsx)(h.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,m.__)("Legacy widget"),value:null!=e?e:"",options:[{value:"",label:(0,m.__)("Select widget")},...i.map((e=>({value:e.id,label:e.name})))],onChange:e=>{if(e){const n=i.find((t=>t.id===e));t({selectedId:n.id,isMulti:n.is_multi})}else t({selectedId:null})}}):(0,o.jsx)(h.Spinner,{})}function b({name:e,description:t}){return(0,o.jsxs)("div",{className:"wp-block-legacy-widget-inspector-card",children:[(0,o.jsx)("h3",{className:"wp-block-legacy-widget-inspector-card__name",children:e}),(0,o.jsx)("span",{children:t})]})}const v=window.wp.notices,y=window.wp.compose,_=window.wp.apiFetch;var x=e.n(_);class j{constructor({id:e,idBase:t,instance:i,onChangeInstance:n,onChangeHasPreview:s,onError:r}){this.id=e,this.idBase=t,this._instance=i,this._hasPreview=null,this.onChangeInstance=n,this.onChangeHasPreview=s,this.onError=r,this.number=++k,this.handleFormChange=(0,y.debounce)(this.handleFormChange.bind(this),200),this.handleFormSubmit=this.handleFormSubmit.bind(this),this.initDOM(),this.bindEvents(),this.loadContent()}destroy(){this.unbindEvents(),this.element.remove()}initDOM(){var e,t;this.element=B("div",{class:"widget open"},[B("div",{class:"widget-inside"},[this.form=B("form",{class:"form",method:"post"},[B("input",{class:"widget-id",type:"hidden",name:"widget-id",value:null!==(e=this.id)&&void 0!==e?e:`${this.idBase}-${this.number}`}),B("input",{class:"id_base",type:"hidden",name:"id_base",value:null!==(t=this.idBase)&&void 0!==t?t:this.id}),B("input",{class:"widget-width",type:"hidden",name:"widget-width",value:"250"}),B("input",{class:"widget-height",type:"hidden",name:"widget-height",value:"200"}),B("input",{class:"widget_number",type:"hidden",name:"widget_number",value:this.idBase?this.number.toString():""}),this.content=B("div",{class:"widget-content"}),this.id&&B("button",{class:"button is-primary",type:"submit"},(0,m.__)("Save"))])])])}bindEvents(){if(window.jQuery){const{jQuery:e}=window;e(this.form).on("change",null,this.handleFormChange),e(this.form).on("input",null,this.handleFormChange),e(this.form).on("submit",this.handleFormSubmit)}else this.form.addEventListener("change",this.handleFormChange),this.form.addEventListener("input",this.handleFormChange),this.form.addEventListener("submit",this.handleFormSubmit)}unbindEvents(){if(window.jQuery){const{jQuery:e}=window;e(this.form).off("change",null,this.handleFormChange),e(this.form).off("input",null,this.handleFormChange),e(this.form).off("submit",this.handleFormSubmit)}else this.form.removeEventListener("change",this.handleFormChange),this.form.removeEventListener("input",this.handleFormChange),this.form.removeEventListener("submit",this.handleFormSubmit)}async loadContent(){try{if(this.id){const{form:e}=await C(this.id);this.content.innerHTML=e}else if(this.idBase){const{form:e,preview:t}=await S({idBase:this.idBase,instance:this.instance,number:this.number});if(this.content.innerHTML=e,this.hasPreview=!T(t),!this.instance.hash){const{instance:e}=await S({idBase:this.idBase,instance:this.instance,number:this.number,formData:M(this.form)});this.instance=e}}if(window.jQuery){const{jQuery:e}=window;e(document).trigger("widget-added",[e(this.element)])}}catch(e){this.onError(e)}}handleFormChange(){this.idBase&&this.saveForm()}handleFormSubmit(e){e.preventDefault(),this.saveForm()}async saveForm(){const e=M(this.form);try{if(this.id){const{form:t}=await C(this.id,e);if(this.content.innerHTML=t,window.jQuery){const{jQuery:e}=window;e(document).trigger("widget-updated",[e(this.element)])}}else if(this.idBase){const{instance:t,preview:i}=await S({idBase:this.idBase,instance:this.instance,number:this.number,formData:e});this.instance=t,this.hasPreview=!T(i)}}catch(e){this.onError(e)}}get instance(){return this._instance}set instance(e){this._instance!==e&&(this._instance=e,this.onChangeInstance(e))}get hasPreview(){return this._hasPreview}set hasPreview(e){this._hasPreview!==e&&(this._hasPreview=e,this.onChangeHasPreview(e))}}let k=0;function B(e,t={},i=null){const n=document.createElement(e);for(const[e,i]of Object.entries(t))n.setAttribute(e,i);if(Array.isArray(i))for(const e of i)e&&n.appendChild(e);else"string"==typeof i&&(n.innerText=i);return n}async function C(e,t=null){let i;return i=t?await x()({path:`/wp/v2/widgets/${e}?context=edit`,method:"PUT",data:{form_data:t}}):await x()({path:`/wp/v2/widgets/${e}?context=edit`,method:"GET"}),{form:i.rendered_form}}async function S({idBase:e,instance:t,number:i,formData:n=null}){const s=await x()({path:`/wp/v2/widget-types/${e}/encode`,method:"POST",data:{instance:t,number:i,form_data:n}});return{instance:s.instance,form:s.form,preview:s.preview}}function T(e){const t=document.createElement("div");return t.innerHTML=e,H(t)}function H(e){switch(e.nodeType){case e.TEXT_NODE:return""===e.nodeValue.trim();case e.ELEMENT_NODE:return!["AUDIO","CANVAS","EMBED","IFRAME","IMG","MATH","OBJECT","SVG","VIDEO"].includes(e.tagName)&&(!e.hasChildNodes()||Array.from(e.childNodes).every(H));default:return!0}}function M(e){return new window.URLSearchParams(Array.from(new window.FormData(e))).toString()}function I({title:e,isVisible:t,id:i,idBase:n,instance:s,isWide:r,onChangeInstance:a,onChangeHasPreview:c}){const d=(0,w.useRef)(),u=(0,y.useViewportMatch)("small"),g=(0,w.useRef)(new Set),f=(0,w.useRef)(new Set),{createNotice:b}=(0,p.useDispatch)(v.store);return(0,w.useEffect)((()=>{if(f.current.has(s))return void f.current.delete(s);const e=new j({id:i,idBase:n,instance:s,onChangeInstance(e){g.current.add(s),f.current.add(e),a(e)},onChangeHasPreview:c,onError(e){window.console.error(e),b("error",(0,m.sprintf)((0,m.__)('The "%s" block was affected by errors and may not function properly. Check the developer tools for more details.'),n||i))}});return d.current.appendChild(e.element),()=>{g.current.has(s)?g.current.delete(s):e.destroy()}}),[i,n,s,a,c,u]),r&&u?(0,o.jsxs)("div",{className:l({"wp-block-legacy-widget__container":t}),children:[t&&(0,o.jsx)("h3",{className:"wp-block-legacy-widget__edit-form-title",children:e}),(0,o.jsx)(h.Popover,{focusOnMount:!1,placement:"right",offset:32,resize:!1,flip:!1,shift:!0,children:(0,o.jsx)("div",{ref:d,className:"wp-block-legacy-widget__edit-form",hidden:!t})})]}):(0,o.jsx)("div",{ref:d,className:"wp-block-legacy-widget__edit-form",hidden:!t,children:(0,o.jsx)("h3",{className:"wp-block-legacy-widget__edit-form-title",children:e})})}function V({idBase:e,instance:t,isVisible:i}){const[n,s]=(0,w.useState)(!1),[r,a]=(0,w.useState)("");(0,w.useEffect)((()=>{const i=void 0===window.AbortController?void 0:new window.AbortController;return async function(){const n=`/wp/v2/widget-types/${e}/render`;return await x()({path:n,method:"POST",signal:i?.signal,data:t?{instance:t}:{}})}().then((e=>{a(e.preview)})).catch((e=>{if("AbortError"!==e.name)throw e})),()=>i?.abort()}),[e,t]);const c=(0,y.useRefEffect)((e=>{if(!n)return;function t(){var t,i;const n=Math.max(null!==(t=e.contentDocument.documentElement?.offsetHeight)&&void 0!==t?t:0,null!==(i=e.contentDocument.body?.offsetHeight)&&void 0!==i?i:0);e.style.height=`${0!==n?n:100}px`}const{IntersectionObserver:i}=e.ownerDocument.defaultView,s=new i((([e])=>{e.isIntersecting&&t()}),{threshold:1});return s.observe(e),e.addEventListener("load",t),()=>{s.disconnect(),e.removeEventListener("load",t)}}),[n]);return(0,o.jsxs)(o.Fragment,{children:[i&&!n&&(0,o.jsx)(h.Placeholder,{children:(0,o.jsx)(h.Spinner,{})}),(0,o.jsx)("div",{className:l("wp-block-legacy-widget__edit-preview",{"is-offscreen":!i||!n}),children:(0,o.jsx)(h.Disabled,{children:(0,o.jsx)("iframe",{ref:c,className:"wp-block-legacy-widget__edit-preview-iframe",tabIndex:"-1",title:(0,m.__)("Legacy Widget Preview"),srcDoc:r,onLoad:e=>{e.target.contentDocument.body.style.overflow="hidden",s(!0)},height:100})})})]})}function P({name:e}){return(0,o.jsxs)("div",{className:"wp-block-legacy-widget__edit-no-preview",children:[e&&(0,o.jsx)("h3",{children:e}),(0,o.jsx)("p",{children:(0,m.__)("No preview available.")})]})}function E({clientId:e,rawInstance:t}){const{replaceBlocks:i}=(0,p.useDispatch)(d.store);return(0,o.jsx)(h.ToolbarButton,{onClick:()=>{t.title?i(e,[(0,s.createBlock)("core/heading",{content:t.title}),...(0,s.rawHandler)({HTML:t.text})]):i(e,(0,s.rawHandler)({HTML:t.text}))},children:(0,m.__)("Convert to blocks")})}function F({attributes:{id:e,idBase:t},setAttributes:i}){return(0,o.jsx)(h.Placeholder,{icon:(0,o.jsx)(d.BlockIcon,{icon:u}),label:(0,m.__)("Legacy Widget"),children:(0,o.jsx)(h.Flex,{children:(0,o.jsx)(h.FlexBlock,{children:(0,o.jsx)(f,{selectedId:null!=e?e:t,onSelect:({selectedId:e,isMulti:t})=>{i(e?t?{id:null,idBase:e,instance:{}}:{id:e,idBase:null,instance:null}:{id:null,idBase:null,instance:null})}})})})})}function N({attributes:{id:e,idBase:t,instance:i},setAttributes:n,clientId:s,isSelected:r,isWide:a=!1}){const[c,l]=(0,w.useState)(null),p=null!=e?e:t,{record:f,hasResolved:v}=(0,g.useEntityRecord)("root","widgetType",p),y=(0,w.useCallback)((e=>{n({instance:e})}),[]);if(!f&&v)return(0,o.jsx)(h.Placeholder,{icon:(0,o.jsx)(d.BlockIcon,{icon:u}),label:(0,m.__)("Legacy Widget"),children:(0,m.__)("Widget is missing.")});if(!v)return(0,o.jsx)(h.Placeholder,{children:(0,o.jsx)(h.Spinner,{})});const _=t&&!r?"preview":"edit";return(0,o.jsxs)(o.Fragment,{children:["text"===t&&(0,o.jsx)(d.BlockControls,{group:"other",children:(0,o.jsx)(E,{clientId:s,rawInstance:i.raw})}),(0,o.jsx)(d.InspectorControls,{children:(0,o.jsx)(b,{name:f.name,description:f.description})}),(0,o.jsx)(I,{title:f.name,isVisible:"edit"===_,id:e,idBase:t,instance:i,isWide:a,onChangeInstance:y,onChangeHasPreview:l}),t&&(0,o.jsxs)(o.Fragment,{children:[null===c&&"preview"===_&&(0,o.jsx)(h.Placeholder,{children:(0,o.jsx)(h.Spinner,{})}),!0===c&&(0,o.jsx)(V,{idBase:t,instance:i,isVisible:"preview"===_}),!1===c&&"preview"===_&&(0,o.jsx)(P,{name:f.name})]})]})}const L=[{block:"core/calendar",widget:"calendar"},{block:"core/search",widget:"search"},{block:"core/html",widget:"custom_html",transform:({content:e})=>({content:e})},{block:"core/archives",widget:"archives",transform:({count:e,dropdown:t})=>({displayAsDropdown:!!t,showPostCounts:!!e})},{block:"core/latest-posts",widget:"recent-posts",transform:({show_date:e,number:t})=>({displayPostDate:!!e,postsToShow:t})},{block:"core/latest-comments",widget:"recent-comments",transform:({number:e})=>({commentsToShow:e})},{block:"core/tag-cloud",widget:"tag_cloud",transform:({taxonomy:e,count:t})=>({showTagCounts:!!t,taxonomy:e})},{block:"core/categories",widget:"categories",transform:({count:e,dropdown:t,hierarchical:i})=>({displayAsDropdown:!!t,showPostCounts:!!e,showHierarchy:!!i})},{block:"core/audio",widget:"media_audio",transform:({url:e,preload:t,loop:i,attachment_id:n})=>({src:e,id:n,preload:t,loop:i})},{block:"core/video",widget:"media_video",transform:({url:e,preload:t,loop:i,attachment_id:n})=>({src:e,id:n,preload:t,loop:i})},{block:"core/image",widget:"media_image",transform:({alt:e,attachment_id:t,caption:i,height:n,link_classes:s,link_rel:r,link_target_blank:o,link_type:a,link_url:c,size:l,url:d,width:h})=>({alt:e,caption:i,height:n,id:t,link:c,linkClass:s,linkDestination:a,linkTarget:o?"_blank":void 0,rel:r,sizeSlug:l,url:d,width:h})},{block:"core/gallery",widget:"media_gallery",transform:({ids:e,link_type:t,size:i,number:n})=>({ids:e,columns:n,linkTo:t,sizeSlug:i,images:e.map((e=>({id:e})))})},{block:"core/rss",widget:"rss",transform:({url:e,show_author:t,show_date:i,show_summary:n,items:s})=>({feedURL:e,displayAuthor:!!t,displayDate:!!i,displayExcerpt:!!n,itemsToShow:s})}].map((({block:e,widget:t,transform:i})=>({type:"block",blocks:[e],isMatch:({idBase:e,instance:i})=>e===t&&!!i?.raw,transform:({instance:t})=>{const n=(0,s.createBlock)(e,i?i(t.raw):void 0);return t.raw?.title?[(0,s.createBlock)("core/heading",{content:t.raw.title}),n]:n}}))),D={to:L},W={$schema:"https://schemas.wp.org/trunk/block.json",apiVersion:3,name:"core/legacy-widget",title:"Legacy Widget",category:"widgets",description:"Display a legacy widget.",textdomain:"default",attributes:{id:{type:"string",default:null},idBase:{type:"string",default:null},instance:{type:"object",default:null}},supports:{html:!1,customClassName:!1,reusable:!1},editorStyle:"wp-block-legacy-widget-editor"},{name:A}=W,O={icon:a,edit:function(e){const{id:t,idBase:i}=e.attributes,{isWide:n=!1}=e,s=(0,d.useBlockProps)({className:l({"is-wide-widget":n})});return(0,o.jsx)("div",{...s,children:t||i?(0,o.jsx)(N,{...e}):(0,o.jsx)(F,{...e})})},transforms:D},z=(0,o.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)(r.Path,{d:"M18 4h-7c-1.1 0-2 .9-2 2v3H6c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h7c1.1 0 2-.9 2-2v-3h3c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4.5 14c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5v-7c0-.3.2-.5.5-.5h3V13c0 1.1.9 2 2 2h2.5v3zm0-4.5H11c-.3 0-.5-.2-.5-.5v-2.5H13c.3 0 .5.2.5.5v2.5zm5-.5c0 .3-.2.5-.5.5h-3V11c0-1.1-.9-2-2-2h-2.5V6c0-.3.2-.5.5-.5h7c.3 0 .5.2.5.5v7z"})});function R({clientId:e}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(h.Placeholder,{className:"wp-block-widget-group__placeholder",icon:(0,o.jsx)(d.BlockIcon,{icon:z}),label:(0,m.__)("Widget Group"),children:(0,o.jsx)(d.ButtonBlockAppender,{rootClientId:e})}),(0,o.jsx)(d.InnerBlocks,{renderAppender:!1})]})}function G({attributes:e,setAttributes:t}){var i;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d.RichText,{tagName:"h2",identifier:"title",className:"widget-title",allowedFormats:[],placeholder:(0,m.__)("Title"),value:null!==(i=e.title)&&void 0!==i?i:"",onChange:e=>t({title:e})}),(0,o.jsx)(d.InnerBlocks,{})]})}const U=[{attributes:{title:{type:"string"}},supports:{html:!1,inserter:!0,customClassName:!0,reusable:!1},save:({attributes:e})=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d.RichText.Content,{tagName:"h2",className:"widget-title",value:e.title}),(0,o.jsx)(d.InnerBlocks.Content,{})]})}],$={$schema:"https://schemas.wp.org/trunk/block.json",apiVersion:3,name:"core/widget-group",title:"Widget Group",category:"widgets",attributes:{title:{type:"string"}},supports:{html:!1,inserter:!0,customClassName:!0,reusable:!1},editorStyle:"wp-block-widget-group-editor",style:"wp-block-widget-group"},{name:Q}=$,Z={title:(0,m.__)("Widget Group"),description:(0,m.__)("Create a classic widget layout with a title that’s styled by your theme for your widget areas."),icon:z,__experimentalLabel:({name:e})=>e,edit:function(e){const{clientId:t}=e,{innerBlocks:i}=(0,p.useSelect)((e=>e(d.store).getBlock(t)),[t]);return(0,o.jsx)("div",{...(0,d.useBlockProps)({className:"widget"}),children:0===i.length?(0,o.jsx)(R,{...e}):(0,o.jsx)(G,{...e})})},save:function({attributes:e}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d.RichText.Content,{tagName:"h2",className:"widget-title",value:e.title}),(0,o.jsx)("div",{className:"wp-widget-group__inner-blocks",children:(0,o.jsx)(d.InnerBlocks.Content,{})})]})},transforms:{from:[{type:"block",isMultiBlock:!0,blocks:["*"],isMatch:(e,t)=>!t.some((e=>"core/widget-group"===e.name)),__experimentalConvert(e){let t=[...e.map((e=>(0,s.createBlock)(e.name,e.attributes,e.innerBlocks)))];const i="core/heading"===t[0].name?t[0]:null;return t=t.filter((e=>e!==i)),(0,s.createBlock)("core/widget-group",{...i&&{title:i.attributes.content}},t)}}]},deprecated:U},J=(0,o.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(r.Path,{d:"M19.75 9c0-1.257-.565-2.197-1.39-2.858-.797-.64-1.827-1.017-2.815-1.247-1.802-.42-3.703-.403-4.383-.396L11 4.5V6l.177-.001c.696-.006 2.416-.02 4.028.356.887.207 1.67.518 2.216.957.52.416.829.945.829 1.688 0 .592-.167.966-.407 1.23-.255.281-.656.508-1.236.674-1.19.34-2.82.346-4.607.346h-.077c-1.692 0-3.527 0-4.942.404-.732.209-1.424.545-1.935 1.108-.526.579-.796 1.33-.796 2.238 0 1.257.565 2.197 1.39 2.858.797.64 1.827 1.017 2.815 1.247 1.802.42 3.703.403 4.383.396L13 19.5h.714V22L18 18.5 13.714 15v3H13l-.177.001c-.696.006-2.416.02-4.028-.356-.887-.207-1.67-.518-2.216-.957-.52-.416-.829-.945-.829-1.688 0-.592.167-.966.407-1.23.255-.281.656-.508 1.237-.674 1.189-.34 2.819-.346 4.606-.346h.077c1.692 0 3.527 0 4.941-.404.732-.209 1.425-.545 1.936-1.108.526-.579.796-1.33.796-2.238z"})});function X({currentWidgetAreaId:e,widgetAreas:t,onSelect:i}){return(0,o.jsx)(h.ToolbarGroup,{children:(0,o.jsx)(h.ToolbarItem,{children:n=>(0,o.jsx)(h.DropdownMenu,{icon:J,label:(0,m.__)("Move to widget area"),toggleProps:n,children:({onClose:n})=>(0,o.jsx)(h.MenuGroup,{label:(0,m.__)("Move to"),children:(0,o.jsx)(h.MenuItemsChoice,{choices:t.map((e=>({value:e.id,label:e.name,info:e.description}))),value:e,onSelect:e=>{i(e),n()}})})})})})}function q(e){return e.attributes.__internalWidgetId}function K(e,t){return{...e,attributes:{...e.attributes||{},__internalWidgetId:t}}}function Y(e){const t=(0,p.subscribe)((()=>{var i;const n=null!==(i=e?.widgetTypesToHideFromLegacyWidgetBlock)&&void 0!==i?i:[],r=(0,p.select)(g.store).getWidgetTypes({per_page:-1})?.filter((e=>!n.includes(e.id)));r&&(t(),(0,p.dispatch)(s.store).addBlockVariations("core/legacy-widget",r.map((e=>({name:e.id,title:e.name,description:e.description,attributes:e.is_multi?{idBase:e.id,instance:{}}:{id:e.id}})))))}))}function ee(e={}){const{yu:t,W0:n,UU:r}=i;(0,s.registerBlockType)({name:r,...t},{...n,supports:{...n.supports,...e}})}function te(e={}){const{yu:t,W0:i,UU:r}=n;(0,s.registerBlockType)({name:r,...t},{...i,supports:{...i.supports,...e}})}(window.wp=window.wp||{}).widgets=t})();