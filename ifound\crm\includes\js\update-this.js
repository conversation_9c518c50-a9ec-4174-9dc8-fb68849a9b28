jQuery( document ).ready( function( $ ) {
	
	$( '.update-this' ).on( 'click', function( e ) {
		
		var input = new Object();
			input.params 	= $( '#ifound-dynamic-form' ).serialize();
			input.save_this_id 	= $( this ).attr( 'save_this_id' );
		
		runAjax(input);
	});
		
	function runAjax(input){
		
		jQuery.ajax ( {
			url : update_this.endpoint,
			type : 'post', 
			data : {
				action : 'update_this',
				input : input,
				update_this_nonce : update_this.nonce,
			},
			beforeSend: function() {
				$( '#save-this-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
			},
			success: function( response ) {
				$( '#save-this-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response );
			},
			dataType:'json'
		});
	}
	
});
