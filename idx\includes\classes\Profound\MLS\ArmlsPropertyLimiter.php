<?php

namespace Profound\MLS;

class ArmlsPropertyLimiter extends PropertyLimiter {
	public function limitProperties(&$sql, $options = array()) {
		parent::limitProperties($sql, $options);

		// See case 516. Ahwatukee is not a real city, so we replace the city
		// search by a zip code search code, for convenience.
		$sql = preg_replace("/LIST_39\s=\s'ahwatukee'/i", "(LIST_43 = '85044' or LIST_43 = '85045' or LIST_43 = '85048')", $sql);

		if ($this->getMls()->getAccess()->getHideUCB()) {
			$sql .= " AND LIST_19 <> 'UCB (Under Contract-Backups)' AND LIST_19 <> 'CCBS (Contract Contingent on Buyer Sale)' AND LIST_15 <> 'Pending'";
		}
	}
}
