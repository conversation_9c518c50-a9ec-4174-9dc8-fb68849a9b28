@charset "UTF-8";
/* CSS Document */

.mobile-show-criteria {
	display: none;
}

.search-results-wrapper{
	min-height: 350px;
}


@media all and (max-width: 480px) {

	.mobile-show-criteria {
		display: block;
		cursor: pointer;
		float: right;
	}

	.mobile-show-criteria:before {
		font-family: 'Font Awesome 5 Pro';
		content: "\f078";
	}

	.mobile-show-criteria.active:before {
		display: block;
		font-family: 'Font Awesome 5 Pro';
    	content: "\f077";
	}

	.search-criteria-wrapper,
	.ifound-dynamic-form {
		display: none;
	}

	.search-results-wrapper{
		min-height: 30px;
	}

}


