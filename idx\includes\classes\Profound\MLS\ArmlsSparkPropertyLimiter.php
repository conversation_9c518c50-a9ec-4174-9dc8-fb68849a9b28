<?php

namespace Profound\MLS;

class ArmlsSparkPropertyLimiter extends PropertyLimiter {
	public function limitProperties(&$sql, $options = array()) {
		parent::limitProperties($sql, $options);

		if ($this->getMls()->getAccess()->getHideUCB()) {
			$sql .= " AND MlsStatus <> 'UCB (Under Contract-Backups)' AND MlsStatus <> 'CCBS (Contract Contingent on Buyer Sale)' AND MlsStatus <> 'Pending'";
		}
	}
}
