import {
	getLatestPropertyHistory,
	insertPropertyHistory,
	upsertPropertyChanges,
	withTransaction,
	Property,
	PropertyHistory,
} from './database.js'
import mysql from 'mysql2/promise'
import config from './config.js'

export interface ChangeDetectionResult {
	listingId: string
	currentProperty: Property
	lastHistory: PropertyHistory | null
	hasChanges: boolean
	standardStatusChanged: boolean
	priceChanged: boolean
	isFirstTime: boolean
}

export async function detectChanges(
	currentProperty: Property,
	mlsSystemName: string,
	mlsClassName: string,
	conn?: mysql.Connection
): Promise<ChangeDetectionResult> {
	const mlsClass = config.getMlsClass(mlsSystemName, mlsClassName)
	const lastHistory = await getLatestPropertyHistory(
		currentProperty.ListingId,
		mlsClass.property_history_table_name,
		conn
	)

	const isFirstTime = !lastHistory

	if (isFirstTime) {
		return {
			listingId: currentProperty.ListingId,
			currentProperty,
			lastHistory: null,
			hasChanges: false, // First time is not considered a "change"
			standardStatusChanged: false,
			priceChanged: false,
			isFirstTime: true,
		}
	}

	const standardStatusChanged = currentProperty.StandardStatus !== lastHistory.StandardStatus
	const priceChanged = Math.abs(currentProperty.Price - lastHistory.Price) > 0.001 // Handle decimal precision
	const hasChanges = standardStatusChanged || priceChanged

	return {
		listingId: currentProperty.ListingId,
		currentProperty,
		lastHistory,
		hasChanges,
		standardStatusChanged,
		priceChanged,
		isFirstTime: false,
	}
}

export async function processListingChange(
	result: ChangeDetectionResult,
	mlsSystemName: string,
	mlsClassName: string,
	conn?: mysql.Connection
): Promise<void> {
	const { currentProperty } = result
	const mlsClass = config.getMlsClass(mlsSystemName, mlsClassName)

	// Always insert into property_history for first time or when changes detected
	if (result.isFirstTime || result.hasChanges) {
		await insertPropertyHistory(
			{
				ListingId: currentProperty.ListingId,
				StandardStatus: currentProperty.StandardStatus,
				Price: currentProperty.Price,
				ModificationTimestamp: currentProperty.ModificationTimestamp,
			},
			mlsClass.property_history_table_name,
			conn
		)
	}

	// Only upsert property_changes when there are actual changes (not first time)
	if (result.hasChanges && !result.isFirstTime) {
		const now = new Date()

		await upsertPropertyChanges(
			{
				ListingId: currentProperty.ListingId,
				StandardStatus: currentProperty.StandardStatus,
				Price: currentProperty.Price,
				StandardStatus_ModificationTimestamp: result.standardStatusChanged
					? currentProperty.ModificationTimestamp
					: undefined,
				StandardStatus_updated_at: result.standardStatusChanged ? now : undefined,
				Price_ModificationTimestamp: result.priceChanged
					? currentProperty.ModificationTimestamp
					: undefined,
				Price_updated_at: result.priceChanged ? now : undefined,
			},
			mlsClass.property_changes_table_name,
			conn
		)
	}
}

export async function processListingRecords(
	records: Property[],
	mlsSystemName: string,
	mlsClassName: string
): Promise<ChangeDetectionResult[]> {
	// Wrap all record processing in a single transaction
	return await withTransaction(async (conn) => {
		const results: ChangeDetectionResult[] = []

		for (const record of records) {
			try {
				const result = await detectChanges(record, mlsSystemName, mlsClassName, conn)
				await processListingChange(result, mlsSystemName, mlsClassName, conn)
				results.push(result)
			} catch (error) {
				// Re-throw with more context - transaction will be rolled back
				throw new Error(
					`Failed to process ListingId ${record.ListingId}: ${error instanceof Error ? error.message : 'Unknown error'}`
				)
			}
		}

		return results
	})
}
