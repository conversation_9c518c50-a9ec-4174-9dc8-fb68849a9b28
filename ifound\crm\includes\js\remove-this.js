jQuery( document ).ready( function( $ ) {
	
	$( '.remove-this' ).on( 'click', function( e ) {
		
		var ID = e.target.id;
		var status_div = $( this ).parents( '.save-this-section' );
		
		jQuery.ajax ( {
			url : remove_this.endpoint,
			type : 'post', 
			data : {
				action : 'remove_this',
				id : ID,
				remove_this_nonce : remove_this.nonce,
			},
			beforeSend: function() {
				$( status_div ).html( '<i class="fal fa-spinner fa-spin" aria-hidden="true"></i>' );
			},
			success: function( response ) {
				if(response == 'success' )
					$( status_div ).remove();
			},
			dataType:'json'
		});
	});
	
});