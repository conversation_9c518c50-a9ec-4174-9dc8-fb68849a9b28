<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * iFoundMap Class
 *
 * @since 3.4.0
 */

class iFoundMap extends iFoundIdx {
	public static $GOOGLE_MAPS_URL = 'https://maps.googleapis.com/maps/api/js?v=3.60';

	private $map_object;

	/**
	 * init iFoundMap class.
	 *
	 * @since 3.4.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}


	/**
	 * Constructor
	 *
	 * @since 3.4.0
	 */

	public function __construct($options = []) {
		// Normally our constructors do nothing but set up hooks so there is no need to call parent constructors.
		// But in this case, it does more than that so we must call it.
		parent::__construct(['enable_hooks' => false]);

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action( 'wp_enqueue_scripts', array( $this, 'map_scripts' ) );
			add_action( 'admin_enqueue_scripts', array( $this, 'map_scripts' ) );

			add_action( 'ifound_load_map', array( $this, 'load_map' ), 10, 2 );
			add_action( 'ifound_map_object', array( $this, 'map_object' ), 10, 3 );
			add_filter( 'ifound_map_data', array( $this, 'map_data' ), 10, 2 );
			add_filter( 'ifound_map_id', array( $this, 'map_id' ) );
			add_filter( 'ifound_map_colors', array( $this, 'map_colors' ) );
			add_filter( 'ifound_map_api', array( $this, 'map_api' ), 10, 2 );
			add_action( 'ifound_shapes_map', array( $this, 'shapes_map' ) );
		}
	}

	/**
	 * Map Scripts
	 *
	 * @since 3.4.0
	 */

	public function map_scripts() {

		global $mls_associations;

		// Get user's api key, if none have the iFound Agent api key
                if( ! ( $this->google_key = get_option( 'ifound_api_settings' )['google_custom_api'] ) ) {
                        $this->google_key = $this->get_config()['ifa_google_maps_api_key'];
			wp_register_script( 'disable_show_default_js', plugins_url('admin/js/disable-show-default.js', __DIR__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		}

		wp_register_script('load_map_js', plugins_url('js/load-map.js', __FILE__), ['jquery'], iFOUND_PLUGIN_VERSION);
		wp_localize_script('load_map_js', 'ifound_load_map', [
			'url'			=> static::$GOOGLE_MAPS_URL . '&callback=Function.prototype',
			'key'			=> $this->google_key,
			'geo'           => $mls_associations->geo,
		]);

		/** TODO: Look at css options for the Nearby Places buttons. That os the only thing that requires jquery-ui */
		wp_register_script( 'jquery-ui', 'https://code.jquery.com/ui/1.12.1/jquery-ui.js' );
		wp_register_script( 'nearby_places_js', plugins_url( 'js/nearby-places.js', __FILE__ ), array( 'jquery', 'jquery-ui', 'load_map_js' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'nearby_places_js', 'nearby_places', array(
			'subject_icon'	=> $this->subject_map_icon()
		));

		wp_register_script( 'results_map_js', plugins_url( 'js/results-map.js', __FILE__ ), array( 'jquery', 'load_map_js', 'underscore' ), iFOUND_PLUGIN_VERSION, true );
		wp_localize_script( 'results_map_js', 'ifound_map', array(
			'endpoint'        => '/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/search/' . wp_create_nonce('search_secure_me') . '/',
			'image_url'       => plugins_url('images/', __DIR__),
			'site_url'        => site_url(),
			'closed_icon'     => $this->closed_map_icon(),
			'active_icon'     => $this->active_map_icon(),
			'open_house_icon' => $this->open_house_map_icon(),
			'subject_icon'    => $this->subject_map_icon(),
			'geo'             => $mls_associations->geo,
			'do_bounds'       => defined('DOING_RESULTS') ? true : false,
		));

		wp_register_script( 'ifound_map_js', plugins_url( 'js/advanced.js', __FILE__ ), array( 'jquery', 'load_map_js', 'underscore' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'ifound_map_js', 'ifound_map', array(
			'endpoint'           => '/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/search/' . wp_create_nonce('search_secure_me') . '/',
			'shortcode'          => '/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/shortcode/' . wp_create_nonce('shortcode_secure_me') . '/',
			'image_url'          => plugins_url('images/', __DIR__),
			'site_url'           => site_url(),
			'closed_icon'        => $this->closed_map_icon(),
			'active_icon'        => $this->active_map_icon(),
			'open_house_icon'    => $this->open_house_map_icon(),
			'subject_icon'       => $this->subject_map_icon(),
			'geo'                => $mls_associations->geo,
			'advanced'           => true,
			'isMultipleStateMls' => $this->is_multiple_state_mls(),
		));

		wp_register_script( 'polygons_map_js', plugins_url( 'js/polygons-map.js', __FILE__ ), array( 'jquery', 'load_map_js' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'polygons_map_js', 'polygons_map', array(
			'geo'			=> $mls_associations->geo,
			'image_url'		=> plugins_url( 'images/', __DIR__ ),
			'site_url'		=> site_url( '/' ),
			'colors'		=> $this->map_colors(),
			'top' 			=> defined( 'ifound_map_top' ) ? ifound_map_top : 100
		));

		wp_register_script( 'area_map_js', plugins_url( 'js/area-map.js', __FILE__ ), array( 'jquery', 'load_map_js' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'area_map_js', 'area_map', array(
			'geo'			=> $mls_associations->geo,
			'image_url'		=> plugins_url( 'images/', __DIR__ ),
			'top' 			=> defined( 'ifound_map_top' ) ? ifound_map_top : 100
		));

	}

	/**
	 * Map Settings
	 *
	 * @since 3.4.0
	 */

	public function map_settings() {
		return (object) get_option( 'ifound_map_settings' );
	}

	public function map_icons() {
		return [
			'active'  => $this->active_map_icon(),
			'closed'  => $this->closed_map_icon(),
			'subject' => $this->subject_map_icon(),
		];
	}

	/**
	 * Active Map Icon
	 *
	 * @since 3.4.0
	 */

	public function active_map_icon() {
		$image = plugins_url( 'images/active.png', __DIR__ );
		return $image;
	}

	/**
	 * Closed Map Icon
	 *
	 * @since 3.4.0
	 */

	public function closed_map_icon() {
		$image = plugins_url( 'images/closed.png', __DIR__ );
		return $image;
	}

	/**
	 * Subject Map Icon
	 *
	 * @since 3.4.0
	 */

	public function subject_map_icon() {
		$image = plugins_url( 'images/subject.png', __DIR__ );
		return $image;
	}

	public function open_house_map_icon() {
		$image = plugins_url( 'images/open-house.png', __DIR__ );
		return $image;
	}

	/**
	 * Load Map
	 *
	 * Loads the map data script.
	 *
	 * @since 1.0.0
	 */

	public function load_map( $results, $extra = [] ) {

		$map_data = apply_filters( 'ifound_map_data', $results, $extra ); ?>

		<script>
			var map_data = <? echo json_encode( $map_data ); ?>
		</script><?

	}

	/**
	 * Map Object
	 *
	 * Loads the map_object script. This will eventually replace @see iFOUND::load_map()
	 *
	 * @since 1.9.0
	 *
	 * @param object $results The main results object.
	 * @param int 	 $map_id  The rand map id for this map. @see iFOUND::map_id()
	 */

	public function map_object( $results, $map_id, $extra = [] ) {

		// I don't know what the deal is with this map_object member var. It was originally written in a way such that
		// it was never initialized. Maybe the expectation is that this method would be called multiple times? Let's
		// initialize it unless it already has been.
		if (!isset($this->map_object->maps)) {
			$this->map_object = new stdClass();
			$this->map_object->maps = [];
		}
		$this->map_object->maps[$map_id] = apply_filters( 'ifound_map_data', $results, $extra ); ?>

		<script>
			if(typeof map_object === 'undefined') var map_object = [];
			map_object.push(<? echo json_encode( $this->map_object ); $this->map_object = NULL; ?>);
		</script><?

	}

	/**
	 * Map ID
	 *
	 * Rand map ID.
	 *
	 * @since 1.0.0
	 * @since 1.9.0 Add time() to map ID.
	 *
	 * @return int  $map_id A rand id for maps.
	 */

	public function map_id() {
		return random_int( 100, 99999999 ) . time();
	}

	/**
	 * Map Data
	 *
	 * Create the json map data oblect
	 *
	 * @since 1.0.0
	 * @since 1.0.8  Prevent JS errors by not allowing empty values.
	 * @since 1.2.20 Add conditions for missing or coordinates = 0 || 0.0 Note: 0 can be string, int, or float.
	 * @since 3.4.3  Check if $listings is an array or object.
	 * @since 4.1.4  Check list status in lowercase.
	 *
	 * @param  object $listing  MLS data for a all the listings.
	 * @return object $map_data The map data used to place pins on map.
	 */

	public function map_data( $results, $extra = [] ) {

		$listings = isset( $results->listings ) ? $results->listings : false;

		if( ( is_array( $listings ) || is_object( $listings ) ) && ! empty ( $listings ) ) {

			foreach( $listings as $listing ) {

				$latitude 	= floatval( $listing->Latitude );
				$longitude 	= floatval( $listing->Longitude );


				if (
					! is_float( $latitude )
					||
					! is_float( $longitude )
					||
					$latitude == -1
					||
					$longitude == -1
					||
					$latitude == 0
					||
					$longitude == 0
				) continue;

				$list_status = strtolower( $listing->ListStatus );

				/** Verify status and add it for results-map.js map pin type. */
				$pin_status = ( $list_status == 'closed' || $list_status == 'sold' ) ? 'closed' : 'active';

				$address = apply_filters( 'ifound_address', $listing );
				$has_open_house = isset($listing->open_houses) && count($listing->open_houses);

				$pins[] = array(
					'title' 		 => $address			?: false, 	//Displays on map pin hover.
					'center_lat' 	 => $latitude 			?: false,	// Pin lat
					'center_lng' 	 => $longitude 			?: false,	// Pin lng
					'status' 		 => $pin_status			?: false,  	// Determines map pin type.
					'has_open_house' => $has_open_house     ?: false,
					'mls_id'		 => $listing->ListingID	?: false	// Used in some cases as div ID to scroll page to that listing.
				);

			}

		}

		$input = isset( $results->input_obj ) ? $results->input_obj : $results;
		$input_obj = apply_filters( 'ifound_obj', $input );

		if( isset( $input_obj->polygons ) ) {

			$map = $this->polygon_to_map( $input_obj );

		}
		$other_items = [];
		if (isset($input_obj->nearby)) {
			if ($input_obj->nearby) {
				$other_items[] = [
					'metadata' => [
						'type' => 'nearby',
						'radius' => floatval($input_obj->nearby->radius),
					],
					'data' => [
						'position' => [
							'lat' => floatval($input_obj->nearby->lat),
							'lng' => floatval($input_obj->nearby->lng),
						],
					],
				];
			}
		}
		if (isset($extra['extra_map_data'])) {
			foreach (($extra['extra_map_data']['dropped_pins'] ?: []) as $dropped_pin) {
				$other_items[] = [
					'metadata' => [
						'type' => 'dropped_pin',
					],
					'data' => [
						'position' => $dropped_pin['position'],
					],
				];
			}
		}

		$show_map_by_default = false;
		$map_settings = get_option('ifound_map_settings');
		if (is_array($map_settings)) {
			$show_map_by_default = !!($map_settings['show_map_by_default'] ?? false);
		}

		return apply_filters( 'ifound_obj', array(
			'pins'                => !empty($pins) ? $pins : false,
			'options'             => isset($map['options']) ? $map['options'] : false,
			'shapes'              => isset($map['shapes']) ? $map['shapes'] : false,
			'other_items'         => $other_items ?: false,
			'map_type'            => isset($results->display['map_type']) ? 'satellite' : 'roadmap',
			'show_map_by_default' => $show_map_by_default,
		) );

	}

	/**
	 * Polygon to Map
	 *
	 * Convert polygons to map.
	 *
	 * @since 3.6.7
	 *
	 * @param  object $input_obj The input for this request.
	 * @return object $map       The map_object.
	 */

	public function polygon_to_map( $input_obj ) {

		global $mls_associations;

		$display  = isset( $input_obj->display ) ? $input_obj->display : false;

		$options = array(
			'zoom'	=> isset( $display->zoom )		? $display->zoom   : $mls_associations->geo->zoom,
			'height'=> isset( $display->height )	? $display->height : false,
			'center'=> array(
				'lat' => $mls_associations->geo->center_lat,
				'lng' => $mls_associations->geo->center_lng
			)
		);

		$polygons = $input_obj->polygons;

		foreach( $polygons as $polygon_id => $polygon ) {

			$paths = str_replace( '),(', ')(', $polygon->paths );

			$coords = explode( ')(', $paths );
			foreach( $coords as $coord ){

				list( $lat, $lng ) = explode( ',', $coord );
				$remove = array( '(', ')' );
				$path[] = array(
					'lat' => floatval( str_replace( $remove, '', $lat ) ),
					'lng' => floatval( str_replace( $remove, '', $lng ) )
				);

			}

			$shapes[] = array(
				'id'        => $polygon_id,
				'paths' 	=> $path 					?				  : false,
				'color'		=> isset( $polygon->color )	? $polygon->color : '#555'
			);

			unset($path);


		}


		$map = array(
			'shapes'  => $shapes,
			'other_items' => $other_items,
			'options' => $options
		);

		return $map;

	}

	/**
	 * Map Colors
	 *
	 * Approved map polygon colors
	 *
	 * @since 1.0.0
	 * @since 2.5.26 Add 20 more colors.
	 *
	 * @return array $map_colors Array of colors.
	 */

	public function map_colors() {

		return array(
			'#030237', '#eef848', '#379865',
			'#7c3798', '#385bdd', '#03557C',
			'#0985A7', '#c90101', '#4B0082',
			'#5C9496', '#680a29', '#92B5BF',
			'#32CD32', '#17BEC4', '#FF8C00',
			'#04515B', '#0A7269', '#14A488',
			'#841A35', '#F15C3D', '#2A82B8',
			'#071A30', '#033951', '#8e2a4b',
			'#359AD9', '#E64B3F', '#2C3F4F',
			'#1E90FF', '#FF1493', '#1bd874'
		);

	}

	/**
	 * Map API URL
	 *
	 * @since 1.0.0
	 * @since 2.5.29 Update URL and method name.
	 *
	 * @return string $api_url The API url.
	 */

	private function map_api_url() {
		return 'https://ifoundmaps.com/wp-json/ifound/1.0.0/';
	}

	/**
	 * Map API
	 *
	 * Make a request to the iFound API.
	 *
	 * @uses wp_remote_get()
	 * @link https://codex.wordpress.org/Function_Reference/wp_remote_get
	 *
	 * @since 1.0.0
	 * @since 2.5.27 Join params.
	 * @since 2.5.29 Update method name.
	 *
	 * @param  string $endpoint     The endpoint to make the request.
	 * @param  array  $params_array An array of params for this request.
	 * @return object $response     The API response on success. Bool false on failure.
	 */

	public function map_api( $endpoint, $params_array ) {

		global $idxurls;

		$params = join( '/', $params_array );

		$url = $this->map_api_url() . $endpoint . $params;

		$idxurls[] = $url;

		$response = wp_remote_get( $url );

		if( is_array( $response ) ) {

			return  json_decode( $response['body'] );

		}

		return false;

	}

	/**
	 * Shapes Map
	 *
	 * The shapes map html.
	 *
	 * @since 3.6.7
	 */

	public function shapes_map() { ?>
		<div class="map-and-palette-wrapper">
			<div class="shapes-map">
				<div class="ifound-wrap">
					<div id="ifound-shapes-map" class="ifound-shapes-map"></div>
				</div>
			</div>
			<span id="color-palette" class="ifound-color-palette"></span>
			<div class="legend">
				<div class="item">
					<div><img width="20" src="<?= $this->active_map_icon() ?>" /></div>
					<div>Active</div>
				</div>
				<div class="item">
					<div><img width="20" src="<?= $this->open_house_map_icon() ?>" /></div>
					<div>Open House</div>
				</div>
				<div class="item">
					<div><img width="20" src="<?= $this->closed_map_icon() ?>" /></div>
					<div>Sold</div>
				</div>
				<div class="item">
					<div><img width="20" src="<?= $this->subject_map_icon() ?>" /></div>
					<div>Subject</div>
				</div>
			</div>
		</div><?
	}
}

