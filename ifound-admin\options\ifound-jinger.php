<?
$wprmenu_options = array(
    'wprmenu_options'			    => array(
  	    'enabled' 							=> '1',
    		'wpr_live_preview' 					=> '1',
    		'search_box_menu_block' 			=> '18',
    		'search_box_menubar' 				=> '7',
    		'rtlview' 							=> '14',
    		'submenu_opened' 					=> '4',
    		'menu' 								=> (string) $this->menu_ids['main'],
    		'hide' 								=> '.nav-primary',
    		'search_box_text' 					=> 'Search...',
    		'bar_title' 						=> 'MENU',
    		'bar_logo' 							=> '',
    		'bar_logo_pos' 						=> 'left',
    		'logo_link' 						=> $this->dev_url,
    		'swipe' 							=> 'no',
    		'zooming'	 						=> 'yes',
    		'parent_click' 						=> 'no',
    		'menu_type' 						=> 'default',
    		'custom_menu_top' 					=> '0',
    		'menu_symbol_pos' 					=> 'left',
    		'custom_menu_left' 					=> '0',
    		'custom_menu_bg_color' 				=> '#CCCCCC',
    		'menu_icon_animation' 				=> 'hamburger--slider',
    		'slide_type' 						=> 'bodyslide',
    		'position' 							=> 'left',
    		'from_width' 						       => '1300',
    		'how_wide' 							            => '80',
    		'menu_max_width' 					        => '400',
    		'menu_title_size' 					       => '20',
    		'menu_title_weight' 				        => 'normal',
    		'menu_font_size' 					        => '15',
    		'menu_font_weight' 					       => 'normal',
    		'menu_font_text_type' 				     => 'uppercase',
    		'submenu_alignment' 				       => 'left',
    		'sub_menu_font_size' 				       => '15',
    		'sub_menu_font_weight' 				     => 'normal',
    		'sub_menu_font_text_type' 			   => 'uppercase',
    		'cart_contents_bubble_text_size'   => '12',
    		'menu_border_bottom_show' 			   => 'yes',
    		'menu_border_top_opacity' 			   => '0.05',
    		'menu_border_bottom_opacity' 		   => '0.05',
    		'menu_bg' 							=> '',
    		'menu_bg_size' 						=> 'cover',
    		'menu_bg_rep' 						=> 'repeat',
    		'menu_bar_bg' 						=> '',
    		'menu_bar_bg_size' 					=> 'cover',
    		'menu_bar_bg_rep' 					=> 'repeat',
    		'order_menu_items' 					=> '',
    		'bar_bgd' 							=> '#0D0D0D',
    		'bar_color' 						=> '#F2F2F2',
    		'menu_bgd' 							=> '#2E2E2E',
    		'menu_color' 						=> '#CFCFCF',
    		'menu_color_hover' 					=> '#606060',
    		'menu_textovrbgd' 					=> '#d53f3f',
    		'active_menu_color' 				=> '#FFFFFF',
    		'active_menu_bg_color' 				=> '#d53f3f',
    		'menu_icon_color' 					=> '#FFFFFF',
    		'menu_icon_hover_color' 			=> '#FFFFFF',
    		'menu_border_top' 					=> '#0D0D0D',
    		'menu_border_bottom' 				=> '#131212',
    		'social_icon_color' 				=> '#FFFFFF',
    		'social_icon_hover_color' 			=> '#FFFFFF',
    		'search_icon_color' 				=> '#FFFFFF',
    		'search_icon_hover_color' 			=> '#FFFFFF',
    		'google_font_type' 					=> 'standard',
    		'google_font_family' 				=> 'Arial, Helvetica, sans-serif',
    		'google_web_font_family' 			=> 'ABeeZee',
    		'menu_icon_type' 					=> 'default',
    		'custom_menu_font_size' 			=> '40',
    		'custom_menu_icon_top' 				=> '-7',
    		'social_icon_font_size' 			=> '16'
	  )
);

$genesis_settings = array(
    'genesis-vestige'           => array(
        'nav_superfish'         => 1,
        'nav_home'              => 1,
        'subnav_superfish'      => 1,
        'subnav_home'           => 1
    ),
    'genesis-settings'          => get_option( 'genesis-settings' ),
    'theme_mods_' . $this->website['stylesheet']  => array( 
        'nav_menu_locations'    => array(
            'primary'           => $this->menu_ids['main']
        ),
        'custom_css_post_id'    => -1
    )
);



$sidebars_widgets = array(
  	'sidebars_widgets' => array(
    	'wp_inactive_widgets' => array(),
	    'header-right' => array(
	    	  0 => 'ifound_broker_logo-2',
	    ),
	    'sidebar' => array(
	      	0 => 'ifound_quick_search-2',
	      	1 => 'categories-2',
	    ),
	    'before-header-1' => array(
	      	0 => 'custom_html-3',
	    ),
	    'before-header-2' => array(
	      	0 => 'ifound_social_media-2',
	    ),
	    'home-slider' => array(
	      	0 => 'ifound_slider_widget-2',
	      	1 => 'featured-page-11',
	      	2 => 'featured-page-12',
	    ),
	    'home-search' => array(
	      	0 => 'custom_html-10',
	      	1 => 'ifound_quick_search-3',
	    ),
	    'home-section-2' => array(
	      	0 => 'ifound_featured_listings-2',
	    ),
	    'home-section-3' => array(
	      	0 => 'featured-page-2',
	      	1 => 'featured-page-3',
	      	2 => 'featured-page-4',
	    ),
	    'home-section-4' => array(
	      	0 => 'custom_html-7',
	      	1 => 'featured-page-5',
	      	2 => 'featured-page-6',
	      	3 => 'featured-page-7',
	      	4 => 'featured-page-8',
	      	5 => 'featured-page-9',
	     	6 => 'featured-page-10',
	    ),
	    'home-section-4-1' => array(),
	    'home-section-4-2-left' => array(
	      	0 => 'custom_html-8',
	    ),
	    'home-section-4-2-right' => array(
	      	0 => 'featured-post-2',
	    ),
	    'home-section-5' => array(
	      	0 => 'custom_html-9',
	    ),
	    'footer-1' => array(
	      	0 => 'nav_menu-2',
	    ),
	    'footer-2' => array(
	      	0 => 'recent-posts-2',
	    ),
	    'footer-3' => array(
	      	0 => 'text-11',
	      	1 => 'ifound_social_media-3',
	    ),
	    'after-entry' => array(),
	    'details-before-slider' => array(
	        0 => 'ifound_whats_my_payment-2',
	    ),
	    'details-after-slider' => array(
	        0 => 'ifound_save_this_property-2',
	    ),
	    'after-details' => array(
	        0 => 'ifound_featured_listings-3',
	        1 => 'ifound_text_me_now-3',
	        2 => 'ifound_call_me_now-2',
	    ),
	    'search-results-before-criteria' => array(),
	    'search-results-after-criteria' => array(),
	    'search-results-after-map' => array(
	        0 => 'ifound_save_this_search-2',
	    ),
	    'search-results-after-results' => array(),
	    'mobile-phone-me-now' => array(
		    0 => 'ifound_call_me_now-3',
		    1 => 'ifound_text_me_now-3',
	    ),
	    'array_version' => 3,
  	)
);



$widgets = array(
  	'widget_archives' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_calendar' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_categories' => array(
    	2 => array(
      		'title' => '',
      		'count' => 0,
      		'hierarchical' => 0,
      		'dropdown' => 0,
    	),
    	'_multiwidget' => 1,
  	),
  	'widget_featured-page' => array(
	    2 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids['listing-search'],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    3 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids['whats-my-home-worth'],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'content_limit' => '',
	     	 'more_text' => '',
	    ),
	    4 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids['contact'],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    5 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[1]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    6 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[2]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    7 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[3]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    8 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[4]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    9 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[5]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    10 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids[$this->page_slug[6]],
	      	'show_image' => '1',
	      	'image_size' => 'featured',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    11 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids['for-buyers'],
	      	'show_image' => '1',
	      	'image_size' => 'home-cta',
	      	'image_alignment' => 'alignnone',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    12 => array(
	      	'title' => '',
	      	'page_id' => $this->post_ids['for-sellers'],
	      	'show_image' => '1',
	      	'image_size' => 'home-cta',
	      	'image_alignment' => 'alignnone',
	      	'content_limit' => '',
	      	'more_text' => '',
	    ),
	    '_multiwidget' => 1,
  	),
  	'widget_featured-post' => array(
    	2 => array(
      		'title' => 'Latest Real Estate News',
	      	'posts_cat' => '0',
	      	'posts_num' => 4,
	      	'posts_offset' => '0',
	      	'orderby' => 'date',
	      	'order' => 'DESC',
	      	'gravatar_size' => '45',
	      	'gravatar_alignment' => 'alignnone',
	      	'image_size' => 'thumbnail',
	      	'image_alignment' => 'alignnone',
	      	'show_title' => '1',
	      	'post_info' => '[post_date] By [post_author_posts_link] [post_comments]',
	      	'show_content' => 'content-limit',
	      	'content_limit' => '210',
	      	'more_text' => 'Read More',
	      	'extra_title' => '',
	      	'extra_num' => '',
	      	'more_from_category_text' => 'More Posts from this Category',
    	),
    	'_multiwidget' => 1,
  	),
  	'widget_gform_widget' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_meta' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_slider_widget' => array(
    	2 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_monarchwidget' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_nav_menu' => array(
    	2 => array(
      	'title' => 'Communities',
      	'nav_menu' => $this->menu_ids['communities'],
    	),
    	'_multiwidget' => 1,
  	),
  	'widget_pages' => array(
    	'_multiwidget' => 1,
  	), 
  	'widget_recent-comments' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_recent-posts' => array(
    	2 => array(
      		'title' => 'Real Estate News',
      		'number' => 3,
      		'show_date' => false,
    	),
    	3 => array(
      		'title' => '',
      		'number' => 5,
      		'show_date' => false,
    	),
    	'_multiwidget' => 1,
  	),
  	'widget_rss' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_search' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_tag_cloud' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_custom_html' => array(
    	1 => array(''),
    	3 => array(
      		'title' => '',
      		'content' => $this->header_phone(),
    	),
    	7 => array(
      		'title' => 'Featured Communities',
      		'content' => '',
    	),
    	8 => array(
      		'title' => 'Featured Tour',
      		'content' => 'https://www.youtube.com/watch?v=tsqJq8KtUeM',
    	),
    	9 => array(
      		'title' => '',
      		'content' => $this->home_worth_tagline(),
    	),
    	10 => array(
      		'title' => 'Search For Homes',
      		'content' => '',
    	),
    	11 => array(
      		'title' => '',
      		'content' => $this->contact_info(),
    	),
    	'_multiwidget' => 1,
  	),
  	'widget_user-profile' => array(
    	'_multiwidget' => 1,
  	),
    'widget_ifound_broker_logo' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
  	'widget_ifound_cmc_form' => array(
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_featured_listings' => array(
    	2 => array(''),
    	3 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_quick_search' => array(
    	2 => array(''),
    	3 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_save_this_property' => array(
    	2 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_save_this_search' => array(
    	2 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_search_nearby' => array(
    	2 => array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_text_me_now' => array(
	    2 => array(''),
    	3 => array(''),
    	'_multiwidget' => 1,
  	),
    'widget_ifound_call_me_now' => array(
	    2 => array(''),
	    3 => array(''),
	    '_multiwidget' => 1,
    ),
  	'widget_ifound_whats_my_payment' => array(
    	2 =>  array(''),
    	'_multiwidget' => 1,
  	),
  	'widget_ifound_social_media' => array(
    	2 =>  array(''),
    	3 =>  array(''),
    	'_multiwidget' => 1,
  	)
);


$theme_settings = array_merge( $wprmenu_options, $genesis_settings, $sidebars_widgets, $widgets );
