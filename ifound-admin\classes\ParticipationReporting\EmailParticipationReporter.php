<?php

require_once(__DIR__ . '/ParticipationReporter.php');

abstract class EmailParticipationReporter extends ParticipationReporter {
	protected static $internal_mls_display_name;
	protected static $to_email;
	protected static $email_subject;
	protected static $email_message;
	protected static $filename_date_format_string = 'Y-m-d_H-i-s';

	abstract protected function generate_participation_report();

	private function make_filename($date_string) {
		$filename = "ifoundagent_{$this->mls_name}_subscriber_report_{$date_string}.csv";
		return $filename;
	}

	public function submit(): string {
		if ($this->client->get_config()['environment'] !== 'production') {
			$dev_success_markup = <<<CONTENT
			<div class="notice notice-success">
				The <span style="font-weight: bold;">NON-PRODUCTION</span> report was not really submitted. If you have
				your email intercepted in dev, you can comment this out to let the submission go through.
			</div>
CONTENT;
			return $dev_success_markup;
		}
		$error_markup = <<<CONTENT
			<div class="notice notice-error">
				An error occurred. See the history.
			</div>
CONTENT;

		$report_data = $this->generate_participation_report();
		$wp_upload_dir = wp_upload_dir();
		$date_string = date(static::$filename_date_format_string);
		$filename = $this->make_filename($date_string);
		$path = $wp_upload_dir['path'] . '/' . $filename;
		$file_contents = $this->array2csv($report_data);
		$result = file_put_contents($path, $file_contents);
		$headers = [
			'Content-type: text/plain; charset=UTF-8',
			'From: iFoundAgent Support <<EMAIL>>',
		];
		$attachments = [$path];
		$email_success = wp_mail(static::$to_email, static::$email_subject, static::$email_message, $headers,
			$attachments);
		$result = [
			'email_success' => $email_success,
			'csv_filepath' => $path,
		];
		$this->capture_report_result($result);
		if (!$email_success) {
			return $error_markup;
		}

		$success_markup = <<<CONTENT
			<div class="notice notice-success">
				The report was submitted successfully. See the history.
			</div>
CONTENT;
		return $success_markup;
	}

	// I got this initially from https://stackoverflow.com/a/53882337/135101
	private function array2csv($data, $include_headers = true, $delimiter = ',', $enclosure = '"', $escape_char = "\\") {
		$f = fopen('php://memory', 'r+');
		if ($include_headers && count($data)) {
			$header = array_keys($data[0]);
			fputcsv($f, $header, $delimiter, $enclosure, $escape_char);
		}
		foreach ($data as $item) {
			fputcsv($f, $item, $delimiter, $enclosure, $escape_char);
		}
		rewind($f);
		return stream_get_contents($f);
	}

	public function print_report_preview(): void {
		$report_data = $this->generate_participation_report($this->mls_name);
		$report_string = $this->array2csv($report_data);

		?>
		<h1><?= static::$internal_mls_display_name ?> Report Preview</h1>
		<div>Here's a preview of what we'd send if you hit this button right now.</div>
		<div>This report contains <?= count($report_data) ?> items (clients/agents). They are
			sorted by first name.</div>
		<textarea cols="100" rows="25" style="font-family: monospace;"><?= $report_string ?></textarea>
		<form method="POST">
			<input type="hidden" name="mls_name" value="<?= $this->mls_name ?>">
			<input type="submit" class="button button-primary button-large" name="send_participation_report" value="Send this report to the MLS">
		</form>
		<?php
	}

	protected function is_history_record_success($record): bool {
		return $record['email_success'];
	}

	protected function get_result_from_history_record($record): array {
		return ['csv_filepath' => $record['csv_filepath']];
	}
}
