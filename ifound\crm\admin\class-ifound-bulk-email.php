<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

class iFoundBulkEmail extends iFoundCrm{
	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_filter('handle_bulk_actions-edit-contacts', array($this, 'handle_bulk_actions_edit_contacts'), 10, 3);
		add_filter('handle_bulk_actions-edit-private_contact', array($this, 'handle_bulk_actions_edit_contacts'), 10, 3);
		// This is my attempt to highlight this page's parent menu item, even though this page is hidden from the menu.
		// It does highlight the 'Contacts' submenu, but the Contact Manager menu doesn't show its submenus, so it's kind of worthless.
		// I got it from https://wordpress.stackexchange.com/a/203156/27896
		add_filter('submenu_file', function ($submenu_file) {
			$screen = get_current_screen();
			if ($screen->id === 'admin_page_bulk_email') {
				$submenu_file = apply_filters('ifound_crm_menu', null);
			}
			return $submenu_file;
		});
		add_action('ifound_bulk_email_page', array($this, 'bulk_email_page'));
		add_action('admin_post_ifound_bulk_email', array($this, 'admin_post_ifound_bulk_email'));
		add_action('admin_notices', array($this, 'my_bulk_action_admin_notice'));
	}

	public function handle_bulk_actions_edit_contacts($redirect, $doaction, $object_ids) {
		if ($doaction === 'ifound_bulk_email') {
			$redirect = menu_page_url('bulk_email');
			$redirect = add_query_arg('contact_ids', $object_ids, $redirect);
		}
		return $redirect;
	}

	private function bulk_email_page_done() {
		?>
		<div>
			Return to <a href="<?= admin_url('edit.php?post_type=contacts') ?>">contacts</a> page
		</div>
		<?php
	}

	private function bulk_email_page_start() {
		if (!apply_filters('ifound_has_feature', 'bulk-campaigns')) {
			do_action('ifound_warn_feature', 'bulk-campaigns');
			return;
		}

		wp_enqueue_script( 'email_editor_js' );
		$contacts = apply_filters('ifound_get_contacts_from_request', $_GET['contact_ids']);
		$crm = iFoundCrm::new_hookless()->crm();
		?>
		You are sending emails to the following <?= count($contacts) ?> contacts:
		<div class="contacts">
			<table>
				<?php foreach ($contacts as $contact) : ?>
					<tr>
						<td><?= $contact['name'] ?></td>
						<td><?= $contact['email'] ?></td>
						<td><?= $contact['address'] ?></td>
					</tr>
				<?php endforeach ?>
			</table>
		</div>
		<form id="bulk-email-form" action="<?= admin_url('admin-post.php') ?>" method="POST">
			<input type="hidden" name="action" value="ifound_bulk_email">
			<? wp_nonce_field('crm_action', 'crm_nonce_field'); ?>
			<?php foreach ($contacts as $contact) : ?>
				<input type="hidden" name="contact_ids[]" value="<?= $contact['id'] ?>">
			<?php endforeach ?>
			<table class="form-table">
				<tbody>
				<tr>
					<td class="label">Email header template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'header',
								false, [], 'header_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email subject/body template</td>
					<td>
						<div class="edit-email-wrapper">
							<? do_action('ifound_email_dropdown', '', 'bulk-email', false, []); ?>
						</div>
                        <div>Pick a <em>Bulk Email</em> template to default the email subject and body below, or write your own</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email subject</td>
					<td>
						<input type="text" id="email-subject" name="custom_subject" placeholder="Email Subject"
						       size="70"/>
					</td>
				</tr>
				<tr>
					<td class="label">Email body</td>
					<td>
						You can use the merge tags in the subject or body:
						<?php
						$email_tags = apply_filters('ifound_default_email_tags', []);
						foreach(['AlertLink', 'ContactLink'] as $tag_name) {
							unset($email_tags[$tag_name]);
						}
						do_action('ifound_merge_tag_select', $email_tags);
						$settings = ['textarea_name' => 'custom_content', 'wpautop' => false];
						wp_editor('', 'ifound_email_editor', $settings);
						?>
					</td>
				</tr>
				<tr>
					<td class="label">Email signature template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'signature',
								$crm->signature, [], 'signature_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				<tr>
					<td class="label">Email footer template</td>
					<td>
						<div>
							<? do_action('ifound_email_dropdown', '', 'footer',
								$crm->footer, [], 'footer_template_id', 'None'); ?>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
			<button type="submit" class="button button-primary" value="bulk_email">Send emails</button>
		</form>
		<?php
	}

	public function bulk_email_page() {
		?>
		<div class="ifound_wrap bulk-email-page">
			<h1>Send Emails</h1>
			<?
			if ($_GET['page'] === 'bulk_email' && $_GET['done'] === 'true') {
				$this->bulk_email_page_done();
			} else {
				$this->bulk_email_page_start();
			} ?>
		</div>
		<?php
	}

	public function admin_post_ifound_bulk_email() {
		// PHP auto escapes input. We don't want that. Fix it.
		// https://stackoverflow.com/a/33604648/135101
		$post_values = array_map('stripslashes_deep', $_POST);
		$contacts = apply_filters('ifound_get_contacts_from_request', $post_values['contact_ids']);
		foreach ($contacts as $contact) {
			do_action('ifound_bulk_email', $contact['id'], $post_values['custom_subject'],
				$post_values['custom_content'], $post_values['header_template_id'], $post_values['signature_template_id'],
				$post_values['footer_template_id']);
		}
		$url = admin_url('admin.php?page=bulk_email');
		$url = add_query_arg('done', 'true', $url);
		wp_redirect($url);
	}

	public function my_bulk_action_admin_notice() {
		if (!($_GET['page'] === 'bulk_email' && $_GET['done'] === 'true')) {
			return;
		}

		?>
		<div class="notice notice-success is-dismissible pad-me">Sent emails</div>
		<?php
	}

}
