jQuery(document).ready(function($) {
  window.iFoundGlobal.loadGoogleMaps().then(go);

  function go() {
    var map = getMap(polyjson.height, polyjson.zoom, polyjson.center);
    var bounds = new google.maps.LatLngBounds();
    if(typeof polyjson.polygons !== 'undefined' && polyjson.polygons) {
      for(var polygonMeta of polyjson.polygons) {
        var path = createPathArray(polygonMeta.polypath);
        var polygon = new google.maps.Polygon({
          'strokeColor': (typeof polygonMeta.bordercolor === 'undefined' ? polygonMeta.polycolor : polygonMeta.bordercolor),
          'strokeOpacity': 0.8,
          'strokeWeight': 2,
          'fillOpacity': 0.35,
          'fillColor': polygonMeta.polycolor,
          'editable': false,
          'draggable': false,
          'paths': path,
          'map': map
        });
        polygon.polyName = polygonMeta.polyname;
        polygon.polyLink = polygonMeta.polylink;
        polygon.labelCoord = polygonMeta.labelcoord;
        polygon.polyLabel = getPolyLabel(polygon);
        polygon.polyColor = polygonMeta.polycolor;
        polygon.borderColor = polygonMeta.bordercolor;

        polygon.getPath().forEach(function(coord) {
          bounds.extend(coord);
        });

        google.maps.event.addListener(polygon, 'click', function() {
          window.location.href = this.polyLink;
        });

        google.maps.event.addListener(polygon, 'mouseover', function() {
          this.setOptions({'fillOpacity' : 0.7});
        });

        google.maps.event.addListener(polygon, 'mouseout', function() {
          this.setOptions({'fillOpacity' : 0.35});
        });
      }
    }

    if(typeof polyjson.labels !== 'undefined' && polyjson.labels) {
      for(var labelMeta of polyjson.labels) {
        var label = getLabel(labelMeta);
        label.open(map);
        label.labelLink = labelMeta.labellink;
        label.labelName = labelMeta.labelname;
        label.labelColor = labelMeta.labelcolor;
        bounds.extend(label.getPosition());
      }
    }

    map.fitBounds(bounds);
    if ((typeof polyjson.zoom !== 'undefined' && polyjson.zoom)
        && map.setZoom(Number(polyjson.zoom)));
  }

  // TODO: Move to util file, all of it
  // this is temp
  function getMap(height, zoom, center) {
    var mapOpts = getMapOptions();

    var div = document.createElement('div');
    div.id = 'ifa_polygon_map';
    if(typeof height !== 'undefined' && height)
      div.style.height = height;
    else
      div.style.height = '65rem';
    if(document.getElementsByTagName('body')[0].classList.contains('home')) {
      var pElem = document.querySelector('script.map-drawing-tools').parentElement;
      pElem.appendChild(div);
    } else {
      var pElem = document.querySelector('script.map-drawing-tools');
      $(div).insertAfter(pElem);
    }

    var map = new google.maps.Map(div, mapOpts);
    if(typeof zoom !== 'undefined' && zoom)
      map.setZoom(Number(zoom));

    // Center format: lat, lng
    if(typeof center !== 'undefined' && center) {
      try {
        var [lat, lng] = center.replace(/,/g, '').split(' ');
        map.setCenter(new google.maps.LatLng({'lat': Number(lat), 'lng': Number(lng)}));
      } catch(e) {
        console.error('Error setting the custom map center point... Reverting to default');
      }
    }
    return map;
  }

  function getMapOptions() {
      /******
        VAR STYLES
          ******/
      var styles = [
        {
          'featureType': 'all',
          'stylers': [
              {
                'saturation': 0,
              }
           ]
        },
        {
          'featureType': 'road.arterial',
          'elementType': 'geometry',
          'stylers': [
            {
              'hue': '#c0c0c0'
            },
            {
              'saturation': 20
            }
          ]
        }
      ];
      //\\//\\///\\//\\//\\//\\
     //|||||| End STYLES ||||||\
    //\\//\\//\\//\\//\\//\\//\\\

    return {
      // TODO: This about this one
      'center': new google.maps.LatLng(ifound_load_map.geo.center_lat, ifound_load_map.geo.center_lng),
      'styles': styles,
      'zoom': ifound_load_map.geo.zoom,
      ...window.iFoundGlobal.sharedGoogleMapBaseOptions,
    };
  }
  //// <!> End get Map options <!> ///

  // TODO: Change class names from legacy
  // But together with CSS changes
  //

  function getLabel(labelObj) {
    var label = new google.maps.InfoWindow();

    // We used to use label.setContent() here by passing a string of HTML. However, that forces an outer div around the
    // content that we pass. As in, we can't style that outer div by doing it that way. But if we use setContent and
    // pass a DOM element directly, then we can truly control all the content, including putting a class name on an
    // outer div. So we do that, and allow for a class name on the label.
    var div = document.createElement('div');
    if (labelObj.labelclass) {
      div.className = labelObj.labelclass;
    }
    var linkText = document.createTextNode(labelObj.labelname);
    if(labelObj.polylink == '')
      div.appendChild(linkText);
    else {
      var a = document.createElement('a');
      a.appendChild(linkText);
      a.href = labelObj.labellink;
      div.appendChild(a);
    }
    label.setContent(div);

    label.setPosition(getCoords(labelObj.labelcoord));
    return label;
  }

  function getPolyLabel(polygon) {
    var label = new google.maps.InfoWindow();

    if(polygon.polyLink == '')
      label.setContent(polygon.polyName);
    else
      label.setContent('<a href="' + polygon.polyLink + '">' + polygon.polyName + '</a>');

    if(typeof polygon.labelCoord === 'undefined' || polygon.labelCoord.includes('undefined')) {
      label.setPosition(getPolyCenter(getFattestRect(polygon)));
    } else {
      var coord = getCoords(polygon.labelCoord);
      label.setPosition({'lat': coord.lat, 'lng': coord.lng});
    }

    label.open(polygon.map);
    return label;
  }
  // Move to that utils file too
  function createPathArray(coordStr) {
    var coords = coordStr.match(/(\(.*?\))/g);
    var path = [];
    for(var coord of coords) {
      path.push(getCoords(coord));
    }
    return path;
  }

  function getCoords(coord) {
      var regArray = coord.match(/(-|)(\d+\.\d*)/g);
      return {
          'lat': Number(regArray[0]),
          'lng': Number(regArray[1])
      };
  }

  function getFattestRect(polygon) {
    var mvcPaths = polygon.getPath().getArray();
    var paths = [];
    // Weird API ugh
    mvcPaths.forEach(function(point) {
      paths.push({
        'lat': point.lat(),
        'lng': point.lng()
      });
    });

    // find north east
    // Use slice() so not to modify the paths array
    var latArray = paths.map(function(path) {
      return path.lat;
    });
    var lngArray = paths.map(function(path) {
      return path.lng;
    });

    var n = Math.max.apply(null, latArray);
    var w = Math.max.apply(null, lngArray);
    var s = Math.min.apply(null, latArray);
    var e = Math.min.apply(null, lngArray);

    // Starting position: North West
    var points = {
      'nw': {
        'lat': n,
        'lng': w
      },
      'ne': {
        'lat': n,
        'lng': e
      },
      'se': {
        'lat': s,
        'lng': e
      },
      'sw': {
         'lat': s,
         'lng': w
      }
    };

    return scanAndFind(points, paths);
  }

  function scanAndFind(points, paths) {
    // The lower the rate, the more accurate the estimation is
    // but the slower the calculation rate.

    //TODO: change rate mechanism
    var partitionSize = 100;
    var scanRate = getScanRate(partitionSize, points);

    var rows = [];
    for(var i = points.nw.lat; i > points.sw.lat; i-=scanRate) {
      var row = [];
      for(var j = points.nw.lng; j > points.ne.lng; j-=scanRate) {
        var lIndex = row.push({
          'lat': i,
          'lng': j,
          'isWithin': isWithinPolygon({'lat': i, 'lng': j}, paths)
        }) - 1;
      }
      rows.push(row);
    }

    var rectangle = findBiggestRect(rows);
    return rectangle;
  }

  function getScanRate(pSize, pts) {
    var { ne, se, sw, nw } = pts;
    var longestBoundingLine = (function(points) {
      var lines = [];
      for(var i = 0; i < points.length - 2; i++) {
        if(i % 2 === 0 && lines.push(Math.abs(points[i].lat - points[i+1].lat)));
        else lines.push(Math.abs(points[i].lng - points[i+1].lng));
      }
      return Math.max.apply(null, lines);
    })([ne, se, sw, nw]);

    return longestBoundingLine / pSize;
  }

  function isWithinPolygon(point, paths) {
    var numOfCrossings = 0;
    for(var i = 0; i < paths.length; i++) {
      var vertex1 = paths[i];
      var vertex2 = paths[(i+1) % paths.length];
      if(westOf(vertex1, vertex2, point))
        ++numOfCrossings;
    }

    function westOf(v1, v2, p) {
      if(v1.lat <= v2.lat) {
        if(p.lat <= v1.lat || p.lat > v2.lat || p.lng >= v1.lng && p.lng >= v2.lng)
          return false;
        else if(p.lng < v1.lng && p.lng < v2.lng)
          return true;
        else
          return (p.lat - v1.lat) / (p.lng - v1.lng) > (v2.lat - v1.lat) / (v2.lng - v1.lng);
      } else return westOf(v2, v1, p);
    }

    return numOfCrossings % 2;
  }

  function findBiggestRect(rows) {
    var maxRatio = 5/2;

    var rects = [];
    for(var i = 0; i < rows.length - 1; i++) {
      for(var j = 0; j < rows[i].length - 1; j++) {
        var rect = {
          'nw': rows[i][j],
          'ne': rows[i][j],
          'sw': rows[i][j],
          'se': rows[i][j]
        }
        while(typeof rows[i][++j] !== 'undefined' && rows[i][j].isWithin) {
          rect.ne = rows[i][j];
          var index = i;
          while(typeof rows[++index] !== 'undefined' &&  rows[index][j].isWithin) {
            rect.sw = rows[index][j];
          }
        }
        rects.push(rect);
      }
    }

    return rects.reduce(function(max, rect) {
      var width = Math.abs(rect.nw.lng - rect.ne.lng);
      var len = Math.abs(rect.nw.lat - rect.sw.lat);
      var area = len * width;
      if(area > max.area) {
        max = {
          'rect': rect,
          'area': area
        };
      }
      return max;
     }, {'rect': rects[0], 'area': 0}).rect;
  }

  function getPolyCenter(polygon) {
    var vert = polygon.ne.lng + Math.abs((polygon.nw.lng - polygon.ne.lng) / 2);
    var hor = polygon.sw.lat + Math.abs((polygon.nw.lat - polygon.sw.lat) / 2);
    return { 'lng': vert, 'lat': hor };
  }
});
