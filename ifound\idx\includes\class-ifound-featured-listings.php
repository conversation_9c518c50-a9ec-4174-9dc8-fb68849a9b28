<?
/**
 * iFOUND_featured class
 *
 * Display MLS search results pages.
 *
 * @package iFOUND
 * @since 1.0.7
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundFeaturedListings extends iFoundIdx {
	use NewHooklessTrait;

	/**
	 * init iFOUND_featured class.
	 *
	 * @since 1.0.7
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.7
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('ifound_featured_listings', array($this, 'featured_listings'), 10, 1);
		}
	}

	private function get_featured_listing_body() {
		$body = <<<EOT
<div class="featured-listing {Classes}">
	<div class="ifound-wrap">
		<div class="ifound-featured-image">
			<div class="ifound-wrap" style="position: relative;">
				<a href="{DetailsURL}">
					<img class="ifound-featured-img" src="{Image}">
				</a>
				{VirtualTourSection}
				{OpenHouse}
			</div>
		</div>
		<div class="ifound-featured-data">
			<div class="ifound-wrap">
				<h2><a href="{DetailsURL}">{results_h2}</a></h2>
				<div class="results-status">
					<div class="status-wrap">{Status}</div>
				</div>
				{results_content}
			</div>
		</div>
	</div>
</div>
EOT;
		return $body;
	}

		/**
	 * Featured Listings
	 *
	 * Show featured listings.
	 *
	 * @since 1.0.0
	 * @since 2.1.4 Re-define $featured_listings var.
	 * @since 2.3.0 Move backup request here.
	 * @since 2.5.14 Add Status filter.
	 * @since 2.5.37 Save in transient.
	 * @since 2.6.0 Define DOING_FEATURED_LISTINGS
	 */

	public function featured_listings( $input ) {

		define( 'DOING_FEATURED_LISTINGS', true );

		wp_enqueue_style( 'featured_listings_css' );
		wp_enqueue_style( 'slick_css' );

		wp_enqueue_script( 'slick_js' );
		wp_enqueue_script( 'jquery-migrate' );

		$transient = 'ifound_featured_listings';

		$featured_listings = get_transient( $transient );

		if( $featured_listings ) {

			echo $featured_listings;

			return;

		}

		parse_str( html_entity_decode( $input['featured_query'] ), $query );

		$query['max_results'] = intval( $input['featured_qty'] ) ?: 10;

		if( ! empty( $input['featured_bu_query'] ) ) {

			parse_str( html_entity_decode( $input['featured_bu_query'] ), $backup_query );
			$backup_query = empty( $backup_query ) ? false : $backup_query;

		}

		$results = $this->process_input( $query );

		if( $backup_query && empty( $results->listings ) ) {

			$results = $this->process_input( $backup_query );

		}

		if( ! empty( $results->listings ) ) {
			$html_body = $this->get_featured_listing_body();
			$extra = ['mls_class' => $query['mls_class']];

			ob_start();

			foreach( $results->listings as $listing ) {

				do_action( 'ifound_single_listing', $html_body, $listing, $extra );

			}

			$featured_listings = ob_get_clean();

			echo $featured_listings;

			set_transient( $transient, $featured_listings, 1 * HOUR_IN_SECONDS );

		}

	}

	// I'm not sure how some of these queries got messed up, but I'm seeing them on many different sites. They are
	// showing up as a warning of "Unrecognized query argument: list_status_" (for example). Let's fix them.
	public function maybe_fix_queries($input) {
		$needs_to_write_option_back = false;

		// Example bad featured_query:
		//   abc_=123&def =456&list_pricemin=200000&list_pricemax=800000&open_house=Y&list_status%5B0%5D=active
		// Example bad featured_bu_query:
		//   list_pricemin=200000
		// Note 1 (referenced below):
		//   http_build_query() replaces [ with %5B, and ] with %5D. However, the value passed to update_option()
		//   is expected not to be SQL encoded. So, we hack around this by initially using LEFT_BRACKET and
		//   RIGHT_BRACKET before http_build_query() is called, and then replace themselves after.
		$query_keys = ['featured_query', 'featured_bu_query'];
		foreach ($query_keys as $query_key) {
			if (isset($input[$query_key])) {
				parse_str($input[$query_key], $qs);
				foreach ($qs as $key => $value) {
					$ends_in_numbers_pattern = '/(\D+)(\d+)/';
					if (strlen($key) > 0) {
						$last_char = $key[strlen($key) - 1];
						if (in_array($last_char, [' ', '_'])) {
							unset($qs[$key]);
							$new_key = substr($key, 0, -1);
							$qs[$new_key] = $value;
							$needs_to_write_option_back = true;
						} else if (preg_match($ends_in_numbers_pattern, $key)) {
							unset($qs[$key]);
							// See Note 1 above about LEFT_BRACKET, RIGHT_BRACKET
							$new_key = preg_replace($ends_in_numbers_pattern,
								// '$1LEFT_BRACKETRIGHT_BRACKET', $key);
								'$1[]', $key);
							$qs[$new_key] = $value;
							$needs_to_write_option_back = true;
						}
					}
					if (in_array($key, ['list_pricemin', 'list_pricemax'])) {
						unset($qs[$key]);
						$new_key = preg_replace('/((min)|(max))$/', '_$1', $key);
						$qs[$new_key] = $value;
						$needs_to_write_option_back = true;
					}
					if (in_array($key, ['price_min', 'price_max'])) {
						unset($qs[$key]);
						$new_key = 'list_' . $key;
						$qs[$new_key] = $value;
						$needs_to_write_option_back = true;
					}
					if ($key === 'property_type') {
						unset($qs[$key]);
						$new_key = 'prop_type';
						$qs[$new_key] = $value;
						$needs_to_write_option_back = true;
					}
				}
				$new_qs = http_build_query($qs);
				// See Note 1 above about LEFT_BRACKET, RIGHT_BRACKET
				$new_qs = str_replace('LEFT_BRACKET', '[', $new_qs);
				$new_qs = str_replace('RIGHT_BRACKET', ']', $new_qs);
				$input[$query_key] = $new_qs;
			}
		}

		if ($needs_to_write_option_back) {
			update_option('ifound_featured_settings', $input);
		}

		return $input;
	}
}
