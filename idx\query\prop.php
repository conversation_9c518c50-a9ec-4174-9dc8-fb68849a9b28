<?php

# New Relic custom attributes stuff
require_once('../includes/newrelic.php');

require_once('../vendor/autoload.php');

use Profound\API\PropAPIResponder;

// Only track 20% of Property Details Page requests with New Relic APM, to avoid Insights usage limits
if (extension_loaded('newrelic') && mt_rand(1, 5) > 1) {
	newrelic_ignore_transaction();
}

$propAPI = new PropAPIResponder();
$propAPI->handleRequest();