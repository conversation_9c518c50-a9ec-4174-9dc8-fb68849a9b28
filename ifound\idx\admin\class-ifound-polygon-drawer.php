<?php
if( ! defined ( 'ABSPATH' ) )
  die();

class iFoundPolygonDrawer extends iFoundIdx {
  // File path for our tiny mce plugin js file
  private $js_path;

  public static function init() {
    $class = __CLASS__;
    new $class;
  }

  public function __construct() {
    add_shortcode('ifound_polygon_maker', array($this, 'polygon_maker_shortcode'), 10, 1);
    add_action('the_post', array($this, 'pfmls_map_converter'));
    add_shortcode('pfmls_map', function() {});
    add_filter('ifound_polygon_maker', [$this, 'polygon_maker']);

    if(is_admin()) {
      $this->js_path = plugins_url('js/create-polygon.js?ver=' . iFOUND_PLUGIN_VERSION, __FILE__);
      // TODO: move to util file
      add_action('wp_ajax_get_posts_', array($this, 'get_posts_'));
      add_action('admin_head', array($this, 'mce_button'));
      add_action( 'admin_enqueue_scripts', array( $this, 'scripts' ) );
    }
  }

  public function scripts() {
    if (method_exists(get_current_screen(), 'is_block_editor') && get_current_screen()->is_block_editor()) {
      wp_register_script( 'ifound_polygon_drawing', $this->js_path, array( 'jquery', 'load_map_js' ), iFOUND_PLUGIN_VERSION );
      wp_localize_script('ifound_polygon_drawing', 'ifound_polygon_drawing', [
        'is_block_editor' => true,
      ]);
      wp_enqueue_script('ifound_polygon_drawing');
    }
  }

  /**
   * Check if a post or a page are being edited
   * Include polygon drawing button and js file if yes
   *
   * @since 5.4.4
   *
   * @param void
   * @return void
   */
  public function mce_button() {
    global $typenow;

    if(!current_user_can('edit_pages') && !current_user_can('edit_posts'))
      return;

    if(get_user_option('rich_editing')) {
      add_filter('mce_external_plugins', array($this, 'add_polygon_drawing'));
      add_filter('mce_buttons', array($this, 'register_button'));
      echo '<script>var create_polygon_endpoint = "' . admin_url('admin-ajax.php') . '";</script>';
    }
  }

  /**
   * Add the JS file to mce plugin array
   *
   * @since 5.4.4
   *
   * @param array $plugins Associative array of mce plugins
   * @return array Associative array of mce plguins
   */
  public function add_polygon_drawing($plugins) {
    $plugins['ifound_polygon_drawing'] = $this->js_path;
    return $plugins;
  }

  /**
   * Append the MCE buttons array with our button
   *
   * @since 5.4.4
   *
   * @param array $buttons. An associative arrray of mce buttons
   * @return array.An associative arrray of mce buttons
   */
  public function register_button($buttons) {
    $buttons[] = 'ifound_polygon_drawing';
    return $buttons;
  }

  // TODO: move to a serarate file containing utils methods
  public static function get_posts_() {
    $post_type = array('page');
    $posts = array();
    foreach($post_type as $type) {
      $params = array(
        'numberposts' => -1,
        'post_type' => $type
      );
      $posts = array_merge($posts, get_posts($params));
    }

    // Return the posts title and the link
    $outgoing_payload = [];
    foreach($posts as $post) {
      $outgoing_payload[] = array(
        'title' => $post->post_title,
        'link' => $post->post_name
      );
    }
    echo json_encode($outgoing_payload);
    die();
  }

  public function polygon_maker($atts) {
	  global $mls_associations;

	  $mapObjNames = ['height', 'zoom', 'center', 'polygons', 'labels'];
	  $mapObjects = [];
	  foreach($atts as $key => $val) {
		  if(in_array($key, $mapObjNames)) {
			  if($key == 'height' || $key == 'zoom' || $key == 'center')
				  $mapObjects[$key] = $val;
			  else
				  $mapObjects[$key] = $this->assembleObject($val);
		  }
	  }

	  wp_register_script('polygon_drawing_frontend_js', plugins_url('../includes/js/polygon-drawing-fe.js', __FILE__), ['load_map_js'], iFOUND_PLUGIN_VERSION);
	  wp_enqueue_script('polygon_drawing_frontend_js');

	  try {
		  $json = json_encode($mapObjects);
	  } catch(Exception $e) {
		  error_log(serialize($e));
		  $json = NULL;
	  }
	  return '<script class="map-drawing-tools">var polyjson = '. $json . ';</script>';
  }

	/**
	 * Hook this via add_shortcode
	 * Make the input into a json object
   * The front end can read and assemble the map
   * on the front end
   * @param array $atts. An associative array of atts derived from the shortcode
   * @return string. A JSON object string enclosed within the script tag
   */
  public function polygon_maker_shortcode($atts) {
    return $this->polygon_maker($atts);
  }

  /**
   * Parse the value from a map object
   * @param string $val. A string containing data for a map object
   * @return array. A string deserialized into the associatve array
   */
  private function assembleObject($val) {
    $objs = explode('object:', $val, 150);
    $objs_len = count($objs);
    if($objs_len < 2) return null;

    $deseriazlied = [];
    for($i = 1; $i < $objs_len; $i++) {
      $parts = explode(';', $objs[$i]);
      $obj = NULL;
      for($j = 0; $j < count($parts); $j++) {
        if($parts[$j] === NULL || $parts[$j] === '')
          continue;

        $parts_of_parts = explode('::', $parts[$j]);
        $obj[$parts_of_parts[0]] = $parts_of_parts[1];
      }
      $deserialized[] = $obj;
    }

    return $deserialized;
  }

  /**
   * Convert the old plugin map shortcodes into the map drawing
   * new plugin shortcode
   *
   * @return void
   */
  public function pfmls_map_converter() {
    // Let's not do this. It looks like this will completely demolish the post content if the shortcode name is
    // ProFoundMLSSearchListings, and wipe out everything but the map if the shortcode name is pfmls_map.
    return false;

    global $post;

    $pfmls_shortcode_name = 'ProFoundMLSSearchListings';
    $pfmls_alt_shortcode_name = 'pfmls_map';

    $pattern = get_shortcode_regex(array($pfmls_shortcode_name, $pfmls_alt_shortcode_name));
    $is_convertable = ((preg_match_all('/' . $pattern . '/s', $post->post_content, $matches)
                    && array_key_exists(2, $matches)
                    && in_array($pfmls_shortcode_name, $matches[2]))
                   || ((preg_match_all('/' . $pattern . '/s', $post->post_content, $matches)
                    && array_key_exists(2, $matches)
                    && in_array($pfmls_alt_shortcode_name, $matches[2]))));

    if(!$is_convertable) return false;

    switch($matches[2][0]) {
      case $pfmls_alt_shortcode_name:
        $shortcode = $this->get_pfmls_map($matches[2][0], $matches[3][0], $post->post_content);
        break;
    }

    $postarr = array(
      'ID' => $post->ID,
      'post_content' => $shortcode
    );

    wp_update_post($postarr);
  }

  /**
   * Convert the pfmls_map shortcode to the ifound_polygon_maker
   *
   * @param {string} $sc_name. Shortcode name
   * @param {string} $sc_params. Shortcode parameters
   * @param {string} $content. Content of the post
   * @return {string}. ifound_polygon_maker shorcode
   */
  private function get_pfmls_map($sc_name, $sc_params, $content) {
    // Get meta params from the shortocode first
    $params_needed = array(
      'height', 'center', 'zoom'
    );
    $params_keyval = explode(' ', $sc_params);
    $params = [];
    $len = count($params_keyval);
    for($i = 1; $i < $len; $i++) {
      foreach($params_needed as $param) {
        if(strpos($params_keyval[$i], $param) !== FALSE) {
          $params[$param] = explode('=', $params_keyval[$i])[1];
          break;
        }
      }
    }
    $params_str = '';
    foreach($params as $param => $val) {
      if($param == 'height') {
        $regex = '/"(\d*)"/';
        $replacement = '"${1}px"';
        $params_str .= $param . '=' . preg_replace($regex, $replacement, $val) . ' ';
      } else {
        $params_str .= $param . '=' . $val . ' ';
      }
    }

    $regex = '/\[pfmls_map.*?\](.*?)\[\/pfmls/s';
    preg_match_all($regex, $content, $matches);
    $sc_body = json_encode($matches[1][0]);

    $sc_body_parts = explode('\r\n', $sc_body);
    $entries = [];
    // Format of each entry ex:
    //Ahwatukee Retirement Village,/ahwatukee-retirement-village-homes-for-sale/,33.339,-111.985,marker,marker
    foreach($sc_body_parts as $entry) {
      $e_parts = explode(',', $entry);
      $p_len = count($e_parts);
      if($p_len < 6) continue;

      $entries[] = array(
        'labelname' => $e_parts[0],
        'labellink' => $e_parts[1],
        'labelcoord' => '(' . $e_parts[2] . ', ' . $e_parts[3] . ')',
        'labelcolor' => '#999'
      );
    }

    $sc_contents = 'labels="';
    foreach($entries as $entry) {
      $sc_contents .= $this->assembleFromOld($entry);
    }
    $shortcode = '[ifound_polygon_maker ' . $params_str . ' ' . $sc_contents . '" ]';
    return $shortcode;
  }

  private function assembleFromOld($entry) {
    $new_str = 'object:';
    foreach($entry as $field => $val) {
      $new_str .= $field . '::' . $val . ';';
    }
    return $new_str;
  }
}
