export default {
  // Database Configuration
  database: {
    host: 'localhost',
    port: 3306,
    user: 'your_username',
    password: 'your_password',
    database: 'your_database',
  },

  // Test Database Configuration (for integration tests)
  testDatabase: {
    host: 'localhost',
    port: 3306,
    user: 'your_test_username',
    password: 'your_test_password',
    database: 'your_test_database',
  },

  // Admin Email Configuration (array of email addresses)
  adminEmails: [
    '<EMAIL>',
    '<EMAIL>',
  ],

  // Sendgrid Configuration
  sendgrid: {
    apiKey: 'your_sendgrid_api_key',
    fromEmail: '<EMAIL>',
    fromName: 'History Tracker',
  },

  // Twilio Configuration
  twilio: {
    accountSid: 'your_account_sid',
    apiKey: 'your_twilio_api_key',
    apiSecret: 'your_twilio_api_secret',
    fromNumber: '+**********',
    adminSmsNumber: '+**********',
  },

  // MLS Systems Configuration
  // Each MLS system has its own set of tables
  mls_systems: {
    // Example MLS system
    mls1: {
      mls_classes: {
        // Residential properties
        residential: {
          property_changes_table_name: 'mls1_residential_property_changes',
          property_history_table_name: 'mls1_residential_property_history',
          data_directory: 'mls1/residential'
        },
        // Commercial properties
        commercial: {
          property_changes_table_name: 'mls1_commercial_property_changes',
          property_history_table_name: 'mls1_commercial_property_history',
          data_directory: 'mls1/commercial'
        }
      }
    },
    // Another example MLS system
    mls2: {
      mls_classes: {
        // Residential properties  
        residential: {
          property_changes_table_name: 'mls2_residential_property_changes',
          property_history_table_name: 'mls2_residential_property_history',
          data_directory: 'mls2/residential'
        },
        // Land properties
        land: {
          property_changes_table_name: 'mls2_land_property_changes', 
          property_history_table_name: 'mls2_land_property_history',
          data_directory: 'mls2/land'
        }
      }
    },
    // Test MLS system for integration tests
    test: {
      mls_classes: {
        residential: {
          property_changes_table_name: 'test_residential_property_changes',
          property_history_table_name: 'test_residential_property_history',
          data_directory: 'test/residential'
        }
      }
    }
  }
}
