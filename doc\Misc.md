# Misc

This page contains info links from the old documentation that hasn't been cleaned up, organized, removed, or updated properly yet.

## Terminology and Domain-specific knowledge

You might see references to LIST_22, LIST_75, etc. These are examples of unhelpful labels to "List Price" and "Total Taxes", respectively. The mappings of labels can be found on [RETS MD](http://www.retsmd.com). You'll need login credentials.

* http://en.wikipedia.org/wiki/Internet_Data_Exchange

The overarching domain of Pro-Found is real estate, which may be unfamiliar to developers. There is a glossary of terms that might be helpful on the FogBugz wiki. Look for the "Glossary" article.

## Dev Setup

The recommended hostnames for development are:

* **IDX Server**: `pf_idx`
* **WordPress site**: `wordpress`

Add entries to your `/etc/hosts` file for the IDX server and WordPress install:

    127.0.0.1   wordpress pf_idx

### Apache

Two virtual hosts should be configured on the system:

* **IDX Server**: pointing at the `idx` directory
* **WordPress**: pointing at an installation of WordPress

Include the following in your Apache config so that symlinks, rewrites, `.htacces` files, etc. will work:

    AllowOverride All
    Options All

### Charles

For debugging purposes, you should purchase a copy of [Charles](http://www.charlesproxy.com/).

The WordPress plugin communicates with the IDX Server using cURL.  HTTP POST requests are made, with form-encoded
variables, and JSON responses are returned.

Charles will allow you to see all communication between the plugin and the IDX server, and quickly debug issues.  It
is currently a decent way to debug the IDX server as well, as there isn't a friendly way to construct the HTTP requests
for the IDX server.  (TODO: check if GET requests will work)

## WordPress

### General Information for Developers

- [Local WordPress multisite setup notes](misc/wp_local_multisite_setup.md)
- [Automated Testing in WordPress](misc/wp_automated_testing.md)
- [Creating an API Endpoint in WordPress](misc/wp_api_endpoint.md)
- [Programmatically Configuring WordPress](misc/wp_programmatic_config.md)

### Automated WordPress Multisite Deployment

- [Implementation notes for automated WordPress site creation](misc/wp_automated_deployment_programmatic.md)
- [Initial research notes for WordPress automated site creation](misc/wp_research.md)

### WpBuilder API

- [High-level description of the WpBuilder plugin](misc/wpbuilder/WPBuilder.md)
- [Blueprint of the JSON object expected by the API](misc/wp_api_json_blueprint.md)
