import { useState } from 'react';
import useLocalStorage from './useLocalStorage';
import _get from 'lodash/get';

function useLocalStorageState(key, defaultValue) {
    const { value: iFoundLocalStorage, setKeyValue } = useLocalStorage();
    const [val, setVal] = useState(() => _get(iFoundLocalStorage, key, defaultValue));

    function save(value) {
        setKeyValue(key, value);
        setVal(value);
    }

    return [val, save];
}

export default useLocalStorageState;
