<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * iFoundSaveThisAdmin Class
 *
 * @since 3.0.0
 */

class iFoundSaveThisAdmin extends iFoundSaveThis {

	/**
	 * init iFoundSaveThisAdmin class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct() {
		// Normally our constructors do nothing but set up hooks so there is no need to call parent constructors.
		// But in this case, it does more than that so we must call it.
		parent::__construct(['enable_hooks' => false]);

		add_filter( 'parse_query', array( $this, 'filter_query' ) );
		add_action( 'restrict_manage_posts', array( $this, 'filter_dropdown' ), 20, 1 );
		add_action( 'save_post_save_this', array( $this, 'save_campaign_post' ), 10, 1 );
		add_filter( 'map_meta_cap', array( $this, 'map_meta_cap' ), 10, 4 );
		add_filter( 'manage_save_this_posts_columns' , array( $this, 'save_this_columns' ) );
		$shared_campaign_obj = iFoundSharedCampaign::new_hookless();
		add_action( 'manage_save_this_posts_custom_column' , [$shared_campaign_obj, 'contact_info_column'], 10, 2 );
		add_action( 'manage_save_this_posts_custom_column' , array( $this, 'search_column'), 10, 2 );

		add_action( 'add_meta_boxes_save_this', array( $this, 'save_this_metabox' ) );
		add_action( 'add_meta_boxes_contacts', array( $this, 'contacts_metabox' ) );
		add_action( 'add_meta_boxes_private_contact', array( $this, 'contacts_metabox' ) );

		add_action( 'admin_enqueue_scripts', array( $this, 'scripts' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'save_this_admin_scripts' ) );

		add_action( 'wp_ajax_save_alert_ajax', array( $this, 'save_alert_ajax' ) );

		add_action( 'ifound_save_this_search_metabox', array( $this, 'save_this_search_metabox' ), 10, 1 );
		add_action( 'ifound_save_this_property_metabox', array( $this, 'save_this_property_metabox' ), 10, 1 );
		add_action( 'ifound_save_this_campaign_metabox', array( $this, 'save_this_campaign_metabox' ), 10, 1 );

		add_action( 'pre_get_posts', array( $this, 'save_this_query'), 10, 1 );
	}

	public function save_this_admin_scripts(){

		wp_register_script( 'save_alert_js', plugins_url( 'js/save-alert.js', __FILE__ ), array( 'jquery', 'jquery-ui-core', 'jquery-ui-datepicker', 'ifound_shared_admin_js' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'save_alert_js', 'save_alert', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'save_alert_secure_me' ),
			'sms_campaign_templates' => iFoundSms::new_hookless()->get_sample_campaign_templates(),
		));

	}


	/**
	 *	Search Column
	 *
	 *	Search Column for the save this post type.
	 *
	 *	@since 1.2.48
	 *
	 *  @param array $column       The column for contact link.
	 *  @param int   $save_this_id The ID if the save this.
	 */

	public function search_column( $column, $save_this_id ) {
		$terms = get_the_terms(get_post(), $this->taxonomy) ?: [];
		$term_strings = array_map(function($wp_term) {
			return $wp_term->slug;
		}, $terms);
		if (array_intersect(['market-update', 'search-update', 'homeowner-campaign'], $term_strings)) {

			$label = '';
			$label .= '<a ';
			$label .= 'href="';
			$label .= $this->util()->build_post_href($save_this_id, 'view');
			$label .= '" target="_blank">';
			$label .= 'Edit Filters';
			$label .= '</a>';

		} else {

			$label = '-';

		}

		if ( $column == 'search_criteria' ) {

			echo $label;

		}

	}

	/**
	 *	Save This Columns
	 *
	 *	Add Custom Column to the save this post type.
	 *
	 *	@since 1.2.48
	 *
	 *  @param array $columns The columns for the save this.
	 */

	public function save_this_columns( $columns ) {

		$columns = array_merge(
			array( 'cb' 						=> __( 'checkall', 'ifound' ) ),
			array( 'title' 						=> __( 'Title', 'ifound' ) ),
			array( 'contact' 					=> __( 'Contact', 'ifound' ) ),
			array( 'search_criteria' 			=> __( 'Filters', 'ifound' ) ),
			array( 'taxonomy-save_type'			=> __( 'Campaign Type', 'ifound' ) ),
			array( 'taxonomy-campaign_status'	=> __( 'Campaign Status', 'ifound' ) )
		);

		return apply_filters( 'ifound_admin_columns', $columns );

	}

	/**
	 *	Contacts Metabox
	 *
	 *	Add metaboxes.
	 *
	 *	@since 1.0.8
	 *
	 *	@link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */

	public function contacts_metabox() {

		global $typenow;

		add_meta_box(
			'save_this_search_meta',
			__( '<i class="fal fa-bookmark" aria-hidden="true"></i> Saved Searches', 'ifound' ),
			array( $this, 'save_this_search_metabox'),
			$typenow,
			'advanced',
            'default'
		);

		add_meta_box(
			'save_this_property_meta',
			__( '<i class="fal fa-home" aria-hidden="true"></i> Saved Properties', 'ifound' ),
			array( $this, 'save_this_property_metabox'),
			$typenow,
			'advanced',
            'default'
		);

		add_meta_box(
			'save_this_campaign_meta',
			__( '<i class="fal fa-calendar" aria-hidden="true"></i> Email Campaigns', 'ifound' ),
			array( $this, 'save_this_campaign_metabox'),
			$typenow,
			'advanced',
            'default'
		);

	}

	/**
	 *	Save This Metabox
	 *
	 *	Add metaboxes.
	 *
	 *	@since 1.0.8
	 *
	 *	@link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */

	public function save_this_metabox() {

		add_meta_box(
			'saved_canpaigns_meta',
			__( '<i class="fal fa-database" aria-hidden="true"></i> Saved Campaign Data', 'ifound' ),
			array( $this, 'saved_canpaigns_metabox'),
			$this->post_type,
			'advanced',
            'high'
		);

	}

	/**
	 * Saved Campaigns Metabox
	 *
	 * Main metabox gor the Saved Campaigns page.
	 *
	 * @since 1.2.48
	 * @since 2.5.22 Add created date.
	 */

	public function saved_canpaigns_metabox() {

		wp_enqueue_script( 'jquery-ui-datepicker' );
		wp_enqueue_script( 'save_alert_js' );
		wp_enqueue_script( 'ifound_spa_js' );

		$save_this_id = get_the_ID();
		$campaign_link = $this->util()->build_post_href($save_this_id, 'view');
		$save_this_meta = $this->util()->get_single_metas($save_this_id, [
			'contact_id',
			iFoundSharedCampaign::$DO_EMAIL_KEY,
			iFoundSharedCampaign::$DO_SMS_KEY,
			'end_date',
			iFoundSharedCampaign::$SMS_TEMPLATE_ID_KEY,
			iFoundSharedCampaign::$TO_EMAIL_KEY,
			iFoundSharedCampaign::$TO_SMS_KEY,
		]);
		$contact_id = $save_this_meta['contact_id'];
		$contact_post = get_post($contact_id);
		$contact_name = $contact_post->post_title;
		$contact_link = admin_url("post.php?post=$contact_id&action=edit");
		$contact_meta = $this->util()->get_single_metas($contact_id, ['email', 'email2', 'email_spouse',
			'email2_spouse', 'mphone', 'mphone_spouse']);
		$to_emails = explode(',', $save_this_meta[iFoundSharedCampaign::$TO_EMAIL_KEY]);
		$emails_map = [
			'email' => $contact_meta['email'],
			'email2' => $contact_meta['email2'],
			'email_spouse' => $contact_meta['email_spouse'],
			'email2_spouse' => $contact_meta['email2_spouse'],
		];
		$do_email_checked_str = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id)
			? 'checked="checked"' : '';
		$campaign_is_enabled = has_term( 'active', $this->taxonomy2, $save_this_id );

		$doing_prop_alert = iFoundSaveThis::new_hookless()->is_property_alert($save_this_id);
		?>

		<table class="form-table notes_meta views_meta">

			<tbody>

				<tr>

					<th scope="row"><label><? _e( 'Created', 'ifound' ); ?></label></th>

					<td><? echo get_the_date( 'M d, Y', $save_this_id ); ?></td>

				</tr>

				<tr>

					<th scope="row"><label><? _e( 'Contact', 'ifound' ) ; ?></label></th>

					<td>
						<a href="<?= $contact_link ?>"><?= $contact_name?></a>
					</td>

				</tr>
				<tr>
					<th scope="row"><label><? _e( 'View campaign', 'ifound' ) ; ?></label></th>

					<td><a href="<?= $campaign_link ?>">View campaign</a></td>
				</tr>
				<tr>
					<th scope="row"><label><? _e( 'Status', 'ifound' ) ; ?></label></th>

					<td>
						<div class="ifound-campaign-status-toggle">
							Loading...
						</div>
						<script>
							jQuery(document).ready(function() {
								var domContainer = document.querySelector('.ifound-campaign-status-toggle');
								var props = {
									isPropertyAlert: <?= $doing_prop_alert ? 'true' : 'false' ?>,
									initialEnabledStatus: <?= $campaign_is_enabled ? 'true' : 'false' ?>,
								};
								window.ifound_spa.render_app('campaign_status_toggle', domContainer, props);
							});
						</script>
					</td>
				</tr>
			</tbody>
		</table>

		<div class="default-criteria-heading"><i class="fal fa-envelope" aria-hidden="true"></i> Email</div>

		<table class="form-table">
			<tbody>
				<tr>
					<th colspan="2">
						<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" id="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" value="<?= iFoundSharedCampaign::$DO_EMAIL_YES ?>"
							<?= $do_email_checked_str ?>>
						<label for="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>"><? _e( 'Include email with this search', 'ifound' ); ?></label>
					</th>
				</tr>

				<tr>
					<th scope="row"><label><? _e( 'To Email(s)', 'ifound' ) ; ?></label></th>
					<td>
						<input type="hidden" name="<?= iFoundSharedCampaign::$TO_EMAIL_KEY ?>" id="<?= iFoundSharedCampaign::$TO_EMAIL_KEY ?>" value="<?= $save_this_meta[iFoundSharedCampaign::$TO_EMAIL_KEY] ?>">
						<div class="to_emails"></div>
						<strong class="no_email_addresses_checked_warning hidden">You must check at least one email address</strong>
						<script>
							jQuery(document).ready(function($) {
								var emails_map = <?= json_encode($emails_map); ?>;
								var options = <?= json_encode(['selected_emails' => $to_emails]) ?>;
								window.ifound_populateEmailsTo(emails_map, options);
							});
						</script>
					</td>
				</tr>
				<?

				if( ! $doing_prop_alert ) { ?>

					<tr>

						<th scope="row"><label><? _e( 'Subject', 'ifound' ) ; ?></label></th>

						<td><input type="text" name="custom_subject" class="large-text code email-validate" value="<? echo get_post_meta( $save_this_id, 'custom_subject', true ); ?>"></td>

					</tr>

					<tr>

						<th scope="row"><label><? _e( 'Content', 'ifound' ) ; ?></label></th>

						<td><textarea class="large-text code email-validate" name="custom_content" rows="7"><? echo get_post_meta( $save_this_id, 'custom_content', true ); ?></textarea></td>

					</tr>

					<tr>

						<th scope="row"><label><? _e( 'Header', 'ifound' ) ; ?></label></th>

						<td><? do_action( 'ifound_email_dropdown', 'header', 'header', get_post_meta( $save_this_id, 'header', true ) ); ?></td>

					</tr>

					<tr>

						<th scope="row"><label><? _e( 'Signature', 'ifound' ) ; ?></label></th>

						<td><? do_action( 'ifound_email_dropdown', 'signature', 'signature', get_post_meta( $save_this_id, 'signature', true ) ); ?></td>

					</tr>

					<tr>

						<th scope="row"><label><? _e( 'Footer', 'ifound' ) ; ?></label></th>

						<td><? do_action( 'ifound_email_dropdown', 'footer', 'footer', get_post_meta( $save_this_id, 'footer', true ) ); ?></td>

					</tr><?

				} ?>

			</tbody>

		</table><?

		if( $doing_prop_alert ) { ?>

			<table class="form-table notes_meta views_meta">

				<tbody>

					<tr>

						<th scope="row"><label><? _e( 'Last Time', 'ifound' ) ; ?></label></th>

						<td><? _e( apply_filters( 'pretty_date', get_post_meta( $save_this_id, 'last_time', true ) ), 'ifound' ) ; ?></td>

					</tr>

				</tbody>

			</table><?


		} else {

			if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)): ?>
			<?php
				$sms_templates = iFoundSms::new_hookless()->get_sample_campaign_templates();
				$to_smss = explode(',', $save_this_meta[iFoundSharedCampaign::$TO_SMS_KEY]);
				// If the user has the texting feature, but creates the campaign when the user has no mphone, the to_sms
				// value will be '0'. We remove invalid values here.
				$to_smss = array_values(array_filter($to_smss, function($x) {
					return $x !== '0' && $x !== '';
				}));
				$mphones_map = [
					'mphone' => $contact_meta['mphone'],
					'mphone_spouse' => $contact_meta['mphone_spouse'],
				];
				$do_sms_checked_str = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id)
					? 'checked="checked"' : '';
				$may_text_vals = iFoundJointContact::new_hookless()->get_may_text_vals($contact_id);
			?>
				<div class="title-heading default-criteria-heading"><i class="fal fa-comments" aria-hidden="true"></i> <? _e( 'Text Message(s)', 'ifound' ); ?></div>

				<div class="edit-sms-wrapper">
					<table class="form-table">
						<tbody>
						<tr>
							<th colspan="2">
								<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" id="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" value="<?= iFoundSharedCampaign::$DO_SMS_YES ?>"
									<?= $do_sms_checked_str ?>>
								<label for="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>"><? _e( 'Include text message(s) with this search', 'ifound' ); ?></label>
								<div class="may_not_text_message"></div>
							</th>
						</tr>
						<tr>
							<th scope="row"><label for="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>"><? _e( 'To Mobile Phone(s):', 'ifound' ); ?></label></th>
							<td>
								<input type="hidden" name="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>" id="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>">
								<div class="to_smss"></div>
								<strong class="no_to_sms_checked_warning hidden">You must check at least one mobile phone</strong>
								<strong class="no_mphone_warning hidden">This contact does not have a mobile phone number</strong>
								<script>
									jQuery(document).ready(function($) {
										var mphones_map = <?= json_encode($mphones_map); ?>;
										var options = <?= json_encode(['selected_mphones' => $to_smss]) ?>;
										window.ifound_populateToSmss(mphones_map, options);
										window.ifound_handleMayText(<?= json_encode($may_text_vals) ?>);
									});
								</script>
							</td>
						</tr>
						<tr>
							<th scope="row"><label for="sms_template"><? _e( 'Template', 'ifound' ); ?></label></th>
							<td>
								<select name="sms_template_id" id="sms_template_id" class="mphone-validate">
									<? foreach ($sms_templates as $sms_template): ?>
									<? $selected_str = $sms_template['id'] === $save_this_meta[iFoundSharedCampaign::$SMS_TEMPLATE_ID_KEY] ? 'selected' : ''; ?>
										<option value="<?= $sms_template['id'] ?>" <?= $selected_str ?>><?= $sms_template['label'] ?></option>
									<? endforeach ?>
								</select>
								<div class="sms_template_body_wrapper mphone-validate">
									Message: <span class="sms_template_body">
												<?= $sms_templates[0]['body'] ?>
											</span>
								</div>
							</td>
						</tr>
						</tbody>
					</table>
				</div>
			<? endif ?>

			<?php

		 	do_action( 'ifound_campaign_schedule', $save_this_id );

			?>

			<table class="form-table notes_meta views_meta">

				<tbody>

					<tr>

						<th scope="row"><label><? _e( 'Last Time', 'ifound' ) ; ?></label></th>

						<td><? _e( apply_filters( 'pretty_date', get_post_meta( $save_this_id, 'last_time', true ) ), 'ifound' ) ; ?></td>

					</tr><?

					if( isset( $_GET['admin'] ) ) { ?>

						<tr>

							<td colspan="2"><? _e( 'Admin Section - Date Format is Critical (i.e. 2017-11-10 12:17:12 )', 'ifound' ) ; ?></td>

						</tr>

						<tr>

							<th scope="row"><label><? _e( 'Next Time', 'ifound' ) ; ?></label></th>

							<td><input type="text" class="regular-text" name="next_time" value="<? echo get_post_meta( $save_this_id, 'next_time', true ); ?>"></td>

						</tr><?

					} else { ?>


						<tr>

							<th scope="row"><label><? _e( 'Next Time', 'ifound' ) ; ?></label></th>

							<td id="next_time"><? _e( apply_filters( 'pretty_date', get_post_meta( $save_this_id, 'next_time', true ) ), 'ifound' ) ; ?></td>

						</tr><?

					} ?>

				</tbody>

			</table>

			<?php
			$stats_params = get_post_meta($save_this_id, 'stats', true);
			?>

			<div class="display-stats-wrapper">

				<div class="ifound-wrap">

					<div class="display-stats-criteria-heading">

						<i class="fal fa-chart-line" aria-hidden="true"></i>
						<? _e( 'Stats', 'ifound' ); ?>

					</div>

					<div class="display-stats">

						<? do_action( 'ifound_display_stats_form', false ); ?>
						<script>
							jQuery(document).ready(function() {
								var data = <?= json_encode($stats_params) ?>;
								var target = document.querySelector('.ifound-stats-spa');
								window.ifound_spa.render_app('shortcode_creator_stats', target, data);
							});
						</script>

					</div>

				</div>

			</div>

			<?

		}

	}

	/**
	 *	Save This Search Metabox
	 *
	 *	@since 1.0.0
	 */

	public function save_this_search_metabox( $contact_id = false ) {

		global $post;

		$contact_id = is_int( $contact_id ) ? $contact_id : $post->ID;

		$args = array(
    		'post_type' 		=> $this->post_type,
			'posts_per_page'   	=> -1,
    		'tax_query' => array(
				array(
					'taxonomy' => $this->taxonomy,
					'field' => 'slug',
					'terms' => 'search-update'
				)
    		),
			'meta_query' => array(
				array(
					'key'     => 'contact_id',
					'value'   => $contact_id,
					'compare' => '=',
				)
			)
		);

		$this->save_this_meta_display( $args, 'search-update' );

	}

	/**
	 *	Save This Property Metabox
	 *
	 *	@since 1.0.0
	 */
	public function save_this_property_metabox( $contact_id = false ) {

		global $post;

		$contact_id = is_int( $contact_id ) ? $contact_id : $post->ID;

		$args = array(
    		'post_type' 		=> $this->post_type,
			'posts_per_page'   	=> -1,
    		'tax_query' => array(
				array(
					'taxonomy' => $this->taxonomy,
					'field' => 'slug',
					'terms' => 'property-alert'
				)
    		),
			'meta_query' => array(
				array(
					'key'     => 'contact_id',
					'value'   => $contact_id,
					'compare' => '=',
				)
			)
		);

		$this->save_this_meta_display( $args, 'property-alert' );

	}

	/**
	 *	Save This Campaign Metabox
	 *
	 *	@since 1.0.8
	 */
	public function save_this_campaign_metabox( $contact_id = false ) {
		global $post;
		$contact_id = is_int( $contact_id ) ? $contact_id : $post->ID;

		$args_for_save_this_campaigns = $this->get_campaigns_args_for_contact($contact_id);
		$save_this_posts = get_posts($args_for_save_this_campaigns);
		$args_for_drip_campaigns = iFoundDripCampaign::new_hookless()->get_campaigns_args_for_contact($contact_id);
		$drip_campaign_posts = get_posts($args_for_drip_campaigns);
		$posts = array_merge($save_this_posts, $drip_campaign_posts);
		usort($posts, function($a, $b) {
			return $a->post_date_gmt <=> $b->post_date_gmt;
		});
		$posts = array_reverse($posts);
		$this->save_this_meta_display($posts);
	}

	/**
	 * Save Alert Ajax
	 *
	 * Process the Save Alert Ajax.
	 *
	 * @since 1.0.0
	 * @since 2.0.5 Bug fix - Change no evaluation to recurring.
	 * @since 2.5.42 Remove conditions from campaign status.
	 */

	public function save_alert_ajax() {

		check_ajax_referer( 'save_alert_secure_me', 'save_alert_nonce' );

		$input = $_REQUEST['input'];
		$contact_id = intval( $input['contact_id'] );

		/** Make sure we have a contact to send this to. */
		if( is_int( $contact_id ) && get_post_status( $contact_id ) ) {

			parse_str( $input['form'], $form );

			$data = $this->obj($form);
			$data->params = $input['params'];
			$data->stats = $input['stats'];
			$data->contact_id = $contact_id;
			$data->{iFoundSharedCampaign::$TO_EMAIL_KEY} = $input[iFoundSharedCampaign::$TO_EMAIL_KEY];
			$data->extra_map_data = $input['extra_map_data'];
			if( $save_this_id = $this->save_this_campaign( $data ) ) {
				if ($data->{iFoundSharedCampaign::$DO_EMAIL_KEY} === iFoundSharedCampaign::$DO_EMAIL_YES
					|| $data->{iFoundSharedCampaign::$DO_SMS_KEY} === iFoundSharedCampaign::$DO_SMS_YES
				) {
					iFoundSaveThis::new_hookless()->save_campaign_stuff_and_maybe_run($save_this_id, $data);
				}

				$response = array(
					'class' 		=> 'fa-plus-square',
					'save_this_id' 	=> $save_this_id,
					'campaign_link' => admin_url('/post.php?action=edit&post=' . $save_this_id),
				);


				echo json_encode( $response );

				die();

			}

		}

		$response = array(
			'class' 		=> 'fa-exclamation-triangle',
			'save_this_id' 	=> 0
		);

		/** We didn't have success, so let's go ahead and error. */
		echo json_encode( $response );

		die();

	}

	/**
	 * Save This Campaign
	 *
	 * Save the data for the Save This post type.
	 *
	 * @since 1.0.0
	 * @since 1.0.6  Check to see if contact_id is really a contact.
	 * @since 1.0.23 Add a post category if a blog post is created.
	 * @since 1.2.34 Add ifound_activity_log action hook.
	 * @since 1.3.0  Move the create post functions to a separate method. @see iFOUND_save_this::campaign_builder_post()
	 * @since 2.5.42 Clean up the order of processes.
	 * @since 3.0.0 Change name to save_this_campaign
	 * @since 3.1.2 Define contact id for external crm integrations.
	 * @since 4.1.10 Move Buffer conditions to iFoundCampaignBuilder::campaign_builder_post()
	 *
	 * @uses get_post_status()
	 * @link https://codex.wordpress.org/Function_Reference/get_post_status
	 *
	 * @param  object $input        The input data from the campaign builder form.
	 * @return int    $save_this_id The db ID of the save this post type.
	 */

	public function save_this_campaign( $input ) {

		$contact_id = $input->contact_id;

		define( 'iFOUND_CONTACT_ID', $contact_id );

		$campaign_title = sanitize_text_field( $input->campaign_title );
		$campaign_title = ! empty( $campaign_title ) ? $campaign_title : 'Custom Search Prepared Just For You';

		$save_this_post = array(
			'post_title'    => $campaign_title,
			'post_status'   => 'publish',
			'post_type'		=> $this->post_type,
			'post_author'	=> $input->post_author ?? get_current_user_id(),
		);

		$save_this_id = wp_insert_post( $save_this_post );

		$data = new stdClass();
		$data->save_type = sanitize_text_field( $input->save_type );

		// In this section, we either take inputs as is, if they're already arrays, or we convert them into array data.
		$params = null;
		$stats = null;
		$extra_map_data = null;
		if (is_array($input->params)) {
			$params = $input->params;
		} else {
			parse_str( $input->params, $params );
		}
		if (is_array($input->stats)) {
			$stats = $input->stats;
		} else {
			parse_str( $input->stats, $stats );
			$stats = json_decode($stats['stats_data'], true);
		}
		if (is_array($input->extra_map_data)) {
			$extra_map_data = $input->extra_map_data;
		} else {
			parse_str( $input->extra_map_data, $extra_map_data );
			// PHP automatically adds slashes to input data. See https://stackoverflow.com/q/8949768/135101.
			$extra_map_data = stripslashes($input->extra_map_data);
			$extra_map_data = json_decode($extra_map_data, true);
		}

		add_post_meta( $save_this_id, 'params', $params, true );
		add_post_meta( $save_this_id, 'stats', $stats, true );
		add_post_meta( $save_this_id, 'contact_id', $contact_id, true );
		add_post_meta( $save_this_id, 'extra_map_data', $extra_map_data, true );

		wp_set_object_terms( $save_this_id, $data->save_type, $this->taxonomy, true );

		if( isset( $input->instant_update ) && $input->instant_update == 'checked' ) {
			wp_set_object_terms( $save_this_id, 'instant-update', $this->taxonomy, true );
		}
		if( isset( $input->instant_update_recently_closed ) && $input->instant_update_recently_closed == 'checked' ) {
			wp_set_object_terms( $save_this_id, 'instant-update-recently-closed', $this->taxonomy, true );
		}
		do_action( 'ifound_campaign_builder_post', $input, $params );
		do_action( 'ifound_activity_log', $contact_id, 'Save Campaign', $campaign_title );
		do_action( 'ifound_external_crm_save_this', 'SavedCampaign', $input );

		return $save_this_id;

	}

	/**
	 * Save Alert
	 *
	 * Clean the alert data and insert or update the database.
	 *
	 * @since 1.0.0
	 * @since 1.2.48 Set this up to update as well as save new alerts.
	 * @since 2.1.0 Move function to save last_time to iFOUND_email::email()
	 * @since 2.2.2 Break this down into smaller methods.
	 * @since 2.5.22 Create dates array to allow empty values for start_date and end_date.
	 * @since 2.5.23 Use filter hook for next_time.
	 *
	 * @param  int    $save_this_id
	 * @param  object $data
	 */

	public function save_alert( $save_this_id, $data ) {
		$clean_data = apply_filters('ifound_sanitize', [
			'recurring'   => $data->recurring,
			'how_often'   => $data->how_often,
			'time_of_day' => $data->time_of_day,
			'next_time'   => apply_filters('ifound_next_time', $data),
			'start_date'  => $this->start_date($data),
			'end_date'    => $this->end_date($data),
		]);
		foreach ($clean_data as $key => $value) {
			update_post_meta($save_this_id, $key, $value);
		}

		$vals = apply_filters('ifound_sanitize', [
			iFoundSharedCampaign::$DO_EMAIL_KEY => $data->{iFoundSharedCampaign::$DO_EMAIL_KEY},
			iFoundSharedCampaign::$TO_EMAIL_KEY => $data->{iFoundSharedCampaign::$TO_EMAIL_KEY},
			'header'                            => $data->header,
			'signature'                         => $data->signature,
			'footer'                            => $data->footer,
		]);
		$vals['custom_subject'] = $this->util()->sanitize_text($data->custom_subject);
		$vals['custom_content'] = $this->util()->sanitize_textarea($data->custom_content);
		foreach ($vals as $key => $value) {
			update_post_meta($save_this_id, $key, $value);
		}

		if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)) {
			$vals = apply_filters('ifound_sanitize', [
				iFoundSharedCampaign::$DO_SMS_KEY => $data->{iFoundSharedCampaign::$DO_SMS_KEY},
				iFoundSharedCampaign::$TO_SMS_KEY => $data->{iFoundSharedCampaign::$TO_SMS_KEY},
				'sms_template_id'                 => $data->sms_template_id,
			]);
			foreach ($vals as $key => $value) {
				update_post_meta($save_this_id, $key, $value);
			}
		}
	}

	/**
	 * Save Campaign Post
	 *
	 * Update Save Alert data. This is called by a action hook save_post.
	 *
	 * @since 2.0.0
	 * @since 2.2.1 Bug fix - Add condirion for original_publish.
	 * @since 2.2.2 Update contact ID here.
	 * @since 2.5.32 Rename method from update_alert()
	 *
	 * @param int $save_this_id The ID of the custom post type save_this.
	 */

	public function save_campaign_post( $save_this_id ) {
		if( isset( $_POST['original_publish' ] ) ) {

			do_action( 'ifound_activity_log', $save_this_id, 'Update Campaign Data', 'Campaign ID:' . $save_this_id . ' Updated' );

			$data = $this->obj( $_POST );

			// Wordpress automatically escapes values, so we undo it with stripslashes.
			$stats_json_string = stripslashes($data->stats_data);
			$stats_array = json_decode($stats_json_string, true);
			update_post_meta($save_this_id, 'stats', $stats_array);

			$is_property_alert = iFoundSaveThis::new_hookless()->is_property_alert($save_this_id);
			if (!$is_property_alert) {
				$this->save_alert( $save_this_id, $data );
			}
			$previous_campaign_status = has_term('active', $this->taxonomy2, $save_this_id) ? 'active' : 'inactive';
			$updated_campaign_status = $data->campaign_status;
			if (!$is_property_alert) {
				if ($updated_campaign_status !== $previous_campaign_status) {
					do_action('ifound_update_campaign_status', $save_this_id, $updated_campaign_status,
						'Manually updated');
					if ($updated_campaign_status === 'active') {
						iFoundSaveThis::new_hookless()->send_campaign_now($save_this_id);
					}
				}
			}
		}

	}

	public function save_this_query( $query ) {

		if ( ! is_admin() || $this->support() )	return;

		global $typenow;

		if ( $typenow == iFoundSaveThis::$the_post_type && ! isset( $_GET['page'] ) && ! isset( $_GET['post'] ) ) {
			if ($this->is_site_admin()) {
				$query->set('author__in', iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids());
			} else {
				$query->set('author', get_current_user_id());
			}

			// Don't show drafts, which should only occur if the IFA staff uses the bulk campaign tool. We don't want to
			// show drafts to non IFA staff because there's no way for others to publish them.
			$possible_statuses = get_post_stati();
			$statuses = array_filter($possible_statuses, function($x) { return $x !== 'draft'; });
			$query->set( 'post_status', $statuses );
		}

	}

	public function draft_submit_meta_box($post) {
		// The <style> section here will hide the lower portion of the metabox that has these two pieces:
		// * Move to Trash link
		// * Publish button
		?>
		<style>
			#major-publishing-actions {
				display: none;
			}
		</style>
		<?php
		// This will show some standard Wordpress post data and the Safe Draft button.
		call_user_func('post_submit_meta_box', $post);
	}
}
