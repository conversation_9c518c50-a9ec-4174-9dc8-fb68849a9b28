import React, { useState, useEffect, forwardRef } from 'react';
import useIfoundToasts from '../hooks/useIfoundToasts';
import produce from 'immer';
import {
    DndContext,
    DragOverlay,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
    useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import get from 'lodash/get';
import cloneDeep from 'lodash/cloneDeep';
import { makeRandomId } from "../lib/utils";

function DripTemplates(props) {
    const stepsData = cloneDeep(get(props, 'template_data.steps', []))
    // I'm not sure how, but existing steps data somehow seems to have been saved without an ID for each step. This
    // breaks the drag-n-drop to reorder functionality. So, just throw a random ID on each step that's missing an ID.
    stepsData.forEach(step => {
        if (!step.id) {
            step.id = makeRandomId();
        }
    });
    const [steps, setSteps] = useState(stepsData);
    const [jsonDataToSave, setJsonDataToSave] = useState('');
    const [activeDragItemId, setActiveDragItemId] = useState(null);
    const { addErrorToast } = useIfoundToasts();

    useEffect(() => {
        props.setShouldAllowSubmit(() => {
            if (steps.length === 0) {
                addErrorToast('Must have at least one step');
                return false;
            }
            for (let step of steps) {
                if (!step.customer_template_id && !step.reminder_template_id) {
                    addErrorToast('Each step must have an Email Template for Customer and/or Reminder to Agent');
                    return false;
                }
                if (step.interval === null) {
                    addErrorToast('Each step must have an interval');
                    return false;
                }
            }
            buildJsonDataToSave();
            return true;
        });

        return () => {
            props.setShouldAllowSubmit(props.originalShouldAllowSubmit);
        }
    })

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    function buildJsonDataToSave() {
        const data = {
            steps,
        }
        // I don't really understand this. The browser automatically URI encodes the value. But then it ends up with
        // backslashes (for each double-quote) on the PHP side, and json_decode() says there is a syntax error. So we
        // encode it again here, and then URI decode it there, and then json_decode(). Oh well, it works.
        setJsonDataToSave(encodeURIComponent(JSON.stringify(data)));
    }

    function setStepValue(stepIndex, key, value) {
        let v = value;
        if (['customer_template_id', 'reminder_template_id'].includes(key)) {
            v = parseInt(value, 10);
        }
        setSteps(produce(steps, draftState => {
            draftState[stepIndex][key] = v;
        }));
    }

    function removeStep(index) {
        setSteps(produce(steps, draftState => {
            draftState.splice(index, 1);
        }));
    }

    function addStepAfter(index) {
        const newStep = {
            // Use a random ID. The id is solely because dnd-kit needs an ID.
            id: makeRandomId(),
            customer_template_id: null,
            reminder_template_id: null,
            interval: index === -1 ? 'Campaign Start' : null,
        };
        setSteps(produce(steps, draftState => {
            draftState.splice(index + 1, 0, newStep);
        }));
    }

    function handleDragEnd(event) {
        const { active, over } = event;

        if (active.id !== over.id) {
            const oldIndex = steps.findIndex(x => x.id === active.id);
            const newIndex = steps.findIndex(x => x.id === over.id);
            setSteps(produce(steps, draftState => {
                const stepToMove = draftState[oldIndex];
                draftState.splice(oldIndex, 1);
                draftState.splice(newIndex, 0, stepToMove);
                // Only the first step may have an interval of 'Campaign Start'.
                if (oldIndex === 0 && draftState[newIndex].interval === 'Campaign Start') {
                    draftState[newIndex].interval = '1 Day';
                }
                return draftState;
            }));
        }

        setActiveDragItemId(null);
    }

    function handleDragStart(event) {
        const { active } = event;
        setActiveDragItemId(active.id);
    }

    function handleDragCancel(event) {
        setActiveDragItemId(null);
    }

    return <div style={{ overflowX: 'auto' }}><DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
    >
        <table className="widefat striped">
            <thead>
            <tr>
                <th>Step #</th>
                <th>Email Template for Customer</th>
                <th>Reminder to Agent</th>
                <th>Interval from Previous Step</th>
                <th>Actions</th>
                <th>(Drag to reorder)</th>
            </tr>
            </thead>
            <tbody>
            <SortableContext
                items={steps}
                strategy={verticalListSortingStrategy}
            >
                {steps.map((step, stepIndex) => {
                    return <SortableItem
                        key={stepIndex}
                        id={step.id}
                        step={step}
                        stepIndex={stepIndex}
                        emailTemplates={props.emailTemplates}
                        reminderTemplates={props.reminderTemplates}
                        intervals={props.intervals}
                        setStepValue={setStepValue}
                        removeStep={removeStep}
                        addStepAfter={addStepAfter}
                        isDragItem={activeDragItemId === step.id}
                    />
                })}
            </SortableContext>
            {
                steps.length === 0 && <tr>
                    <td colSpan="5">
                        <button type="button" className="button" onClick={() => addStepAfter(steps.length - 1)}>
                            Add first step
                        </button>
                    </td>
                </tr>
            }
            </tbody>
        </table>
        <input type="hidden" name="template_data" value={jsonDataToSave}/>
        <DragOverlay>
            <table className="widefat">
                {/*
                    If we wanted the drag item to look exactly like it does it does when not being dragged (as in, the
                    exact same widths on the td's, we'd need a thead and th's here. However, I can't figure out how to
                    do that and not actually show the thead section, or perhaps the th's. I've tried using these
                    properties all together but no luck.
                        * height: 0
                        * overflow: hidden
                        * line-height: 0
                    Oh well. It looks similar enough anyway I guess.
                */}
                <tbody>
                {(() => {
                    if (!activeDragItemId) {
                        return false;
                    }
                    const s = steps.find(x => x.id === activeDragItemId);
                    const foundIndex = steps.findIndex(x => x.id === activeDragItemId);
                    return <Item
                        key={activeDragItemId}
                        id={s.id}
                        step={s}
                        stepIndex={foundIndex}
                        emailTemplates={props.emailTemplates}
                        reminderTemplates={props.reminderTemplates}
                        intervals={props.intervals}
                        setStepValue={setStepValue}
                        removeStep={removeStep}
                        addStepAfter={addStepAfter}
                    />
                })()}
                </tbody>
            </table>
        </DragOverlay>
    </DndContext></div>;
}

const Item = forwardRef((
    {
        step,
        emailTemplates,
        reminderTemplates,
        intervals,
        setStepValue,
        removeStep,
        addStepAfter,
        stepIndex,
        style,
        attributes,
        listeners,
    },
    ref,
) => {
    const intervalSelect = (
        <select value={step.interval === null ? '' : step.interval}
                onChange={e => setStepValue(stepIndex, 'interval', e.target.value)}>
            <option value="" disabled="disabled">(Select)</option>
            {intervals.map(interval => {
                const disabledProp = {};
                // Only the first step may have an interval of 'Campaign Start'.
                if (stepIndex !== 0 && interval === 'Campaign Start') {
                    disabledProp.disabled = 'disabled';
                }
                return <option key={interval} {...disabledProp} value={interval}>{interval}</option>
            })}
        </select>
    );

    return <tr ref={ref} style={style} {...attributes}>
        <td>
            {stepIndex + 1}
        </td>
        <td>
            <select value={step.customer_template_id || ''}
                    onChange={e => setStepValue(stepIndex, 'customer_template_id', e.target.value)}>
                <option value="">(None)</option>
                {Object.entries(emailTemplates).map(([title, id]) => {
                    return <option key={id} value={id}>{title}</option>
                })}
            </select>
        </td>
        <td>
            <select value={step.reminder_template_id || ''}
                    onChange={e => setStepValue(stepIndex, 'reminder_template_id', e.target.value)}>
                <option value="">(None)</option>
                {Object.entries(reminderTemplates).map(([title, id]) => {
                    return <option key={id} value={id}>{title}</option>
                })}
            </select>
        </td>
        <td>
            {stepIndex === 0
                ? <span>Campaign Start</span>
                : intervalSelect
            }
        </td>
        <td>
            <i className="far fa-trash" onClick={() => removeStep(stepIndex)} style={{ cursor: 'pointer', color: 'red' }}
               title="Remove this step"/>
            <i className="far fa-plus-circle" onClick={() => addStepAfter(stepIndex)}
               style={{ cursor: 'pointer', color: 'green', 'marginLeft': '15px' }} title="Add another step"/>
        </td>
        <td>
            <i className="fas fa-ellipsis-v" style={{ cursor: 'move' }} {...listeners} title="Drag me" />
        </td>
    </tr>
});

function SortableItem(props) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
    } = useSortable({ id: props.id });
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: props.isDragItem ? '10%' : '100%',
    };

    return <Item ref={setNodeRef} style={style} {...props} attributes={attributes} listeners={listeners} />
}

export default DripTemplates;
