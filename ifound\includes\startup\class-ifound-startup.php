<?
/**
 * iFoundStartup class
 *
 * Run startup methods to install plugin defaults.
 *
 * @since 1.2.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundStartup {
	use UtilTrait;

	/**
	 * init iFoundStartup class.
	 *
	 * @since 1.2.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.2.0
	 */

	public function __construct() {
		$this->start_up();
	}

	public function start_up() {
		if( get_option( 'ifound_forms_installed' ) ) {
			return;
		}

		define( 'iFOUND_STARTUP', true );

		do_action( 'update_meta_box_order' );

		$this->install_allowed_features();
		$this->install_admin_roles();
		$this->install_db_limits_cron();
		$this->install_crm_settings();
		$this->install_contacts();
		$this->install_save_this();
		$this->install_email();
		$this->install_listen();
		$this->install_cmc();
		$this->install_registration();
		$this->install_seo();

		/** If we do not have these set. We cannot install all the settings. */
		$this->api_settings = get_option( 'ifound_api_settings' );
		if( empty( $this->api_settings['api_secret'] ) || empty( $this->api_settings['mls_name'] ) ) return;

		global $mls_associations;
		if( ! isset( $mls_associations ) )
			do_action( 'ifound_mls_associations' );

		$this->mappings = iFoundIdx::new_hookless()->mappings;

		$this->install_criteria();
		$this->install_more_criteria();
		$this->install_adv_criteria();
		$this->install_more_adv_criteria();
		$this->install_settings();
		$this->install_search_template_table();

		if ( $this->idx_forms = $this->get_idx_forms() ) {
			$this->install_cmc_form();
			$this->install_cmc_address_form();
			/** Always do this last */
			$this->install_forms();
		}
	}

	public function install_allowed_features() {

		if( ! get_option( 'ifound_allowed_features' ) ) {

			update_option( 'ifound_allowed_features', array( 'idx' ) );

		}

	}

	public static function install_admin_roles() {

		$role = get_role( 'administrator' );

		$caps = array(
			'edit_crm_settings',
			'create_campaigns',
			'crm_import',
			'crm_export',
			'edit_contactss',
			'edit_others_contactss',
			'edit_published_contactss',
			'read_contactss',
			'read_others_contactss',
			'delete_contactss',
			'delete_others_contactss',
			'delete_published_contactss',
			'publish_contactss',
			iFoundContacts::$manage_contact_tags_cap_name,
		);

		foreach( $caps as $cap ) {
   			$role->add_cap( $cap );
		}

	}

	public function install_db_limits_cron() {
		do_action('ifound_install_db_limits_cron');
	}

	private function install_contacts() {

		if( get_option( 'install_contacts' ) ) return;

		/** Call the post type to insure it is registered. */
		$contacts = iFoundContacts::new_hookless();
		$contacts->post_type();
		$contacts->taxonomy();

		$contact_defaults = array(
			'Active',
			'Closed',
			'New',
			'Prospect',
			'Opportunity',
			'Removed',
			'Unsubscribed',
			iFoundContacts::$PREVIOUS_RELATIONSHIP_LABEL,
		);

		foreach( $contact_defaults as $default ) {
			$term = term_exists( $default, 'contacts_status' );
			if ( $term == 0 || $term == null ) {
				wp_insert_term( $default, 'contacts_status' );
			}
		}

		$crm_settings = get_option( 'ifound_crm_settings' ) ?: array();
		$crm_settings['time_of_day'] = 'none';
		$crm_settings['qty_logs_to_report'] = 30;
		$crm_settings['contact_visit_email'] = 'enabled';
		update_option( 'ifound_crm_settings', $crm_settings );

		update_option( 'install_contacts', 'installed' );

	}

	/**
	 * Install Save This
	 *
	 * @since 2.4.28 Discontinue storing Property Alert term ID.
	 */

	private function install_save_this() {

		if( get_option( 'install_save_this' ) ) return;

		/** Call the post type to insure it is registered. */
		$iFOUND_save_this = iFoundSaveThis::new_hookless();
		$iFOUND_save_this->post_type();
		$iFOUND_save_this->taxonomy();
		$iFOUND_save_this->taxonomy2();

		$save_this_defaults = array(
			'Property Alert',
			'Search Update',
			'Holiday',
			'Newsletter',
			'Birthday',
			'Marketing',
			'Open House',
			'Event',
			'Market Update',
			'Instant Update',
			'Homeowner Campaign',
			'Instant Update Recently Closed'
		);

		foreach( $save_this_defaults as $default ) {

			$term = term_exists( $default, 'save_type');

			if ( $term == 0 || $term == null ) {

				wp_insert_term( $default, 'save_type' );

			}
		}

		$new_terms = array(
			'Active',
			'Inactive'
		);

		foreach( $new_terms as $new_term ) {

			$term = term_exists( $new_term, 'campaign_status');

			if ( $term == 0 || $term == null ) {

				wp_insert_term( $new_term, 'campaign_status' );

			}

		}

		update_option( 'install_save_this', 'installed' );

	}

	private function install_email() {

		require_once( ABSPATH . 'wp-admin/includes/post.php' );

		if( get_option( 'install_email' ) ) return;

		$crm_settings = get_option( 'ifound_crm_settings' ) ?: array();

		$email = iFoundEmail::new_hookless();

		$email->post_type();
		$email->taxonomy();

		$email_defaults = array(
			'content'                                  => 'Content',
			'header'                                   => 'Header',
			'footer'                                   => 'Footer',
			'signature'                                => 'Signature',
			'drip-email'                               => 'Drip Email',
			'reminder-text'                            => 'Reminder Text',
			'search-campaign'                          => 'Search Campaign',
			'bulk-email'                               => 'Bulk Email',
			iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_SLUG => iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_LABEL,
		);

		foreach( $email_defaults as $slug => $default ) {
			$term = term_exists( $default, 'ifound_email_type' );
			if ( $term == 0 || $term == null ) {
				$args = ['slug' => $slug];
				wp_insert_term( $default, 'ifound_email_type', $args );
			}
		}

		$email_templates = iFoundEmail::new_hookless()->email_templates();

		foreach( $email_templates as $template ) {
			$post_id = iFoundEmail::new_hookless()->create_email_template($template);
			if( $template['default_key'] ) {
				$crm_settings[ $template['default_key'] ] = $post_id;
			}
		}

		update_option( 'ifound_crm_settings', $crm_settings );

		update_option( 'install_email', 'installed' );

	}


	private function install_crm_settings() {

		$crm_settings = get_option( 'ifound_crm_settings' ) ?: array();

		require_once( ABSPATH . 'wp-admin/includes/taxonomy.php' );
		$cat_id = wp_create_category( 'Real Estate' );
		$crm_settings['campaign_post_category_id'] = $cat_id;
		update_option( 'ifound_crm_settings', $crm_settings );

	}

	private function install_registration() {

		if( get_option( 'install_registration' ) ) return;

		$regist = get_option( 'ifound_registration_settings' ) ?: array();
		$regist['registration_limit_all']		= 10;
		$regist['registration_limit_results']	= 5;
		$regist['registration_limit_advanced']	= 2;
		$regist['registration_limit_details']	= 5;
		$regist['registration_delay_before']	= 5;
		$regist['registration_delay_between']	= 10;
		$regist['registration_body']			= $this->util()->get_html( 'defaults/', 'registration-body' );
		update_option( 'ifound_registration_settings', $regist );

		update_option( 'install_registration', 'installed' );

	}

	public static function install_seo() {

		if( get_option( 'ifound_seo_settings' ) ) return;

		$defaults = array(
			'results_h1'				=> 'Property Search',
			'results_h2'				=> '<span class="ifound-prop-h2">{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber}</span> <span class="ifound-prop-h2">{City} {PostalCode}</span>',
			'results_meta_title'		=> 'Property Search',
			'results_meta_description'	=> 'Complete list of homes for sale in {City}',
			'address'					=> '{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City}, {PostalCode}',
			'detail_h1'					=> '{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City}, {PostalCode}',
			'detail_url'				=> '{StreetNumber}-{StreetDirPrefix}-{StreetName}-{StreetSuffix}-{UnitNumber}-{City}-{PostalCode}',
			'detail_meta_title'			=> '{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City} {PostalCode}',
			'detail_meta_description'	=> '{City} Home for sale, {Remarks}'
		);

		update_option( 'ifound_seo_settings', $defaults );

	}

	private function install_listen() {

		if( get_option( 'install_listen' ) ) return;

		iFoundListen::new_hookless()->endpoints();
		flush_rewrite_rules();

		update_option( 'install_listen', 'installed' );

	}

	private function install_cmc() {

		if( get_option( 'install_cmc' ) ) return;

		if( ! get_option( 'cmc_activate' ) ) {
			update_option( 'cmc_activate', 'checked' );
		}

		if( ! get_option( 'cmc_widget_title' ) ) {
			update_option( 'cmc_widget_title', 'What&#39;s My Home Worth?' );
		}

		if( ! get_option( 'cmc_pop_up_text' ) ) {

			$cmc_pop_up_text = 'To get a more accurate representation of what&#39;s selling and what&#39;s sold around your home, please complete the information on the form provided. Thanks.';

			update_option( 'cmc_pop_up_text', trim( $cmc_pop_up_text ) );

		}

		if( ! get_option( 'cmc_admin_map_settings' ) ) {

			/** Default settings include the lower 48 states */
			$map_settings = (object) array(
				'north'			=> 48.764578762482,
				'south'			=> 23.644727798361,
				'east'			=> -65.99809963559,
				'west'			=> -128.97579983091,
				'center_lat'	=> 37.989086530067,
				'center_lng'	=> -93.91133938168,
				'zoom'			=> 3
			);

			update_option( 'cmc_admin_map_settings', $map_settings );

		}

		update_option( 'install_cmc', 'installed' );

	}

	private function install_criteria() {

		if( get_option( 'ifound_campaign_bulder_criteria' ) ) return;

		$criteria = array(
			'prop_type' 		=> 'prop_type',
			'city' 				=> 'city',
			'list_price' 		=> 'list_price',
			'bathrooms' 		=> 'bathrooms',
			'bedrooms' 			=> 'bedrooms',
			'school_district' 	=> 'school_district',
			'living_sqft' 		=> 'living_sqft',
			'year_built' 		=> 'year_built',
			'garage_spaces' 	=> 'garage_spaces',
			'pool_private' 		=> 'pool_private',
			'nearby' 			=> 'nearby',
		);

		update_option( 'ifound_campaign_bulder_criteria', array_filter( $criteria, array( $this, 'filter_criteria' ) ) );

	}

	private function install_more_criteria() {

		if( get_option( 'ifound_more_campaign_bulder_criteria' ) ) return;

		$more_criteria = array(
			'subdivision' 			=> 'subdivision',
			'list_status' 			=> 'list_status',
			'days_back' 			=> 'days_back',
			'mls_id' 				=> 'mls_id',
			'special_listing_cond' 	=> 'special_listing_cond',
			'architechtural_style' 	=> 'architechtural_style',
			'property_features' 	=> 'property_features',
			'comm_features' 		=> 'comm_features',
			'green_features' 		=> 'green_features',
			'energy_green_feature' 	=> 'energy_green_feature',
			'parking_features' 		=> 'parking_features',
			'horses' 				=> 'horses',
			'horse_features' 		=> 'horse_features',
			'kitchen_features' 		=> 'kitchen_features',
			'lot_size' 				=> 'lot_size',
			'landscaping' 			=> 'landscaping',
			'lot_sqft' 				=> 'lot_sqft',
			'zip' 					=> 'zip',
			'high_school' 			=> 'high_school',
			'hoa' 					=> 'hoa',
			'elementary_school' 	=> 'elementary_school',
			'model' 				=> 'model',
			'marketing_name' 		=> 'marketing_name',
			'dwelling_type' 		=> 'dwelling_type',
			'exterior_features' 	=> 'exterior_features'
		);

		update_option( 'ifound_more_campaign_bulder_criteria', array_filter( $more_criteria, array( $this, 'filter_criteria' ) ) );

	}

	private function install_adv_criteria() {

		if( get_option( 'ifound_adv_search_criteria' ) ) return;

		$adv_criteria = array(
			'prop_type' 		=> 'prop_type',
			'city' 				=> 'city',
			'list_price' 		=> 'list_price',
			'bathrooms' 		=> 'bathrooms',
			'bedrooms' 			=> 'bedrooms',
			'school_district' 	=> 'school_district',
			'living_sqft' 		=> 'living_sqft',
			'year_built' 		=> 'year_built',
			'garage_spaces' 	=> 'garage_spaces',
			'pool_private' 		=> 'pool_private'
		);

		update_option( 'ifound_adv_search_criteria', array_filter( $adv_criteria, array( $this, 'filter_criteria' ) ) );

	}

	private function install_more_adv_criteria() {

		if( get_option( 'ifound_more_adv_search_criteria' ) ) return;

		$adv_more_criteria = array(
			'subdivision' 			=> 'subdivision',
			'list_status' 			=> 'list_status',
			'days_back' 			=> 'days_back',
			'mls_id' 				=> 'mls_id',
			'special_listing_cond' 	=> 'special_listing_cond',
			'architechtural_style' 	=> 'architechtural_style',
			'property_features' 	=> 'property_features',
			'comm_features' 		=> 'comm_features',
			'green_features' 		=> 'green_features',
			'energy_green_feature' 	=> 'energy_green_feature',
			'parking_features' 		=> 'parking_features',
			'horses' 				=> 'horses',
			'horse_features' 		=> 'horse_features',
			'kitchen_features' 		=> 'kitchen_features',
			'lot_size' 				=> 'lot_size',
			'landscaping' 			=> 'landscaping',
			'lot_sqft' 				=> 'lot_sqft',
			'zip' 					=> 'zip',
			'high_school' 			=> 'high_school',
			'hoa' 					=> 'hoa',
			'elementary_school' 	=> 'elementary_school',
			'model' 				=> 'model',
			'marketing_name' 		=> 'marketing_name',
			'dwelling_type' 		=> 'dwelling_type',
			'exterior_features' 	=> 'exterior_features'
		);

		update_option( 'ifound_more_adv_search_criteria', array_filter( $adv_more_criteria, array( $this, 'filter_criteria' ) ) );


	}

	private function install_settings() {

		if( get_option( 'install_settings' ) ) return;

		$settings = array(
			'ifound_results_settings' 		=> array(
				'grid_layout'					=> array( 1 => 'checked' ),
			),
			'ifound_detalis_settings' 		=> array(
				'details_slider_script'			=> $this->util()->get_html( 'defaults/', 'details-slider-script' ),
			),
			'ifound_featured_settings' 		=> array(
				'featured_widget_title'			=> 'Featured Listings',
				'featured_query'				=> 'list_price[min]=200000&list_price[max]=800000',
				'featured_bu_query'				=> 'list_price[min]=200000',
				'featured_qty'					=> 6,
				'featured_slider_script'		=> $this->util()->get_html( 'defaults/', 'featured-script' ),
			),
			'ifound_unsubscribe_settings' 	=> array(
				'unsubscribe_body'				=> $this->util()->get_html( 'defaults/', 'unsubscribe-body' ),
			),
			'ifound_search_settings' 		=> array(
				'prop_type'						=> $this->get_keys( 'prop_type' ),
				'mls_class'						=> $this->get_keys( 'mls_class' ),
				'contingent'					=> $this->get_keys( 'contingent' ),
				'list_status'					=> $this->get_keys( 'list_status' ),
				'city'							=> $this->get_keys( 'city' )
			),
			// So far, we only have two checkboxes here, and we default them to unchecked. To use the same behavior that
			// occurs if a user unchecks the checkboxes later, we don't even list the setting here (as in, that's the
			// equivalent of the values being unchecked).
			'ifound_teams_settings'         => [],
		);

		foreach( $settings as $setting => $value )
			update_option( $setting, $value );

		update_option( 'install_settings', 'installed' );

	}

	public function get_idx_forms() {

		$build_admin_origin = apply_filters('ifound_config', [])['build_admin_origin'];
		$endpoint = $build_admin_origin . '/wp-json/ifound-admin/1.0.0/idx-forms/';
		$headers = array(
			'headers'     =>  array(
            	'Accept' 			=> 'application/json',
            	'AUTHENTICATION' 	=> $this->api_settings['api_secret']
        	)
		);

		$response = wp_remote_get( $endpoint, $headers );

		if( is_array( $response ) ) {

			return json_decode( $response['body'], true );

		}

	}

	private function install_cmc_form() {

		if( ! class_exists( 'GFAPI' ) ) return;

		if( get_option( 'cmc_ifound_form_id' ) ) return;

		global $mls_associations;

		$form_object = $this->idx_forms['cmc-form'];

		$form_object[0]['confirmations'][0]['url'] = site_url( '/current-market-comparison/' );

		$form_id = GFAPI::add_form( $form_object[0] );

		if( $form_id ) {

			update_option( 'cmc_ifound_form_id', $form_id );

			iFoundCmc::cmc_update_form_ptypes();

		}

	}

	private function install_cmc_address_form() {

		if( ! class_exists( 'GFAPI' ) ) return;

		if( get_option( 'cmc_ifound_address_capture_form_id' ) ) return;

		$form_object = $this->idx_forms['address-capture-form'];

		$form_id = GFAPI::add_form( $form_object[0] );

		if( $form_id ) {

			update_option( 'cmc_ifound_address_capture_form_id', $form_id );

		}

	}

	private function install_forms() {

		if( ! class_exists( 'GFAPI' ) ) return;

		if( get_option( 'ifound_forms_installed' ) ) return;

		$forms = array(
			'make-offer-form'			=> 'make_offer_form_id',
			'schedule-showing-form'		=> 'schedule_showing_form_id',
			'registration-form'			=> 'registration_form_id',
			'get-pre-qualified-form'	=> 'prequal_form_id'
		);

		$settings = get_option( 'ifound_registration_settings', array() );

		foreach( $forms as $content_type => $option ) {

			$form_object = $this->idx_forms[$content_type];

			$form_id = GFAPI::add_form( $form_object[0] );

			if( $form_id ) {
				$settings[$option] = $form_id;
			}

		}

		update_option( 'ifound_registration_settings', $settings );

		update_option( 'ifound_forms_installed', 'installed' );

	}

	public static function install_search_template_table() {

		global $wpdb;

		$table_name = $wpdb->prefix . 'ifound_meta';

		$sql = "
			CREATE TABLE IF NOT EXISTS " . $table_name . " (
  			meta_id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  			object_id bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  			meta_key varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  			meta_value longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  			PRIMARY KEY (meta_id),
  			KEY object_id (object_id),
  			KEY meta_key (meta_key(191))
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  		";

  		require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );

		dbDelta( $sql );

	}

	public function filter_criteria( $key ) {
		return isset( $this->mappings->$key );
	}

	private function get_keys( $easy_name ) {

		global $mls_associations;

		if( ! isset( $mls_associations ) )

			die( 'There was a problem with the installation. <NAME_EMAIL> ERROR CODE: getkeys' );

		foreach( $mls_associations->$easy_name as $key => $value ) :
			$keys[] = $key;
		endforeach;

		return $keys;

	}

}
