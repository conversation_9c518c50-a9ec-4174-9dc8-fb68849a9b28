<?php

namespace Profound\MLS;

class BrightmlsImageHandler extends ImageHandler {
    static $mlsname = "brightmls";
    public function getImagePathsList($listing_id) {
        $imgtable = self::$mlsname . "_images";

		$sql = <<<EOT
			SELECT
				`Content-Description`,
				`Object-ID`,
				Location,
				MediaURLFull,
				MediaURLMedium,
				MediaURLHiRes
			FROM $imgtable
			WHERE `Content-ID` = '$listing_id'
			ORDER BY `Preferred` DESC, `Object-ID` ASC
EOT;

		$results = $this->getDb()->fetchAll($sql);
	    $img_array = array();

	    // Reminder of sizes
	    // ListingKey 302192504996, Object-ID = 10
	    // http://bright-media.brightmls.com/bright/images/0000/3017/5995/0713/301759950713_1024_768_WM_CSKtorBbI9GcF-eS.jpg
	    // Original is 5152x3864. It is not available, but their data has its dimensions.
	    // Full is 1024x768
	    // HD is 2047x1536
	    // HiRes is 1440x1081
	    // Medium is 512x384
	    // Thumb is 128x96

	    foreach ($results as $result) {
			$img_array[] = [
				'thumbnail_url' => $result['MediaURLMedium'],
				'normal_url' => $result['MediaURLFull'],
				'highres_url' => $result['MediaURLHiRes'],
				'description' => $result['Content-Description'],
			];
	    }
		return $img_array;
    }
}