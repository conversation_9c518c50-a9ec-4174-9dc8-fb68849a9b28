<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * iFoundCampaignBuilder Class
 *
 * @since 2.5.0
 */

class iFoundCampaignBuilder extends iFoundCrm {


	/**
	 * init iFoundCampaignBuilder class.
	 *
	 * @since 2.5.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 2.5.0
	 */

	public function __construct() {

		wp_register_script( 'draw_campaign_map_js', plugins_url( 'js/draw-campaign-map.js', __FILE__), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		add_action( 'admin_menu', array( $this, 'settings_menu' ), 5 );
		add_filter( 'create_post_form', array( $this, 'create_post_form' ), 10 );
		add_action( 'ifound_campaign_builder_post', array( $this, 'campaign_builder_post' ), 10, 2 );
		add_action( 'ifound_campaign_schedule', array( $this, 'campaign_schedule' ), 10, 1 );
		add_action( 'ifound_time_of_day_select', array( $this, 'time_of_day_select' ), 10, 3 );
		add_action( 'ifound_choose_mls_ids', array( $this, 'choose_mls_ids' ) );
		add_action( 'ifound_how_often_select', array( $this, 'how_often_select' ) );
		add_filter( 'ifound_how_often_options', array( $this, 'how_often_options') );
	}

	/**
	 * Settings Menu
	 *
	 * Add campaign builder to menu.
	 *
	 * @since 1.0.0
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 */

	public function settings_menu() {

		add_menu_page(
			__( 'Campaign Builder', 'ifound' ),
			__( 'Campaign Builder', 'ifound' ),
        	'create_campaigns',
			'campaign-builder',
        	array( $this, 'campaign_builder_page' ),
			'dashicons-groups',
			2
		);

		add_submenu_page(
			'campaign-builder',
			'New Search Campaign',
			'Search Campaign',
			'create_campaigns',
			'campaign-builder'
            // We don't need to specify a function here because we are using the same menu_slug as the Campaign Builder
			// menu above, so it will use the function specified there.
		);

	}

	/**
	 * Campaign Builder Page
	 *
	 * The campaign builder page display.
	 *
	 * @since 1.0.0
	 * @since 2.5.0 Move to iFOUND_campaign_builder class.
	 * @since 3.6.5 Enqueue paging_js
	 * @since 4.1.9 Update Buffer Class name.
	 */

	public function campaign_builder_page() {

		if ( ! current_user_can( 'create_campaigns' ) ) return;

		wp_enqueue_script( 'cookie_js' );
		wp_enqueue_script( 'ifound_map_js' );
		wp_enqueue_script( 'paging_js' );
		wp_enqueue_script( 'save_this_js' );
		wp_enqueue_script( 'dynamic_js' );
		wp_enqueue_script( 'ifound_spa_js' );
		wp_enqueue_script( 'jquery-ui-datepicker' );
		wp_enqueue_script( 'save_alert_js' );
		wp_enqueue_script( 'budget_search_js' );
		wp_enqueue_script( 'draw_campaign_map_js' );

		wp_enqueue_style( 'campaign_builder_css' );

		do_action( 'ifound_load_map' );

		$crm = $this->crm();
		$contact_id = $_GET['contact_id'];
		$add_new_contact_url = admin_url('post-new.php');
		$add_new_contact_url = add_query_arg(
			['post_type' => iFoundJointContact::new_hookless()->get_new_contact_type()], $add_new_contact_url);
		$post_type = iFoundEmail::$the_post_type;
		$exclude['exclude'] = array(
			get_page_by_title( 'Instant Update Notification', 'OBJECT', $post_type )->ID,
			get_page_by_title( 'Instant Update Recently Closed Notification', 'OBJECT', $post_type )->ID,
			get_page_by_title( 'Saved Property Notification', 'OBJECT', $post_type )->ID,
			get_page_by_title( 'Saved Search Notification', 'OBJECT', $post_type )->ID,
			get_page_by_title( 'Property Alert', 'OBJECT', $post_type )->ID
		);
		$iFoundEmailObj = iFoundEmail::new_hookless();
		$sms_templates = iFoundSms::new_hookless()->get_sample_campaign_templates();
		?>

		<div id="campaign-builder" class="ifound-wrap doing_campaign">

			<h1 class="ifound-admin-h1"><? _e( 'Campaign Builder', 'ifound' ); ?></h1><?

			do_action( 'ifound_help_button', 'campaign_builder' );

			do_action( 'ifound_wa_button' ); ?>
			<div class="ifound-wrap">

				<div class="default-criteria-heading"><i class="fal fa-user"></i> <? _e( 'Contact', 'ifound' ); ?></div>

				<table class="campaign-builder-table form-table">

					<tbody>

						<tr>

							<th scope="row"><label for=""><? _e( 'Contact:', 'ifound' ); ?></label></th>

							<td>
								<input type="text" id="contact_autocomplete" placeholder="Type Here" class="regular-text ifound-validate">
								<div style="margin-top: 4px;">
									Or, start by <a href="<?= $add_new_contact_url ?>">adding a new contact</a>
								</div>
							</td>

							<input type="hidden" id="contact_id" name="contact_id" value="<?= $contact_id ?>">

							<? do_action('ifound_wa_sync_button'); ?>
						</tr>

					</tbody>

				</table>

				<div class="default-search-criteria">

					<div class="ifound-wrap">

						<div class="default-criteria-heading">

							<i class="fal fa-search" aria-hidden="true"></i>
							<? _e( 'Filters', 'ifound' ); ?>

						</div>

						<div class="default-criteria-wrapper"><?

							do_action( 'ifound_search_criteria', 'ifound_campaign_bulder_criteria', 'search-', true ); ?>

						</div>

					</div>

				</div>

				<div class="additional-criteria">

					<div class="ifound-wrap">

						<div class="criteria-heading">

							<i class="fal fa-plus-circle" aria-hidden="true"></i>
							<? _e( 'More Filters', 'ifound' ); ?>


						</div><?

						do_action( 'ifound_search_criteria', 'ifound_more_campaign_bulder_criteria' ); ?>

					</div>

				</div><?

				do_action( 'ifound_budget_search' ); ?>

				<div class="default-criteria-heading map-button" id="map-button"><i class="fal fa-map" aria-hidden="true"></i> <? _e( 'Show Map/Draw Polygon', 'ifound' ); ?></div><?
				do_action( 'ifound_shapes_map' );

				?>

				<input type="hidden" name="extra_map_data" id="extra-map-data">

				<div class="display-stats-wrapper">

					<div class="ifound-wrap">

						<div class="display-stats-criteria-heading">

							<i class="fal fa-chart-line" aria-hidden="true"></i>
							<? _e( 'Stats', 'ifound' ); ?>

						</div>

						<div class="display-stats">

							<? do_action( 'ifound_display_stats_form', false ); ?>
							<script>
								jQuery(document).ready(function() {
									var target = document.querySelector('.ifound-stats-spa');
									window.ifound_spa.render_app('shortcode_creator_stats', target);
								});
							</script>

						</div>

					</div>

				</div>
				<?

				do_action( 'ifound_search_template_box' );

				do_action( 'ifound_dynamic_form_wrapper', false );

				do_action( 'ifound_advanced_button' );

				do_action( 'ifound_choose_mls_ids' );?>

				<form id="ifound-save-alert-form" class="save-alert-form hide-choose-mls-ids">

					<div class="campaign-builder-results">

						<div class="ifound-wrap">

							<div class="hide-results-wrapper">
								<i class="fal fa-arrow-circle-up hide-results" aria-hidden="true"></i>
							</div>

							<div class="display-results-wrapper"><?

								if( ! empty( $results ) ) {

									do_action( 'ifound_display_results', $results );

								} ?>

							</div>

						</div>

					</div>

					<div class="clear"></div>

					<div class="title-heading default-criteria-heading"><i class="fal fa-heading" aria-hidden="true"></i> <? _e( 'Title', 'ifound' ); ?></div>

					<table class="form-table">

						<tbody>

							<tr>

								<th scope="row"><label for="campaign_title"><? _e( 'Campaign Title:', 'ifound' ); ?></label></th>

								<td><input type="text" name="campaign_title" id="campaign_title" placeholder="e.g. Homes in areas we discussed on Wednesday May 3." class="search-title-input regular-text ifound-validate"></td>

							</tr><?

							echo apply_filters( 'create_post_form', false );

							if( class_exists( 'WP_To_Buffer_Pro' ) && $this->is_site_admin() ) { ?>

								<tr class="create-post">

									<td colspan="2"><input type="checkbox" name="do_buffer" id="do_buffer" value="checked" checked>

									<? _e( 'Share Blog Post with Buffer', 'ifound' ); ?></td>

								</tr><?

							} ?>

						</tbody>

					</table>

					<? do_action( 'ifound_campaign_schedule', false ); ?>

					<div class="title-heading default-criteria-heading"><i class="fal fa-bolt" aria-hidden="true"></i> <? _e( 'Instant Updates', 'ifound' ); ?></div>

					<table class="form-table">

						<tbody>
						<tr>

							<th scope="row"><? _e( 'Include Instant Updates:', 'ifound' ); ?></th>
							<td>
								<div>
									<? _e( 'Send an update via email/text when:', 'ifound' ); ?>
								</div>
								<div>
									<input type="checkbox" name="instant_update" id="instant_update" value="checked"/>
									<label for="instant_update"><? _e( 'new listings become available', 'ifound' ); ?></label>
								</div>
								<div>
									<input type="checkbox" name="instant_update_recently_closed" id="instant_update_recently_closed" value="checked"/>
									<label for="instant_update_recently_closed"><? _e( 'existing listings become closed', 'ifound' ); ?></label>
								</div>
							</td>

						</tr>
						</tbody>

					</table>

					<div class="edit-email-wrapper">
						<input type="hidden" name="save_type" value="market-update">

						<div class="default-criteria-heading"><i class="fal fa-envelope" aria-hidden="true"></i> <? _e( 'Email(s)', 'ifound' ); ?></div>

						<table class="form-table">

							<tbody>

								<tr>
									<th colspan="2">
										<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" id="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>" value="<?= iFoundSharedCampaign::$DO_EMAIL_YES ?>" checked>
										<label for="<?= iFoundSharedCampaign::$DO_EMAIL_KEY ?>"><? _e( 'Include email with this search', 'ifound' ); ?></label>
									</th>
								</tr>

								<tr>
									<th scope="row"><label for="<?= iFoundSharedCampaign::$TO_EMAIL_KEY ?>"><? _e( 'To Email(s):', 'ifound' ); ?></label></th>
									<td>
										<input type="hidden" name="<?= iFoundSharedCampaign::$TO_EMAIL_KEY ?>" id="<?= iFoundSharedCampaign::$TO_EMAIL_KEY ?>">
										<div class="to_emails_spinner hidden"><i class="fa fa-redo fa-spin"></i></div>
										<strong class="no_contact_selected_warning">No contact selected</strong>
										<div class="to_emails"></div>
										<strong class="no_email_addresses_checked_warning hidden">You must check at least one email address</strong>
									</td>
								</tr>

								<tr>
									<th scope="row"><label for="template_id"><? _e( 'Header:', 'ifound' ); ?></label></th>
									<td><? do_action( 'ifound_email_dropdown', 'header', 'header', $crm->header ); ?></td>
								</tr>

								<tr>

									<th scope="row"><label for="template_id"><? _e( 'Email Template:', 'ifound' ); ?></label></th>
									<td><? do_action( 'ifound_email_dropdown', 'content', 'content', false, $exclude ); ?></td>

								</tr>

								<tr>

									<th scope="row"><label for="email-subject"><? _e( 'Email Subject:', 'ifound' ); ?></label></th>
									<td><input type="text" id="email-subject" name="custom_subject" placeholder="Email Subject" class="ifound-validate regular-text email-validate"/></td>

								</tr>

								<tr>
									<th scope="row"><? _e( 'Email Body:', 'ifound' ); ?></th>
									<td>
										<div style="margin-bottom:20px;">
											<div style="display:inline-block;margin-right:20px;">
												<strong>Merge Tags</strong>
												<? $iFoundEmailObj->merge_tag_select( $iFoundEmailObj->default_email_tags() ); ?>
											</div>

											<div style="display:inline-block;">
												<strong>Add Activity Tracking Link</strong>
												<? $iFoundEmailObj->activity_tracking_link_input(); ?>
											</div>
										</div>
										<div class="ifound-wp-editor-wrapper">
											<?
											/** @link https://codex.wordpress.org/Function_Reference/wp_editor */
											$settings = ['textarea_name' => 'custom_content', 'wpautop' => false];
											wp_editor( '', 'ifound_email_editor', $settings );
											?>
										</div>
									</td>
								</tr>


								<tr>
									<th scope="row"><? _e( 'Signature:', 'ifound' ); ?></th>
									<td><? do_action( 'ifound_email_dropdown', 'signature', 'signature', $crm->signature ); ?></td>
								</tr>

								<tr>
									<th scope="row"><? _e( 'Footer:', 'ifound' ); ?></th>
									<td><? do_action( 'ifound_email_dropdown', 'footer', 'footer', $crm->footer ); ?></td>
								</tr>

							</tbody>

						</table>

						<? if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)): ?>
						<div class="title-heading default-criteria-heading"><i class="fal fa-comments" aria-hidden="true"></i> <? _e( 'Text Message(s)', 'ifound' ); ?></div>

						<div class="edit-sms-wrapper">
							<table class="form-table">
								<tbody>
								<tr>
									<th colspan="2">
										<div>
											<input type="checkbox" name="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" id="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>" value="<?= iFoundSharedCampaign::$DO_SMS_YES ?>">
											<label for="<?= iFoundSharedCampaign::$DO_SMS_KEY ?>"><? _e( 'Include text message(s) with this search', 'ifound' ); ?></label>
										</div>
										<div class="may_not_text_message"></div>
									</th>
								</tr>
								<tr>
									<th scope="row"><label for="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>"><? _e( 'To Mobile Phone(s):', 'ifound' ); ?></label></th>
									<td>
										<input type="hidden" name="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>" id="<?= iFoundSharedCampaign::$TO_SMS_KEY ?>">
										<div class="to_smss_spinner hidden"><i class="fa fa-redo fa-spin"></i></div>
										<strong class="no_contact_selected_warning">No contact selected</strong>
										<div class="to_smss"></div>
										<strong class="no_to_sms_checked_warning hidden">You must check at least one mobile phone</strong>
										<strong class="no_mphone_warning hidden">This contact does not have a mobile phone number</strong>
									</td>
								</tr>
								<tr>
									<th scope="row"><label for="sms_template"><? _e( 'Template', 'ifound' ); ?></label></th>
									<td>
										<select name="sms_template_id" id="sms_template_id" class="mphone-validate">
											<? foreach ($sms_templates as $sms_template): ?>
											<option value="<?= $sms_template['id'] ?>"><?= $sms_template['label'] ?></option>
											<? endforeach ?>
										</select>
										<div class="sms_template_body_wrapper mphone-validate">
											Message: <span class="sms_template_body">
												<?= $sms_templates[0]['body'] ?>
											</span>
										</div>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<? endif ?>

					</div>

					<div class="title-heading default-criteria-heading"><i class="fal fa-save" aria-hidden="true"></i> <? _e( 'Save', 'ifound' ); ?></div>

					<table class="form-table">

						<tbody>

							<tr>

								<td>

									<div class="button save-campaign button-primary">
										<i class="fal fa-plus-square" id="save-campaign-spinner" aria-hidden="true"></i>
										<? _e( 'Save Campaign', 'ifound' ); ?>
									</div>

									<? do_action( 'ifound_external_crm_share_button' ); ?>

								</td>

							</tr>

							<tr>

								<td colspan="2">

									<div class="success-msg"><? _e( 'Campaign Processed Successfully', 'ifound' ); ?></div>
                                    <div class="success-sub-msg"></div>

								</td>

							</tr>

							<tr>

								<td colspan="2">

									<div class="failed-msg"><? _e( 'There was an error processing your campaign. ', 'ifound' ); ?></div>

								</td>

							</tr>

						</tbody>

					</table>

				</form>

			</div>

		</div>

		<?
		iFoundContactsAdmin::new_hookless()->enqueue_contact_autosuggest();
	}

	/**
	 * Create Post Form
	 *
	 * A form used to collect data to create a blog post.
	 *
	 * @since 2.0.0
	 * @since 2.4.19 Add featured image box.
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 *
	 * @param object $post_form The data from the post form.
	 */

	public function create_post_form( $post_form ) {

		ob_start(); ?>

		<?
		// Only allow admins to create blog posts along with campaigns.
		if (apply_filters('ifound_current_user_can_create_posts', null)) {
        ?>
			<tr>

				<td colspan="2">

					<input type="checkbox" name="do_post" id="do_post" value="checked">

					<? _e( 'Create Blog Post with the Filters.', 'ifound' ); ?>

				</td>

			</tr>
		<?
		}
		?>

		<tr class="create-post">

			<th scope="row"><label for="post_title"><? _e( 'Blog Post Title:', 'ifound' ); ?></label></th>

			<td><input type="text" name="post_title" id="post_title" placeholder="i.e. Newer 3 Bedroom 2 Bath Homes on Corner Lots." class="search-title-input regular-text"></td>

		</tr>

		<tr class="create-post">

			<th scope="row"><label for="post_content"><? _e( 'Blog Post Content:', 'ifound' ); ?></label></th>

			<td><textarea name="post_content" id="post_content" rows="7" placeholder="Optional blog post content." class="search-content-input large-text"></textarea></td>

		</tr>

		<tr class="create-post">

			<th scope="row"><label for="featuredimagediv"><? _e( 'Featured Image:', 'ifound' ); ?></label></th>

			<td><? $this->featured_image_box(); ?></td>

		</tr>

		<tr class="create-post">

			<td colspan="2" class="title-msg"><? _e( '** The blog post is publicly viewable. DO NOT include personal information. **', 'ifound' ); ?></td>

		</tr><?

		return ob_get_clean();

	}

	/**
	 * Featured Image Box
	 *
	 * Adds a featured image to a campaign builder post.
	 *
	 * @since 2.4.19
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 *
	 * @link https://hugh.blog/2015/12/18/create-a-custom-featured-image-box/
	 */

	public function featured_image_box() {

		wp_enqueue_script( 'featured_image_js' );?>

		<div id="featuredimagediv">

			<img src="" style="width:300px;height:auto;border:0;display:none;" />

			<p class="hide-if-no-js">

				<a
					title="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>"
					href="javascript:;"
					id="upload_featured_image_button"
					id="set-featured-image"
					data-uploader_title="<? echo esc_attr__( 'Choose an image', 'ifound' ); ?>"
					data-uploader_button_text="<? echo esc_attr__( 'Set featured image', 'ifound' ); ?>"
					class="button button-primary"
				>
						<i class="far fa-images"></i>
						<? _e( 'Set featured image', 'ifound' ); ?>
				</a>

			</p>

			<input type="hidden" id="upload_featured_image" name="post_thumbnail" value="" />

		</div><?

	}

	/**
	 * Campaign Schedule
	 *
	 * Inputs for the campaihn schedule.
	 *
	 * @since 1.0.0
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 * @since 2.5.22 Do not allow start_date before today's date.
	 */

	public function campaign_schedule( $save_this_id = false ) {

		$recurring 		= null;
		$how_often 		= null;
		$time_of_day	= null;
		$start_date		= null;
		$start_date 	= null;
		$end_date 		= null;
		if( $save_this_id ) {
			$recurring 		= get_post_meta( $save_this_id, 'recurring', 	true );
			$how_often 		= get_post_meta( $save_this_id, 'how_often', 	true );
			$time_of_day	= get_post_meta( $save_this_id, 'time_of_day',  true );
			$start_date		= get_post_meta( $save_this_id, 'start_date',   true );
			$start_date 	= ( strtotime( $start_date ) <= $this->current_time() ) ? '' : $start_date;
			$end_date 		= get_post_meta( $save_this_id, 'end_date', 	true );
		}

		$yes = ( $recurring == 'yes' || empty( $recurring ) ) 	? 'checked' : '';
		$no  = ( $recurring == 'no' ) 							? 'checked' : ''; ?>

		<div class="default-criteria-heading"><i class="fal fa-calendar-alt" aria-hidden="true"></i> <? _e( 'Schedule', 'ifound' ); ?></div>

		<table class="form-table">

			<tbody>


				<tr>
					<th scope="row">
						<input type="radio" name="recurring" class="recurring" value="yes" <? echo $yes; ?>/>
						<label class="recurring-label"><? _e( 'Recurring', 'ifound' ); ?></label>
					</th>

					<th scope="row">
						<input type="radio" name="recurring" class="recurring" value="no" <? echo $no; ?>/>
						<label class="recurring-label"><? _e( 'Only Once', 'ifound' ); ?></label>
					</th>
				</tr>

				<tr>
					<th scope="row"><label for="how_often"><? _e( 'How Often?:', 'ifound' ); ?></label></th>
					<td>
						<? do_action( 'ifound_how_often_select', $how_often ); ?>
					</td>
				</tr>

				<tr>
					<th scope="row"><label for="time_of_day"><? _e( 'What Time?:', 'ifound' ); ?></label></th>
					<td>
						<? do_action( 'ifound_time_of_day_select', $time_of_day, array( 'now' => 'Send Now' ) ); ?>
					</td>
				</tr>

				<tr>

					<th scope="row"><label class="recurring"><? _e( 'Start Date:', 'ifound' ); ?></label></th>
					<td><input type="text" name="start_date" id="start_date" class="regular-text" value="<? echo $start_date; ?>"/></td>

					<th scope="row"><label class="recurring"><? _e( 'End Date:', 'ifound' ); ?></label></th>
					<td><input type="text" name="end_date" id="end_date" class="regular-text set-disable" value="<? echo $end_date; ?>"/></td>

				</tr>

			</tbody>

		</table><?

	}

	public function how_often_options() {
		$options = array(
			'Daily' 		=> 'Once a Day',
			'Twice Daily' 	=> 'Twice a Day',
			'3 Days' 		=> 'Every 3 Days',
			'Bi-Weekly' 	=> 'Twice a Week',
			'Weekly' 		=> 'Once a Week',
			'Bi-Monthly' 	=> 'Twice a Month',
			'Monthly' 		=> 'Once a Month',
			'Quarterly' 	=> 'Once a Quarter',
			'Bi-Annually' 	=> 'Twice a Year',
			'Annually' 		=> 'Once a Year'
		);
		return $options;
	}

	/**
	 * How Often Select
	 *
	 * @since 1.0.0
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 */

	public function how_often_select( $how_often ) {
		$options = apply_filters('ifound_how_often_options', []);
		?>

		<select name="how_often" id="how_often" class="set-disable"><?

			foreach( $options as $key => $value ) {

				$selected = ( $how_often == $key ) ? 'selected' : ''; ?>

				<option value="<? echo $key; ?>" <? echo $selected; ?>><? _e( $value, 'ifound' ); ?></option><?

			} ?>

		</select><?

	}

	/**
	 * Time of Day Select
	 *
	 * @since 1.0.0
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 */

	public function time_of_day_select( $time_of_day, $first_selection, $name = 'time_of_day' ) {

		$options = array_merge(
			$first_selection,
			array(
				'4:00:00' 	=> '4:00 am',
				'5:00:00' 	=> '5:00 am',
				'6:00:00' 	=> '6:00 am',
				'7:00:00' 	=> '7:00 am',
				'8:00:00' 	=> '8:00 am',
				'9:00:00' 	=> '9:00 am',
				'10:00:00' 	=> '10:00 am',
				'11:00:00' 	=> '11:00 am',
				'12:00:00' 	=> '12:00 pm',
				'13:00:00' 	=> '1:00 pm',
				'14:00:00' 	=> '2:00 pm',
				'15:00:00' 	=> '3:00 pm',
				'16:00:00' 	=> '4:00 pm',
				'17:00:00' 	=> '5:00 pm',
				'18:00:00' 	=> '6:00 pm',
				'19:00:00' 	=> '7:00 pm',
				'20:00:00' 	=> '8:00 pm',
				'21:00:00' 	=> '9:00 pm',
				'22:00:00' 	=> '10:00 pm',
				'23:00:00' 	=> '11:00 pm'
			)
		); ?>

		<select name="<? echo $name; ?>" id="time_of_day"><?

			foreach( $options as $key => $value ) {

				$selected = ( $time_of_day == $key ) ? 'selected' : ''; ?>

				<option value="<? echo $key; ?>" <? echo $selected; ?>><? _e( $value, 'ifound' ); ?></option><?

			} ?>

		</select><?

	}

	/**
	 * Choose MLS IDs
	 *
	 * @since 3.6.1
	 */

	public function choose_mls_ids() { ?>

		<div class="choose-mls-ids-wrapper">

			<div class="ifound-wrap">

				<input type="checkbox" class="choose-mls-ids"/><? _e( 'I want to choose the listings to include with this search.', 'ifound' ); ?>

			</div>

		</div><?

	}

	/**
	 * Campaign Builder Post
	 *
	 * Publishes the post with the Campaign Builder search criteria.
	 *
	 * @uses wp_set_post_categories()
	 * @link https://codex.wordpress.org/Function_Reference/wp_set_post_categories
	 *
	 * @since 1.3.0
	 * @since 2.4.16 Bug fix - change data key 'params' to 'query'. Update meta name to save_this_shortcode.
	 * @since 2.5.0  Move to iFOUND_campaign_builder class.
	 * @since 4.1.10 Move our Buffer conditions. If we want to stop Buffer from sending. We must schedule post for later.
	 *
	 * @param object $input  The input data from the campaign builder form.
	 * @param object $params The mls search params.
	 */

	public function campaign_builder_post( $input, $params ) {

		if (!apply_filters('ifound_current_user_can_create_posts', null)) {
		    return;
		}
	 	if( $input->do_post != 'checked' ) return;

		$post_title = sanitize_text_field( $input->post_title );

		$crm = $this->crm();

		// This is for Buffer.
		$post_status = ( $input->do_buffer == 'checked' ) ? 'future' : 'publish';

		$post_time 	= apply_filters( 'ifound_current_time', ( 4 + time() ) );
		$date 		= array( 'post_date' => date( 'Y-m-d H:i:s', $post_time ) );
		$post_date 	= ( $input->do_buffer == 'checked' ) ? $date : array();

		$my_post = array_merge(
			array(
				'post_status'   => $post_status,
				'post_title'    => $post_title,
				'post_author' 	=> $crm->campaign_post_author,
				'post_type'     => 'somepost', // Fake post type so buffer doesn't pick up and pushes it right away
			),
			$post_date
		);

		// Insert the post into the database
		$post_id = wp_insert_post( $my_post );

		if( isset( $input->post_thumbnail ) ) {
			$thumbnail_id = intval( $input->post_thumbnail );
			set_post_thumbnail( $post_id, $thumbnail_id );
		}

		$post_content = isset( $input->post_content ) ? $input->post_content : '';
		$data['query'] = $params ;
		$post_meta_id = add_post_meta( $post_id, 'save_this_shortcode', $data );

		$update_post = array(
      			'ID'           => $post_id,
      			'post_content' => $post_content . '[ifound id=' . $post_meta_id . ']',
			'post_type'    => 'post',
		);

		// Update the post into the database
  		wp_update_post( $update_post );

		/** Set post category by ID */
		wp_set_post_categories( $post_id, $crm->campaign_post_category_id );

		$this->do_buffer( $input->do_buffer, $post_id );
	}

	/**
	 * Do Buffer
	 *
	 * Add post_meta to stop the post from sharing.
	 *
	 * @since 4.1.10
	 *
	 * @param string $do_buffer The value of the do_buffer checkbox.
	 * @param int    $post_id   The ID of the blog post.
	 */

	public function do_buffer( $do_buffer, $post_id ) {

		if( class_exists( 'WP_To_Buffer_Pro' ) && $do_buffer != 'checked' ) {

			$meta = get_post_meta( $post_id, 'wp-to-buffer-pro', true ) ?: array();

			$meta['override'] = '-1';

			update_post_meta( $post_id, 'wp-to-buffer-pro', $meta );

		}

	}

	public function make_start_campaign_url($post, $type = 'campaign-builder') {
		$url = menu_page_url($type, false);
		$url = add_query_arg('contact_id', $post->ID, $url);
		return $url;
	}
}
