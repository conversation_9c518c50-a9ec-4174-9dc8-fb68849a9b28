<?php
require_once("classes/class-auth.php");
require_once("classes/class-query_db.php");
require_once("classes/class-parse_seo.php");
require_once("classes/class-sanitizer.php");



//Create an array of the form information
while (list($key,$val)=each($_REQUEST)){ // HTTP_GET_VARS
	if(isset($query_info[$key])){
		$query_info[$key] = NULL;
	}
	$query_info[$key] = $val;
}

// Process The Form Cleaner (you can't trust anyone these days)
$sanitizer_obj = & new sanitizer;
$query_info = $sanitizer_obj->cleandata( $query_info ); //1.7



$auth_db = new Auth();
$db_obj = new sql_query();
$seo_obj = new SEO_Parse();



// Get authorization or die
$access_array = $auth_db->authenticate($query_info);




/**
 * @TODO: This needs to return a list of neighborhoods
 */




// Insert the SEO info
// This is going to take some work to get the city and category into the mapping
$seo_array['title'] = $seo_obj->parse_seomap($prop_array, $access_array['meta_cat_title']); 
$seo_array['h1'] = $seo_obj->parse_seomap($prop_array, $access_array['meta_cat_h1']); 
$seo_array['description'] = $seo_obj->parse_seomap($prop_array, $access_array['meta_cat_description']); 
$seo_array['keywords'] = $seo_obj->parse_seomap($prop_array, $access_array['meta_cat_keywords']); 

$prop_array['seo'] = $seo_array;



// Send out the results (if there are any)
if(is_array($prop_array)){
	echo json_encode($prop_array);
}
?>