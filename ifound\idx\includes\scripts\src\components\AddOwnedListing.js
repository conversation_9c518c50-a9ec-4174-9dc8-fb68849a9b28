import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import axios from 'axios';
import classnames from 'classnames';
import useFetcher from '../hooks/useFetcher';
import { makeAddress } from '../lib/address';

function AddOwnedListing(props) {
	const [listingIdToAdd, setListingIdToAdd] = useState('');

	const {
		isLoading: isFetchingAdd,
		errorMessage: errorMessageForAdd,
		isSuccess: isSuccessForAdd,
		run: add,
	} = useFetcher({
		fetchFn: () => axios({
			url: props.endpoint,
			method: 'POST',
			params: {
				// Reminder: This is required because it's the only way I know of that authenticates the user,
				// such that in Wordpress the current_user_can('edit_posts') returns true. Otherwise, the user ID is 0.
				// The docs at https://codex.wordpress.org/WordPress_Nonces
				// say "Nonces should never be relied on for authentication, authorization, or access control.
				// Protect your functions using current_user_can(), and always assume nonces can be compromised."
				// But I'm not sure how else to do it. The problem as I see it is that we are posting to the
				// /wp-json/ route, but the admin cookie's path is for wp-admin/, so it's not sent.
				// My best guess is that there is another cookie that IS sent, it's called e.g.
				// wordpress_logged_in_[HASH].
				// Perhaps that value ALONG with the nonce is what WP uses for auth.
				_wpnonce: props.nonce,
			},
			data: ({
				mls_id: listingIdToAdd,
			}),
		})
			.then(response => {
				const data = response.data;
				const ownedListing = data.owned_listing;
				setListingIdToAdd('');
				const paredOwnedListing = {
					mls_id: ownedListing.data.ListingID,
					address: makeAddress(ownedListing.data),
					lat: ownedListing.data.Latitude,
					lng: ownedListing.data.Longitude,
				};
				props.onAdd(ownedListing, paredOwnedListing);
			})
	})

	return <div>
		<div>
			<input
				type="text"
				value={listingIdToAdd}
				onChange={e => setListingIdToAdd(e.target.value)}
				placeholder="MLS ID"
				disabled={isFetchingAdd}
			/>
			{' '}
			<button type="button" className="button button-primary" onClick={add} disabled={!listingIdToAdd || isFetchingAdd}>
				<i className={classnames('fal', {
					'fa-plus-square': !isFetchingAdd,
					'fa-spinner': isFetchingAdd,
					'fa-spin': isFetchingAdd,
				})}></i>
				{' '}
				Add listing
			</button>
		</div>
		<div>
			{isSuccessForAdd && <div className="success">Successfully added MLS ID</div>}
		</div>
		<div>
			{errorMessageForAdd && <div className="error">Error adding Owned Listing: {errorMessageForAdd}</div>}
		</div>
	</div>
}

AddOwnedListing.propTypes = {
	endpoint: PropTypes.string,
	nonce: PropTypes.string.isRequired,
	onAdd: PropTypes.func,
};

export default AddOwnedListing;
