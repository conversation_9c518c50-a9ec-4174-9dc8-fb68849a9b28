<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

class iFoundCrmAdmin extends iFoundCrm{
	private static $stats_metabox_id = 'ifound_campaign_stats';
	private static $meta_box_order_dashboard = 'meta-box-order_dashboard';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('wp_dashboard_setup', array($this, 'activity_metabox'));
			add_filter('get_user_option_' . static::$meta_box_order_dashboard, [$this,
				'get_user_option_meta_box_order_dashboard'], 10, 3);
		}
	}

	public function activity_metabox() {
		wp_add_dashboard_widget(
			static::$stats_metabox_id,
			'Campaign Stats',
			[$this, 'stats_widget']
		);
	}

	public function stats_widget() {
		?>
		<div>Over the last 30 days</div>
		<div style="margin-top: 10px;">Email</div>
		<?php
		iFoundEmailTrackingPixel::new_hookless()->display_stats();

		if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)):
		?>
		<div style="margin-top: 10px;">Text messages</div>
		<?php
		iFoundSms::new_hookless()->display_stats();
		endif;
	}

	// Put our widget at the top, if it hasn't been put there before.
	public function get_user_option_meta_box_order_dashboard($orders, $option_name, $user) {
		if ($orders === false) {
			$orders = ['normal' => static::$stats_metabox_id];
			return $orders;
		}
		// Only rearrange if we haven't rearranged before.
		$transient_name = 'ifound_rearrange_dashboard_widgets_2024_07_08';
		if (!$this->util()->get_transient_for_user_id($user->ID, $transient_name)) {
			// Delete this old attempt at this, which was by CRM ID.
			$old_transient_name = 'ifound_rearrange_dashboard_widgets_2024_06_27';
			$this->delete_transient_for_agent($old_transient_name);

			$to_remove = [
				// We're deleting this because I had a bug before where the widget name was put multiple times, which
				// ends up showing the widget 0 times. It'll be re-added below.
				static::$stats_metabox_id,

				'ifound_email_stats',
				'ifound_latest_email_activity',
				'ifound_latest_website_activity',
			];
			foreach ($orders as $key => $order) {
				// Remove our old widgets.
				$orders[$key] = join(',', array_filter(explode(',', $order), function($widget_id) use ($to_remove) {
					return !in_array($widget_id, $to_remove, true);
				}));
			}
			// Set forever
			$this->util()->set_transient_for_user_id($user->ID, $transient_name, 'x');
			$orders['normal'] = static::$stats_metabox_id . ',' . $orders['normal'];
			update_user_meta($user->ID, static::$meta_box_order_dashboard, $orders);
		}

		return $orders;
	}
}
