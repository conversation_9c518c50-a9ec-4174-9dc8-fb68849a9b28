-- MySQL dump 10.13  Distrib 5.1.62, for debian-linux-gnu (x86_64)
--
-- Host: *************    Database: pfndidx_azdb
-- ------------------------------------------------------
-- Server version	5.1.53-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `property_a_fields`
--

DROP TABLE IF EXISTS `property_a_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `property_a_fields` (
  `field_id` int(11) NOT NULL AUTO_INCREMENT,
  `mls` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `SystemName` text COLLATE utf8_unicode_ci NOT NULL,
  `StandardName` text COLLATE utf8_unicode_ci NOT NULL,
  `LongName` text COLLATE utf8_unicode_ci NOT NULL,
  `DBName` text COLLATE utf8_unicode_ci NOT NULL,
  `ShortName` text COLLATE utf8_unicode_ci NOT NULL,
  `MaximumLength` text COLLATE utf8_unicode_ci NOT NULL,
  `DataType` text COLLATE utf8_unicode_ci NOT NULL,
  `Precision` text COLLATE utf8_unicode_ci NOT NULL,
  `Searchable` text COLLATE utf8_unicode_ci NOT NULL,
  `Interpretation` text COLLATE utf8_unicode_ci NOT NULL,
  `Alignment` text COLLATE utf8_unicode_ci NOT NULL,
  `UseSeparator` text COLLATE utf8_unicode_ci NOT NULL,
  `EditMaskID` text COLLATE utf8_unicode_ci NOT NULL,
  `LookupName` text COLLATE utf8_unicode_ci NOT NULL,
  `MaxSelect` text COLLATE utf8_unicode_ci NOT NULL,
  `Units` text COLLATE utf8_unicode_ci NOT NULL,
  `Index` text COLLATE utf8_unicode_ci NOT NULL,
  `Minimum` text COLLATE utf8_unicode_ci NOT NULL,
  `Maximum` text COLLATE utf8_unicode_ci NOT NULL,
  `Default` text COLLATE utf8_unicode_ci NOT NULL,
  `Required` text COLLATE utf8_unicode_ci NOT NULL,
  `SearchHelpID` text COLLATE utf8_unicode_ci NOT NULL,
  `Unique` text COLLATE utf8_unicode_ci NOT NULL,
  `MetadataEntryID` text COLLATE utf8_unicode_ci NOT NULL,
  `ModTimeStamp` text COLLATE utf8_unicode_ci NOT NULL,
  `ForeignKeyName` text COLLATE utf8_unicode_ci NOT NULL,
  `ForeignField` text COLLATE utf8_unicode_ci NOT NULL,
  `InKeyIndex` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`field_id`),
  KEY `MLS` (`mls`)
) ENGINE=MyISAM AUTO_INCREMENT=2284 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `property_lookup_opts`
--

DROP TABLE IF EXISTS `property_lookup_opts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `property_lookup_opts` (
  `lookup_id` int(11) NOT NULL AUTO_INCREMENT,
  `mls` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `LookupName` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `MetadataEntryID` text COLLATE utf8_unicode_ci NOT NULL,
  `Value` text COLLATE utf8_unicode_ci NOT NULL,
  `ShortValue` text COLLATE utf8_unicode_ci NOT NULL,
  `LongValue` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`lookup_id`),
  KEY `MLS` (`mls`)
) ENGINE=MyISAM AUTO_INCREMENT=118332 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2012-05-12 14:05:47
