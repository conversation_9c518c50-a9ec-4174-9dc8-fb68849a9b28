import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { promises as fs } from 'fs'
import path from 'path'
import { getJsonFiles, readJsonFile, deleteJsonFile, processJsonFiles } from '../fileProcessor.js'

// Mock fs
vi.mock('fs', () => ({
	promises: {
		readdir: vi.fn(),
		readFile: vi.fn(),
		unlink: vi.fn(),
		mkdir: vi.fn(),
	},
}))

const mockFs = vi.mocked(fs)

describe('fileProcessor', () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	describe('getJsonFiles', () => {
		it('should return sorted JSON files', async () => {
			mockFs.readdir.mockResolvedValue([
				'2024-01-03-sync.json',
				'2024-01-01-sync.json',
				'not-json.txt',
				'2024-01-02-sync.json',
			] as any)

			const result = await getJsonFiles('/test/data')

			expect(result).toEqual([
				'/test/data/2024-01-01-sync.json',
				'/test/data/2024-01-02-sync.json',
				'/test/data/2024-01-03-sync.json',
			])
		})

		it('should create directory if it does not exist', async () => {
			const error = new Error('ENOENT') as NodeJS.ErrnoException
			error.code = 'ENOENT'
			mockFs.readdir.mockRejectedValue(error)

			const result = await getJsonFiles('/test/data')

			expect(mockFs.mkdir).toHaveBeenCalledWith('/test/data', { recursive: true })
			expect(result).toEqual([])
		})

		it('should rethrow non-ENOENT errors', async () => {
			const error = new Error('Permission denied')
			mockFs.readdir.mockRejectedValue(error)

			await expect(getJsonFiles('/test/data')).rejects.toThrow('Permission denied')
		})
	})

	describe('readJsonFile', () => {
		it('should read and parse valid JSON with value array', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ListPrice: 350000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					},
					{
						ListingId: 'LISTING456',
						StandardStatus: 'Pending',
						ListPrice: 425000,
						ModificationTimestamp: '2024-01-01T13:30:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')

			expect(result).toHaveLength(2)
			expect(result[0]).toEqual({
				ListingId: 'LISTING123',
				StandardStatus: 'Active',
				Price: 350000,
				ModificationTimestamp: new Date('2024-01-01T12:00:00Z')
			})
			expect(result[1]).toEqual({
				ListingId: 'LISTING456',
				StandardStatus: 'Pending',
				Price: 425000,
				ModificationTimestamp: new Date('2024-01-01T13:30:00Z')
			})
			expect(mockFs.readFile).toHaveBeenCalledWith('/test/file.json', 'utf-8')
		})

		it('should throw error for JSON without value property', async () => {
			const jsonData = JSON.stringify({ listings: ['LISTING123'] })
			mockFs.readFile.mockResolvedValue(jsonData)

			await expect(readJsonFile('/test/file.json')).rejects.toThrow(
				'JSON file must contain an object with a "value" property'
			)
		})

		it('should throw error for value property that is not array', async () => {
			const jsonData = JSON.stringify({ value: 'not an array' })
			mockFs.readFile.mockResolvedValue(jsonData)

			await expect(readJsonFile('/test/file.json')).rejects.toThrow(
				'The "value" property must be an array of listing records'
			)
		})

		it('should throw error for record missing ListingId', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						StandardStatus: 'Active',
						ListPrice: 350000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			await expect(readJsonFile('/test/file.json')).rejects.toThrow(
				'Record at index 0 must have a valid ListingId string'
			)
		})

		it('should throw error for record missing StandardStatus', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						ListPrice: 350000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			await expect(readJsonFile('/test/file.json')).rejects.toThrow(
				'Record at index 0 must have a valid StandardStatus string'
			)
		})

		it('should skip record missing ListPrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should throw error for invalid ModificationTimestamp', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ListPrice: 350000,
						ModificationTimestamp: 'invalid-date'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			await expect(readJsonFile('/test/file.json')).rejects.toThrow(
				'Record at index 0 has invalid ModificationTimestamp format'
			)
		})

		it('should use ClosePrice when StandardStatus is Closed', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Closed',
						ListPrice: 350000,
						ClosePrice: 340000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')

			expect(result).toHaveLength(1)
			expect(result[0]).toEqual({
				ListingId: 'LISTING123',
				StandardStatus: 'Closed',
				Price: 340000, // Should use ClosePrice, not ListPrice
				ModificationTimestamp: new Date('2024-01-01T12:00:00Z')
			})
		})

		it('should skip Closed record without ClosePrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Closed',
						ListPrice: 350000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should skip record with zero ListPrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ListPrice: 0,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should skip record with null ListPrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ListPrice: null,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should skip Closed record with zero ClosePrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Closed',
						ListPrice: 350000,
						ClosePrice: 0,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should skip Closed record with null ClosePrice', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Closed',
						ListPrice: 350000,
						ClosePrice: null,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toEqual([]) // Record should be skipped
		})

		it('should process mixed records and skip only invalid prices', async () => {
			const jsonData = JSON.stringify({
				value: [
					{
						ListingId: 'LISTING123',
						StandardStatus: 'Active',
						ListPrice: 350000,
						ModificationTimestamp: '2024-01-01T12:00:00Z'
					},
					{
						ListingId: 'LISTING456',
						StandardStatus: 'Active',
						ListPrice: 0, // Should be skipped
						ModificationTimestamp: '2024-01-01T13:00:00Z'
					},
					{
						ListingId: 'LISTING789',
						StandardStatus: 'Closed',
						ListPrice: 425000,
						ClosePrice: 420000,
						ModificationTimestamp: '2024-01-01T14:00:00Z'
					}
				]
			})
			mockFs.readFile.mockResolvedValue(jsonData)

			const result = await readJsonFile('/test/file.json')
			expect(result).toHaveLength(2) // Only valid records processed
			expect(result[0].ListingId).toBe('LISTING123')
			expect(result[0].Price).toBe(350000)
			expect(result[1].ListingId).toBe('LISTING789')
			expect(result[1].Price).toBe(420000) // ClosePrice used
		})

		it('should throw error for invalid JSON', async () => {
			mockFs.readFile.mockResolvedValue('{ invalid json }')

			await expect(readJsonFile('/test/file.json')).rejects.toThrow('Invalid JSON format:')
		})
	})

	describe('deleteJsonFile', () => {
		it('should delete file successfully', async () => {
			mockFs.unlink.mockResolvedValue(undefined)

			await expect(deleteJsonFile('/test/file.json')).resolves.toBeUndefined()
			expect(mockFs.unlink).toHaveBeenCalledWith('/test/file.json')
		})

		it('should not throw error if file does not exist', async () => {
			const error = new Error('ENOENT') as NodeJS.ErrnoException
			error.code = 'ENOENT'
			mockFs.unlink.mockRejectedValue(error)

			await expect(deleteJsonFile('/test/file.json')).resolves.toBeUndefined()
		})

		it('should rethrow non-ENOENT errors', async () => {
			const error = new Error('Permission denied')
			mockFs.unlink.mockRejectedValue(error)

			await expect(deleteJsonFile('/test/file.json')).rejects.toThrow('Permission denied')
		})
	})

	describe('processJsonFiles', () => {
		it('should process all files successfully', async () => {
			// Mock getJsonFiles
			mockFs.readdir.mockResolvedValue(['file1.json', 'file2.json'] as any)

			// Mock readJsonFile
			const mockRecord1 = {
				ListingId: 'LISTING123',
				StandardStatus: 'Active',
				ListPrice: 350000,
				ModificationTimestamp: '2024-01-01T12:00:00Z'
			}
			const mockRecord2 = {
				ListingId: 'LISTING456',
				StandardStatus: 'Pending',
				ListPrice: 425000,
				ModificationTimestamp: '2024-01-01T13:30:00Z'
			}
			const mockRecord3 = {
				ListingId: 'LISTING789',
				StandardStatus: 'Sold',
				ListPrice: 475000,
				ModificationTimestamp: '2024-01-01T14:00:00Z'
			}

			mockFs.readFile
				.mockResolvedValueOnce(JSON.stringify({ value: [mockRecord1] }))
				.mockResolvedValueOnce(JSON.stringify({ value: [mockRecord2, mockRecord3] }))

			// Mock deleteJsonFile
			mockFs.unlink.mockResolvedValue(undefined)

			const mockProcessor = vi.fn().mockResolvedValue(undefined)

			const results = await processJsonFiles('/test/data', mockProcessor)

			expect(results).toHaveLength(2)

			expect(results[0]).toEqual({
				filename: 'file1.json',
				records: [{
					ListingId: 'LISTING123',
					StandardStatus: 'Active',
					Price: 350000,
					ModificationTimestamp: new Date('2024-01-01T12:00:00Z')
				}],
				processed: true,
			})

			expect(results[1]).toEqual({
				filename: 'file2.json',
				records: [
					{
						ListingId: 'LISTING456',
						StandardStatus: 'Pending',
						Price: 425000,
						ModificationTimestamp: new Date('2024-01-01T13:30:00Z')
					},
					{
						ListingId: 'LISTING789',
						StandardStatus: 'Sold',
						Price: 475000,
						ModificationTimestamp: new Date('2024-01-01T14:00:00Z')
					}
				],
				processed: true,
			})

			expect(mockProcessor).toHaveBeenCalledTimes(2)
			expect(mockFs.unlink).toHaveBeenCalledTimes(2)
		})

		it('should throw error immediately and not delete files when processing fails', async () => {
			// Mock getJsonFiles
			mockFs.readdir.mockResolvedValue(['file1.json'] as any)

			// Mock readJsonFile
			const mockRecord = {
				ListingId: 'LISTING123',
				StandardStatus: 'Active',
				ListPrice: 350000,
				ModificationTimestamp: '2024-01-01T12:00:00Z'
			}
			mockFs.readFile.mockResolvedValue(JSON.stringify({ value: [mockRecord] }))

			const mockProcessor = vi.fn().mockRejectedValue(new Error('Processing failed'))

			// Should throw immediately instead of returning error results
			await expect(processJsonFiles('/test/data', mockProcessor)).rejects.toThrow(
				'Failed to process file file1.json: Processing failed'
			)

			expect(mockFs.unlink).not.toHaveBeenCalled()
		})

		it('should throw error immediately when file reading fails', async () => {
			// Mock getJsonFiles
			mockFs.readdir.mockResolvedValue(['invalid.json'] as any)

			// Mock readJsonFile error
			mockFs.readFile.mockRejectedValue(new Error('File read error'))

			const mockProcessor = vi.fn()

			// Should throw immediately instead of returning error results
			await expect(processJsonFiles('/test/data', mockProcessor)).rejects.toThrow(
				'Failed to process file invalid.json: File read error'
			)

			expect(mockProcessor).not.toHaveBeenCalled()
			expect(mockFs.unlink).not.toHaveBeenCalled()
		})

		it('should skip processing for empty files but still delete them', async () => {
			// Mock getJsonFiles
			mockFs.readdir.mockResolvedValue(['empty.json'] as any)

			// Mock readJsonFile
			mockFs.readFile.mockResolvedValue(JSON.stringify({ value: [] }))

			// Mock deleteJsonFile
			mockFs.unlink.mockResolvedValue(undefined)

			const mockProcessor = vi.fn()

			const results = await processJsonFiles('/test/data', mockProcessor)

			expect(results).toHaveLength(1)
			expect(results[0]).toEqual({
				filename: 'empty.json',
				records: [],
				processed: true,
			})

			expect(mockProcessor).not.toHaveBeenCalled()
			expect(mockFs.unlink).toHaveBeenCalledWith(path.join('/test/data', 'empty.json'))
		})
	})
})
