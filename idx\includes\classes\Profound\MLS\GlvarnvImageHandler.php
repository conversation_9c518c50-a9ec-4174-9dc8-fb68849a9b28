<?php

namespace Profound\MLS;

class GlvarnvImageHandler extends ImageHandler {
	static $mlsname = "glvarnv";

	protected function getDbTable() {
		return "glvarnv_property_1";
	}

	protected function getImageCountField() {
		return "PhotoCount";
	}

	protected function getListingKey() {
		return "Matrix_Unique_ID";
	}

	protected function getImagePath($listingId, $imageNumber, $suffix = '') {
		return "http://glvarnv-images.s3.amazonaws.com/" . $this->getImageFilename($listingId, $imageNumber, $suffix);
	}

	function getImageFilename($listingId, $imageNumber, $suffix = '') {
		return "IMG-{$listingId}_{$imageNumber}{$suffix}.jpg";
	}

	public function getImagePathsList($listing_id) {
		$dbTable = $this->getDbTable();

		$imageCountField = $this->getImageCountField();
		$listingKey = $this->getListingKey();

		$sql = "SELECT `$imageCountField` FROM $dbTable WHERE `$listingKey` = $listing_id";

		$result = $this->getDb()->fetchAll($sql);
		$count = $result[0][$imageCountField];
		$imagesArray = array();
		for ($i = 1; $i <= $count; $i++) {
			$imgValues = array();
			$imgValues['img_url'] = $this->getImagePath($listing_id, $i);
			$imgValues['normal_url'] = $this->getImagePath($listing_id, $i);
			$imgValues['thumbnail_url'] = $this->getThumbnailImagePath($listing_id, $i);
			$imgValues['highres_url'] = $this->getImagePath($listing_id, $i);
			$imgValues['description'] = "";
			$imagesArray[] = $imgValues;
		}
		return $imagesArray;
	}
}
