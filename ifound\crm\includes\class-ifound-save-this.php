<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

/**
 * Save This Class
 *
 * @since 1.0.0
 */

class iFoundSaveThis extends iFoundCrm {
	use NewHooklessTrait;
	use UtilTrait;

	public static $the_post_type = 'save_this';
	public static $the_taxonomy  = 'save_type';
	public static $the_taxonomy2 = 'campaign_status';

	protected $post_type;
	protected $taxonomy;
	protected $taxonomy2;
	protected $taxonomy_title	= 'Campaign Type';
	protected $taxonomy2_title	= 'Campaign Status';
	protected $label_name 		= 'Saved Campaign';
	protected $label_names		= 'Saved Campaigns';
	protected $taxonomys 		= array( 'save_type', 'campaign_status' );

	// Track when the campaign was enabled or re-enabled. It will not exist for campaigns created before this commit,
	// except if the campaign was disabled and then re-enabled after deploying this.
	public static $ENABLED_DATETIME_KEY = 'ifound_enabled_datetime_gmt';
	public static $stats_default = [
		'show' => 'default',
		'fields' => [],
	];

	/**
	 * init iFOUND_save_this class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$this->post_type = static::$the_post_type;
		$this->taxonomy = static::$the_taxonomy;
		$this->taxonomy2 = static::$the_taxonomy2;

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			/** WP functions for custom post type */
			add_action('init', array($this, 'post_type'));
			add_action('init', array($this, 'taxonomy'));
			add_action('init', array($this, 'taxonomy2'));
			add_action('wp_enqueue_scripts', array($this, 'scripts'));
			add_action('wp_ajax_update_this', array($this, 'update_this'));
			add_action('wp_ajax_nopriv_update_this', array($this, 'update_this'));
			add_action('wp_ajax_remove_this', array($this, 'remove_this'));
			add_action('wp_ajax_nopriv_remove_this', array($this, 'remove_this'));
			add_action('ifound_save_this_button', array($this, 'save_this_button'), 10, 3);
			add_action('ifound_update_this_button', array($this, 'update_this_button'), 10, 1);
			add_action('ifound_client_profile_title', array($this, 'client_profile_title'));
			add_action('ifound_client_profile', array($this, 'client_profile'));
			add_filter('ifound_next_time', array($this, 'next_time'));
			add_filter('ifound_next_time_data', array($this, 'next_time_data'));
			add_action('wp_ajax_update_campaign', array($this, 'update_campaign'));
			add_action('wp_ajax_nopriv_update_campaign', array($this, 'update_campaign'));
			add_action('ifound_update_campaign_status', array($this, 'update_campaign_status'), 10, 3);
			add_action('rest_api_init', array($this, 'register_save_this_routes'));
		}
	}

	/**
	 * Scripts
	 *
	 * @since 1.0.0
	 * @since 1.0.39 Use iFOUND_PLUGIN_VERSION for JS version.
	 */

	public function scripts() {

		wp_register_script( 'update_campaign_js', plugins_url( 'js/update-campaign.js', __FILE__  ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'update_campaign_js', 'update_campaign', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'update_campaign_secure_me' )
		));

		wp_register_script( 'remove_this_js', plugins_url( 'js/remove-this.js', __FILE__  ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'remove_this_js', 'remove_this', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'remove_this_secure_me' )
		));

		wp_register_script( 'save_this_js', plugins_url( 'js/save-this.js', __FILE__  ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'save_this_js', 'save_this', array(
			'endpoint' 				=> site_url( 'wp-json/ifound/' . iFOUND_PLUGIN_VERSION ),
			'nonce' 				=> wp_create_nonce( 'save_this_secure_me' ),
			'contact_id'			=> iFoundCrm::get_contact_id(),
			'contact_id_endpoint' 	=> site_url( '/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/contact_id/' . wp_create_nonce( 'cookie_to_contact_id_secure_me' ) . '/' ),
			// I'm commenting this out because we don't need it. But I'm keeping the comment for a reminder about the
			// 'nested' concept.
			// Wow. We need to nest to allow legit booleans.
			// See: https://developer.wordpress.org/reference/functions/wp_localize_script/#comment-6291
			// 'nested' => [
			// 	'is_monetization_project' => $this->util()->is_monetization_project(),
			// ],
		));

		wp_register_script( 'update_this_js', plugins_url( 'js/update-this.js', __FILE__  ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'update_this_js', 'update_this', array(
			'endpoint' 				=> admin_url( 'admin-ajax.php' ),
			'nonce' 				=> wp_create_nonce( 'update_this_secure_me' )
		));
	}

	/**
	 * Post Type
	 *
	 * @since 1.0.0
	 * @since Remove Add New button from admin pages.
	 *
	 * @link https://codex.wordpress.org/Function_Reference/register_post_type
	 */

	public function post_type() {

		$capabilities = $this->is_site_admin() ? $this->admin_capabilities() : $this->team_capabilities();

		register_post_type( $this->post_type,
			array_merge( array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new'		=> __( 'Add New ' . $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' )
				),
				'has_archive' 			=> false,
				'query_var'         	=> true,
				'public' 				=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'show_ui'				=> true,
				'show_in_menu' 			=> $this->crm_menu(),
				'supports'				=> array( 'title' ),
			), $capabilities )
	  	);

	}

	private function team_capabilities() {

		return array(
			'capability_type'		=> $this->post_type,
			'capabilities' 			=> array(
				'edit_post'          	=> 'edit_save_this',
				'read_post'          	=> 'read_save_this',
				'delete_post'        	=> 'delete_save_thiss',
				'edit_posts'         	=> 'edit_save_thiss',
				'edit_others_posts'  	=> 'edit_others_save_thiss',
				'publish_posts'      	=> 'publish_save_thiss',
				'create_posts'       	=> 'do_not_allow'
			)
		);

	}

	private function admin_capabilities() {

		return array(
			'capability_type' 		=> 'post',
  			'capabilities' 			=> array(
    			'create_posts' 		=> 'do_not_allow',
  			),
  			'map_meta_cap' 			=> true
		);

	}

	/**
	 *	Taxonomy
	 *
	 *	@since 1.0.0
	 *
	 *	@link https://codex.wordpress.org/Function_Reference/register_taxonomy
	 */

	public function taxonomy() {

		$labels = array(
			'name'              => _x( $this->taxonomy_title, iFound::$default_context, 'ifound' ),
			'singular_name'     => _x( $this->taxonomy_title, iFound::$default_context, 'ifound' ),
			'search_items'      => __( 'Search ' . $this->taxonomy_title . 's', 'ifound' ),
			'all_items'         => __( 'All ' . $this->taxonomy_title . 's', 'ifound' ),
			'parent_item'       => __( 'Parent ' . $this->taxonomy_title, 'ifound' ),
			'parent_item_colon' => __( 'Parent ' . $this->taxonomy_title, 'ifound' ),
			'edit_item'         => __( 'Edit ' . $this->taxonomy_title, 'ifound' ),
			'update_item'       => __( 'Update ' . $this->taxonomy_title, 'ifound' ),
			'add_new'           => __( 'Add New ' . $this->taxonomy_title, 'ifound' ),
			'add_new_item'      => __( 'Add New ' . $this->taxonomy_title, 'ifound' ),
			'new_item_name'     => __( 'New ' . $this->taxonomy_title, 'ifound' ),
			'menu_name'         => __( $this->taxonomy_title, 'ifound' ),
		);

		$args = array(
			'labels'            => $labels,
			'hierarchical'      => true,
			'show_ui'           => true,
			'show_in_menu'		=> false,
			'show_admin_column' => true,
			'show_in_nav_menus'	=> false,
			'query_var'         => true,
			'public' 			=> false,
			'show_tagcloud'		=> false,
            'rewrite' 			=> false,
            'capabilities'      => array(
				'edit_terms'   => 'god',
				'manage_terms' => 'god',
			)
		);

		register_taxonomy( $this->taxonomy, array( $this->post_type ), $args );

	}

	public function taxonomy2() {

		$labels = array(
			'name'              => _x( $this->taxonomy2_title, iFound::$default_context, 'ifound' ),
			'singular_name'     => _x( $this->taxonomy2_title, iFound::$default_context, 'ifound' ),
			'search_items'      => __( 'Search ' . $this->taxonomy2_title . 's', 'ifound' ),
			'all_items'         => __( 'All ' . $this->taxonomy2_title . 's', 'ifound' ),
			'parent_item'       => __( 'Parent ' . $this->taxonomy2_title, 'ifound' ),
			'parent_item_colon' => __( 'Parent ' . $this->taxonomy2_title, 'ifound' ),
			'edit_item'         => __( 'Edit ' . $this->taxonomy2_title, 'ifound' ),
			'update_item'       => __( 'Update ' . $this->taxonomy2_title, 'ifound' ),
			'add_new'           => __( 'Add New ' . $this->taxonomy2_title, 'ifound' ),
			'add_new_item'      => __( 'Add New ' . $this->taxonomy2_title, 'ifound' ),
			'new_item_name'     => __( 'New ' . $this->taxonomy2_title, 'ifound' ),
			'menu_name'         => __( $this->taxonomy2_title, 'ifound' ),
		);

		$args = array(
			'labels'            => $labels,
			'hierarchical'      => true,
			// We don't want to show this taxonomy metabox in the UI. In fact, we already always hide it due to not
			// including the value of 'campaign_statusdiv' in iFoundCrm::allowed_meta_boxes() (although, before this
			// change I'm about to commit, you could show it by using the Screen Options checkboxes on the top of the
			// page). However, if 'show_ui' is true, it still outputs the HTML for the campaign status on the Edit Post
			// page, and if the user presses Update Campaign, it will send the taxonomy values (via
			// tax_input[campaign_status][]) during the POST. This was causing a bug where, using the Edit Post
			// (campaign) page, using the Status control would enable/disable the campaign via AJAX, but then users
			// would press Update Campaign, which would send the stale campaign status value from the page. Because we
			// don't want to allow users to control the values, ever, we'll just force it to not output.
			'show_ui'           => false,
			'show_in_menu'		=> false,
			'show_in_nav_menus'	=> false,
			'show_admin_column' => true,
			'query_var'         => true,
			'public' 			=> false,
			'show_tagcloud'		=> false,
            'rewrite' 			=> false,
            'capabilities'      => array(
				'edit_terms'   => 'god',
				'manage_terms' => 'god',
			)
		);

		register_taxonomy( $this->taxonomy2, array( $this->post_type, iFoundDripCampaign::$the_post_type ), $args );

	}

	public function register_save_this_routes() {

		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/property-alert/(?P<nonce>\S+)/(?P<contact_id>\d+)/(?P<mls_id>\S+)/(?P<title>\S+)',
			array(
				'methods'  				=> WP_REST_Server::CREATABLE,
				'callback' 				=> array( $this, 'save_this_property' ),
				'args' => array(
      				'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'save_this_secure_me', $param, false );
						}
					),
					'contact_id' => array(
						'sanitize_callback' => 'absint',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_numeric( $param );
						}
					),
					'mls_id' => array(
						'sanitize_callback' => 'sanitize_text_field',
        				'validate_callback' => function( $param, $request, $key ) {
							return is_string( $param );
						}
					),
					'title' => array(
						'sanitize_callback' => array( $this, 'clean_url_text' ),
        				'validate_callback' => function( $param, $request, $key ) {
							return is_string( $param );
						}
					)
      			)
			)
		);

		register_rest_route(
			'ifound/' . iFOUND_PLUGIN_VERSION,
			'/search-update/(?P<nonce>\S+)/(?P<contact_id>\d+)/(?P<params>\S+)/(?P<title>\S+)',
			array(
				'methods'  				=> WP_REST_Server::CREATABLE,
				'callback' 				=> array( $this, 'save_this_search' ),
				'args' => array(
      				'nonce' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					check_ajax_referer( 'save_this_secure_me', $param, false );
						}
					),
					'contact_id' => array(
						'sanitize_callback' => 'absint',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_numeric( $param );
						}
					),
					'params' => array(
        				'validate_callback' => function( $param, $request, $key ) {
							return is_string( $param );
						}
					),
					'title' => array(
						'sanitize_callback' => array( $this, 'clean_url_text' ),
        				'validate_callback' => function( $param, $request, $key ) {
							return is_string( $param );
						}
					)
      			)
			)
		);

	}

	public function clean_url_text( $text ) {
		return sanitize_text_field( urldecode( $text ) );
	}

	public function save_this_property( $input ) {

		$success = false;

		$contact_id = $input['contact_id'];

		if( $contact_id && get_post_status( $contact_id ) ) {

			define( 'iFOUND_CONTACT_ID', $contact_id );

			$data = array();

			$data['title'] = ! empty( $input['title'] ) ? apply_filters( 'ifound_sanitize', $input['title'] ) : 'Your Saved Property';

			$save_this_post = array(
				'post_title'    => $data['title'],
				'post_status'   => 'publish',
				'post_type'		=> $this->post_type,
				'post_author'	=> $this->crm_post_author( $contact_id )
			);

			if( $save_this_id = wp_insert_post( $save_this_post ) ) {

				$data['params'] = array( 'mls_id' => $input['mls_id'] ) ;
				add_post_meta( $save_this_id, 'params', $data['params'], true );
				add_post_meta( $save_this_id, 'contact_id', $contact_id, true );

				wp_set_object_terms( $save_this_id, 'property-alert', $this->taxonomy, true );

				do_action( 'ifound_update_campaign_status', $save_this_id, 'active' );
				do_action( 'ifound_email', $save_this_id, 'agent_notification' );
				do_action( 'ifound_activity_log', $contact_id, 'Saved Property', $data['title'] );

				do_action( 'ifound_external_crm_save_this', 'SavedProperty', $data );

				$success = true;

			}

		}

		return array(
			'class' 		=> $success ? 'fa-check-circle' : 'fa-exclamation-triangle',
			'pop_form'		=> $success ? false : true
		);

	}

	public function save_this_search( $input ) {

		$success = false;

		$contact_id = intval( $input['contact_id'] ) ?: false;

		if( $contact_id && get_post_status( $contact_id ) ) {

			define( 'iFOUND_CONTACT_ID', $contact_id );

			$data = array();

			$data['title'] = ! empty( $input['title'] ) ? apply_filters( 'ifound_sanitize', $input['title'] ) : 'Your Saved Search';

			$save_this_post = array(
				'post_title'    => $data['title'],
				'post_status'   => 'publish',
				'post_type'		=> $this->post_type,
				'post_author'	=> $this->crm_post_author( $contact_id )
			);

			if( $save_this_id = wp_insert_post( $save_this_post ) ) {

				parse_str( $input['params'], $params );
				$data['params'] = $params;

				add_post_meta( $save_this_id, 'params', $params, true );
				add_post_meta( $save_this_id, 'contact_id', $contact_id, true );

				wp_set_object_terms( $save_this_id, 'search-update', $this->taxonomy, true );

				do_action( 'ifound_update_campaign_status', $save_this_id, 'active' );
				do_action( 'ifound_email', $save_this_id, 'agent_notification' );
				do_action( 'ifound_activity_log', $contact_id, 'Saved Search', $data['title'] );
				do_action( 'ifound_external_crm_save_this', 'SavedSearch', $data );

				$success = true;

			}

		}

		return array(
			'class' 		=> $success ? 'fa-check-circle' : 'fa-exclamation-triangle',
			'pop_form'		=> $success ? false : true
		);

	}

	public function update_this() {

		check_ajax_referer( 'update_this_secure_me', 'update_this_nonce' );

		// TODO: Do more to secure this. Possilby send the contact ID and make sure it matches the defined contact ID.

		$input = $_REQUEST['input'];

		$save_this_id = intval( $input['save_this_id'] ) ?: false;

		if( is_int( $save_this_id ) ) {

			parse_str( $input['params'], $params );
			$params = array_filter( $params ); // TODO: Sanitize content as multi-dem array

			update_post_meta( $save_this_id, 'params', $params );

			do_action( 'ifound_activity_log', $this->get_contact_id(), 'Update Search', get_the_title( $save_this_id ) );

			echo json_encode( 'fa-plus-square' );

			die();

		}

		/** No Success */
		echo json_encode( 'fa-exclamation-triangle' );

		die();

	}

	// Reminder that this hook exists for both search campaigns and drip campaigns.
	public function remove_this() {

		check_ajax_referer( 'remove_this_secure_me', 'remove_this_nonce' );

		// TODO: Do more to secure this. We do check post type now. Possilby send the contact ID and make sure it matches the defined contact ID. Or the user/admin is logged in.

		$save_this_id = intval( $_REQUEST['id'] );

		/** By checking post type, someone cannot empty the sttes content completely. */
		if( $save_this_id > 0 && is_int( $save_this_id ) && get_post_type( $save_this_id ) == $this->post_type ) {

			do_action( 'ifound_activity_log', $this->get_contact_id(), 'Delete Search/Property', get_the_title( $save_this_id ) );

			/** Set 2nd param to true to bypass trash. This also deletes all the meta tied to the post.
			 *
			 * @link https://codex.wordpress.org/Function_Reference/wp_delete_post
			 */
			wp_delete_post( $save_this_id, true );

			echo json_encode( 'success' );

			die();
		}
	}

	// This is called from:
	// 1. Client Profile page, campaign status toggle control
	// 2. Edit Contact page, campaign status toggle control
	// It used to be on the Edit Campaign page, but is no longer.
	public function update_campaign() {

		check_ajax_referer( 'update_campaign_secure_me', 'update_campaign_nonce' );

		$save_this_id = intval( $_REQUEST['id'] );

		/** By checking post type, someone cannot empty the sttes content completely. */
		// This hook runs for both search campaigns and drip campaigns. Only run this code if it's this type.
		if( $save_this_id > 0 && is_int( $save_this_id ) && get_post_type( $save_this_id ) == $this->post_type ) {

			$new_campaign_status = has_term( 'active', $this->taxonomy2, $save_this_id ) ? 'inactive' : 'active';

			if ($new_campaign_status === 'active') {
				$contact_id = get_post_meta($save_this_id, 'contact_id', true);

				// Has contact unsubscribed? If so, the campaign may not be enabled.
				$is_unsubscribed = has_term('unsubscribed', iFoundContacts::$the_taxonomy, $contact_id);
				if ($is_unsubscribed) {
					$msg = 'Contact is unsubscribed. Edit the contact and remove the Unsubscribed checkbox on the'
						. ' Contact Status';
					$this->util()->respond_with_error($msg);
				}

				// Is end date in the past?
				$end_date = get_post_meta($save_this_id, 'end_date', true);
				if ($end_date && strtotime($end_date) < current_time('timestamp', false)) {
					$msg = 'Campaign end date is in the past. On the edit campaign page, either remove it or set it'
						. ' to the future';
					$this->util()->respond_with_error($msg);
				}

				// Do not allow campaign to be re-enabled if it is not owned by this user (initially expected scenario
				// is an admin who can see a contact that they assigned to a team member).
				$user_crm_id = $this->crm_id();
				$contact_crm_id = $this->crm_id_from_user_id(get_post($contact_id)->post_author);
				if ($user_crm_id !== $contact_crm_id) {
					$msg = 'The campaign cannot be enabled because the contact does not belong to you';
					$this->util()->respond_with_error($msg);
				}
			}

			do_action( 'ifound_update_campaign_status', $save_this_id, $new_campaign_status, 'Manually updated' );

			$next_time = null;
			if ($new_campaign_status == 'active') {
				if ($this->is_search_campaign($save_this_id)) {
					$this->send_campaign_now($save_this_id);
					$next_time = $this->update_campaign_status_or_next_time($save_this_id);
				}
				$class = 'fa-toggle-on';
			} else {

				$class = 'fa-toggle-off';
				$next_time = '-';
			 	delete_post_meta( $save_this_id, 'next_time' );

			}

			$response = array(
				'status' 	=> ucwords( $new_campaign_status ),
				'class'		=> $class,
				'next_time' => apply_filters( 'pretty_date', $next_time )
			);

			echo json_encode( $response );

			die();

		}

	}

	public function is_property_alert($save_this_id) {
		return has_term('property-alert', $this->taxonomy, $save_this_id);
	}

	// WARNING: I don't remember which tags designate search campaigns. I think it's search-update, market-update, and
	// homeowner-campaign. I THINK search-update means it's user created, market-update means a one-off agent-created
	// campaign, and homeowner-campaign is a bulk campaign. However, for the code I'm working on at the moment, all I
	// care about is that it's not a property alert campaign, so I simply return the opposite of that function. Right
	// now, this function could give a wrong answer for a campaign ID that doesn't exist or e.g. a drip campaign. So
	// don't call this in those situations.
	public function is_search_campaign($save_this_id) {
		return !$this->is_property_alert($save_this_id);
	}

	// This forces the campaign to send. It does not check if it should be disabled due to things like overactivity,
	// unread emails (which we check for in iFoundProcessAlerts), etc. It only makes sense to call for search campaigns,
	// not saved properties.
	public function send_campaign_now($save_this_id) {
		$did_send = false;
		$is_do_email = iFoundSharedCampaign::new_hookless()->is_do_email_yes($save_this_id);
		$is_do_sms = iFoundSharedCampaign::new_hookless()->is_do_sms_yes($save_this_id);
		$results = null;
		if ($is_do_email || $is_do_sms) {
			$results = iFoundSaveThis::new_hookless()->get_updated_campaign_listings($save_this_id);
		}
		if ($is_do_email) {
			do_action('ifound_email', $save_this_id, iFoundEmail::$CAMPAIGN_ALERT, $results);
			$did_send = true;
		}
		if ($is_do_sms) {
			iFoundSms::new_hookless()->send_campaign_alert($save_this_id, $results);
			$did_send = true;
		}
		if ($did_send) {
			update_post_meta($save_this_id, 'last_time', current_time('mysql'));
			$contact_id = get_post_meta($save_this_id, 'contact_id', true);
			do_action('ifound_activity_log', $contact_id, 'Process Campaign', get_the_title($save_this_id));
		}
	}

	/**
	 * Update Campaign Status
	 *
	 * Update the campaign status.
	 *
	 * @since 2.5.32
	 * @since 2.5.42 Add $campaign_status param. Change from a switch type function to use a defined value.
	 *
	 * @param int    $save_this_id    The ID if the save this.
	 * @param string $campaign_status The campaign status.
	 */

	public function update_campaign_status( $save_this_id, $campaign_status, $message = null ) {
		wp_set_object_terms( $save_this_id, $campaign_status, $this->taxonomy2 );
		if ($campaign_status === 'active') {
			update_post_meta($save_this_id, iFoundSaveThis::$ENABLED_DATETIME_KEY, current_time('mysql', true));
		}
		if (!$message) {
			$message = get_the_title($save_this_id);
		}
		do_action('ifound_activity_log', $save_this_id, 'Update Campaign Status - ' . ucwords( $campaign_status),
			$message);
	}

	public function is_campaign_active($post_id) {
		$terms = wp_get_object_terms($post_id, static::$the_taxonomy2);
		$term_slugs = array_map(function($x) { return $x->slug; }, $terms);
		return in_array('active', $term_slugs, true);
	}

	/**
	 * Start Date
	 *
	 * @since 2.2.2
	 * @since 2.5.22 Never return a start_date before today's date.
	 * @since 2.5.22 Return an empty value if start_date is before today's date.
	 *
	 * @param  object $data       The save_alert data object.
	 * @return string $start_date The string value for the date. Bool false if no date.
	 */

	public function start_date( $data ) {

		$start_date = isset( $data->start_date ) ? sanitize_text_field( $data->start_date ) : 0;

		return ( strtotime( $start_date ) <= $this->current_time() ) ? '' : $start_date;

	}

	/**
	 * End Date
	 *
	 * @since 2.2.2
	 * @since 2.5.22 Return an empty value if no end_date is provided..
	 *
	 * @param  object $data     The save_alert data object.
	 * @return string $end_date The string value for the date. Bool false if no date.
	 */

	public function end_date( $data ) {

		return isset( $data->end_date ) ? sanitize_text_field( $data->end_date ) : '';

	}

	/**
	 * Make Timestamp
	 *
	 * Create a timestamp for the next scheduled email.
	 *
	 * @since 1.0.0
	 * @since 2.1.0  Create a timestamp from the supplied data regardless of supploed time_of_day.
	 * @since 2.5.21 Use current_time() for start_date. Due to GMT offset, date() returns the next days date when late in the day.
	 *
	 * @param  object $data      The data for this email campaign.
	 * @return string $timestamp The timestamp of the next scheduled email.
	 */

	public function make_timestamp( $data ) {

		$time_of_day = ( $data->time_of_day == 'now' )	? current_time( 'H:i:s' )	: $data->time_of_day;
		$start_date  = empty( $data->start_date )      	? current_time( 'Y-m-d' )	: $data->start_date;

		return $start_date . ' ' . $time_of_day;

	}

	/**
	 * Next Time
	 *
	 * Sets the mext time an email is to be sent.
	 *
	 * @since 2.1.0
	 *
	 * @param  object $data      The data for this email campaign.
	 * @return mixed  $next_time The next timestamp for the email or bool false if save_type is property-alert.
	 */

	public function next_time( $data ) {

		return $this->get_next_time( $this->make_timestamp( $data ), $data->how_often );

	}

	/**
	 * Get Next Time
	 *
	 * @since 1.0.0
	 * @since 2.5.21 Move how_often convertion to iFOUND_save_this::how_often_in_seconds().
	 *
	 * @param  string $timestamp The timestamp of the last email.
	 * @param  string $how_often The frequency of the email alerts.
	 * @return string $next_time The next scheduled timestamp to send an email.
	 */

	public function get_next_time( $timestamp, $how_often ) {

		$seconds = $this->how_often_in_seconds( $how_often );

		/** If we are missing $timestamp. We default to this time +. */
		if( empty( $timestamp ) )
			return date( 'Y-m-d H:i:s', strtotime( current_time( 'mysql' ) ) + $seconds );

		/**
		 * If the $timestamp is for the future we need to leave it alone.
		 * This happens when a client schedules emails for event/birthdays in advance.
		 * This would not have anything to do with property/search emails.
		 */
		if( strtotime( $timestamp ) > $this->current_time() )
			return $timestamp;

		$future_timestamp = strtotime($timestamp) + $seconds;
		// Fix the problem where, for Twice Daily only, to calculate the next_date, 12 hours would be added to the
		// prescribed time_of_day which would result in it not changing. For example, if the start time was
		// 09:00 am, then when the next_time was calculated on or after 9:00 pm, it would again be 9 pm, until the
		// calculation was run after midnight of the next day.
		if ($how_often === 'Twice Daily' && $future_timestamp < $this->current_time()) {
			$future_timestamp += $seconds;
		}
		return date('Y-m-d H:i:s', $future_timestamp);
	}

	/**
	 * Next Time Data
	 *
	 * Getch the data to process the next time.
	 *
	 * @since 2.5.32
	 *
	 * @param  int    $save_this_id The ID of the save campaign.
	 * @return object $data         The nect time data object.
	 */

	public function next_time_data( $save_this_id ) {

		$data 				= new stdClass();
		$data->time_of_day 	= get_post_meta( $save_this_id, 'time_of_day', true );
		$data->start_date 	= current_time( 'Y-m-d' );
		$data->how_often 	= get_post_meta( $save_this_id, 'how_often', true );

		return $data;

	}

	/**
	 * How Often In Seconds
	 *
	 * @since 2.5.21
	 * @since 2.5.21 Change from seconds to PHP add time.
	 *
	 * @param  string $how_often The frequency of the email alerts.
	 * @return int    $seconds   The number of seconds from conversion. Defaluts to 1 day in seconds.
	 */

	public function how_often_in_seconds( $how_often ) {

		$choices = array(
			'Daily' 		=> '+1 day',
			'Twice Daily' 	=> '+12 hours',
			'3 Days'		=> '+3 days',
			'Bi-Weekly' 	=> '+3 days',
			'Weekly' 		=> '+1 week',
			'Bi-Monthly' 	=> '+16 days',
			'Monthly' 		=> '+1 month',
			'Quarterly' 	=> '+3 months',
			'Bi-Annually' 	=> '+6 months',
			'Annually'		=> '+1 year'
		);

		return isset( $choices[$how_often] ) ? ( strtotime( $choices[$how_often] ) - time() ) : 86400;

	}

	public function get_date_interval_from_how_often($how_often) {
		$choices = array(
			'Daily' 		=> 'P1D',
			'Twice Daily' 	=> 'PT12H',
			'3 Days'		=> 'P3D',
			'Bi-Weekly' 	=> 'P3D',
			'Weekly' 		=> 'P1W',
			'Bi-Monthly' 	=> 'P16D',
			'Monthly' 		=> 'P1M',
			'Quarterly' 	=> 'P3M',
			'Bi-Annually' 	=> 'P6M',
			'Annually'		=> 'P1Y',
		);

		$duration = $choices[$how_often];
		return new DateInterval($duration);
	}

	/**
	 * Save This Button
	 *
	 * Button used for saved searches and properties.
	 *
	 * @since 1.0.0
	 * @since 1.2.32 Add HTML for custon titles.
	 *
	 * @param string $save_type The type of search for this button.
	 * @param string $title 	The title of the search. Defaults is false for no title.
	 * @param string $mls_id 	The MLS ID if this button is for a saved property.
	 */

	public function save_this_button( $save_type, $title = false, $mls_id = '' ) {

		wp_enqueue_script( 'save_this_js' );

		$title 			= $title				         ? $title			: '';
		$save_type      = $save_type == 'property-alert' ? 'property-alert' : 'search-update';
		$button         = $save_type == 'property-alert' ? 'Property' 		: 'Search'; ?>

		<div class="save-this-wrapper">

			<div class="save-this-title-wrapper">

				<div class="ifound-wrap">

					<div class="save-this-title-close">
							<i class="fal fa-window-close" aria-hidden="true"></i>
					</div>

					<div class="save-this-title-heading"><? _e( 'Title', 'ifound' ); ?></div>

					<input type="text" class="save-this-title" value="<? echo $title; ?>" placeholder="Type title here." />

					<div class="save-this-title-button button" save_type="<? echo $save_type; ?>" mls_id="<? echo $mls_id; ?>"><? _e( 'Save Now', 'ifound' ); ?></div>

				</div>

			</div>

			<div class="button save-this">

				<div class="ifound-wrap">

					<i class="fal fa-heart save-this-spinner" aria-hidden="true"></i>
					<? _e( 'Save ' . $button, 'ifound' ); ?>

				</div>

			</div>

		</div><?

	}

	public function update_this_button( $save_this_id ) {

		wp_enqueue_script( 'update_this_js' ); ?>

    	<div class="button update-this" save_this_id="<? echo $save_this_id; ?>">

       		 <div class="ifound-wrap">

       		 	<i class="fal fa-plus-square" id="save-this-spinner" aria-hidden="true"></i>
        		<? _e( 'Update Saved Search', 'ifound' ); ?>

			</div>

		</div><?

	}

	/**
	 * Client Profile Title
	 *
	 * This is the Client/User profile page. Currently labeled Portal in navigation menu.
	 *
	 * @since 1.0.0
	 * @since 1.0.23 Escape the title text.
	 */
	public function client_profile_title() {

		$title = 'Welcome';

		if( defined( 'iFOUND_CONTACT_ID' ) ) {

			$title .= ' ' . get_post_meta( iFOUND_CONTACT_ID, 'fname', true ) . ' ' . get_post_meta( iFOUND_CONTACT_ID, 'lname', true );

		}

		_e( trim( $title ), 'ifound' );

	}

	/**
	 * Client Profile
	 *
	 * This is the Client/User profile page. Currently labeled Portal in navigation menu.
	 *
	 * @since 1.0.0
	 * @since 1.0.23 If not a contact we dispaly our registration form.
	 *
	 * @uses iFoundRegistration::registration_form_body()
	 * @uses iFOUND_save_this::save_this_meta_display()
	 */

	public function client_profile() { ?>

		<div class="client-profile">

			<div class="ifound-wrap"><?

				if( ! defined( 'iFOUND_CONTACT_ID' ) ) {

					iFoundRegistration::registration_form_body( true );

					return;

				} ?>

				<div class="button" id="logoutb">Log Out</div>
				<script>
                                let button = document.getElementById('logoutb');
                                button.addEventListener('click', function() {
                                        document.cookie = 'ifound-registered' + '=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                                        location.reload();
                                });
                                </script>

				<h2><? _e( 'Your Saved Properties', 'ifound' ); ?></h2><?

				$args = array(
					'post_type' 		=> $this->post_type,
					'posts_per_page'   	=> -1,
					'tax_query' => array(
						array(
							'taxonomy' => $this->taxonomy,
							'field' => 'slug',
							'terms' => 'property-alert'
						)
					),
					'meta_query' => array(
						array(
							'key'     => 'contact_id',
							'value'   => iFOUND_CONTACT_ID,
							'compare' => '=',
						)
					)
				);

				$this->save_this_meta_display( $args, 'property-alert' ); ?>

				<h2><? _e( 'Your Saved Searches', 'ifound' ); ?></h2><?

				$args = array(
					'post_type' 		=> $this->post_type,
					'posts_per_page'   	=> -1,
					'tax_query' => array(
						array(
							'taxonomy' => $this->taxonomy,
							'field' => 'slug',
							'terms' => array( 'search-update', 'market-update', 'homeowner-campaign' )
						)
					),
					'meta_query' => array(
						array(
							'key'     => 'contact_id',
							'value'   => iFOUND_CONTACT_ID,
							'compare' => '=',
						)
					)
				);

				$this->save_this_meta_display( $args, 'search-update' ); ?>

			</div>

		</div><?

	}

	/**
	 * Save This Meta Display
	 *
	 * Display the Save This post type by taxonomy.
	 * This is also used for thr Client Profile @see iFOUND_save_this::client_profile()
	 * This is a WP loop inside a loop. So we need to be careful.
	 * @link https://wordpress.stackexchange.com/questions/71724/loop-within-a-loop
	 *
	 * Use &debug as a url param to display the save this ID in the contact record or client profile.
	 *
	 * @since 1.0.8
	 * @since 1.2.34 Add condition for market-update to include link to search.
	 *
	 * @param array $args Args for the WP Query
	 * @param mixed $type String name of taxonomy to display or false for all other taxonomy names.
	 */

	public function save_this_meta_display( $args, $type = false ) {

		wp_enqueue_script( 'update_campaign_js' );
		wp_enqueue_script( 'remove_this_js' );

		$posts = $args;
		// Were we passed args, or posts? If there is no value at $posts[0], it's args. Fetch the posts.
		if (count($posts) && !isset($posts[0])) {
			$posts = get_posts($args);
		}

		if( $posts ) {

			foreach( $posts as $post ) :

				$save_this_id = $post->ID;
            $campaign_type = 'Search Campaign';
            $is_drip_campaign = false;
            if ($post->post_type === iFoundDripCampaign::$the_post_type) {
                $campaign_type = iFoundDripCampaign::$the_label_name;
                $is_drip_campaign = true;
            } else if ($type === 'property-alert') {
                $campaign_type = 'Property Alert';
            }

				$allow_toggle_status = true;
				$statusBool = has_term('active', $this->taxonomy2, $save_this_id);
				$update_campaign_hint_visibility = $statusBool ? 'hidden' : 'visible';
				if ($statusBool) {

					$status = 'Active';
					$toggle = 'fa-toggle-on';

				} else {

					$status = 'Inactive';
					if ($is_drip_campaign && get_post_meta($post->ID, 'is_done', true) === 'true') {
						$status = 'Inactive, Done';
						$allow_toggle_status = false;
					}
					$toggle = 'fa-toggle-off';

				} ?>

				<table class="form-table save-this-section">

					<tbody>

						<tr>

							<th scope="row"><label><? _e( 'Title', 'ifound' ); ?></label></th><?

								$title = get_the_title( $save_this_id );

								if( $type == 'property-alert' ) {

									$params = get_post_meta( $save_this_id, 'params', true );

									$mls_id = isset( $params['mls_id'] ) ? $params['mls_id'] : 0;

									$href 	= apply_filters( 'ifound_get_detail_url', $title, $mls_id ); ?>

									<td colspan="3"><a href="<? echo $href; ?>" target="_blank"><? _e( $title, 'ifound' ); ?></a></td><?

								} else {

									// Use "?: []" because get_the_terms returns false if there are no terms.
									$terms = get_the_terms( $save_this_id, 'save_type' ) ?: [];
									$term_strings = array_map(function($wp_term) {
										return $wp_term->slug;
									}, $terms);
									if( $is_drip_campaign ) {
                                        ?>
										<td colspan="3"><? _e( $title, 'ifound' ); ?></td><?
                                    } else if ($type == 'search-update' || array_intersect(['market-update', 'homeowner-campaign'], $term_strings) ) {

                                        $href = site_url( '/' . $this->save_this . '/' . $save_this_id ); ?>

                                        <td colspan="3"><a href="<? echo $href; ?>" target="_blank"><? _e( $title, 'ifound' ); ?></a></td><?

                                    } else {


                                            if ( $terms && ! is_wp_error( $terms ) ) :

                                                $types = array();

                                                foreach ( $terms as $term ) {
                                                    $types[] = $term->name;
                                                } ?>

                                                <td colspan="3">
                                                    <? _e( $title, 'ifound' ); ?> - <? _e( join( ', ', $types ), 'ifound' ); ?>
                                                </td><?


                                            endif;
                                    }

								} ?>


						</tr>
                        <tr>
							<th scope="row"><label><? _e( 'Campaign Type', 'ifound' ); ?></label></th>
							<td class="status"><?= $campaign_type ?></td>
						</tr>
						<tr class="created-search-section">

							<th scope="row"><label><? _e( 'Created', 'ifound' ); ?></label></th>
							<td><? echo get_the_date( 'M d, Y', $save_this_id ); ?></td><?

							if( $type == 'property-alert' ) {

								$last_time = get_post_meta( $save_this_id, 'last_time', true );
								$last_time = apply_filters( 'pretty_date', $last_time ) ?: '-'; ?>

								<th scope="row"><label><? _e( 'Last Email', 'ifound' ); ?></label></th>
								<td class="status"><? _e( $last_time, 'ifound' ); ?></td><?

							} else {

                                $next_timestamp_string = null;
								if ($is_drip_campaign) {
                                    $next_timestamp = wp_next_scheduled(iFoundDripCampaign::$cron_event_name, [$save_this_id]);
									$next_time_string = apply_filters( 'pretty_date', $next_timestamp, true) ?: '-';
								} else {
									$next_timestamp = get_post_meta($save_this_id, 'next_time', true);
									$next_time_string = apply_filters( 'pretty_date', $next_timestamp) ?: '-';
								}
								?>

								<th scope="row"><label><? _e( 'Next Email', 'ifound' ); ?></label></th>
								<td class="status next-time"><? _e( $next_time_string, 'ifound' ); ?></td><?

							} ?>

						</tr>

						<?php if (!$is_drip_campaign): ?>
                            <tr>

                                <th scope="row"><label><? _e('Start Date', 'ifound'); ?></label></th>
                                <td class="status"><? _e(apply_filters('pretty_date', get_post_meta($save_this_id, 'start_date', true)) ?: 'NA', 'ifound'); ?></td>

                                <th scope="row"><label><? _e('End Date', 'ifound'); ?></label></th>
                                <td class="status"><? _e(apply_filters('pretty_date', get_post_meta($save_this_id, 'end_date', true)) ?: 'NA', 'ifound'); ?></td>

                            </tr>
						<?php endif ?>

						<tr>

							<th scope="row"><label><? _e( 'Campaign Status', 'ifound' ); ?></label></th>
							<td class="status campaign-status"><? _e( $status, 'ifound' ); ?></td>

							<?php if ($allow_toggle_status): ?>
                                <th scope="row"><label><? _e('Toggle Status', 'ifound'); ?></label></th>
                                <td>
									<div>
										<i class="fa <?= $toggle; ?> fa-2x update-campaign" id="<?= $save_this_id; ?>">
										</i>
									</div>
									<?php
									if ($this->is_search_campaign($save_this_id)):
									?>
									<div class="update-campaign-hint" style="visibility: <?= $update_campaign_hint_visibility ?>">
										Enabling a campaign will send it immediately
									</div>
									<?php
									endif;
									?>
								</td>
							<?php endif ?>

						</tr>

						<tr>

							<td class="delete-this-trash">

								<div class="button remove-this button-primary red-bg" id="<? echo $save_this_id; ?>">
									<i class="fal fa-trash" aria-hidden="true"></i>
									<? _e( 'Delete', 'ifound' ); ?>
								</div>

							</td>

							<td> </td>

								<td class="edit-this"><?

								if( is_admin() ) { ?>

									<a href="<? echo admin_url( '/post.php?action=edit&post=' . $save_this_id ); ?>" class="button edit-this button-primary">
										<i class="fa fa-pencil" aria-hidden="true"></i>
										<? _e( 'Edit Details', 'ifound' ); ?>
									</a><?

								} ?>

								</td>

							<td> </td><?

							if( isset( $_GET['debug'] ) ) { ?>

								<tr>

									<th scope="row"><label><? _e( 'Save This ID', 'ifound' ); ?></label></th>
									<td><? echo $save_this_id; ?></td>

								</tr><?

							} ?>

						</tr>

					</tbody>

				</table><?

			endforeach;

		} else {

			_e( 'You do not have any saved at this time.', 'ifound' );

		}

	}

	public function get_campaigns_args_for_contact($contact_id = false) {
		global $post;

		if (!is_numeric($contact_id)) {
			if ($post) {
				$contact_id = $post->ID;
			} else {
				// For safety. My thinking is if there is no post, then we want to return 0 records.
				$contact_id = -1;
			}
		}
		$args = array(
			'post_type' 		=> $this->post_type,
			'posts_per_page'	=> -1,
			'tax_query' => array(
				array(
					'taxonomy' => $this->taxonomy,
					'field' => 'slug',
					'terms' => array(
						'holiday',
						'newsletter',
						'birthday',
						'marketing',
						'open-house',
						'event',
						'market-update',
						'homeowner-campaign',
					)
				)
			),
			'meta_query' => array(
				array(
					'key'     => 'contact_id',
					'value'   => $contact_id,
					'compare' => '=',
				)
			)
		);

		return $args;
	}

	public function get_active_campaign_ids_for_user($user_id) {
		$args = [
			'post_type'      => $this->post_type,
			'posts_per_page' => -1,
			'author'         => $user_id,
            'fields'         => 'ids',
			'tax_query'      => [
				[
					'taxonomy' => $this->taxonomy2,
					'field'    => 'slug',
					'terms'    => 'active',
				],
			],
		];
		$posts = get_posts($args);
        return $posts;
	}

	public function get_active_campaign_ids_for_contact_id($contact_id) {
		$args = [
			'post_type'      => $this->post_type,
			'posts_per_page' => -1,
			'fields'         => 'ids',
			'tax_query'      => [
				[
					'taxonomy' => $this->taxonomy2,
					'field'    => 'slug',
					'terms'    => 'active',
				],
			],
			'meta_query' => [
				[
					'key'     => 'contact_id',
					'value'   => $contact_id,
					'compare' => '=',
				],
			],
		];
		$posts = get_posts($args);
		return $posts;
	}

	// If the campaign has expired, update the status. Or, schedule the next event.
	public function update_campaign_status_or_next_time( $save_this_id ) {
		$end_date = get_post_meta( $save_this_id, 'end_date', true );
		if(
			($end_date && strtotime( $end_date ) < $this->current_time())
			||
			get_post_meta( $save_this_id, 'recurring', true ) == 'no'
		) {
			do_action( 'ifound_update_campaign_status', $save_this_id, 'inactive' );
			return;
		}
		$data = apply_filters( 'ifound_next_time_data', $save_this_id );
		$next_time = apply_filters( 'ifound_next_time', $data );
		update_post_meta( $save_this_id, 'next_time', $next_time );
		return $next_time;
	}

	// It's so very very sad but "=== false" means there are zero results in the campaign.
	// See: ifound/crm/includes/class-ifound-save-this.php::get_updated_campaign_listings().
	public function get_updated_campaign_listings_results_count_str($results) {
		if ($results || $results === false) {
			if ($results === false) {
				return 0;
			}
			return $results->paging->parseable->total_items;
		}
		return '?';
	}

	public function get_updated_campaign_listings($save_this_id) {
		$last_time = get_post_meta( $save_this_id, 'last_time', true );
		$params = get_post_meta( $save_this_id, 'params', true );
		$contact_id = get_post_meta ($save_this_id, 'contact_id', true);

		if ($params) {
			// In theory we shouldn't need to geocode here. Campaigns done by radius should all have a nearby radius
			// with a lat/lng for a centroid. But if the agent did not put an address on the contact, then we could get
			// to this situation. We might as well do this unless we find it hurting performance.
			$params = $this->util()->maybe_geocode_campaign($save_this_id);

			$results = $this->process_input($params, [
				'stats' => [
					'show' => 'no_stats',
				],
				'trace' => [
					'save_this_id' => $save_this_id,
				],
				'email_content_options' => [
					'should_include' => true,
					'options' => [
						'base_url' => site_url('/'),
						'contact_id' => $contact_id,
						'email_type' => iFoundEmail::$CAMPAIGN_ALERT,
					],
				],
			]);

			if (isset($results->query->numFound) && is_numeric($results->query->numFound) && $results->query->numFound > 0) {
				return $results;
			}
		}
		return false;
	}

	public function save_campaign_stuff_and_maybe_run($save_this_id, $data) {
		iFoundSaveThisAdmin::new_hookless()->save_alert($save_this_id, $data);
		$campaign_status = 'active';
		if ($data->time_of_day == 'now' && strtotime( $data->start_date ) <= $this->current_time()) {
			$this->send_campaign_now($save_this_id);
			if ($data->recurring === 'no') {
				$campaign_status = 'inactive';
			}
		}
		do_action('ifound_update_campaign_status', $save_this_id, $campaign_status);
	}
}
