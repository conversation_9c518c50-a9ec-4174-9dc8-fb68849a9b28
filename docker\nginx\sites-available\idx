server {
    listen 80;
    server_name idx idx.test;

    root /var/www/idx;
    index index.php;

    access_log /var/log/nginx/idx.log;
    error_log /var/log/nginx/idx-error.log;

    # Ignore requests for favicon.ico during dev. It's an extra request for each page load from the browser,
    # which clutters debugging sequences.
    location /favicon.ico {
        return 444;
    }

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        set $upstream idx:9000;
        fastcgi_pass $upstream;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
        fastcgi_param PHP_VALUE "include_path=/www/includes/:/usr/share/php/";
        # Make PHP think we're using HTTPS if an upstream reverse-proxy set X-Forwarded-Proto.
        # See: https://stackoverflow.com/a/46984243/135101
        if ($http_x_forwarded_proto = 'https') {
            set $fe_https 'on';
        }
        fastcgi_param HTTPS $fe_https;
    }

    include /etc/nginx/global/idx.conf;

}
