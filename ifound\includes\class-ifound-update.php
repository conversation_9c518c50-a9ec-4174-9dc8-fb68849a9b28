<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * iFound_update Class
 *
 * @since 2.4.0
 */
class iFoundUpdate {
	use UtilTrait;

	private $updates;

	public function __construct() {
		$this->updates = get_option( 'ifound_plugin_updates' ) ?: array();
	}

	public static function run() {
		$updater = new iFoundUpdate();
		$updater->run_updates();
	}

	/**
	 * Run Updates.
	 *
	 * On plugin iFOUND_PLUGIN_VERSION update we run updates.
	 *
	 * @since 2.4.0
	 */

	private function run_updates() {
		// Due to the plugin versioning problem I discovered on 2021-01-14, the plugin updates listed in the database
		// under the ifound_plugin_updates option might not be in order. Just keep it in mind.
		// If necessary, we could sort them next time when we put them in the DB.

		$versions = [
			'v3_6_7' => [],
			'v4_0_0' => [],
			'v4_1_0' => [],
			'v4_1_3' => [],
			'v4_1_8' => [],
			'v5_0_0' => [],
			'v5_0_2' => [],
			'v5_0_3' => [],
			'v5_0_5' => [],
			'v5_0_6' => [],
			'v5_0_7' => [],
			'v5_0_9' => [],
			'v5_6_0' => [],
			'v5_6_1' => [],
			'v5_6_2' => [],
			'v5_6_3' => [],
			'v5_6_4' => [],
			'v5_7_0' => [],
			'v5_8_0' => [],
			'v5_9_0' => [],
			'v5_9_1' => [],
			'v5_10_0' => [],
			'v5_10_1' => [],
			'v5_10_2' => [],
			'v5_10_3' => [],
			'v5_11_0' => [],
			'v5_11_1' => [],
			'v5_11_2' => [],
			'v5_11_3' => [],
			'v5_12_0' => [],
			'v5_12_1' => [],
			'v5_12_2' => [],
			'v5_12_3' => [],
			'v5_12_4' => [],
			'v5_12_5' => [],
			'v5_12_6' => [],
			'v5_12_7' => [],
			'v5_12_8' => [],
			'v5_12_9' => [],
			'v5_13_0' => [],
			'v5_14_0' => [],
			'v5_15_0' => [],
			'v5_15_1' => [],
			'v5_15_2' => [],
			'v5_15_3' => [],
			'v5_15_4' => [],
			'v5_15_5' => [],
			'v5_16_0' => [],
			'v5_17_0' => [],
			'v5_17_1' => [],
			'v5_17_2' => [],
			'v5_18_0' => [],
			'v5_19_0' => [],
			'v5_19_1' => [],
			'v5_20_0' => [],
			'v5_20_1' => [],
			'v5_20_2' => [],
			'v5_20_3' => [],
			'v5_21_0' => [],
			'v5_21_1' => [],
			'v5_21_2' => [],
			'v5_21_3' => [],
			'v5_22_0' => [],
			'v5_23_0' => [],
			'v5_23_1' => [],
			'v5_24_0' => [],
			'v5_25_0' => [],
			'v5_26_0' => [],
			'v5_27_0' => [],
			'v5_28_0' => [],
			'v5_29_0' => [],
			'v5_30_0' => [],
			'v5_31_0' => [],
			'v5_31_1' => ['has_update_function' => false],
			'v5_32_0' => [],
			'v5_33_0' => [],
			'v5_34_0' => ['has_update_function' => false],
			'v5_35_0' => [],
			'v5_35_1' => [],
			'v5_35_2' => [],
			'v5_36_0' => ['has_update_function' => false],
			'v5_36_1' => ['has_update_function' => false],
			'v5_36_2' => ['has_update_function' => false],
			'v5_37_0' => [],
			'v5_37_1' => [],
			'v5_37_2' => [],
			'v5_38_0' => [],
			'v5_39_0' => ['has_update_function' => false],
			'v5_40_0' => [],
			'v5_41_0' => ['has_update_function' => false],
			'v5_42_0' => [],
			'v5_43_0' => [],
			'v5_44_0' => [],
			'v5_45_0' => [],
			'v5_45_1' => [],
			'v5_45_2' => [],
			'v5_45_3' => [],
			'v5_45_4' => [],
			'v5_46_0' => [],
			'v5_47_0' => [],
			'v5_48_0' => [],
			'v5_49_0' => ['has_update_function' => false],
		];
		$currently_installed_version = get_option( 'ifound_current_version' );
		foreach ($versions as $version => $meta) {
			// Only run new updates. For example, don't run v5_10_3 or below if what's installed is v5_10_3.
			$should_run_update_for_version = version_compare('v' . $currently_installed_version, $version, '<') || version_compare('v' . iFOUND_PLUGIN_VERSION, $version, 'eq');
			if ($should_run_update_for_version && !in_array( $version, $this->updates ) ) {
				// For the sake of JS asset cache busting, we will now update the plugin version whenever we change JS,
				// but we shouldn't HAVE to create a new function in this file in that case because that's inconvenient.
				if ($meta['has_update_function'] ?? true) {
					$this->$version();
				}
				$this->log_updates( $version );
			}
		}

		// We do these at every update.
		do_action( 'update_meta_box_order' );
		do_action( 'ifound_push_website_update', true );
		do_action( 'ifound_refresh_transients', true );
		flush_rewrite_rules();

	}

	/**
	 * Log Updates.
	 *
	 * Log the update version into the database.
	 *
	 * @param string $version The version number for an update.
	 *@since 2.4.0
	 *
	 */

	public function log_updates( $version) {
		$this->updates = array_merge( $this->updates, array( $version) );
		update_option( 'ifound_plugin_updates', $this->updates );

		// Turn e.g. v5_12_8 into 5.12.8
		$dot_version = str_replace('v', '', str_replace('_', '.', $version));
		update_option( 'ifound_current_version', $dot_version );
	}

	private function sample() {

		$update = 'sample';

		if( ! in_array( $update, $this->updates ) ) {

			// Do the update stuff here.

			$this->log_updates( $update );

		}

	}

	private function v3_6_7() {

		$update = 'v3_6_7';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$value = unserialize( $meta->meta_value );

					$array = json_decode( json_encode( $value ), true );

					if( isset( $array['query']['words'] ) ) {

						$clean = apply_filters( 'ifound_words_to_array', $array['query']['words'] );

						$array['query'] = array_merge( $array['query'], $clean );

						unset( $array['query']['words'] );

					}

					if( isset( $array['backup_query']['words'] ) ) {

						$clean = apply_filters( 'ifound_words_to_array', $array['backup_query']['words'] );

						$array['backup_query'] = array_merge( $array['backup_query'], $clean );

						unset( $array['backup_query']['words'] );

					}

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v4_0_0() {

		$update = 'v4_0_0';

		if( ! in_array( $update, $this->updates ) ) {

			if( $settings = get_option( 'ifound_featured_settings' ) ) {

				parse_str( $settings['featured_query'], $query );

				if( array_key_exists( 'price_min' , $query ) ) {

					$query['list_price_min'] = $query['price_min'];
					unset( $query['price_min'] );

				}

				if( array_key_exists( 'price_max' , $query ) ) {

					$query['list_price_max'] = $query['price_max'];
					unset( $query['price_max'] );

				}

				$settings['featured_query'] = empty( $query ) ? '' : http_build_query( $query );

				parse_str( $settings['featured_bu_query'], $bu_query );

				if( array_key_exists( 'price_min' , $bu_query ) ) {

					$bu_query['list_price_min'] = $bu_query['price_min'];
					unset( $bu_query['price_min'] );

				}

				if( array_key_exists( 'price_max' , $bu_query ) ) {

					$bu_query['list_price_max'] = $bu_query['price_max'];
					unset( $bu_query['price_max'] );

				}

				$settings['featured_bu_query'] = empty( $bu_query ) ? '' : http_build_query( $bu_query );

				update_option( 'ifound_featured_settings', $settings );

			}

			$this->log_updates( $update );

		}

	}

	private function v4_1_0() {

		$update = 'v4_1_0';

		if( ! in_array( $update, $this->updates ) ) {

			iFoundStartup::install_seo();

			$settings = get_option( 'ifound_results_settings' );

			$settings['results_body'] = str_replace( 'prop_h2', 'results_h2', $settings['results_body'] );
			$settings['results_body'] = str_replace( 'prop_content', 'results_content', $settings['results_body'] );

			update_option( 'ifound_results_settings', $settings );

			$settings = get_option( 'ifound_featured_settings' );

			$settings['featured_body'] = str_replace( 'prop_h2', 'results_h2', $settings['featured_body'] );
			$settings['featured_body'] = str_replace( 'prop_content', 'results_content', $settings['featured_body'] );

			update_option( 'ifound_featured_settings', $settings );

			$this->log_updates( $update );

		}

	}

	private function v4_1_3() {

		$update = 'v4_1_3';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				AND meta_value LIKE '%price%'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$value = unserialize( $meta->meta_value );

					$array = json_decode( json_encode( $value ), true );

					if( isset( $array['query']['price_min'] ) ) {

						$array['query']['list_price_min'] = $array['query']['price_min'];

						unset( $array['query']['price_min'] );

					}

					if( isset( $array['query']['price_max'] ) ) {

						$array['query']['list_price_max'] = $array['query']['price_max'];

						unset( $array['query']['price_max'] );

					}

					if( isset( $array['backup_query']['price_min'] ) ) {

						$array['backup_query']['list_price_min'] = $array['backup_query']['price_min'];

						unset( $array['backup_query']['price_min'] );

					}

					if( isset( $array['backup_query']['price_max'] ) ) {

						$array['backup_query']['list_price_max'] = $array['backup_query']['price_max'];

						unset( $array['backup_query']['price_max'] );

					}

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v4_1_8() {

		$update = 'v4_1_8';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$value = unserialize( $meta->meta_value );

					$array = json_decode( json_encode( $value ), true );

					if( isset( $array['query'] ) ) {

						foreach ( $array['query'] as $key => $value ) {

							$new_key = strtolower( $key );
							unset( $array['query'][$key] );
							$array['query'][$new_key] = $value;

						}

					}

					if( isset( $array['backup_query'] ) ) {

						foreach ( $array['backup_query'] as $key => $value ) {

							$new_key = strtolower( $key );
							unset( $array['backup_query'][$key] );
							$array['backup_query'][$new_key] = $value;

						}

					}

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v5_0_0() {

		$update = 'v5_0_0';

		if( ! in_array( $update, $this->updates ) ) {

			$mls_name = iFoundIdx::mls_name();

			switch( $mls_name ) {

    			case 'recolorado':
        			$defaults = array(
        				'city',
						'neighborhood',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
        			break;

        		case 'trendmls':
        			$defaults = array(
        				'city',
						'neighborhood',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
        			break;

				case 'brightmls':
					$defaults = array(
						'city',
						'neighborhood',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
					break;
    			case 'crisnet':
        			$defaults = array(
        				'city',
						'mls_area',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
       		 		break;
       		 	case 'crmls':
        			$defaults = array(
        				'city',
						'mls_area',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
       		 		break;
       		 	case 'sdcrca':
        			$defaults = array(
        				'city',
						'mls_area',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);
       		 		break;
    			default:
        			$defaults = array(
        				'city',
						'subdivision',
						'zip',
						'bedrooms',
						'bathrooms',
						'list_price'
					);

			}

			update_option( 'ifound_quick_search_criteria', $defaults );

			$this->log_updates( $update );

		}

	}

	private function v5_0_2() {

		$update = 'v5_0_2_2';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$value = unserialize( $meta->meta_value );

					$array = json_decode( json_encode( $value ), true );

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v5_0_3() {

		$update = 'v5_0_3_2';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'params'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$value = unserialize( $meta->meta_value );

					$array = json_decode( json_encode( $value ), true );

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v5_0_5() {

		$update = 'v5_0_5';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				AND ( meta_value LIKE '%_min%' OR meta_value LIKE '%_max%' )
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$meta_value = unserialize( $meta->meta_value );

					$values = json_decode( json_encode( $meta_value ), true );

					$array = array();

					if ( isset( $values['query'] ) ) {

						foreach ( $values['query'] as $key => $value ) {

							if ( strpos( $key, '_min' ) ) {

								$easy_name = str_replace( '_min', '', $key );
								$array['query'][$easy_name]['min'] = $value;

							} elseif ( strpos( $key, '_max') ) {

								$easy_name = str_replace( '_max', '', $key );
								$array['query'][$easy_name]['max'] = $value;

							} else {

								$array['query'][$key] = $value;

							}

						}

					}

					if ( isset( $values['backup_query'] ) ) {

						foreach ( $values['backup_query'] as $key => $value ) {

							if ( strpos( $key, '_min' ) ) {

								$easy_name = str_replace( '_min', '', $key );
								$array['backup_query'][$easy_name]['min'] = $value;

							} elseif ( strpos( $key, '_max') ) {

								$easy_name = str_replace( '_max', '', $key );
								$array['backup_query'][$easy_name]['max'] = $value;

							} else {

								$array['backup_query'][$key] = $value;

							}

						}

					}

					if( isset( $values['display'] ) ) {

						$array['display'] = $values['display'];

					}

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v5_0_6() {

		$update = 'v5_0_6';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'params'
				AND ( meta_value LIKE '%_min%' OR meta_value LIKE '%_max%' )
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				foreach ( $meta_array as $meta ) {

					$values = unserialize( $meta->meta_value );

					$array = array();

					foreach ( $values as $key => $value ) {

						if( empty( $value ) ) continue;

						$easy_name = str_replace( array( '_min', '_max' ), '', $key );

						if ( strpos( $key, '_min' ) !== false ) {

							$array[$easy_name]['min'] = $value;

						} elseif ( strpos( $key, '_max') !== false ) {

							$array[$easy_name]['max'] = $value;

						} else {

							$array[$easy_name] = $value;

						}

					}

					if ( ! empty( $array  ) ) {

						$wpdb->update(
							$wpdb->postmeta,
							array( 'meta_value' => serialize( $array ) ),
							array( 'meta_id' => $meta->meta_id ),
							array( '%s'),
							array( '%d' )
						);

					}

				}

			}

			$this->log_updates( $update );

		}

	}

	private function v5_0_7() {

		$update = 'v5_0_7';

		if( ! in_array( $update, $this->updates ) ) {

			$featured = get_option( 'ifound_featured_settings' );

			$featured['featured_query'] = str_replace( '_min', '[min]', $featured['featured_query'] );
			$featured['featured_query'] = str_replace( '_max', '[max]', $featured['featured_query'] );
			$featured['featured_bu_query'] = str_replace( '_min', '[min]', $featured['featured_bu_query'] );
			$featured['featured_bu_query'] = str_replace( '_max', '[max]', $featured['featured_bu_query'] );

			update_option( 'ifound_featured_settings', $featured );

			$this->log_updates( $update );

		}

	}

	private function v5_0_9() {

		$update = 'v5_0_12';

		if( ! in_array( $update, $this->updates ) ) {

			global $wpdb;

			$meta_array = $wpdb->get_results(
				"
				SELECT *
				FROM " . $wpdb->postmeta . "
				WHERE meta_key = 'save_this_shortcode'
				"
			);

			if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

				$queries = array( 'query', 'backup_query' );
				$easynames = array( 'list_price', 'bedrooms', 'bathrooms', 'garage_spaces' );

				foreach ( $meta_array as $meta ) {

					$array = unserialize( $meta->meta_value );

					foreach ( $queries as $query ) {

						if ( isset( $array[$query] ) ) {

							foreach ( $array[$query] as $key => $value ) {

								if ( in_array( $key, $easynames ) ) {

									if ( ! isset( $value['min'] ) && ! isset( $value['max'] ) ) {

										if ( is_array( $value ) ) {

											$array[$query][$key] = array( 'min' => $value[0] );

										}

										if ( is_string( $value ) ) {

											$array[$query][$key] = array( 'min' => $value );

										}

									}

								}

							}

						}

					}

					$wpdb->update(
						$wpdb->postmeta,
						array( 'meta_value' => serialize( $array ) ),
						array( 'meta_id' => $meta->meta_id ),
						array( '%s'),
						array( '%d' )
					);

				}

			}

			$this->log_updates( $update );

		}

	}


	private function v5_6_0() {

		$update = __FUNCTION__;

		if( ! in_array( $update, $this->updates ) ) {

			// Must register the taxonomy before we insert a term using the taxonomy.
			iFoundSaveThis::new_hookless()->taxonomy();

			if (!term_exists('Homeowner Campaign', 'save_type')) {
				$response = wp_insert_term( 'Homeowner Campaign', 'save_type' );
				if (!is_wp_error($response)) {
					$this->log_updates( $update );
				}
			} else {
				$this->log_updates( $update );
			}
		}

	}

	private function v5_6_1() {
		$email_templates = apply_filters('ifound_default_email_templates', []);
		$email_template = array_values(array_filter($email_templates, function($template) {
			return $template['post_title'] === 'Homeowner Campaign';
		}))[0];

		$posts = get_posts([
			'post_type' => 'ifound_email',
			'name' => 'Homeowner Campaign',
		]);
		if (count($posts)) {
			return;
		}

		// Must register the taxonomy before we insert a term using the taxonomy.
		iFoundEmail::new_hookless()->taxonomy();
		do_action('ifound_create_email_template', $email_template);
	}

	private function v5_6_2() {
		$crm_settings = get_option( 'ifound_crm_settings' ) ?: array();

		if (key_exists('homeowner_campaign', $crm_settings)) {
			return;
		}

		$post = get_posts([
			'post_type' => 'ifound_email',
			'name' => 'homeowner-campaign',
		])[0];
		$crm_settings['homeowner_campaign'] = $post->ID;
		update_option( 'ifound_crm_settings', $crm_settings );
	}

	private function v5_6_3() {
		// Note the misspelling of 'builder' in the option name! It's a legacy bug that we must continue.
		$campaign_bulder_criteria = get_option( 'ifound_campaign_bulder_criteria') ?: array();

		if (key_exists('nearby', $campaign_bulder_criteria)) {
			return;
		}

		$campaign_bulder_criteria['nearby'] = 'nearby';
		update_option( 'ifound_campaign_bulder_criteria', $campaign_bulder_criteria);

		$adv_search_criteria = get_option( 'ifound_adv_search_criteria') ?: array();
		$adv_search_criteria['nearby'] = 'nearby';
		update_option( 'ifound_adv_search_criteria', $adv_search_criteria);
	}

	private function v5_6_4() {
		$email = iFoundEmail::new_hookless();
		$email->post_type();
		$email->taxonomy();

		if (term_exists('Search Campaign', 'ifound_email_type')) {
			return;
		}

		$search_campaign_term = wp_insert_term( 'Search Campaign', 'ifound_email_type' );
		$bulk_email_term = wp_insert_term( 'Bulk Email', 'ifound_email_type' );

		$search_campaign_post_slugs = ['homeowner-campaign', 'buyer-search-template', 'market-update-home-owner', 'market-update-past-client'];
		$bulk_email_post_slugs = ['happy-holidays'];
		$merged_posts = array_merge($search_campaign_post_slugs, $bulk_email_post_slugs);

		$posts = get_posts([
			'post_type' => 'ifound_email',
			'numberposts' => -1,
			// Reminder to self: if we instead wanted to search for a single post_name, use 'name', not 'post_name.
			'post_name__in' => $merged_posts,
		]);
		foreach ($posts as $post) {
			if (in_array($post->post_name, $search_campaign_post_slugs, true)) {
				$search_id = wp_set_object_terms( $post->ID, $search_campaign_term['term_id'], 'ifound_email_type', true );
			} else if (in_array($post->post_name, $bulk_email_post_slugs, true)) {
				$bulk_id = wp_set_object_terms( $post->ID, $bulk_email_term['term_id'], 'ifound_email_type', true );
			}
		}
	}

	private function v5_7_0() {
		$settings = get_option( 'ifound_results_settings' );
		$settings['results_body'] = str_replace( ' target="_blank"', '', $settings['results_body'] );
		update_option( 'ifound_results_settings', $settings );
	}

	// REMINDER! As of 2023-12-05, don't copy this code... because I already did.
	// Call $this->update_cmc_gravity_form_years(). Just update it there.
	private function v5_8_0() {
		$cmc_ifound_form_id = get_option( 'cmc_ifound_form_id' );
		$form = GFAPI::get_form( $cmc_ifound_form_id );
		foreach ($form['fields'] as $index => $field) {
			if ($field['label'] === 'Year Built') {
				$choices = $field['choices'];

				// Check if we've already done this
				foreach ($choices as $choice) {
					if ($choice['text'] === '2018') {
						return;
					}
				}

				foreach ([2018, 2019, 2020] as $year) {
					$choices[] = [
						'text' => strval($year),
						'value' => strval($year),
						'isSelected' => false,
						'price' => '',
					];
				}
				$form['fields'][$index]['choices'] = $choices;
				GFAPI::update_form( $form, $cmc_ifound_form_id );
				break;
			}
		}
	}



	private function update_latest_meta_for_contacts($meta_key_to_update, $meta_key_clause) {
		global $wpdb;

		$contact_post_types_str = join(',',
			$this->util()->quote_elements(iFoundJointContact::$all_contact_post_types)
		);

		$sql = <<<SQL
			SELECT post_id, meta_value
			FROM $wpdb->postmeta pm
			JOIN $wpdb->posts p
			ON pm.post_id = p.ID
			WHERE p.post_type IN ($contact_post_types_str)
			AND meta_key $meta_key_clause
			ORDER BY meta_id DESC
SQL;

		$meta_array = $wpdb->get_results($sql);

		$latest_visits_by_contact_id = [];
		if (is_array($meta_array) && !empty($meta_array) && isset($meta_array[0])) {
			foreach ($meta_array as $meta) {
				if (!array_key_exists($meta->post_id, $latest_visits_by_contact_id)) {
					$latest_visit = explode(';', $meta->meta_value)[0];
					$latest_visits_by_contact_id[$meta->post_id] = $latest_visit;
				}
			}
			foreach ($latest_visits_by_contact_id as $contact_id => $latest_visit) {
				update_post_meta($contact_id, $meta_key_to_update, $latest_visit);
			}
		}
	}

	// There are 5 different wp_postmeta records we create when we track a user's visits.
	// To be able to sort contacts by latest visit, we take the most recent record per
	// contact and create a wp_postmeta record with key 'latest_visit'.
	private function update_latest_visit($meta_key_clause) {
		$this->update_latest_meta_for_contacts('latest_visit', $meta_key_clause);
	}

	private function v5_9_0() {
		// Do nothing.
		// We previously did upgrade work here that is superceded by v5_9_1.
	}

	private function v5_9_1() {
		global $wpdb;

		$sql = <<<SQL
			SELECT 'x'
			FROM $wpdb->postmeta pm
			JOIN $wpdb->posts p
			ON pm.post_id = p.ID
			WHERE p.post_type = 'contacts'
			AND meta_key = 'latest_visit'
			limit 1
SQL;

		$meta_array = $wpdb->get_results($sql);
		$has_latest_visits = count($meta_array) > 0;
		if ($has_latest_visits) {
			return;
		}

		$this->update_latest_visit("IN ('advanced_view', 'detail_view', 'page_view', 'polygons_view', 'results_view')");
	}

	private function v5_10_0() {
		// Must register the taxonomy before we insert a term using the taxonomy.
		iFoundSaveThis::new_hookless()->taxonomy();

		if (term_exists('Instant Update Recently Closed', 'save_type')) {
			return;
		}

		$term_id = wp_insert_term( 'Instant Update Recently Closed', 'save_type' );

		iFoundEmail::new_hookless()->taxonomy();
		$new_post_content = $this->util()->get_html( 'email/', 'instant-update-recently-closed-notice' );
		$template = [
			'default_key'	=> 'instant_update_recently_closed',
			'email_type'	=> 'content',
			'subject'		=> '{ContactFirstName} These homes recently closed near you',
			'post_title'	=> 'Instant Update Recently Closed Notification',
			'post_content'	=> $new_post_content,
		];
		$email_post = array(
			'post_title'    => $template['post_title'],
			'post_content'  => $template['post_content'],
			'post_status'   => 'publish',
			'post_type'		=> 'ifound_email',
			'post_author' 	=> 1,
		);
		$post_id = wp_insert_post( $email_post );
		wp_set_object_terms( $post_id, $template['email_type'], 'ifound_email_type' );
		add_post_meta( $post_id, 'subject', $template['subject'] );

		$crm_settings = get_option( 'ifound_crm_settings', [] );
		$crm_settings[ $template['default_key'] ] = $post_id;
		update_option( 'ifound_crm_settings', $crm_settings );

		$instant_update_post_id = $crm_settings['instant_update'];
		if ($instant_update_post_id !== 0) {
			$instant_update_post = get_post( $instant_update_post_id );
			if ($instant_update_post) {
				$updated_post_content = $instant_update_post->post_content;
				$updated_post_content_without_newlines = str_replace("\n", '', $updated_post_content);
				$old_content = '<p>{ContactFirstName}, There has been an update to a search saved to your account. Click here to see the update::</p><p>{AlertLink}</p>';
				if ($updated_post_content_without_newlines === $old_content) {
					$updated_post_content = $new_post_content;
				}
				$updated_post_content = str_replace('{AlertLink}', '{InstantUpdateLinks}', $updated_post_content);
				wp_update_post([
					'ID' => $instant_update_post->ID,
					'post_content' => $updated_post_content,
				]);

				update_post_meta($instant_update_post_id, 'subject', '{ContactFirstName} There are new homes for sale near you', '{ContactFirstName} There is an update to your property search!');
			}
		}
	}

	private function v5_10_1() {
		// Do nothing.
		// This is a meta-update that proves our plugin has updated, and has run the previous updates.
		// See: https://ifoundagent.teamwork.com/#/tasks/********
	}

	private function remove_newlines($str) {
		return str_replace("\r", '', str_replace("\n", '', $updated_post_content));
	}

	// I accidentally updated the Instant Update content to be the Instant Update Recently Closed content in
	// the update for v5_10_0. If that happened, update it to the proper content for Instant Update.
	private function v5_10_2() {
		$instant_update_post_content = $this->util()->get_html( 'email/', 'instant-update-notice' );
		$instant_update_recently_closed_post_content = $this->util()->get_html( 'email/', 'instant-update-recently-closed-notice' );
		$crm_settings = get_option( 'ifound_crm_settings', [] );
		$instant_update_post_id = $crm_settings['instant_update'];
		if ($instant_update_post_id !== 0) {
			$instant_update_post = get_post( $instant_update_post_id );
			if ($instant_update_post) {
				// Removing newlines is necessary I think because the template file has \r\n and the database must only use \n?
				if ($this->remove_newlines($instant_update_post->post_content) === $this->remove_newlines($instant_update_recently_closed_post_content)) {
					wp_update_post([
						'ID' => $instant_update_post->ID,
						'post_content' => $instant_update_post_content,
					]);
				}
			}
		}
	}

	private function v5_10_3() {
		delete_option('ifound_stats_settings');
	}

	private function v5_11_0() {
		global $mls_associations;

		$option = get_option( 'ifound_api_settings' );
		$mls_name = strtolower( $option['mls_name'] );
		if ($mls_name === 'crmls') {
			do_action( 'ifound_refresh_transients', true );
			$prop_type = array_keys((array)$mls_associations->prop_type);
			$settings = get_option( 'ifound_search_settings' );
			$settings['prop_type'] = $prop_type;
			update_option('ifound_search_settings', $settings);
		}
	}

	private function v5_11_1() {
		// Do it again. I had forgotten to change the MLS Assocation at https://mls-associations.ifoundadmin.com/wp-admin/post.php?post=70&action=edit before deploying.
		$this->v5_11_0();
	}

	private function v5_11_2() {
		iFoundCmc::cmc_update_form_ptypes();
	}

	// We added rental and land data to recolorado, so we need to force our websites to update themselves with the
	// MLS classes. Also, the property types were wrong, so force those to update too.
	private function v5_11_3() {
		global $mls_associations;

		if (iFoundIdx::mls_name() === 'recolorado') {
			do_action( 'ifound_refresh_transients', true );
			$settings = get_option( 'ifound_search_settings' );
			$prop_type = array_keys((array)$mls_associations->prop_type);
			$settings['prop_type'] = $prop_type;
			$mls_class = array_keys((array)$mls_associations->mls_class);
			$settings['mls_class'] = $mls_class;
			update_option('ifound_search_settings', $settings);
		}
	}

	// Create CRM settings for team members, including copying existing email templates
	private function v5_12_0() {
		$args = array(
			'role' => iFoundTeams::$role_name,
		);
		$users = get_users( $args );
		$ifound_teams = iFoundTeams::new_hookless();
		foreach ($users as $user) {
			$ifound_teams->create_crm_settings($user->ID);
		}
	}

	// Create teams options
	private function v5_12_1() {
		update_option('ifound_teams_settings', []);
	}

	// Remove ifa-mls usermeta value because it's not used.
	private function v5_12_2() {
		// I got this from https://wordpress.stackexchange.com/a/308463/27896
		delete_metadata(
			'user',        // the meta type
			0,             // this doesn't actually matter in this call
			'ifa-mls',     // the meta key to be removed everywhere
			'',            // this also doesn't actually matter in this call
			true           // tells the function "yes, please remove them all"
		);
	}

	// Change the usermeta named ifa-agent-phone to ifa-agent-office-phone
	// Change the ifa-office-location usermeta value to ifa-agent-city.
	private function v5_12_3() {
		$args = array(
			'role' => iFoundTeams::$role_name,
		);
		$users = get_users( $args );
		foreach ($users as $user) {
			$phone = get_user_meta($user->ID, 'ifa-agent-phone', true);
			update_user_meta($user->ID, 'ifa-agent-office-phone', $phone);

			$city = get_user_meta($user->ID, 'ifa-office-location', true);
			update_user_meta($user->ID, 'ifa-agent-city', $city);
		}
		delete_metadata(
			'user',
			0,
			'ifa-agent-phone',
			'',
			true
		);
		delete_metadata(
			'user',
			0,
			'ifa-office-location',
			'',
			true
		);
	}

	// Remove unused edit_team_member* roles from administrators and team_members.
	private function v5_12_4() {
		$role = get_role('administrator');
		$role->remove_cap('edit_team_member');

		$role = get_role(iFoundTeams::$role_name);
		// Some old installs, at least on my local dev, didn't have this role.
		if ($role) {
			$role->remove_cap('edit_team_member');
			$role->remove_cap('edit_team_members');
			$role->remove_cap('edit_team_memberss');
		}
	}

	// Copy base options like campaign criteria to team members.
	private function v5_12_5() {
		$args = array(
			'role' => iFoundTeams::$role_name,
		);
		$users = get_users( $args );
		$ifound_teams = iFoundTeams::new_hookless();
		foreach ($users as $user) {
			$ifound_teams->copy_base_options_to_user($user->ID);
		}
	}

	// Delete this term ('Team Lead') that we were using during development (including a few live sites for testing),
	// but we don't want now.
	private function v5_12_6() {
		$taxonomy = iFoundContacts::new_hookless()->taxonomy();
		// I was previously using $taxonomy->name in code further below, but it wasn't working in production. Having
		// half-heartedly debugged in production, it was due to the line above not returning anything, perhaps due to
		// changes in wordpress core, and just forcing the value here is a way around it.
		$taxonomy_to_delete = 'contacts_status';
		$terms = get_terms([
			'taxonomy' => $taxonomy_to_delete,
			'hide_empty' => false,
		]);
		foreach ($terms as $term) {
			if ($term->name === 'Team Lead') {
				$result = wp_delete_term($term->term_id, $taxonomy_to_delete);
				break;
			}
		}
	}

	// We want to bump the version, but don't need to do anything. Here's why:
	// In production, many sites were at 5.11.3, and since I had previously deployed 5.12.6, I manually commented out
	// lines in production to skip updates 5.12.0 through 5.12.5, but since those sites already had 5.12.6 in their list
	// of updates processed (wp_options meta_key 'ifound_plugin_updates' array), it didn't see the version to 5.12.6
	// and got into an update loop. Adding an empty version here fixes this.
	private function v5_12_7() {
		// Do nothing
	}

	// Another no-op, trying to get production websites to update.
	private function v5_12_8() {
		// Do nothing
	}

	// Shared email templates, as in, those used by admins, should be owned by user 1, the super admin. In production,
	// I'm seeing old sites with ownership not user 1, and even some newer sites. I'm not sure how that happened (with
	// with the exception of templates created by the user), because our startup code assigns 1 as the post author.
	// So, fix them here: All email templates owned by admins should have the owner changed to 1. Just in case there's a
	// problem and we need to undo it, save off who used to own it.
	private function v5_12_9() {
		iFoundEmail::new_hookless()->post_type();

		$wp_user_search = new WP_User_Query([
			'role'   => 'administrator',
			'fields' => 'ID',
		]);
		$admin_ids = $wp_user_search->get_results();

		$posts = get_posts([
			'post_type'   => 'ifound_email',
			'numberposts' => -1,
			'author__in'  => $admin_ids,
		]);
		$count = count($posts);
		foreach ($posts as $post) {
			update_post_meta($post->ID, 'ifound_former_post_author_5.12.9', $post->post_author);
			wp_update_post([
				'ID'          => $post->ID,
				'post_author' => 1,
			]);
		}
	}

	private function v5_13_0() {
		do_action('ifound_install_db_limits_cron');
	}

	// Update existing email templates and campaigns to use the new {Listings} tag.
	// For each email and campaign, we'll update the content, and save off the old content just in case in postmeta.
	// This will allow us to undo if needed, but also helps us keep track of which ones have been updated, so for big
	// rollouts, like on westusaagents.com where there are hundreds of accounts, it might have to run the upgrade code
	// multiple times, and this way it won't ever try to update the content more than once since it knows it's been
	// done.
	private function v5_14_0() {
		//posts to get:
		// 1) get email templates from contact manager settings
		// 		not just admin, but all ifound_crm_settings%
		//      Instant Update New Listing Notification
		//      Instant Update Recently Closed Notification
		//      Homeowner Campaign
		// 2) by title
		//      a) Market Update - Home Owner
		//      b) Market Update - Past Client
		// 3) get campaigns
		//      a) market-update
		//      b) homeowner-campaign


		$former_content_meta_key = 'ifound_former_content_5.14.0';
		$add_listings_to_post = function($post) use ($former_content_meta_key) {
			$additional_content = <<<EOF
{Listings}
<p>See all homes in this list {AlertLink}</p>
EOF;
			// For content with {InstantUpdateLinks}, we want to replace that. Otherwise, we want to postfix the
			// content.
			$regex = '#(?:<p>\s*?)?{InstantUpdateLinks}(?:\s*?</p>)?#im';
			$matches = null;
			$former_post_content = $post->post_type === 'save_this' ? get_post_meta($post->ID, 'custom_content', true) : $post->post_content;
			$regex_matched = preg_match_all($regex, $former_post_content, $matches);
			$updated_post_content = null;
			if ($regex_matched === 1) {
				$updated_post_content = preg_replace($regex, $additional_content, $former_post_content);
			} else {
				$updated_post_content = $former_post_content . "\n" . $additional_content;
			}
			if ($post->post_type === 'save_this') {
				update_post_meta($post->ID, 'custom_content', $updated_post_content);
			} else {
				wp_update_post([
					'ID' => $post->ID,
					'post_content' => $updated_post_content,
				]);
			}
			update_post_meta($post->ID, $former_content_meta_key, $former_post_content);
		};

		global $wpdb;

		// Part 1: get email templates from contact manager settings
		$post_ids = [];
		$sql = "SELECT option_name, option_value from {$wpdb->options} where option_name like 'ifound_crm_settings%'";
		$results = $wpdb->get_results($sql, ARRAY_A);
		foreach ($results as $result) {
			$option_value = unserialize($result['option_value']);
			$user_id = substr($result['option_name'], strlen('ifound_crm_settings'));
			if ($user_id === '') {
				$user_id = iFoundAdmin::$shared_owner_ID;
			}
			$post_ids[] = $option_value['instant_update'];
			$post_ids[] = $option_value['instant_update_recently_closed'];
			$post_ids[] = $option_value['homeowner_campaign'];
		}
		$meta_query = [
			[
				'key' => $former_content_meta_key,
				'compare' => 'NOT EXISTS',
			],
		];
		$posts = get_posts([
			'numberposts' => -1,
			'post_type' => 'ifound_email',
			'post__in' => $post_ids,
			'ignore_sticky_posts' => true,
			'orderby' => 'ID',
			'meta_query' => $meta_query,
		]);
		$mapped_post_ids = array_map(function($x) {return $x->ID;}, $posts);
		foreach ($posts as $post) {
			$add_listings_to_post($post);
		}

		// Part 2: Get email templates by title
		$titles = [
			'Market Update - Home Owner',
			'Market Update - Past Client',
		];
		foreach ($titles as $title) {
			// If there is a better way to get multiple posts by title, I don't know it.
			// See: https://wordpress.stackexchange.com/a/396348/27896
			$prepared = $wpdb->prepare("SELECT ID FROM $wpdb->posts WHERE post_title = %s AND post_type = %s AND post_status = 'publish' ORDER BY ID", $title, 'ifound_email');
			$results = $wpdb->get_results($prepared, ARRAY_A);
			$post_ids = array_map(function($x) { return intval($x['ID']); }, $results);
			$posts = get_posts([
				'numberposts' => -1,
				'post_type' => 'ifound_email',
				'post__in' => $post_ids,
				'ignore_sticky_posts' => true,
				'orderby' => 'ID',
				'meta_query' => $meta_query,
			]);
			foreach ($posts as $post) {
				$has_already_been_processed = metadata_exists('post', $post->ID, $former_content_meta_key);
				if (!$has_already_been_processed) {
					$add_listings_to_post($post);
				}
			}
		}

		// Part 3: Get campaigns with a Campaign Type of Market Update or Homeowner Campaign
		iFoundSaveThis::new_hookless()->taxonomy();
		$terms = get_terms([
			'taxonomy' => 'save_type',
			'hide_empty' => false,
		]);
		$market_update_taxonomy_term = array_values(array_filter($terms, function($x) {
			return $x->name === 'Market Update';
		}))[0];
		$homeowner_campaign_taxonomy_term = array_values(array_filter($terms, function($x) {
			return $x->name === 'Homeowner Campaign';
		}))[0];
		$posts = get_posts([
			'post_type' => 'save_this',
			'numberposts' => -1,
			'orderby' => 'ID',
			'tax_query' => [
				'relation' => 'OR',
				[
					'taxonomy' => 'save_type',
					'field' => 'term_taxonomy_id',
					'terms' => $market_update_taxonomy_term->term_taxonomy_id,
				],
				[
					'taxonomy' => 'save_type',
					'field' => 'term_taxonomy_id',
					'terms' => $homeowner_campaign_taxonomy_term->term_taxonomy_id,
				],
			],
			'meta_query' => $meta_query,
		]);
		foreach ($posts as $post) {
			$add_listings_to_post($post);
		}
	}

	// Handle capabilities for drip templates.
	private function v5_15_0() {
		iFoundTeams::new_hookless()->install_teams();

		$role = get_role('team_member');
		// Get rid of this capability that was never used.
		$role->remove_cap('assign_ifound_email_type');

		$role = get_role('team_member');
		$role->add_cap('edit_drip_template');
		$role->add_cap('read_drip_template');
		$role->add_cap('delete_drip_template');
		$role->add_cap('edit_drip_templates');
		$role->add_cap('delete_drip_templates');
		$role->add_cap('publish_drip_templates');
		$role->add_cap('create_drip_templates');
		// Get rid of this capability that was never used.
		$role->remove_cap('assign_ifound_email_type');
	}

	// Handle capabilities for drip campaigns.
	private function v5_15_1() {
		$role = get_role('team_member');
		$role->add_cap('edit_drip_campaign');
		$role->add_cap('read_drip_campaign');
		$role->add_cap('delete_drip_campaign');
		$role->add_cap('edit_drip_campaigns');
		$role->add_cap('delete_drip_campaigns');
		$role->add_cap('publish_drip_campaigns');
		$role->add_cap('create_drip_campaigns');
	}

	// Wordpress uses the plural form for delete permissions.
	private function v5_15_2() {
		$role = get_role('team_member');
		$role->remove_cap('delete_save_this');
		$role->add_cap('delete_save_thiss');
	}

	// During some preview testing, we created example email and drip templates here. We chose to do that on login
	// instead.
	private function v5_15_3() {
		// Do nothing
	}

	// Delete old process_activity_reportXYZ crons. We do this as a new version as a convenient way to only do it once.
	private function v5_15_4() {
		$crons = get_option('cron');
		if (is_array($crons)) {
			foreach ($crons as $timestamp => $cron) {
				// Ignore the key/value of (e.g.) 'version' => 2
				if (is_array($cron)) {
					foreach ($cron as $hook => $info) {
						if (preg_match('/process_activity_report\d+/', $hook, $matches)) {
							$first_key = null;
							foreach ($info as $key => $data) {
								wp_unschedule_event($timestamp, $matches[0], $data['args']);
							}
						}
					}
				}
			}
		}
	}

	// It no longer makes sense to run activity reports without a "CRM ID". We remove existing cron jobs set to run
	// activity reports without a CRM ID, and set them up to run with a CRM ID for all users that have activity reports
	// enabled. However, this is still ambiguous because the ifound_crm_settings option (without a number at the end) is
	// shared among all admins. To handle that, we'll just create a cron for the "primary" admin.
	private function v5_15_5() {
		global $wpdb;

		$next_update = wp_next_scheduled(iFoundActivity::$activity_report_hook_name);
		if ($next_update) {
			wp_clear_scheduled_hook(iFoundActivity::$activity_report_hook_name);
		}
		$next_update = wp_next_scheduled(iFoundActivity::$activity_report_hook_name, ['']);
		if ($next_update) {
			wp_clear_scheduled_hook(iFoundActivity::$activity_report_hook_name, ['']);
		}

		$sql = "SELECT * FROM {$wpdb->options} WHERE option_name LIKE 'ifound_crm_settings%' ORDER BY option_name";
		$settings = $wpdb->get_results($sql);
		$schedule_activity_report_if_needed = function($time, $user_id, $is_admin) {
			if (!$is_admin) {
				// For team members, the activity report is enabled by default. But if the user isn't paying for the
				// CRM feature, we don't want to enable the activity report.
				if (!iFoundTeams::new_hookless()->has_crm_enabled(['id' => $user_id])) {
					return;
				}
			}
			$next_update = wp_next_scheduled(iFoundActivity::$activity_report_hook_name, [$user_id]);
			if (!$next_update) {
				wp_schedule_event($time, 'daily', iFoundActivity::$activity_report_hook_name, [$user_id]);
			}
		};
		foreach ($settings as $setting) {
			$settings_value = unserialize($setting->option_value);
			if ($settings_value['time_of_day'] === 'none') {
				continue;
			}

			preg_match('/ifound_crm_settings(?P<user_id>\d+)/', $setting->option_name, $matches);
			$cron_time = iFoundUtil::get_cron_time($settings_value['time_of_day']);
			if ($matches['user_id']) {
				$schedule_activity_report_if_needed($cron_time, intval($matches['user_id']), false);
			} else {
				$admin_id = iFoundAdmin::new_hookless()->get_primary_admin_id();
				$schedule_activity_report_if_needed($cron_time, $admin_id, true);
			}
		}
	}

	// To fix the inaccuracies with the Latest Email Activity page content, we need to make sure some strings match in
	// the database. We have been using 'ifound_tracking_pixel' as a meta key, but 'ifound_email_tracking_pixel_id' in
	// the meta value. Let's just have them be the same. Then we can join in the database. We'll update the meta value
	// to 'ifound_tracking_pixel'.
	private function v5_16_0() {
		global $wpdb;

		$old_value_string = 'ifound_email_tracking_pixel_id:';
		$new_value_string = 'ifound_tracking_pixel:';
		$sql = <<<EOT
			update {$wpdb->postmeta}
			set meta_value = replace(meta_value, '{$old_value_string}', '{$new_value_string}')
			where meta_key = 'activity_log' and meta_value like '%{$old_value_string}%';
EOT;
		$wpdb->query($sql);
	}

	// The idea here is to rename options for Wise Agent, and remove unused ones.
	private function v5_17_0() {
		$val = get_option('wiseagent_api_key');
		update_option(iFoundWiseAgent::$api_key_option_name, $val);
		delete_option('wiseagent_api_key');

		delete_option('initial_update_complete');
		delete_option('wa-excluded-contacts');
		delete_option('manual_sync_bumped');

		// No need to rename transients here. They'll be created with the correct name elsewhere.
		delete_transient('wa-team');

		// Remove these
		delete_transient('fetch-cron');
		delete_transient('send-cron');
	}

	// Rename meta keys of e.g. activity_log_last to ifound_activity_log_wise_agent_count.
	private function v5_17_1() {
		global $wpdb;

		$meta_keys = ['activity_log', 'advanced_view', 'page_view', 'results_view'];
		foreach ($meta_keys as $meta_key) {
			$old_meta_key = $meta_key . '_last';
			$new_meta_key = "ifound_{$meta_key}_wise_agent_count";
			$sql = <<<EOT
				update {$wpdb->postmeta}
				set meta_key = '{$new_meta_key}'
				where meta_key = '{$old_meta_key}';
EOT;
			$wpdb->query($sql);
		}
	}

	// Rename postmeta meta key from wa_id to ifound_wise_agent_id
	// For consistency, rename meta keys of e.g. ifound_activity_log_wise_agent_count to
	// ifound_wise_agent_activity_log_count.
	private function v5_17_2() {
		global $wpdb;
		$sql = "update {$wpdb->postmeta} set meta_key = 'ifound_wise_agent_id' where meta_key = 'wa_id'";
		$wpdb->query($sql);

		$meta_keys = ['activity_log', 'advanced_view', 'page_view', 'results_view'];
		foreach ($meta_keys as $meta_key) {
			$old_meta_key = "ifound_{$meta_key}_wise_agent_count";
			$new_meta_key = "ifound_wise_agent_{$meta_key}_count";
			$sql = <<<EOT
				update {$wpdb->postmeta}
				set meta_key = '{$new_meta_key}'
				where meta_key = '{$old_meta_key}';
EOT;
			$wpdb->query($sql);
		}
	}

	// Assign any posts (of types owned by iFound) with post_author = 0 to to be the primary admin ID.
	private function v5_18_0() {
		global $wpdb;

		$condition = <<<CONDITION
				post_type in ('contacts', 'private_contact', 'save_this')
			and post_author = 0
CONDITION;

		// First, let's make sure we can undo this if necessary by marking posts-to-be-updated with postmeta.
		$sql = <<<SQL
			insert into {$wpdb->postmeta} (post_id, meta_key, meta_value)
			select id, 'ifound:v5.18.0:post_author_was_zero', 'yes'
			from {$wpdb->posts}
			where {$condition}
SQL;
		$wpdb->query($sql);

		// Now change the post author to be non-zero.
		$primary_admin_id = iFoundAdmin::new_hookless()->get_primary_admin_id();
		$sql = <<<EOT
			update {$wpdb->posts} set post_author = {$primary_admin_id}
			where {$condition}
EOT;
		$wpdb->query($sql);
	}

	private function update_ifound_detalis_settings($fn) {
		$ifound_detalis_settings = get_option('ifound_detalis_settings');
		$slider_script = $ifound_detalis_settings['details_slider_script'];
		$slider_script = $fn($slider_script);
		$ifound_detalis_settings['details_slider_script'] = $slider_script;
		update_option('ifound_detalis_settings', $ifound_detalis_settings);
	}

	// Update the Slick slider script
	private function v5_19_0() {
		$this->update_ifound_detalis_settings(function($str) {
			$s = $str;
			// Remove lines that are the defaults anyway.
			// Is there a better way to deal with Windows line endings? I can't use $ here as end-of-line, so I use my own
			// \r?\n to match windows and unix newlines.
			$s = preg_replace('/^.*slidesToShow: 1,\r?\n/m', '', $s);
			$s = preg_replace('/^.*slidesToScroll: 1,\r?\n/m', '', $s);
			$s = preg_replace('/^.*centerMode:false,?\r?\n/m', '', $s);
			$s = preg_replace('/^.*dots: false,\r?\n/m', '', $s);

			// We need to be careful here because we want to replace A with A,B, but then change a later B without changing
			// the first B. So we use a temp substitution.
			// Also, just a reminder (not sure where else to put this) that the Slick carousel breakpoints are old school,
			// where the "main" (non-breakpoint) settings are for desktop, and then each breakpoint is to be thought of as
			// if you start to shrink the screen. As opposed to the modern way of thinking of it which is opposite, where we
			// think mobile first and breakpoints are as you get bigger.
			// Oh, fun fact that I learned. There is a setting for this actually called mobileFirst that defaults to false.
			$s = preg_replace('/fade: true,/', 'TEMP_FADE_TRUE', $s);
			$s = preg_replace('/adaptiveHeight: true/', "fade: false,\n        adaptiveHeight: false,", $s);
			$s = preg_replace('/TEMP_FADE_TRUE/', "fade: true,\n    adaptiveHeight: true,", $s);

			return $s;
		});
	}

	// Update the Slick slider script again because adaptiveHeight: true doesn't work with lazy loading.
	private function v5_19_1() {
		$this->update_ifound_detalis_settings(function($str) {
			$s = $str;
			$s = preg_replace('/^.*adaptiveHeight:.*\r?\n/m', "", $s);
			return $s;
		});
	}

	private function change_cron_time($option_name, $user_id) {
		$ifound_crm_settings = get_option($option_name);
		if ($ifound_crm_settings) {
			$time_of_day = sanitize_text_field($ifound_crm_settings['time_of_day']);
			if ($time_of_day !== 'none') {
				wp_clear_scheduled_hook(iFoundActivity::$activity_report_hook_name, [$user_id]);
				$cron_time = iFoundUtil::get_cron_time($time_of_day);
				// Add 0-3600 seconds, so that all crons from all sites don't run at the same time. Base the number of
				// seconds on a numerical hash of the domain plus the user id, trying to space multiple users out by
				// about 5 minutes.
				$host = $this->util()->get_host();
				// To generate a numerical hash, I used this idea:
				// https://stackoverflow.com/a/3379493/135101
				$bignum = hexdec(substr(sha1($host), 0, 15));
				$extra_seconds = ($bignum + $user_id * 5 * 60) % (60 * 60);
				$cron_time += $extra_seconds;
				wp_schedule_event($cron_time, 'daily', iFoundActivity::$activity_report_hook_name, [$user_id]);
			}
		}
	}

	// For the primary admin and all team members:
	// Update the process_activity_report cron to run at a random time within the scheduled hour, so that all crons from
	// all our sites don't run at the same time.
	private function v5_20_0() {
		$option_name = 'ifound_crm_settings';

		$primary_admin_id = iFoundAdmin::new_hookless()->get_primary_admin_id();
		$option_name_for_admin = $option_name;
		$this->change_cron_time($option_name, $primary_admin_id);

		$args = ['role' => iFoundTeams::$role_name];
		$users = get_users($args);
		foreach ($users as $user) {
			$user_id = $user->ID;
			$option_name_for_user = $option_name . $user_id;
			$this->change_cron_time($option_name_for_user, $user_id);
		}
	}

	// The previous update turned on crons for all team members, but shouldn't have, because some team members don't
	// have the CRM feature enabled. For those team members, unschedule the cron.
	private function v5_20_1() {
		$option_name = 'ifound_crm_settings';
		$args = ['role' => iFoundTeams::$role_name];
		$users = get_users($args);
		foreach ($users as $user) {
			$user_id = $user->ID;
			if (!iFoundTeams::new_hookless()->has_crm_enabled(['id' => $user_id])) {
				wp_clear_scheduled_hook(iFoundActivity::$activity_report_hook_name, [$user_id]);
			}
		}
	}

	private function v5_20_2() {
		// This update step doesn't actually do anything itself. But it's a reminder that we are altering the database.
		// We are creating a new ifound_emails table for tracking emails. See the iFoundEmailTrackingPixel class.
	}

	// Update 2: It doesn't matter now, but this table is only created for customers with the CRM feature. Therefore
	// this caused an error for sites that don't have it. I think it was a one-time error, and we don't need to do
	// anything about it now. But keep in mind not all sites do email.
	//
	// Update: unfortunately, deploying this update didn't fix the issue and I don't entirely understand why. While the
	// new code (which writes to the ifound_emails table whenever an email is sent) was deployed, it should have written
	// all records to the table, so I'm not sure why in some cases I was seeing some missing. At that point though, it
	// was not writing to the postmeta table (with meta_keys starting with 'ifound_tracking_pixel:', so when I wiped out
	// the ifound_emails table with this update, we would have lost the stats for the emails sent in the meantime. It
	// shouldn't be a huge deal, there are now a few emails that we don't have stats on. We just have to keep in mind
	// that we can't expect to have stats, as in, make sure our code doesn't assume we do and cause an error.
	//
	// Today I deployed the code for the tracking pixel table, and it took down most sites, as they all tried to run the
	// long running queries at once. So I reverted. And I'm about to run things again (albeit in a more manual fashion).
	// However, in the meantime, some records that should be in the ifound_emails table will have been missed. The
	// easiest thing to do is to clear that table and regenerate its data.
	private function v5_20_3() {
		global $wpdb;

		$sql = "truncate " . iFoundEmailTrackingPixel::new_hookless()->get_table_name();
		$wpdb->query($sql);
		$option_name_prefix = 'ifound_update_25753321_part1_';
		delete_option($option_name_prefix . 'offset');
		delete_option($option_name_prefix . 'done');
	}

	// Approx 108 sites have a cron job for user 1 in addition to another user. This isn't what we want, at least on
	// multisite. Since it's theoretically ok on a non-multisite, I'll fix those ones by hand.
	public function v5_21_0() {
		if (is_multisite() && is_super_admin(1)) {
			// I don't think there's a WP API to get all the crons for a given name (irrespective of args). So I use the
			// cron option array directly. Doesn't seem safe but what choice do I have?
			$crons = get_option('cron');
			if (is_array($crons)) {
				$found_cron_for_user1 = false;
				$found_cron_for_not_user1 = false;
				$timestamp_for_user1 = null;
				foreach ($crons as $timestamp => $cron) {
					// Ignore the key/value of (e.g.) 'version' => 2
					if (is_array($cron)) {
						foreach ($cron as $hook => $info) {
							if ($hook === iFoundActivity::$activity_report_hook_name) {
								foreach ($info as $key => $data) {
									if (count($data['args']) && $data['args'][0] === 1) {
										$found_cron_for_user1 = true;
										$timestamp_for_user1 = $timestamp;
									}
									if (count($data['args']) && $data['args'][0] !== 1) {
										$found_cron_for_not_user1 = true;
									}
								}
							}
						}
					}
				}
				if ($found_cron_for_user1 && $found_cron_for_not_user1) {
					wp_unschedule_event($timestamp_for_user1, iFoundActivity::$activity_report_hook_name, [1]);
					$crm_settings = get_option('ifound_crm_settings');
					if ($crm_settings) {
						$crm_settings['time_of_day'] = 'none';
						update_option('ifound_crm_settings', $crm_settings);
					}
				}
			}
		}
	}

	// In the previous update, I changed the time_of_day option to 'none', when I shouldn't have touched it. We can
	// figure out what it should be based on the remaining cron job.
	public function v5_21_1() {
		// Only do this if we would have done it in the previous update.
		if (is_multisite() && is_super_admin(1)) {
			$crons = get_option('cron');
			if (is_array($crons)) {
				foreach ($crons as $timestamp => $cron) {
					// Ignore the key/value of (e.g.) 'version' => 2
					if (is_array($cron)) {
						foreach ($cron as $hook => $info) {
							if ($hook === iFoundActivity::$activity_report_hook_name) {
								foreach ($info as $key => $data) {
									if (count($data['args']) && $data['args'][0] !== 1) {
										$user_id = $data['args'][0];
										if ($this->util()->user_has_admin_role($user_id)) {
											$datetime = DateTime::createFromFormat('U', $timestamp);
											$timezone = iFoundUtil::new_hookless()->wp_timezone();
											$datetime->setTimezone($timezone);
											$hour = $datetime->format('G');
											$time_of_day_str = $hour . ':00:00';
											$crm_settings = get_option('ifound_crm_settings');
											if ($crm_settings) {
												$crm_settings['time_of_day'] = $time_of_day_str;
												update_option('ifound_crm_settings', $crm_settings);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// In the previous updates, I deleted the cron for user ID 1, and not for the non-1 admin user. It should have been
	// the other way around. In the CRM settings page (wp-admin/edit.php?post_type=contacts&page=crm-settings), if an
	// admin hits save, it will create the cron using the ID of 1, and that point there would be two crons, meaning
	// there'd be a duplicate, and we'd be back to where we started. So, change the user ID to 1.
	public function v5_21_2() {
		// Only do this if we would have done it in the previous update.
		if (is_multisite() && is_super_admin(1)) {
			$crons = get_option('cron');
			if (is_array($crons)) {
				foreach ($crons as $timestamp => $cron) {
					// Ignore the key/value of (e.g.) 'version' => 2
					if (is_array($cron)) {
						foreach ($cron as $hook => $info) {
							if ($hook === iFoundActivity::$activity_report_hook_name) {
								foreach ($info as $key => $data) {
									if (count($data['args']) && $data['args'][0] !== 1) {
										$user_id = $data['args'][0];
										if ($this->util()->user_has_admin_role($user_id)) {
											// First, unschedule any for user 1, just in case, ensuring we don't end up
											// with duplicate events for user 1.
											wp_unschedule_event($timestamp,
												iFoundActivity::$activity_report_hook_name, [1]);
											// Remove for other user
											wp_unschedule_event($timestamp,
												iFoundActivity::$activity_report_hook_name, [$user_id]);
											// Add back
											wp_schedule_event($timestamp, 'daily',
												iFoundActivity::$activity_report_hook_name, [1]);
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// I'm not sure what I was looking at in the last one, but I had backwards (right... even though I said I had it
	// backwards then). So, now, if we see a cron for user ID 1, change it to the primary admin.
	public function v5_21_3() {
		// Only do this if we would have done it in the previous update.
		if (is_multisite() && is_super_admin(1)) {
			$crons = get_option('cron');
			if (is_array($crons)) {
				foreach ($crons as $timestamp => $cron) {
					// Ignore the key/value of (e.g.) 'version' => 2
					if (is_array($cron)) {
						foreach ($cron as $hook => $info) {
							if ($hook === iFoundActivity::$activity_report_hook_name) {
								foreach ($info as $key => $data) {
									if (count($data['args']) && $data['args'][0] === 1) {
										// First, unschedule any for user 1, just in case, ensuring we don't end up
										// with duplicate events for user 1.
										wp_unschedule_event($timestamp,
											iFoundActivity::$activity_report_hook_name, [1]);

										// Add back for primary admin
										$user_id = iFoundAdmin::new_hookless()
											->get_this_user_id_or_primary_admin_id();
										wp_schedule_event($timestamp, 'daily',
											iFoundActivity::$activity_report_hook_name, [$user_id]);
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Unschedule crons for campaigns that have been deleted.
	public function v5_22_0() {
		$crons = get_option('cron');
		if (is_array($crons)) {
			foreach ($crons as $timestamp => $cron) {
				// Ignore the key/value of (e.g.) 'version' => 2
				if (is_array($cron)) {
					foreach ($cron as $hook => $info) {
						if ($hook === iFoundDripCampaign::$cron_event_name) {
							foreach ($info as $key => $data) {
								if (count($data['args'])) {
									$post_id = $data['args'][0];
									$post = get_post($post_id);
									if (!$post) {
										wp_unschedule_event($timestamp,
											iFoundDripCampaign::$cron_event_name, $data['args']);
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// We will force the open house criteria into search criteria and campaign criteria.
	private function v5_23_0() {
		// Note the misspelling of 'builder' in the option name! It's a legacy bug that we must continue.
		$option_name = 'ifound_more_campaign_bulder_criteria';
		$more_campaign_bulder_criteria = get_option($option_name) ?: array();
		if (!key_exists('open_house', $more_campaign_bulder_criteria)) {
			$more_campaign_bulder_criteria['open_house'] = 'open_house';
			update_option($option_name, $more_campaign_bulder_criteria);
		}

		$option_name = 'ifound_more_adv_search_criteria';
		$more_adv_search_criteria = get_option($option_name) ?: array();
		if (!key_exists('open_house', $more_adv_search_criteria)) {
			$more_adv_search_criteria['open_house'] = 'open_house';
			update_option($option_name, $more_adv_search_criteria);
		}
	}

	// Remove these options because we never allowed people to customize them anyway, or no one did.
	private function v5_23_1() {
		$option_names = [
			'ifound_results_settings' => 'results_body',
			'ifound_detalis_settings' => 'details_body',
			'ifound_featured_settings' => 'featured_body',
		];
		foreach ($option_names as $option_name => $sub_option_name) {
			$option = get_option($option_name);
			unset($option[$sub_option_name]);
			update_option($option_name, $option);
		}
	}

	// Change decimal 0 to string
	private function v5_24_0() {
		global $wpdb;
		$sql = "select option_name, option_value from {$wpdb->options} where option_name like 'ifound_crm_settings%'";
		$results = $wpdb->get_results($sql, ARRAY_A);
		foreach ($results as $result) {
			$unserialized = unserialize($result['option_value']);
			if ($unserialized['notification_cc_email_addresses'] === 0.0) {
				$unserialized['notification_cc_email_addresses'] = '';
				update_option($result['option_name'], $unserialized);
			}
		}
	}

	// Delete all from_name and from_email postmeta, because we haven't allowed those to be customized per campaign for
	// months now.
	private function v5_25_0() {
		global $wpdb;
		$sql = "delete from {$wpdb->postmeta} where meta_key in ('from_name', 'from_email')";
		$wpdb->query($sql);
	}

	private function v5_26_0() {
		$this->update_latest_meta_for_contacts('latest_activity_log',
			"= 'activity_log'"
		);
	}

	private function v5_27_0() {
		if (iFoundIdx::mls_name() === 'paaraz') {
			$option = get_option( 'ifound_api_settings' );
			$option['mls_name'] = 'paaraz_mlg';
			update_option('ifound_api_settings', $option);
			iFoundSaveThisUpgrader::new_hookless()->add_action_scheduler_action_to_upgrade();
		}
	}

	private function v5_28_0() {
		as_enqueue_async_action('ifound_v5_28_0_update', [], 'ifound');
	}

	private function v5_29_0() {
		// The way I've been doing things, each version represents a change to the data. In this case, I have no change
		// to the data. I'm bumping the version because I changed a bunch of JS files and want the version used in the
		// calls to wp_register_script() to be changed for the sake of cache busting.
	}

	// Remove the Featued Listings widget from the After Details sidebar.
	private function v5_30_0() {
		$sidebars = get_option('sidebars_widgets', []);
		$has_made_change = false;
		if (isset($sidebars['after-details'])) {
			$settings = &$sidebars['after-details'];
			for ($i = count($settings) - 1; $i >= 0; $i--) {
				$widget_name = $settings[$i];
				if (strpos($widget_name, 'ifound_featured_listings') !== false) {
					array_splice($settings, $i, 1);
					$has_made_change = true;
				}
			}
		}
		if ($has_made_change) {
			update_option('sidebars_widgets', $sidebars);
		}
	}

	private function v5_31_0() {
		// See note in v5_29_0().
	}

	private function v5_32_0() {
		// No change in this version. The purpose is to smooth out a mistake where I set the plugin version, in
		// ifound.php, to 5.31.1, put in an update line in run_updates() of this file, but forgot to put the necessary
		// function called v5_31_1(), which blew up our sites. So I took out the line in run_updates(), but didn't
		// roll back the version in ifound.php. So our sites have been continuously pushing updates to ifoundadmin.com,
		// wasting CPU there.
	}

	// Bump for sake of JS asset cache busting.
	private function v5_33_0() {
		// Do nothing. See note in v5_29_0().
	}

	private function v5_35_0() {
		as_enqueue_async_action('ifound_v5_35_0_update', [], 'ifound');
	}

	// Do it again, because:
	// 1) A few jobs failed, due to lack of memory and timeouts (why?). It won't hurt to run others again.
	// 2) We now disable drip campaigns too.
	private function v5_35_1() {
		as_enqueue_async_action('ifound_v5_35_1_update', [], 'ifound');
	}

	// Again. We had some sites fail because of memory exhaustion, because we were getting all contact info, when really
	// all we need is contact ID.
	private function v5_35_2() {
		as_enqueue_async_action('ifound_v5_35_2_update', [], 'ifound');
	}

	// Give the administrator role these cabilities regarding contacts (plural: contactss).
	// I think we should've added these capabilities a long time ago, but we used the crutch of the map_meta_cap filter
	// in ifound/crm/admin/class-ifound-contacts-admin.php, which I'm removing.
	private function v5_37_0() {
		$role = get_role('administrator');
		$role->add_cap('edit_contactss');
		$role->add_cap('edit_others_contactss');
		$role->add_cap('edit_published_contactss');
		$role->add_cap('read_contactss');
		$role->add_cap('read_others_contactss');
		$role->add_cap('delete_contactss');
		$role->add_cap('delete_others_contactss');
		$role->add_cap('delete_published_contactss');
	}

	// Now that I know that singular capabilities (yes, 'contacts' is singular for us) are for meta capabilities, and
	// the advice from https://learn.wordpress.org/tutorial/custom-post-types-and-capabilities suggests not to give meta
	// capabilities directly to roles, I'll remove them.
	private function v5_37_1() {
		$role = get_role(iFoundTeams::$role_name);

		if ($role) {
			$role->remove_cap('edit_contacts');
			$role->remove_cap('read_contacts');
			$role->remove_cap('delete_contacts');

			$role->add_cap('edit_published_contactss');
		}
	}

	// Add the publish_contactss role to admins
	private function v5_37_2() {
		$role = get_role('administrator');

		if ($role) {
			$role->add_cap('publish_contactss');
		}
	}

	private function update_cmc_gravity_form_years() {
		$cmc_ifound_form_id = get_option( 'cmc_ifound_form_id' );
		$form = GFAPI::get_form( $cmc_ifound_form_id );
		foreach ($form['fields'] as $index => $field) {
			if ($field['label'] === 'Year Built') {
				$choices = $field['choices'];

				// Check if we've already done this
				foreach ($choices as $choice) {
					if ($choice['text'] === '2025') {
						return;
					}
				}

				foreach ([2025] as $year) {
					$choices[] = [
						'text' => strval($year),
						'value' => strval($year),
						'isSelected' => false,
						'price' => '',
					];
				}
				$form['fields'][$index]['choices'] = $choices;
				GFAPI::update_form( $form, $cmc_ifound_form_id );
				break;
			}
		}
	}

	// Add years to CMC gravity form. We should do this at the start of every year. Or... pad it out a few years.
	private function v5_38_0() {
		$this->update_cmc_gravity_form_years();
	}

	// Update ARMLS (RETS) to ARMLS Spark.
	private function v5_40_0() {
		iFoundSaveThisUpgrader::new_hookless()->transition_mls();
		as_enqueue_async_action('ifound_v5_40_0_update', [], 'ifound');
	}

	// Add the publish_contactss role to admins.
	// This is the same as 5_37_2, but I had forgotten to add it to the startup capabilities.
	private function v5_42_0() {
		$role = get_role('administrator');

		if ($role) {
			$role->add_cap('publish_contactss');
		}
	}

	// Update the Search Settings page. If the agent previously had contingent values set to be shown, make those same
	// ones shown in the list status field.
	// I tried to do this as part of the 5.40 update, but it didn't work. See iFoundUpdateHelper::v5_40_0_update().
	private function v5_43_0() {
		if (iFoundIdx::mls_name() !== 'armls_spark') {
			return;
		}

		$option_name = 'ifound_search_settings';
		$search_settings = get_option($option_name);
		$has_search_settings_change = false;
		if (in_array('UCB', $search_settings['contingent'] ?? [])) {
			$search_settings['list_status'][] = 'UCB';
			$has_search_settings_change = true;
		}
		if (in_array('CCBS', $search_settings['contingent'] ?? [])) {
			$search_settings['list_status'][] = 'CCBS';
			$has_search_settings_change = true;
		}
		if ($has_search_settings_change) {
			update_option($option_name, $search_settings);
		}
	}

	private function v5_44_0() {
		$role = get_role('administrator');

		if ($role) {
			$role->add_cap(iFoundContacts::$manage_contact_tags_cap_name);
		}

		$role = get_role(iFoundTeams::$role_name);

		if ($role) {
			$role->add_cap(iFoundContacts::$manage_contact_tags_cap_name);
		}
	}

	private function v5_45_0() {
		// Must register the taxonomy before we insert a term using the taxonomy.
		iFoundContacts::new_hookless()->taxonomy();
		wp_insert_term(iFoundContacts::$PREVIOUS_RELATIONSHIP_LABEL, iFoundContacts::$the_taxonomy);
	}

	private function v5_45_1() {
		as_enqueue_async_action('ifound_v5_45_1_update', [], 'ifound');
	}

	private function v5_45_2() {
		as_enqueue_async_action('ifound_v5_45_2_update', [], 'ifound');
	}

	private function v5_45_3() {
		as_enqueue_async_action('ifound_v5_45_3_update', [], 'ifound');
	}

	private function v5_45_4() {
		as_enqueue_async_action('ifound_v5_45_4_update', [], 'ifound');
	}

	// Comment out Featured Listings JavaScript in Wordpress that was supposed to use smaller thumbnails but didn't work
	// and will break harder due to Cloudinary changes.
	private function v5_46_0() {
		$option_name = 'ifound_featured_settings';
		$featured = get_option($option_name);
		$setting_name = 'featured_slider_script';
		$regex = '#\$\(\'.ifound-featured-img\'\).each\(function\(\)\s*\{\s*this\.src\s*=\s*\'https:\/\/res\.cloudinary\.com\/ifoundagent\/image\/fetch\/c_scale,w_600\/\'\s*\+\s*this\.src;\s*\}\);#m';
		$replacement = iFoundUtil::new_hookless()->remove_leading_whitespace(<<<EOT
		// ifound-comment-id-2893745682456-start
		// 2024-07-14: This code, worst case, allows full size images to be downloaded AND THEN to be replaced by
		// thumbnails. We need a better solution. More relevant is that this domain for Cloudinary will no longer work
		// as of 2024-07-15, so we will comment out this code for now.
		// $('.ifound-featured-img').each(function() {
		//     this.src = 'https://res.cloudinary.com/ifoundagent/image/fetch/c_scale,w_600/' + this.src;
		// });
		// ifound-comment-id-2893745682456-end
		EOT);
		$featured[$setting_name] = preg_replace($regex, $replacement, $featured[$setting_name]);
		update_option($option_name, $featured);
	}

	private function v5_47_0() {
		$value = get_option(iFoundAdmin::$ifound_broker_compensations_option_name);
		if (!$value) {
			return;
		}
		$record = [
			'status' => 'checked',
			'date_gmt' => '2024-08-05T12:00:00Z',
		];
		$new_value = [
			'legal_disclaimer_history' => [$record],
			'compensations' => $value,
		];
		update_option(iFoundAdmin::$ifound_broker_compensations_option_name, $new_value);
	}

	private function v5_48_0() {
		$this->update_cmc_gravity_form_years();
	}
}
