[Home](Home)

[TOC]

# Creating an API Endpoint in WordPress

This is a relatively simple process.  All information here is gleaned from the code example on [the coderrr blog](http://www.coderrr.com/create-an-api-endpoint-in-wordpress/) and adapted to our needs.

The API created below is an extremely basic example that merely echoes a string in the URL back to the user.

First, create a basic plugin.  Next, define a class to serve as the API endpoint.  I'm creatively naming mine `API_Endpoint`.

To hook into WordPress functionality, we will be defining the following:

        public function __construct(){
            add_filter('query_vars', array($this, 'add_query_vars'), 0);
            add_action('parse_request', array($this, 'sniff_requests'), 0);
        }

We'll be defining the functions referenced here (`add_query_vars` and `sniff_requests`) separately below.  Here's the gist of what they do:

- `add_query_vars()` is a filter, not an action.  This adds the query variables we'll be using to access our API to the public WordPress query variables.

- `sniff_requests()` determines if the variables we're setting in `add_query_vars()` are present in the query string.  if so, then our plugin will handle the request, rather than WordPress itself.  This is hooked to the parsing of an HTTP request via the `parse_request` event.

And now for the code.  This API can be accessed by going to `http://mysite/?__api=1&string=echoThisBackToMe`.

        // borrowed and adapted from http://www.coderrr.com/create-an-api-endpoint-in-wordpress/

        class EchoBack_API_Endpoint{
            
            /** Hook WordPress
             * @return void
             */
            public function __construct(){
                add_filter('query_vars', array($this, 'add_query_vars'), 0);
                add_action('parse_request', array($this, 'sniff_requests'), 0);
            } 
            
            /** Add public query vars
             * @param array $vars List of current public query vars
             * @return array $vars 
             */
            public function add_query_vars($vars){
                $vars[] = '__api';
                $vars[] = 'string';
                return $vars;
            }
            
            /** Add API Endpoint
             * This is where the magic happens - brush up on your regex skillz
             * @return void
             */
            public function add_endpoint(){
                add_rewrite_rule('^api/echoback/(\w+)','index.php?__api=1&string=$matches[1]','top');
            }
         
            /** Sniff Requests
             * This is where we hijack all API requests
             *   If $_GET['__api'] is set, we kill WP and handle the request here
             * @return die if API request
             */
            public function sniff_requests(){
                global $wp;
                if(isset($wp->query_vars['__api'])){
                    $this->handle_request();
                    exit;
                }
            }
            
            /** Handle Requests
             * This is where we echo back the string
             * @return void 
             */
            protected function handle_request(){
                global $wp;
                $string = $wp->query_vars['string'];
                if($string) {
                    echo($string);
                } else {
                    echo('Please specify a string to echo back.');
                }
            }
        }
        new EchoBack_API_Endpoint();





