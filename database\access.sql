-- MySQL dump 10.13  Distrib 5.1.61, for debian-linux-gnu (x86_64)
--
-- Host: *************    Database: pfndidx_azdb
-- ------------------------------------------------------
-- Server version	5.1.53-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `access`
--

DROP TABLE IF EXISTS `access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `access` (
  `access_id` int(11) NOT NULL AUTO_INCREMENT,
  `access_account_id` varchar(255) NOT NULL,
  `access_apikey` varchar(255) NOT NULL,
  `access_domain` varchar(255) NOT NULL,
  `access_ip` varchar(255) NOT NULL,
  `access_company` varchar(255) NOT NULL,
  `access_fullname` varchar(255) NOT NULL,
  `access_address` varchar(255) NOT NULL,
  `access_address2` varchar(255) NOT NULL,
  `access_city` varchar(255) NOT NULL,
  `access_state` varchar(255) NOT NULL,
  `access_zipcode` varchar(255) NOT NULL,
  `access_phone` varchar(255) NOT NULL,
  `access_emailaddress` varchar(255) NOT NULL,
  `access_status` tinyint(2) NOT NULL DEFAULT '1',
  `access_member_id` varchar(15) NOT NULL,
  `access_office_id` varchar(15) NOT NULL,
  `access_youtube_id` varchar(12) DEFAULT NULL,
  `domain` varchar(255) NOT NULL,
  `ftp_addr` varchar(255) NOT NULL,
  `ftp_user` varchar(255) NOT NULL,
  `ftp_pass` varchar(255) NOT NULL,
  `mls` varchar(255) NOT NULL,
  PRIMARY KEY (`access_id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `access`
--

LOCK TABLES `access` WRITE;
/*!40000 ALTER TABLE `access` DISABLE KEYS */;
INSERT INTO `access` VALUES (1,'cactusmtnproperties001','HwRFt782S6eGtt4','','','Cactus Mountain Properties','Debbie Johnson','ffff','','','Arizona','','(*************','<EMAIL>',1,'218923','MICHAELG',NULL,'rockpointreo.com','','rockpoin','V_PUv=(CgWTC','trendmls');
INSERT INTO `access` VALUES (2,'philicity001','KJHSDFUH434G2','philicity.com','*************','Phili-City Properties','George McFly','4848 E Frontwheel Dr','Suite 10','Some town','Pennsylvania','25555','(555) 555-5555','<EMAIL>',1,'218923','MICHAELG',NULL,'example.com','ftp.example.com','aaaa','pass','trendmls');
INSERT INTO `access` VALUES (3,'somecompany001','DiPKkM5tZo1LSfv','','','Some Other Company','Samantha Conley','5555 West Westly Road','Suite 10','dddd','Arizona','85555','(555) 555-5555','<EMAIL>',0,'','',NULL,'superlame.com','ftp.rockpointreo.com','bozo','bozo','armls');
INSERT INTO `access` VALUES (4,'','','','','This rules','','','','','Alabama','','','',1,'','',NULL,'ddd','sss','','sss','armls');
/*!40000 ALTER TABLE `access` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `access_meta`
--

DROP TABLE IF EXISTS `access_meta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `access_meta` (
  `meta_id` int(11) NOT NULL AUTO_INCREMENT,
  `access_id` int(11) NOT NULL,
  `meta_prop_url` varchar(255) NOT NULL,
  `meta_prop_title` varchar(255) NOT NULL,
  `meta_prop_h1` varchar(255) NOT NULL,
  `meta_prop_keywords` text NOT NULL,
  `meta_prop_description` text NOT NULL,
  `meta_result_title` varchar(255) NOT NULL,
  `meta_result_h1` varchar(255) NOT NULL,
  `meta_result_keywords` text NOT NULL,
  `meta_result_description` text NOT NULL,
  `meta_result_prop_h2` varchar(255) NOT NULL,
  `meta_result_prop_content` text NOT NULL,
  `meta_cat_title` varchar(255) NOT NULL,
  `meta_cat_h1` varchar(255) NOT NULL,
  `meta_cat_keywords` text NOT NULL,
  `meta_cat_description` text NOT NULL,
  `meta_geoapi` varchar(255) NOT NULL,
  `meta_links_seo` mediumtext,
  PRIMARY KEY (`meta_id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `access_meta`
--

LOCK TABLES `access_meta` WRITE;
/*!40000 ALTER TABLE `access_meta` DISABLE KEYS */;
INSERT INTO `access_meta` VALUES (1,1,'{StreetNumber}-{StreetDirPrefix}-{StreetName}-{UnitNumber}-{StreetSuffix}-{City}-{State}-{PostalCode}','{StreetNumber} {StreetDirPrefix} {StreetName} {City} {PostalCode} | {InteriorFeatures}','{StreetNumber} {StreetName} {City}, {Beds} Bed, {Bathrooms} Bath Home for Sale','{SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath,  in {City},  {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','{BrokerRemarks}. Wonderful {SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath, in {City}, near {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','Search results for {Beds} bed {Baths} bath {DwellingType} in {City} ','Homes for Sale : ','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}\r\n','{DwellingType} {Beds} bed, {Baths} bath, {SquareFeet} sqft Home For Sale','{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber}<br>\r\n{City}, {State} {PostalCode}<br>\r\n<br>\r\n<strong>{ListPrice}</strong>','{category} in {City}','{category} in {City}','{category} in {City}','{category} in {City}','','Single Story Houses For Sale in {Subdivision}\r\nTwo Story Homes for Sale in {HighSchool}\r\nHomes Near {HighSchool} High School For Sale in {Subdivision}\r\nHomes Near {ElementarySchool} Elementary School For Sale in {Subdivision}\r\nHouses in District {SchoolDistrict} For Sale in {Subdivision}\r\nHomes Near {CrossStreet} For Sale in {Subdivision}');
INSERT INTO `access_meta` VALUES (2,3,'{StreetNumber}-{StreetDirPrefix}-{StreetName}-{StreetSuffix}-{UnitNumber}-{City}-{State}-{PostalCode}','{StreetNumber} {StreetDirPrefix} {StreetName} {City} {PostalCode} | {InteriorFeatures}','{StreetNumber} {StreetName} {City} Home for Sale {Beds} Bedroom, {Bathrooms} Bathroooms','{SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath,  in {City},  {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','{BrokerRemarks}. Wonderful {SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath, in {City}, near {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','Search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City} ','Search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City} ','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}','{Beds} bed, {Bathrooms} bath, {DwellingType}','{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber}<br>\r\n{City}, {State} {PostalCode}<br>\r\n<br>\r\n{ListPrice}','{category} in {City}','{category} in {City}','{category} in {City}','{category} in {City}','','');
INSERT INTO `access_meta` VALUES (3,2,'{StreetNumber}-{StreetDirPrefix}-{StreetName}-{UnitNumber}-{StreetSuffix}-{City}-{State}-{PostalCode}','{StreetNumber} {StreetDirPrefix} {StreetName} {City} {PostalCode} | {InteriorFeatures}','{StreetNumber} {StreetName} {City}, {Beds} Bed, {Bathrooms} Bath Home for Sale','{SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath,  in {City},  {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','{BrokerRemarks}. Wonderful {SquareFeet} sqft, {Beds} Bed, {Bathrooms} Bath, in {City}, near {ElementarySchool} Elementary School, {MiddleSchool} Middle School, {HighSchool} High School','Search results for {Beds} bed {Baths} bath {DwellingType} in {City} ','Homes for Sale : ','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}','{DwellingType} search results for {Beds} bed {Bathrooms} bath {DwellingType} in {City}, {State}\r\n','{DwellingType} {Beds} bed, {Baths} bath, {SquareFeet} sqft Home For Sale','{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber}<br>\r\n{City}, {State} {PostalCode}<br>\r\n<br>\r\n<strong>{ListPrice}</strong>','','','','','',NULL);
/*!40000 ALTER TABLE `access_meta` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2012-04-19 15:59:31

CREATE TABLE `access_log` (
  `LogID` INTEGER  NOT NULL AUTO_INCREMENT,
  `access_id` INTEGER  NOT NULL,
  `ClientIP` VARCHAR(15) NOT NULL,
  `BrowserIP` VARCHAR(15) NOT NULL,
  `QueryType` ENUM('FeatList','Search','Details','Options','RSS')  NOT NULL,
  `PropID` INTEGER  NOT NULL,
  `SearchString` VARCHAR(255)  NOT NULL,
  `UserAgent` VARCHAR(255)  NOT NULL,
  `Referer` VARCHAR(255)  NOT NULL,
  `QueryTime` DOUBLE  NOT NULL,
  `NumQueries` INTEGER  NOT NULL,
  `TotalTime` DOUBLE  NOT NULL,
  `Created` TIMESTAMP  NOT NULL,
  PRIMARY KEY (`LogID`),
  INDEX `IX_Customer`(`access_id`),
  INDEX `IX_ClientIP`(`ClientIP`),
  INDEX `IX_BrowserIP`(`BrowserIP`),
  INDEX `IX_Type`(`QueryType`),
  INDEX `IX_Created`(`Created`)
)
ENGINE = MyISAM;

ALTER TABLE `access_log`
 ADD COLUMN `Features` VARCHAR(255)  NOT NULL,
 ADD COLUMN `Words` VARCHAR(255)  NOT NULL,
 ADD COLUMN `Hostname` VARCHAR(255)  NOT NULL,
 ADD COLUMN `Category` VARCHAR(255)  NOT NULL,
 ADD COLUMN `City` VARCHAR(255)  NOT NULL,
 ADD COLUMN `RequestURL` VARCHAR(255)  NOT NULL,
 ADD COLUMN `Page` SMALLINT UNSIGNED NOT NULL,
 ADD COLUMN `SortOrder` VARCHAR(255)  NOT NULL,
 ADD COLUMN `Price` VARCHAR(20)  NOT NULL,
 ADD COLUMN `Sqft` VARCHAR(20)  NOT NULL,
 ADD COLUMN `Bedroom` VARCHAR(20)  NOT NULL,
 ADD COLUMN `Bathroom` VARCHAR(20)  NOT NULL,
 ADD COLUMN `ZipCode` VARCHAR(20)  NOT NULL;

ALTER TABLE `access_log` DROP COLUMN `SearchString`;

ALTER TABLE `access_log`
 ADD COLUMN `PropType` VARCHAR(20)  NOT NULL;

ALTER TABLE `access_log` ADD COLUMN `NumResults` INTEGER UNSIGNED NOT NULL;
