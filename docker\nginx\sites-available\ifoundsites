server {
    listen 80;
    server_name ifoundsites.test *.ifoundsites.test;
    # This is how I test https for the sake of Sign In With Google, which must use https and may not use .test as a TLD.
    server_name ifoundsites.xyz *.ifoundsites.xyz;
    server_name multiagent.test;

    root /var/www/ifoundsites;
    index index.php;

    access_log /var/log/nginx/ifoundsites.log;
    error_log /var/log/nginx/ifoundsites-error.log;

    # Ignore requests for favicon.ico during dev. It's an extra request for each Wordpress page load,
    # which clutters debugging sequences.
    location /favicon.ico {
        return 444;
    }

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        set $upstream ifoundsites:9000;
        fastcgi_pass $upstream;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
        # Make PHP think we're using HTTPS if an upstream reverse-proxy set X-Forwarded-Proto.
        # See: https://stackoverflow.com/a/46984243/135101
        if ($http_x_forwarded_proto = 'https') {
            set $fe_https 'on';
        }
        fastcgi_param HTTPS $fe_https;

        # I was having a problem when using the User Switching plugin in dev. Sometimes it'd have a 502 error, and
        # in the logs I'd see "194 upstream sent too big header while reading response header from upstream".
        # The following helped.
        # https://stackoverflow.com/a/23845727/135101
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
    }

    location /wp-content/ {
        alias /var/www/ifoundsites/wp-content/;
    }

    include /etc/nginx/global/idx.conf;

}
