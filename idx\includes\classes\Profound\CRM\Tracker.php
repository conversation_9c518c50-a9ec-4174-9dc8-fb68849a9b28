<?

namespace Profound\CRM;

use Profound\Config;
use Profound\Utils;

class Tracker {
	public function viewPage($queryInfo, $propArray, $activityType) {
		if (isset($queryInfo['meta']) && Config::getCrmApiBaseUrl()) {
			$meta = json_decode($queryInfo['meta'], true);
			$networkId = $meta['network_id'];
			$username = $meta['username'];
			if (!$networkId || !$username) {
				\D::elog("Tracker::viewPage: Meta was available, but incomplete: " . json_encode($queryInfo));
				return;
			}

			$referer = $_SERVER['HTTP_X_IDX_CLIENT_REFERER'];
			if (isset($propArray['uri']))
				$url = "" . Utils::baseUrl($referer) . '/' . $propArray['uri'];
			else
				$url = $referer;

			$client = new \GuzzleHttp\Client();
			$promise = $client->postAsync(Config::getCrmApiBaseUrl() . '/crm/events/go', [
				// 'proxy' => 'tcp://127.0.0.1:8888',
				'json' => [
					'metadata' => [
						'event_name' => 'Pfmls::NewActivity',
						'date' => gmdate('Y-m-d\\TG:i:s\\Z', \time())
					],
					'data' => $this->dataFor($activityType, $queryInfo, $propArray, $networkId, $username, $url),
					'apikey' => $queryInfo['apikey']
				]
			]);
			$promise->then(function($response) {
				// \D::elog('Tracker::viewPage: Success. Response body: ' . $response->getBody());
			}, function($ex) {
				\D::elog("Tracker::viewPage: Exception: " . $ex->getMessage());
			});
			// \D::elog("Tracker::viewPage: About to post to IDX2");
			$promise->wait();
		}
	}

	public function viewListing($queryInfo, $propArray) {
		return $this->viewPage($queryInfo, $propArray, "property_viewed");
	}

	public function dataFor($activityType, $queryInfo, $propArray, $networkId, $username, $url) {

		$data = [
			'contactid' => $username,
			'siteid' => $networkId,
			'sitedomain' => Utils::baseUrl($url),
			'type' => $activityType,
			'created' => gmdate('Y-m-d\\TG:i:s\\Z', \time()),
		];

		switch($activityType) {
		case 'property_viewed':
			$data['description'] = "User viewed listing at " . $url;
			// We use the STFU operator here (@) to not issue warnings for missing array keys.
			$data['property'] = @[
				'mls' => $propArray['ListingID'],
				'url' => $url,
				'street_address' => implode(' ', array_filter([$propArray['StreetNumber'], $propArray['StreetDirPrefix'], $propArray['StreetName'], $propArray['StreetSuffix'], $propArray['UnitNumber']])),
				'city' => $propArray['City'],
				'state' => $propArray['State'],
				'zip' => $propArray['PostalCode'],
				'beds' => $propArray['Beds'],
				'full_baths' => (integer)$propArray['Bathrooms'],
				'half_baths' => (float)$propArray['Bathrooms'] - floor((float)$propArray['Bathrooms']),
				'type' => $propArray['PropType'],
				'sqft' => $propArray['SquareFeet'],
				'listing_price' => $propArray['ListPrice'],
				'listing_status' => $propArray['ListStatus'],
				'latitude' => $propArray['Latitude'],
				'longitude' => $propArray['Longitude'],
				// I'm going to leave out images at first, because they quickly fill our logs, and this is more of a proof of concept at the moment.
				// 'images' => array_map(function($item) { return $item['normal_url']; }, $propArray['images']),
				'year_built' => $propArray['YearBuilt'],
				'list_date' => $propArray['ListDate'],
			];
			break;
		default:
			$data['description'] = "User viewed page " . $url;
			break;
		}

		return $data;
	}
}
