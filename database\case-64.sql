ALTER TABLE access 
 ADD COLUMN domain varchar(255)  NOT NULL AFTER access_youtube_id,
 ADD COLUMN ftp_addr varchar(255)  NOT NULL AFTER domain,
 ADD COLUMN ftp_user varchar(255)  NOT NULL AFTER ftp_addr,
 ADD COLUMN ftp_pass varchar(255)  NOT NULL AFTER ftp_user;

ALTER TABLE property_a_fields ADD COLUMN mls VARCHAR(255)  NOT NULL AFTER field_id;

UPDATE property_a_fields SET mls='armls' WHERE mls='';

ALTER TABLE property_a_fields ADD INDEX MLS(mls);

ALTER TABLE property_lookup_opts 
 ADD COLUMN mls VARCHAR(255)  NOT NULL AFTER lookup_id, 
 ADD INDEX MLS(mls);

ALTER TABLE access ADD COLUMN mls VARCHAR(255)  NOT NULL AFTER ftp_pass;

UPDATE access SET mls='armls' WHERE mls='';

CREATE TABLE field_mapping (
  map_id INTEGER  NOT NULL AUTO_INCREMENT,
  MapName VARCHAR(255)  NOT NULL,
  mls VARCHAR(255)  NOT NULL,
  SystemName VARCHAR(255)  NOT NULL,
  PRIMARY KEY (map_id),
  INDEX MLS(mls)
)
ENGINE = MyISAM;

ALTER TABLE action_log ADD COLUMN mls VARCHAR(255)  NOT NULL AFTER action_status;
