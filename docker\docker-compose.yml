services:
    nginx:
        image: nginx:1.13.0-alpine
        restart: unless-stopped
        expose:
            - 80
            - 443
        ports:
            - "${HTTP_PORT:-80}:80"
            - "${HTTPS_PORT:-443}:443"
            # For the value of exposing port 39229, see docker/nginx/sites-available/node-debug in this project.
            - "39229:39229"
        volumes:
            # nginx config
            - ./nginx/logs:/var/log/nginx
            - ./nginx/nginx.conf:/etc/nginx/nginx.conf
            - ./nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
            - ./nginx/global:/etc/nginx/global
            - ./nginx/sites-available:/etc/nginx/sites-available
            - ./nginx/ssl/:/etc/nginx/ssl/
            - ./nginx/snippets/:/etc/nginx/snippets/
            # profoundwp
            - pfwpdir:/var/www/profoundwp
            - ../plugin/:/var/www/profoundwp/wp-content/plugins/profoundmls/
            - ../themes/denali/:/var/www/profoundwp/wp-content/themes/denali/
            # ifoundadmin
            - ../volumes/ifoundadmin:/var/www/ifoundadmin
            - ../ifound-admin/:/var/www/ifoundadmin/wp-content/plugins/ifound-admin/
            - ../themes/altitude-pro/:/var/www/ifoundadmin/wp-content/themes/altitude-pro/
            - ../../blogdog/blogdog_api_github/:/var/www/ifoundadmin/wp-content/plugins/blogdog_api/
            - ./php/wordpress/base/downloads/genesis/:/var/www/ifoundadmin/wp-content/themes/genesis
            # ifoundsites
            - ../volumes/ifoundsites:/var/www/ifoundsites
            - ../ifound/:/var/www/ifoundsites/wp-content/plugins/ifound/
            - ../ifound-slider/:/var/www/ifoundsites/wp-content/plugins/ifound-slider/
            - ../ifound-ez-admin/:/var/www/ifoundsites/wp-content/plugins/ifound-ez-admin/
            - ../ifound-themes/ifound-mont_blanc/:/var/www/ifoundsites/wp-content/themes/ifound-mont_blanc/
            - ../ifound-themes/ifound-denali/:/var/www/ifoundsites/wp-content/themes/ifound-denali/
            - ../ifound-themes/foothills/:/var/www/ifoundsites/wp-content/themes/foothills/
            - ../ifound-themes/wilson/:/var/www/ifoundsites/wp-content/themes/wilson/
            - ../../blogdog/blogdog_wp/:/var/www/ifoundsites/wp-content/plugins/blogdog/
            # This is the Yoast SEO plugin
            - ./php/wordpress/base/downloads/wordpress-seo/:/www/wp-content/plugins/wordpress-seo/
            - ./php/wordpress/base/downloads/genesis/:/var/www/ifoundsites/wp-content/themes/genesis
            # This is the iHome Finder plugin I was testing
            # - ./php/wordpress/base/downloads/optima-express/:/var/www/ifoundsites/wp-content/plugins/optima-express/
        environment:
            PHP_ENV: development
        networks:
            default:
                aliases:
                    - idx.test
                    - idx-server.test
                    - ifaidx.test
                    - ifoundadmin.test
                    - build.ifoundadmin.test
                    - featured-images.ifoundadmin.test
                    - sliders.ifoundadmin.test
                    - mls-associations.ifoundadmin.test
                    - reblogdog.ifoundadmin.test
                    - ifoundsites.test
                    - multiagenttest.ifoundsites.test
                    - realtracs.ifoundsites.test
                    - recolorado-mlg.ifoundsites.test
                    - brightmls.ifoundsites.test
                    - prescottdemo.ifoundsites.test
                    - paaraz_mlg.ifoundsites.test
                    - armlsspark.ifoundsites.test
                    - wilsontheme.ifoundsites.test
                    - api.reblogdog.test
                    - ifoundadmin.xyz
                    - build.ifoundadmin.xyz
                    - featured-images.ifoundadmin.xyz
                    - sliders.ifoundadmin.xyz
                    - mls-associations.ifoundadmin.xyz
                    - ifoundsites.xyz
                    - paaraz_mlg.ifoundsites.xyz
                    - armlsspark.ifoundsites.xyz
                    - cabor.ifoundsites.xyz
                    - wmar.ifoundsites.xyz
    idx_db:
        build: idx_db
        hostname: idx-db
        restart: unless-stopped
        volumes:
            - ./idx_db/config.cnf:/etc/mysql/conf.d/config.cnf
            - ./idx_db/downloads:/tmp/downloads
            - ../database/dumps:/tmp/db-dumps
            - ./idx_db/Makefile:/Makefile
            # TODO: Switch to custom MySQL image, with make included
            - ./idx_db/unpackdb.sh:/unpackdb.sh
            - ./idx_db/import-props.sh:/import-props.sh
            - ../volumes/idx_db:/var/lib/mysql
        environment:
            MYSQL_ROOT_PASSWORD: root
        ports:
            - 33066:3306
    solr:
        build: ./solr
        hostname: solr
        volumes:
            - ../solr/configs:/var/lib/solr/configs
            - ../solr/templates:/var/lib/solr/templates
            - ../solr/lib:/var/lib/solr/lib
            - ../solr/tools:/var/lib/solr/tools
            - ./solr/Makefile:/var/lib/solr/Makefile
            - ../volumes/solr:/var/lib/solr/dist/server/solr
            - ../solr/configs/solr.xml:/var/lib/solr/dist/server/solr/solr.xml
            - ../server/:/var/lib/server/
            - ../server/node_modules/:/var/lib/solr/node_modules/
            - ./solr/resources/log4j.properties:/var/lib/solr/dist/server/resources/log4j.properties
            - ./solr/etc/jetty.xml:/var/lib/solr/dist/server/etc/jetty.xml
        command: "/bin/solr start -f -a -Xmx2048m"
        # command: tail -f /dev/null
    idx:
        image: ifoundagent:idx-bullseye
        restart: unless-stopped
        expose:
            - 80
        depends_on:
            - idx_db
        volumes:
            - ../idx:/www
            - ../history-tracker/data/:/www/history_tracker_data/
            - ../tests:/tests
            - ../volumes/idx_vendor:/www/vendor
            - ../volumes/composer_cache:/var/www/.composer/cache/
            - ./php/idx/config/solr.ini:/etc/php/8.1/cli/conf.d/20-solr.ini
            - ./php/idx/config/solr.ini:/etc/php/8.1/fpm/conf.d/20-solr.ini
            - ./php/idx/config/fpm/pool.d/www.conf:/etc/php/8.1/fpm/pool.d/www.conf
            - ./php/idx/config/php.ini:/etc/php/8.1/cli/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/php.ini:/etc/php/8.1/fpm/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/cli/conf.d/xdebug.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/fpm/conf.d/xdebug.ini
            - ./user/.bashrc:/root/.bashrc
            - ./user/.bash_aliases:/root/.bash_aliases
            - ./user/.bash_aliases:/home/<USER>/.bash_aliases
        env_file:
            - ../.env
        environment:
            MYSQL_ROOT_PASSWORD: root
            PHP_ENV: development
            PHP_IDE_CONFIG: "serverName=idx"
        # command: tail -f /dev/null
    railsadmin:
        build: ./rails_admin
        restart: unless-stopped
        #image: 094784567917.dkr.ecr.us-west-1.amazonaws.com/rails-admin
        depends_on:
            - idx_db
            # For use with old plugin, which I've never used in dev.
            # - redis
        volumes:
            - ../idx2:/www
            - ../volumes/rubygems:/var/lib/gems
            - ../volumes/binstubs:/usr/local/bin
        environment:
            RAILS_ENV: development
            REDIS_URL: redis://redis/0
        # entrypoint: "/usr/bin/tail -f /dev/null"
    newidx:
        build: ./newidx
        restart: unless-stopped
        depends_on:
            - idx_db
        expose:
            - 8155
        ports:
            - 59229:9229
        volumes:
            - ../server:/www
        user: node
        # command: tail -f /dev/null
    wpdb:
        image: mysql:5.7.44
        restart: unless-stopped
        volumes:
            - ../volumes/wpdb:/var/lib/mysql
        environment:
            MYSQL_ROOT_PASSWORD: wordpress
            # Easy way to create a user, for now.  We'll re-use this user for multiple WordPress sites.
            MYSQL_USER: wordpress
            MYSQL_PASSWORD: wordpress
        ports:
            - 33067:3306

    # For use with old plugin, which I've never used in dev.
#    redis:
#        image: redis:5.0.4-alpine
#        hostname: redis
#        volumes:
#            - ../volumes/redis/:/var/redis/

    # -----------------------------------------------------------------------------------
    # WordPress sites

#    I'm disabling this one for now because I haven't ever used it.
#    profoundwp:
#        build:
#            context: ./php/wordpress
#            dockerfile: ./profoundwp/Dockerfile
#        hostname: profoundwp
#        expose:
#            - 9000
#        depends_on:
#            - wpdb
#        volumes:
#            - pfwpdir:/www
#            - ../plugin/:/www/wp-content/plugins/profoundmls/
#            - ../themes/denali/:/www/wp-content/themes/denali/
#            - ./php/wordpress/Makefile:/www/Makefile
#            - ./php/wordpress/profoundwp/install_files/dev_database_dumps:/tmp/dbdumps/
#            #- ./php/wordpress/profoundwp/install_files/use-shortcodes-in-sidebar-widgets /www/wp-content/plugins/use-shortcodes-in-sidebar-widgets/
#            #- ./php/wordpress/profoundwp/install_files/widget-shortcode /www/wp-content/plugins/widget-shortcode/
#            #- ./php/wordpress/profoundwp/install_files/login-with-ajax /www/wp-content/plugins/login-with-ajax/
#            #- ./php/wordpress/profoundwp/install_files/jquery-ui-1.12.1 /www/
#            #- ./php/wordpress/profoundwp/install_files/uploads /www/wp-content/uploads/

    ifoundadmin:
        image: ifoundagent:idx-bullseye
        restart: unless-stopped
        expose:
            - 9000
        depends_on:
            - wpdb
        volumes:
            # You're expected to copy the version of Wordpress you want to (as) ../volumes/ifoundadmin/.
            - ../volumes/ifoundadmin:/www
            - ../ifound-admin/:/www/wp-content/plugins/ifound-admin/
            - ./php/wordpress/base/downloads/genesis/:/www/wp-content/themes/genesis
            - ../themes/altitude-pro/:/www/wp-content/themes/altitude-pro/
            # The ifound-signup plugin is on ifoundagent.com, not ifoundadmin.com, but it shouldn't really make a difference for dev
            - ../ifound-signup/:/www/wp-content/plugins/ifound-signup/
            - ../../blogdog/blogdog_api_github/:/www/wp-content/plugins/blogdog_api/
            - ../phrase_chooser/:/www/wp-content/plugins/phrase_chooser/
            - ./php/idx/config/fpm/pool.d/www.conf:/etc/php/8.1/fpm/pool.d/www.conf
            - ./php/idx/config/php.ini:/etc/php/8.1/cli/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/php.ini:/etc/php/8.1/fpm/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/cli/conf.d/xdebug.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/fpm/conf.d/xdebug.ini
            - ./user/.bashrc:/root/.bashrc
            - ./user/.bash_aliases:/root/.bash_aliases
            - ./user/.bash_aliases:/home/<USER>/.bash_aliases
        env_file:
            - ../.env
        environment:
            PHP_IDE_CONFIG: "serverName=ifoundadmin"

    ifoundsites:
        image: ifoundagent:idx-bullseye
        restart: unless-stopped
        expose:
            - 9000
        depends_on:
            - wpdb
        volumes:
            # You're expected to copy the version of Wordpress you want to (as) ../volumes/ifoundsites/.
            - ../volumes/ifoundsites:/www
            - ../ifound/:/www/wp-content/plugins/ifound/
            - ../ifound/vendor/woocommerce/:/www/wp-content/plugins/ifound/vendor/woocommerce/
            - ../ifound-builder/:/www/wp-content/plugins/ifound-builder/
            - ../ifound-slider/:/www/wp-content/plugins/ifound-slider/
            - ../ifound-ez-admin/:/www/wp-content/plugins/ifound-ez-admin/
            - ../../blogdog/blogdog_wp/:/www/wp-content/plugins/blogdog/
            - ../phrase_chooser/:/www/wp-content/plugins/phrase_chooser/
            - ./php/wordpress/base/downloads/wordpress-mu-domain-mapping/:/www/wp-content/plugins/wordpress-mu-domain-mapping/
            # This is the Yoast SEO plugin
            - ./php/wordpress/base/downloads/wordpress-seo/:/www/wp-content/plugins/wordpress-seo/
            - ./php/wordpress/base/downloads/genesis/:/www/wp-content/themes/genesis
            - ../ifound-themes/ifound-cielo/:/www/wp-content/themes/ifound-cielo/
            - ../ifound-themes/ifound-mont_blanc/:/www/wp-content/themes/ifound-mont_blanc/
            - ../ifound-themes/ifound-denali/:/www/wp-content/themes/ifound-denali/
            - ../ifound-themes/foothills/:/www/wp-content/themes/foothills/
            - ../ifound-themes/wilson/:/www/wp-content/themes/wilson/
            - ./php/idx/config/fpm/pool.d/www.conf:/etc/php/8.1/fpm/pool.d/www.conf
            - ./php/idx/config/php.ini:/etc/php/8.1/cli/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/php.ini:/etc/php/8.1/fpm/conf.d/php-ifoundagent-idx.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/cli/conf.d/xdebug.ini
            - ./php/idx/config/xdebug.ini:/etc/php/8.1/fpm/conf.d/xdebug.ini
            - ./user/.bashrc:/root/.bashrc
            - ./user/.bash_aliases:/root/.bash_aliases
            - ./user/.bash_aliases:/home/<USER>/.bash_aliases
        env_file:
            - ../.env
        environment:
            PHP_IDE_CONFIG: "serverName=ifoundsites"
        # command: tail -f /dev/null

volumes:
    pfwpdir:
