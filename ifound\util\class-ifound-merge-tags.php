<?

defined( 'ABSPATH' ) or die( 'You do not have access!' );

// The original intent of this class is to supplement the merge tags that are in class-ifound-email.php, which are
// coupled to email functionality, with use of member variables that make things hard to trace. We can try to use the
// functionality here going forward when possible. Some functionality might be duplicated. I don't really want to
// refactor that class due to the difficulty of testing and surfacing problems in production.
class iFoundMergeTags {
	use NewHooklessTrait;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			// No hooks yet
		}
	}

	private function get_replacements($data = []) {
		$map = [];
		if (isset($data['contact_id'])) {
			$contact_id = $data['contact_id'];
			$map = wp_parse_args($map, [
				'ContactFirstName'   => get_post_meta($contact_id, 'fname', true),
				'ContactLastName'    => get_post_meta($contact_id, 'lname', true),
				'SpouseFirstName'    => get_post_meta($contact_id, 'fname_spouse', true),
				'SpouseLastName'     => get_post_meta($contact_id, 'lname_spouse', true),
				'ContactEmail'       => get_post_meta($contact_id, 'email', true),
				'ContactHomePhone'   => get_post_meta($contact_id, 'hphone', true),
				'ContactWorkPhone'   => get_post_meta($contact_id, 'wphone', true),
				'ContactMobilePhone' => get_post_meta($contact_id, 'mphone', true),
			]);

			$agent = iFoundCrm::new_hookless()->agent(['contact_id' => $contact_id]);
			$map = wp_parse_args($map, [
				'AgentName'        => $agent->agent_name,
				'BrokerName'       => $agent->broker_name,
				'StreetAddress'    => $agent->street_address,
				'City'             => $agent->city,
				'State'            => $agent->state,
				'Zip'              => $agent->zip,
				'OfficePhone'      => $agent->office_phone,
				'MobilePhone'      => $agent->mobile_phone,
				'Fax'              => $agent->fax,
				'Email'            => $agent->email,
			]);
		}
		$map['Year'] = date('Y');
		return $map;
	}

	// Originally written just to do merge tags for campaign titles, so it will not replace all merge tags from all
	// contexts, such as {ContactLink}, {HeadShot}, {BrokerLogo}, {SignatureGraphic}, {AlertLink}, {Unsubscribe},
	// {Listings}. But refactor as necessary.
	public function replace_content($content, $data = []) {
		$map = $this->get_replacements($data);
		foreach ($map as $key => $value) {
			$content = str_ireplace( '{' . $key . '}', $value, $content );
		}
		if (isset($data['contact_id'])) {
			$content = $this->replace_contact_street_name($content, $data['contact_id']);
		}
		return html_entity_decode($content, ENT_QUOTES);
	}

	// Reminder: the ContactStreetName merge tag is considered special. We replace it only when necessary because it
	// takes time to grab it with a web request, and that web request costs money.
	public function replace_contact_street_name($content, $contact_id) {
		$content = preg_replace_callback('/\{ContactStreetName\}/i', function() use ($contact_id) {
			return iFoundJointContact::new_hookless()->get_street_name($contact_id);
		}, $content);
		return $content;
	}
}
