<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * Activity Class
 *
 * @since 2.5.1
 */

class iFoundActivity extends iFoundCrm {

	private $post_type = 'contacts';

	public static $activity_report_hook_name = 'process_activity_report';
	public static $email_tracking_meta_key = 'ifound_tracking_pixel';
	public static $website_visit_follow_up_timestamp_option_base = 'ifound_website_visit_follow_up_sent_at';
    private static $send_follow_up_visit_email_hook_name = 'ifound_send_follow_up_visit_email';

	/**
	 * init iFOUND_activity class.
	 *
	 * @since 2.5.1
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 2.5.1
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_init', array($this, 'save_activity_schedule'), 99);

			add_action('add_meta_boxes_contacts', array($this, 'contact_meta_boxes'));
			add_action('add_meta_boxes_private_contact', array($this, 'contact_meta_boxes'));
			add_action('add_meta_boxes_save_this', array($this, 'save_this_meta_boxes'));
			add_action('add_meta_boxes_drip_campaign', array($this, 'drip_campaign_meta_boxes'));
			add_action('wp_dashboard_setup', array($this, 'ifa_referral_dashboard_widgets'));
			add_action('wp_dashboard_setup', array($this, 'disable_default_dashboard_widgets'));
			add_action('wp_dashboard_setup', array($this, 'ifa_support_dashboard_widgets'));

			add_action('wp_footer', array($this, 'track'), 20);
			add_action('ifound_tracking', array($this, 'track'), 10, 2);
			add_action('init', [$this, 'track_activity_tracking_link']);
			// Reminder: we want to do this before redirects. We want to take out the tpx query param so the user
			// doesn't see it in their browser URL bar.
			add_action('init', [$this, 'track_email_clicks']);
			// Same for these SMS clicks.
			add_action('init', [$this, 'track_sms_clicks']);

			add_action('ifound_activity_log', array($this, 'activity_log'), 10, 4);
			add_action('ifound_next_activity_report', array($this, 'next_activity_report'));
			add_action(static::$activity_report_hook_name, array($this, 'process_activity_report'), 10, 1);
			add_action('delete_user', array($this, 'delete_user_hook'), 10);
			add_action('remove_user_from_blog', array($this, 'remove_user_from_blog_hook'), 10);

			add_action('ifound_contact_visit', array($this, 'contact_visit'), 10, 2);

			add_action('ifound_results_views_metabox', array($this, 'results_views_metabox'), 10, 1);
			add_action('ifound_detail_views_metabox', array($this, 'detail_views_metabox'), 10, 1);
			add_action('ifound_views_metabox', array($this, 'views_metabox'), 10, 1);

			add_action('ifound_activity_log_page', array($this, 'activity_log_page'));

			add_filter('ifound_website_activity_types', [$this, 'website_activity_types']);
			add_filter('ifound_website_activity_types_string', [$this, 'website_activity_types_string']);

			add_action('ifound_install_db_limits_cron', [$this, 'install_db_limits_cron']);
			add_action('ifound_enforce_db_limits', [$this, 'enforce_db_limits']);

			add_action('rest_api_init', [$this, 'register_routes']);

            add_action(static::$send_follow_up_visit_email_hook_name, [$this, 'send_follow_up_visit_email'], 10, 1);
		}
	}

        public function ifa_referral_dashboard_widgets() {
                wp_add_dashboard_widget(
                'ifa_referral_widget',
                'iFoundAgent Referral Program',
                array( $this, 'ifa_referral_dashboard_widget_function')
        	);
        }

        public function ifa_support_dashboard_widgets() {
                add_meta_box( 'ifa_support_widget', 'iFoundAgent Support & Training', array( $this, 'ifa_support_dashboard_widget_function'), 'dashboard', 'side', 'high' );
        }

        /*
         *   Disable Default Dashboard Widgets
         */
        public function disable_default_dashboard_widgets() {
                        global $wp_meta_boxes;

                        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_right_now']);
                        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_comments']);
                        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_incoming_links']);
                        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_plugins']);
                        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_primary']);
                        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_secondary']);
                        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_quick_press']);
                        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_recent_drafts']);
                        unset($wp_meta_boxes['dashboard']['normal']['high']['dashboard_browser_nag']);
                        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_activity']);
        }

	/**
	 *	Meta Boxes
	 *
	 *	Add meta boxes to contacts.
	 *
	 *	@since 2.5.1
	 *
	 *	@link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */

	public function contact_meta_boxes() {

		global $typenow;

		add_meta_box(
			'activity_log_meta',
			__( '<i class="fal fa-table" aria-hidden="true"></i> Activity Log', 'ifound' ),
			array( $this, 'activity_log_metabox'),
			$typenow,
			'advanced',
            'default'
		);

		add_meta_box(
			'results_views_meta',
			__( '<i class="fal fa-search" aria-hidden="true"></i> Search Views', 'ifound' ),
			array( $this, 'results_views_metabox'),
			$typenow,
			'advanced',
            'low'
		);

		add_meta_box(
			'detail_views_meta',
			__( '<i class="fal fa-street-view" aria-hidden="true"></i> Property Views', 'ifound' ),
			array( $this, 'detail_views_metabox'),
			$typenow,
			'advanced',
            'low'
		);

		add_meta_box(
			'views_meta',
			__( '<i class="fal fa-globe" aria-hidden="true"></i> All Contact Activity', 'ifound' ),
			array( $this, 'views_metabox'),
			$typenow,
			'advanced',
            'low'
		);

	}

	/**
	 * Save This Meta Boxes
	 *
	 * Add meta boxes to contacts.
	 *
	 * @since 2.5.1
	 * @since 2.5.3 Move to iFOUND_activity class
	 *
	 * @link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */

	public function save_this_meta_boxes() {

		add_meta_box(
			'activity_log_meta',
			__( '<i class="fal fa-table" aria-hidden="true"></i> Activity Log', 'ifound' ),
			array( $this, 'activity_log_metabox'),
			'save_this',
			'advanced',
            'default'
		);

	}

	public function drip_campaign_meta_boxes() {

		add_meta_box(
			'drip_campaign_activity_log_meta',
			__( '<i class="fal fa-table" aria-hidden="true"></i> Activity Log', 'ifound' ),
			array( $this, 'activity_log_metabox'),
			'drip_campaign',
			'advanced',
			'default'
		);

	}

	/**
	 * Activity Log Metabox
	 *
	 * @since 1.2.34
	 * @since 2.5.2 Move to iFOUND_activity class.
	 */

	public function activity_log_metabox( $post_id = false ) {
		global $wpdb;

		$post_id = is_int( $post_id ) ? $post_id : get_the_ID();
		$sql = <<<EOT
			SELECT meta_id, meta_value
			FROM {$wpdb->postmeta}
			WHERE post_id = %d
			AND meta_key = %s
			ORDER BY meta_id DESC
		EOT;
		$logs = $wpdb->get_results($wpdb->prepare($sql, $post_id, 'activity_log'), ARRAY_A);
		$ife_records = [];
		$sms_records = [];
		// Don't call out to DB if we don't have to
		if ($logs) {
			$ife_records = iFoundEmailTrackingPixel::new_hookless()->find_by_post_id($post_id);
			$sms_records = iFoundSms::new_hookless()->find_by_post_id($post_id);
		}
		$group_by_meta_id = function($index, $value) {
			return [$value['meta_id'], $value];
		};
		$ife_records_by_meta_id = $this->util()->array_map_modify_both($group_by_meta_id, $ife_records);
		$sms_records_by_meta_id = $this->util()->array_map_modify_both($group_by_meta_id, $sms_records);
		?>
		<table class="form-table activity_log_meta views_meta">
			<tbody>
				<tr>
					<th><label><? _e( 'Date', 'ifound' ) ; ?></label></th>
					<th><label><? _e( 'Action', 'ifound' ) ; ?></label></th>
					<th><label><? _e( 'Title or Note', 'ifound' ) ; ?></label></th>
				</tr>
				<?
				if (!count($logs)) {
					?>
					<tr>
						<td colspan="3">(None)</td>
					</tr>
					<?
				}
				foreach( $logs as $log ) {
					list($date, $action, $msg) = explode(';', $log['meta_value']);
					$full_message = $msg;
					$matches = null;
					$url = null;
					$ife_record = $ife_records_by_meta_id[$log['meta_id']];
					$sms_record = $sms_records_by_meta_id[$log['meta_id']];
					if (preg_match('#^https?://#', $msg)) {
						$url = $msg;
					} else if (preg_match('/ifound_email_sent_meta_id:(\d+)/', $log['meta_value'], $matches)) {
						$url = admin_url('admin.php?page=activity_log');
						$url = add_query_arg('post_id', $post_id, $url);
						$url = add_query_arg('ifound_email_sent_meta_id', $matches[1], $url);
					}
					?>
					<tr>
						<th scope="row"><label for="activity-log_data"><? _e( apply_filters( 'pretty_date', $date ), 'ifound' ) ; ?></label></th>
						<td id="activity-log_data"><? _e( $action, 'ifound' ) ; ?></td>
						<td id="activity-log_data">
							<?
							if ($ife_record) {
								$icon = 'fas fa-envelope';
								$alt = 'Unread email';
								$title = 'Unread email';
								if ($ife_record['read_count'] > 0) {
									$icon = 'far fa-envelope-open';
									$alt = 'Read email';
									$title = 'Read: ' . apply_filters('pretty_date', $ife_record['date_first_read']);
									if ($ife_record['read_count'] > 1) {
										$title = 'First read: ' . apply_filters('pretty_date', $ife_record['date_first_read']);
										$title .= "\n" . 'Most recent: ' . apply_filters('pretty_date', $ife_record['date_most_recent_read']);
										$title .= "\n" . 'Read count: ' . $ife_record['read_count'];
									}
								}
								echo "<i class=\"$icon\" alt=\"$alt\" title=\"$title\"></i>";
							} else if ($sms_record) {
								$icon = 'fas fa-comment';
								$alt = 'Unclicked text message';
								$title = 'Unclicked text message';
								if ($sms_record['clicked_count'] > 0) {
									$icon = 'far fa-comment';
									$alt = 'Clicked text message';
									$count = $sms_record['clicked_count'];
									$date_str = apply_filters('pretty_date', $sms_record['date_first_clicked_gmt'],
										true);
									$title = "Text message, clicked {$count} time(s), first clicked: {$date_str}";
								} else if ($sms_record['error_message']) {
									$alt = 'Error sending text message';
									$title = 'Error sending text message';
									$full_message .= ' - Error: ' . $sms_record['error_message'];
								}
								echo "<i class=\"$icon\" alt=\"$alt\" title=\"$title\"></i>";
							}
							?>
							<? if ($url): ?>
								<a href="<?=$url?>"><?=$msg?></a>
							<? else: ?>
								<?=$full_message?>
							<? endif ?>
						</td>
					</tr>
					<?
				}
				?>
			</tbody>
		</table>
		<?
	}

	/**
	 * Remove Params
	 *
	 * Remove all URL params.
	 *
	 * @since 2.5.55 Remove params from URL.
	 *
	 * @param  string $url A URL.
	 * @return string $url A URL with params removed.
	 */

	public function remove_params( $url ) {
		return strpos( $url, '?' ) ? strstr( $url, '?', true ) : $url;
	}

	/**
	 * Views Metabox
	 *
	 * @since 1.0.0
	 * @since 2.5.2 Move to iFOUND_activity class.
	 * @since 2.5.55 Remove params from URL
	 */

	public function views_metabox( $contact_id = false ) {

		$contact_id = is_int( $contact_id ) ? $contact_id : get_the_ID();

		if( $views = get_post_meta( $contact_id, 'page_view' ) ) {

			rsort( $views ); ?>

			<table class="form-table views_meta">

				<tbody><?

					foreach( $views as $view ) {

						list( $date, $url ) = explode( ';', $view );

						$url = $this->remove_params( $url ); ?>

						<tr>

							<th scope="row"><label for="views-url"><? echo apply_filters( 'pretty_date', $date ); ?></label></th>
							<td><a href="<? echo esc_url( $url ); ?>" target="_blank" id="views-url"><? _e( $url, 'ifound' ); ?></a></td>

						</tr><?

					} ?>

				</tbody>

			</table><?

		}

	}

	/**
	 * Results Views Metabox
	 *
	 * @since 1.0.0
	 * @since 2.5.2 Move to iFOUND_activity class.
	 * @since 2.5.55 Remove params from URL
	 */

	public function results_views_metabox( $contact_id = false ) {

		$contact_id = is_int( $contact_id ) ? $contact_id : get_the_ID();

		if( $views = get_post_meta( $contact_id, 'results_view' ) ) {

			krsort( $views ); ?>

			<table class="form-table views_meta">

				<tbody><?

					foreach( $views as $view ) {

						list( $date, $url ) = explode( ';', $view );

						$url = $this->remove_params( $url ); ?>

						<tr>
							<th scope="row"><label for="results-views-url"><? echo apply_filters( 'pretty_date', $date ); ?></label></th>
							<td><a href="<? echo $url; ?>" target="_blank" id="results-views-url"><? echo $url; ?></a></td>
						</tr><?

					} ?>

				</tbody>

			</table><?

		}

	}

	/**
	 * Detail Views Metabox
	 *
	 * @since 1.0.0
	 * @since 2.5.2 Move to iFOUND_activity class.
	 */

	public function detail_views_metabox( $contact_id = false ) {

		$contact_id = is_int( $contact_id ) ? $contact_id : get_the_ID();

		if ( $views = get_post_meta( $contact_id, 'detail_view' ) ) {

			krsort( $views ); ?>

			<table class="form-table views_meta">

				<tbody><?

					foreach( $views as $view ) {

						list( $date, $address, $mls_id ) = explode( ';', $view );

						$href = apply_filters( 'ifound_get_detail_url', $address, $mls_id );?>

						<tr>
							<th scope="row"><label for="property-views-url"><? echo apply_filters( 'pretty_date', $date ); ?></label></th>
							<td><a href="<? echo $href; ?>" target="_blank" id="property-views-url"><? echo $address; ?></a></td>
						</tr><?

					} ?>

				</tbody>

			</table><?

		}

	}

	/**
	 * Activity Log
	 *
	 * Log specific contact activities.
	 *
	 * @since 1.2.34
	 * @since 1.3.3 Restructure activity_log meta to allow for sorting by meta_value.
	 * @since 2.5.2 Move to iFOUND_activity class.
	 * @since 3.2.0 Convert post_id to int.
	 * @since 3.5.1 Check get_post_status to insure post exists.
	 *
	 * @param int    $post_id The ID of the post. Post types can vary.
	 * @param string $action  The name of the action taken.
	 * @param string $msg 	  The message for the action performed by the contact.
	 */

	public function activity_log( $post_id, $action, $msg, $options = [] ) {

		$post_id = intval( $post_id );

		if( is_int( $post_id ) && $post_id > 0 && get_post_status( $post_id ) ) {

			$current_time = current_time('mysql');
			$data = array(
				$current_time,
				$action,
				$msg,
				// This is a little awkward. It outputs the empty string after the Member: part if the user is an admin.
				// I'd change it except... is it valuable anyway? And it's been in production for a while as is, so
				// perhaps best to leave it as it is for consistency's sake.
				'Member:' . $this->crm_id(),
			);
			if (isset($options['ifound_email_sent_meta_id'])) {
			    $data[] = 'ifound_email_sent_meta_id:' . $options['ifound_email_sent_meta_id'];
			}
			if (isset($options[static::$email_tracking_meta_key])) {
				$data[] = static::$email_tracking_meta_key . ':' . $options[static::$email_tracking_meta_key];
			}

			$meta_id = add_post_meta( $post_id, 'activity_log', join( ';', $data ) );
			$post = get_post($post_id);
			if (iFoundJointContact::new_hookless()->is_post_type_a_contact($post->post_type)) {
				update_post_meta($post_id, 'latest_activity_log', $current_time);
			}

			return [
				'meta_id'      => $meta_id,
				'current_time' => $current_time,
			];
		}

		return null;
	}

	/**
	 * Process Activity Report
	 *
	 * The cron callback for activity report.
	 *
	 * @since 2.5.1
	 * @since 2.5.5 Add expire_logs().
	 */

	public function process_activity_report($user_id) {

		$crm = $this->crm_from_user_id($user_id);

		if( isset( $crm->time_of_day ) && $crm->time_of_day != 'none' ) {

			do_action( 'ifound_activity_report_email', $user_id );

		}

	}

	/**
	 * Save Activity Schedule
	 *
	 * Saves new cron time on form submit.
	 *
	 * @since 2.5.1
	 */

	public function save_activity_schedule() {
		$this->update_cron_for_hook('time_of_day', static::$activity_report_hook_name, true);
	}

	private function clean_up_crons($user_id) {
		wp_clear_scheduled_hook(static::$activity_report_hook_name, [$user_id]);
	}

	public function delete_user_hook($user_id) {
		$this->clean_up_crons($user_id);
	}

	public function remove_user_from_blog_hook($user_id) {
		$this->clean_up_crons($user_id);
	}

	/**
	 * Next Activity Report
	 *
	 * Display for the next activity report.
	 *
	 * @since 2.5.1
	 */

	public function next_activity_report() {
		$user_id = iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id();
		$crm = $this->crm_from_user_id($user_id);
		if( isset( $crm->time_of_day ) && $crm->time_of_day != 'none' ) {
			$next_update = wp_next_scheduled( static::$activity_report_hook_name, [$user_id] );
            // Reminder: the time actually used is offset by a random amount of time within one hour, so that we don't
            // have all crons from all sites running at the same time.
            ?>
                Next Update: <?= apply_filters( 'pretty_date', $next_update, true ) ?>
            <?php
		} else {
			?>
			<div>
				<em>The actual time of day will be sometime within the hour chosen.</em>
			</div>
			<?php
		}
	}

	// Custom iFound Dashboard widget functions
	public function ifa_referral_dashboard_widget_function() {
                echo "<p><img src='https://ifoundagent.com/wp-content/uploads/sites/1/2018/12/ifa_referral3.jpg' /></p>
                        <p style='font-size: 16px;'><strong>We love referrals!</strong></p>
                        <p style='font-size: 16px;'><a href='https://ifoundagent.com/refer-a-friend/' target='_blank'>Refer a friend and <strong><span style='font-size: 18px; font-weight: 700;'>get a FREE month</span></strong></a></p>";
        }

        public function ifa_support_dashboard_widget_function() {
                echo "<p style='font-size: 16px;'><img src='https://ifoundagent.com/wp-content/uploads/sites/1/2018/12/support3.jpg'></p>
                        <p style='font-size: 16px;'><a href='https://ifoundagent.com/knowledgebase/' target='_blank'>View our knowledgebase</a> for in depth videos and articles.</p>
                        <p style='font-size: 16px;'>Submit a support request to <strong><EMAIL></strong></p>
                        <p style='font-size: 16px;'>Attend one of our bi-weekly free webinars. <a href='https://ifoundagent.com/knowledgebase/training-event-calendar-grid/' target='_blank'><strong>Sign up here</strong></a></p>";
        }

	/**
	 * Track
	 *
	 * Track contact page views by type.
	 * If there is a cookie, but no contact. There is no reason to store a view.
	 *
	 * @since 1.0.0
	 * @since 1.7.1 Disallow views while using WP customize.
	 * @since 2.5.2 Move to iFOUND_activity class.
	 * @since 2.6.9 Add hook ifound_external_crm_page_view.
	 * @since 3.5.1 Check get_post_status to insure post exists.
	 *
	 * @param object $options If we rendered a template, the main results object from API. Otherwise, various values.
	 * @param string $type    The name of the page type. Defaults to bool false if no type is provided.
	 */

	public function track( $options = array() , $type = false ) {
		global $IFOUND_PAGE_LOAD_ID;

		if( $this->should_track() ) {

			/** Check if $contact_id set. */
			$contact_id = $this->get_contact_id();

			if( $contact_id && get_post_status( $contact_id ) ) {

				$type = $type ?: 'page_view';
				$contact_visit_options = [];
				$current_time = current_time('mysql');

				if( defined( 'DOING_DETAILS' ) && ! empty( $options ) ) {
					$results = $options;

					$address = apply_filters('ifound_address', $results);

					$data = array(
						$current_time,
						$address,
						$results->ListingID
					);
					// Reminder: it makes no sense to put the address as a URL. The reason this is "ok" is because when
					// we later call do_action( 'ifound_contact_visit', ...), it checks a transient and won't actually
					// do anything, because this will have been the second call to it. I won't make changes here now
					// because I already think it's weird enough how it was coded in the first place (the fact
					// that we first track a page view and then later a detail view).
					$contact_visit_options['url'] = $address;
				} else if (is_array($options) && $options['is_activity_tracking_link']) {
					$data = [
						$current_time,
						$options['link'],
					];
					$contactName = get_the_title( $contact_id );
					$contact_visit_options['is_activity_tracking_link'] = true;
					$contact_visit_options['action'] = $contactName . ' clicked a campaign link';
					$contact_visit_options['title'] = $options['link'];
					$contact_visit_options['url'] = $options['link'];
				} else {

					$url = site_url( $_SERVER['REQUEST_URI'] );
					$data = array(
						$current_time,
						$url,
					);
					$contact_visit_options['url'] = $url;

				}

				/** Record this page view */
				add_post_meta( $contact_id, $type, join( ';', $data ), false );
				if (strpos($type, '_view') !== false) {
					update_post_meta($contact_id, 'latest_visit', $current_time);
				}

				do_action( 'ifound_contact_visit', $contact_id, $contact_visit_options );
				$this->maybe_send_follow_up_visit_email($contact_id);
				do_action( 'ifound_external_crm_page_view', $type );
				// Reminder: This next line updates the post modified date. I'm not sure if there's another reason to do
				// so, but one upshot is we know that the contact's web activity needs to be synced to Wise Agent.
				wp_update_post(array('ID' => $contact_id));
			}

		}

	}

	/**
	 * Should Track
	 *
	 * Check to see if we should track this view.
	 *
	 * @since 2.5.55
	 *
	 * @return bool $should_track True if we should track. False if not.
	 */

	public function should_track() {
		global $IFOUND_PAGE_LOAD_ID;

		// Trim off the query string.
		$uri = strtok($_SERVER['REQUEST_URI'], '?');

		if(
			is_admin()
			||
			isset( $_GET['customize_theme'] )
			||
			isset( $_GET['preview'] )
			||
			isset( $_GET['p'] )
			||
			// .map is mainly for when we do quick prod testing on getprequalified.com and I rsync my local stuff, which
			// includes .css.map, .js.map, .ts.d.map, etc files meant for dev. But they still show up as website
			// activity for Dale, which we don't want.
			preg_match( '/(\.jpg|\.jpeg|\.png|\.gif|\.css|\.js|\.map)$/i', $uri )
		)  {
			return false;
		}

		$contact_id = $_GET['contact_id'];
		$transient_name = 'ifound_last_tracked_page_load_id:contact_id=' . $contact_id;
		$is_a_duplicate_request = function() use ($contact_id, $transient_name) {
			global $IFOUND_PAGE_LOAD_ID;

			// So far I've only seen duplicates that seem to come from emails, i.e. there is a ?contact_id=X in the URL.
			if ($contact_id) {
		        $transient_val = get_transient($transient_name);
		        $transient_val_int = intval($transient_val);
				if (!$transient_val_int) {
					return false;
				}
				// We must be careful. We have two hooks that end up tracking. The wp_footer action will record, e.g., a
				// page_view event, and then later the ifound_tracking action will record, e.g., a detail_view event,
				// and we want that to happen. That's not a duplicate, it's still the same web request.
				if ($transient_val_int === $IFOUND_PAGE_LOAD_ID) {
					return false;
				}
				return true;
			}
			return false;
		};
		if ($is_a_duplicate_request()) {
			// Reminder to self: For a while I was tracking some data here and stuffing it in the postmeta table (only
			// for certain sites that I was tracking specifically). The meta_keys were 'ifound_dupe_visit',
			// 'user_agent', 'referer', and 'ifound_page_load_id'. I'm not yet ready to remove that data. But I should
			// someday to clean it up.
			return false;
		}

		set_transient($transient_name, $IFOUND_PAGE_LOAD_ID, 60);

		return true;
	}

	/**
	 * Contact Visit
	 *
	 * Send contact visit email and save 1 hour transient.
	 *
	 * @since 2.5.30
	 * @since 2.5.36 Check if contact_visit_email is enabled.
	 * @since 2.5.40 Add activity log.
	 *
	 * @param int $contact_id The id of the current contact.
	 */

	public function contact_visit( $contact_id, $options = [] ) {
		$options = wp_parse_args($options, [
			'action'                    => 'Website Visit',
			'title'                     => get_the_title($contact_id) . ' visited your website.',
			'is_activity_tracking_link' => false,
		]);

		$crm = $this->crm();

		if( isset( $crm->contact_visit_email ) && $crm->contact_visit_email == 'enabled' ) {

			$transient = 'ifound_contact_visit_' . $contact_id;

			if(! get_transient( $transient ) ) {

				$contact_visit_email_options = [];
				if ($options['is_activity_tracking_link']) {
					$contact_visit_email_options['subject'] = '{ContactFirstName} {ContactLastName} clicked a campaign link';
					$contact_visit_email_options['content'] = '<p>{ContactFirstName} {ContactLastName} clicked a campaign link</p>';
				} else if ($options['url']) {
					$contact_visit_email_options['content'] = '<p>{ContactFirstName} {ContactLastName} has visited your website</p>';
				}
				$contact_visit_email_options['content'] .= '<p>The page visited was <a href="' . esc_url($options['url']) . '">' . $options['url'] . '</a></p>';
				do_action( 'ifound_contact_visit_email', $contact_id, $contact_visit_email_options );
				$this->send_contact_visit_sms($contact_id);

				do_action( 'ifound_activity_log', $contact_id, $options['action'], $options['title'] );

				set_transient( $transient, 'hold_contact_visit_email', 1 * HOUR_IN_SECONDS );

			}

		}

	}

	private function maybe_send_follow_up_visit_email($contact_id) {
		$crm_id = $this->crm_id(['contact_id' => $contact_id]);
		$crm = $this->crm($crm_id);
		$setting_key = iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_ENABLED_SETTING_KEY;
		$enabled = isset($crm->{$setting_key}) && $crm->{$setting_key} == 'enabled';
		$sent_timestamp = get_option($this->make_option_name_for_contact($contact_id));
        $has_been_sent = !!$sent_timestamp;
		if ($enabled && !$has_been_sent) {
			$start_time_as_interval = new DateInterval('PT8H');
			$end_time_as_interval = new DateInterval('PT20H');
            $send_later_interval = new DateInterval('PT3H');
			$current_datetime = iFoundUtil::current_datetime();
            $start_datetime = $current_datetime->modify('today')->add($start_time_as_interval);
			$end_datetime = $current_datetime->modify('today')->add($end_time_as_interval);
			$is_between_visiting_hours = $current_datetime > $start_datetime && $current_datetime < $end_datetime;
			$send_later_datetime = $current_datetime->add($send_later_interval);
            if (!$is_between_visiting_hours) {
	            $send_later_datetime = $current_datetime->modify('tomorrow')->add($start_time_as_interval);
            }
            $args = [$contact_id];
			$is_already_scheduled = !!wp_next_scheduled(static::$send_follow_up_visit_email_hook_name, $args);
			if (!$is_already_scheduled) {
				wp_schedule_single_event($send_later_datetime->getTimestamp(), static::$send_follow_up_visit_email_hook_name,
					$args);
			}
		}
	}

    private function make_option_name_for_contact($contact_id) {
	    $crm_id = $this->crm_id(['contact_id' => $contact_id]);
	    $option_name = static::$website_visit_follow_up_timestamp_option_base . $crm_id . '_' . $contact_id;
        return $option_name;
    }

    public function send_follow_up_visit_email($contact_id) {
	    do_action('ifound_email', $contact_id, iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE, null, [
		    'post_id_type' => 'contact']);
        update_option($this->make_option_name_for_contact($contact_id), time());
    }

	private function send_contact_visit_sms($contact_id) {
		$crm_id = $this->crm_id(['contact_id' => $contact_id]);
		$crm = $this->crm($crm_id);
		if ($crm->sms_opt_in === 'enabled' && $crm->sms_number) {
			$content = iFoundEmail::new_hookless()->get_contact_visit_sms_content($contact_id);
			$iFoundSms = iFoundSms::new_hookless();
			$post_owner_id = get_post($contact_id)->post_author;
			$response = $iFoundSms->send($crm->sms_number, $content, iFoundSms::$PURPOSE_AGENT_VISIT_ALERT, [
				'user_id' => $post_owner_id,
				'post_id' => $contact_id,
			]);
			$iFoundSms->write_to_activity_log($contact_id, 'Website Visit',
				'Notified agent via SMS', $response);
		}
	}

	public function activity_log_page() {
		// Initially this page was built just to show sent emails. I used $_GET['post_id'] instead of e.g. contact_id with the
		// idea that we might expand the page's capabilities at some point.

		$ifound_email_sent_meta_id = $_GET['ifound_email_sent_meta_id'];
		$data = get_metadata_by_mid('post', $_GET['ifound_email_sent_meta_id'])->meta_value;
		?>

        <style>
            .activity-log-page td.label {
                vertical-align: top;
                font-weight: bold;
            }
            .email_body {
                background: white;
            }
        </style>

        <div class="ifound_wrap activity-log-page">
            <h1>Activity Log</h1>
            <table class="form-table">
                <tbody>
                <tr>
                    <td class="label">Date</td>
                    <td><?=apply_filters( 'pretty_date', $data['date']->format(DateTime::ATOM) )?></td>
                </tr>
                <tr>
                    <td class="label">To</td>
                    <td><?=$data['to']?></td>
                </tr>
                <tr>
                    <td class="label">Subject</td>
                    <td><?=$data['subject']?></td>
                </tr>
                <tr>
                    <td class="label">Body</td>
                    <td class="email_body"><?=$data['body']?></td>
                </tr>
                </tbody>
            </table>
        </div>
		<?php
	}

	public function track_activity_tracking_link() {
		$request_uri = $_SERVER['REQUEST_URI'];
		if (strpos($request_uri, '/ifoundcampaign') !== false) {
			$actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
			$parsed_url = parse_url($actual_link);
			parse_str($parsed_url['query'], $query_string_vals);
			$contact_id = $query_string_vals['c'];
			if (!$contact_id) {
				return;
			}
			if (!defined('iFOUND_CONTACT_ID')) {
				define('iFOUND_CONTACT_ID', $contact_id);
			}
			$forward_url = $query_string_vals['f'];
			if (!$forward_url) {
				return;
			}
			$is_custom = $query_string_vals['d'] === '1';
			do_action('ifound_tracking', [
				'is_activity_tracking_link' => true,
				'link' => $forward_url,
				'is_custom' => $is_custom,
			]);

			wp_redirect($forward_url);
			exit;
		}
	}

	public function website_activity_types($types = []) {
		$types[] = 'advanced_view';
		$types[] = 'detail_view';
		$types[] = 'page_view';
		$types[] = 'polygons_view';
		$types[] = 'results_view';

		return $types;
	}

	// Build a string meant to issue to SQL for an IN clause.
	public function website_activity_types_string($types_string = '') {
		$types = $this->website_activity_types([]);
		// Wrap all the terms with single quotes.
		$types = array_map(function($x) { return "'" . $x . "'"; }, $types);
		// Join all terms with commas between them and surround the whole thing with parentheses.
		$types_string = '(' . implode(', ', $types) . ')';
		return $types_string;
	}

	public function install_db_limits_cron() {
		$cron_time = iFoundUtil::get_cron_time( '6:00:00' );

		if ( ! wp_next_scheduled( 'ifound_enforce_db_limits' ) ) {
			wp_schedule_event( $cron_time, 'weekly', 'ifound_enforce_db_limits' );
		}
	}

	// Let's not allow too many activity_log records. Theoretically other postmeta records could also clog the DB, but
	// the activity_log ones are the only one that we've seen cause a problem so far.
	public function enforce_db_limits() {
	    global $wpdb;
	    $sql = "select count(*) as count from $wpdb->postmeta";
	    $results = $wpdb->get_results($sql, ARRAY_A);
	    $count = intval($results[0]['count']);
	    // This is a guess of how many rows might be allowed before becoming a problem with the amount of memory
		// allowed. It's based on one particular site (meaning one particular set of activity_log values), on one
		// particular Wordpress host, so it's reasonable to assume it might need adjusting over time.
		$max_allowed = 1000 * 1000;
		$num_to_delete = $count - $max_allowed;
		if ($num_to_delete > 0) {
			$sql = "delete from $wpdb->postmeta where meta_key = 'activity_log' order by meta_id asc limit $num_to_delete";
	        $wpdb->query($sql);
		}
	}

	public function register_routes() {
	    register_rest_route(
			'ifound',
		    '/tpxl/(?P<id>[0-9a-f]{32}).png',
			[
				'methods' => WP_REST_Server::READABLE,
				'callback' => [$this, 'process_tracking_pixel'],
			]
	    );
	}

	public function process_tracking_pixel($request) {
		$name = __dir__ . '/images/tracking_pixel.png';
		$fp = fopen($name, 'rb');
		header("Content-Type: image/png");
		header("Content-Length: " . filesize($name));
		fpassthru($fp);

		$tracking_pixel_id = $request['id'];
		iFoundEmailTrackingPixel::new_hookless()->increment_stat($tracking_pixel_id, 'read');
	}

	public function track_email_clicks() {
		$tracking_pixel_id = $_GET['tpx'] ?? null;
		if ($tracking_pixel_id) {
			iFoundEmailTrackingPixel::new_hookless()->increment_stat($tracking_pixel_id, 'clicked');

			$url = remove_query_arg('tpx');
			$url = $this->ensure_trailing_slash_if_not_file($url);
			wp_redirect($url);
			exit;
		}
	}

	public function track_sms_clicks() {
		$idx_server_id = $_GET[iFoundSms::$IDX_SERVER_SMS_MESSAGE_ID_QUERY_PARAM_NAME] ?? null;
		if ($idx_server_id) {
			iFoundSms::new_hookless()->increment_clicked_count($idx_server_id);

			$url = remove_query_arg(iFoundSms::$IDX_SERVER_SMS_MESSAGE_ID_QUERY_PARAM_NAME);
			$url = $this->ensure_trailing_slash_if_not_file($url);
			wp_redirect($url);
			exit;
		}
	}

	// Written by ChatGPT.
	// Examples:
	// echo ensure_trailing_slash_if_not_file("https://example.com/foo");
	// → https://example.com/foo/
	// echo ensure_trailing_slash_if_not_file("https://example.com/foo.php");
	// → https://example.com/foo.php
	// echo ensure_trailing_slash_if_not_file("https://example.com/foo?bar=baz");
	// → https://example.com/foo/?bar=baz
	private function ensure_trailing_slash_if_not_file($url) {
		$parts = parse_url($url);

		// If there's no path at all, set it to "/"
		if (!isset($parts['path']) || $parts['path'] === '') {
			$parts['path'] = '/';
		} else {
			// Get the last segment
			$lastSegment = basename($parts['path']);

			// If it doesn't look like a file (no dot or dot at start), ensure trailing slash
			if (strpos($lastSegment, '.') === false) {
				if (substr($parts['path'], -1) !== '/') {
					$parts['path'] .= '/';
				}
			}
		}

		// Rebuild the URL
		return (isset($parts['scheme']) ? "{$parts['scheme']}://" : '')
			. (isset($parts['user']) ? "{$parts['user']}" . (isset($parts['pass']) ? ":{$parts['pass']}" : '') . '@' : '')
			. ($parts['host'] ?? '')
			. (isset($parts['port']) ? ":{$parts['port']}" : '')
			. ($parts['path'] ?? '')
			. (isset($parts['query']) ? "?{$parts['query']}" : '')
			. (isset($parts['fragment']) ? "#{$parts['fragment']}" : '');
	}
}
