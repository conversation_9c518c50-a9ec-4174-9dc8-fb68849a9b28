<?php

namespace Profound\MLS;

class RecoloradoImageHandler extends ImageHandler {
	static $mlsname = "recolorado";

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$img_array = array();
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		$img_array = array();
		foreach ($results as $key => $result) {
			$loc = $result["Location"];

			$img_array[$key]['normal_url'] = $loc;
			$img_array[$key]['thumbnail_url'] = $loc;
			$img_array[$key]['highres_url'] = $loc;
			$img_array[$key]['description'] = $result['Content-Description'];
		}
		return $img_array;
	}

}
