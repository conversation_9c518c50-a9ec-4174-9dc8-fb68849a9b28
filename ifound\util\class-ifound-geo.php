<?

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundGeo {
	use UtilTrait;
	use NewHooklessTrait;

	private static $ifound_geo_meta_key = 'ifound_geo';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_filter( 'ifound_geocode', array( $this, 'geocode' ), 10, 1);
		}
	}

	public function geocode($address) {
		$data = $this->fetch_geocode($address);

		return $this->get_response_lat_lng($data);
	}

	// Ideally you shouldn't call this function unless needed because it costs money to use the Google Maps API.
	public function fetch_geocode($address) {
		// Trying to cut back on geocoding for CRMLS for certain routes for bots, because CRMLS (mostly) doesn't provide
		// Latitude and Longitude, so we were hitting our daily quota for geocoding from Google.
		$current_url = iFoundIdx::current_url();
		$matches_route = preg_match('/listing-details|current-market-comparison/', $current_url);
		if ($matches_route && $this->util()->is_user_agent_bot()) {
			$error_message = 'iFound: Refusing to geocode for suspected bot';
			return [
				'error_message' => $error_message,
				'results' => [],
				'status' => 'REQUEST_DENIED',
			];
		}

		$api_key = iFoundIdx::new_hookless()->api_secret_urlencoded();
		$endpoint = iFound::new_hookless()->get_config()['api_base_url'] . '/geo/geocode';
		$endpoint = add_query_arg('apikey', $api_key, $endpoint);
		$endpoint = add_query_arg('address', $address, $endpoint);
		$headers = iFoundIdx::new_hookless()->headers();
		$response = wp_remote_get($endpoint, $headers);
		if (is_wp_error($response)) {
			return join("\n", $response->get_error_messages());
		}
		$body = $response['body'];
		$data = json_decode($body, true);
		return $data;
	}

	// Returns blank if it can't determine the street.
	public function get_street_name($ifound_geo) {
		$geocode_result = $ifound_geo['response']['results'][0] ?? [];
		$item = $this->util()->array_find($geocode_result['address_components'] ?? [], function($x) {
			return ($x['types'][0] ?? null) === 'route';
		});
		// long_name example: East Rockwood Drive
		// short_name example: E Rockwood Dr
		return $item['long_name'] ?? '';
	}

	// The idea here is to geocode the contact if it hasn't been already. Ideally you shouldn't call this function
	// unless needed because it costs money to use the Google Maps API.
	public function maybe_geocode_contact($contact_id) {
		$ifound_geo = get_post_meta($contact_id, static::$ifound_geo_meta_key, true);
		$address_fields = iFoundJointContact::new_hookless()->get_address_fields($contact_id);
		$address = iFoundAddress::new_hookless()->build_address($address_fields, ['for_geocoding' => true]);
		$geo = null;
		$need_to_geocode = true;
		if ($ifound_geo) {
			$recorded_address = $ifound_geo['address'];
			if ($address === $recorded_address) {
				// Other possible statuses:
				// REQUEST_DENIED - Missing or invalid API key
				// OVER_QUERY_LIMIT - We can set quotas per minute/day
				//   See: https://console.cloud.google.com/google/maps-apis/quotas?project=ifoundagent1
				if (in_array($ifound_geo['response']['status'], ['OK', 'ZERO_RESULTS'], true)) {
					$need_to_geocode = false;
				}
			}
		}
		if ($need_to_geocode) {
			$geo = $this->fetch_geocode($address);
			$datetime = new \DateTime('now', new \DateTimeZone('UTC'));
			$ifound_geo = [
				'version'  => '1.0',
				'datetime' => current_time('mysql', true),
				// This is the address in the format returned from iFoundJointContact::build_address(), meaning no
				// address2 field, no commas. The format is important because we'll know not to geocode the next time
				// if the address exactly matches.
				'address'  => $address,
				'response' => $geo,
			];
			update_post_meta($contact_id, static::$ifound_geo_meta_key, $ifound_geo);
		}
		return $ifound_geo;
	}

	private function get_response_lat_lng($response) {
		return [
			'lat' => $response['results'][0]['geometry']['location']['lat'],
			'lng' => $response['results'][0]['geometry']['location']['lng'],
		];
	}

	// $ifound_geo is what we saved off in the post_meta for the contact.
	public function get_lat_lng($ifound_geo) {
		return $this->get_response_lat_lng($ifound_geo['response']);
	}

	// $ifound_geo is what we saved off in the post_meta for the contact.
	// We assume if we have the latitude, we have the longitude.
	public function has_lat_lng($ifound_geo) {
		return isset($ifound_geo['response']['results'][0]['geometry']['location']['lat']);
	}
}
