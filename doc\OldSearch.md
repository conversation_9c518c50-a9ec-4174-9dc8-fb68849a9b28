# IDX Property Search

Functionality

* Paging
* SEO extra data - `$seometa_array`

Query types

* exact field matches
* RegExp matches on a list of fields
* Polygon bounds
* 

Files

* includes/classes
    * PropQuery
    * Profound
        * MLS
            * {X}PropertyLimiter.php

Range Mapping

* Square Feet
* Price / Sq Ft
* Bathrooms
* Bedrooms
* Year build
* Price
* Garage


Query args

* `mls_class`
* `sort`
* `words`
* `max_results`
* `pp`
* `extended_data`
* `options`
* `apikey`
* `features`

WordPress query args

* `pfmls_listings_search_{n}`

Words/Features

* `get_words()`
* `get_features()`
* `_getSQLfragment()`

Operators for `words`: `:`, `=`, ` IN `, ` LIKE `, `>`, `<`, `>=`, `<=`

Other lists

* City

Outline:

* construct query, get results
* Ci

Algorithm

* Loop over Quick Search fields & add SQL
    * price, sqft, bedroom, bathroom, city, yrbuilt, garage
    * translate keys to ranges
* Add zip code SQL
* Decode `features` argument, loop over array, & add SQL
* Nearby radius - add SQL using haversine function
* Generic keyword search using `words`
* Price / Sq Ft
* MLS number
* Limit properties - `$this->limitProperties()`
   * Remove `AddressNoExport` and `UCB` properties
   * MLS specific stuff - map "Ahwatukee" to some zip codes
   * Limit by Polygons - generates a list of IDs inside a polygon, and adds `IN` clause
* Get field list
* Get paging: Total, parseable, URL
* Add the `LIMIT` SQL
* Sorting - add `ORDER BY` SQL
* Generate SQL meta data
* Run SQL query & get results
* Loop over results
	* Fill in missing latitude & longitude
	* Get the preferred image
	* Generate the property URL
	* Generate SEO stuff for properties
	* Encode property details as JSON
* Add extra fields to response
    * Last Updated
    * MLS class
    * Paging
    * SEO
    * Domain
* Aggregate results with extra SQL query

