-- MySQL dump 10.13  Distrib 5.6.33, for debian-linux-gnu (x86_64)
--
-- Host: dbserver    Database: pfndidx_azdb
-- ------------------------------------------------------
-- Server version	5.6.19-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `access`
--

DROP TABLE IF EXISTS `access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `access` (
  `access_id` int(11) NOT NULL AUTO_INCREMENT,
  `access_account_id` varchar(255) NOT NULL,
  `access_apikey` varchar(63) DEFAULT NULL,
  `access_member_id` varchar(11) NOT NULL DEFAULT '',
  `access_office_id` varchar(11) NOT NULL DEFAULT '',
  `access_youtube_id` varchar(50) DEFAULT NULL,
  `domain` varchar(255) NOT NULL DEFAULT '',
  `ftp_addr` varchar(255) NOT NULL DEFAULT '',
  `ftp_user` varchar(255) NOT NULL DEFAULT '',
  `ftp_pass` varchar(255) NOT NULL DEFAULT '',
  `mls` varchar(255) NOT NULL,
  `access_domain` varchar(255) NOT NULL DEFAULT '',
  `access_ip` varchar(255) NOT NULL DEFAULT '',
  `access_company` varchar(255) NOT NULL DEFAULT '',
  `access_fullname` varchar(255) NOT NULL DEFAULT '',
  `access_address` varchar(255) NOT NULL DEFAULT '',
  `access_address2` varchar(255) NOT NULL DEFAULT '',
  `access_city` varchar(255) NOT NULL DEFAULT '',
  `access_state` varchar(255) NOT NULL DEFAULT '',
  `access_zipcode` varchar(255) NOT NULL DEFAULT '',
  `access_phone` varchar(255) NOT NULL DEFAULT '',
  `access_emailaddress` varchar(255) NOT NULL DEFAULT '',
  `access_status` tinyint(2) NOT NULL DEFAULT '1',
  `hide_awc` tinyint(1) NOT NULL DEFAULT '0',
  `hide_ucb` tinyint(1) NOT NULL DEFAULT '0',
  `URLs` text NOT NULL,
  `feature_gui` tinyint(1) DEFAULT '1',
  `reseller_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `broker_office_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`access_id`),
  KEY `IX_auth` (`access_apikey`,`access_status`)
) ENGINE=InnoDB AUTO_INCREMENT=3732 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `access_meta`
--

DROP TABLE IF EXISTS `access_meta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `access_meta` (
  `meta_id` int(11) NOT NULL AUTO_INCREMENT,
  `access_id` int(11) NOT NULL,
  `meta_prop_url` varchar(255) NOT NULL DEFAULT '',
  `meta_prop_title` varchar(255) NOT NULL DEFAULT '',
  `meta_prop_h1` varchar(255) NOT NULL DEFAULT '',
  `meta_prop_keywords` text NOT NULL,
  `meta_prop_description` text NOT NULL,
  `meta_result_title` varchar(255) NOT NULL DEFAULT '',
  `meta_result_h1` varchar(255) NOT NULL DEFAULT '',
  `meta_result_keywords` text NOT NULL,
  `meta_result_description` text NOT NULL,
  `meta_result_prop_h2` varchar(255) NOT NULL DEFAULT '',
  `meta_result_prop_content` text NOT NULL,
  `meta_cat_title` varchar(255) NOT NULL DEFAULT '',
  `meta_cat_h1` varchar(255) NOT NULL DEFAULT '',
  `meta_cat_keywords` text NOT NULL,
  `meta_cat_description` text NOT NULL,
  `meta_geoapi` varchar(255) NOT NULL DEFAULT '',
  `meta_links_seo` mediumtext,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`meta_id`),
  KEY `FK_access` (`access_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3722 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `action_log`
--

DROP TABLE IF EXISTS `action_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `action_log` (
  `action_id` int(11) NOT NULL AUTO_INCREMENT,
  `action_field_id` int(11) NOT NULL DEFAULT '0',
  `action_field` varchar(255) NOT NULL,
  `action_startdate` int(11) NOT NULL DEFAULT '0',
  `action_enddate` int(11) NOT NULL DEFAULT '0',
  `action_notes` text NOT NULL,
  `action_status` tinyint(2) NOT NULL DEFAULT '0',
  `mls` varchar(255) NOT NULL,
  PRIMARY KEY (`action_id`),
  KEY `last_updated` (`action_enddate`,`action_field_id`,`mls`)
) ENGINE=InnoDB AUTO_INCREMENT=515294 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `communities`
--

DROP TABLE IF EXISTS `communities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `communities` (
  `comm_id` int(11) NOT NULL AUTO_INCREMENT,
  `comm_name` varchar(255) NOT NULL,
  `comm_status` int(2) NOT NULL DEFAULT '1',
  PRIMARY KEY (`comm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1682 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `field_mapping`
--

DROP TABLE IF EXISTS `field_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `field_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mls_class` varchar(20) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'res',
  `MapName` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `DisplayName` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `EasyName` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `Type` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'text',
  `armls` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `trendmls` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `sndmls` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `paaraz` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `sdcrca` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `glvarnv` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `tarmlsaz` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `mredil` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `naar` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `crmls` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `wmar` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `cabor` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_field_mapping_on_mls_class_and_MapName` (`mls_class`,`MapName`),
  KEY `mls_class` (`mls_class`),
  KEY `mapname` (`MapName`),
  KEY `easyname` (`EasyName`),
  KEY `armls` (`armls`),
  KEY `trendmls` (`trendmls`),
  KEY `sndmls` (`sndmls`),
  KEY `paaraz` (`paaraz`),
  KEY `sdcrca` (`sdcrca`),
  KEY `glvarnv` (`glvarnv`),
  KEY `tarmlsaz` (`tarmlsaz`),
  KEY `mredil` (`mredil`),
  KEY `naar` (`naar`),
  KEY `crmls` (`crmls`),
  KEY `index_field_mapping_on_display_name` (`DisplayName`),
  KEY `index_field_mapping_on_type` (`Type`),
  KEY `index_field_mapping_on_wmar` (`wmar`),
  KEY `index_field_mapping_on_cabor` (`cabor`)
) ENGINE=InnoDB AUTO_INCREMENT=480 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `property_a_fields`
--

DROP TABLE IF EXISTS `property_a_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `property_a_fields` (
  `field_id` int(11) NOT NULL AUTO_INCREMENT,
  `mls` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `SystemName` text COLLATE utf8_unicode_ci NOT NULL,
  `StandardName` text COLLATE utf8_unicode_ci NOT NULL,
  `LongName` text COLLATE utf8_unicode_ci NOT NULL,
  `DBName` text COLLATE utf8_unicode_ci NOT NULL,
  `ShortName` text COLLATE utf8_unicode_ci NOT NULL,
  `MaximumLength` text COLLATE utf8_unicode_ci NOT NULL,
  `DataType` text COLLATE utf8_unicode_ci NOT NULL,
  `Precision` text COLLATE utf8_unicode_ci NOT NULL,
  `Searchable` text COLLATE utf8_unicode_ci NOT NULL,
  `Interpretation` text COLLATE utf8_unicode_ci NOT NULL,
  `Alignment` text COLLATE utf8_unicode_ci NOT NULL,
  `UseSeparator` text COLLATE utf8_unicode_ci NOT NULL,
  `EditMaskID` text COLLATE utf8_unicode_ci NOT NULL,
  `LookupName` text COLLATE utf8_unicode_ci NOT NULL,
  `MaxSelect` text COLLATE utf8_unicode_ci NOT NULL,
  `Units` text COLLATE utf8_unicode_ci NOT NULL,
  `Index` text COLLATE utf8_unicode_ci NOT NULL,
  `Minimum` text COLLATE utf8_unicode_ci NOT NULL,
  `Maximum` text COLLATE utf8_unicode_ci NOT NULL,
  `Default` text COLLATE utf8_unicode_ci NOT NULL,
  `Required` text COLLATE utf8_unicode_ci NOT NULL,
  `SearchHelpID` text COLLATE utf8_unicode_ci NOT NULL,
  `Unique` text COLLATE utf8_unicode_ci NOT NULL,
  `MetadataEntryID` text COLLATE utf8_unicode_ci NOT NULL,
  `ModTimeStamp` text COLLATE utf8_unicode_ci NOT NULL,
  `ForeignKeyName` text COLLATE utf8_unicode_ci NOT NULL,
  `ForeignField` text COLLATE utf8_unicode_ci NOT NULL,
  `InKeyIndex` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`field_id`),
  KEY `MLS` (`mls`),
  KEY `systemname` (`SystemName`(50)),
  KEY `standardname` (`StandardName`(30)),
  KEY `lookupname` (`LookupName`(30))
) ENGINE=InnoDB AUTO_INCREMENT=2911 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `property_images`
--

DROP TABLE IF EXISTS `property_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `property_images` (
  `image_id` int(11) NOT NULL AUTO_INCREMENT,
  `image_enterdate` int(11) NOT NULL,
  `image_status` tinyint(2) NOT NULL DEFAULT '1',
  `Data` text COLLATE utf8_unicode_ci NOT NULL,
  `Content-Type` text COLLATE utf8_unicode_ci NOT NULL,
  `Content-ID` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `Object-ID` smallint(6) NOT NULL,
  `Location` text COLLATE utf8_unicode_ci NOT NULL,
  `Content-Description` text COLLATE utf8_unicode_ci NOT NULL,
  `Preferred` text COLLATE utf8_unicode_ci NOT NULL,
  `Success` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`image_id`),
  KEY `Content-ID` (`Content-ID`),
  KEY `IX_Image` (`Content-ID`,`Object-ID`)
) ENGINE=InnoDB AUTO_INCREMENT=113425015 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `property_lookup_opts`
--

DROP TABLE IF EXISTS `property_lookup_opts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `property_lookup_opts` (
  `lookup_id` int(11) NOT NULL AUTO_INCREMENT,
  `mls` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `LookupName` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `MetadataEntryID` text COLLATE utf8_unicode_ci NOT NULL,
  `Value` text COLLATE utf8_unicode_ci NOT NULL,
  `ShortValue` text COLLATE utf8_unicode_ci NOT NULL,
  `LongValue` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`lookup_id`),
  KEY `MLS` (`mls`),
  KEY `lookupname` (`LookupName`),
  KEY `shortvalue` (`ShortValue`(100)),
  KEY `case796` (`LookupName`,`ShortValue`(100)),
  KEY `case809` (`mls`,`LookupName`),
  KEY `LongValue` (`LongValue`(100)),
  KEY `LookupName_LongValue` (`LookupName`(100),`LongValue`(100)),
  KEY `mls_LookupName_LongValue` (`mls`(100),`LookupName`(100),`LongValue`(100)),
  KEY `mls_2` (`mls`,`LookupName`,`MetadataEntryID`(100),`Value`(100),`ShortValue`(100),`LongValue`(150))
) ENGINE=InnoDB AUTO_INCREMENT=48407726 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos`
--

DROP TABLE IF EXISTS `videos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos` (
  `access_id` int(11) NOT NULL,
  `MLS_id` int(11) NOT NULL,
  `youtube_id` varchar(255) NOT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2017-04-11 19:08:55
