<?php
/*
 * Previous format to run the
 * http://rets-next.com.local/rets.php?mls=armls&cmd=props&limit=1&images=1
 *
 * Run the following command from your terminal
 * php rets.php --mls "armls" --class "A" --cmd "props" --limit "5" --images "0"
 * php rets.php --mls "sndmls" --class "9" --cmd "props" --limit "5" --images "0"
 * Set class = A for residential property - StandardName: ResidentialProperty
 * Set class = D for Commercial property -StandardName: CommonInterest
 * Set class = F for Multiple Dwellings - StandardName: MultiFamily
 */

require_once __DIR__ . '/../custom_error_handler.php';
require_once 'RETSHelper.php';

$longopts = array(
	"mls:", // Required value
	"class:", // Required value
	"cmd:", // Required value
	"limit:", // Optional value
	"images:", // Optional value
	"id:",
	"qty:",
	"lookups:",
	"cache:",
	"batch:",
	"deletion_limit:", // Optional value
    "schema_only:", // Don't fetch properties
    "daysago:",
	// Only for use with cmd=props. If true, it will fetch listings since the most recent record. Otherwise it will
	// fetch all. Use like "--incremental true".
	"incremental:",
);

$options = getopt(null, $longopts);

# TODO: should be able to default options using getopt?
// Fill in defaults
$vars = array(
	'class' => '',
	'limit' => 5,
	'images' => 1,
	'id' => null,
	'qty' => 0,
	'lookups' => 1,
	'batch' => 50,
	'deletion_limit' => 1200,
    'daysago' => -1,
);
foreach ($vars as $name => $default) {
	$$name = isset($options[$name]) ? $options[$name] : $default;
}
// We handle bool variables specially to turn them from text to boolean.
// Only "true" will suffice as being true. (E.g. "True" with a capital T will not.)
$bool_vars = array(
	'cache'       => false,
	'schema_only' => false,
	'incremental' => false,
);
foreach ($bool_vars as $name => $default) {
	$options[$name] = (bool) (isset($options[$name]) && $options[$name] == "true" ? $options[$name] : $default);
}

# TODO: should be able to print a usage statement directly via the getopt stuff.  This below is not DRY.
// Check options
if (!isset($options['mls'])) {
	die('mls value not found./n execute script as php rets.php --mls "xxx" --cmd "xxx" --class "XX" --limit "n" --images "n"');
}

if (!isset($options['cmd'])) {
	die('cmd value not found./n execute script as php rets.php --mls "xxx" --cmd "xxx" --class "XX" --limit "n" --images "n"');
}

// echo "MLS: {$options['mls']}, class: {$options['class']}, cmd: {$options['cmd']}, limit: {$options['limit']}, images: {$options['images']}, id: {$options['id']}, qty: {$options['qty']}, lookups: {$options['lookups']}";
$echo = array();
foreach($options as $key => $value) {
	if (is_bool($value)) {
		$string_value = $value ? "true" : "false";
		$echo[] = "{$key}: {$string_value}";
	} else {
		$echo[] = "{$key}: {$value}";
	}
}
$echo_string = implode(", ", $echo);
echo "$echo_string\n";

// Create the object & run the command
$rh = new RETSHelper('./config.ini', $options['mls'], $class);

unset($options['mls']);
unset($options['class']);

$rh->runCommand($options);
