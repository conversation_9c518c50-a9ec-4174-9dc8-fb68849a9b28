jQuery(document).ready(function($) {
	function getAgentCookie(cookieName) {
		var cookies = document.cookie.split(';');
		for(var cookie of cookies) {
			if(cookie.includes(cookieName + '=')) {
				var obj = JSON.parse(decodeURIComponent(cookie).split('=')[1]);
				return { 'username' : obj.username, 'id' : obj.id };
			}
		}

		return null;
	}

	function addAgentIdToLinks() {
		var agentCookie = getAgentCookie('agent');
		if (!agentCookie) {
			return;
		}

		var hostname = window.location.hostname;
		var links = $('a');
		Array.prototype.forEach.call(links, link => {
			var linkUrl;
			try {
				linkUrl = new URL(link.href);
			} catch (e) {
				return;
			}
			if(linkUrl.hostname === hostname && !linkUrl.searchParams.get('aid')) {
				linkUrl.searchParams.set('aid', agentCookie.id);
				link.href = linkUrl.toString();
			}
		});
	}

	addAgentIdToLinks();
});
