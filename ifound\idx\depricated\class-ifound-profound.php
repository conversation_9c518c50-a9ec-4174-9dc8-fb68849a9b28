<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
* iFoundProfound class
*
* The methods in this class may not be the exact methods from the Profound plugin.
* Rather, these are methods used for convertion to the iFound plugin. They may be removed at some point. 
*
* @since 1.0.5
*/

class iFoundProfound extends iFoundIdx {
	use NewHooklessTrait;
	use UtilTrait;

	private static $profound_shortcode_meta_key = 'profound_shortcode';

	/**
	 * init iFoundProfound class.
	 *
	 * @since 1.0.5
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}
	
	public function __construct($options = []){
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'add_rewrite_rule'));
			add_action('init', array($this, 'hasprofound'));
			add_action('template_redirect', array($this, 'rewrite_catch'));
			add_shortcode('pfmls_city_list', array($this, 'pfmls_city_list'));
			add_filter('ifound_words_to_array', array($this, 'words_to_array'));
			add_filter('ifound_min_max_to_array', array($this, 'min_max_to_array'));
			add_filter('ancient_profound_sc', array($this, 'ancient_profound_sc'));

			// For the following two lines, this ensures WP doesn't mangle the content inside the shortcode.
			// See https://alex.blog/2009/11/22/wordpress-code-earlier-shortcodes/
			add_shortcode('ProFoundMLSSearchListings', '__return_false');
			add_filter('the_content', [$this, 'ProFoundMLSSearchListings_run_shortcode'], 9);
		}
	}

	// This ensures WP doesn't mangle the content inside the shortcode.
	// See https://alex.blog/2009/11/22/wordpress-code-earlier-shortcodes/
	public function ProFoundMLSSearchListings_run_shortcode($content) {
	    global $shortcode_tags;

	    // Backup current registered shortcodes and clear them all out
	    $orig_shortcode_tags = $shortcode_tags;
	    remove_all_shortcodes();

	    add_shortcode( 'ProFoundMLSSearchListings', [$this, 'profound_to_ifound'] );

	    // Do the shortcode (only the one above is registered)
	    $content = do_shortcode( $content );

	    // Put the original shortcodes back
	    $shortcode_tags = $orig_shortcode_tags;

	    return $content;
    }
   
   	public function add_rewrite_rule(){
		add_rewrite_tag( '%listing_id%', '([^&]+)' );
		add_rewrite_rule(
    		'^([^/]*)-idx-((?:\d){6,}+)((?:\?).*)?/?',
    		'index.php?listing_id=$matches[1]',
    		'top'
 	 	);
   	}

	public function rewrite_catch(){
		
		global $wp_query;

		if( array_key_exists( 'listing_id', $wp_query->query_vars ) ){
			
			$uri = untrailingslashit( $_SERVER['REQUEST_URI'] );
			
			$mls_id = substr( strrchr( $uri, '-idx-' ), 1 );
			
			$address = strstr( $uri, '-idx-', true );
			
			$url = apply_filters( 'ifound_get_detail_url', $address, $mls_id );
			
			wp_redirect( $url, 301 );
			
    		exit;
		}
	}
	
	/**
	 * Generate the HTML for the [pfmls_city_list] shortcode
	 *
	 * @since 1.0.0
	 * @deprecated 1.0.0 This is no longer in use.
	 *
	 * @return string Content to insert in place of the WP shortcode
	 */
	
	public function pfmls_city_list() {
		
		/** This entry is only in our older sites. */
		$option = get_option( 'ProFoundMLSAdminOptions' );
		
		$city_names = $option['city_list'];
		
		sort( $city_names );

		$columns = 4;
		$count = count( $city_names );
		$cities_per_col = ceil( $count / $columns );

		ob_start();
		
		?>
		
		<div class="pfmls-city-list">
			
			<?
			
			if ( $count > 0 ) {
				
				for ( $i = 0; $i < $columns; $i++ ) {
					
					$this->cityColumn( $i + 1, array_slice( $city_names, $i * $cities_per_col, $cities_per_col ) );
				
				}
				
			} else {
			
			?>
				
				<span style="color: red">Please configure cities in Wordpress Admin under <b>ProFoundMLS</b></span>
			
			<?
				
			}
		
			?>
			
			</div>
			
			<div class="clear"></div>
			
			<?

		return ob_get_clean();
		
	}

	/**
	 * Profound to iFound
	 *
	 * Convert Profound shortcodes to iFound shortcodes.
	 *
	 * @since 1.0.0
	 * @since 1.1.4 Add functionality to convert Profound extended_data or map_json to our new format.
	 */
	
	public function profound_to_ifound( $atts, $json = '' ) {

		$atts = shortcode_atts(
			array(
			 	'max_results' 			=> false,
				'items_per_page' 		=> false,
				'query' 			=> false,
				'mls_class' 			=> 'res',
				'backup_query' 			=> false,
				'show_map' 			=> false,
				'show_paging' 			=> false,
				'map_type' 			=> 'ROADMAP'
			),
			$atts
		); 
			
		$query = array(
			'max_results' 			=> $atts['max_results'] ?: false,
			'items_per_page' 		=> false,
			'words' 				=> $atts['query'] ?: false,
			'mls_class' 			=> ( $atts['mls_class'] == 'res' ) ? false : $atts['mls_class']
		);

		if( $query['words'] ) {

			$array = apply_filters( 'ifound_words_to_array', $query['words'] );

			$query = array_merge( $query, $array );

		}

		unset( $query['words'] );
		
		// Look fo polygons
		if( ! empty ( $json ) && strpos( $json, 'polygons' ) !== false ) {

			// I don't think these lines are needed anymore because earlier, when we set up the shortcode callback, we
			// did so in a manner that did it before WP had a chance to mangle our shortcode content.
			// I'm leaving them here as a reminder just in case.
			// $json = strip_tags($json);
			// $json = str_replace("&#8220;", "\"", $json);
			// $json = str_replace("&#8221;", "\"", $json);

			$obj = json_decode( $json );

			$i = 1;
			foreach( $obj->map->polygons->list as $p ) {

				$color = isset( $p->passthru_props->fillColor ) ? $p->passthru_props->fillColor : '#000';

				$paths = str_replace( ')(', '),(', $p->paths );

				$query['polygons'][$i]['paths'] = $paths;
				$query['polygons'][$i]['color'] = $color;

				$i++;

			}
			
		}

		$backup_query = array(
			'words' => $atts['backup_query'] ?: false,
		);

		if( $backup_query['words'] ) {

			$array = apply_filters( 'ifound_words_to_array', $backup_query['words'] );

			$backup_query = array_merge( $backup_query, $array );

		}

		unset( $backup_query['words'] );

		$display = array(
			'hide_map' 	 	=> $atts['show_map'] == 'false' 		? true 	: false,
			'hide_paging' 	=> $atts['show_paging'] == 'false' 		? true 	: false,
			'map_type' 		=> ( $atts['map_type'] == 'ROADMAP' ) 	? false : 'satellite'
		);

		$data = array_filter( array(
			'query' 		=> array_filter( $query ),
			'backup_query' 	=> array_filter( $backup_query ),
			'display'		=> array_filter( $display )
		));
		
		$meta_id = add_post_meta( get_the_ID(), 'save_this_shortcode', $data );

		$shortcode = '[ifound id=' . $meta_id . ']';
	
		$shortcode = $this->profound_to_ifound_replace( $shortcode, $json );

		return do_shortcode( $shortcode );

	}
	
	public function profound_to_ifound_replace( $shortcode, $json ) {
		
		global $post;
		$find = 'ProFoundMLSSearchListings';
		$replacement_content = $shortcode;

		$pattern = get_shortcode_regex();
		if ( 
			preg_match_all( '/'. $pattern .'/s', $post->post_content, $matches )
        	&& 
			array_key_exists( 2, $matches )
        	&& 
			in_array( $find, $matches[2] ) 
		) {
        	foreach( $matches[0] as $match ) {
			    	
				if( strpos( $match, $find ) !== false ) {
					// The old plugin and the new are not exactly the same in how they handle displaying maps and
					// listings. We're going to make some assumptions with this conversion process. I'm going to assume
					// an old shortcode was meant to show a polygon map, or search results, but not both.
					$has_polygons = strpos($match, '"polygons":') !== false;
					$post_id = get_the_ID();
					if ($has_polygons) {
						$profound_shortcode_show_listings_is_false = preg_match('/show_listings=[\'"]false[\'"]/i',
							$match);
						// Here's an alternative that seems to have been used on some pages as a way to not have any
						// listings. We will also take this to mean it should be a polygon map.
						$profound_shortcode_mls_id_0 = preg_match('/mls_id=0/i', $match);
						if ($profound_shortcode_show_listings_is_false === 1 || $profound_shortcode_mls_id_0 === 1) {
							// In this scenario, the original Profound shortcode was meant to output a polygon map but
							// not show and listings. Really what I should do is not generate the [ifound id=123] style
							// shortcode in the first place. But I confess I'm tired of this case, so I'm doing the easy
							// thing and not going back to ideally refactor it. So we output the [ifound_polygon_maker]
							// shortcode and delete the postmeta for the save_this_shortcode.
							$polygon_map = $this->convertMap($json);
							$replacement_content = $polygon_map;
							delete_post_meta($post_id, 'save_this_shortcode', $shortcode);
						}
					}
					$new_content = str_replace( $match, $replacement_content, $post->post_content );
					// We must update the global post content. If we don't, and their are multiple shortcodes in the
					// post's content, for all of the shortcodes we change after the first, we'll be working with stale
					// post content. This whole thing churns my stomach but we're hopefully almost done converting our
					// sites from the old plugin.
					$post->post_content = $new_content;

					$new_post = array(
						'ID'           => $post_id,
						'post_content' => $new_content
 					 );
					
					wp_update_post( $new_post );
					update_post_meta( $new_post['ID'], static::$profound_shortcode_meta_key, $match );
					/** Break in case there is more tnan 1 shortcode. */
					break;
					
				}	
				
			}
    	}

		return $replacement_content;
	}

	/**
	 * Convert old ProFoundMLS json map to the new polygon drawer tool shortcode
	 *
	 * @param string $json. Shortcode contents
	 * @return string new shortcode
	 */
	public function convertMap( $json ) {
		$obj = json_decode( $json, true );
		// These are just for live debug convenience. There's not much we can do if there's a decode error.
		// $last_error = json_last_error();
		// $last_error_msg = json_last_error_msg();

		/* Scrape data from the old JSON-like shortcode */
		$shortcode_options = array(
			'height' => $obj['map']['options']['height'],
			'center' => $obj['map']['options']['center'],
			'zoom' => ''
		);

		$polygon_list = $obj['map']['polygons']['list'];
		$polygons = [];
		foreach( $polygon_list as $polygon ) {
			$polygons[] = array(
				'polyname' => $polygon['label']['text'],
				'polylink' => $polygon['click']['url'],
				'polypath' => $polygon['paths'],
				'polycolor' => ( $polygon['passthru_props']['fillColor'] == NULL ? '#696969' : $polygon['passthru_props']['fillColor']),
				'labelcoord' => '(' . $polygon['label']['lat'] . ', ' . $polygon['label']['lon'] . ')',
				'bordercolor' => $polygon['passthru_props']['strokeColor']
			);
		}
		/* Finish scraping data */

		/* Assemble new shortcode */
		$assemble_polygons = function($polygons) {
			$str = '';
			foreach( $polygons as $polygon ) {
				$str .= 'object:';
				$str .= 'polyname::' . $polygon['polyname'] . ';';
				$str .= 'polylink::' . $polygon['polylink'] . ';';
				$str .= 'polypath::' . $polygon['polypath'] . ';';
				$str .= 'polycolor::' . $polygon['polycolor'] . ';';
				$str .= 'labelcoord::' . $polygon['labelcoord'] . ';';
				$str .= 'bordercolor::' . $polygon['bordercolor'] . ';';
			}
			return $str;
		};

		$sc = '[ifound_polygon_maker height="' . $shortcode_options['height']
			. '" zoom ="' . $shortcode_options['zoom'] . '" center="' . $shortcode_otpions['center'] . '" '
			. 'polygons="' . $assemble_polygons($polygons) . '" labels=""]';

		return $sc;
	}

	public function words_to_array( $words ) {

		$param_type = $this->param_type();

		$array = array();

		$words = html_entity_decode( $words );
		$words = apply_filters( 'ancient_profound_sc', $words );

		$items = explode( ';', $words );

		foreach( $items as $item ) {

			if ( empty( $item ) ) continue;

			$mm = '';
			    
			if ( stripos( $item, ' IN ' ) !== false ) {

			   	list( $key, $value ) = explode( ' IN ', $item );

			}
			   
			elseif ( stripos( $item, ' LIKE ' ) !== false ) {

				list( $key, $value ) = explode( ' LIKE ', $item );

			}

			elseif ( strpos( $item, '>=' ) !== false ) {

				list( $key, $value ) = explode( '>=', $item );
			 	$mm = 'min';

			}

			elseif ( strpos( $item, '<=' ) !== false ) {

				list( $key, $value ) = explode( '<=', $item );
			 	$mm = 'max';

			}

			elseif ( strpos( $item, '>' ) !== false ) {

				list( $key, $value ) = explode( '>', $item );
			 	$mm = 'min';

			}

			elseif ( strpos( $item, '<' ) !== false ) {

				list( $key, $value ) = explode( '<', $item );
			 	$mm = 'max';

			}

			elseif ( strpos( $item, '=' ) !== false ) {

				list( $key, $value ) = explode( '=', $item );
				$mm = 'min';

			}	

			elseif ( strpos( $item, ':' ) !== false ) {

				list( $key, $value ) = explode( ':', $item );
			
			}

			$key = trim( $key );
			$key = strtolower( $key );
			$key = ( $key == 'price' ) ? 'list_price' : $key;
			$key = ( $key == 'price' ) ? 'list_price' : $key;

			if( in_array( $key, $param_type['number'] ) || in_array( $key, $param_type['date'] ) ) {

				if ( ! empty( $mm ) ) {
					$array[$key][$mm] = trim( $value );
				}
					
			} elseif ( in_array( $key, $param_type['text'] ) ) {
				
				$value = explode( ',', $value );
				$value = array_map( 'trim', $value );			
				$array[$key] = $value;
					
			}

		}

		$array = array_merge( $array );
		return empty( $array ) ? array() : $array;

	}

	public function hasprofound() {

		if( isset( $_GET['hasprofound'] ) ) {

			global $wpdb;

			$results = $wpdb->get_results( 
				"
				SELECT * 
				FROM " . $wpdb->prefix . "posts 
				WHERE post_content LIKE '[ProFoundMLSSearchListings %'
				AND post_type IN ('post','page')
				AND post_status = 'publish'
				AND post_password = ''
				", 
				OBJECT 
			);

			$i = 1;

			$url = site_url();
			
			if( $results ) {
			
				foreach( $results as $result ) {

					$page = get_the_permalink( $result->ID );

					if( $i < 11 ) {
						file_get_contents( $page );
						$msg = ' - Shortcode Replaced';
					}
					
					echo $i . ' - <a href="' . $page . '?dump" target="_blank">' . $page . '</a>' . $msg . '<br/>';

					$i++;
					$msg = '';
				
				}
				
			} else {

				echo '<h3>There are no ProFoundMLSSearchListings shortcodes to convert.</h3>';

			}

			echo '<br/>';
			echo 'We only convert 10 pages in a run. Resresh this page until all the pages have been converted.<br/>';
			echo '<br/>';
			echo 'Some pages may continue to show here even after the shortcode has been replaced.<br/>';
			echo 'Use the links above to verify quailty.<br/>';
			echo 'The ?dump param has been added to the URL to show the coverted data. However, the page should still display correctly.<br/>';
			echo 'You may want to check the new shorcode by loading it in the shortcode creator and saving it again.';

			die();

		}

	}

	public function ancient_profound_sc($words) {
		// This list includes both residential and rentals (maybe land?). It was originally reversed, key to value, but
		// that doesn't make sense in PHP because it would discard duplicate keys.
		$armls_fields_array = array(
			// Note: in our field mappings, we have some fields (that should have had some kind of virtual mapping, but
			// alas we did it in the database directly) that are mapped as 'x' (or 'X'). An example of such an 'easy
			// name' field is 'days_back'. It doesn't make sense to do those here. If we did, then we'd end up turning
			// e.g. "LIST_39:phoenix" into 'city' => 'phoenidays_back'.

			'EffectiveListPrice' => 'list_price',
			'FEAT20070914185512915828000000' => 'updated_floor_year',
			'FEAT20070914185537166128000000' => 'updated_floor_partial_full',
			'FEAT20070914185619317168000000' => 'updated_wiring_partial_full',
			'FEAT20070914185637531067000000' => 'updated_plumbing_partial_full',
			'FEAT20070914185656377577000000' => 'updated_heat_cool_partial_full',
			'FEAT20070914185710087970000000' => 'updated_roof_partial_full',
			'FEAT20070914185721572556000000' => 'updated_kitchen_partial_full',
			'FEAT20070914185737610830000000' => 'updated_bath_partial_full',
			'FEAT20070914185759423413000000' => 'updated_room_adtn_partial_full',
			'FEAT20070914185820659904000000' => 'updated_pool_partial_full',
			'FEAT20070914185837037390000000' => 'updated_pool_year',
			'FEAT20070914185851819154000000' => 'updated_room_adtn_year',
			'FEAT20070914185910024437000000' => 'updated_bath_year',
			'FEAT20070914185927720111000000' => 'updated_kitchen_year',
			'FEAT20070914185941507638000000' => 'updated_roof_year',
			'FEAT20070914185955745813000000' => 'updated_heat_cool_year',
			'FEAT20070914190010164381000000' => 'updated_plumbing_year',
			'FEAT20070914190026251332000000' => 'updated_wiring_year',
			'FEAT20070914205202933704000000' => 'hoa',
			'FEAT20070914205229919954000000' => 'hoa_fee',
			'FEAT20070914205252059680000000' => 'hoa_paid_freq',
			'FEAT20070914205338315648000000' => 'hoa_name',
			'FEAT20071116191255291108000000' => 'deposits',
			'FEAT20071116191308106368000000' => 'security_deposit',
			'FEAT20071116191333214036000000' => 'credit_fee',
			'FEAT20071116191400275839000000' => 'pet_deposit',
			'FEAT20071116191435989249000000' => 'pets',
			'FEAT20071116192529063673000000' => 'hoa_name',
			'FEAT20071116193640412686000000' => 'furnished',
			'FEAT20071201025833384891000000' => 'hoa_fee',
			'FEAT20071201025902514716000000' => 'hoa_paid_freq',
			'FEAT20080207221436220503000000' => 'hoa_transfer_fee',
			'FEAT20110412222540943628000000' => 'hoa',
			'FEAT20110510163155967817000000' => 'garage_spaces',
			'FEAT20110510163434654648000000' => 'carport_spaces',
			'FEAT20110510163623017500000000' => 'slab_parking_spaces',
			'FEAT20110510163837118120000000' => 'total_covered_spaces',
			'FEAT20110510165207213719000000' => 'garage_spaces',
			'FEAT20110510165242171220000000' => 'carport_spaces',
			'FEAT20110510165305297027000000' => 'slab_parking_spaces',
			'FEAT20120127181504522660000000' => 'hoa_fee',
			'FEAT20120127220034630438000000' => 'hoa',
			'FEAT20120127220433216437000000' => 'hoa_transfer_fee',
			'FEAT20120316180703890806000000' => 'ttl_fee_equiv',
			'FEAT20121203210547070787000000' => 'solar_panel',
			'FEAT20121212151848358845000000' => 'solar_panel_output',
			'FEAT20130513161043032254000000' => 'basement',
			'FEAT20130513161604325096000000' => 'sep_den_office',
			'GF20070913202500131436000000' => 'exterior_features',
			'GF20070913202500131719000000' => 'building_style',
			'GF20070913202500131755000000' => 'other_rooms',
			'GF20070913202500131789000000' => 'kitchen_features',
			'GF20070913202500135427000000' => 'dining_area',
			'GF20070913202500135494000000' => 'spa_features',
			'GF20070913202500135598000000' => 'laundry',
			'GF20070913202500135630000000' => 'interior_features',
			'GF20070913202500135662000000' => 'technology',
			'GF20070913202500135695000000' => 'master_bath_features',
			'GF20070913202500135727000000' => 'pool_private',
			'GF20070913202500135759000000' => 'fireplace_features',
			'GF20070913202500135794000000' => 'additional_room',
			'GF20070913202500135827000000' => 'parking_features',
			'GF20070913202500135892000000' => 'architechtural_style',
			'GF20070914133911514723000000' => 'construction',
			'GF20070914134004605295000000' => 'roofing',
			'GF20070914134018021136000000' => 'cooling',
			'GF20070914134031105159000000' => 'heating',
			'GF20070914134051409501000000' => 'green_features',
			'GF20070914134107566397000000' => 'utilities',
			'GF20070914134121853504000000' => 'water',
			'GF20070914134136202282000000' => 'sewer_septic',
			'GF20070914134205261619000000' => 'fencing',
			'GF20070914134222636466000000' => 'property_features',
			'GF20070914134237403049000000' => 'horse_features',
			'GF20070914134331193935000000' => 'unit_style',
			'GF20070914134345186288000000' => 'hoa_includes',
			'GF20070914134530512049000000' => 'new_financing',
			'GF20071116202126800795000000' => 'construction',
			'GF20071116202243924929000000' => 'roofing',
			'GF20071116203454610486000000' => 'cooling',
			'GF20071116203454812219000000' => 'dining_area',
			'GF20071116203455097260000000' => 'exterior_features',
			'GF20071116203455421778000000' => 'green_features',
			'GF20071116203455499945000000' => 'lease_info',
			'GF20071116203455542805000000' => 'fencing',
			'GF20071116203455656746000000' => 'fireplace_features',
			'GF20071116203455818652000000' => 'interior_features',
			'GF20071116203456041048000000' => 'seasonal_rent',
			'GF20071116203456456266000000' => 'horse_features',
			'GF20071116203456593784000000' => 'heating',
			'GF20071116203457156956000000' => 'kitchen_features',
			'GF20071116203457283613000000' => 'laundry',
			'GF20071116203458108130000000' => 'other_rooms',
			'GF20071116203458325054000000' => 'comm_features',
			'GF20071116203458450967000000' => 'property_features',
			'GF20071116203459550472000000' => 'pool_private',
			'GF20071116203500085248000000' => 'spa_features',
			'GF20071116203500372419000000' => 'sewer_septic',
			'GF20071116203500475777000000' => 'technology',
			'GF20071116203500811243000000' => 'utilities',
			'GF20071116203500925479000000' => 'water',
			'GF20071117035349289855000000' => 'present_use',
			'GF20071117035412533196000000' => 'zoning',
			'GF20071117035428139715000000' => 'potential_use',
			'GF20071117035530068525000000' => 'land_features',
			'GF20071117035758145618000000' => 'water',
			'GF20071117035809896289000000' => 'sewer_septic',
			'GF20071117035837834509000000' => 'gas_services',
			'GF20071117035912148930000000' => 'cable_distance',
			'GF20071117035936300779000000' => 'water_distance',
			'GF20071117040409433166000000' => 'horse_features',
			'GF20071117040445786956000000' => 'fencing',
			'GF20071118030907305058000000' => 'phone_distance',
			'GF20071118031522618058000000' => 'dist_to_electric',
			'GF20071118031535726840000000' => 'gas_distance',
			'GF20071118031602694422000000' => 'sewer_distance',
			'GF20080205185309955416000000' => 'flooring',
			'GF20080205190243824076000000' => 'flooring',
			'GF20080207202713312731000000' => 'special_listing_cond',
			'GF20080207210325432062000000' => 'comm_features',
			'GF20090805174405513958000000' => 'green_energy_cert',
			'GF20091106191417931128000000' => 'access_features',
			'GF20100909235843952771000000' => 'landscaping',
			'GF20100910001037700375000000' => 'landscaping',
			'GF20121130191428826666000000' => 'master_bed_features',
			'LIST_101' => 'planned_comm_name',
			'LIST_105' => 'mls_id',
			'LIST_108' => 'elementary_school',
			'LIST_109' => 'middle_school',
			'LIST_110' => 'high_school',
			'LIST_111' => 'school_district',
			'LIST_113' => 'model',
			'LIST_117' => 'lot_sqft',
			'LIST_12' => 'close_date',
			'LIST_122' => 'lot_sqft',
			'LIST_13' => 'contract_date',
			'LIST_130' => 'cross_street',
			'LIST_131' => 'subdivision',
			'LIST_132' => 'listing_date',
			'LIST_137' => 'days_market',
			'LIST_15' => 'list_status',
			'LIST_19' => 'contingent',
			'LIST_22' => 'list_price',
			'LIST_23' => 'close_price',
			'LIST_24' => 'monthly_rate_low',
			'LIST_25' => 'price_sqft',
			'LIST_29' => 'map_code_grid',
			'LIST_31' => 'house_number',
			'LIST_33' => 'compass',
			'LIST_34' => 'street_name',
			'LIST_35' => 'unit_num',
			'LIST_36' => 'street_dir_sfx',
			'LIST_37' => 'street_type',
			'LIST_39' => 'city',
			'LIST_40' => 'state',
			'LIST_41' => 'county',
			'LIST_43' => 'zip',
			'LIST_46' => 'latitude',
			'LIST_47' => 'longitude',
			'LIST_48' => 'living_sqft',
			'LIST_51' => 'bedrooms_plus',
			'LIST_53' => 'year_built',
			'LIST_56' => 'lot_size',
			'LIST_57' => 'lot_acre',
			'LIST_66' => 'bedrooms',
			'LIST_67' => 'bathrooms',
			'LIST_68' => 'int_stories',
			'LIST_69' => 'ext_stories',
			'LIST_73' => 'private_community_pool',
			'LIST_75' => 'taxes',
			'LIST_78' => 'public_remarks',
			'LIST_80' => 'assessor_num',
			'LIST_82' => 'directions',
			'LIST_85' => 'marketing_name',
			'LIST_86' => 'builder_name',
			'LIST_87' => 'mod_time',
			'LIST_9' => 'dwelling_type',
			'LIST_8' => 'prop_type',
			'LIST_91' => 'vacation_rental',
			'LIST_94' => 'horses',
			'LIST_96' => 'ownership_type',
			'UNBRANDEDIDXVIRTUALTOUR' => 'virtual_tour_unbranded',
			'colisting_member_shortid' => 'colisting_member_shortid',
			'list_agent_last_name' => 'list_agent_last_name',
			'listing_member_shortid' => 'listing_member_shortid',
			'listing_office_name' => 'listing_office_name',
			'listing_office_shortid' => 'office_id',
			'selling_member_shortid' => 'selling_member',
		);
	
		$shortcode = $words;
		// Order the array by key length (reverse) because otherwise, for example, when LIST_131 should be replaced with
		// subdivision, it instead first matches LIST_13, and so "LIST_131:mysubdivision" becomes
		// "contract_date1:mysubdivision". If we replace LIST_131 first, and then LIST_13 after, the problem doesn't
		// exist.
		uksort($armls_fields_array, function($a_key, $b_key) {
			return strlen($b_key) - strlen($a_key);
		});
		foreach( $armls_fields_array as $mlsname => $easyname ) {
			$shortcode = str_ireplace( $mlsname, $easyname, $shortcode );
		}
		// Here are more that couldn't be done above because it'd be a duplicate key issue in PHP.
		$shortcode = str_ireplace( 'GF20070914134051409501000000', 'energy_green_feature', $shortcode );
		return $shortcode;
	}

	// This is an attempt to fix shortcode conversions that have previously run and didn't work as intended.
	// It grabs post that have the old shortcode in their metadata and swaps out the new shortcode with the old
	// shortcode in the post content. Then, the next time that page is loaded, the existing shortcode conversion process
	// (which I modified slightly and think it will work as expected now) will run.
	public function run_profound_to_ifound_shortcode_conversion($post_id = null) {
		global $wpdb;

		$conversion_done_meta_key_name = 'profound_to_ifound_shortcode_conversion_done';
		$posts = null;
		if ($post_id) {
			$posts = [get_post($post_id)];
		} else {
			$posts = get_posts([
				'numberposts' => -1,
				'post_type' => 'page',
				'ignore_sticky_posts' => true,
				'orderby' => 'ID',
				'meta_query' => [
					'relation' => 'AND',
					[
						'key' => static::$profound_shortcode_meta_key,
						'compare' => 'EXISTS',
					],
					[
						'key' => $conversion_done_meta_key_name,
						'compare' => 'NOT EXISTS',
					],
				]
			]);
		}
		$fixed_post_ids = [];
		$not_fixed_post_ids = [];
		foreach ($posts as $post) {
			$post_id = $post->ID;
			$content = $post->post_content;
			$old_shortcode = get_post_meta($post_id, static::$profound_shortcode_meta_key, true);
			// This regex will match any shortcode starting with ifound, including both
			// [ifound id=123] and [ifound_polygon_maker ...].
			// The (\R) is used instead of $ to match \r\n style newlines which Wordpress's rich-text editor seems to
			// use. Read more about it here: https://stackoverflow.com/a/18992691/135101. Note the /m modifier.
			$regex = '/\[ifound[ _].*\](\R)?/m';
			// Then, we insert back the same newline using '$1'.
			$new_content = preg_replace($regex, $old_shortcode . '$1', $content);
			if ($new_content !== $content) {
				// Note: previously we were using wp_update_post() here. What I think was happening is it would update
				// the post content as expected, and then some hook would run such that it immediately update the post
				// content again. The post content would thus end up not with the original ProFoundMLSSearchListings
				// shortcode, but with something like [ifound id=123]. When I'd look in the database, that postmeta ID
				// of 123 would not exist. Rather than take time to explore the hooks or conversion process, updating
				// the database directly will skip those hooks. It has the added bonus of not updating the post modified
				// date, which seems unnecessary anyway.
				$wpdb->update($wpdb->posts, ['post_content' => $new_content], ['ID' => $post_id], '%s', '%d');
				delete_post_meta($post_id, 'save_this_shortcode');
				update_post_meta($post_id, $conversion_done_meta_key_name, 'true');
				$fixed_post_ids[] = $post_id;
			} else {
				$not_fixed_post_ids[] = $post_id;
			}
		}
		return [$fixed_post_ids, $not_fixed_post_ids];
	}

	// This is similar to run_profound_to_ifound_shortcode_conversion(). We want to roll back posts to the most recent
	// revision before a certain date. That date would basically be when the site was converted from the old plugin to
	// the new. To 'roll back', we'll use the revisions system. So we'll create a new revision, which is a duplicate of
	// the old revision, and then set the post content to the revision's content.
	public function run_profound_shortcode_rollback($revision_datetime_gmt_before, $post_id = null) {
		global $wpdb;

		$rollback_done_meta_key = 'profound_shortcode_rollback_done';
		$posts = null;
		if ($post_id) {
			$posts = [get_post($post_id)];
		} else {
			$posts = get_posts([
				'numberposts' => -1,
				'post_type' => ['page', 'post'],
				'ignore_sticky_posts' => true,
				'meta_query' => [
					[
						'key' => static::$profound_shortcode_meta_key,
						'compare' => 'EXISTS',
					],
					[
						'key' => $rollback_done_meta_key,
						'compare' => 'NOT EXISTS',
					],
				],
			]);
		}
		$fixed_post_ids = [];
		$not_fixed_post_ids = [];
		$datetime_obj_revision_datetime_gmt_before = $this->util()
			->get_datetime_mysql_format($revision_datetime_gmt_before);
		foreach ($posts as $post) {
			$post_id = $post->ID;
			$was_fixed = false;
			$revisions = wp_get_post_revisions($post_id);
			if (count($revisions)) {
				// Sort by datetime desc
				usort($revisions, function($a, $b) {
					return $b->post_modified_gmt <=> $a->post_modified_gmt;
				});
				$revisions = array_filter($revisions, function($x) use ($datetime_obj_revision_datetime_gmt_before) {
					$datetime_of_revision = $this->util()->get_datetime_mysql_format($x->post_modified_gmt);
					return $datetime_of_revision < $datetime_obj_revision_datetime_gmt_before;
				});
				if (count($revisions)) {
					$first_revision = array_values($revisions)[0];
					$success_post_id = wp_restore_post_revision($first_revision->ID);
					if ($success_post_id) {
						update_post_meta($post_id, $rollback_done_meta_key, 'yes');
						$was_fixed = true;
					}
				}
			}
			if ($was_fixed) {
				$fixed_post_ids[] = $post_id;
			} else {
				$not_fixed_post_ids[] = $post_id;
			}
		}
		return [$fixed_post_ids, $not_fixed_post_ids];
	}
}
