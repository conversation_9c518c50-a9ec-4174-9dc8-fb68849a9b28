<?php
require_once("classes/dbroot.php");

class SEO_DB extends dbroot {



public function get_meta( $aid ){
		
	$sql = "SELECT * FROM `access_meta` WHERE `access_id` = '$aid' LIMIT 1;";
	
	return $this->process('get_assoc', $sql);
	
} // End get_meta Function



public function put_meta( $aid ){
		
	$sql = "INSERT INTO `access_meta` ( `access_id` ) 
			VALUES ( '$aid' );";
	
	return $this->process('put', $sql);
	
} // End put_meta Function




public function update_meta( $aid, $meta_var, $meta_val ){

	$sql = "UPDATE `access_meta` SET `$meta_var` = '$meta_val' WHERE `access_id` = '$aid'";
			
	return $this->process('update', $sql);
	
} // End update_meta Function




} //end class

?>