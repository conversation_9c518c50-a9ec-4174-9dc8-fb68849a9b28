<?php

namespace Profound\MLS;

class FieldMappings {
	private $options;
	private $db;
	private $fieldMappingByMlsFieldName;
	private $fieldMappingByEasyName;
	private $fieldMappingByMapName;
	private $fieldInfoByMlsAndClass;

	public function __construct($options = array()) {
		$this->options = $options;
		$this->load();
	}

	protected function load() {
		$mls = $this->getMls();
		$mls_class = $this->getMlsClass();
		$results = $this->fetchData($mls, $mls_class);
		$fieldMappings = $this->buildMappings($results, $mls, $mls_class);
		$this->fieldMappingsByMlsFieldName = $fieldMappings['byMlsFieldName'];
		$this->fieldMappingsByEasyName = $fieldMappings['byEasyName'];
		$this->fieldMappingsByMapName = $fieldMappings['byMapName'];
	}

	protected function getDb() {
		if (!$this->db) {
			$this->db = $this->options['db'];
		}
		return $this->db;
	}

	public function getByEasyName($easyName) {
		$fieldMapping = isset($this->fieldMappingsByEasyName[$easyName]) ? $this->fieldMappingsByEasyName[$easyName] : null;
		return $fieldMapping;
	}

	public function getByMapName($mapName) {
		$fieldMapping = isset($this->fieldMappingsByMapName[$mapName]) ? $this->fieldMappingsByMapName[$mapName] : null;
		return $fieldMapping;
	}

	public function getByMlsFieldName($mlsFieldName) {
		$fieldMapping = isset($this->fieldMappingsByMlsFieldName[$mlsFieldName]) ? $this->fieldMappingsByMlsFieldName[$mlsFieldName] : null;
		return $fieldMapping;
	}

	protected function getMls() {
		return $this->options['mls'];
	}

	protected function getMlsClass() {
		return $this->options['mls_class'];
	}

	protected function fetchData($mls, $mls_class) {
		$db = $this->getDb();
		$sql = "SELECT MapName, $mls as MlsFieldName, EasyName FROM field_mapping WHERE mls_class = ?";
		$results = $db->fetchAll($sql, array($mls_class));
		return $results;
	}

	protected function buildMappings($results, $mls, $mls_class) {
		$byMlsFieldName = array();
		$byEasyName = array();
		$byMapName = array();
		foreach ($results as $result) {
			$fieldMapping = $this->buildMapping($result, $mls, $mls_class);
			$byMlsFieldName[$fieldMapping->mlsFieldName] = $fieldMapping;
			$byEasyName[$fieldMapping->easyName] = $fieldMapping;
			if ($fieldMapping->easyName == 'list_price') {
				// This is a one-off problem for now. See the test named
				// testCanAddSecondaryEasyName for explanation.
				$byEasyName['price'] = $fieldMapping;
			}
			// Case 587: I'm still seeing a lot of references to the easy name
			// 'sqft' in our logs, so I'm adding it back here.
			if ($fieldMapping->easyName == 'living_sqft') {
				$byEasyName['sqft'] = $fieldMapping;
			}
			$byMapName[$fieldMapping->mapName] = $fieldMapping;
		}
		return array(
			'byMlsFieldName' => $byMlsFieldName,
			'byEasyName' => $byEasyName,
			'byMapName' => $byMapName,
		);
	}

	protected function buildMapping($result, $mls, $mls_class) {
		$fieldMapping = new FieldMapping(array('field_mappings' => $this));
		$fieldMapping->mls = $mls;
		$fieldMapping->mlsFieldName = $result['MlsFieldName'];
		$fieldMapping->mlsClass = $mls_class;
		$fieldMapping->easyName = $result['EasyName'];
		$fieldMapping->mapName = $result['MapName'];
		return $fieldMapping;
	}

	// Case 557. Ultimately, we should be using a proper sql-injection-proof
	// way of quoting SQL values. Until we have a good way to do that, we have
	// this way, which will at least help us quote or not quote values, which
	// will improve lookup time.
	public function quoteFieldValue($mlsFieldName, $mls, $mls_class, $val) {
		if ($this->isFieldOfKnownType($mlsFieldName, $mls, $mls_class)) {
			if ($this->shouldAddQuotes($mlsFieldName, $mls, $mls_class)) {
				return $this->addQuotes($val);
			}
			return $val;
		}
		// This is not perfect. A zip code would not be quoted when it should.
		// But hopefully better than nothing.
		if (is_numeric($val)) {
			return $val;
		}
		return $this->addQuotes($val);
	}

	protected function isFieldOfKnownType($mlsFieldName, $mls, $mls_class) {
		$fieldInfoByMlsAndClass = $this->getFieldInfoByMlsAndClass($mls, $mls_class);
		return isset($fieldInfoByMlsAndClass[$mls][$mls_class][$mlsFieldName]);
	}

	protected function shouldAddQuotes($mlsFieldName, $mls, $mls_class) {
		$fieldInfoByMlsAndClass = $this->getFieldInfoByMlsAndClass($mls, $mls_class);
		return $fieldInfoByMlsAndClass[$mls][$mls_class][$mlsFieldName]['should_quote'];
	}

	// In the future we can split this out using OOP. Today, as I solve MySQL
	// speed issues, it's more of a proof of concept.
	protected function getFieldInfoByMlsAndClass($mls, $mls_class) {
		if (!$this->fieldInfoByMlsAndClass) {
			$this->fieldInfoByMlsAndClass = array(
				'armls' => array(
					'res' => array(
						'LIST_43' => array('should_quote' => true),
						'APN' => array('should_quote' => true),
					),
					'rentals' => array(
						'LIST_43' => array('should_quote' => true),
					),
					'land' => array(
						'LIST_43' => array('should_quote' => true),
					)
				)
			);
		}
		return $this->fieldInfoByMlsAndClass;
	}

	protected function addQuotes($val) {
		return "'$val'";
	}
}
