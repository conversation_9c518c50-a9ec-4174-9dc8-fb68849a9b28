<?
/**
 * iFound_search_nearby class
 *
 * Display search_nearby in widget areas.
 *
 * @package iFOUND
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


/** TODO: Get <select> options ftom DB. This is only set up for an example. */
class iFound_search_nearby extends WP_Widget {
	
	private $settings;
		
	public function __construct(){

		parent::__construct( 
			false, 
			'iFound Mobile - Search Nearby', 
			array(
			'description' => 'Add Mobile Search Nearby to any widget area. This will only display on mobile devices.'
		));
		
	}
	
	/**
	 * Widget
	 *
	 * @since 1.0.0
	 */
	
	public function widget( $args, $instance ) {

		// Reminder: wp_is_mobile() checks the user-agent, which means that if you use the "Device toolbar" in Chrome to
		// simulate a mobile browser, it will work if you use a specific one, e.g. 'iPhone 12 Pro', but not if you use
		// the responsive mode and just make it small like a phone.
		if( ! wp_is_mobile() ) return;

		wp_enqueue_script( 'search_nearby_js' );
		
		echo $args['before_widget']; ?>
		
		<div class="ifound-search-nearby">
		
			<div class="widgets-wrap">
				
				<div class="search-nearby">
			
					<div href="<? echo $href; ?>" class="search-nearby-button button"><? _e( 'Search Nearby', 'ifound' ); ?></div>
					
				</div>
				
			</div>
			
		</div><?
		
		echo $args['after_widget'];
		
	}
	
}

?>