# WordPress IDX Plugin

The PHP, JS, and CSS files for the plugin are contained in the `plugin` directory.

WordPress widgets each have their own file & PHP Class.

The cURL code for accessing the IDX Server is in `plugin/classes/ProFoundIDX.php`.

The rest of the code is all contained in `plugin/classes/ProFoundMLS.php`.

#### CoffeeScript and Stylus

New development of JavaScript and CSS files is done via [CoffeeScript](http://coffeescript.org/) and [Stylus](http://learnboost.github.com/stylus/).

TODO: Change shoreman out for foreman.

For development, install [shoreman](https://github.com/hecticjeff/shoreman) globally to execute the `Procfile`:

    make shoreman-install

To start `shoreman` so that the `.coffee` and `.stylus` files are watched and compiled when changed, run:

    shoreman
