<?php

namespace Profound\MLS;

use Profound\Client\Access;

abstract class Mls {
	protected $options;
	protected $db;
	protected $propAPI;
	protected $searchAPI;

	function __construct($options = array()) {
		$this->options = $options;
	}

	public static function create($name, $options = array()) {
		switch ($name) {
			case 'armls':
				return new Armls($options);
			case 'armls_spark':
				return new ArmlsSpark($options);
			case 'glvarnv':
				return new Glvarnv($options);
			case 'mredil':
				return new Mredil($options);
			case 'paaraz':
				return new Paaraz($options);
			case 'paaraz_mlg':
				return new PaarazMlg($options);
			case 'sdcrca':
				return new Sdcrca($options);
			case 'sndmls':
				return new Sndmls($options);
			case 'tarmlsaz':
				return new Tarmlsaz($options);
			case 'trendmls':
				return new Trendmls($options);
			case 'brightmls':
				return new Brightmls($options);
			case 'naar':
				return new Naar($options);
			case 'naar_spark':
				return new NaarSpark($options);
			case 'cabor':
				return new Cabor($options);
			case 'wmar':
				return new Wmar($options);
			case 'crmls':
				return new Crmls($options);
			case 'recolorado':
				return new Recolorado($options);
			case 'recolorado_mlg':
				return new RecoloradoMlg($options);
			case 'wardex':
				return new Wardex($options);
			case 'realtracs':
				return new Realtracs($options);
			default:
				throw new \Exception("Invalid MLS name");
		}
	}

	public static function getMlsNameAbbrevs() {
		return array('armls', 'armls_spark', 'glvarnv', 'mredil', 'paaraz', 'paaraz_mlg', 'sdcrca', 'sndmls', 'tarmlsaz', 'trendmls', 'brightmls', 'naar', 'naar_spark', 'crmls', 'wmar', 'cabor', 'recolorado', 'recolorado_mlg', 'wardex', 'realtracs');
	}

	public function getAccess() {
		return $this->options['access'];
	}

	public function getPropAPI() {
		if (!$this->propAPI) {
			$this->propAPI = $this->options['propAPI'];
			// TODO: Could load API here if no such option was passed.
		}
		return $this->propAPI;
	}

	public function getSearchAPI() {
		if (!$this->searchAPI) {
			$this->searchAPI = $this->options['searchAPI'];
			// TODO: Could load API here if no such option was passed.
		}
		return $this->searchAPI;
	}

	protected function getSelfAsOption() {
		return array('mls' => $this);
	}

	public function getImageHandler($options = array()) {
		$image_handler_class_name = get_class($this) . "ImageHandler";
		return new $image_handler_class_name(array_merge($options, $this->options, $this->getSelfAsOption()));
	}

	public function getDb() {
		if (!$this->db) {
			$this->db = $this->options['db'];
			// TODO: Could load database here if no db option was passed.
		}
		return $this->db;
	}

	public function getPropertyLimiter() {
		$handler_name = get_class($this) . "PropertyLimiter";
		if (class_exists($handler_name)) {
			return new $handler_name(array_merge($this->options, $this->getSelfAsOption()));
		}
		return new PropertyLimiter(array_merge($this->options, $this->getSelfAsOption()));
	}

	public function getFieldPrefix() {
		return '';
	}
}
