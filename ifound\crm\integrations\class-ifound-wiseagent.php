<?php
/*
 * iFoundWiseAgent Class
 *
 * since 5.3.0
 *
 * Sync the contact list between iFound CRM and a Wise Agent account
 * Dependency Plugins: Gravity Forms
 */


/*
 *==========================================================
 * These API endpoints aren't being currently used         *
 * but are still here in case the implementation is needed *
 * in the future                                           *
 *==========================================================
 * 1. get_all_categories
 * 2. get_programs
 * 3. get_login_token
 * 4. add_clients_to_category
 * 5. get_category_client_count
 * 6. get_transaction_names
 * 7. get_transaction_notes
 * 8. add_clients_to_marketing_program
 * 9. remove_clients_from_category
 * 10. add_planner_event
 * 11. add_property
 */

defined('ABSPATH') or die ('You do not have access');

class iFoundWiseAgent extends iFoundContacts {
	private $wa_api_key;
	private $is_wa_enabled = false;

	private static $last_push_sync_option_name = 'ifound_wise_agent_last_push_sync';
	private static $last_pull_sync_option_name = 'ifound_wise_agent_last_pull_sync';
	private static $wa_date_format = 'Y-m-d H:i';
	private static $wa_date_alternate_format = 'm/d/Y H:i:s A';
	public static $wa_id_key = 'ifound_wise_agent_id';
	public static $api_key_option_name = 'ifound_wise_agent_api_key';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('ifound_wa_button', array($this, 'wa_button'));

		$wiseagent_api_key = $this->local_get_option(static::$api_key_option_name);
		if ($wiseagent_api_key) {
			$this->wa_api_key = $wiseagent_api_key;
			$this->is_wa_enabled = true;

			add_action('admin_init', [$this, 'scripts']);
			add_action('wp_ajax_wa_launch_full_sync', array($this, 'wa_launch_full_sync'));
			add_action('save_post', array($this, 'check_updated_contact'), 10, 1);
			add_action('ifound_wiseagent_new', array($this, 'check_updated_contact'), 10, 1);
		} else {
			wp_register_script('wiseagent_info_js', plugins_url('js/wiseagent-info.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);
		}
	}

	// To explain the word 'local', it is just a way to make it stand out that we are not directly calling wordpress'
	// methods, when you see e.g. $this->local_get_option() instead of plain get_option().
	private function local_get_option($name, $default = false) {
		$modified_key = $this->get_option_name($name);
		return get_option($modified_key, $default);
	}

	private function local_update_option($name, $value) {
		$modified_key = $this->get_option_name($name);
		return update_option($modified_key, $value);
	}

	// To work with teams, we have multiple options. One for the shared admin, and one for each team member. We will
	// simply append the CRM ID to the end of the option name, which is how we do it elsewhere.
	public function get_option_name($option_name) {
		return $option_name . $this->crm_id();
	}

	public function scripts() {
		wp_register_script('wiseagent_js', plugins_url('js/wiseagent.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);
		wp_localize_script('wiseagent_js', 'wa_launch_full_sync', array(
			'endpoint'              => admin_url( 'admin-ajax.php' ),
			'nonce'                 => wp_create_nonce( 'wa_launch_full_sync_secure_me' ),
		));
	}

	// This method is handy during dev/debug.
	private function local_get_transient($key) {
		// return false;
		$modified_key = $this->get_option_name($key);
		return get_transient($modified_key);
	}

	private function local_set_transient($transient, $value, $expiration) {
		$modified_key = $this->get_option_name($transient);
		set_transient($modified_key, $value, $expiration);
	}

	private function timestamp_to_wa_formatted_date($timestamp) {
		return gmdate(static::$wa_date_format, $timestamp);
	}

	// The WA date format is 'Y-m-d H:i', except when it's (seemingly randomly, even for the same API endpoint)
	// 'm/d/Y H:i:s A'. This caught me off guard, but really shouldn't have, given the other inconsistencies I've seen
	// from WA.
	private function wa_formatted_date_to_timestamp($wa_formatted_date) {
		if (!$wa_formatted_date || gettype($wa_formatted_date) !== 'string') {
			return 0;
		}
		$format = static::$wa_date_format;
		if (count(explode(':', $wa_formatted_date)) === 3) {
			$format = static::$wa_date_alternate_format;
		}
		$datetime = DateTime::createFromFormat($format, $wa_formatted_date);
		if (!$datetime) {
			return 0;
		}
		return intval($datetime->format('U'));
	}

	/*
	 * Sync the IFA CRM and Wiseagent CRM
	 *
	 * @since 5.3.0
	 *
	 * @args manual - boolean value that indicicates whether sync was triggered
	 * by the user
	 * @return no return value
	 */
	public function wa_full_sync() {
		$last_push_timestamp = $this->local_get_option(static::$last_push_sync_option_name, null);
		// We only need to send the recently updated contacts.
		$contacts = $this->get_local_contacts($last_push_timestamp);
		$this->send_all_contacts($contacts);
		// But when we download contacts from WA and compare them locally, we need all the local contacts.
		$contacts = $this->get_local_contacts();
		$last_pull_timestamp = $this->local_get_option(static::$last_pull_sync_option_name, null);
		$last_pull_wa_formatted_date = $this->timestamp_to_wa_formatted_date($last_pull_timestamp);
		$this->pull_contacts_from_wa($contacts, $last_pull_wa_formatted_date);
	}

	/*
	 * The handle used by jQuery's AJAX call to launch the full sync
	 *
	 * @since 5.3.0
	 *
	 * @args void
	 * @return void
	 */
	public function wa_launch_full_sync() {
		check_ajax_referer('wa_launch_full_sync_secure_me', 'wa_launch_full_sync_nonce');
		$this->wa_full_sync();
		die();
	}

	/*
	 * This method serves as a fork, deciding whether create a contact
	 * or update an exisitng on, or forgo updaing/creaing entirely
	 * @since 5.3.0
	 *
	 * @args integer - contact id
	 *       wp post object - contact
	 *       boolean - update
	 * @return void
	 */
	public function check_updated_contact($post_id) {
		$post = get_post($post_id);
		if (!in_array($post->post_type, iFoundJointContact::$all_contact_post_types, true)) {
			return;
		}

		$contact_id = $post_id;
		if(!$this->has_required_field($contact_id)) return;
		$this->send_or_update_ifa_contact($contact_id);
	}

	/*
	 * Displays the "Sync Wiseagent Contacts" button
	 * Enqueues a script, depends whether WA is activated or not
	 *
	 * @since 5.3.0
	 *
	 * @args void
	 * @return void
	 */
	public function wa_button() {
		if(!$this->is_wa_enabled) wp_enqueue_script('wiseagent_info_js');
                else wp_enqueue_script('wiseagent_js');

                echo '<div id="wa-sync" class="button wa-sync-button" style="position:relative;">Sync Wise Agent Contacts</div>';
	}

	private function get_local_contacts($filter_timestamp = null) {
		$contacts = iFoundJointContact::new_hookless()->get_all_contacts();
		// Order the contacts by modified date, because we save off the datetime of the most recent one so that we know
		// to only grab newer ones next time.
		usort($contacts, function($a, $b) {
			return $a->post_modified_gmt <=> $b->post_modified_gmt;
		});
		if (!$filter_timestamp) {
			return $contacts;
		}

		$filtered_contacts = [];
		if (gettype($filter_timestamp === 'string')) {
			$filter_timestamp = intval($filter_timestamp);
		}
		foreach($contacts as $contact) {
			$post_last_updated = get_the_modified_time('U', $contact->ID);
			if($post_last_updated >= $filter_timestamp) $filtered_contacts[] = $contact;
		}

		return $filtered_contacts;
	}

	/*
	 * Sending all the contacts to Wiseagent
	 * Send new contacts without the Wiseagent client id
	 * Update contacts that have the Wiseagent client id
	 *
	 * @since 5.3.0
	 *
	 * @args contacts - array of post objects or a single post object
	 * @return no return value
	 */
	private function send_all_contacts($contacts) {
		for ($i = 0; $i < count($contacts); $i++) {
			$contact = $contacts[$i];
			$this->send_or_update_ifa_contact($contact->ID);
			$post_last_updated = get_the_modified_time('U', $contact->ID);
			// If this is the last contact, we'll do a little trick where we set the timestamp to be one second greater,
			// such that the next time we grab the list of contacts to sync, we won't grab it again (or several, in the
			// case where multiple have the same timestamp).
			if ($i === count($contacts) - 1) {
				$post_last_updated++;
			}
			$this->local_update_option(static::$last_push_sync_option_name, $post_last_updated);
		}
	}

	private function send_or_update_ifa_contact($contact_id) {
		$wa_id = get_post_meta($contact_id, static::$wa_id_key, true);
		if ($wa_id) {
			$this->update_ifa_contact($contact_id, $wa_id);
		} else {
			$this->send_ifa_contact($contact_id);
		}
	}

	private function should_exclude_wa_contact($wa_contact) {
		$categories_json = $wa_contact->Categories;
		$categories = json_decode($categories_json, true);
		$lowercase_category_names = array_map(function($x) {
			return strtolower($x['name']);
		}, $categories);
		return in_array(iFoundCrm::$exclude_tag_name, $lowercase_category_names);
	}

	/*
	 * Getting all the contacts from Wiseagent,
	 * checking the incoming results to see if either a contact
	 * with a particular agent id already exists inside of the IFA CRM
	 * or a contact with the particular email exists in the IFA CRM or both.
	 * If none of it is the case, create a new contact in the IFA CRM
	 *
	 * @since 5.3.0
	 *
	 * @args ifa contacts - an array of current contact objects in the IFA CRM
	 *       date
	 * @return no return value
	 */
	private function pull_contacts_from_wa($ifa_contacts, $wa_formatted_date) {
		$wa_contacts = $this->wa_get_contacts($wa_formatted_date);

		foreach($wa_contacts as $wa_contact) {
			if ($this->should_exclude_wa_contact($wa_contact)) {
				continue;
			}

			$dupe_found = false;
			foreach($ifa_contacts as $ifa_contact) {
				$ifa_wa = get_post_meta($ifa_contact->ID, static::$wa_id_key, true);

				if($wa_contact->ClientID == $ifa_wa) {
					$dupe_found = true;
					$this->save_contact_meta($ifa_contact->ID, $this->wa_to_ifa_contact($wa_contact));
					break;
				}
			}

			if(!$dupe_found) $this->create_ifa_post($wa_contact);
			$timestamp = $this->wa_formatted_date_to_timestamp($wa_contact->DateUpdatedUTC);
			// Note: we do a trick when SENDING contacts, where when we get to the last contact in the list, we set the
			// timestamp to one second in the future. This helps with not sending the same contact again in the future.
			// However, we can't really make use of that trick for pulling contacts since WA's granularity is in
			// minutes.
			$this->local_update_option(static::$last_pull_sync_option_name, $timestamp);
		}
	}

	public function save_contact_meta($contact_id, $input) {
		parent::save_contact_meta($contact_id, $input);
		update_post_meta($contact_id, static::$wa_id_key, $input[static::$wa_id_key]);
	}

	/*
	 * Transforms an array of categories to a string seprated by a comma
	 *
	 * @since 5.3.0
	 *
	 * @args catergories array or an empty string
	 * @returns a string of comma separated values if the categries array
	 * isn't an empty string, returns an empty string otherwise
	 */
	private function transform_categories($categories) {
		if($categories === '' || $categories === NULL) return '';
		return @implode(',', $categories);
	}

	/*
	 * Transforms an array of client ids to a string seprated by a comma
	 *
	 * @since 5.3.0
	 *
	 * @args client ids array or an empty string
	 * @returns a string of comma separated values if the client ids array
	 * isn't an empty string, returns an empty string otherwise
	 */
	private function transform_client_ids($client_ids) {
		if($client_ids === '') return '';
		return implode(',', $client_ids);
	}

	/*
	 * This method exracts the number value from the address
	 *
	 * @since 5.3.0
	 *
	 * @args address string
	 * @return string containing the number part of the address
	 */
	private function get_address_number($address) {
		return explode(' ', $address)[0];
	}

	/*
	 * Gets the street name part of the address
	 *
	 * @since 5.3.0
	 *
	 * @args address string
	 * @return string containing the street name part of the address
	 */
	private function get_address_street($address) {
		$parts = explode(' ', $address);
		unset($parts[0]);
		return implode(' ', $parts);
	}

	/*
	 * Transforms ifa contact fields into wiseagent contact fields
	 * Some of the fields are missing in the ifa, so now they will
	 * be populated with empty strings, in case we add those
	 * fields in the future
	 *
	 * @since 5.3.0
	 *
	 * @args contact id - post ID of a contact
	 * @returns an array formatted to be sent to Wiseagent
	 */
	private function ifa_to_wa_contact($contact_id) {
		$contact_meta = get_post_meta($contact_id);
		$not_in_ifa = '';

		return array(
			'clientid'    => $contact_meta[static::$wa_id_key][0],
			'cfirst'      => $contact_meta['fname'][0],
			'clast'       => $contact_meta['lname'][0],
			'cemail'      => $contact_meta['email'][0],
			'phone'       => $contact_meta['hphone'][0],
			'fax'         => $not_in_ifa,
			'cell'        => $contact_meta['mphone'][0],
			'work'        => $contact_meta['wphone'][0],
			'company'     => $not_in_ifa,
			'address'     => $contact_meta['address'][0],
			'suiteno'     => $contact_meta['address_2'][0],
			'city'        => $contact_meta['city'][0],
			'state'       => $contact_meta['state'][0],
			'zip'         => $contact_meta['zip'][0],
			'rank'        => $not_in_ifa,
			'contactstatus' => $contact_meta['status'],
			'source'      => $contact_meta['acquired_from'][0],
			'categories'  => $this->transform_categories($contact_meta['status'][0]), // FIXME:
			'calltype'    => $not_in_ifa,
			'cdff'        => $not_in_ifa,
			'programid'   => $not_in_ifa
		);
	}

	/*
	 * Transforms incoming Wiseagent contact fields into the IFA contacts format
	 *
	 * @since 5.3.0
	 *
	 * @args wa contact - Associative array
	 * @returns an associative array with IFA fields
	 */
	private function wa_to_ifa_contact($wa_contact) {
		return array(
			'fname'            => $wa_contact->CFirst,
			'lname'            => $wa_contact->CLast,
			'email'            => $wa_contact->CEmail,
			'hphone'           => $wa_contact->HomePhone,
			'wphone'           => $wa_contact->WorkPhone,
			'mphone'           => $wa_contact->MobilePhone,
			'birthday'         => $wa_contact->Birthday,
			'address'          => $wa_contact->AddressNumber . ' ' . $wa_contact->AddressStreet,
			'address2'         => $wa_contact->SuiteNo,
			'city'             => $wa_contact->City,
			'state'            => $wa_contact->State,
			'zip'              => $wa_contact->Zip,
			'acquired_from'    => '',    // FIXME
			'acquired_date'    => $wa_contact->DateMet,
			'fname_spouse'     => $wa_contact->SpouseFirst,
			'lname_spouse'     => $wa_contact->SpouseLast,
			'status'           => $wa_contact->Status,
			static::$wa_id_key => $wa_contact->ClientID,
		);
	}

	private function does_contact_exist_by_email($email_address) {
		$args = iFoundJointContact::new_hookless()->get_all_contacts_args();
		$args['numberposts'] = 1;
		$args['meta_query'] = [
			[
				'key' => 'email',
				'value' => $email_address,
			],
		];
		$contacts_with_same_email = get_posts($args);
		return count($contacts_with_same_email) > 0;
	}

	/*
	 * Creates a post from a recieved Wiseagent contact
	 * If the date param is passed, post publishment is set to the date
	 *
	 * @since 5.3.0
	 *
	 * @args wa contact - a contact array with the WiseAgent meta fields
	 *       date - date to publish the post
	 * @return - no return value
	 */
	private function create_ifa_post($wa_contact, $date = NULL) {
		$contact = $this->wa_to_ifa_contact($wa_contact);

		// Prevent duplicates by email address
		if ($this->does_contact_exist_by_email($contact['email'])) {
			return;
		}

		$postarr = array(
			'post_type'   => iFoundJointContact::new_hookless()->get_new_contact_type(),
			'post_author' => iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id(),
			'post_date'   => $date, // TEST
			'post_content_filtered' => '',
			'post_title'  => ucwords($contact['fname']) . ' ' . ucwords($contact['lname']),
			'post_status' => $date === NULL ? 'publish' : 'future'
		);

		$contact_id = wp_insert_post($postarr);
		if(!$contact_id) {
			error_log('Error while creating a contact recieved from Wiseagent');
			return;
		}

		$this->save_contact_meta($contact_id, $contact);
	}

	/*
	 * Accepts entry and split it into an associative array
	 * that contains two keys - date and uri
	 *
	 * @since 5.3.0
	 *
	 * @args entry = entry string
	 * @returns associative array with the data and uri keys
	 */
	private function split_url_and_date($entry) {
		// Entry format example: "2019-07-18 10:32:54;https://testdonotdelete.sitedistrict.com/listing-search/"
		$entry_array = explode(';', $entry);

		return array(
			'date' => $entry_array[0],
			'uri'  => $entry_array[1]
		);
	}

	private function send_notes($contact_id, $wa_id) {
		$this->send_subdata($contact_id, 'activity_log', function($entry) use ($wa_id) {
			$this->wa_add_contact_note([$wa_id], $entry, 'acitivity_log', ['activity_log']);
		});
	}

	// Subdata means notes or activity.
	private function send_subdata($contact_id, $meta_key, $send_fn) {
		$meta_array = get_post_meta($contact_id, $meta_key);
		if (!$meta_array) {
			return;
		}

		// We'll store a count in the database of successfully uploaded entries of subdata. That way, the next time it's
		// time to send subdata, we can start from where we last left off.
		$count_key_name = "ifound_wise_agent_{$meta_key}_count";
		$last_count = intval(get_post_meta($contact_id, $count_key_name, true));
		for ($i = $last_count; $i < count($meta_array); $i++) {
			$entry = $meta_array[$i];
			$send_fn($entry);
			update_post_meta($contact_id, $count_key_name, $i + 1);
		}
	}

	private function send_activity($contact_id, $wa_id) {
		$send_fn = function ($entry) use ($wa_id) {
			$entry_data = $this->split_url_and_date($entry);
			$this->wa_add_visited_website($wa_id, $entry_data['uri'], $entry_data['date']);
		};

		$meta_keys = ['page_view', 'advanced_view', 'results_view'];
		foreach ($meta_keys as $meta_key) {
			$this->send_subdata($contact_id, $meta_key, $send_fn);
		}
	}

	/*
	 * Sends an IFA contact to Wiseagent. Gets the new WA id returned from WA
	 *
	 * @since 5.3.0
	 *
	 * @args integer - post id
	 *       [,integer] - object id in case the method was triggered by
	 *                    the Registration Form
	 * @return no return value
	 */
	public function send_ifa_contact($contact_id, $obj_id = NULL) {
		if($obj_id !== NULL) $contact_id = $obj_id;
		if(!$this->has_required_field($contact_id)) return;

		$contact = $this->ifa_to_wa_contact($contact_id);
		$wa_id = $this->wa_webcontact($contact);

		if($wa_id !== NULL) {
			update_post_meta($contact_id, static::$wa_id_key, $wa_id);
		}

		$this->send_notes($contact_id, $wa_id);
		$this->send_activity($contact_id, $wa_id);
	}

	/*
	 * Updates a contact in Wiseagent
	 *
	 * @since 5.3.0
	 *
	 * @args post id
	 * @return no return value
	 */
	private function update_ifa_contact($contact_id, $wa_id) {
		$contact = $this->ifa_to_wa_contact($contact_id);
		$result = $this->wa_update_contact($contact);

		$this->send_notes($contact_id, $wa_id);
		$this->send_activity($contact_id, $wa_id);
	}

	/*
	 *
	 * @since 5.3.0
	 *
	 * @args request type - string
	 * @returns an endpoint associative array
	 * with 2 keys - endpoint's url and the http request type
	 * for a corresponding WiseAgent API request type
	 */
	private function get_api_endpoint($request_type) {
		$endpoint = NULL;
		$default_url = 'https://sync.thewiseagent.com/http/webconnect.asp';
		$visitedd_url = 'https://thewiseagent.com/secure/client/api/ClientVisitedDomain.aspx?apikey=' . $this->wa_api_key;

		switch($request_type) {
			case 'getSingleContact':
			case 'getContactsCount':
			case 'getContacts':
			case 'getAllCategoriesWithCount':
			case 'getPrograms':
			case 'getLoginToken':
				$endpoint = array(
					'url'           => $default_url,
					'http_req_type' => 'GET'
				);

			break;
			case 'webcontact':
			case 'updateContact':
			case 'addContactNote':
			case 'addClientsToCategory':
			case 'getCategoryClientCount':
			case 'getTransactionNames':
			case 'getTransactionNotes':
			case 'getTeam':
			case 'addClientsToMarketingProgram':
			case 'removeClientsFromCategory':
			case 'addPropertySearchCriteria':
			case 'addPlannerEvent':
			case 'addProperty':
				$endpoint = array(
					'url'           => $default_url,
					'http_req_type' => 'POST'
				);
			break;
			case 'addVisitedWebsite':
				$endpoint = array(
					'url'           => $visitedd_url,
					'http_req_type' => 'PUT'
				);
			break;
			default:
				$endpoint = array(
					'url'		=> $visitedd_url,
					'http_req_type' => 'PUT'
				);
		}

		return $endpoint;
	}

	/*
	 * This method is reponsible for handling all of the outgoing
	 * connections to Wiseagent
	 *
	 * @since 5.3.0
	 *
	 * @args body - request data, associative array
	 * @return result - an array of json objects
	 *                  NULL if there was a connection error
	 */
	private function wa_curl($body) {
		$endpoint = $this->get_api_endpoint($body['requestType']);
		$wa_auth_token = $this->get_auth_token();

		$ch = curl_init();

		if($endpoint['http_req_type'] === 'PUT') $post_data = json_encode($body);
		else $post_data = http_build_query($body);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_URL, $endpoint['url']);
		curl_setopt($ch, CURLOPT_HTTPHEADER, ["Authorization: Basic $wa_auth_token", 'cache-control: no-cache']);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $endpoint['http_req_type']);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);

		$result = curl_exec($ch);
		if($errno = curl_errno($ch)) {
			error_log(serialize($errno));
			$result = NULL;
		}

		curl_close($ch);

		return json_decode($result);
	}

	private function get_auth_token() {
		return iFound::new_hookless()->get_config()['wise_agent_auth_token'];
	}

	/*
	 * Takes a json decoded response from wa
	 * On successful response returns wa_id
	 * On failure returns false
	 *
	 * @since 5.3.0
	 *
	 * @args string $wa_response
	 * @return string/boolean
	 */
	private function get_wa_id($wa_response) {
		$success = $wa_response[0]->success;
		if($success !== 'true') return false;
		return $wa_response[1]->ClientID;
	}

	/*
	 * Takes a contact id and checks if it has the
	 * first name meta value so we're not sending an empty contact
	 *
	 * @since 5.3.0
	 *
	 * @args integer - post ID
	 * @return boolean if the contact has required fields
	 */
	private function has_required_field($contact_id) {
		$meta = get_post_meta($contact_id, 'fname', true);
		if($meta === '') return false;
		return true;
	}

	/*
	 * Extracts the label from a URL. The last page in the URL is the base for the label
	 *
	 * @since 5.4.1
	 *
	 * @args string - url
	 * @return - string. Empty if the page visited is the home page
	 */
	private function getLabel($uri) {
		$url_parts = explode('/', $uri);
		$last_index = count($url_parts) - (substr($uri, -1) !== '/' ? 1 : 2);
		$url_last_part = $url_parts[$last_index];

		if(stripos($url_last_part, $_SERVER['HTTP_HOST']) !== FALSE)
			return '';

		$words = ucwords($url_last_part, '-');
		return str_replace('-', ' ', $words);
	}

	// https://www.thewiseagent.com/docs/api.asp#anch4
	private function wa_get_contacts($date = '', $categories = '', $page = '', $page_size = '') {
		$body = array(
			'apikey'      => $this->wa_api_key,
			'requestType' => 'getContacts',
			'DateUpdatedUTC'=> $date,
			'categories'  => $this->transform_categories($categories),
			'page'        => $page,
			'page_size'   => $page_size
		);
		return $this->wa_curl($body);
	}

	// https://www.thewiseagent.com/docs/api.asp#anch9
	private function wa_webcontact($contact) {
		$body = array(
			'apikey'      => $this->wa_api_key,
			'requestType' => 'webcontact',
			'CFirst'      => $contact['cfirst'],
			'CLast'       => $contact['clast'],
			'CEmail'      => $contact['cemail'],
			'HomePhone'   => $contact['phone'],
			'Fax'         => $contact['fax'],
			'MobilePhone' => $contact['cell'],
			'WorkPhone'   => $contact['work'],
			'Company'     => $contact['company'],
			'AddressNumber' => $this->get_address_number($contact['address']),
			'AddressStreet' => $this->get_address_street($contact['address']),
			'SuiteNo'     => $contact['suiteno'],
			'city'        => $contact['city'],
			'state'       => $contact['state'],
			'zip'         => $contact['zip'],
			'Rank'        => $contact['rank'],
			'ContactStatus' => $contact['contactstatus'],
			'Source'      => $contact['source'],
			'Categories'  => $this->transform_categories($contact['categories']),
			'calltype'    => $contact['calltype'],
			'CommaDelimitedFormFields' => $contact['cdff'],
			'ProgramID'   => $contact['programid']
		);

		return $this->get_wa_id($this->wa_curl($body));
	}

	// https://www.thewiseagent.com/docs/api.asp#anch10
	// FIXME:
	private function wa_update_contact($contact) {
		$body = array(
			'apikey'      => $this->wa_api_key,
			'requestType' => 'updateContact',
			'clientID'    => $contact['clientid'],
			'CFirst'      => $contact['cfirst'],
			'Clast'       => $contact['clast'],
			'CEmail'      => $contact['cemail'],
			'HomePhone'   => $contact['phone'],
			'MobilePhone' => $contact['cell'],
			'WorkPhone'   => $contact['work'],
			'Company'     => $contact['company'],
			'SuiteNo'       => $contact['suiteno'],
			'City'        => $contact['city'],
			'State'       => $contact['state'],
			'Zip'         => $contact['zip'],
			'County'      => $contact['county'],
			'Country'     => $contact['country'],
			'AddressNumber' => $this->get_address_number($contact['address']),
			'AddressStreet'  => $this->get_address_street($contact['address']),
			'Subdivision' => $contact['subdivision'],
			'BuildingFloor' => $contact['buildingfloor']
		);

		return $this->wa_curl($body);
	}

	// https://www.thewiseagent.com/docs/api.asp#anch11
	private function wa_add_contact_note($client_ids, $note, $subject, $categories = '') {
		$body = array(
			'apikey'      => $this->wa_api_key,
			'requestType' => 'addContactNote',
			'note'        => $note,
			'subject'     => $subject,
			'categories'  => $this->transform_categories($categories),
			'clientIds'   => $this->transform_client_ids($client_ids)
		);

		return $this->wa_curl($body);
	}

	// https://www.thewiseagent.com/docs/api.asp#anch22
	private function wa_add_visited_website($client_id, $uri, $dtvisited) {
		$body = array(
			'SingleClient' => array(
				'ClientId'        => $client_id,
				'UrlVisited'      => $uri,
				'Label'           => $this->getLabel($uri),
				'DateTimeVisited' => $dtvisited
			),
			'BulkClients'  =>  '' // WTF. We don't need it
		);

		return $this->wa_curl($body);
	}
}
