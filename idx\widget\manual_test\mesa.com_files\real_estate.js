	function checkRealEstateForm(form)
	{
	
		if(form.name.value == "") 
		{
	
			alert('The name field is required.');
			form.name.focus();
			return false;
			
		}else if(form.prange.value == "")
		{
		
			alert('Please select a price range.');
			form.prange.focus();
			return false;
			
		}else if(form.email.value == "")
		{
		
			alert('Please enter an email address.');
			form.email.focus();
			return false;
			
		}else if(form.sqft.value == "")
		{
		
			alert('Please select a square footage.');
			form.sqft.focus();
			return false;
			
		}else if(form.brooms.value == "")
		{
		
			alert('Please select the number of bedrooms.');
			form.brooms.focus();
			return false;
			
		}else if(form.baths.value == "")
		{
		
			alert('Please select the number of bathrooms.');
			form.baths.focus();
			return false;
			
		}else if(form.dtype.value == "")
		{
		
			alert('Please select dwelling type.');
			form.dtype.focus();
			return false;
			
		}else if(form.interest.value == "")
		{
		
			alert('Please enter cities or areas of interest.');
			form.interest.focus();
			return false;
			
		}
		
		return true;
		
	}

	function checkMesaRealEstateForm(form)
	{
	
		if(form.name.value == "") 
		{
	
			alert('The name field is required.');
			form.name.focus();
			return false;
			
		}else if(form.email.value == "")
		{
		
			alert('Please enter an email address.');
			form.email.focus();
			return false;
			
		}else if(form.phone.value == "")
		{
		
			alert('Please enter a telephone number.');
			form.phone.focus();
			return false;
			
		}
		
		return true;
		
	}