<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );
	
/**
 * Liondesk Class
 * 
 * @link https://api-v1.liondesk.com/docs.html
 *
 * @since 1.0.21
 */
 
class iFoundLionDesk extends iFoundContacts {

	/**
	 * init iFOUND_liondesk class.
	 *
	 * @since 1.0.21
	 */
	 
	public static function init() {
		if( get_option( 'liondesk_api_key' ) ) {
        	$class = __CLASS__;
        	new $class;
        }
    }
	
	/**
	 * Constructor
	 *
	 * @since 1.0.21
	 */
	 
	public function __construct() {

		// Disable for now. We don't have anyone on LionDesk now and aren't sure if this works anymore.
		// add_action( 'ifound_external_crm_share_button', array( $this, 'liondesk_share_button' ) );
		// add_action( 'ifound_external_crm_new_submission', array( $this, 'liondesk_new_submission' ), 10, 1 );
		// add_action( 'ifound_external_crm_search_entry', array( $this, 'liondesk_search_entry' ), 10, 1 );
		// add_action( 'ifound_external_crm_property_view', array( $this, 'liondesk_property_view' ), 10, 1 );
		// add_action( 'ifound_external_crm_contact_inputs', array( $this, 'liondesk_contact_inputs' ) );
		// add_action( 'ifound_external_crm_save_contact_data', array( $this, 'liondesk_save_contact_data' ), 10, 2 );
		
	}
	
	/**
	 * Contact Inputs
	 *
	 * Custom inputs to be displayed in the contact record.
	 *
	 * @since 1.0.26
	 */
	
	public function liondesk_contact_inputs() { ?>
		
		<h4 class="liondesk-heading"><? _e( 'LionDesk', 'ifound' ); ?></h4>
		
		<table class="form-table contact-table">
				
			<tbody>
		
				<tr>

					<th scope="row"><label for="liondesk_contact_id"><? _e( 'Contact ID', 'ifound' ); ?></label></th>

					<td>

						<input type="text" name="liondesk_contact_id" id="liondesk_contact_id" value="<? echo get_post_meta( get_the_ID(), 'liondesk_contact_id', true ); ?>" class="regular-text">

					</td>

				</tr>
			
			</tbody>
			
		</table><?
		
	}
	
	/**
	 * Save Contact Data
	 *
	 * Save the custom data from the contact record for LionDesk.
	 *
	 * @since 1.0.26
	 */
	
	public function liondesk_save_contact_data( $post_id, $input ) {
		
		update_post_meta( $post_id, 'liondesk_contact_id', intval( $input['liondesk_contact_id'] ) );
		
	}
	
	/**
	 * Share Button
	 *
	 * This button is displayed in the Campaign Builder and will share the current email attached to a save_this_id for the link.
	 *
	 * @since 1.0.21
	 */
	
	public function liondesk_share_button() { ?>
			
		<div class="button button-primary liondesk-button" disabled>
			<i class="fal fa-share" aria-hidden="true"></i>
			<? _e( 'Share with LionDesk', 'ifound' ); ?>
		</div><?
		
	}
	
	/**
	 * LionDesk New Submission
	 *
	 * Push the contatct info to LionDesk. 
	 *
	 * @since 1.0.21
	 *
	 * @param object $entry The contact data from form submit.
	 */
	
	public function liondesk_new_submission( $entry ) {
		
		$data = array(
			'action' 				=> 'NewSubmission',
			'firstname' 			=> $entry->fname,
			'lastname' 				=> $entry->lname,
			'comments' 				=> $entry->comments,
			'phone' 				=> $entry->hphone,
			'email' 				=> $entry->email,
			'tag' 					=> 'Contact from your iFoundAgent website: ' . site_url( '/' ),
			'contactid' 			=> $entry->contact_id,
			'siteid' 				=> '1'
		);
	
		$response = $this->liondesk_request( $data );
		
		$object = json_decode( $response );
		
		update_post_meta( $entry->contact_id, 'liondesk_contact_id', $object->id );
		
	}

	/**
	 * LionDesk Search Entry
	 *
	 * Push the criteria from a search info to LionDesk. 
	 *
	 * @since 1.0.21
	 *
	 * @param object $input The input data from a the search.
	 */
	
	public function liondesk_search_entry( $input ) {
		
		/** Check if $contact_id set. */
		if( ! defined( 'iFOUND_CONTACT_ID' ) ) return;
			
		if( isset( $input ) ) {
			
			$criteria = join( ', ', $input );
	
		} else $criteria = 'None Specified.';
		
		$data = array(
			'action' 				=> 'NewActivity',
			'action' 				=> 'NewActivity',
			'liondeskcontactid' 	=> get_post_meta( iFOUND_CONTACT_ID, 'liondesk_contact_id', true ),
			'liondeskactivityid'	=> '1',
			'siteid' 				=> '1',
			'sitedomain' 			=> site_url( '/' ),
			'type' 					=> 'search_performed',
			'description' 			=> 'New Search:' . $criteria
		);
		
		$this->liondesk_request( $data );
		
	}
	
	/**
	 * LionDesk Property View
	 *
	 * Push the property data info to LionDesk. 
	 *
	 * @since 1.0.21
	 *
	 * @param object $results The data for the property viewed
	 */
	
	public function liondesk_property_view( $results ) {
		
		/** Check if $contact_id set. */
		if( ! defined( 'iFOUND_CONTACT_ID' ) ) return;
		
		$address = apply_filters( 'ifound_address', $results );

		$data = array(
			'action' 				=> 'NewActivity',
			'liondeskcontactid' 	=> get_post_meta( iFOUND_CONTACT_ID, 'liondesk_contact_id', true ),
			'liondeskactivityid'	=> '1',
			'siteid' 				=> '1',
			'sitedomain' 			=> site_url( '/' ),
			'type' 					=> 'property_viewed',
			'description' 			=> 'Property View: ' . $address,
			'property' 				=>  array(
				'mls' 					=> $results->ListingID,
				'url' 					=> site_url( '/' . $_SERVER['REQUEST_URI'] ),
				'street_address'		=> $address,
				'city' 					=> $results->City,
				'state' 				=> $results->State
			)
		);
		
		$this->liondesk_request( $data );
		
	}
	
	/**
	 * LionDesk Request
	 *
	 * Make the API request to LionDesk. 
	 *
	 * @link https://api-v1.liondesk.com/docs.html
	 *
	 * @since 1.0.21
	 *
	 * @param  array  $data     The data for a the resuest.
	 * @return string $response A json sting including the error code and contact ID.
	 */
	
	private function liondesk_request( $data ) {
		
		$APIKEY = 'user';
		$USERKEY = get_option( 'liondesk_api_key' );
		
		$url = 'https://api-v1.liondesk.com/';
		
		$content = json_encode( $data );
		
		$ch = curl_init();
		curl_setopt( $ch, CURLOPT_URL, $url );
		curl_setopt( $ch, CURLOPT_POST, TRUE );
		curl_setopt( $ch, CURLOPT_POSTFIELDS, $content );
		curl_setopt( $ch, CURLOPT_USERPWD, $APIKEY . ":" . "" );  
		curl_setopt( $ch, CURLOPT_HTTPHEADER, array(
			'Content-Type: application/json',
			'X-LionDesk-Id: ' . $USERKEY
		));
		curl_setopt( $ch, CURLOPT_RETURNTRANSFER, TRUE );
		$response = curl_exec( $ch );
		
		curl_close( $ch );
		
		return $response;
		
	}

}
