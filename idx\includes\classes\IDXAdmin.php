<?php

/**
 * Include the file with the base class providing DB access and configuration file handling
 */
require_once 'IDXBase.php';

/**
 * Functions for the IDX Admin Control Panel
 */
class IDXAdmin extends IDXBase
{

	/**
	 * Display the form for the domain and the FTP settings
	 */
	private function renderFTPform() {

		$fields = array(
			'ftp_addr' => 'Hostname',
			'ftp_user' => 'Username',
			'ftp_pass' => 'Password'
		)
		?>
	        <h3>Domain Name & FTP Settings</h3><br />

			<?php $this->_formTable(array('domain' => 'Domain')); ?>

			<h4>FTP Settings</h4>
			<?php $this->_formTable($fields); ?>
		<?php
	}

	public function renderForm($type) {

		$id = $_GET['aid'];
		$sql = $this->db->quoteInto("SELECT * FROM access LEFT JOIN access_meta USING (access_id) WHERE access_id = ?", $id);
		$this->settings = $access_array = $this->db->fetchRow($sql);

		if ($id) {
			$header = $access_array['access_company'];
		} else {
			$header = "Add Customer";
		}
		?>
		<script type="text/javascript">
			/*
			$("#data-form").validate({
				rules: { access_company: { required: true } },
				messages: { access_company: { required: "Enter a company name" } },
				submitHandler: saveChanges
			});
			*/
		</script>

		<form id="data-form" onSubmit="return saveChanges(this);">
			<input type="hidden" name="aid" value="<?= $_GET['aid'] ?>" />
			<input type="hidden" name="type" value="<?= $type ?>" />
			<h2><?= $header ?></h2>

			<?php
			switch ($type) {
				case 'ftp':
					$this->renderFTPform($id);
					break;
				case 'access':
					$this->renderMainForm($id);
					break;
				case 'cat':
				case 'result':
				case 'prop':
					$this->renderSEOForm($type);
			}
			?>
			<div id="form-buttons">
				<input id="save-btn" type="submit" class="button" value="Save Changes"/>
			</div>
		</form><?
	}


	/**
	 * @see http://stackoverflow.com/questions/4356289/php-random-string-generator
	 * @param int $length
	 * @return string
	 */
	private function generateRandomString() {
	    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	    $randomString = '';
	    for ($i = 0; $i < 15; $i++) {
	        $randomString .= $characters[rand(0, strlen($characters) - 1)];
	    }
	    return $randomString;
	}

	public function renderMainForm($id) {
		// Get the world items
		require_once("class-world_db.php");
		$world_obj = new world();
		$states_array = $world_obj->get_states_assoc();

		// TODO: kind of a hack so that we can access array fields that don't exist without a NOTICE
		error_reporting(~ E_NOTICE);
		if ($id == 0) {
			$this->settings['access_apikey'] = $this->generateRandomString();
		}

		$fields = array(
			'access_company' => 'Company Name',
			'access_account_id' => 'Account ID',
			'access_apikey' => 'API Key',
			'access_member_id' => 'MLS Member ID',
			'access_office_id' => 'MLS Office ID',
		);

		$rest_fields = array(
			'access_domain' => 'Domain',
			'access_ip' => 'IP Address'
		);
		$addr = array(
			'access_fullname' => 'Customer Name',
			'access_address' => 'Address',
			'access_address2' => '',
			'access_city' => 'City',
		);
		$more = array(
			'access_zipcode' => 'Zip Code',
			'access_phone' => 'Phone',
			'access_emailaddress' => 'Email',
		);
		$access_status_array = array( array('status_var'=>'1', 'status_name'=>'Active'),
			array('status_var'=>'0', 'status_name'=>'Inactive')
		);

		# FIXME: create a function for select boxes - or just switch to PHP framework with form support

		?>
		<?php $this->_formTable($fields); ?>

			<? /*
			<table class="form_table">
                      <tr>
                        <td class="form_l_col">&nbsp;</td>
                        <td class="form_r_col"><div id="strict_button" class="caption">[ <a href="#restrictions" class="caption">Restrictions</a> ]</div></td>
                      </tr>

                      <tr>
                          <td colspan="2">
                              <div id="strict_form" style="display:none;">


                                  <table class="form_table" cellspacing="0">
                                      <tr>
                                          <td class="form_l_col">&nbsp;</td>
                                          <td class="form_r_col"><div class="caption">Optional restrictions control responses from the idx.<br>Leave them blank to allow any domain<br>and/or any IP used for this account.</div></td>
                                      </tr>

	                                  <?php $this->_formTable($rest_fields); ?>
                                  </table>
                              </div>
                          </td>
                      </tr>
	            </table>
            */ ?>

			<?php $this->_formTable($addr); ?>

			<table class="form_table">
			<tr>
				<td class="form_l_col">State:</td>
				<td class="form_r_col">
					<select name="access_state" id="access_state" class="txt">
						<? foreach ($states_array as $sresult): ?>
						<option value="<?=$sresult['name'];?>"<?=($this->settings['access_state'] == $sresult['name'])? ' selected="selected"' : '';?>><?=$sresult['name'];?></option>
						<? endforeach ?>
						</select>
				</td>
            </tr>
			<tr>
				<td class="form_l_col">Hide UCB Listings:</td>
				<td class="form_r_col">
					<input type="checkbox" name="hide_ucb" <? if ($this->settings['hide_ucb']) echo 'checked' ?>>
				</td>
			</tr>
			</table>

			<?php $this->_formTable($more); ?>

			<table class="form_table">
			<tr>
				<td class="form_l_col">Status:</td>
				<td class="form_r_col">
					<select name="access_status" id="access_status" style="width:80px;"  class="txt">
							<?
								foreach ($access_status_array as $sresult){ ?>
							<option value="<?=$sresult['status_var'];?>"<?=($this->settings['access_status'] == $sresult['status_var'])? ' selected="selected"' : '';?>><?=$sresult['status_name'];?></option>
							<? }
							 ?>
						</select></td>
                      </tr>

				<? # TODO: Get this list of MLSes from the config.ini? ?>
	                  <tr>
		                  <td class="form_l_col">MLS:</td>
		                  <td class="form_r_col">
			                  <select name="mls" id="mls_<?=$this->settings['id'] ?>" class="txt">
				                  <option value="armls"    <?= $this->settings['mls'] == 'armls' ? 'selected' : ''?>>Arizona</option>
				                  <option value="trendmls" <?= $this->settings['mls'] == 'trendmls' ? 'selected' : '' ?>>Philadelphia</option>
				                  <option value="brightmls" <?= $this->settings['mls'] == 'brightmls' ? 'selected' : '' ?>>Philadelphia</option>
				                  <option value="sndmls" <?= $this->settings['mls'] == 'sndmls' ? 'selected' : '' ?>>Sandicor (TEMPO/FUSION)</option>
				                  <option value="paaraz" <?= $this->settings['mls'] == 'paaraz' ? 'selected' : '' ?>>PAAR (Prescott)</option>
				                  <option value="sdcrca" <?= $this->settings['mls'] == 'sdcrca' ? 'selected' : '' ?>>Sandicor (Paragon)</option>
				                  <option value="glvarnv" <?= $this->settings['mls'] == 'glvarnv' ? 'selected' : '' ?>>GLVAR</option>
				                  <option value="tarmlsaz" <?= $this->settings['mls'] == 'tarmlsaz' ? 'selected' : '' ?>>TARMLS</option>
			                  </select>
		                  </td>
	                  </tr>
		    </table>
		<?
	}

	/**
	 * Render a table of text input fields
	 *
	 * @param $fields Array Key/Value array of DB field names and labels
	 */
	private function _formTable($fields) {
		?>
        <table class="form_table" cellspacing="0">
	        <?php foreach ($fields as $name => $label): ?>
			<tr>
				<td class="form_l_col"><?= $label ? $label . ":" : '' ?></td>
				<td class="form_r_col">
					<input type="text" name="<?= $name ?>" class="<?= $name ?>" value="<?= $this->settings[$name];?>" />
				</td>
			</tr>
	        <?php endforeach ?>
        </table>
		<?php
	}

	/**
	 * Save the form data to the database - No Validation right now
	 * @return array
	 */
	public function saveFormData() {
		$data = $_POST;
		$newrow = false;
		$table = (in_array($data['type'], array('access', 'ftp'))) ? 'access' : 'access_meta';

		$rmcols = array('aid', 'type', 'prop_opt_to');
		foreach ($rmcols as $colname) {
			unset($data[$colname]);
		}

		// FIXME: should switch to using a proper PHP framework for all this at some point.  Ugh.

		# FIXME: merge the access and access_meta tables together.  they should not be split in to two tables.
		// Check to see if a row exists for the meta table
		if ($table == 'access_meta') {
			$sql = $this->db->quoteInto("SELECT 1 FROM access_meta WHERE access_id = ?", $_POST['aid']);
			if (!$this->db->fetchOne($sql)) {
				$newrow = true;
				$data['access_id'] = $_POST['aid'];
			}
		}

		// Fix up values for boolean fields for 'Edit' popup
		if (isset($data['access_company'])) {
			$data['hide_ucb'] = (isset($data['hide_ucb']) && $data['hide_ucb'] == 'on') ? 1 : 0;
		}

		try {
			if ($_POST['aid'] == 0 || $newrow) {
				$this->db->insert($table, $data);
			} else {
				$where = $this->db->quoteInto("access_id = ?", $_POST['aid']);
				$this->db->update($table, $data, $where);
			}
		} catch (Exception $e) {
			return array('success' => false, 'message' => $e->getMessage());
		}
		return array('success' => true);

	}

	/**
	 * Get the domains and FTP information for all of the accounts
	 */
	public function getHosts($access_id = null) {
		$q = "
			SELECT
				access_id, access_company, domain, ftp_addr, ftp_user, ftp_pass
			FROM access
			WHERE
				domain <> '' AND
				ftp_user <> '' AND
				ftp_pass <> ''
		";
		$this->db->setFetchMode(Zend_Db::FETCH_OBJ);

		if ($access_id) {
			# TODO: fix SQL injection security hole
			$q .= " AND access_id = $access_id";
			return $this->db->fetchRow($q);
		} else {
			return $this->db->fetchAll($q);
		}
	}

	private function sendErr($msg) {
		return array('result' => false, 'id' => $this->id, 'msg' => $msg);
	}

	/**
	 * Deploy the plugin via FTP to a specific site
	 *
	 * @param $id ID of the customer/site in the database
	 * @return array Response to be sent as JSON to browser
	 */
	public function deployPlugin($id) {
		# TODO: this is kind of hacky to avoid passing an extra param
		$this->id = $id;

		$host = $this->getHosts($id);
		if (!$host) {
			return $this->sendErr("Could not get domain");
		}

		// If no FTP host was specified, use the domain name with "ftp" subdomain
		$hostname = $host->ftp_addr ? $host->ftp_addr : "ftp." . $host->domain;

		$dir = "public_html/wp-content/plugins";
		$cmd = sprintf("/usr/bin/ncftpput -R -u %s -p '%s' %s $dir profoundmls 2>&1",
			$host->ftp_user, $host->ftp_pass, $hostname);

		// Change in to the plugin source directory
		$ok = @chdir(dirname(__FILE__) . '/../../../build/source');
		if (!$ok) {
			return $this->sendErr("Could not change to source directory\n");
		}

		exec($cmd, $output, $retval);

		if ($retval) {
			return $this->sendErr($output);
		}

		# TODO: check that this is successful
		// Force an update from the IDX of the options
		file_get_contents("http://" . $host->domain . "/?pfmls_idx_update&pfmls_idx_enable");

		return array('result' => true, 'id' => $id, 'msg' => 'OK');
	}

	public function addSEOFields($fields) {
		foreach ($fields as $id => $name) {
			?>
			<tr>
                <td class="form_l_col" valign="top"><?= $name ?><br>
					<input name="prop_opt_to" class="prop_opt_to" type="radio" value="<?= $id ?>">
                </td>
				<td class="form_r_col">
					<textarea name="<?= $id ?>" class="txt_req" id="<?= $id ?>"><?= $this->settings[$id] ?></textarea>
				</td>
            </tr>
			<?
		}
	}

	public function renderSEOForm($type) {
		$fields = array(
			'prop' => array(
				'title' => 'Property Detail Page',
				'fields' => array(
					'meta_prop_url' => 'URL',
					'meta_prop_title' => 'Page Title',
					'meta_prop_h1' => 'Page H1',
					'meta_prop_description' => 'Description META',
					'meta_prop_keywords' => 'Keywords META',
				)
			),

			'cat' => array(
				'title' => 'Category/Neighborhood Page',
				'fields' => array(
					'meta_cat_title' => 'Page Title',
					'meta_cat_h1' => 'Page H1',
					'meta_cat_description' => 'Description META',
					'meta_cat_keywords' => 'Keywords META',
				)
			),

			'result' => array(
				'title' => 'Search Results Detail Page',
				'fields' => array(
					'meta_result_title' => 'Page Title',
					'meta_result_h1' => 'Page H1',
					'meta_result_description' => 'Description META',
					'meta_result_prop_h2' => 'Property H2',
					'meta_result_prop_content' => 'Property Content',
				)
			)
		);
		?>
			<h3><?= $fields[$type]['title'] ?> SEO Map</h3><br />
			<table class="form_table">
				<?
				$this->addSEOFields($fields[$type]['fields']);
				$this->addFieldSelectionBox();
				?>
			</table>
		<?
	}

	# FIXME: this is a temporary function so that we can update the SEO mappings in the DB for old records
	public function updateSEOMappings() {
		$this->access_array = array('mls' => 'armls');
		$map = array_flip((array) $this->getFieldMapping());

		# TODO: no anonymous functions with PHP 5.2 :-(
		#$regex = array_map(function ($key) { return "/{" . $key ."}/"; }, array_keys($map));
		#$replace = array_map(function ($key) { return "{" . $key . "}";}, array_values($map));
		$regex = $replace = array();
		foreach (array_keys($map) as $key) { $regex[] = "/{" . $key . "}/"; }
		foreach (array_values($map) as $key) { $replace[] = "{" . $key . "}"; }

		$cnt = 0;
		$accounts = $this->db->fetchAll("SELECT * FROM access_meta");
		foreach ($accounts as $acct) {
			foreach ($acct as $key => $val) {
				if (in_array($key, array('meta_id', 'access_id'))) continue;

				$val = preg_replace($regex, $replace, $val);
				$this->db->update('access_meta', array($key => $val), "meta_id = " . $acct['meta_id']);
			}
			$cnt++;
		}
		echo "$cnt accounts updated\n";
	}

	public function addFieldSelectionBox() {
		$sql = "SELECT MapName FROM field_mapping WHERE " . $this->settings['mls'] . " <> '' ORDER BY MapName";
		$fnames = $this->db->fetchCol($sql);

		?>
		<tr>
			<td class="form_l_col">Fields:</td>
			<td class="form_r_col">
				<select id="field_prop_opts" class="txt">
					<option value=""> -- Select Field to Insert -- </option>
					<? foreach ($fnames as $fname): ?>
					<option value="{<?= $fname ?>}"><?= $fname ?></option>
					<? endforeach ?>
				</select>
			</td>
		</tr>
		<?
	}
}
