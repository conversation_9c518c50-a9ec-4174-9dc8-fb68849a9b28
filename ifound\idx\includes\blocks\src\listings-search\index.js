import { registerBlockType } from '@wordpress/blocks';
import { __ } from '@wordpress/i18n';
import Edit from './edit';
import { fromShortcode } from '../lib/transform';
import shortcode from '@wordpress/shortcode';

import './style.scss';

registerBlockType( 'ifound/listings-search', {
	title: __( 'iFound Listings Search', 'ifound' ),
	description: __(
		'Add a Listings Search into your post (known as The iFound Shortcode Creator in the classic WP editor)',
		'ifound'
	),
	category: 'embed',
	icon: {
		src: <img src="/wp-content/plugins/ifound/idx/admin/images/ifound-shortcode.png" style={{ width: '100%' }} />
	},
	supports: {
		// Removes support for an HTML mode.
		html: false,
	},
	attributes: {
		id: {
			// Reminder: only specify a type because we don't need a save function. We want wordpress to save
			// (serialize) the attributes only.
			// We use a string (instead of a number) for backward compatability with the ifound shortcode.
			type: 'string',
		},
	},
	transforms: {
		from: [{
			// This transform is used when the block is 'classic', and the user uses the triple dots and clicks "Convert
			// to blocks".
			type: 'shortcode',
			tag: 'ifound',
			attributes: {
				id: {
					type: 'string',
					shortcode({ named }) {
						return named.id;
					},
				},
			},
			isMatch(attributes) {
				// We decided that shortcode with polygon_id is meant to be edited by staff only and we won't spend
				// time making them play nicely with blocks.
				return !('polygon_id' in attributes.named);
			},
		}, {
			...fromShortcode('ifound', 'ifound/listings-search'),
			isMatch(attributes) {
				const obj = shortcode.next('ifound', attributes.text);
				// We decided that shortcode with polygon_id is meant to be edited by staff only and we won't spend
				// time making them play nicely with blocks.
				return obj && obj.shortcode.tag === 'ifound' && !('polygon_id' in obj.shortcode.attrs.named);
			},
		}],
	},
	edit: Edit,
	// We don't need a save function. We'll use the built-in save which returns null, which means save the attributes
	// in JSON format.
	// Update: When deploying to older WP versions like what's on getprequalified.com right now (5.1.8), you'll see
	// this error if you don't specify the save function:
	// The "save" property must be specified and must be a valid function
	save: () => null,
} );
