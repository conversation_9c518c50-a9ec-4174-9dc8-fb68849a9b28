<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * Features Class
 *
 * @since 2.7.0
 */

class iFoundFeatures {
  use UtilTrait;

  private $features;

  public static function init() {
        $class = __CLASS__;
        new $class;
    }

  /**
   * Constructor
   *
   * @since 2.7.0
   */

  public function __construct()  {

    $this->features = $this->features();
    $this->register_classes();

    add_action( 'rest_api_init', array( $this, 'features_route' ) );
    add_action( 'ifound_feature_log', array( $this, 'feature_log' ), 10, 2 );

    add_filter( 'ifound_has_feature', array( $this, 'has_feature' ), 10, 1 );
    add_action( 'ifound_warn_feature', array( $this, 'warn_feature' ), 10, 1);

  }

  public function features() {
    $config = iFound::new_hookless()->get_config();
    if (isset($config['allowed_features'])) {
        return explode('|', $config['allowed_features']);
    }

    return get_option( 'ifound_allowed_features' ) ?: array();

  }

  public function has_feature($slug) {
    $features = $this->features();
    return in_array($slug, $features, true);
  }

	public function warn_feature($slug) {
		$features = [
			'bulk-campaigns' => 'Bulk Campaigns',
			'drip-campaigns' => 'Drip Campaigns',
		];
		?>
		<div class="notice notice-error">
            <div>You do not have the feature <strong><?=$features[$slug]?></strong></div>
		</div>
		<?php
	}

	public function register_classes() {

		$features = [
			'load'           => [
				'iFound'                => [],
				'iFoundActionScheduler' => [],
				'iFoundAddress'         => [],
				'iFoundGeo'             => [],
				'iFoundLogin'           => [],
				'iFoundMergeTags'       => [],
				'iFoundUtil'            => [],
				'iFoundGravityForms'    => [],
			],
			'idx'            => [
				'iFoundIdx'                => [],
				'iFoundMap'                => [],
				'iFoundListen'             => [],
				'iFoundSeo'                => [],
				'iFoundResults'            => [],
				'iFoundSearch'             => [],
				'iFoundSearchController'   => [],
				'iFoundShortcode'          => [],
				'iFoundFeaturedListings'   => [],
				'iFoundSidebars'           => [
					'hook' => 'widgets_init',
				],
				'iFoundBlogdog'            => [],
				'iFoundProfound'           => [],
				'iFoundAdmin'              => [
					'condition' => is_admin(),
				],
				'iFoundIdxAdminController' => [],
				'iFoundCreateShortcode'    => [
					'condition' => is_admin(),
				],
				'iFoundPolygonDrawer'      => [],
				'iFoundSocialMediaAdmin'   => [
					'condition' => is_admin(),
				],
				'iFoundQuickSearchAdmin'   => [
					'condition' => is_admin(),
				],
			],
			'cmc'            => [
				'iFoundCmc'      => [],
				'iFoundCmcAdmin' => [
					'condition' => is_admin(),
				],
			],
			'crm'            => [
				'iFoundCrm'                          => [],
				'iFoundCrmAdmin'                     => [
					'condition' => is_admin(),
				],
				'iFoundCrmLogin'                     => [],
				'iFoundContacts'                     => [],
				'iFoundRegistration'                 => [],
				'iFoundSaveThis'                     => [],
				'iFoundSaveThisUpgrader'             => [],
				'iFoundSharedCampaign'               => [],
				'iFoundEmail'                        => [],
				'iFoundEmailTrackingPixel'           => [],
				'iFoundActivity'                     => [],
				'iFoundNotes'                        => [],
				'iFoundProcessAlerts'                => [],
				'iFoundCampaignBuilder'              => [
					'condition' => is_admin(),
				],
				'iFoundSearchTemplate'               => [
					'condition' => is_admin(),
				],
				'iFoundContactsAdmin'                => [],
				'iFoundSaveThisAdmin'                => [
					'condition' => is_admin(),
				],
				'iFoundBulkImport'                   => [
					'condition' => is_admin(),
				],
				'iFoundBulkEmail'                    => [
					'condition' => is_admin(),
				],
				'iFoundHomeownerCampaign'            => [],
				'iFoundHomeownerCampaignsController' => [],
				'iFoundLionDesk'                     => [
					'condition' => get_option('liondesk_api_key'),
				],
				'iFoundImax'                         => [
					'condition' => get_option('imax_api_key'),
				],
				'iFoundMlsContacts'                  => [],
				'iFoundJointContact'                 => [],
				'iFoundDripCampaignShared'           => [],
				'iFoundBulkDripCampaigns'            => [],
			],
			'drip-campaigns' => [
				'iFoundDripCampaign'        => [],
				'iFoundDripCampaignBuilder' => [
					'condition' => is_admin(),
				],
				'iFoundDripTemplate'        => [],
				'iFoundDripCampaignAdmin'   => [],
			],
			'sms' => [
				'iFoundSms'           => [],
				'iFoundSmsController' => [],
			],
			'teams'          => [
				'iFoundTeams'          => [],
				'iFoundPrivateContact' => [
					'condition' => function($state) {
						return $state['is_crm_enabled'];
					},
				],
			],
			'aerial-sphere'  => [
				'iFoundAerialSphere' => [],
			],
		];

		$features = apply_filters('ifound_features_register_classes', $features);

		foreach ($features as $feature => $classes) {

			if (in_array($feature, $this->features) || $feature == 'load') {
				// Hide CRM stuff (menus, etc) if teams are enabled but the user does not have CRM enabled.
				$should_enable_crm = function() {
					$user_id = is_admin() ? get_current_user_id() : $_REQUEST['aid'];
					if ($user_id) {
						$teams = iFoundTeams::new_hookless();
						if ($teams->user_has_team_member_role(['id' => $user_id])) {
							return $teams->has_crm_enabled(['id' => $user_id]);
						}
					}
					return true;
				};
				if (in_array($feature, ['crm', 'drip-campaigns'], true)) {
					if (!$should_enable_crm()) {
			            continue;
			        }
				}

				foreach ($classes as $class => $values) {
					$condition_value = isset($values['condition'])
						? is_callable($values['condition'])
							? $values['condition']([
								'is_crm_enabled' => $should_enable_crm(),
								])
							: $values['condition']
						: true;

					if ($condition_value) {
						$hook = isset($values['hook']) ? $values['hook'] : 'plugins_loaded';

						/** Do not check if class exists here since not all the scripts are included when this runs. */
						add_action($hook, array($class, 'init'));

					}

				}

			}

		}

  }

  /**
   * Register Routes
   *
   * Register the REST API Route. We handle all validation here. If any param is not valid. The whole thing dies.
   *
   * @since 2.7.0
   *
   * @see https://developer.wordpress.org/rest-api/extending-the-rest-api/adding-custom-endpoints/
   */

  public function features_route() {

    register_rest_route(
      'ifound/1.0.0/',
      'features/',
      array(
        'methods'  => WP_REST_Server::EDITABLE,
        'callback' => array( $this, 'process_features' ),
      )
    );

  }

  public function process_features() {

    if( $this->validate() ) {

      $previous_features = get_option('ifound_allowed_features');
      $new_features = (array) $_POST['features'];
      $clean_new_features =  array_filter( $new_features, 'sanitize_text_field' );

      update_option( 'ifound_allowed_features', $clean_new_features );

      if (
          in_array('drip-campaigns', $previous_features, true)
          && !in_array('drip-campaigns', $clean_new_features, true)
      ) {
	      iFoundDripCampaign::new_hookless()->disable_feature();
	  }
    }

  }

  public function validate() {

    if( $api_settings = get_option( 'ifound_api_settings' ) ) {

      if( $api_settings['api_secret'] == $this->request_api_key() ) {

        return true;

      }

    }

    return false;

  }

  public function request_api_key() {
    return sanitize_text_field( $_SERVER['HTTP_AUTHENTICATION'] );
  }

  /**
   * Feature Log
   *
   * Log usage of features.
   *
   * @since 3.5.0
   *
   * @param int $feature_id The admin ID of the feature.
   */

  public function feature_log( $feature_id, $action_id ) {

    wp_remote_post(
      join( '/', array(
        'https://feature-log.ifoundadmin.com',
        'wp-json',
        'ifound-admin',
        '1.0.0',
        'log-use',
        $feature_id,
        $action_id . '/'
      )),
      array(
        'blocking' => false,
        'headers' => array(
            'Accept'  => 'application/json',
          'Referer'   => iFound::current_url()
        )
      )
    );

  }

}
