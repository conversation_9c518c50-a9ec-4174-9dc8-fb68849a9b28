.ifound {
	table {
		&.striped {
			border-collapse: collapse;
			width: 100%;

			tbody {
				tr:nth-child(odd) {
					background-color: #eee;
				}

				td {
					padding: 5px;
				}
			}
		}

		// Sticky headers. I got this from https://stackoverflow.com/a/44004100/135101, first example. Note that
		// the example they give uses a CLASS of table (.table).
		thead.sticky tr:first-child th{
			background: white;
			position: sticky;
			top: 0;
			z-index: 10;
		}
	}

	// This is a technique called the lobotomized owl. It creates margin between all elements of some container.
	.owl > * + * {
		margin-top: 10px;
	}
	// For when you want the first item to have margin too.
	.owl-plus > * {
		margin-top: 10px;
	}

	.h-owl {
		display: flex;
		align-items: baseline;

		& > * + * {
			margin-left: 10px;
		}
	}

	.full-cover {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
