<?php

namespace Profound\MLS;

require_once 'vendor/autoload.php';
require_once __DIR__ . '/../TestHelper.php';

use \PHPUnit_Framework_TestCase;
use \Profound\TestHelper;

class MlsTest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testCreateAbstractFactory() {
		$factory = Mls::create('armls');
		$this->assertInstanceOf('Profound\MLS\Armls', $factory);
	}

	public function testGetAbstractFactoryCreation() {
		$factory = Mls::create('armls', array('db' => TestHelper::getDb()));
		$image_handler = $factory->getImageHandler();
		$this->assertInstanceOf('Profound\MLS\ArmlsImageHandler', $image_handler);
	}
}