-- Note that we don't need to touch the "_images" tables because they've already been altered by previous cases.


-- Listing ID
create index field_LIST_105 on paaraz_property_B (field_LIST_105);
-- City
alter table paaraz_property_B change column field_LIST_39 field_LIST_39 varchar(100);
create index field_LIST_39 on paaraz_property_B (field_LIST_39);
-- Contingent status
alter table paaraz_property_B change column field_LIST_19 field_LIST_19 varchar(100);
create index field_LIST_19 on paaraz_property_B (field_LIST_19);
-- The original column type is string! Change to a numerical format to allow normal numerical sorting.
alter table paaraz_property_B change column field_LIST_22 field_LIST_22 decimal(15,0);
-- Subdivision
create index field_LIST_77 on paaraz_property_B (field_LIST_77);
-- Lot Acres
alter table paaraz_property_B change column field_LIST_57 field_LIST_57 decimal(10,0);

-- Listing ID
create index field_LIST_105 on paaraz_property_F (field_LIST_105);
-- City
alter table paaraz_property_F change column field_LIST_39 field_LIST_39 varchar(100);
create index field_LIST_39 on paaraz_property_F (field_LIST_39);
-- Contingent status
alter table paaraz_property_F change column field_LIST_19 field_LIST_19 varchar(100);
create index field_LIST_19 on paaraz_property_F (field_LIST_19);
-- The original column type is string! Change to a numerical format to allow normal numerical sorting.
alter table paaraz_property_F change column field_LIST_22 field_LIST_22 decimal(15,0);
-- Subdivision
create index field_LIST_77 on paaraz_property_F (field_LIST_77);
-- Square feet
alter table paaraz_property_F change column field_LIST_48 field_LIST_48 int;
-- Bathrooms
alter table paaraz_property_F change column field_LIST_67 field_LIST_67 smallint;

-- Listing ID
create index field_LIST_105 on tarmlsaz_property_B (field_LIST_105);
-- City
alter table tarmlsaz_property_B change column field_LIST_39 field_LIST_39 varchar(100);
create index field_LIST_39 on tarmlsaz_property_B (field_LIST_39);
-- Contingent status
alter table tarmlsaz_property_B change column field_LIST_19 field_LIST_19 varchar(100);
create index field_LIST_19 on tarmlsaz_property_B (field_LIST_19);
-- The original column type is string! Change to a numerical format to allow normal numerical sorting.
alter table tarmlsaz_property_B change column field_LIST_22 field_LIST_22 decimal(15,0);
-- Subdivision
create index field_LIST_77 on tarmlsaz_property_B (field_LIST_77);
-- Lot Acres
alter table tarmlsaz_property_B change column field_LIST_57 field_LIST_57 decimal(10,0);

-- Listing ID
create index field_LIST_105 on tarmlsaz_property_E (field_LIST_105);
-- City
alter table tarmlsaz_property_E change column field_LIST_39 field_LIST_39 varchar(100);
create index field_LIST_39 on tarmlsaz_property_E (field_LIST_39);
-- Contingent status
alter table tarmlsaz_property_E change column field_LIST_19 field_LIST_19 varchar(100);
create index field_LIST_19 on tarmlsaz_property_E (field_LIST_19);
-- The original column type is string! Change to a numerical format to allow normal numerical sorting.
alter table tarmlsaz_property_E change column field_LIST_22 field_LIST_22 decimal(15,0);
-- Subdivision
create index field_LIST_77 on tarmlsaz_property_E (field_LIST_77);
-- Square feet
alter table tarmlsaz_property_E change column field_LIST_48 field_LIST_48 int;
-- Bathrooms
alter table tarmlsaz_property_E change column field_LIST_67 field_LIST_67 smallint;


