<?
/**
 * iFOUND_quick_search class
 *
 * Display featured listings in widget areas.
 *
 * @package iFOUND
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );


class iFound_featured_listings extends WP_Widget {

	public function __construct(){

		parent::__construct(
			false,
			'iFound Featured Listings',
			array(
			'description' => 'Add Featured Listings to any widget area.'
		));

	}

	/**
	 * Widget
	 *
	 * @since 1.0.0
	 */

	public function widget( $args, $instance ) {

		/**
		 * Featured Listings Slider Script
		 *
		 * @since 1.0.0
		 */

		add_action( 'wp_footer', array( $this, 'featured_listings_slider_script' ), 999 );

		$featured = get_option( 'ifound_featured_settings' );

		if( empty( $featured['featured_query'] ) ) return;

		$featured = iFoundFeaturedListings::new_hookless()->maybe_fix_queries($featured);

		echo $args['before_widget']; ?>

		<div class="featured-listings">

			<div class="widget-wrap"><?

				$title = $featured['featured_widget_title'] ?: 'Featured Listings'; ?>

				<h3 class="widget-title ifound-widget-title"><? _e( $title, 'ifound' ); ?></h3>


				<div class="ifound-featured-listings ifound-featured-slider <? echo $featured['featured_class']; ?>">

					<? do_action( 'ifound_featured_listings', $featured ); ?>

				</div>

			</div>

		</div><?

		echo $args['after_widget'];

	}

	/**
	 * Featured Listings Slider Script
	 *
	 * @since 1.0.0
	 */

	public function featured_listings_slider_script() {

		$featured = get_option( 'ifound_featured_settings' );

		if( empty( $featured['featured_slider_script'] ) ) return; ?>

		<script>

			jQuery( document ).ready( function( $ ) {

				<? echo $featured['featured_slider_script']; ?>

			});

		</script><?

	}

}
