<?

require_once(__DIR__ . '/class-ifound-contacts.php');
require_once(__DIR__ . '/../teams/includes/class-ifound-private-contact.php');

defined( 'ABSPATH' ) or die( 'You do not have access!' );

// This class is for shared functionality for contacts and private contacts. To be clear, the term joint has nothing to
// do with a contact belonging to multiple people.
class iFoundJointContact {
	use UtilTrait;
	use NewHooklessTrait;

	private $contacts_being_deleted_by_id = [];

	public static $all_contact_post_types;
	public static $all_contact_post_types_quoted_str_for_db;

	public static $EMAIL_FIELD_NAMES = ['email', 'email2', 'email_spouse', 'email2_spouse'];
	public static $MPHONE_FIELD_NAMES = ['mphone', 'mphone_spouse'];

	// This is the same type of situation as NOTE_279085.
	private static $is_initial_insert_with_previous_relationship_term = false;

	// PHP can't parse non-trivial expressions in initializers.
	// Here's a way around it. Note this is called at the end of the file.
	// Idea from: https://stackoverflow.com/a/693799/135101
	public static function static_init() {
		static::$all_contact_post_types = [
			iFoundContacts::$the_post_type,
			iFoundPrivateContact::$the_post_type,
		];

		$quoted_contact_types = array_map(function($x) { return "'" . $x . "'"; }, static::$all_contact_post_types);
		static::$all_contact_post_types_quoted_str_for_db = join(',', $quoted_contact_types);
	}

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []){
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			// Note: we use page_row_actions instead of post_row_actions because our CPT is hierarchical. I don't know
			// if it needs to be, but it was marked as such 6 years ago at this point.
			add_filter('page_row_actions', [$this, 'row_actions'], 10, 2);
			add_action('add_meta_boxes', [$this, 'add_start_engaging_metabox']);
			add_filter('parse_query', array($this, 'parse_query'));
			add_action('restrict_manage_posts', [$this, 'add_tag_filter'], 10, 2);
			add_action('added_term_relationship', [$this, 'added_term_relationship_hook'], 10, 3);
			add_action('deleted_term_relationships', [$this, 'deleted_term_relationships_hook'], 10, 3);
			add_action('before_delete_post', [$this, 'before_delete_post_hook'], 10, 2);
			add_action('deleted_post', [$this, 'deleted_post_hook'], 10, 2);
			add_action('wp_insert_post', [$this, 'wp_insert_post_hook'], 10, 3);
		}
	}

	public function wp_insert_post_hook($post_id, $post, $update) {
		if (!$this->is_post_type_a_contact($post->post_type)) {
			return;
		}
		if (static::$is_initial_insert_with_previous_relationship_term) {
			$this->add_opt_in_record('user_opt_in', $post_id);
		}
	}

	public function add_start_engaging_metabox() {
		global $post, $typenow;
		if (in_array($post->post_type, static::$all_contact_post_types)) {
			add_meta_box(
				'start_campaign_metabox',
				__( 'Start Engaging', 'ifound' ),
				array( $this, 'start_engaging_metabox'),
				$typenow,
				'side',
				'default'
			);
		}
	}

	public function start_engaging_metabox() {
		global $post;
		$search_campaign_url = iFoundCampaignBuilder::new_hookless()->make_start_campaign_url($post);
		$drip_campaign_url = iFoundCampaignBuilder::new_hookless()
			->make_start_campaign_url($post, iFoundDripCampaignBuilder::$menu_slug);
		$send_email_url = menu_page_url('bulk_email', false);
		$send_email_url = add_query_arg('contact_ids', [$post->ID], $send_email_url);
		?>
		<div>
			<a href="<?= $search_campaign_url ?>">Start a search campaign</a>
		</div>
		<?php

		if (apply_filters('ifound_has_feature', 'drip-campaigns')) {
			?>
			<div>
				<a href="<?= $drip_campaign_url ?>">Start a drip campaign</a>
			</div>
			<div>
				<a href="<?= $drip_campaign_url ?>">Set a task reminder</a>
			</div>
			<?php
		}

		?>
		<div>
			<a href="<?= $send_email_url ?>">Send email</a>
		</div>
		<?php
	}

	public function row_actions($actions, $post) {
		if (in_array($post->post_type, static::$all_contact_post_types)) {
			$url = iFoundCampaignBuilder::new_hookless()->make_start_campaign_url($post);
			$actions['search_campaign'] = "<a href=\"$url\">Search campaign</a>";

			if (apply_filters('ifound_has_feature', 'drip-campaigns')) {
				$url = iFoundCampaignBuilder::new_hookless()
					->make_start_campaign_url($post, iFoundDripCampaignBuilder::$menu_slug);
				$actions['drip_campaign'] = "<a href=\"$url\">Drip campaign</a>";
				$actions['task_reminder'] = "<a href=\"$url\">Task reminder</a>";
			}

			$url = menu_page_url('bulk_email', false);
			$url = add_query_arg('contact_ids', [$post->ID], $url);
			$actions['send_email'] = "<a href=\"$url\">Send email</a>";
		}
		return $actions;
	}

	public function get_all_contacts_args($user_id = null, $only_id_fields = false) {
		if (!$user_id) {
			$user_id = iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id();
		}
		$args = [
			'numberposts' => -1,
			'post_type' => [iFoundContacts::$the_post_type, iFoundPrivateContact::$the_post_type],
			'post_status' => 'publish',
		];
		if ($only_id_fields) {
			$args['fields'] = 'ids';
		}
		if ($this->util()->user_has_admin_or_super_role($user_id)
			&& $user_id !== iFoundAdmin::$shared_owner_ID) {
			$args['author__in'] = [iFoundAdmin::$shared_owner_ID, $user_id];
		} else {
			$args['author'] = $user_id;
		}
		return $args;
	}

	public function get_all_contacts($user_id = null, $only_id_fields = false) {
		$args = $this->get_all_contacts_args($user_id, $only_id_fields);
		$contacts = get_posts($args);
		return $contacts;
	}

	public function get_new_contact_type($user_id = null) {
		return $this->util()->is_site_admin($user_id) ? iFoundContacts::$the_post_type : iFoundPrivateContact::$the_post_type;
	}

	public function get_new_contact_author() {
		if ($this->util()->is_site_admin()) {
			return iFoundAdmin::new_hookless()->get_primary_admin_id();
		}
		return get_current_user_id();
	}

	public function get_address_fields($contact_id) {
		$vals = [];
		foreach (['address', 'address2', 'city', 'state', 'zip'] as $field_name) {
			$vals[$field_name] = get_post_meta($contact_id, $field_name, true);
		}
		return $vals;
	}

	public function get_street_name($contact_id) {
		$ifound_geo = $this->maybe_geocode_contact($contact_id);
		return iFoundGeo::new_hookless()->get_street_name($ifound_geo);
	}

	public function maybe_geocode_contact($contact_id) {
		return iFoundGeo::new_hookless()->maybe_geocode_contact($contact_id);
	}

	// Inspired by and originally copied from:
	// https://plugins.trac.wordpress.org/browser/admin-posts-list-tag-filter/trunk/admin-posts-list-tag-filter.php
	// I originally had this in ifound/idx/admin/class-ifound-admin.php, and maybe it's more appropriate there if it's
	// properly generalized, but for now, it's specific to contacts so it's here in this file.
	public function add_tag_filter($post_type, $which) {
		$tax_name = iFoundContacts::$contact_tag_taxonomy;

		// If the post type doesn't have tags, don't bother showing the select box.
		$post_type_tax = get_object_taxonomies($post_type, 'objects');
		if (is_array($post_type_tax) && !array_key_exists($tax_name, $post_type_tax)) {
			return;
		}

		// Get the current list of tags.
		$tags = get_tags([
			'type' => $post_type,
			'taxonomy' => $tax_name,
		]);
		$tags_by_parent_id = [];
		foreach ($tags as $tag) {
			if (!isset($tags_by_parent_id[$tag->parent])) {
				$tags_by_parent_id[$tag->parent] = [];
			}
			$tags_by_parent_id[$tag->parent][] = $tag;
		}

		// Set the default to "All tags".
		$selected_tag_ids = [];

		// If there are no tags, don't bother showing the select box.
		if (empty($tags)) {
			return;
		}

		wp_enqueue_style('ifound_checkbox_dropdown_css');
		wp_enqueue_script('ifound_checkbox_dropdown_js');

		// Get the currently selected tag(s), if any.
		if (array_key_exists(iFoundContacts::$generic_contact_tag_taxonomy, $_REQUEST)) {
			$requested_tags = $_REQUEST[iFoundContacts::$generic_contact_tag_taxonomy];
			// Make sure the requested tags are valid.
			if (getType($requested_tags) === 'string') {
				$requested_tags = [$requested_tags];
			}
			foreach ($tags as $tag) {
				if (in_array($tag->slug, $requested_tags, true)) {
					$selected_tag_ids[] = $tag->name;
				}
			}
		}
		// Determine if the user is searching for all selected tags, or any.
		$op_field_name = 'ifound_contact_tag_op';
		$ifound_contact_tag_op = 'AND';
		if (isset($_GET[$op_field_name])) {
			$op_value = strtoupper($_GET[$op_field_name]);
			if ($op_value === 'IN') {
				$ifound_contact_tag_op = 'IN';
			}
		}
		?>
		<div class="dropdown" data-control="checkbox-dropdown">
			<label for="tag" class="dropdown-label"><?php _e( 'Filter by tag', 'ifound' ); ?></label>
			<div class="dropdown-list">
				<div>Select <?= iFoundContacts::$contact_tag_taxonomy_plural_label ?></div>
				<div class="operator">
					<label for="ifound_contact_tag_op_AND">
						<input type="radio" name="ifound_contact_tag_op" id="ifound_contact_tag_op_AND" value="AND"
							<?= $ifound_contact_tag_op === 'AND' ? 'checked="checked"' : '' ?>
						>
						All
					</label>
					<label for="ifound_contact_tag_op_IN" style="margin-left: 10px;">
						<input type="radio" name="ifound_contact_tag_op" id="ifound_contact_tag_op_IN" value="IN"
							<?= $ifound_contact_tag_op === 'IN' ? 'checked="checked"' : '' ?>
						>
						Any
					</label>
				</div>
				<button type="button" data-toggle="check-all" class="check-all-button">
					Check All
				</button>

				<? $this->output_tags_for_parent_id($tags_by_parent_id, 0, $selected_tag_ids, 0) ?>
			</div>
		</div>
		<?php
	}

	private function output_tags_for_parent_id($tags_by_parent_id, $parent_id, $selected_tag_ids, $indent_level) {
		$tags = $tags_by_parent_id[$parent_id];
		if (!$tags) {
			return;
		}
		$margin = $indent_level * 8;
		foreach ($tags as $tag) {
			$checked_string = in_array($tag->name, $selected_tag_ids, true) ? 'checked="checked"' : '';
			?>
			<label class="dropdown-option" style="margin-left: <?= $margin ?>px">
				<input type="checkbox" name="<?= iFoundContacts::$generic_contact_tag_taxonomy ?>[]" value="<?= $tag->slug ?>" <?= $checked_string ?> />
				<?= $tag->name ?>
			</label>
			<?
			$this->output_tags_for_parent_id($tags_by_parent_id, $tag->term_id, $selected_tag_ids, $indent_level + 1);
		}
	}

	public function parse_query( $query ) {
		global $pagenow, $typenow;

		// Allow contacts to be filtered by contact tags.
		if (in_array($typenow, iFoundJointContact::$all_contact_post_types)) {
			$tax = iFoundContacts::$contact_tag_taxonomy;
			$q_vars = &$query->query_vars;
			if ($pagenow === 'edit.php'
				&& isset($q_vars['post_type'])
				&& $q_vars['post_type'] === $typenow
				&& isset($q_vars[iFoundContacts::$generic_contact_tag_taxonomy])
				&& gettype($q_vars[iFoundContacts::$generic_contact_tag_taxonomy]) === 'array'
				&& count($q_vars[iFoundContacts::$generic_contact_tag_taxonomy]) > 0
			) {
				// This article helped me.
				// https://applerinquest.com/how-to-filter-posts-by-custom-taxonomies-in-wordpress-admin-area/

				// We unset the query vars, because we don't want Wordpress to filter its default way, because the
				// default way is to use the 'IN' operator, and we potentially want 'AND'. But first we grab them.
				$terms = $q_vars[iFoundContacts::$generic_contact_tag_taxonomy];
				unset($q_vars[iFoundContacts::$generic_contact_tag_taxonomy]);

				$op_name = 'ifound_contact_tag_op';
				$operator = 'AND';
				if (isset($_GET[$op_name]) && strtoupper($_GET[$op_name]) === 'IN') {
					$operator = 'IN';
				}
				$q_vars['tax_query'][] = array(
					'taxonomy' => $tax,
					'field' => 'slug',
					'operator' => $operator,
					'terms' => $terms,
					// Dale and I were confused when we searched for a parent tag, and the results included a post
					// that had only the child tag. I guess it makes sense from a certain perspective, but we think
					// it'll confuse our users too so let's not do it that way.
					'include_children' => false,
				);
			}
		}
	}

	public function get_full_name($contact_id) {
		$first_name = get_post_meta($contact_id, 'fname', true);
		$last_name = get_post_meta($contact_id, 'lname', true);
		return $this->make_full_name([$first_name, $last_name]);
	}

	public function make_full_name($name_parts) {
		return trim(implode(' ', $name_parts));
	}

	private function is_the_tt_id_previous_relationship($tt_id, $taxonomy) {
		if ($taxonomy !== iFoundContacts::$the_taxonomy) {
			return false;
		}
		$term = get_term($tt_id, $taxonomy);
		if (!$term) {
			return false;
		}
		if ($term->slug !== iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE) {
			return false;
		}
		return true;
	}

	public function add_opt_in_record($type, $contact_id) {
		$post_meta = $this->util()->get_single_metas($contact_id, ['fname', 'lname', 'mphone', 'email']);
		if (!$post_meta['email']) {
			// If we don't have this value, it's the "initial" (not counting the draft) insert, and the postmetas
			// haven't been saved yet. We'll handle in the wp_insert_post hook.
			static::$is_initial_insert_with_previous_relationship_term = true;
			return;
		}
		$record = [
			'type' => $type,
			// The user ID will be 0 when this is called from our IDX server, but that's ok.
			'user_id' => get_current_user_id(),
			'contact_id' => $contact_id,
			'first_name' => $post_meta['fname'],
			'last_name' => $post_meta['lname'],
			'mobile_phone' => $post_meta['mphone'],
			'email' => $post_meta['email'],
		];
		iFoundSms::new_hookless()->insert_opt_ins_record($record);
	}

	public function added_term_relationship_hook($object_id, $tt_id, $taxonomy) {
		global $wpdb;
		if (!$this->is_the_tt_id_previous_relationship($tt_id, $taxonomy)) {
			return;
		}
		$this->add_opt_in_record('user_opt_in', $object_id);
	}

	public function deleted_term_relationships_hook($object_id, $tt_ids, $taxonomy) {
		global $wpdb;
		$tt_id = $this->util()->array_find($tt_ids, fn($x) => $this->is_the_tt_id_previous_relationship($x, $taxonomy));
		if (!$tt_id) {
			return;
		}
		$type = 'user_opt_out';
		if (array_key_exists($object_id, $this->contacts_being_deleted_by_id)) {
			$type = 'contact_deleted';
		} else if (iFoundContacts::$is_currently_reassigning_contact_owner) {
			$type = 'contact_reassigned';
		}
		$this->add_opt_in_record($type, $object_id);
	}

	// NOTE_279085:
	// I don't know of a better way to do this. Here's the situation. When a post is deleted, our
	// deleted_term_relationships_hook method will be called. But we want to know WHY it's running. So, we'll
	// track if it's being deleted here, and then we can tell downstream in the hook.
	public function before_delete_post_hook($post_id, $post) {
		if ($this->is_post_type_a_contact($post->post_type)) {
			$this->contacts_being_deleted_by_id[$post_id] = true;
		}
	}

	// See NOTE_279085.
	public function deleted_post_hook($post_id, $post) {
		if ($this->is_post_type_a_contact($post->post_type)) {
			unset($this->contacts_being_deleted_by_id[$post_id]);
		}
	}

	// If you want to know if it's any type of contact, meaning type = 'contacts' or 'private_contact'.
	public function is_post_type_a_contact($post_type) {
		return in_array($post_type, static::$all_contact_post_types, true);
	}

	public function get_contact_first_name_by_mphone($contact_id, $mphone) {
		$contact_mphone = get_post_meta($contact_id, 'mphone', true);
		if ($mphone === $contact_mphone) {
			return get_post_meta($contact_id, 'fname', true);
		}
		$mphone_spouse = get_post_meta($contact_id, 'mphone', true);
		if ($mphone_spouse === $contact_mphone) {
			return get_post_meta($contact_id, 'fname_spouse', true);
		}
		return null;
	}

	public function has_opted_out_of_sms($contact_id) {
		return get_post_meta($contact_id, iFoundContacts::$CONTACT_SMS_OPT_OUT_KEY, true)
			=== iFoundSms::$CONTACT_SMS_OPT_OUT_YES;
	}

	public function get_may_text_vals($contact_id) {
		return [
			'hasPreviousRelationship' => has_term(iFoundContacts::$PREVIOUS_RELATIONSHIP_KEBAB_CASE, iFoundContacts::$the_taxonomy, $contact_id),
			'hasAtLeastOneMobilePhoneNumber' => !!(get_post_meta($contact_id, 'mphone', true)
				|| get_post_meta($contact_id, 'mphone_spouse', true)),
			'hasAgentSetUpIntroTitle' => !!iFoundCrm::new_hookless()
				->get_option_for_agent('ifound_crm_settings')[iFoundCrm::$SMS_INTRO_TITLE_KEY],
			'hasOptedOut' => iFoundJointContact::new_hookless()->has_opted_out_of_sms($contact_id),
			'contactLink' => $this->util()->build_post_href($contact_id, 'edit'),
			'crmSettingsLink' => iFoundCrm::new_hookless()->get_crm_menu_admin_url(),
		];
	}

	// $type is 'email' or 'sms'
	public function add_activity_log_click($contact_id, $type, $campaign_title) {
		$type_str = '';
		if ($type === 'email') {
			$type_str = ' Email';
		} else if ($type === 'sms') {
			$type_str = ' SMS';
		}
		do_action('ifound_activity_log', $contact_id, "Clicked{$type_str} Link", $campaign_title);
	}
}

iFoundJointContact::static_init();
