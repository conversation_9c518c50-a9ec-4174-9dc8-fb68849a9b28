jQuery( document ).ready( function( $ ) {

	$( '#button-type-select' ).val( social_media_admin.button_type );

	$.each( social_media_admin.networks , function(){	
		$( '#active-social-profiles').append( socialInput( this.name, this.url, this.class ) );
	});

	$( '#active-social-profiles' ).sortable();
    $( '#active-social-profiles' ).disableSelection();
	
	$( '#button-type-select' ).on( 'change', function() {	
		$( '#social_media_admin').removeClass();
		$( '#social_media_admin').addClass( 'ifound-social-' + $( this ).val() );
	});

	$( '#button-type-select' ).val( social_media_admin.button_type );

	$( '.all-choices-wrapper .social-network-icon' ).on( 'click', function( e ) {	
		$( '#active-social-profiles').append( socialInput( this.id, $( this ).val(), $( this ).attr( 'class' ) ) );
	});
	
	$( '#sodial-media-save' ).on( 'click', function( e ) {			
		var input = $( '#social_networks_form' ).serialize();
		
		$.ajax ( {
			url : social_media_admin.endpoint,
			type : 'post', 
			data : {
				action : 'sociaL_media_save',
				input : input,
				social_media_admin_nonce : social_media_admin.nonce,
			},
			beforeSend: function() {
				$( '#save-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
			},
			success: function( response ) {
				$( '#save-spinner' ).removeClass( 'fa-spinner fa-spin' ).addClass( response );
			},
			dataType:'json'
		});
		
	});
	
	
	function socialInput( n, v, c ) {
		
		var input = '<div class="sortable chosen-social-link">'
		input += '<div class="ifound-wrap">';
		
		input += '<div class="sort-arrows-warpper">';
		input += '<div class="ifound-wrap">';
		input += '<i class="fa fa-sort"></i>';
		input += '</div>';
		input += '</div>';
		
		input += '<div class="social-network-icon-wrapper">';
		input += '<div class="ifound-wrap">';
		input += '<i class="fab fa-' + c + ' fa-fw social-network-icon"></i>';
		input += '</div>';
		input += '</div>';
		
		input += '<div class="social-network-input-wrapper">';
		input += '<div class="ifound-wrap">';
		input += '<input type="text" name="' + n + '" value="' + v + '" class="social-input" placeholder="https://"/>';
		input += '</div>';
		input += '</div>';
		
		
		input += '<div class="trash-can-wrapper">';
		input += '<div class="ifound-wrap">';
		input += '<i class="fal fa-trash delete-social-network"></i>';
		input += '</div>';
		input += '</div>';
		
		input += '</div>';
		input += '</div>';

		return input;
	}
	
	$( '#active-social-profiles' ).on( 'click', '.delete-social-network', function( e ) {	
		$( this ).parents( '.chosen-social-link').remove();
	});

});
