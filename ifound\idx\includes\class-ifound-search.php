<?
/**
 * iFoundSearch class
 *
 * Gets MLS search results.
 *
 * @package iFOUND
 * @since 1.0.4
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundSearch extends iFoundIdx {

	/**
	 * init iFoundSearch class.
	 *
	 * @since 1.0.4
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.4
	 */

	public function __construct() {

		add_action( 'ifound_search_bar', array( $this, 'search_bar' ), 10, 2 );
		add_action( 'ifound_advanced_search_bar', array( $this, 'advanced_search_bar' ) );

		add_action( 'ifound_advanced_scripts', array( $this, 'advanced_scripts' ), 10, 2 );

		add_action( 'ifound_search_criteria', array( $this, 'search_criteria' ), 10, 3 );

		add_action( 'ifound_budget_search', array( $this, 'budget_search' ) );
		add_action( 'ifound_draw_polygon_map', array( $this, 'draw_polygon_map' ) );
		add_action( 'ifound_dynamic_form_wrapper', array( $this, 'dynamic_form_wrapper' ), 10, 1 );
		add_action( 'ifound_dynamic_form', array( $this, 'dynamic_form' ), 10, 1 );
		add_action( 'ifound_display_options_form', array( $this, 'display_options_form' ), 10, 1 );
		add_action( 'ifound_display_stats_form', array( $this, 'display_stats_form' ), 10, 1 );
		add_action( 'ifound_advanced_button', array( $this, 'advanced_button' ) );
		add_action( 'ifound_show_map_button', array( $this, 'show_map_button' ), 10, 1 );
		add_action( 'ifound_advanced_search_results', array( $this, 'advanced_search_results' ), 10, 2 );
		add_action( 'ifound_qsearch_page_button', array( $this, 'qsearch_page_button' ) );
		add_action( 'ifound_radius_select', array( $this, 'radius_select' ), 10, 1 );

		add_filter( 'ifound_search_stats_form', array( $this, 'search_stats_form' ) );

	}

	/**
	 * Advanced Scripts
	 *
	 * The scripts for the advanced search.
	 *
	 * @since 1.0.5
	 * @since 3.6.5 Enqueue paging_js
	 *
	 * @param object $results The response object from the IDX API.
	 */

	public function advanced_scripts( $results, $extra = [] ) {

		wp_enqueue_script( 'cookie_js' );
		wp_enqueue_script( 'ifound_map_js' );
		wp_enqueue_script( 'paging_js' );
		wp_enqueue_script( 'save_this_js' );
		wp_enqueue_script( 'dynamic_js' );
		wp_enqueue_script( 'jquery-ui-datepicker' );

		wp_enqueue_style( 'advanced_search_css' );
		wp_enqueue_style( 'search_results_css' );

		do_action( 'ifound_load_map', $results, $extra );

	}

	/**
	 * Search Bar
	 *
	 * Displays on Quick Search, 50-50 pages.
	 *
	 * @since 1.0.0
	 * @since 2.5.37 Save in transient.
	 * @since 2.5.38 Move dynamic form out of transient.
	 *
	 * @param object $results The search API response object.
	 */

	public function search_bar( $results, $extra = [] ) {

		wp_enqueue_script( 'cookie_js' );
		wp_enqueue_script( 'save_this_js' );
		wp_enqueue_script( 'dynamic_js' );
		wp_enqueue_script( 'jquery-ui-datepicker' );
		wp_enqueue_script( 'budget_search_js' );
		wp_enqueue_style( 'search_bar_css' );

		?><div class="search-bar">

			<div class="ifound-wrap">

				<div class="search-criteria-heading">

					<i class="fal fa-search" aria-hidden="true"></i> <? _e( 'Filters', 'ifound' ); ?>
					<div id="show-criteria-arrow" class="criteria-collapse-control"></div>

					<!-- Show on mobile under 480 -->
					<div class="mobile-show-criteria"></div>
					<!-- End show on mobile under 480 -->

				</div>

				<!-- Hide on mobile under 480 -->
				<div class="search-criteria-wrapper">
					<div class="ifound-wrap"><?
						do_action( 'ifound_search_criteria', 'ifound_adv_search_criteria', 'search-', true ); ?>
					</div>
				</div>

                <div class="more-filters-heading">
                    <div>
                        <i class="fal fa-search" aria-hidden="true"></i>
                        <? _e( 'More Filters', 'ifound' ); ?>
                    </div>
                    <div id="show-criteria-arrow" class="criteria-collapse-control"><i class="fas fa-chevron-down"></i></div>
                </div>

                <div class="more-filters-wrapper">
                    <div class="ifound-wrap">
                        <div class="more-filters-criteria">
                            <div class="ifound-wrap">
                                <?
								do_action( 'ifound_search_criteria', 'ifound_more_adv_search_criteria', 'more-filters-' );
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?

				do_action( 'ifound_dynamic_form', $results->input_obj );
				do_action( 'ifound_budget_search' );
				do_action( 'ifound_display_map_wrapper', $results, $extra ); ?>
				<!-- End hide on mobile under 480 -->

			</div>

		</div><?

	}

	/**
	 * Advanced Search Bar
	 *
	 * Displays on all pages that @uses advanced-search.php
	 *
	 * @since 2.5.37
	 * @since 2.5.54 Enqueue JS in adv search bar due to transient.
	 */

	public function advanced_search_bar() {

		wp_enqueue_script( 'budget_search_js' );

		?><div class="default-search-criteria">

			<div class="ifound-wrap">

				<div class="default-criteria-heading">

					<i class="fal fa-search" aria-hidden="true"></i>
					<? _e( 'Filters', 'ifound' ); ?>
					<div id="show-criteria-arrow" class="criteria-collapse-control"><i class="fas fa-chevron-up"></i></div>

				</div>

				<div class="default-criteria-wrapper"><?

					do_action( 'ifound_search_criteria', 'ifound_adv_search_criteria', 'search-', true ); ?>

				</div>

			</div>

		</div>

		<div class="additional-criteria">

			<div class="ifound-wrap">

				<div class="criteria-heading">

					<i class="fal fa-plus-circle" aria-hidden="true"></i>
					<? _e( 'More Filters', 'ifound' ); ?>

				</div><?

				do_action( 'ifound_search_criteria', 'ifound_more_adv_search_criteria' ); ?>

			</div>

		</div><?
		do_action( 'ifound_draw_polygon_map' );
		do_action( 'ifound_budget_search' );
	}

	public function search_criteria( $type, $class = '', $use_filters = false ) {
		$full_class = $class . 'criteria-body';
		if (strpos($class, 'criteria-body') !== false) {
			$full_class = $class;
		}
		// In the following script section, we are outputting the name of the "more" criteria section,
		// aka "More Filters". This is so that the name can be sent back to us
		// via XHR when the MLS class dropdown is changed and we need to swap out the appropriate section
		// of the DOM.
		?>
		<script>
			<?
				if ($type === false || strpos($type, 'more') !== false) {
					echo "var moreCriteriaClassName = '$full_class';";
				} else {
					echo "var criteriaClassName = '$full_class';";
				}
			?>
		</script>
		<div class="<? echo $full_class; ?>">

			<div class="ifound-wrap">

				<? $this->dynamic_sections( $type, $use_filters ); ?>
			</div>

		</div><?

	}

	public function radius_select($options = []) {
		$options = wp_parse_args($options, [
			'disabled' => false,
		]);
		$disabled_string = $options['disabled'] ? 'disabled="disabled"' : '';
		$radius_options = range(0.25, 3, .25);
		?>
		<select name="radius" <?= $disabled_string ?>>
			<? foreach ($radius_options as $radius_option) : ?>
				<option value="<?=$radius_option?>" <?= $radius_option === 0.5 ? 'selected="selected"' : ''?>><?=number_format($radius_option, 2)?> miles</option>
			<? endforeach ?>
		</select>
		<?php
	}

	private function radius_section() {
		$button_title = is_admin() ? 'Radius/Drop Pin' : 'Radius';
		?>
		<script>
			jQuery(document).ready(function($) {
				var domContainer = document.querySelector('.ifound-radius-spa');
				function onDone() {
					var $lookups_body = $('.dynamic-button.radius > .lookups-body');
					$lookups_body.slideUp();
				}
				var props = {
					isAdmin: <?= is_admin() ? 'true' : 'false' ?>,
					// The global function might not exist yet, so we don't bind to it directly but save it as a thunk.
					addRadius: function() {
						window.ifound_add_radius.apply(undefined, arguments);
					},
					googleMapsLoadedPromise: window.iFoundGlobal.loadGoogleMaps(['places']),
					onDone: onDone,
					addOwnedListingEndpointBase: '<?= rest_url('ifound/' . iFOUND_PLUGIN_VERSION . '/admin/contacts/') ?>',
					addOwnedListingToContact: function(contact, ownedListing) {
						contact.owned_listings.push(ownedListing);
						doRender();
					},
					nonce: typeof ifound_shared_admin === 'object' ? ifound_shared_admin.nonce : null,
					allowDropPin: <? var_export(is_admin()) ?>,
					dropPin: function(chosenAddressFields) {
						var position = {
							lat: chosenAddressFields.lat,
							lng: chosenAddressFields.lng,
						};
						$(window).trigger('ifound:drop-pin', [position]);
						onDone();
					},
				};
				function doRender() {
					window.ifound_spa.render_app('radius', domContainer, props);
				};
				$(window).on('ifound:chose-contact-address', function(event, data) {
					props.contact = data;
					doRender();
				});
				doRender();
			});
		</script>
		<div class="dynamic-button radius">
			<div class="dynamic-heading radius"><?=$button_title?> </div>
			<div class="lookups-body">
				<div class="ifound-radius-spa"></div>
			</div>
		</div>
		<?php
	}

	private function multibox($type) {
		$endpoint = rest_url(iFoundSearchController::$endpoint_namespace . iFoundSearchController::$endpoint_base
			. '/search');
		$icons = iFoundMap::new_hookless()->map_icons();
		$campaign_builder_types = ['ifound_campaign_bulder_criteria', 'ifound_more_campaign_bulder_criteria'];
		$is_campaign_builder = in_array($type, $campaign_builder_types, true);
		// We don't really know if we're on the shortcode creator page. But if we're rendering the multibox and we're
		// not on the advanced search page or campaign builder, then we figure it is the shortcode creator.
		$is_shortcode_creator = !$is_campaign_builder && !$this->util()->is_advanced_search_page();
		?>
		<script>
			jQuery(document).ready(function($) {
				var domContainer = document.querySelector('.ifound-multibox-advanced-spa');
				var mlsClass = $('.dynamic-value[name="mls_class"]').val();
				$(window).on('ifound:mls-class-select', function(event, newMlsClass) {
					mlsClass = newMlsClass;
					doRender();
				});
				function doRender() {
					var props = {
						endpoint: '<?= $endpoint ?>',
						addSearchCriteria: function(data) {
							window.ifound_add_search_criteria(data);
						},
						mlsClass: mlsClass,
						icons: <?= json_encode($icons) ?>,
						isCampaignBuilder: <?= $is_campaign_builder ? 'true' : 'false' ?>,
						isShortcodeCreator: <?= $is_shortcode_creator ? 'true' : 'false' ?>,
                        isMultipleStateMls: <?= static::is_multiple_state_mls() ? 'true' : 'false' ?>,
					};
					window.ifound_spa.render_app('multibox_advanced', domContainer, props);
				}
				doRender();
			});
		</script>
		<div class="dynamic-button multibox-advanced">
			<div class="ifound-multibox-advanced-spa"></div>
		</div>
		<?
	}

	private function output_prop_type_mapped_form_input($input) {
		$choices = $this->get_prop_type_mapped();
		$filtered_choices = $this->get_agent_filtered_prop_type_mapped_labels($choices);
		$this->general_form_button($input, $filtered_choices);
	}

	public function display_options_form( $input ) {

		$display = $input['display']; ?>

		<form id="ifound-display-form">

			<table class="form-table notes_meta views_meta">

				<tbody>

					<tr>

						<th><label for="map_type"><? _e( 'Map View', 'ifound' ); ?></label></th>
						<td><input type="checkbox" id="map_type" name="map_type" value="checked" <? echo isset( $display['map_type'] ) ? 'checked' : ''; ?>/></td>
						<td><? _e( 'Default is road map, check this for satellite view.', 'ifound' ); ?></td>

					</tr>

					<tr>

						<th><label for="hide_map"><? _e( 'Hide Map', 'ifound' ); ?></label></th>
						<td><input type="checkbox" id="hide_map" name="hide_map" value="checked" <? echo isset( $display['hide_map'] ) ? 'checked' : ''; ?>/></td>

					</tr>

					<tr>

						<th><label for="hide_paging"><? _e( 'Hide Paging', 'ifound' ); ?></label></th>
						<td><input type="checkbox" id="hide_paging" name="hide_paging" value="checked" <? echo isset( $display['hide_paging'] ) ? 'checked' : ''; ?>/></td>

					</tr>

					<tr>

						<th><label for="hide_stats"><? _e( 'Hide Price Stats', 'ifound' ); ?></label></th>
						<td><input type="checkbox" id="hide_stats" name="hide_stats" value="checked" <? echo isset( $display['hide_stats'] ) ? 'checked' : ''; ?>/></td>

					</tr>

					<tr>

						<th><label for="items_per_page"><? _e( 'Items Per Page', 'ifound' ); ?></label></th>
						<td colspan="2"><input type="text" id="items_per_page" name="items_per_page" value="<? echo $input['query']['items_per_page'] ?: ''; ?>" placeholder="25" class="small-text"/></td>

					</tr>

					<tr>

						<th><label for="zero_results_message"><? _e( 'Zero Results Message', 'ifound' ); ?></label></th>
						<td colspan="2"><input type="text" id="zero_results_message" name="zero_results_message" value="<? echo empty( $display['zero_results_message'] ) ? '' : $display['zero_results_message']; ?>" placeholder="Sorry, the specified listing(s) category you're looking for isn't available." class="large-text"/></td>

					</tr>

				</tbody>

			</table>

		</form><?

	}

	public function search_stats_form($input) {
		return $input['stats'] ?: [];
	}

	public function display_stats_form() {
		?>
		<form id="ifound-stats-form">
			<div class="ifound-stats-spa"></div>
		</form>
		<?
	}

  	/**
  	 * Dynamic Sections
  	 *
  	 * These are the buttons displayed as search criteria and more filters.
  	 *
  	 * @since 1.0.0
  	 * @since 1.2.16 Set to show all mappings for the more criteria if mls_class is not res.
  	 *
  	 * @param string $Type 		  The option name for the saved sortable criteria.
  	 * @param bool   $use_filters Use saved sortable criteria on true, Use all mappings on false.
  	 */

	public function dynamic_sections( $type, $use_filters = false ) {

		$mappings = $this->field_mappings();

		if( $type && ( $this->mls_class() == 'res' || $use_filters ) ) {

			$option_name = $type . iFoundCrm::new_hookless()->crm_id();
			if (!is_admin()) {
				$agent_id = apply_filters('ifound_get_agent_id', null);
				$option_name = $type . $agent_id;
		    }
			$sections = get_option($option_name) ?: [];
			$jsString = '<script>if(typeof criteriaFields === "undefined") var criteriaFields=[';
			foreach( $sections as $section ) {
				$jsString .= '"' . $section . '",';
				$this->dynamic_form_button( $mappings->$section, $type );

			}
			$jsString .= '];</script>';
			echo $jsString;

		} else {

			foreach( $mappings as $mapping ) {

				$this->dynamic_form_button( $mapping, $type );

			}

		}

	}

	/**
	 * Lookups Options
	 *
	 * This creates the buttons for the lookups.
	 *
	 * @since 1.0.0
	 * @since 1.0.11 $lookups was changes to $criteria @see iFOUND::lookups_to_criteria()
	 * @since 2.4.14 Remove depricated dynamic-input-single from plugin.
	 * @since 2.5.24 Document $input->type descriptions.
	 *
	 * @param object $input The mappings info for this input.
	 */

	public function lookups_options( $input, $values = null ) {
		wp_enqueue_script( 'jquery-ui-tabs' );

		$criteria = $this->lookups_to_criteria();
		$easy_name = $input->easy_name;
		$criterion = $criteria->$easy_name;
		$is_days_ago_input = $this->util()->is_easy_name_a_days_ago_field($easy_name);
		$display_name = $input->display_name;
		?>
		<div class="ifound-wrap"><?

			if ($input->type === 'boolean') {
				$values = ['Y', 'N'];
			} else {
                if (!$values && isset($criterion->values)) {
                    $values = $criterion->values;
                }
			}
			if( ! empty( $values ) ) {

				$values = $this->make_value_to_label_mapping($values, $easy_name);
                if ($criterion && $criterion->interpretation === 'LookupMulti') {
                    ?>
                    <div class="dynamic-input-tabs">
                        <ul>
                            <li><a href="#<?= $easy_name . '-include-tab' ?>">Include</a></li>
                            <li><a href="#<?= $easy_name . '-exclude-tab' ?>">Exclude</a></li>
                        </ul>
                        <div id="<?= $easy_name . '-include-tab' ?>">
                            <?
                            $this->output_lookups($values, $easy_name, $display_name);
                            ?>
                        </div>
                        <div id="<?= $easy_name . '-exclude-tab' ?>">
                            <?
                            $negValues = $this->util()->array_map_modify_both(function($key, $value) {
                                $new_key = '-' . $key;
                                return [$new_key, $value];
                            }, $values);
                            $this->output_lookups($negValues, $easy_name, $display_name, true);
                            ?>
                        </div>
                    </div>
                    <?
                } else {
	                $this->output_lookups($values, $easy_name, $display_name);
                }
			} elseif( $easy_name == 'virtual_tour_unbranded' ) {
                $values = [ 'Y' ];
                foreach ( $values as $value ) {
                    ?>
					<div
						class="dynamic-input-button dynamic-input-array dynamic-input-criteria"
						name="<? echo $easy_name; ?>"
						value="<? echo 'https'; ?>"
						label="<? echo $display_name; ?>"
						display="<? echo $value; ?>"
					>
						<?php _e( $value, 'ifound' ); ?>
					</div>
                    <?php
                }
            } elseif( $input->type == 'number' || $is_days_ago_input ) {

				/** This are text inputs. With min max placeholders */ ?>

				<input class="dynamic-input-listen dynamic-input-min" type="text" placeholder="Min"/>
				<input class="dynamic-input-listen dynamic-input-max" type="text" placeholder="Max"/>
				<span
					class="dynamic-input-validate dynamic-input-range dynamic-input-criteria"
					name="<? echo $easy_name; ?>"
					label="<? echo $display_name; ?>"
					display="<? echo $display_name; ?>"
				>
					<i class="fal fa-plus-circle" aria-hidden="true"></i>
					<? _e( 'Add to filters', 'ifound' );?>
				</span><?

			} elseif( $input->type == 'date' ) {

				/** This are text inputs. With From abd To placeholders.  The ID tells us to load the proper datepicker. */ ?>

				<input class="dynamic-input-listen dynamic-input-min" type="text" id="ifound-to" placeholder="From"/>
				<input class="dynamic-input-listen dynamic-input-max" type="text" id="ifound-from" placeholder="To"/>
				<span
					class="dynamic-input-validate dynamic-input-range dynamic-input-criteria"
					name="<? echo $easy_name; ?>"
					label="<? echo $display_name; ?>"
					display="<? echo $display_name; ?>"
				>
					<i class="fal fa-plus-circle" aria-hidden="true"></i>
					<? _e( 'Add to filters', 'ifound' );?>
				</span><?

			} else {

				/** This is a single text input. We use this when we have no lookups. $input->type == 'text' */


				$class = 'plus';
				?>

				<input class="dynamic-input-listen dynamic-input-text" type="text" placeholder="Type Here"/>
				<span
					class="dynamic-input-validate dynamic-input-<? echo $class; ?> dynamic-input-criteria"
					name="<? echo $easy_name; ?>"
					label="<? echo $display_name; ?>"
					display="<? echo $display_name; ?>"
				>
					<i class="fal fa-plus-circle" aria-hidden="true"></i>
					<? _e( 'Add to filters', 'ifound' );?>
				</span><?

			} ?>

		</div><?

	}

	public function dynamic_form_wrapper( $input ) { ?>

		<div class="ifound-dynamic-form-wrapper">

			<div class="ifound-wrap">

				<h3 class="dynamic-heading">

					<? _e( 'Selected Filters', 'ifound' ); ?>

				</h3>

				<? do_action( 'ifound_dynamic_form', $input ); ?>


			</div>

		</div><?

	}

	/**
	 * Other Params
	 *
	 * List of params we do not show in Dynanic Form.
	 *
	 * @since 3.6.8
	 *
	 * @return array $other_params An array of other params.
	 */

	public function other_params() {

		return array(
			'items_per_page',
			'latlon',
			'max_results',
			'mls_class',
			'nearby',
			'pp',
			'all',
			'sort',
			'bounds',
			'polygons'
		);

	}

	/**
	 * Dynamic Form
	 *
	 * This is the form used in all searches and shortcode dashboard. This works as a container for the selected search criteria.
	 * dynamic.js and advanced.js also populate this form. The form itself is very basic. But, the inputs that populate the form are very complex.
	 * The decision of the input types is determined by the input button. @see iFOUND_search::lookups_options() is used to repopulate the form on page reload.
	 *
	 * @since 1.0.0
	 * @since 1.0.24 $mls_class use Residential if in the WP Admin.
	 * @since 1.0.25 Set input type as single value for days_back. This hack can be reviewed after we have the ability to set input types in the rails mapping for the iFound plugin.
	 * @since 2.4.10 Always use the current MLS class. Even if in the WP Adnim.
	 *
	 * @param object $results The main results object. @see iFOUND::process_input()
	 */

	public function dynamic_form( $input ) {

		$mappings = $this->field_mappings(); ?>

		<form id="ifound-dynamic-form" class="ifound-dynamic-form"><?

		 	//TODO: Do not display bounds input if not doing bounds map.
			if ( ! is_admin() ) {

				$bounds = isset( $input['bounds'] ) ? $input['bounds'] : ''; ?>

				<input type="hidden" id="ifound_bounds" name="bounds" value="<? echo $bounds; ?>"/><?

			}

			$this->mls_class_select( $input );
			$this->sort_select( $input );

			if ( is_array( $input ) ) {

				foreach( $input as $key => $value ) {

					/** Budget is an addon. This is not a param or words. It is not in the Rails Mapping either. */
					if( $key == 'budget' ) {

						$this->budget_form_input($value);
					} else if ($key === 'polygons') {
						// Here's a hacky hack. On the advanced search results page, we want to draw the polygon
						// criteria inputs. Normally we'd do them in JavaScript, but the JS map init code might not run
						// immediately if the map is initially hidden; it would only run when the user clicks the map
						// button. So we output them here in PHP. However, we don't want to output them for other pages
						// such as the shortcode creator (or campaign results), because in that case, when you load an
						// existing shortcode, there would be the criteria inputs from here in PHP, as well as the JS
						// ones. So, don't output except when it's the advanced search results page.
						if ($this->util()->is_advanced_search_page()) {
							foreach ($value as $polygon_id => $v) {
								$this->polygon_form_input($polygon_id, $v);
							}
						}
					} else if ( $key == 'nearby' && isset($value['address']) ) {

						$this->nearby_form_input($value);

					} else if ($key === 'prop_type_mapped') {
						foreach ($value as $val) {
							$this->prop_type_mapped_form_input($val);
						}
					} else if( in_array( $key, $this->other_params() ) ) {

						continue;

					} elseif( isset( $value['min'] ) || isset( $value['max'] ) ) {

						$object = new stdClass();

						$object->name = $key;

						if( isset( $value['min'] ) ) {

							$object->label = 'Min ' . ( isset( $mappings->$key->display_name )  ? $mappings->$key->display_name : '' );
							$object->type = '[min]';
							$object->value = $value['min'];

							$this->dynamic_form_input( $object );

						}

						if( isset( $value['max'] ) ) {

							$object->label = 'Max ' . ( isset( $mappings->$key->display_name )  ? $mappings->$key->display_name : '' );
							$object->type = '[max]';
							$object->value = $value['max'];

							$this->dynamic_form_input( $object );

						}

					} else {

						$object = new stdClass();

						$object->name = $key;

						$object->type = ( isset( $mappings->$key->type ) && $mappings->$key->type == 'text' ) ? '[]' : '';
						if( $key == 'days_back' ) $object->type = '';

						$object->label = ( isset( $mappings->$key->display_name )  ? $mappings->$key->display_name : '' );

						if( is_array( $value ) || is_object( $value ) ) {
							$criteria = $this->lookups_to_criteria();
							$criterion = $criteria->$key;

							/** Thsi only runs if we have an array of values. */

							foreach( $value as $val ) {

								$object->value = $val;

								$this->dynamic_form_input( $object, $criterion->interpretation );

							}

						} else {

							$object->value = $value;

							$this->dynamic_form_input( $object );

						}

					}

				}

			} ?>
            <div class="search-criteria-placeholder">
                Any filters you add will show here
            </div>

		</form><?


	}

	private function polygon_form_input($polygon_id, $input) {
		?>
		<div class="dynamic-input">
			<div class="dynamic-input-label">
				<span class="polygon-color polygon-id-<?= $polygon_id ?>" style="background:<?= $input['color'] ?>"></span> - Polygon
				<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true" polyid="<?= $polygon_id ?>"></i>
			</div>
			<input type="hidden" name="polygons[<?= $polygon_id ?>][paths]" value="<?= $input['paths'] ?>" class="dynamic-value" id="paths-<?= $polygon_id ?>">
			<input type="hidden" name="polygons[<?= $polygon_id ?>][color]" value="<?= $input['color'] ?>" class="dynamic-value" id="color-<?= $polygon_id ?>">
		</div>
		<?php
	}

	//Do not add conditions here EVER again. Add them somewhere before you get here.
	public function dynamic_form_input( $input, $interpretation = null ) {

		/** We make a custom label for price here. Or use as is.*/
		$value_text = ( strpos( $input->name, 'price' ) !== false ) ? '$' . number_format( $input->value ) : $input->value;
		if ($interpretation === 'LookupMulti' && strlen($input->value) && $input->value[0] === '-') {
            $value_text = 'NOT ' . substr($input->value, 1);
        }
        ?>

		<div class="dynamic-input">

			<div class="dynamic-input-label">
		        <?php if ( 'Virtual Tour Unbranded' == $input->label ) :
				    _e( 'Y', 'ifound' ); ?> - <?php echo $input->label;
                      else :
				    _e( $input->label, 'ifound' ); ?> - <?php _e( urldecode( $value_text ), 'ifound' );
                      endif; ?>
				<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true"></i>

			</div>
			<input type="hidden" name="<? echo $input->name; ?><? echo $input->type; ?>" value="<? echo $input->value; ?>" class="dynamic-value">

		</div><?

	}

	/**
	 * MLS Class Select
	 *
	 * This select is located inside the dynamic form. We can only search by 1 MLS Class at a time.
	 *
	 * @since 1.0.22
	 * @since 2.4.8  Move selected conditions to @see iFoundSearch::is_selected()
	 * @since 3.4.6  Only display if more than 1 allowed mls class.
	 *
	 * @param object $input The selected/saved search criteria for this request.
	 */

	public function mls_class_select( $input ) {

		$mls_classes = $this->allowed_mls_classes();

		if( ! empty( $mls_classes ) && count( $mls_classes ) > 1 ) {
			$admin = $this->is_admin() ? '_admin' : '';

			$id = isset( $_GET['page'] ) ? 'mls_class_select_' . $_GET['page'] : 'mls_class_select';
			$used_mls_class_name = $input ? $input['mls_class'] : null;
			// If the MLS class was not set by a certain search or PDP that's running, the dropdown should be set to the
			// user's last selection, for their convenience. That will be done by JavaScript.
			$use_user_mls_class_preference = false;
			if (!isset($input['mls_class'])) {
				// First check to see if this is a map search, which is always residential.
				$matches = [];
				$uri = urldecode($_SERVER['REQUEST_URI']);
				preg_match('#/map-search/#', $uri, $matches);
				if ($matches) {
					$used_mls_class_name = 'Residential';
				} else {
					$use_user_mls_class_preference = true;
				}
			}
			?>

			<div class="mls-class-select-wrap">

				<div class="ifound-wrap">

					<select class="dynamic-value mls_class_select<? echo $admin; ?>" data-pfmls-use-user-mls-class-preference="<?=$use_user_mls_class_preference?>" name="mls_class" id="<? echo $id; ?>"><?

					foreach( $mls_classes as $mls_class_code => $mls_class_name ) {

						$selected = ( $mls_class_name == $used_mls_class_name ) ? 'selected="selected"' : '';
						?>

						<option value="<? echo $mls_class_name; ?>" <? echo $selected; ?>><? _e( $mls_class_name, 'ifound' ); ?></option><?

					} ?>

					</select>

				</div>

			</div><?

		}

	}

	/**
	 * Sort Select
	 *
	 * This select is located inside the dynamic form.
	 *
	 * @since 1.0.22
	 * @since 1.0.24 Add _admin to class if in WP Admin. This will disable any jQuery that is controled by this.
	 *
	 * @param object $input The selected/saved sort criteria for this request.
	 */

	public function sort_select( $input ) {

		$admin = $this->is_admin() ? '_admin' : ''; ?>

		<div class="sort-select-wrap">

			<div class="ifound-wrap">

				<select class="dynamic-value sort_select<? echo $admin; ?>" name="sort"><?

				foreach( $this->sort_options() as $key => $value ) {

					$selected = ( isset( $input['sort'] ) && $key == $input['sort'] ) ? 'selected' : ''; ?>

					<option value="<? echo $key; ?>" <? echo $selected; ?>><? _e( $value, 'ifound' ); ?></option><?

				} ?>

				</select>

			</div>

		</div><?

	}

	/**
	 * Sort Options
	 *
	 * List of possible sort options.
	 *
	 * @since 1.4.0
	 * @since 2.5.65 Change label for latest_listing. This acts as last modified.
	 *
	 * @return array $sort_options An array of possible sort options.
	 */

	public function sort_options() {

		global $mls_associations;

		return isset( $mls_associations->sort_options ) ? $mls_associations->sort_options : array();

	}

	/**
	 * Budget Form Input
	 *
	 * Dynamic input for search by budget.
	 *
	 * @since 1.0.0
	 *
	 * @param object $input This is the input stored in a save this meta. @see iFOUND_contacts::save_this()
	 */

	public function budget_form_input( $input ) { ?>

		<div class="dynamic-input">
			<div class="dynamic-input-label">
				<? _e( 'Budget', 'ifound' ); ?> - <? _e( '$' . number_format( $input->payment ) . '/month', 'ifound' ); ?>
				<i class="fal fa-times-circle dynamic-input-remove" aria-hidden="true"></i>
			</div>
			<input type="hidden" name="budget[min]" value="<? echo $input->min; ?>" class="dynamic-value">
			<input type="hidden" name="budget[max]" value="<? echo $input->max; ?>" class="dynamic-value">
			<input type="hidden" name="budget[payment]" value="<? echo $input->payment; ?>" class="dynamic-value">
		</div><?

	}

	public function nearby_form_input($input) {
		?>
        <div class="dynamic-input">
            <div class="dynamic-input-label">
				<?=$input['address']?> - Address, <?=number_format($input['radius'], 2)?> miles - Radius
                <i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true"></i>
            </div>
            <input type="hidden" name="nearby[radius]" value="<?=number_format($input['radius'], 2)?>" class="dynamic-value">
            <input type="hidden" name="nearby[lat]" value="<?=$input['lat']?>" class="dynamic-value">
            <input type="hidden" name="nearby[lng]" value="<?=$input['lng']?>" class="dynamic-value">
            <input type="hidden" name="nearby[address]" value="<?=$input['address']?>" class="dynamic-value">
            <input type="hidden" name="nearby[mls_id]" value="<?=$input['mls_id']?>" class="dynamic-value">
        </div>
		<?
	}

	private function prop_type_mapped_form_input($val) {
		$choices = $this->get_prop_type_mapped();
		if (!$choices) {
			return;
		}
		$filtered_choices = $this->get_agent_filtered_prop_type_mapped_labels($choices);
		$label = $filtered_choices[$val];
		if (!$label) {
			return;
		}
		?>
		<div class="dynamic-input">
			<div class="dynamic-input-label">
				<?= $label ?> - Property Type
				<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true"></i>
			</div>
			<input type="hidden" name="prop_type_mapped[]" value="<?= $val ?>" class="dynamic-value">
		</div>
		<?
	}

	public function advanced_button() { ?>

		<div class="advanced-button-wrapper active">

			<div class="ifound-wrap">

				<div class="button modify-button">

					<i class="fal fa-sync" aria-hidden="true"></i> <? _e( 'Modify and Rerun Search', 'ifound' ); ?>

				</div>

				<div class="button advanced-button search-button">

					<? _e( 'View Search Results', 'ifound' ); ?>

				</div>

			</div>

		</div>
		<?

	}

	public function show_map_button( $visible = false ) {
		$buttonClass = $visible ? '' : 'button-invisible'; ?>
		<div class="show-map-button-wrapper">
			<div class="button map-button <? echo $buttonClass ?>">
				<i class="fal fa-map"></i> <? _e( 'Show Map', 'ifound' ); ?>
			</div>
		</div><?
	}

	public function qsearch_page_button() { ?>
		<div class="qsearch-button-wrapper">
			<div class="button button-invisible search-button" id="quick-search-button">Search</div>
		</div> <?
	}

	public function dynamic_form_button( $input, $type ) {

		if( empty( $input ) ) return;
		else if(strpos($input->display_name, 'City') !== FALSE) {
			$this->do_city_autofill($input);
			return;
		} else if(strpos($input->display_name, 'School District') !== FALSE
				|| strpos($input->display_name, 'Zip') !== FALSE) {
			$this->do_generic_autofill($input);
			return;
		} else if($input->type === 'composite') {
			$this->do_composite_input($input, $type);
			return;
		}
		$this->general_form_button($input);
	}

	private function general_form_button($input, $values = null) {
		?>
		<div class="dynamic-button">
			<div class="dynamic-heading">
				<? _e( $input->display_name, 'ifound' ); ?>
			</div>
			<div class="lookups-body">
				<? $this->lookups_options($input, $values); ?>
			</div>
		</div>
		<?
	}

	/**
	 * Advanced Search Results
	 *
	 * This displays listings on the Adv Search page and dynamic polygon pages.
	 *
	 * @since 1.0.0
	 * @since 2.5.35 Add ifound_no_results message for dynamic polygon pages.
	 *
	 * @param object $results The search API response object.
	 */

	public function advanced_search_results( $results, $extra = [] ) { ?>

		<div class="results-overlap display-results-wrapper"><?

			if( ! empty( $results->listings ) ) {

				do_action( 'ifound_price_stats', $results, $extra );

				do_action( 'ifound_display_results', $results, $extra );

				do_action( 'ifound_before_pagination', $results );
				do_action( 'ifound_display_pagination', $results );
				do_action( 'ifound_after_pagination', $results );

			} else {

				do_action( 'ifound_no_results', $results );

			} ?>

		</div><?

	}

	/*
	 * Display "draw the polygon" button
 	 */
 	public function draw_polygon_map() {
                wp_enqueue_script('draw_map_js');?>
                <div class="ifound-wrap">
                        <div class="criteria-heading" id="draw-polygon-button">
                                <i class="fas fa-map-marker-alt"></i>
                                <? _e( 'Draw Map', 'ifound' ); ?>
                        </div>
                        <div class="criteria-body" id="draw-map-body"></div>
                </div>
        <?}

	public function budget_search() { ?>

		<div class="budget-search">

			<div class="ifound-wrap">

				<div class="budget-heading">

					<i class="fal fa-money-bill" aria-hidden="true"></i>
					<? _e( 'Search By Budget', 'ifound' ); ?>


				</div>

				<div class="budget-body">

					<div class="budget-input-wrapper">

						<div class="ifound-wrap">

							<div class="one-fourth first budget-item monthly-payment">

								<div class="budget-item-heading monthly-payment"><? _e( 'Desired Monthly Payment - $', 'ifound' ); ?></div>

								<div class="budget-item-input monthly-payment">

									<input type="number" id="budget-payment" step="100" placeholder="1100"/>

								</div>

							</div>

							<div class="one-fourth budget-item down-payment">

								<div class="budget-item-heading down-payment"><? _e( 'Total Down Payment - $', 'ifound' ); ?></div>

								<div class="budget-item-input down-payment">

									<input type="number" id="budget-down" step="1000" placeholder="20000"/>

								</div>

							</div>

							<div class="one-fourth budget-item interest-rale">

								<div class="budget-item-heading interest-rale"><? _e( 'Interest Rate - %', 'ifound' ); ?></div>

								<div class="budget-item-input interest-rale">

									<input type="number" id="budget-rate" step=".125" placeholder="4.5"/>

								</div>

							</div>

							<div class="one-fourth budget-item loan-term">

								<div class="budget-item-heading loan-term"><? _e( 'Loan Term - Years', 'ifound' ); ?></div>

								<div class="budget-item-input loan-term">

									<input type="number" id="budget-term" step="5" placeholder="30"/>

								</div>

							</div>

						</div>

					</div>

					<div class="budget-add-button-wrapper">

						<div class="ifound-wrap">

							<div class="budget-add-button button"><i class="fal fa-plus-circle" aria-hidden="true"></i> <? _e( 'Add Budget Filters', 'ifound' ); ?></div>

						</div>

					</div>

					<div class="budget-msg-wrapper">

						<div class="ifound-wrap">

							<? _e( '** Do not include price in your selected criteria. **', 'ifound' ); ?>

						</div>

					</div>

				</div>

			</div>

		</div><?

	}

	public function do_autofill($input) { ?>
		<div class="dynamic-button">
			<div class="dynamic-heading">
				<? _e( $input->display_name, 'ifound' ); ?>
			</div>
			<div class="lookups-body">
				<div class="autofill-field" style="position:relative;min-width:100px;width:100%;"><input class="dynamic-input-listen dynamic-input-autofill" type="text" id="ifound-<? echo str_replace(' ', '-', strtolower($input->display_name)); ?>" placeholder="<? echo $input->display_name; ?>"/></div>
				<span
					class="dynamic-input-validate validate-autofill dynamic-input-criteria"
					name="<? echo $input->easy_name; ?>"
					label="<? echo $input->display_name; ?>"
					display="<? echo $input->display_name; ?>"
				>
					<i class="fal fa-plus-circle" aria-hidden="true"></i>
					<? _e( 'Add to filters', 'ifound' );?>
				</span>
			</div>
		</div><?
	}

	public function do_city_autofill($input) {
		$criteria = $this->lookups_to_criteria();
		$easy_name = $input->easy_name;
		$s_beg = '<script>var cities = [';
		if( ! empty( $criteria->$easy_name->values ) ) {
				$script_str = '';
				foreach( $criteria->$easy_name->values as $value ) {
					$script_str .= '"' . $value . '", ';
				}

		}
		echo $s_beg . $script_str . '];</script>';
		$this->do_autofill($input);
	}

	public function do_generic_autofill($input) {
		$criteria = $this->lookups_to_criteria();
		$easy_name = $input->easy_name;

		$js_var_name = 'js_var_error';
		switch($easy_name) {
			case 'school_district': $js_var_name = 'sDistricts';
			break;
			case 'zip': $js_var_name = 'zips';
		}

		$s_beg = '<script>var ' . $js_var_name . ' = [';
		$script_str = '';

		if( ! empty( $criteria->$easy_name->values ) ) {
				foreach( $criteria->$easy_name->values as $value ) {

					// get rid of the extra zip codes armls
					if($easy_name === 'zip' && iFoundIDX::mls_name() === 'armls') {
						if(substr($value, 0, 1) !== '8') continue;
					}

					$script_str .= '"' . $value . '", ';
				}

		} else {
			$mls_associations_origin = $this->get_config()['mls_associations_origin'];
			$response = wp_remote_get($mls_associations_origin . iFoundIDX::mls_name() . '/');
			$json = json_decode($response['body'], true);
			if(array_key_exists($easy_name, $json)) {
				foreach($json[$easy_name] as $autofill_item) {
					$script_str .= '"' . $autofill_item . '", ';
				}
			}
		}
		echo $s_beg . $script_str . '];</script>';
		$this->do_autofill($input);
	}

	// I used the term 'composite' in this function name. Perhaps a better name would have been 'custom', meaning less
	// generic than the other form fields that are associated directly with an MLS field, whereas these custom ones are
	// interpreted on some level before issuing a search query to our IDX.
	private function do_composite_input($input, $type) {
		if ($input->easy_name === 'nearby') {
			$this->radius_section();
		} else if ($input->easy_name === 'multibox') {
			$this->multibox($type);
		} else if ($input->easy_name === 'prop_type_mapped') {
			$this->output_prop_type_mapped_form_input($input);
		} else {
			wp_die('Error 95572458: Invalid composite input');
		}
	}

	private function output_lookups(array $values, $easy_name, $display_name, $display_as_exclude = false): void {
		foreach ($values as $value => $label) {
            $display = $label;
            if ($display_as_exclude) {
                $display = 'NOT ' . $display;
            }
            ?>
            <div
                class="dynamic-input-button dynamic-input-array dynamic-input-criteria"
                name="<? echo $easy_name; ?>"
                value="<? echo $value; ?>"
                label="<? echo $display_name; ?>"
                display="<? echo $display; ?>"
            >
                <? _e($label, 'ifound'); ?>
            </div>
            <?
		}
	}
}
