var doing_saveThis = false;
var doing_saveThisFn = null;

jQuery( document ).ready( function( $ ) {

  $( document ).on( 'click', '.save-this', function( e ) {
    if ($(this).data('ifound-save-this-saved')) {
      var win = window.open('/client-profile/', '_blank');
      win.focus();
        return;
    }
    $(this).parents('.save-this-wrapper').find( '.save-this-title-wrapper' ).slideDown( 'slow' );
    $(this).parents('.save-this-wrapper').find( '.save-this-title' ).focus();
  });

  $( document ).on( 'click', '.save-this-title-close', function( e ) {
    $(this).parents( '.save-this-title-wrapper' ).slideUp( 'slow' );
  });

  $( document ).on( 'click', '.save-this-title-button', function( e ) {

    var that = this;
    doing_saveThis = true;

    var spinner_div = $( this ).parents('.save-this-wrapper').children( '.save-this' ).children( '.ifound-wrap' ).children( '.save-this-spinner' );

    var save_type = $( this ).attr( 'save_type' );

    if( save_type == 'property-alert' ) {
      var params  = $( this ).attr( 'mls_id' );
    } else {
      var params  = $( '#ifound-dynamic-form' ).serialize();
    }

    var campaign_title = $(this).siblings( '.save-this-title' ).val();
    if( campaign_title.length < 1 ){
      $(this).siblings( '.save-this-title' ).addClass( 'required-red' );
      return;
    }

    $( '.save-this-title-wrapper' ).slideUp( 'slow' );

    //https://davidwalsh.name/query-string-javascript
    var urlParams = new URLSearchParams(window.location.search);
    if(urlParams.has('contact_id')){
      var contact_id = urlParams.get('contact_id');
    }else{
      var contact_id = save_this.contact_id;
    }

    doing_saveThisFn = function(contact_id) {
      var parts = [ save_this.endpoint, save_type, save_this.nonce, contact_id, params, campaign_title ];
      $.ajax ( {
          url : parts.join('/'),
          method: 'POST',
          beforeSend: function() {
              $( spinner_div ).removeClass( 'fa-heart fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
              $('.while-we-wait').addClass('active');
          },
          success: function( response ) {
              $( spinner_div ).removeClass( 'fa-spinner fa-spin' ).addClass( response.class );
              if( response.pop_form  ){
                  runPopForm();
              }
              $('.while-we-wait').removeClass('active');
              // Indicate to the user that the save was successful, and how to see their saved items.
              var $saveThisButton = $(that).parents('.save-this-wrapper').find('.save-this.button')
              $saveThisButton.find('> .ifound-wrap')[0].childNodes[2].nodeValue = ' View Saved Items';
              $saveThisButton.data('ifound-save-this-saved', true);
          },
          dataType:'json'
      });
    };

    if( contact_id ) {
      doing_saveThisFn(contact_id);
    } else {
      runPopForm();
    }
  });


  function runPopForm(){
    var $window = $(window);
    var window_top_position = $window.scrollTop();
    var img_top_position = (window_top_position + 50);

    $('.ifound-popup').css('top', img_top_position);
    $( '.ifound-popup, .ifound-popup-backdrop, .ifound-popup-close' ).addClass( 'active' );
  }

  /** @link https://www.gravityhelp.com/documentation/article/gform_confirmation_loaded/ */
  $( document ).bind( 'gform_confirmation_loaded', function() {
     $.ajax ( {
      url : save_this.contact_id_endpoint,
      headers: {
        accept: 'application/json',
      },
      dataType:'json',
      success: function( response ) {
        updateContactID( response.contact_id );
        if (response.is_from_lead_gen && response.lead_gen_data?.campaign_url) {
          window.location.href = response.lead_gen_data.campaign_url;
        }
      }
    });
  });

  // Turn _ to space and uppercase first letter of each word
  // https://stackoverflow.com/a/21792507/135101
  function humanize(str) {
    var i, frags = str.split('_');
    for (i=0; i<frags.length; i++) {
      frags[i] = frags[i].charAt(0).toUpperCase() + frags[i].slice(1);
    }
    return frags.join(' ');
  }

  // https://stackoverflow.com/a/16233919/135101
  function formatMoney(val) {
    // Create our number formatter.
    var formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: 'compact',

      // These options are needed to round to whole numbers if that's what you want.
      // I discovered if you use only use maximumFractionDigits of 0, mobile Chrome and Safari
      // will error with "maximumFractionDigits is out of range", so be sure to use both.
      minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
      maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
      minimumSignificantDigits: 2,
    });

    return formatter.format(val);
  }

  function format_value(val, field_name) {
    if (['close_price', 'list_price', 'price_sqft'].includes(field_name)) {
      return formatMoney(val);
    }
    return val;
  }

  var $hide_show_stats_button = $('button.hide_show_stats');
  $hide_show_stats_button.on('click', function() {
    var $stats_stuff = $hide_show_stats_button.parent().siblings('.items');
    $stats_stuff.each(function(index, element) {
      if (getComputedStyle(element).display === 'none') {
        $hide_show_stats_button.text('Hide stats');
        element.style.display = 'flex';
      } else {
        $hide_show_stats_button.text('Show stats');
        element.style.display = 'none';
      }
    });
  });

  var is_showing_trends = false;
  var $ifound_trends = $('.ifound_trends');
  function do_trends(trends_data) {
    var trend_fields = Object.keys(trends_data[0].results);
    trend_fields.forEach(function(field_name) {
      var ctx = $('<canvas></canvas>')
      var facet_to_use = field_name === 'computed_days_on_market' ? 'mean' : 'median';
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: trends_data.map(x => x.title),
          datasets: [{
            label: 'Median ' + humanize(field_name),
            data: trends_data.map(x => x.results[field_name][facet_to_use] === 'NaN' ? NaN : x.results[field_name][facet_to_use]),
          }],
        },
        options: {
          spanGaps: true,
          scales: {
            yAxes: [{
              ticks: {
                callback: function(value, index, values) {
                  return format_value(value, field_name);
                },
              },
            }],
          },
          tooltips: {
            callbacks: {
              label: function(tooltipItem, data) {
                return format_value(tooltipItem.yLabel, field_name);
              },
            },
          },
          onResize: function(chart, size) {
            chart.options.scales.yAxes[0].ticks.maxTicksLimit = Math.round(size.height / 50);
          },
        },
      });
      var $div = $('<div class="ifound_trends_chart"></div>');
      $div.append(ctx);
      $ifound_trends.append($div);
    });
  }
  var $hide_show_trends_button = $('button.hide_show_trends');
  var $hide_show_trends_spinner = $hide_show_trends_button.find('.fa-spinner');
  var $hide_show_trends_error = $hide_show_trends_button.parent().siblings('.error');
  var has_calculated_trends = false;
  var trends_data = null;
  var chartjs_script_loaded = false;
  $hide_show_trends_button.on('click', function() {
    if (is_showing_trends) {
      $ifound_trends.hide();
      is_showing_trends = false;
      $hide_show_trends_button.text('Show trends');
    } else {
      $hide_show_trends_error.hide();
      function do_show() {
        has_calculated_trends = true;
        $hide_show_trends_spinner.hide();
        $ifound_trends.show();
        is_showing_trends = true;
        $hide_show_trends_button.text('Hide trends');
      }
      function do_error(msg) {
        $hide_show_trends_error.text(msg);
        $hide_show_trends_error.show();
        $hide_show_trends_spinner.hide();
      }
      function get_trends_data(trends) {
        if (trends_data) {
          return Promise.resolve(trends_data);
        }
        return $.getJSON(trends.endpoint, {
          inputs: trends.inputs,
          signature: trends.signature,
        });
      }
      function get_script(src) {
        if (chartjs_script_loaded) {
          return Promise.resolve();
        }
        return new Promise(function(resolve, reject) {
          var scriptTag = document.createElement('script');
          scriptTag.type = 'text/javascript';
          scriptTag.src = src;
          scriptTag.onload = function() {
            chartjs_script_loaded = true;
            resolve();
          };
          scriptTag.onerror = function() {
            reject(new Error('We were unable to load the charts library. Please try again.'));
          };
          document.body.appendChild(scriptTag);
        });
      }
      if (!has_calculated_trends) {
        $hide_show_trends_spinner.show();
        Promise.all([
          get_trends_data(window.ifound_trends),
          get_script('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js'),
        ])
          .then(function(responses) {
            do_trends(responses[0]);
            do_show();
          })
          .catch(error => {
            if (error.responseJSON) {
              do_error(error.responseJSON.message);
              return;
            }
            do_error(error.message);
          });
      } else {
        do_show();
      }
    }
  })
});


function updateContactID( contact_id ){
  save_this.contact_id = contact_id;
  fadeoutRegForm(200);
  if (doing_saveThis) {
    doing_saveThisFn(contact_id);
    doing_saveThis = false;
  }
}

function fadeoutRegForm(interval) {
  let form = document.getElementsByClassName('ifound-popup')[0];
  let backdrop = document.getElementsByClassName('ifound-popup-close')[0];

  form.style.opacity = backdrop.style.opacity = 1;

  setTimeout(function() {
    setInterval(function() {
      if( form.style.opacity > 0 ) {
        form.style.opacity = backdrop.style.opacity -= 0.25;
      } else {
        form.style.display = backdrop.style.display = 'none';
        clearInterval(this);
      }
    }, interval)
  }, 1500);
}
