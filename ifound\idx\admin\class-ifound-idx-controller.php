<?php

defined('ABSPATH') or die('You do not have access!');

class iFoundIdxAdminController {
	use UtilTrait;

	public static $endpoint_namespace = 'ifound';
	public static $broker_compensation_endpoint_base = '/broker_compensation';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('rest_api_init', [$this, 'rest_api_init_hook']);
	}

	public function rest_api_init_hook() {
		$this->register_routes();
	}

	private function register_routes() {
		register_rest_route(static::$endpoint_namespace, static::$broker_compensation_endpoint_base, array(
			array(
				'methods'  => WP_REST_Server::CREATABLE,
				// We don't need to check anything. We're using the _wpnonce param to know the user.
				// 'permission_callback' => array($this, 'permissions_check'),
				'callback' => array($this, 'post'),
			),
		));
	}

	public function post(WP_REST_Request $request) {
		$json_string = file_get_contents('php://input');
		$json = json_decode($json_string, true);
		$original_value = iFoundIdx::new_hookless()->get_broker_compensations_for_agent();
		$value = $json;
		if (count($value['legal_disclaimer_history']) > count($original_value['legal_disclaimer_history'])) {
			$status = $value['legal_disclaimer_history'][count($value['legal_disclaimer_history']) - 1]['status'];
			// Note: we can't use menu_page_url() here at this point during a REST call, menus aren't loaded, for the
			// sake of efficiency. See: https://wordpress.stackexchange.com/a/240871/27896
			$link = admin_url('admin.php?page=broker_compensation');
			$user_id = get_current_user_id();
			$user_data = get_userdata($user_id);
			$name = $user_data->data->display_name;
			$body = <<<EOT
				Name: $name
				User ID: $user_id
				Status: $status
				Link: $link

				Be sure to switch to the correct agent for that page to show the correct data.
			EOT;
			iFoundEmail::new_hookless()->email_ifoundagent_staff('Broker Compensation status change for agent', $body);
		}
		iFoundCrm::new_hookless()->update_option_for_agent(iFoundAdmin::$ifound_broker_compensations_option_name,
			$value);
		return new WP_REST_Response(['success' => true], 200);
	}
}
