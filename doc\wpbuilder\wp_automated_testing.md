[Home](Home)

[TOC]

# Automated Testing In WordPress

## **Background**

The WordPress dev team maintains a set of PHPUnit tests in a separate SVN repo.  Instructions for installing and running these tests can be found [here](http://make.wordpress.org/core/handbook/automated-testing/).  Those steps will be detailed here.

### These Tests Are Not Very Good

From the docs:

> ... There are many opportunities for improvement in the existing tests. Some of them are ancient and others are slow or fragile. Some do not tests well in multisite or under certain conditions. Some individual tests try to test too much, and could be improved by using data providers, dependencies, and more narrow assertions.

> Additionally, More than 75 tests (linked to a few dozen different tickets) are currently considered to be known WordPress bugs and therefore skipped.

## **Installation**

The following are my own installation notes, using Ubuntu 12.04.

1. The tests are housed in an SVN repo, but I wanted to use Git to manage my own local install.  My first step was to install `git-svn`:

        $ sudo apt-get install git-svn

2. Clone the repo.

        $ git svn clone -r28181:HEAD http://develop.svn.wordpress.org/trunk/ wp-test

    This creates a shallow copy of the repo, including only the most recent revision.  The `-r28181:HEAD` specifies the head of the current revision, which was 28181 at the time of this writing.  The most recent rev number can be found at the top of [the repo page](http://develop.svn.wordpress.org/trunk/)

3. Create an empty MySQL test database.  This database will be populated and purged every time the tests are run, so use a separate database or an existing one that you feel some level of malevolence towards.

4. Copy `wp-test-config-sample.php` to `wp-test-config.php` and fill out the details.

5. Copy `wp-config-sample.php` to `wp-config.php` and insert relevant database details.  This file will be disregarded by the tests - `wp-test-config.php` will be used instead.

## **Running the Tests**

1. To run the tests, just type `phpunit`.  This will test the WordPress installation in `src/` as a single site.  To test as multisite, use

        $ phpunit -c tests/phpunit/multisite.xml

    To include ajax tests, use

        $ phpunit --group ajax.

    There are over 2000 tests.  Many of them are tied to outstanding bug reports and are skipped - these show an `S` in the test output.

    My first time running the tests, phpunit hit a fatal error around test 1600.  I was testing as multisite.  I didn't try running the tests as a single site install or debugging the issues because I had no particular reason to.  My tests will be written from scratch, and the existing tests will only serve as examples.

## **Writing Custom Tests**

The test cases included in the above repo extend class `WP_UnitTestCase`, which is defined in `tests/phpunit/includes/testcase.php`.

### Capturing Page Output

WordPress provides the surprisingly convenient [HTTP API](https://codex.wordpress.org/HTTP_API) for developers.  This API includes the `wp_remote_get($url)` function, which can be used to retrieve the full HTML source of a given URL.  My local WP multisite install is set up on the url `http://wpmulti/`.  To test this function, I wrote the following "test case":

        // tests/phpunit/tests/experimental/experimental.php
        <?php

        class ExperimentalTestCase extends WP_UnitTestCase {
            function test_remote_get() {
                $result = wp_remote_get('http://wpmulti/');
                fwrite(STDOUT, print_r($result, TRUE));
            }
        }

This was then run from the command line in the project root using:

        $ phpunit phpunit tests/phpunit/tests/experimental/experimental.php

As expected, this output the page source to the command line.






