<?php

namespace Profound\MLS;

class ArmlsImageHandler extends ImageHandler {
	static $mlsname = "armls";

	public function getImagePathsList($listing_id) {
		$imgtable = self::$mlsname . "_images";
		$sql = "SELECT Location, `Content-Description` FROM $imgtable WHERE `Content-ID` = '$listing_id' ORDER BY `Preferred` DESC, `Object-ID` ASC";
		$results = $this->getDb()->fetchAll($sql);
		$img_array = array();
		foreach ($results as $key => $result) {
			$loc = preg_replace('/^https?:\/\/cdn.photos.flexmls.com/', 'https://cdn.photos.sparkplatform.com', $result["Location"]);
			$loc = preg_replace('/^http:/', 'https:', $loc);

			$img_array[$key]['normal_url'] = $loc;
			// Add "-t" to file name before extension.
			$img_array[$key]['thumbnail_url'] = preg_replace("/\.jpg$/", "-t.jpg", $loc);
			// Add "-o" to file name before extension.
			$img_array[$key]['highres_url'] = preg_replace("/\.jpg$/", "-o.jpg", $loc);
			$img_array[$key]['description'] = $result['Content-Description'];
		}
		return $img_array;
	}
}
