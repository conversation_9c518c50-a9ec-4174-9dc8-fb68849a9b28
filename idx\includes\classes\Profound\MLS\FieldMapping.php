<?php

namespace Profound\MLS;

class FieldMapping {
	private $options;
	private $fieldMappings;

	public function __construct($options = array()) {
		$this->options = $options;
	}

	public $mls;
	public $mlsClass;
	public $easyName;
	public $mapName;
	public $mlsFieldName;

	public function quoteValue($val) {
		return $this->getFieldMappings()->quoteFieldValue($this->mlsFieldName, $this->mls, $this->mlsClass, $val);
	}

	protected function getFieldMappings() {
		return @$this->options['field_mappings'];
	}
}
