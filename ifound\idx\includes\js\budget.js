/**
 * @summary Budget.
 *
 * Adds and removes inputs for search by budget.
 *
 * @since 1.0.0
 * @link https://stackoverflow.com/questions/7903890/jquery-interest-calculator-calculate-amount-of-credit-from-monthly-payment
 */

jQuery( document ).ready( function( $ ) {
	
	
	/** Add an input for a text input with range.  */
	$( '.budget-add-button' ).on( 'click', function() {
		
		$( '.required-red' ).removeClass('required-red');
		
		var ready = true;
		
		var input = new Object;
			input.payment = $( '#budget-payment' ).val();
			input.rate = $( '#budget-rate' ).val();
			input.down = $( '#budget-down' ).val();
			input.term = $( '#budget-term' ).val();
		
		$.each( input, function( key, val ){
			if( val < 1 ){
				$( '#budget-' + key ).addClass('required-red');
				ready = false;
			}
		});
		
		if( ! ready ) return;
		
		var price = getPrice(input);
		
		var min = price * .98;
		var max = price * 1.02;
		
		var display = 	'<div class="dynamic-input">'
			display +=	'<div class="dynamic-input-label">';
			display +=	'Budget - $' + input.payment + ' /month ';
			display +=	'<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true"></i>';
			display +=	'</div>';
			display +=	'<input type="hidden" name="list_price[min]" value="' + Math.round(min) + '" class="dynamic-value">';
			display +=	'<input type="hidden" name="list_price[max]" value="' + Math.round(max) + '" class="dynamic-value">';
			display +=	'</div>';
			
			$( '.ifound-dynamic-form' ).append( display );
		
	});
	
	function getPrice(input){
		var Months = input.term * 12;
		var MonthlyPaymentAmount = parseFloat(input.payment);
		var APR = input.rate / 100;   //16.9% APR
		var InterestRate = (APR / 12);     //monthly interest
		var TotalAmountOfCredit = (MonthlyPaymentAmount / InterestRate) * (1 - Math.pow((1 + InterestRate), (-Months)));
		var budget = TotalAmountOfCredit + input.down;
		return budget;
	}
	
});

