-- Note: this is basically an example on how to convert the tables
-- from MyISAM to InnoDB, which allows transactions.
-- As we are in the middle of adding Sandicor and multiple listing
-- classes to the system, the likelihood is high that there will
-- be more tables that you'll need to convert when the time comes.

alter table armls_property_A engine=InnoDB;
alter table armls_images engine=InnoDB;

alter table trendmls_property engine=InnoDB;
alter table trendmls_images engine=InnoDB;

-- Don't forget to add SNDMLS tables here, when we have them.