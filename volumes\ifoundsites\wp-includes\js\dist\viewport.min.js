/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ifViewportMatches:()=>h,store:()=>d,withViewportMatch:()=>u});var r={};e.r(r),e.d(r,{setIsMatching:()=>a});var o={};e.r(o),e.d(o,{isViewportMatch:()=>s});const i=window.wp.compose,n=window.wp.data;const c=function(e={},t){return"SET_IS_MATCHING"===t.type?t.values:e};function a(e){return{type:"SET_IS_MATCHING",values:e}}function s(e,t){return-1===t.indexOf(" ")&&(t=">= "+t),!!e[t]}const d=(0,n.createReduxStore)("core/viewport",{reducer:c,actions:r,selectors:o});(0,n.register)(d);const p=(e,t)=>{const r=(0,i.debounce)((()=>{const e=Object.fromEntries(c.map((([e,t])=>[e,t.matches])));(0,n.dispatch)(d).setIsMatching(e)}),0,{leading:!0}),o=Object.entries(t),c=Object.entries(e).flatMap((([e,t])=>o.map((([o,i])=>{const n=window.matchMedia(`(${i}: ${t}px)`);return n.addEventListener("change",r),[`${o} ${e}`,n]}))));window.addEventListener("orientationchange",r),r(),r.flush()},w=window.ReactJSXRuntime,u=e=>{const t=Object.entries(e);return(0,i.createHigherOrderComponent)((e=>(0,i.pure)((r=>{const o=Object.fromEntries(t.map((([e,t])=>{let[r,o]=t.split(" ");return void 0===o&&(o=r,r=">="),[e,(0,i.useViewportMatch)(o,r)]})));return(0,w.jsx)(e,{...r,...o})}))),"withViewportMatch")},h=e=>(0,i.createHigherOrderComponent)((0,i.compose)([u({isViewportMatch:e}),(0,i.ifCondition)((e=>e.isViewportMatch))]),"ifViewportMatches");p({huge:1440,wide:1280,large:960,medium:782,small:600,mobile:480},{"<":"max-width",">=":"min-width"}),(window.wp=window.wp||{}).viewport=t})();