[Home](Home)

[TOC]

# WP<PERSON>uilder

## Summary

This plugin exposes an API that allows WordPress sites to be cloned and configured automatically.  The API accepts a JSON payload and returns a JSON success/failure message.

In most cases, this plugin makes its changes by directly copying/altering tables with the database.

File paths in this document are all relative to `wp-content/plugins/wpbuilder` within the WordPress directory.

## How It Works

### Cloning and Theme Selection

Within the payload sent to the API, a theme is specified.  For each available theme, a site has been set up on the server that serves as generic template.  These sites serve as the *parent site* - the site that will be cloned into the new site.  The WpBuilder plugin selects the site to be cloned based on an array within `lib/Theme.php`.

Site cloning is accomplished by copying several database tables from the parent site to the child site.  The actual cloning is performed within `lib/SiteCloner.php`, and the tables which are cloned are set within that file.  When a new plugin/theme is added that requires additional database tables, they must be added to the list of tables within that file.

Color is set on the theme during the cloning process.  All of our themes are Genesis themes, and the color is set in the `genesis-settings` option in the `wp_XX_options` table, where `XX` is the blog ID of the site in question.  This option is a PHP serialized array; within that array, the `style_selection` key stores the actual color value.  This is set by `lib/Theme.php` during cloning.

### User Creation and New Site Subdomain

A new admin user (**not** a super admin) is created for the new site after cloning.  The username is defined by the `wp_builder.client.access_fullname` parameter within the JSON payload, by converting to lowercase and removing all non-alphanumeric characters.  For example, the username for a client named Testy McTesterson III would be `testymctestersoniii`.  This value will also be used for the subdomain of the new site.

### Images and Other Files

During cloning, all uploaded files associated with the parent site are copied over to the child site.  This includes images.

### Contact Information

Contact information is set in three places: the social media widgets at the top right and bottom right of the parent page, and the Contact Me page.

In WordPress, widgets are defined in the `wp_XX_options` table, where XX is the blog ID of the site in question.  The social media widget we use is stored in the `widget_social-widget` option as a serialized array of widget instances.

The Contact Me page, like all WordPress pages, is actually technically a post, and can be found in the `wp_XX_posts` table.  Pages in WordPress are posts with the `post_type` set to `page`.  These are stored in the database in the `wp_XX_posts` table.  Content is updated by setting the `post_content` parameter.

Details about the structure of posts in WordPress can be found [in the Codex.](http://codex.wordpress.org/Class_Reference/WP_Post).