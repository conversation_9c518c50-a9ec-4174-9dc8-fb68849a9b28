<?php

/* TODO List

	don't duplicate images in DB
	run for time limit
	get actual rows modified from Zend DB adapter for "update"
	need to use correct type for ID field in WHERE clause
	fix log update
	Get rid of old images, don't store images twice
	Don't refetch images unless picture timestamp is newer
	grab new props first, then get by oldest update
	Fix primary key on table: use same key as RETS
	for Media table, use timestamp in XML return data before fetching data
	merge in field updates with script
	cron:  security/.htaccess on web version, or just convert to CLI only
	merge property field fetch w/ properties
	property & image fetch: make it atomic
	print status every 100 props
	command line version
	speed fix: pull data for TREND with GetObject, and only get description from Media table
	debug prints for much more stuff, debug flag/option
	move stuff in to constructor
	add stats:  time per property, time per image
	Show last 20 logs in RETS log
	RETS Sync log: fix height of log items
	Fix error message sent by RETSHelper
*/

/**
 * File with base class with methods for reading the config.ini file and getting file system paths for images
 */
require_once('IDXBase.php');

/**
 * File containing class & function for basic profiling information & logging of elapsed time
*/
require_once('TimeLog.php');

use Profound\MLS\MlsClass;
use Aws\S3\S3Client;

use Aws\Exception\AwsException;
use Aws\S3\Exception\S3Exception;

define('ZERO_RECORDS_ERROR', 20201);

/**
 * Basic class with methods for duplicating data from a RETS server based on settings in a config.ini file
*/
class RETSHelper extends IDXBase
{
	/**
	 * @var File pointer for the lock file
	 */
	private $lock;
	private $lockfile;
	private $limit = 0;
	private $images = 0;
	private $batch = 50;
	private $msg = '';
	private $offset_support;
	private $rets_version;
	private $tablename;
	private $daysago;

	// We use Chicago because the Flexmls servers we connect to are in that timezone. If we ever connect to servers
	// outside of this timezone, we'll need to change this, or add timezone to the config for each MLS, etc.
	private static $flexmls_timezone_str = 'America/Chicago';
	private static $utc = null;
	private static $retsDateTimeFormat = 'Y-m-d\TH:i:s';

	public static function static_init() {
		static::$utc = new DateTimeZone('UTC');
	}

	/**
	 * Set the MLS based on the $_GET parameter from the URL
	 * @param $configfile
	 */
	public function __construct($configfile, $mls, $class) {
		$this->config = $this->readConfigs($configfile);
		$this->initDB($this->config['retsdb']);

		if (isset($this->config['google']['api_key']))
			$this->apikey = $this->config['google']['api_key'];

		if (!empty($this->config[$mls]['s3_bucket'])) {
			$region = isset($this->config[$mls]['s3_region']) ? $this->config[$mls]['s3_region'] : "us-west-1";
			// The home environment isn't set when the sync runs from the php image. We store our aws creds in our home path
			// Not dry fix if needed later
			putenv('HOME=/home/<USER>');

			$this->s3Client = S3Client::factory(array(
				'profile' => 'default',
				'version' => '2006-03-01',
				'region' => $region,
				'scheme' => 'http' //The images are public anyway. We save some time and resources
			));
		}

		$this->mlsname = $mls;
		$this->access_array['mls'] = $mls;
		$this->class = $class;
		$this->daysago = array_key_exists('daysago', $this->config[$mls]) ? $this->config[$mls]['daysago'] : -1;

		$this->lockfile = "rets.{$this->mlsname}.{$this->class}.lock";
		$this->offset_support = $this->config[$mls]["offset_support"];
		$this->rets_version = $this->config[$mls]["rets_version"];

		if(!empty($class)) {
			$this->mls_class = MlsClass::getGenericMlsClassName($mls, $class);

			$this->mls = (object) $this->config[$mls];
			$this->mls->mls_class = $this->mls_class;

			$solr_host = "solr";
			$solr_port = 80;

			if (isset($this->config['solr']['host']))
				$solr_host = $this->config['solr']['host'];

			if (isset($this->config['solr']['port']))
				$solr_port = $this->config['solr']['port'];

			$options = array
				(
					'hostname' => $solr_host,
					'port'	=> $solr_port,
					'path'	=> "solr/{$mls}_{$this->mls_class}"
				);

			// Instantiate a Solr Client object, for add/update/delete on Solr
			$this->solrClient = new SolrClient($options);
		}

	}

	/**
	 * Remove the lock file when the script finishes
	 */
	public function __destruct() {
		if (!$this->lock) return;

		if ($this->rets) {
			$this->rets->Disconnect();
		}

		fclose($this->lock);
		if (!@unlink($this->lockfile)) {
			$error_message = print_r(error_get_last(), true);
			$this->log($error_message);
		}
	}

	/**
	 * Log function to capture message for email notification, as well as print to STDOUT
	 */
	private function log($msg) {
		echo "$msg\n";
		$this->msg .= "$msg\n";
	}

	/**
	 * Wrap timelog() function to capture message for email notification, as well as print to STDOUT
	 */
	private function timelog($msg) {
		timelog($msg);
		$this->msg .= "$msg\n";
	}

	private function ensureLock() {
		// Check for existing versions of the script
		if (!$this->getLock()) {
			if ($this->lockTooOld()) {
				echo "Lock file too old.\n";
				$message = "Please check the server.  The RETS scripts may not be running properly.";
				$this->sendEmail('action_log', 'Lock file too old: ' . strtoupper($this->mlsname) . '-' . $this->class, $message);

				# TODO: This is rather hacky
				// Disable notification for 3 hours
				touch($this->lockfile);
			}
			die("Could not obtain a lock for MLS: {$this->mlsname}\n");
		}
	}

	/**
	 * Run a command
	 */
	public function runCommand($options = array()) {
		$vars = array(
			'cmd' => '',
			'limit' => 9999999,
			'images' => 1,
			'id' => null,
			'qty' => 0,
			'lookups' => 1,
			'cache' => false,
			'batch' => 50,
			'deletion_limit' => 20000,
			'schema_only' => false,
			'daysago' => -1,
			'incremental',
		);
		foreach ($vars as $name => $default) {
			$$name = isset($options[$name]) ? $options[$name] : $default;
		}

		$this->limit = $limit;
		$this->images = $images;
		$this->batch_size = $batch;
		$this->deletion_limit = $deletion_limit;
		$this->schema_only = $schema_only;

		if($this->daysago === NULL || $this->daysago === -1)
			$this->daysago = $daysago;

		error_reporting(E_ALL & ~ E_NOTICE);
		set_time_limit(0);

		$this->tablename = $this->mlsname . "_property_" . $this->class;

		$this->fieldstbl = $this->mlsname . "_fields_" . $this->class;

		$conf = $this->config[$this->mlsname];

		if(isset($conf['mls_class_all']))
			$this->class = $conf['mls_class_all'];

		switch ($cmd) {
			case 'props':
				$this->ensureLock();
				$update_options = [
					'cache'                 => $cache,
					'do_incremental_update' => $options['incremental'] ?? false,
				];
				$this->updatePropsFromRETS($update_options);
				break;
			case 'office':
				$this->ensureLock();
				$resource_name = $conf['office_resource'];
				$db_table_infix = $conf['office_table_infix'];
				$id_fieldname = $conf['office_id'];
				$lastmod_fieldname = $conf['office_lastmod'];
				$action_field_id = 4;
				$this->updateResourceFromRETS($resource_name, $db_table_infix, $id_fieldname, $lastmod_fieldname, $action_field_id);
				break;
			case 'agent':
				$this->ensureLock();
				$resource_name = $conf['agent_resource'];
				$db_table_infix = $conf['agent_table_infix'];
				$id_fieldname = $conf['agent_id'];
				$lastmod_fieldname = $conf['agent_lastmod'];
				$action_field_id = 5;
				$opts = [];
				// if ($this->mlsname === 'brightmls') {
				//  $opts['cache'] = true;
				// }
				$this->updateResourceFromRETS($resource_name, $db_table_infix, $id_fieldname, $lastmod_fieldname, $action_field_id, $opts);
				break;
			case 'open-house':
				$this->ensureLock();
				$resource_name = $conf['open_house_resource'];
				$db_table_infix = $conf['open_house_table_infix'];
				$id_fieldname = $conf['open_house_id'];
				$lastmod_fieldname = $conf['open_house_lastmod'];
				$action_field_id = 6;
				$options = [
					'event_end_fieldname' => $conf['open_house_event_end'],
				];
				$this->updateResourceFromRETS($resource_name, $db_table_infix, $id_fieldname, $lastmod_fieldname, $action_field_id, $options);
				break;
			case 'images':
				$this->fetchMissingImages();
				break;
			case 'fields':
				$this->updateResPropFields();
				if ($lookups) {
					$this->updatePropLookupOptions();
				}
				break;
			case 'dump':
				$this->getExcelDump($qty);
				break;
			case 'verify':
				$this->updateMemberStatuses();
				break;
			case 'view':
				$this->viewProperty($id);
				break;
			case 'force-update-listings':
				$this->forceUpdatingListings($id);
				break;
			case 'manual-search':
				$this->manualSearch();
				break;
			case 'audit-bad-fields':
				$this->audit_bad_fields();
				break;
			default:
				echo "Unknown command: $cmd\n";
				break;
		}
	}

	/**
	 * Obtain an exclusive lock for this MLS
	 * @return bool
	 */
	private function getLock() {
		// Handy during dev when I'm not running real cron, but manually running things.
		// return true;

		if (file_exists($this->lockfile)) return false;

		$this->lock = fopen($this->lockfile, "w+");
		return flock($this->lock, LOCK_EX);
	}

	/**
	 * Check whether the lock file is older than 3 hours
	 * @return bool
	 */
	private function lockTooOld() {
		if (file_exists($this->lockfile)) {
			return time() - filemtime($this->lockfile) > 3600 * 3;
		}
		return false;
	}

	/**
	 * Create a new log entry in the database for the script
	 *
	 * @param $id ID of the command being run
	 * @param $field Text name of the command (redundant)
	 * @param $notes Notes for this log entry
	 * @param $enddate Action end date
	 * @return mixed
	 */
	public function newActionLog($id, $field, $notes, $status = 0, $enddate = '') {
		$startdate = date("U");

		# TODO: fix the database.  It is not normalized properly.  action_field_id --> action_field
		$values = array(
			'action_field_id' => $id,
			'action_field' => $field,
			'action_status' => $status,
			'action_notes' => $notes,
			'action_startdate' => $startdate,
			'mls' => $this->mlsname,
		);

		if (! empty($enddate)) {
			$values['action_enddate'] = $enddate;
		}

		$this->db->insert('action_log', $values);

		return $this->db->lastInsertId();
	}

	/**
	 * Create a database table to store the RETS data
	 * @param $tblname
	 * @param $prefix
	 */
	private function createDbTable($tblname, $prefix) {
		# FIXME: add the keys/indexes to the table as well
		try {
			$this->db->describeTable($tblname);
		} catch (Throwable $e) {
			$sql = "
			CREATE TABLE $tblname (
			{$prefix}_enterdate INTEGER NULL
			) ENGINE = InnoDB;
			";
			$this->db->query($sql);
		}
	}

	/**
	 * Create a property table
	 * @param $tblname
	 */
	private function createPropertyTable($tblname) {
		$this->createDbTable($tblname, 'property');
	}

	/**
	 * Create a table for the property images
	 * @param $tblname
	 */
	private function createImageTable($tblname) {
		$this->createDbTable($tblname, 'image');
	}

	// We just realized some of our fields are being rounded when they shouldn't be. This will help us identify them and
	// change the ones we want. This is mainly a debug tool and is meant to be run manually, not via cron.
	private function audit_bad_fields() {
		$conf = $this->config[$this->mlsname];
		$rets_table = $conf['prop_table'];
		$rets_class = $this->class;
		$this->setupRETSConnection($conf);
		$fields = $this->rets->GetMetadataTable($rets_table, $rets_class);
		$table = $this->tablename;

		$described_fields = $this->db->fetchAll("DESCRIBE $table");
		$typemap = array(
			'Boolean'   => 'CHAR(1)',
			'Character' => 'VARCHAR',
			'Tiny'      => 'INT',
			'Small'     => 'INT',
			'Long'      => 'BIGINT',
			'Date'      => 'DATETIME'
		);

		$described_fields_by_name = [];
		foreach ($described_fields as $described_field) {
			$described_fields_by_name[$described_field['Field']] = $described_field;
		}

		foreach ($fields as $field) {
			$type = $field['DataType'];
			if (array_key_exists($type, $typemap)) {
				$type = $typemap[$type];
			}
			$len = $field['MaximumLength'];

			if ($type == 'VARCHAR' && $len > 255) {
				$type = 'TEXT';
			}
			if ($type == "Decimal") {
				$prec = !empty($field['Precision']) ? $field['Precision'] : 0;
				$type = "DECIMAL($len,$prec)";
			}

			// Add the field size
			if (in_array($type, array('VARCHAR', 'INT', 'BIGINT'))) {
				// Convert decimal size in to binary
				$type .= "($len)";
			}

			if ($field['LookupName']) {
				$type = 'VARCHAR(127)';
			}

			$described_field = $described_fields_by_name[$field['SystemName']];
			$lc_type = strtolower($type);
			$lc_described_field_type = strtolower($described_field['Type']);
			if ($lc_type !== $lc_described_field_type) {
				$should_output = false;

				// This is what I needed in production the first time. Change this as needed to filter to the changes
				// you care about.
				if (strpos($lc_type, 'decimal') !== false || strpos($lc_described_field_type, 'decimal') !== false) {
					$should_output = true;
				}

				if ($should_output) {
					$sql = "ALTER TABLE $table CHANGE COLUMN `{$field['SystemName']}` `{$field['SystemName']}` $type"
						. " NOT NULL;";
					// The padding length of 135 is just a guess. Change it as needed to make things look good.
					$output = str_pad($sql, 135);
					$output .= "(old: {$described_field['Type']})";
					echo "$output\n";
				}
			}
		}
	}

	/**
	 * Add columns to a database table if they are not already present
	 * @param $table Name of database table
	 * @param $fields Columns to check for and add (if necessary)
	 */
	private function updateTableFields($table, $fields) {
		$conf = $this->config[$this->mlsname];

		$columns = $this->db->fetchCol("SHOW COLUMNS FROM $table");
		$typemap = array(
				'Boolean' => 'CHAR(1)',
				'Character' => 'VARCHAR',
				'Tiny' => 'INT',
				'Small' => 'INT',
				'Long' => 'BIGINT',
				'Date' => 'DATETIME'
		);

		// Number of new fields found on RETS server
		$new_fields_count = 0;
		$fields_added = array();

		$alter_table_queries_to_batch = [];
		foreach ($fields as $field) {
			// Skip any fields that are already present
			if (in_array($field['SystemName'], $columns)) continue;

			$type = $field['DataType'];
			if (array_key_exists($type, $typemap)) {
				$type = $typemap[$type];
			}
			$len = $field['MaximumLength'];

			if ($type == 'VARCHAR' && $len > 255) {
				$type = 'TEXT';
			}
			if ($type == "Decimal") {
				$prec = !empty($field['Precision']) ? $field['Precision'] : 0;
				$type = "DECIMAL($len,$prec)";
			}

			// Add the field size
			if (in_array($type, array('VARCHAR', 'INT', 'BIGINT'))) {
				// Convert decimal size in to binary
				$type .= "($len)";
			}

			#echo "T: {$field['SystemName']} : $type : $len\n";

			if ($field['LookupName']) {
				$type = 'VARCHAR(127)';
			}

			if ($field['SystemName'] == $conf['listing_id']) {
				$this->db->query('set session innodb_strict_mode=off');
				$this->db->query("ALTER TABLE $table ADD COLUMN `{$field['SystemName']}` $type NOT NULL");
				$this->db->query("ALTER TABLE $table ADD PRIMARY KEY (`{$field['SystemName']}`)");
				$this->db->query('set session innodb_strict_mode=on');
			} else {
				$alter_table_queries_to_batch[] = "ADD COLUMN `{$field['SystemName']}` $type NOT NULL";
			}

			$fields_added[$field['SystemName']] = $type;
			$new_fields_count++;
		}
		if ($alter_table_queries_to_batch) {
			$this->db->query('set session innodb_strict_mode=off');
			$sql = "ALTER TABLE $table " . join(', ', $alter_table_queries_to_batch);
			$this->db->query($sql);
			$this->db->query('set session innodb_strict_mode=on');
		}

		// Send a notification email if we found new fields
		if ($new_fields_count > 0) {
			$message = $new_fields_count . ' new field' . ($new_fields_count > 1 ? 's were' : ' was') . ' found for '. $table. ' table.' ;
			$message .= "\n\n";
			$message .= "The fields added were: \n\n";
			foreach ($fields_added as $name => $meta_info) {
				$message .= "{$name}: {$meta_info}\n";
			}
			$this->sendEmail('action_log', 'New Fields Found', $message);
		}

		$table_is_images_table = strpos($table, 'images') !== false;
		$should_notify_about_mls_field_removals = !$table_is_images_table;
		if ($should_notify_about_mls_field_removals) {
			$metadata_fieldnames = array_map(function ($x) {
				return $x['SystemName'];
			}, $fields);
			$deletes_to_ignore = [
				// Many tables
				'property_enterdate',
				'property_status',
				'open_house_enterdate',
				'office_enterdate',
				'agent_enterdate',

				// ARMLS rentals
				'EffectiveListPrice',
			];
			$deleted_fields = array_diff($columns, $metadata_fieldnames, $deletes_to_ignore);
			if ($deleted_fields) {
				$sql = "select * from field_mapping where mls_class = ?;";
				$res = $this->db->fetchAll($sql, [$this->mls_class]);
				$field_mapping_by_mls_field = array_reduce($res, function ($a, $v) {
					$a[$v[$this->mlsname]] = $v;
					return $a;
				}, []);
				$message = count($deleted_fields) . ' fields were removed from the MLS, based on table ' . $table;
				$message .= "\n\n";
				$message .= "The fields removed were: \n\n";
				foreach ($deleted_fields as $deleted_field) {
					$message .= "${deleted_field}\n";
					$field_mapping_text = "(not found)\n";
					$field_mapping = $field_mapping_by_mls_field[$deleted_field];
					if ($field_mapping) {
						$field_mapping_text = print_r($field_mapping, true) . "\n";
					}
					$message .= '    Field mapping: ' . $field_mapping_text;
				}
				$message .= "\n\nHere's the SQL:\n\n";
				$deleted_fields_drop_column = array_map(function ($v) { return 'DROP COLUMN ' . $v; }, $deleted_fields);
				$sql1 = "ALTER TABLE {$table} " . join(",\n", $deleted_fields_drop_column) . ';';
				$message .= $sql1;
				$message .= "\n\nDon't forget to remove from the table named {$this->fieldstbl}\n\n";
				$deleted_fields_name_list = array_map(function ($v) { return "'{$v}'"; }, $deleted_fields);
				$deleted_fields_name_list = join(",\n", $deleted_fields_name_list);
				$sql2 = "DELETE FROM {$this->fieldstbl} where SystemName in ($deleted_fields_name_list);";
				$message .= $sql2;
				$message .= "\n\nThere's also the matter of removing field mappings:\n";
				$message .= "(I'm putting a SELECT here, but you can change it to an UPDATE easily enough)\n\n";
				$sql3 = "SELECT * FROM field_mapping where mls_class = '{$this->mls_class}' and {$this->mlsname} in "
					. "({$deleted_fields_name_list});";
				$message .= $sql3;
				$message .= "\n\nNo need to delete lookups, as those get deleted (as part of the sync that runs) daily";
				$message .= "\n\nDon't forget to remove the data from Solr, update the schema, reload core, optimize.";
				$this->sendEmail('action_log', 'Fields removed from MLS', $message);
			}
		}
	}

	/**
	 * Update the action_log  with information about which tables & how many records were updated
	 *
	 * @param $action_id
	 * @param $mls
	 * @param string $notes
	 */
	public function updateActionLog($action_id, $mls, $notes = '') {

		$enddate = date("U");
		$values = array(
				'action_enddate' => $enddate,
				'action_status' => 1,
				'action_notes' => $notes,
				'mls' => $mls,
		);

		$this->db->update('action_log', $values, "action_id = " . $action_id);
	}

	/**
	 * Set up a new RETS connection
	 *
	 * @throws Exception If the connection failed
	 * @param $config Configuration parameters
	 */
	public function setupRETSConnection($config) {
		require_once(dirname(__FILE__) . '/../../lib/phrets.php');

		extract($config);

		$rets = $this->rets = new phRETS;
		$rets->SetParam("compression_enabled", true);
		$rets->SetParam("offset_support", $this->offset_support);

		if ($user_agent) {
			$rets->AddHeader("User-Agent", $user_agent);
		}
		$rets->AddHeader("RETS-Version", "RETS/$this->rets_version");

		$connect = $rets->Connect($login_url, $username, $password);
		if (!$connect) {
			$info = "\n\nURL: $login_url\nUser: $username\n\n";
			throw new Exception("Could not connect to RETS Server: $info" . print_r($rets->Error(), true));
		}

	}

	// Debug example usage:
	//   PHP_IDE_CONFIG="serverName=idx" php includes/classes/rets.php --mls sdcrca --class RE_1 --cmd view --id 200033378
	public function viewProperty($id) {
		$mls = $this->mlsname;
		$conf = $this->config[$mls];
		$rets_table = $conf['prop_table'];
		$listid = $conf['mls_listing_id_name'];

		$this->setupRETSConnection($conf);

		list($listing) = $this->rets->Search($rets_table, $this->class, "($listid=$id)");
		// Sort naturally. See http://stackoverflow.com/a/12846109/135101.
		uksort($listing, function($a,$b){
			return strnatcmp($a,$b);
		});
		print_r($listing);
	}

	// Reminder about the $id(s): The $rets->Search() code apparently works with either the Listing ID (LIST_105 in ARMLS) or the
	// Internal Listing ID (LIST_1 in ARMLS). But we need the Internal Listing ID for using in our ${mls}_images
	// table's Content-ID field.
	// So make sure you use the Internal Listing ID.
	// I've tried so far with ARMLS and CRMLS.
	// If you don't know the Internal Listing ID, an easy way to get it with RETS systems is to use our viewProperty
	// method above.
	// UPDATE: Apparently some MLSs don't have such a field, so it doesn't apply. E.g. SDCRCA.
	//
	// $ids can be a single value or multiple if separated by commas.
	public function forceUpdatingListings($ids) {
		$mls = $this->mlsname;
		$conf = $this->config[$mls];
		$rets_table = $conf['prop_table'];
		$rets_class = $this->class;
		$listid = $conf['mls_listing_id_name'];

		$this->setupRETSConnection($conf);

		$ids_array = explode(',', $ids);
		$this->updatePropSet([], $listid, $ids_array, $rets_table, $rets_class);
	}

	/**
	 * Check if a table on RETS server has updated rows.
	 *
	 * @param array  $propinfo   Property from RETS server
	 * @param array  $dbprops    Local database properties
	 * @param int    $id         Property ID in local database
	 * @param string $lastmod    Name of last modification date column in RETS server table
	 *
	 * @return bool              Return true if data has changed (or is new) or false if it hasn't
	 */
	public function haveUpdatedProps($propinfo, $dbprops, $id, $lastmod, $picmod = "") {
		if (isset($dbprops[$id])) {
			// Caio: Only for GLVAR for now
			if (!empty($picmod) && (($this->mlsname == "glvarnv") || ($this->mlsname == "trendmls"))) {
				$picmoddate = $dbprops[$id][$picmod];

				if ($picmoddate == "0000-00-00 00:00:00")
					$picmoddate = "";

				if (strtotime($propinfo[$picmod]) - strtotime($picmoddate) > 0) {
					return true;
				}
			}

			if (strtotime($propinfo[$lastmod]) - strtotime($dbprops[$id][$lastmod]) <= 0) {
				return false;
			}
		}
		return true;
	}


	private function lastmod($daysago, $lastmod) {
		$date = new DateTime();
		$date->sub(new DateInterval("P" . $daysago . "D"));
		return "($lastmod=" . $date->format("Y-m-d\TH:i:s\+)");
	}

	/**
	 * Update tables with data from the RETS server
	 * This method update one of the tables depending on the class argument sent to the script at runtime
	 *
	 * Add in columns for any fields that are new.  Send email if there is an error or if a previous run
	 * has not finished or updated the action_log.
	 */
	public function updatePropsFromRETS($options = array()) {

		// During times when we want to resync the entire dataset from RETS,
		// it's quicker to pull down the data and stuff it in a file
		// and then read from that, rather than pulling it down each sync,
		// because it takes a good 10 seconds. However, in less dire
		// circumstances, it's best to fetch the latest data. Anyway, I've left
		// this code here (the cache stuff) for convenience.
		$cache_RETS_timestamps = isset($options['cache']) ? $options['cache'] : false;
		$do_incremental_update = $options['do_incremental_update'];

		$mls = $this->mlsname;

		$conf = $this->config[$mls];

		if (!$conf) {
			throw new Exception("Could not get configuration section '$mls''");
		}

		$limit = $this->limit;
		if ($limit == 0 || $limit == null) {
			echo "Limit should more then 0(zero). ";
			exit;
		}
		$rets_table = $conf['prop_table'];
		$rets_class = $this->class;
		$listid = $conf['listing_id'];
		$lastmod = $conf['lastmod'];
		$picmod = $conf['picture_mod'];
		$status_field = $conf['status_field'];

		$nomail = false;

		$total_count = $img_count = $new = $updated = $toupdate = $img_new = $deleted = 0;
		$dbprops = $data = $found = array();

		$errmsg = '';

		$msg = "Currently processing (active but without param offset support).";

		$logid = $this->newActionLog(3, 'property', $msg);

		if (extension_loaded('newrelic')) {
			newrelic_start_transaction("RETS Sync");
			newrelic_name_transaction("Update Properties");
		}

		try {
			$this->setupRETSConnection($conf);

			$rets = $this->rets;

			timelog();

			$this->log("\nTable updates & initialization");
			// Create the databse table if it doesn't exist
			$this->createPropertyTable($this->tablename);

			// This is comment 8759875687 that I reference elsewhere in this file.
			// We used to sync the table schema on every run. Now we don't because FBS/Flexmls suggested it might be why
			// we were seeing so many 500-level errors to our requests. Theoretically we should know in advance when
			// schema changes will come and we can uncomment these lines temporarily in anticipation.
			// This means this idea of "grooming the RETS data", code that I wrote 10 years ago, won't have any effect,
			// but it hasn't run for a few weeks now and I haven't noticed a problem.
			// See: https://ifoundagent.teamwork.com/app/tasks/37792221
			// Check to see if the field exists and create them if not.
			// if ($this->mlsname === 'naar') {
			// 	$fields = $this->rets->GetMetadataTable($rets_table, $rets_class);
			// 	$this->updateTableFields($this->tablename, $fields);
			// }
			// $this->initRETSDataGrooming($fields);

			if ($this->images) {

				$this->setupImageTable();
			}
			$this->timelog("Table creation & update from metadata");

			if($this->schema_only)
				die(0);

			// Get the property keys and last modification times from the database
			$mysql_format = 'Y-m-d H:i:s';
			$oldest_datetime_to_use = '1980-01-01 00:00:00';
			$lastmod_datetime = DateTimeImmutable::createFromFormat($mysql_format, $oldest_datetime_to_use,
				static::$utc);
			// Bright doesn't seem to like me reaching back very far. The server just doesn't respond. One time it spit
			// out an error saying the query was too complex or something, but usually it just never responds, even
			// after hours. It seems to work with 2021-07-01, currently. However, perhaps a few months from now, that'll
			// be too far back? And I might have to move this date out from time to time. I guess we'll see. Be advised
			// that when I use 2021-07-01, it will sit there for several minutes before returning the first page of
			// IDs, and then each subsequent page takes like 10-20 seconds.
			// Update: let's just subtract 667 days, which is approximately how many days between now as I write this to
			// 2021-07-01, so hopefully it will always work. As an FYI, yesterday it took 14-16 minutes for Bright to
			// return the complete list of IDs. It seems to report exactly 500,000 results, which seems fishy.
			// Update 2: 2 years is working now, as of 2023-10-16, as long as we download the full sync data in batches
			// of 15 days (previously we were using 6 months).
			if ($mls === 'brightmls' && $this->mls_class === 'res') {
				$now_utc = $this->get_now_utc();
				$lastmod_datetime = $now_utc->sub(new DateInterval('P2Y'));
			}
			$dbprops = array();
			$lastmod_datetime_from_mysql = null;
			if ($do_incremental_update) {
				// Reminder: I'm not fetching any records from the database simply to avoid the time spent doing so,
				// which for ARMLS A currently takes almost 3 minutes in production. A consequence of this is that
				// later we can't know whether an insert or update is needed, and thus we must do an "upsert", at which
				// point we can't tell how many records were inserted vs updated.
				$lastmod_datetime_from_mysql = $this->db->fetchOne("SELECT MAX({$lastmod}) from {$this->tablename}");
				// Handy for initial sync during dev
				// $lastmod_datetime_from_mysql = '2023-01-01 12:00:00';
				if ($lastmod_datetime_from_mysql) {
					$lastmod_datetime = DateTimeImmutable::createFromFormat($mysql_format, $lastmod_datetime_from_mysql,
						static::$utc);
				}
				$lastmod_datetime = $lastmod_datetime->add(new DateInterval('PT1S'));
				$this->timelog("Incremental update - no need to fetch properties from DB");
			} else {
				$this->log("\nFetching properties from MySQL database");
				$sql = "SELECT {$listid},{$lastmod},{$picmod} FROM {$this->tablename}";
				$rows = $this->db->fetchAll($sql);
				foreach ($rows as $prop) {
					$id = (string) $prop[$listid];
					$dbprops[$id] = $prop;
				}
				$this->timelog("Retrieval of properties from DB");
			}

			// Cache file
			$file = "rets_{$mls}_{$rets_class}_timestamps.json";

			// Either get the list of properties to sync from a cache file, or query RETS
			if ($cache_RETS_timestamps && file_exists($file)) {
				$data = json_decode(file_get_contents($file), true);
				if ($do_incremental_update) {
					$data = array_values(array_filter($data, function($x) use ($lastmod_datetime_from_mysql) {
						return $x[$lastmod] > $lastmod_datetime_from_mysql;
					}));
				}
				timelog("Loading RETS properties info from local cache");
			} else {
				$this->log("\nFetching properties from RETS");

				// Add one second because RETS only allows greater than or equal to, not greater than.
				// $lastmod_datetime_rets = $lastmod_datetime->add(new DateInterval('PT1S'));
				$lastmod_datetime_rets = $lastmod_datetime;

				// Reminder: I thought our datetimes in the DB were GMT, so I needed to account for the server's
				// offset, but it looks like we're just sticking the returned datetimes straight into the DB. So I'm
				// keeping this here just as a reminder of how to do it if ever needed.
				// $server_info = $rets->GetServerInformation();
				// $lastmod_datetime_rets = $lastmod_datetime_rets->setTimezone(
				// 	new DateTimeZone($server_info['TimeZoneOffset']));

				$non_lastmod_filters = [];

				// Only CRMLS has daysago configured in default.ini, and we don't do CRMLS via RETS anymore.
				// if (!empty($this->daysago) && $this->daysago >= 0) {
				// 	$query = $this->lastmod($this->daysago, $lastmod);
				// }

				// We don't currently suport GLVARNV so I've commented this out, saving it for posterity.
				// if ($mls == "glvarnv") {
				// 	// C -> Contingent Offer
				// 	// P -> Pending Offer
				//
				// 	// We are dealing only with residential properties
				// 	// that's why we have PropertyType=RES. Otherwise we
				// 	// would have to use the following table, based on the class:
				// 	// RES -> Residential
				// 	// RNT -> Rental
				// 	// LND -> Land
				//
				// 	// FIXME: GLVAR does not support the daysago parameter
				// 	$query = "($status_field" . '="A","P","C"),(PropertyType=RES)';
				// }

				// Don't sync cancelled listings for the MLSs that provide them (PAARAZ and CABOR at this time).
				// Reminder: the value is 'Cancelled' in RETS, as opposed to 'Canceled'.
				if ($mls === 'paaraz' && $rets_class === 'A') {
					$non_lastmod_filters[] = '(LIST_15=~15614ASF52L8)';
				} else if ($mls === 'paaraz' && $rets_class === 'F') {
					$non_lastmod_filters[] = '(LIST_15=~157XYFBYYF1K)';
				} else if ($mls === 'paaraz' && $rets_class === 'B') {
					$non_lastmod_filters[] = '(LIST_15=~1574JK4YTB5N)';
				} else if ($mls === 'cabor' && $rets_class === 'A') {
					$non_lastmod_filters[] = '(LIST_15=~12IDU883SHGW)';
					// Deleted
					$non_lastmod_filters[] = '(LIST_15=~12IDU883UMBV)';
					// Expired
					$non_lastmod_filters[] = '(LIST_15=~12IDU883O619)';
				} else if ($mls === 'cabor' && $rets_class === 'B') {
					$non_lastmod_filters[] = '(LIST_15=~12KGNK8LTZHO)';
					// Deleted
					$non_lastmod_filters[] = '(LIST_15=~15243MUZJ0Y5)';
					// Expired
					$non_lastmod_filters[] = '(LIST_15=~12KGNK8LRFRJ)';
				} else if ($mls === 'cabor' && $rets_class === 'C') {
					$non_lastmod_filters[] = '(LIST_15=~12IKXQ03AI8Z)';
					// Deleted
					$non_lastmod_filters[] = '(LIST_15=~15243MUZOE4U)';
					// Expired
					$non_lastmod_filters[] = '(LIST_15=~12IKXQ0382WL)';
				}

				// We don't need closed listings for rentals.
				if ($mls === 'brightmls' && $this->mls_class === 'rentals') {
					$non_lastmod_filters[] = '(MlsStatus=~200004325492)';
				}

				$opts = array(
                    'Select' => "$listid,$lastmod,$picmod"
				);

				if (($mls == "glvarnv") || ($mls == "sdcrca")) {
					$opts["Limit"] = "None";
				}

				if ($mls == "trendmls" || $mls == "brightmls") {
					$opts["Limit"] = "10000";
				}

				$need_to_batch = false;
				// Bright has a max of 500k results. So we're going to specify a max ModificationTimestamp of ~1 year
				// past $lastmod_datetime, and we'll have to loop until we get to today. Because there aren't nearly
				// 500k listings for rentals and land, we only do this for res, and only for a non-incremental sync.
				if ($mls === 'brightmls' && $this->mls_class === 'res' && !$do_incremental_update) {
					$need_to_batch = true;
				}
				$data = $this->SearchRetsProps($rets_table, $rets_class, $non_lastmod_filters, $opts,
					$need_to_batch, $lastmod_datetime, $lastmod, $listid, $this->tablename);

				if ($cache_RETS_timestamps) {
					$content = json_encode($data);
					file_put_contents($file, $content);
				}
				timelog("Fetch of property IDs & modification dates from RETS");
			}

			$error = $rets->Error();
			if ($error) {
				if ($error['type'] == 'xml') {
					$nomail = true;
					unset($error['text']);
				}
				if ($error["code"] != ZERO_RECORDS_ERROR) {
					$this->log(print_r($error, true));
					throw new Exception("RETS Error: " . print_r($error, true));
				}
			}

			$this->log("Number of properties in {$mls}-{$rets_class} RETS: " . count($data));

			$this->log("\nGetting number of modified properties ...");

			// Fetch any properties that are missing or have been modified
			$ids_to_update = array();
			foreach ($data as $propinfo) {
				$id = $propinfo[$listid];
				$found[$id] = 1;

				if (!$this->haveUpdatedProps($propinfo, $dbprops, $id, $lastmod, $picmod)) {
					continue;
				}

				$toupdate++;

				if ($limit && $total_count >= $limit) continue;

				$ids_to_update[] = $id;

				$total_count++;
			}

			$this->propmap = $found;

			timelog("Retrieved IDS for properties to update");

			$this->log("\nProperties to update: " . count($ids_to_update));

			// Fetch & process the properties in batches
			$batch_size = $this->batch_size;

			$processed = 0;
			for ($i = 0; $i < count($ids_to_update); $i += $batch_size) {
				$batch_ids = array_slice($ids_to_update, $i, $batch_size);
				$this->updatePropSet($dbprops, $listid, $batch_ids, $rets_table, $rets_class);

				$processed += count($batch_ids);
				$this->log("\nTotal properties updated: " . $processed);
				timelog();
			}

			$images_table_exists = $this->doesTableExist($mls . "_images");

			if ($do_incremental_update) {
				$this->log("\nThis is an incremental update - will not delete any listings");
			} else {
				try {
					$this->db->beginTransaction();

					$to_delete = 0;
					$ids_to_delete = array();
					foreach (array_keys($dbprops) as $id) {

						if ($found[$id]) continue;
						$ids_to_delete[] = $id;
						$to_delete++;
					}
					$this->log("\nDeleting properties: " . $to_delete);

					$deletion_limit = $this->deletion_limit;
					if ($this->mlsname === 'brightmls') {
						$deletion_limit = 100000;
					}
					if ($to_delete >= $deletion_limit) {
						$this->log("\nWe were going to delete {$to_delete} properties, the limit is {$deletion_limit}\n");

						$this->log("\n" . print_r(array_slice($ids_to_delete, 0, 10), true) . "\n");
						throw new Exception("We were going to delete {$to_delete} properties, the limit is {$deletion_limit}");
					}

					// Delete any properties from the DB and associated images if the property wasn't found in RETS
					foreach (array_keys($dbprops) as $id) {

						if ($found[$id]) continue;

						$where = array("`$listid` = ?" => $id);

						// Delete from MySQL
						$this->db->delete($this->tablename, $where);
						if ($images_table_exists) {
							$where_images = array("`Content-ID` = ?" => (string)$id);
							$this->db->delete($mls . '_images', $where_images);
						}

						if (!empty($id))
							// Delete from Solr
							$updateResponse = $this->solrClient->deleteById($id);

						$deleted++;
					}

					$this->db->commit();

					// Commit changes to Solr
					$this->solrClient->commit();
				} catch (Throwable $e) {
					$this->solrClient->rollback();
					$this->db->rollBack();
					throw new Exception($e);
				}
				$this->timelog("Deletion of properties");
			}

			if (extension_loaded('newrelic')) {
				newrelic_end_transaction();
			}

		} catch (Throwable $e) {
			$errmsg = $e->getMessage();
			$logmsg = $errmsg . "\n" . $e->getTraceAsString();

			if (extension_loaded('newrelic')) {
				newrelic_end_transaction();
				newrelic_notice_error($errmsg, $e);
			}
			$this->log($errmsg);
		}


		// Print some stats
		$this->log("\nPrevious number of properties in DB: " . count($dbprops));
		if ($deleted) $this->log("Deleted $deleted old properties");
		$this->log("Properties left to update: " . ($toupdate - $total_count) . "\n");

		// If there was an error, send an email notification to the admins and write to the log file
		if ($errmsg && !$nomail) {
			$this->sendEmail('action_log', "Failure updating properties on " . strtoupper($this->mlsname) . '-' . $this->class, $this->msg);
		}

		$this->updateActionLog($logid, $mls, $this->msg);
	}

	// Just pass through the call to phrets->Search(), except for Bright, where we must batch 500k at a time. Bright has
	// a fun fact that if you try to get too many listings, it'll just never respond. So we actually batch a year at a
	// time or 500k records, whichever comes first.
	private function SearchRetsProps($rets_table, $rets_class, $non_lastmod_filters, $opts,
		$need_to_batch, $lastmod_datetime, $lastmod, $listid, $tablename
	) {
		$data = [];
		$date_interval = new DateInterval('P15D');
		$max_allowed_per_batch = 500000;
		$server_timezone = null;

		do {
			$lastmod_datetime_rets_formatted = $lastmod_datetime->format(static::$retsDateTimeFormat);
			// The '+' at the end means "greater-than-or-equal to"
			$lastmod_datetime_filter = "($lastmod={$lastmod_datetime_rets_formatted}+)";
			$filters = [$lastmod_datetime_filter];
			$max_datetime = $lastmod_datetime->add($date_interval);
			if ($need_to_batch) {
				if ($this->is_datetime_in_the_future($max_datetime)) {
					$max_datetime = $this->get_now_utc();
				}
				$max_datetime_str = $max_datetime->format(static::$retsDateTimeFormat);
				// The 'minus' at the end means less-than-or-equal
				$max_datetime_filter = "({$lastmod}={$max_datetime_str}-)";
				$filters[] = $max_datetime_filter;
			}
			foreach ($non_lastmod_filters as $non_lastmod_filter) {
				$filters[] = $non_lastmod_filter;
			}
			$this->debug('Filters used: ' . print_r($filters, true));
			$query_str = join(',', $filters);
			$temp_data = $this->rets->Search($rets_table, $rets_class, $query_str, $opts);
			$error = $this->rets->Error();
			if ($error) {
				if ($error['type'] == 'xml') {
					$nomail = true;
					unset($error['text']);
				}
				if ($error["code"] != ZERO_RECORDS_ERROR) {
					$this->log(print_r($error, true));
					throw new Exception("RETS Error: " . print_r($error, true));
				}
			}
			$this->debug('Received ' . count($temp_data) . ' records from the server');
			// The response data's order (at least from Flexmls) is undefined. It would be foolish of us to use an
			// undefined order. Example of why that would be dumb: say our most recent record was Jan 1, and it's
			// June 1. If the first record we insert into the database was March 1, and then an error occurred such
			// that further records were not processed, that means the next time we do an incremental update, we'd
			// assume we have all listings up until March 1, and therefore we'd have a huge hole in our data from
			// Jan 1 to March 1.
			usort($temp_data, function($a, $b) use ($lastmod) {
				return $a[$lastmod] <=> $b[$lastmod];
			});

			if ($need_to_batch) {
				$did_we_receive_the_max_allowed = count($temp_data) >= $max_allowed_per_batch;
				if (!$server_timezone) {
					$server_timezone = $this->fetch_server_timezone();
				}

				// There's some bug in the results returned from Bright where they'll return records that are not within
				// the datetime range I specified. Or sometimes there's no timestamp at all. We need to filter those
				// out.
				// UPDATE: I haven't seen the issue for a while, so I'll comment this out.
				//
				// $temp_data = array_values(array_filter($temp_data, function($record)
				// 	use ($lastmod, $server_timezone, $max_datetime)
				// {
				// 	$record_datetime = DateTimeImmutable::createFromFormat(static::$retsDateTimeFormat,
				// 		$record[$lastmod], $server_timezone);
				// 	// Bright also returns blank for some. :golfclap:
				// 	if (!$record_datetime) {
				// 		return false;
				// 	}
				// 	$b = $this->is_datetime1_in_the_past_of_datetime2($record_datetime, $max_datetime);
				// 	return $b;
				// }));
				// $this->debug('After filtering, we now have this many records: ' . count($temp_data));
				// if (!count($temp_data)) {
				// 	throw new Exception('Filtered out ALL records returned from batch.');
				// }

				foreach ($temp_data as $temp_datum) {
					$data[] = $temp_datum;
				}
				$this->debug('$data now has this many records: ' . count($data));
				if ($did_we_receive_the_max_allowed) {
					$last_record = $temp_data[count($temp_data) - 1];
					$lastmod_datetime = DateTimeImmutable::createFromFormat(static::$retsDateTimeFormat,
						$last_record[$lastmod], $server_timezone);
				} else {
					$lastmod_datetime = $lastmod_datetime->add($date_interval);
					if ($this->is_datetime_in_the_future($lastmod_datetime)) {
						$need_to_batch = false;
					}
				}
				// This process is expected to take a long time. Don't let MySQL hit the wait_timeout.
				$this->db->fetchOne("SELECT 1 FROM `$tablename` LIMIT 1");
			} else {
				$data = &$temp_data;
			}
		} while ($need_to_batch);

		return $data;
	}

	// This is primarily meant for dealing with "double pagination" during the initial sync with Bright and can be
	// removed later.
	private function debug($message) {
		// if ($this->mlsname === 'brightmls' && ($this->class === 'RESI' || $this->class === 'RLSE')) {
		// 	echo "**************************** $message\n";
		// }
	}

	private function fetch_server_timezone() {
		$server_info = $this->rets->GetServerInformation();
		$server_timezone_str = $server_info['TimeZoneOffset'];
		// For Bright, it's "Z-04:00". :facepalm: Take off the leading 'Z'.
		if (strlen($server_timezone_str) >= 2 && substr($server_timezone_str, 0, 2) === 'Z-') {
			$server_timezone_str = substr($server_timezone_str, 1);
		}
		$dateTimeZone = new DateTimeZone($server_timezone_str);
		return $dateTimeZone;
	}

	private function get_now_utc() {
		$now_utc = new DateTimeImmutable('now', static::$utc);
		return $now_utc;
	}

	private function is_datetime_in_the_future($datetime) {
		$now_utc = $this->get_now_utc();
		$unix_timestamp1 = $now_utc->getTimestamp();
		$unix_timestamp2 = $datetime->getTimestamp();
		$diff = $unix_timestamp2 - $unix_timestamp1;
		return $diff > 0;
	}

	private function is_datetime1_in_the_past_of_datetime2($datetime1, $datetime2) {
		$unix_timestamp1 = $datetime1->getTimestamp();
		$unix_timestamp2 = $datetime2->getTimestamp();
		$diff = $unix_timestamp2 - $unix_timestamp1;
		return $diff > 0;
	}

	public function updatePropSet($dbprops, $listid, $ids_to_update, $rets_table, $rets_class) {

		$this->log("\nUpdating " . count($ids_to_update) . " properties");

		$rets = $this->rets;
		$mls = $this->mlsname;

		# FIXME: Duplicated in other methods
		$conf = $this->config[$mls];
		$listid = $conf['listing_id'];
		$lastmod = $conf['lastmod'];
		$picmod = $conf['picture_mod'];


		$updated = $toupdate = $img_new = $deleted = 0;
		$ids_to_update_pics = array();

		$ids_string = implode(",", $ids_to_update);
		#echo "$rets_table, $rets_class, ($listid=$ids_string)";

		// if($mls == 'crmls') {
		// 	#the db taskes so long to pull that sometimes we need to ensure our rets connection
		// 	$this->setupRETSConnection($conf);
		// }

		$max_retries = 3;
		$listings = null;
		for ($i = 0; $i < $max_retries; $i++) {
			$listings = $rets->Search($rets_table, $rets_class, "($listid=$ids_string)");
			$error = $rets->Error();
			if ($error && $error['code'] == 408) {
				$this->log("\nRetrying RETS Search web request");
				continue;
			} else {
				break;
			}
		}
		if ($error = $rets->Error()) {
			echo "RETS Search Error\n";
			print_r($error);
			if ($error["code"] != ZERO_RECORDS_ERROR) {
				echo "Exiting\n";
				throw new Exception("RETS Error - Search: " . print_r($error, true));
			}
		}
		timelog("Fetched properties to update from RETS");
		foreach ($listings as $listing) {
			$listings_by_id[$listing[$listid]] = $listing;
		}

		// Get the list of properties with images to update
		foreach ($ids_to_update as $id) {
			$listing = $listings_by_id[$id];

			// Reminder: We could additionally check if the # of image
			// records we have is the same as the count given to us.
			if ($this->images) {
				if (!isset($dbprops[$id])) {
					$ids_to_update_pics[] = $id;
				} else {
					$timestamp = null;
					if (isset($dbprops[$id][$picmod])) {
						$timestamp = strtotime($dbprops[$id][$picmod]);
					}
					if ($timestamp) {
						$ids_to_update_pics[] = $id;
					}
				}
			}
		}

		// Fetch the images from RETS
		$photos = null;
		if ($this->images) {
			$ids_to_update_pics_string = join(",", $ids_to_update_pics);
			$photos = $this->fetchImages($ids_to_update_pics_string);
			$ids_to_update_pics_string_quoted = join(",", array_map(function($id) { return sprintf("'%s'", $id); }, $ids_to_update_pics));
		}

		list($new_or_updated, $img_count, $img_new) = $this->updateProperties($dbprops, $ids_to_update, $listings_by_id, $ids_to_update_pics_string_quoted, $photos);

		$this->log("Batch Summary:");

		$this->log("New or updated properties: $new_or_updated");
		if ($img_count) $this->log("- Fetched $img_count images\n- New images: $img_new");
	}

	private function updateProperties($dbprops, $ids_to_update, $listings_by_id, $ids_to_update_pics_string, $photos) {
		$img_count = $img_new = $new_or_updated = 0;
		$mls = $this->mlsname;

		# FIXME: Duplicated in other methods
		$conf = $this->config[$mls];
		$listid = $conf['listing_id'];
		$lastmod = $conf['lastmod'];
		$picmod = $conf['picture_mod'];

		$this->log("Updating properties in MySQL");

		// Use a transaction to ensure that listings and their images are always in sync.


		// if($mls == 'crmls') {
		// 	#the db taskes so long to pull that sometimes we need to ensure our rets connection
		// 	$this->setupRETSConnection($conf);
		// }
		try {
			$this->db->beginTransaction();

			$map = $this->getFieldMapping($this->mls_class);

			$field_names = null;
			$field_names_str = null;
			$placeholders = null;
			$update_values_str = null;
			foreach ($ids_to_update as $id) {
				$listing = $listings_by_id[$id];
				$listing = $this->groomRETSData($listing);

				$listing['property_enterdate'] = date("U");

				if (!empty($listing[$conf['listing_id']]) &&
					$this->shouldGeocode() &&
					(empty($listing[$map->Longitude]) ||
					 empty($listing[$map->Latitude]))) {
					$prop = $this->geocode($this->reverseMap($listing, false), false);
					if (!empty($prop["Latitude"]))
						$listing[$map->Latitude] = $prop["Latitude"];
					if (!empty($prop["Longitude"]))
						$listing[$map->Longitude] = $prop["Longitude"];
					if (!empty($prop["Longitude"]) || !empty($prop["Latitude"]))
						$this->log("------ Geocoded property: {$listing[$map->ListingKey]} {$listing[$map->ListingID]} {$prop['Latitude']} {$prop['Longitude']}");
				}

				if (!$field_names) {
					$field_names = array_keys($listing);
					$field_names_str = join(', ', $field_names);
					$placeholders = str_repeat('?, ', count($field_names) - 1) . '?';
					$update_values_str = join(', ', array_map(function($k) {
						return "$k=VALUES($k)";
					}, $field_names));
				}

				if (is_null($listing[$conf['listing_id']])) {
					$this->log("Empty listing {$id}");
					continue;
				} else if (isset($dbprops[ltrim($id, "0")]) || isset($dbprops[$id])
					|| !empty($listing[$conf['listing_id']])
				) {
					// Upsert
					$upsert_sql = "INSERT INTO {$this->tablename} ($field_names_str) values ($placeholders)"
						. " ON DUPLICATE KEY UPDATE $update_values_str";
					$bind = array_values($listing);
					$this->db->query($upsert_sql, $bind);
					$new_or_updated++;
				} else {
					$this->log("Empty listing {$id}");
					continue;
				}

				// Create a new Input Document for Solr
				$doc = new SolrInputDocument();

				// Add the fields to the document
				foreach($listing as $key => $value)
				{
					// Ignore those properties
					if ($key == "property_id" || $key == "property_enterdate" ||
					$key == "property_status") continue;

					// Ignore if it is any coordinate field
					if ($key == $map->Latitude || $key == $map->Longitude) continue;

					// If value is empty, uses the default value instead of the empty string. Otherwise, numbers will be broken for default values
					if ($value == "") continue;

					if ($value == "0000-00-00 00:00:00") {
						$value = null;
					}

					// In ARMLS A, this is a decimal, but we treat it as an int/long (did it change)? Anyway, I'm not
					// sure if this started today or how common it is, but I've seen it twice today and never before.
					// Values are coming in with decimal endings. The right thing to do here is to go change our MySQL
					// schema and Solr schema, but that'd take longer and no one has ever cared before, so let's just
					// force it to be an int.
					if ($key === 'FEAT20121130205704687554000000') {
						if (gettype($value) === 'string' && strpos($value, '.')) {
							$value = round(floatval($value));
						}
					}

					// Handle dates
					// We have to handle datetime fields, as well as date-only fields.
					$solrDateFormat = "Y-m-d\TH:i:s\Z";
                    $trendDateTimeFormat = 'Y-m-d H:i:s';
					$retsDateFormat = 'Y-m-d';
					if (($d = DateTime::createFromFormat(static::$retsDateTimeFormat, $value))) {
						$value = $d->format($solrDateFormat);
                    }
                    if (($d = DateTime::createFromFormat($trendDateTimeFormat, $value))) {
						$value = $d->format($solrDateFormat);
                    } else if (($d = DateTime::createFromFormat(static::$retsDateTimeFormat . ".u", $value))) {
						$value = $d->format($solrDateFormat);

                    } else if (($d = DateTime::createFromFormat($trendDateTimeFormat . ".u", $value))) {
						$value = $d->format($solrDateFormat);

                    } else if (DateTime::createFromFormat($retsDateFormat, $value)) {
						// Unless we manually set the time, it will use the current time.
						// See: http://php.net/manual/en/datetime.createfromformat.php#110385
						$dateWithTimeZeroed = $value . "T00:00:00";
						$d = DateTime::createFromFormat(static::$retsDateTimeFormat, $dateWithTimeZeroed);
						$value = $d->format($solrDateFormat);
					}

					if ($key === 'OnMarketDate' && $this->mlsname === 'brightmls' && $value === '#') {
						$value = '1999-09-09T00:00:00Z';
					}
					$doc->addField($key, $value);
				}

				if (!empty($listing[$map->Longitude]) &&
						!empty($listing[$map->Latitude])) {

					if (!((int)$listing[$map->Latitude] < 90 &&
					(int)$listing[$map->Latitude] > -90) ||
					!((int)$listing[$map->Longitude] < 180 &&
					(int)$listing[$map->Longitude] > -180)) {
						print_r('Error with property lat/lng. Geocoding...  ');
						print_r(' ListingId: '+$listing[$conf['listing_id']] + ' Lat: '+$listing[$map->Latitude]+ ' Long: '+$listing[$map->Longitude]);

						$prop = $this->geocode($this->reverseMap($listing, false), false);
						if (!empty($prop["Latitude"]))
							$listing[$map->Latitude] = $prop["Latitude"];
						if (!empty($prop["Longitude"]))
							$listing[$map->Longitude] = $prop["Longitude"];
						if (!empty($prop["Longitude"]) || !empty($prop["Latitude"]))
							$this->log("------ Geocoded property: {$listing[$map->ListingKey]} {$listing[$map->ListingID]} {$prop['Latitude']} {$prop['Longitude']}");

					}
					$doc->addField("location_0_coordinate", $listing[$map->Latitude]);
					$doc->addField("location_1_coordinate", $listing[$map->Longitude]);
					$doc->addField("location", $listing[$map->Latitude] . "," . $listing[$map->Longitude]);
				}

				$doc->addField("StreetAddress", implode(' ', array_filter([$listing[$map->StreetNumber], $listing[$map->StreetDirPrefix], $listing[$map->StreetName], $listing[$map->StreetSuffix], $listing[$map->UnitNumber]])));
				$doc->addField("FullAddress", implode(' ', array_filter([$listing[$map->StreetNumber], $listing[$map->StreetDirPrefix], $listing[$map->StreetName], $listing[$map->StreetSuffix], $listing[$map->UnitNumber], $listing[$map->City], $listing[$map->PostalCode], $listing[$map->Subdivision], $listing[$map->State]])));

				// Case 1390. Special case for ARMLS rentals. See original motivation in case 535.
				// We create the EffectiveListPrice ourselves.
				if ($this->mlsname == 'armls' && $this->mls_class == 'rentals') {
					$effectiveListPrice = $listing['LIST_22'] > 0 ? $listing['LIST_22'] : $listing['LIST_24'];
					if(empty($effectiveListPrice))
						$effectiveListPrice = $listing['LIST_25'];
					if(empty($effectiveListPrice))
						$effectiveListPrice = "0";
					$doc->addField('EffectiveListPrice', $effectiveListPrice);
				}

				// Add the document to Solr
				$this->solrClient->addDocument($doc);

			}
			timelog("Updated properties");

			// Fetch and sync the images for listings if flag was set
			if ($photos) {

				$this->db->delete($mls . '_images', "`Content-ID` IN ($ids_to_update_pics_string)");
				timelog("Delete images");
				list($num, $inew) = $this->saveImagesToDB($photos);
				$img_count += $num;
				$img_new += $inew;
				timelog("Update all images");
			}

			$this->db->commit();

			// Commit changes to Solr
			$this->solrClient->commit();

		} catch (Throwable $ex) {
			$this->solrClient->rollback();
			$this->db->rollBack();
			throw new Exception($ex);
		}

		// Output files for the history tracker to process.
		try {
			if (count($listings_by_id)) {
				// Reminder that we're outputting this in "RESO Web API" format (including field names), because our History
				// Tracker assumes it.
				$jsonData = [
					'value' => array_values(array_map(function($listing) use ($mls) {
						return [
							'ListingId' => $listing[$mls === 'brightmls' ? 'ListingId' : 'LIST_105'],
							'StandardStatus' => $listing[$mls === 'brightmls' ? 'StandardStatus' : 'LIST_15'],
							'ListPrice' => intval($listing[$mls === 'brightmls' ? 'ListPrice' : 'LIST_22']),
							'ClosePrice' => intval($listing[$mls === 'brightmls' ? 'ClosePrice' : 'LIST_23']),
							'ModificationTimestamp' => $listing[$mls === 'brightmls' ? 'ModificationTimestamp' : 'LIST_87'],
						];
					}, $listings_by_id)),
				];
				$dt = new DateTime("now", new DateTimeZone('UTC'));
				$timestampStr = $dt->format("Y-m-d-\T-H-i-s-v\Z");
				$filePath = $this->joinPaths($this->config['history_tracker']['data_root_dir'], $mls, $this->class, "listings_{$timestampStr}.json");
				$dir = dirname($filePath);
				if (!is_dir($dir)) {
					mkdir($dir, 0755, true); // true enables recursive creation
					if ($this->config['history_tracker']['chown'] ?? null) {
						chown($dir, $this->config['history_tracker']['chown']);
					}
					if ($this->config['history_tracker']['chgrp'] ?? null) {
						chgrp($dir, $this->config['history_tracker']['chgrp']);
					}
				}
				file_put_contents($filePath, json_encode($jsonData));
				if ($this->config['history_tracker']['chown'] ?? null) {
					chown($filePath, $this->config['history_tracker']['chown']);
				}
				if ($this->config['history_tracker']['chgrp'] ?? null) {
					chgrp($filePath, $this->config['history_tracker']['chgrp']);
				}
			}
		} catch (\Throwable $ex) {
			$this->sendEmail('action_log', 'Error outputting history tracker data', print_r($ex, true));
		}

		return array($new_or_updated, $img_count, $img_new);
	}

	private function joinPaths(...$segments) {
		// Trim leading/trailing slashes from each segment to prevent issues with concatenation
		$trimmedSegments = array_map(function($segment) {
			return trim($segment, '/');
		}, $segments);

		// Filter out empty segments (which can happen after trimming)
		$nonEmptySegments = array_filter($trimmedSegments);

		// Join the non-empty segments with a single forward slash
		$combinedPath = implode('/', $nonEmptySegments);

		// Add a leading slash if the path should be absolute (e.g., from /)
		if (isset($segments[0]) && str_starts_with($segments[0], '/')) {
			$combinedPath = '/' . $combinedPath;
		}

		return $combinedPath;
	}

	private $fieldsToGroom = array();
	// This does the initiation for the grooming. See groomRETSData().
	protected function initRETSDataGrooming($fields) {
		foreach ($fields as $field) {
			if ($field['DataType'] == 'Decimal' && $field['Interpretation'] == 'Currency' && $field['UseSeparator'] == '1') {
				$this->fieldsToGroom['CurrencyWithSeparator'][] = $field['SystemName'];
			}
		}
	}

	// Here we can manipulate the incoming data from the RETS server as we see
	// fit. For starters, we're going to take "currency" values and remove
	// commas from them.
	protected function groomRETSData($listing) {
		if (count($this->fieldsToGroom) > 0) {
			foreach ($this->fieldsToGroom['CurrencyWithSeparator'] as $fieldName) {
				$listing[$fieldName] = str_replace(",", "", $listing[$fieldName]);
			}
		}
		return $listing;
	}

	public function doesTableExist($tablename) {
		try {
			$this->db->fetchOne("SELECT 1 FROM `$tablename` LIMIT 1");
			return true;
		} catch (Throwable $e) {
			return false;
		}
	}

	/**
	 * Update the member statuses in the database based on data from RETS server
	 */
	public function updateMemberStatuses()
	{
		$mls = $this->mlsname;

		// Get members from the database
		$access_members = $this->db->fetchAll("SELECT * FROM access WHERE mls = '$mls' order by access_id desc");
		$armls_spark_members = $this->db->fetchAll("SELECT MemberMlsId FROM armls_spark_member");

		$rets = $this->rets;
		$changes = array(0 => array(), 1 => array());

		$armls_spark_members_by_member_id = [];
		foreach ($armls_spark_members as $armls_spark_member) {
			// All members in the table have status 'Active', so we don't need to track any more info, just that they're
			// in the table.
			$armls_spark_members_by_member_id[strtolower($armls_spark_member['MemberMlsId'])] = true;
		}

		// Check the status with RETS and update in the DB
		foreach ($access_members as $access_member) {
			$id = strtolower($access_member['access_member_id']);

			// Skip accounts that don't have a member ID
			if (!$id) continue;

			$status = array_key_exists($id, $armls_spark_members_by_member_id) ? 1 : 0;
			// If the status changed, send email for this user
			if ($status != $access_member['access_status']) {
				$this->db->update('access', array('access_status' => $status), "access_member_id = '$id'");
				$changes[$status][$id] = $access_member;
			}
		}

		$message = "";
		if (!empty($changes[0]) || !empty($changes[1])) {
			// Construct the email message
			if (count($changes[0])) {
				$message .= "The following members are no longer active with ARMLS:\n\n" .
						$this->_getMemberInfo($changes[0]);
			}
			if (count($changes[1])) {
				$message .= "\nThe following members are now active again:\n\n" .
						$this->_getMemberInfo($changes[1]);
			}
			$this->sendEmail('member_status', 'Members Status', $message);
		} else {
			$message = "No changes in member status were found\n";
		}
		$this->newActionLog(6, 'access', $message, 1, date("U"));
		echo $message . "\n";
	}

	private function _getMemberInfo($members) {
		$str = "";
		foreach ($members as $member) {
			$str .= sprintf("%d %-11s %-25s %-25s %-15s\n", $member['access_id'], $member['access_member_id'], $member['access_company'], $member['access_fullname'], $member['access_phone']);
		}
		return $str;
	}

	/**
	 * Send an email notification to the addresses specified by the list ID in the config file
	 *
	 * @throws Exception If sending mail fails
	 * @param $list_id string Key from configuration used to get email addresses
	 * @param $subject string Email subject
	 * @param $message string Message body
	 */
	public function sendEmail($list_id, $subject, $message) {
		// Prevent alarm fatigue due to bogus errors from Bright.
		if (strpos($subject, 'Failure updating') === 0 && strpos($subject, 'BRIGHTMLS') !== false) {
			// I'm using strripos just to try to speed up the search a pinch, knowing that the error message will be at
			// the end of the message.
			// This first one makes sure we don't match the word Possession, which is a field in Bright MLS. Before
			// this, I was just looking for the word session, and where a long SQL statement failed that contained every
			// single field, the word session was there (part of Possession) and so an email was not sent when I
			// actually wanted one.
			if (   preg_match('/[^s]session/i', $message)
				|| strripos($message, 'error fetching images') !== false
				|| strripos($message, 'XML parsing error.  No data to parse') !== false
				|| strripos($message, 'Exception thrown - debug') !== false
				|| strripos($message, 'Missing information to start a session with this server') !== false
				|| preg_match('#\[type\] => xml\s+\[code\] => -1\s#', $message)
			) {
				return;
			}
		}

		// Get the list members from the config file
		$addresses = preg_split('/\s*,\s*/', $this->config['notify'][$list_id]);

		if (!$addresses[0]) return;

		foreach ($addresses as $email_to) {
			$sendgridApiKey = $this->config['sendgrid']['api_key'];
			$client = new \GuzzleHttp\Client();
			$client->post('https://api.sendgrid.com/v3/mail/send', [
				'headers' => [
					'Authorization' => 'Bearer ' . $sendgridApiKey,
				],
				'json' => [
					'personalizations' => [
						[
							'from' => [
								'email' => '<EMAIL>',
							],
							'to' => [
								[
									'email' => $email_to,
								],
							],
						],
					],
					'from' => [
						'email' => '<EMAIL>',
					],
					'subject' => $subject,
					'content' => [
						[
							'type' => 'text/plain',
							'value' => $message,
						],
					],
				],
			]);
		}
	}


	/**
	 * Check if it has been more than 10 hours since the script ran.  If 10 hours elapsed, and not finished, send email.
	 * @return boolean True if action_log for property is OK
	 */
	public function checkActionLog() {

		# FIXME: hack for now.  do we even need to check this any more?
		return true;

		$sql = "SELECT * FROM action_log WHERE action_field_id = 3 ORDER BY action_startdate DESC LIMIT 1";
		$log = $this->db->fetchRow($sql);

		if(is_array($log) && $log['action_status'] != 1 && date("U") - $log['action_startdate'] > 36000){
			$subject = 'ProFoundIDX Property Reset Script Failure';
			$message = 'The property data reset script has been running for over 10 hrs. Please log on to review the issue.';
			$this->sendEmail('action_log', $subject, $message);
			echo "Aborting due to previous script not completing\n";
			return false;
		}
		return true;
	}

	/**
	 * Update the list of fields for RETS tables in the DB
	 * @param $mls
	 */
	public function updateResPropFields()
	{
		$mls = $this->mlsname;
		$this->setupRETSConnection($this->config[$mls]);
		$conf = $this->config[$mls];

		// Add new log in database
		$log_id = $this->newActionLog(3, "fields sync", "Syncing fields for {$this->fieldstbl} table");

		$this->log("Checking for an existing table.");

		$this->createDbTable($this->fieldstbl, 'fields');

		$columns = $this->db->fetchCol("SHOW COLUMNS FROM {$this->fieldstbl}");
		// Check to see if fields table is already created and have required columns
		if (! in_array("SystemName", $columns)) {
			$this->log("Table not found. Attempting to create one.");

			$columns = array (
				"LookupName",
				"SystemName",
				"StandardName",
				"ShortName",
				"DBName",
				"Interpretation",
			);
			$this->db->query(
				"ALTER TABLE `{$this->fieldstbl}`
				 ADD COLUMN `LookupName` VARCHAR(255) NOT NULL,
				 ADD COLUMN `SystemName` VARCHAR(255) NOT NULL,
				 ADD COLUMN `StandardName` TEXT NULL,
				 ADD COLUMN `ShortName` TEXT NULL,
				 ADD COLUMN `DBName` TEXT NULL,
				 ADD COLUMN `Interpretation` VARCHAR(255) NULL,
				 ADD INDEX `SystemName`(`SystemName` ASC)"
			);

			$this->log("New table was created.");

		} else {
			$this->log("Table found. Checking for existing fields.");

			// If table is already created we retrieve fields from it to check which one needs updates
			$sql = "SELECT `LookupName`, `SystemName`, `StandardName`, `ShortName`, `DBName`, `Interpretation` FROM `{$this->fieldstbl}`";
			$existing_fields = $this->db->fetchAll($sql);
			$existing_fields_count = count($existing_fields);

			$this->log("Number of fields in local database: {$existing_fields_count}");
		}

		// RETS Table Fields for Property
		$this->log("Getting fields from RETS server.");

		$fields = $this->rets->GetMetadataTable($conf['prop_table'], $this->class);

		$new_fields_count = 0;

		foreach ($fields as $field) {
			// We get from field only details that we need to insert in our local database table
			$values = array(
					"LookupName"     => $field['LookupName'],
					"SystemName"     => $field["SystemName"],
					"StandardName"   => $field["StandardName"],
					"ShortName"      => $field["ShortName"],
					"DBName"         => $field["DBName"],
					"Interpretation" => $field["Interpretation"],
			);

			// Skip any fields that are already present
			if (count($existing_fields)) {
				if (in_array($values, $existing_fields)) {
					continue;
				}
			}

			// Insert new record in fields table
			$this->db->insert($this->fieldstbl, $values);

			$new_fields_count++;
		}

		$this->log("Got {$new_fields_count} new property fields.");

		// Update database action log
		$this->updateActionLog($log_id, $this->mlsname, $this->msg);
	}

	private function setupImageTable()
	{
		$mls = $this->mlsname;

		$conf = $this->config[$mls];

		# TODO: update the columns on the image table

		$tblname = $mls . '_images';
		$this->createImageTable($tblname);

		# FIXME: should use a mapping later when pulling data out of the DB
		// Add the field names for
		$fields = array(
			array(
				'SystemName' => 'Object-ID',
				'DataType' => 'Small',
				'MaximumLength' => 15,
			),
			array(
				'SystemName' => 'Content-ID',
				'DataType' => 'Character',
				'MaximumLength' => 26,
			),
			array(
				'SystemName' => 'Content-Description',
				'DataType' => 'Character',
				'MaximumLength' => 500,
			),
			array(
				'SystemName' => 'Preferred',
				'DataType' => 'Small',
				'MaximumLength' => 1,
			),
			array(
				'SystemName' => 'Location',
				'DataType' => 'Text',
			)
		);
		$this->updateTableFields($tblname, $fields);

		if ($conf['media_res'] ?? null) {
			$fields = $this->rets->GetMetadataTable($conf['media_res'], $conf['prop_img_class']);
			if ($conf['prop_media_fields_whitelist']) {
				$fields_whitelist = explode(',', $conf['prop_media_fields_whitelist']);
				$fields = array_filter($fields, function($field) use ($fields_whitelist) {
					return in_array($field['SystemName'], $fields_whitelist, true);
				});
				$fields = array_values($fields);
			}


			$this->updateTableFields($tblname, $fields);
		}
	}

	/**
	 * Fetch and sync images from RETS by key. Note that the key can be multiple listings
	 * if they are separated by commas.
	 *
	 * Store the details in the database. Note that we previously saved the images themselves
	 * to the file system, but now we now longer download the images (thus we can't store them).
	 * Instead the data that is synced includes a URL for the image on a CDN.
	 *
	 * @param $key
	 * @return array
	 * @throws Exception
	 */
	private function fetchImages($key) {
		$mls = $this->mlsname;
		$conf = $this->config[$mls];
		$tblname = $mls . '_images';

		// Some RETS servers like MRIS also store the photos in a separate resource/table, with more metadata
		if (isset($conf['media_res']) && isset($conf['prop_img_class'])) {
			$select_conf_fields = [
				'prop_media_key',
				'prop_media_order',
				'prop_media_caption',
				'image_location_field_name',
				'prop_media_type_key',
			];
			if ($conf['prop_media_preferred']) {
				$select_conf_fields[] = 'prop_media_preferred';
			}
			$select_fields = array_reduce($select_conf_fields, function($carry, $conf_field_name) use ($conf) {
				$carry[] = $conf[$conf_field_name];
				return $carry;
			}, []);
			$extra_fields_whitelist = null;
			if ($conf['prop_media_fields_whitelist']) {
				$extra_fields_whitelist = explode(',', $conf['prop_media_fields_whitelist']);
				foreach ($extra_fields_whitelist as $field_name) {
					$select_fields[] = $field_name;
				}
			}
			$select_str = join(',', $select_fields);
			$optional_params = [
				'Select' => $select_str,
			];
			$search = $this->rets->SearchQuery($conf['media_res'], $conf['prop_img_class'], "{$conf['prop_media_key']}=" . $key, $optional_params);

			if ($error = $this->rets->Error()) {
				if ($error['code'] != ZERO_RECORDS_ERROR)
					throw new Exception("Error fetching images from RETS: $key\n" . print_r($error, true));
			}

			# FIXME: kind of a hack to get keys saved - should just use a map later
			$photos = array();
			while ($photo = $this->rets->FetchRow($search)) {
				$media_type = strtolower($photo[$conf['prop_media_type_key']]);
				$allowed_media_types = [
					'image',
					'photo',
					'office photo',
					'jpeg',
				];
				// An example of a media type encountered in Bright MLS that's not an image is 'txt', which we don't
				// want.
				if (!in_array($media_type, $allowed_media_types)) {
					continue;
				}
				$new_photo = [];
				$new_photo['Content-ID'] = $photo[$conf['prop_media_key']];
				$new_photo['Object-ID'] = $photo[$conf['prop_media_order']];
				$new_photo['Content-Description'] = $photo[$conf['prop_media_caption']];
				if ($conf['prop_media_preferred']) {
					if ($photo[$conf['prop_media_preferred']] === 'Y') {
						$new_photo['Preferred'] = 1;
					}
				} else if ($photo[$conf['prop_media_order']] == 1) {
					$new_photo['Preferred'] = 1;
				}
				$new_photo['Location'] = $photo[$conf['image_location_field_name']];
				if ($conf['prop_media_fields_whitelist']) {
					foreach ($extra_fields_whitelist as $field_name) {
						$new_photo[$field_name] = $photo[$field_name];
					}
				}

				$photos[] = $new_photo;
			}
		} else {
			if(empty($conf['prop_img']))
				$resource = "Property";
			else
				$resource = $conf['prop_img'];

            $location = empty($conf['dont_fetch_img_location']);

			// Get the image locations for a property from RETS
			$photos = $this->rets->GetObject($resource, $conf['prop_img_class'], $key, '*', $location);
			timelog("Fetched normal images from RETS");

			if(!empty($conf['prop_thumbnail_class'])) {
				$photos_thumbnail = $this->rets->GetObject($resource, $conf['prop_thumbnail_class'], $key, '*', $location);
				timelog("Fetched thumbnail images from RETS");
			}

			if(!empty($conf['prop_highres_class'])) {
				$photos_highres = $this->rets->GetObject($resource, $conf['prop_highres_class'], $key, '*', $location);
				timelog("Fetched higres images from RETS");
			}

			// FIXME: We should have a loop above, and check the error for each resolution
			if ($error = $this->rets->Error()) {
				throw new Exception("Error fetching images from RETS: $key\n" . print_r($error, true));
			}
		}

		if (!empty($conf['s3_bucket'])) {
			$promises = array();
			$this->log("Saving property images to S3");

			$imageHandler = $this->getMls()->getImageHandler($this->config->all());

			$resolutions = array("thumbnail", "highres", "normal");
			foreach ($resolutions as $resolution) {
				if ($resolution == "thumbnail") {
					$tmp_photos = $photos_thumbnail;
					$suffix = "-t";
				}
				else if ($resolution == "highres") {
					$tmp_photos = $photos_highres;
					$suffix = "-o";
				}
				else {
					$tmp_photos = $photos;
					$suffix = "";
				}
				if (empty($tmp_photos))
					continue;

				foreach ($tmp_photos as &$photo) {
					if (strlen($photo["Data"]) <= 375000) { // 3mb
						$this->s3Client->getConfig()['curl.options'] = array('body_as_string' => true, 'CURLOPT_SSLVERSION' => '3');
					}
					else {
						$this->s3Client->getConfig()['curl.options'] = array('body_as_string' => false, 'CURLOPT_SSLVERSION' => '3');
					}

					$photo[$conf['image_location_field_name']] = $imageHandler->getImagePathFor($photo, "normal");
					$promises[] = $this->s3Client->putObjectAsync(array(
						'Bucket' => $conf['s3_bucket'],
						'Key'    => $imageHandler->getImageFilename($photo["Content-ID"], $photo["Object-ID"], $suffix),
						'Body'   => $photo["Data"],
						'ACL'    => 'public-read'
					));

				}

			}

			foreach($promises as $promise) {
				$promise->wait();
			}
			timelog("Fetched " . count($tmp_photos) * 3 . " images & saved to S3");
		}

		return $photos;
	}

	private function saveImagesToDB($photos) {
		$mls = $this->mlsname;
		$conf = $this->config[$mls];
		$tblname = $mls . '_images';

		$img_count = $new = 0;
		foreach ($photos as $photo) {
			if ($photo['Location'] || isset($conf['prop_media_type']) && isset($conf['prop_media_type_key']) && $photo[$conf['prop_media_type_key']] == $conf['prop_media_type']) {

				// Currently, as the code reads, this situation will never happen because all images from
				// existing listings are deleted, which is fine because it's actually the easiest way to make
				// sure we remove images in our DB that have been removed from RETS.
				// So for speed, we just bypass the check altogether.
				// $record = $this->db->fetchOne("SELECT Location, `Content-Description` FROM $tblname WHERE `Object-ID` = {$photo['Object-ID']} AND `Content-ID` = '{$photo['Content-ID']}'");
				$record = null;
				#echo "Exists: $exists\n";

				// For convenience, remove these fields so when we stuff the record into the database,
				// it won't contain fields the database table doesn't.
				unset($photo['Content-Type']);
				unset($photo['Success']);
				unset($photo['Data']);
				// If there's a whitelist, the photo record already has only whitelisted fields.
				if (!($conf['prop_media_fields_whitelist'] ?? null)) {
					// Previously, we would unset fields on the $photo object that we knew shouldn't be put
					// in the database. However, the problem is when new fields are added by the MLS systems
					// before we are aware of them. In that case, our insert statement will fail.
					// The answer is to use a whitelist of fields instead of a blacklist. The whitelists
					// could be split out by MLS system in good object-oriented form, but due to the fact that
					// we don't have a good way to test this system, I'll do it this simple way without
					// object oriented classes for now.
					$unnacceptable_field_names = array();
					foreach($photo as $key => $value) {
						switch($key) {
							// ALL
							case 'Content-ID':
							case 'Object-ID':
							case 'Content-Description':
							case 'Preferred':
							case 'Location':

								// SNDMLS
							case 'OrderHint':

								// TRENDMLS
							case 'PropMediaSystemLocale':
							case 'PropMediaSubSystemLocale':
							case 'PropObjectKey':
							case 'PropMediaKey':
							case 'PropMediaType':
							case 'PropMediaExternalKey':
							case 'PropItemNumber':
							case 'PropMediaDisplayOrder':
							case 'PropMediaSize':
							case 'PropMimeType':
							case 'PropMediaBytes':
							case 'PropMediaFileName':
							case 'PropMediaCaption':
							case 'PropMediaDescription':
							case 'PropMediaURL':
							case 'PropMediaCreatedTimestamp':
							case 'PropMediaModificationTimestamp':
							case 'PropMediaVendorName':
							case 'PropMediaVendorID':
							case 'PropMediaVendorIDType':
							case 'County':
							case 'ExternalSystemID':
							case 'ListingID':
							case 'LocaleListingStatus':
							case 'PropMediaURLThumb':
								// Do nothing
								break;
							default:
								$unnacceptable_field_names[] = $key;
						}
					}
					foreach($unnacceptable_field_names as $key_to_remove) {
						unset($photo[$key_to_remove]);
					}
				}

				$photo['image_enterdate'] = time();
				if ($record) {
					if ($record['Location'] != $photo['Location'] && $record['Content-Description'] != $photo['Content-Description']) {
						$where = array(
							'`Content-ID` = ?' => $photo['Content-ID'],
							'`Object-ID` = ?' => $photo['Object-ID']
						);
						$ret = $this->db->update($tblname, $photo, $where);
					}
				} else {
					$this->db->insert($tblname, $photo);
					$new++;
				}
				$img_count++;
			}
		}
		return array($img_count, $new);
	}

	/**
	 * Update the Property lookup values in the database from RETS
	 */
	public function updatePropLookupOptions()
	{
		$mls = $this->mlsname;
		$this->setupRETSConnection($this->config[$mls]);
		$conf = $this->config[$mls];

		# FIXME: this is duplicated somewhat - refactor this stuff in to a method
		$lookups = $this->rets->GetAllLookupValues($conf['prop_table']);

		$tblname = 'property_lookup_opts';
		$this->db->delete($tblname, "mls = '$mls'");

		$cnt = 0;
		foreach ($lookups as $lookup) {
			foreach ($lookup['Values'] as $values) {
				$values['mls'] = $mls;
				$values['LookupName'] = $lookup['Lookup'];
				$this->db->insert($tblname, $values);
				$cnt++;
			}
		}

		echo "Got $cnt Lookup values\n";
	}

	/**
	 * Dump the properties from the DB in to Excel XML/HTML format
	 */
	public function getExcelDump($qty)
	{
		try {
			$sql = sprintf("SELECT * FROM %s LIMIT %d", $this->tablename, $qty);
			$prop_array = $this->db->fetchAll($sql);
		} catch(Exception $e) {
			die("No property table was found in database. Sync table from RETS server first!");
		}

		try {
			$sql = "SELECT * FROM {$this->fieldstbl}";
			$field_array = $this->db->fetchAll($sql);
		} catch(Exception $e) {
			die("No fileds table was found in database. Sync table from RETS server first!");
		}

		$head_bg_color = '99CCFF';
		$export_file = strtoupper($this->mlsname) . "_Props_" . $qty . "_" . date("U") . ".xls";

		// Open output file
		$output_file = fopen($export_file, "w+");
		if (! $output_file) {
			die("Error creating output file!");
		}

		fwrite($output_file,
			'<table width="100%" cellspacing="0" cellpadding="2" border="1">
				<tr bgcolor="#' . $head_bg_color . '">
					<td><strong>property_id</strong></td>
					<td><strong>property_enterdate</strong></td>
					<td><strong>property_status</strong></td>');
					foreach ($field_array as $result) {
						fwrite($output_file,
							'<td>
								<strong>' . ($result["SystemName"] ? $result["SystemName"] . " | " : "")
										  . ($result["StandardName"] ? $result["StandardName"] . " | " : "")
										  . ($result["ShortName"] ? $result["ShortName"] . " | " : "")
										  . ($result["DBName"] ? $result["DBName"] : "") .
								'</strong>
							</td>');
					}
				fwrite($output_file,
					'</tr>');
				foreach ($prop_array as $id => $result) {
					fwrite($output_file,
						'<tr>
							<td>' . $id . '</td>
							<td>' .$result["property_enterdate"]. '</td>
							<td>' .$result["property_status"]. '</td>');
							foreach ($field_array as $fresult) {
								fwrite($output_file,
									'<td>' .$result[$fresult["SystemName"]]. '</td>');
							}
						fwrite($output_file,
							'</tr>');
				}
			fwrite($output_file,
				'</table>');

		fclose($output_file);
		echo "{$export_file} was created.";
	}

	/**
	 * Fetch images that are missing from the images DB for a property
	 */
	public function fetchMissingImages()
	{
		$limit = $this->limit;
		$mls = $this->mlsname;

		$sql = "SELECT {$this->config[$mls]['listing_id']} FROM {$this->tablename} p LEFT JOIN {$mls}_images i ON p.{$this->config[$mls]['listing_id']} = i.`Content-ID` WHERE i.`Content-ID` is NULL";
		$images = $this->db->fetchAll($sql);

		echo "Found " . count($images) . " properties missing images in database\n";

		try {
			$this->setupRETSConnection($this->config[$mls]);

			$count = 0;
			foreach ($images as $img) {
				$count++;
				$photos = $this->fetchImages($img[$this->config[$mls]['listing_id']]);
				$this->saveImagesToDB($photos);
				if ($count >= $limit) break;
			}
		} catch (Throwable $e) {
			$errmsg = $e->getMessage() . "\n" . $e->getTraceAsString();
			echo $errmsg;
		}
	}

    private function add_field_prefix($listing) {
        $new_listing = Array();

        $add_prefix = function($item, $key) use(&$new_listing) {
            if($key != "property_enterdate")
                $new_listing["field_" . $key] = $item;
            else
                $new_listing[$key] = $item;
        };

        // It should be a reduce, but PHP has no such thing.
        // It is funny, because most PHP guys have no idea on
        // what reduce should do and they think that array_reduce is
        // worth something.
        // http://stackoverflow.com/q/29213170/106381
        array_walk($listing, $add_prefix);

        return $new_listing;

    }

	// This function differs from updatePropsFromRETS in that it does not handle images or Solr. Its original use case
	// is for syncing office and agent resources.
	// Another difference is that it doesn't handle incremental updates, because when I added the ability to do
	// incremental updates to updatePropsFromRETS(), I only really needed it for ARMLS A (and TARMLS A), and by "needed
	// it", I'm referring to the reason we kept getting 504 timeouts.
	// See: https://ifoundagent.teamwork.com/#/tasks/36316562
	public function updateResourceFromRETS($resource_name, $db_table_infix, $id_fieldname, $lastmod_fieldname,
		$action_field_id, $options = []) {
		// During times when we want to resync the entire dataset from RETS,
		// it's quicker to pull down the data and stuff it in a file
		// and then read from that, rather than pulling it down each sync,
		// because it takes a good 10 seconds. However, in less dire
		// circumstances, it's best to fetch the latest data. Anyway, I've left
		// this code here (the cache stuff) for convenience.
		$cache_RETS_timestamps = isset($options['cache']) ? $options['cache'] : false;

		$mls = $this->mlsname;

		$conf = $this->config[$mls];

		if (!$conf) {
			throw new Exception("Could not get configuration section '$mls''");
		}

		$limit = $this->limit;
		if ($limit == 0 || $limit == null) {
			echo "Limit should more then 0(zero). ";
			exit;
		}
		$this->tablename = "{$this->mlsname}_{$db_table_infix}_{$resource_name}";
		$rets_class = $this->class;

		$nomail = false;

		$total_count = $img_count = $new = $updated = $toupdate = $img_new = $deleted = 0;
		$dbrecords = $data = $found = array();

		$errmsg = '';

		$msg = "Currently processing (active but without param offset support).";

		$logid = $this->newActionLog($action_field_id, $db_table_infix, $msg);

		try {
			$this->setupRETSConnection($conf);

			$rets = $this->rets;

			timelog();

			$this->log("\nTable updates & initialization");
			// Create the databse table if it doesn't exist
			$this->createDbTable($this->tablename, $db_table_infix);

			// Don't do this unless we anticipate schema changes coming soon from the MLS. See comment 8759875687
			// elsewhere in this document.
			// Check to see if the field exists and create them if not.
			// if ($this->mlsname === 'naar') {
			// 	$fields = $this->rets->GetMetadataTable($resource_name, $rets_class);
			// 	$this->updateTableFields($this->tablename, $fields);
			// }
			// $this->initRETSDataGrooming($fields);

			$this->timelog("Table creation & update from metadata");

			if($this->schema_only)
				die(0);

			// Get the record keys and last modification times from the database
			$this->log("\nFetching records data from MySQL database");
			$sql = "SELECT {$id_fieldname},{$lastmod_fieldname} FROM {$this->tablename}";
			$rows = $this->db->fetchAll($sql);
			$dbrecords = array();
			foreach ($rows as $record) {
				$id = (string) $record[$id_fieldname];
				$dbrecords[$id] = $record;
			}
			$this->timelog("Retrieval of records from DB");

			// Cache file
			$file = "rets_{$mls}_{$db_table_infix}_{$rets_class}_timestamps.json";

			// Either get the list of records to sync from a cache file, or query RETS
			if ($cache_RETS_timestamps && file_exists($file)) {
				$data = json_decode(file_get_contents($file), true);
				timelog("Loading RETS records info from local cache");
			} else {
				$this->log("\nFetching records from RETS");

				// Fetch all records, but get only the key and last modification times
				$query = "($lastmod_fieldname=1980-01-01T00:00:00+)";

				if (!empty($this->daysago) && $this->daysago >= 0) {
					$query = $this->lastmod($this->daysago, $lastmod_fieldname);
				} else if ($resource_name === 'OpenHouse') {
					// I'm approaching OpenHouse a little differently than the other resources. We've decided we don't
					// need records where the end date is in the past. So unlike with other resources, we don't care
					// about the date of the most recent record. We just use the current time.

					// This is the first time we've synced a subset of the data. Because we are basing that subset on
					// date/time, we need to be accurate, and we need to pay attention to timezone.
					$now = null;
					if ($mls === 'brightmls') {
						$server_info = $rets->GetServerInformation();
						$server_timezone = $this->fetch_server_timezone();
						$now = new DateTimeImmutable('now', $server_timezone);
					} else if (in_array($mls, ['armls', 'tarmlsaz', 'paaraz', 'naar', 'cabor', 'wmar'], true)) {
						// Initially I was looking at the server's declared timezone from its metadata endpoint.
						// However, that seems WRONG. Even though the server I connect to declares that it is in central
						// time (it reported an offset of -05:00 a month ago, but now it's -06:00 after the DST change),
						// the datetimes, at least the EVENT200 (end date) appear to be in local time (as in, AZ time,
						// where the listings are). Therefore, I'm going to hard-code an offset of -07:00 here, for the
						// FBS/Flexmls MLSs.
						$now = new DateTimeImmutable('now', new DateTimeZone('-07:00'));
						$now = $this->fix_time_based_on_dst($mls, $now);
					} else {
						throw new Exception("Did not expect mls: $mls. Might have timezone issue");
					}

					$now_rets_formatted = $now->format(static::$retsDateTimeFormat);
					// The '+' at the end means "greater-than-or-equal to"
					$query .= ",({$options['event_end_fieldname']}={$now_rets_formatted}+)";
				}

				$opts = array(
					'Select' => "$id_fieldname,$lastmod_fieldname"
				);

				if (($mls == "glvarnv") || ($mls == "sdcrca")) {
					$opts["Limit"] = "None";
				}

				if ($mls == "trendmls" || $mls == "brightmls") {
					$opts["Limit"] = "10000";
				}

				$data = $rets->Search($resource_name, $rets_class, $query, $opts);

				if ($cache_RETS_timestamps) {
					// Here we sort the cached timestamps by modification date
					if ($mls == "crmls") {
						usort($data, function($a, $b) { return strcmp($a['ModificationTimestamp'], $b['ModificationTimestamp']) * -1; });
					}
					$content = json_encode($data);
					file_put_contents($file, $content);
				}
				timelog("Fetch of reocrd IDs & modification dates from RETS");
			}

			$error = $rets->Error();
			if ($error) {
				if ($error['type'] == 'xml') {
					$nomail = true;
					unset($error['text']);
				}
				if ($error["code"] != ZERO_RECORDS_ERROR) {
					$this->log(print_r($error, true));
					throw new Exception("RETS Error: " . print_r($error, true));
				}
			}

			$this->log("Number of records in {$mls}-{$resource_name}-{$rets_class} RETS: " . count($data));

			$this->log("\nGetting number of modified records ...");

			// Fetch any records that are missing or have been modified
			$ids_to_update = array();
			foreach ($data as $recordinfo) {
				$id = $recordinfo[$id_fieldname];
				$found[$id] = 1;

				if (!$this->haveUpdatedProps($recordinfo, $dbrecords, $id, $lastmod_fieldname)) {
					continue;
				}

				$toupdate++;

				if ($limit && $total_count >= $limit) continue;

				$ids_to_update[] = $id;

				$total_count++;
			}

			$this->propmap = $found;

			timelog("Retrieved IDS for records to update");

			$this->log("\nRecords to update: " . count($ids_to_update));

			// Fetch & process the records in batches
			$batch_size = $this->batch_size;

			$processed = 0;
			for ($i = 0; $i < count($ids_to_update); $i += $batch_size) {
				$batch_ids = array_slice($ids_to_update, $i, $batch_size);
				$this->updateResourceSet($dbrecords, $id_fieldname, $batch_ids, $resource_name, $rets_class,
					$db_table_infix);

				$processed += count($batch_ids);
				$this->log("\nTotal records updated: " . $processed);
				timelog();
			}

			try {
				$this->db->beginTransaction();

				$this->log("\nDeleting records");
				$to_delete = 0;
				$ids_to_delete = array();
				foreach (array_keys($dbrecords) as $id) {

					if ($found[$id]) continue;
					$ids_to_delete[] = $id;
					$to_delete++;
				}

				if ($to_delete >= $this->deletion_limit) {
					$this->log("\nWe were going to delete {$to_delete} records, the limit is {$this->deletion_limit}\n");

					$this->log("\n" . print_r(array_slice($ids_to_delete, 0, 10), true) . "\n");
					throw new Exception("We were going to delete {$to_delete} records, the limit is {$this->deletion_limit}");
				}

				// Delete any records from the DB if the record wasn't found in RETS
				foreach (array_keys($dbrecords) as $id) {

					if ($found[$id]) continue;

					$where = array("`$id_fieldname` = ?" => $id);

					// Delete from MySQL
					$this->db->delete($this->tablename, $where);

					$deleted++;
				}

				$this->db->commit();
			} catch (Throwable $e) {
				$this->db->rollBack();
				throw new Exception($e);
			}

			$this->timelog("Deletion of records");

			if (extension_loaded('newrelic')) {
				newrelic_end_transaction();
			}

		} catch (Throwable $e) {
			$errmsg = $e->getMessage();
			$logmsg = $errmsg . "\n" . $e->getTraceAsString();

			if (extension_loaded('newrelic')) {
				newrelic_end_transaction();
				newrelic_notice_error($errmsg, $e);
			}
			$this->log($errmsg);
		}


		// Print some stats
		$this->log("\nPrevious number of records in DB: " . count($dbrecords));
		$this->log("Deleted $deleted old records");
		$this->log("Records left to update: " . ($toupdate - $total_count) . "\n");

		// If there was an error, send an email notification to the admins and write to the log file
		if ($errmsg && !$nomail) {
			$this->sendEmail('action_log', "Failure updating records on " . strtoupper($this->mlsname) . '-' . $this->class, $this->msg);
		}

		$this->updateActionLog($logid, $mls, $this->msg);
	}

	// This is for non-Property resources like Office and Agent. I made it separate because it doesn't need to handle
	// field mapping, images, or Solr stuff.
	public function updateResourceSet($dbrecords, $id_fieldname, $ids_to_update, $rets_resource, $rets_class,
		$db_table_infix) {

		$this->log("\nUpdating " . count($ids_to_update) . " records");

		$ids_string = implode(",", $ids_to_update);

		$max_retries = 3;
		$records = null;
		for ($i = 0; $i < $max_retries; $i++) {
			$records = $this->rets->Search($rets_resource, $rets_class, "($id_fieldname=$ids_string)");
			$error = $this->rets->Error();
			if ($error && $error['code'] == 408) {
				$this->log("\nRetrying RETS Search web request");
				continue;
			} else {
				break;
			}
		}
		if ($error = $this->rets->Error()) {
			echo "RETS Search Error\n";
			print_r($error);
			if ($error["code"] != ZERO_RECORDS_ERROR) {
				echo "Exiting\n";
				throw new Exception("RETS Error - Search: " . print_r($error, true));
			}
		}
		timelog("Fetched properties to update from RETS");
		$records_by_id = [];
		foreach ($records as $record) {
			$records_by_id[$record[$id_fieldname]] = $record;
		}

		list($new, $updated) = $this->updateResource($dbrecords, $ids_to_update, $records_by_id,
			$id_fieldname, $db_table_infix);

		$this->log("Batch Summary:");
		$this->log("- Updated: $updated");
		$this->log("- New records: $new");
	}

	private function updateResource($dbrecords, $ids_to_update, $records_by_id, $id_fieldname, $db_table_infix) {
		$mls = $this->mlsname;

		$this->log("Updating records in MySQL");

		$new = 0;
		$updated = 0;
		try {
			$this->db->beginTransaction();

			$record_counts_by_id = [];
			foreach ($ids_to_update as $id) {
				// Brightmls ActiveMember has had duplicate records for a month or two. Protect against MySQL throwing
				// a duplicate entry error.
				if (isset($record_counts_by_id[$id])) {
					continue;
				}
				$record_counts_by_id[$id] = true;
				$record = $records_by_id[$id];

				$record[$db_table_infix . '_enterdate'] = date("U");

				if (is_null($record[$id_fieldname])) {
					$this->log("Empty record {$id}");
					continue;
				} else if (isset($dbrecords[ltrim($id, "0")]) || isset($dbrecords[$id])) {
					$this->db->update($this->tablename, $record, "$id_fieldname = '$id'");
					$updated++;
				} else if (!empty($record[$id_fieldname])) {
					$this->db->insert($this->tablename, $record);
					$new++;
				} else {
					$this->log("Empty record {$id}");
					continue;
				}
			}
			timelog("Updated records");

			$this->db->commit();
		} catch (Throwable $ex) {
			$this->db->rollBack();
			throw new Exception($ex);
		}
		return array($new, $updated);
	}

	// This is meant to be for human debugging, modified manually whenever needed. E.g. I want to see how many Property
	// records have the Attribution Contact (LIST_239 in Flexmls) value.
	private function manualSearch() {
		$mls = $this->mlsname;
		$conf = $this->config[$mls];
		$this->setupRETSConnection($conf);

		$rets_table = $conf['prop_table'];
		$rets_class = $this->class;
		// Fill out your query below. In this example (commented out), I put (LIST_239=), which I think is the way to
		// specify blank. I can't be sure though, since the response was:
		// RETS Server: Field (LIST_239) is not searchable. See: https://sparkplatform.com/docs/rets/troubleshooting
		// $query = "(LIST_239=)";
		// Coming Soon
		// $query = '(LIST_15=|1IWCZYBFRRQW)';
		// Active
		// $query = '(LIST_15=|OV61GOJ13C0)';
		// Reminder: RETS 1.7.2 Flexmls docs at http://sparkplatform.com/docs/rets/tutorials/replicate say that
		// datetimes are expected to be in server local time, and the server local time (at least for ARMLS A) is
		// Eastern Time (ET).
		$query = '(LIST_87=2021-11-10T07:38:45+)';
		$opts = [
			'Select' => 'LIST_1,LIST_105,LIST_87',
			'Limit' => 1,
			'Count' => 1,
		];
		echo "Started at " . date('Y-m-d H:i:s') . " UTC \n";
		$data = $this->rets->Search($rets_table, $this->class, $query, $opts);
		echo "Ended at " . date('Y-m-d H:i:s') . " UTC \n";
		$error = $this->rets->Error();
		if ($error) {
			echo print_r($error, true);
		} else {
			echo print_r(count($data), true);
		}
	}

	// Here are notes sent to me from Alex at FBS via email on 2022-11-11:
	// > During Daylight Savings Time (between March and November), if a query is sent referencing a date/time not
	// > during Daylight Savings Time (between November and March), then the results of that query will have timestamps
	// > reflecting a shift to one hour earlier than the timestamp in the query.  If a query is sent referencing a
	// > date/time that is during Daylight Savings Time, then the timestamps in the results will exactly match the
	// > timestamp in the query.
	//
	// > Outside of Daylight Savings Time (between November and March) the reverse is true.  A query referencing a
	// > date/time outside of Daylight Savings Time will have results exactly matching the timestampt in the query.  A
	// > query referencing a date/time during Daylight Savings Time will have timestamps reflecting a shift to one hour
	// > later than the timestamp in the query.  For example, the query (LIST_87=2021-06-10T10:30:00+) would currently
	// > return listings with timestamps after 11:30:00 on that date.
	// Therefore, if the real world time in Chicago is in DST, and the time passed in is DST, then we don't need to do
	// anything. Neither do we need to do anything if they are both standard time. If it is truly standard time, and
	// the time passed in is in a DST time of year, then we need to subtract an hour, and add an hour in the other case.
	private function fix_time_based_on_dst($mls, DateTimeImmutable $dti): DateTimeImmutable {
		if (!in_array($mls, ['armls', 'tarmlsaz', 'paaraz', 'naar', 'cabor', 'wmar'], true)) {
			throw new Exception("MLS '{$mls}' not in list and maybe we shouldn't force timezone");
		}

		$is_now_dst = $this->get_is_dst(new DateTimeImmutable('now', new DateTimeZone(static::$flexmls_timezone_str)));
		$is_dti_dst = $this->get_is_dst($dti);
		if ($is_dti_dst === $is_now_dst) {
			return $dti;
		}
		$date_interval = new DateInterval('PT1H');
		if (!$is_now_dst) {
			$date_interval->invert = 1;
		}
		return $dti->add($date_interval);
	}

	private function get_is_dst(DateTimeImmutable $dti): bool {
		$dti = $dti->setTimezone(new DateTimeZone(static::$flexmls_timezone_str));
		return (bool) $dti->format('I');
	}
}

RETSHelper::static_init();
