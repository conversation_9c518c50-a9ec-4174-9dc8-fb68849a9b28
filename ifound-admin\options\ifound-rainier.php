<?
$genesis_settings = array(
	'genesis-vestige' 			    => array(
		'nav_superfish' 			  => 1,
		'nav_home' 					    => 1,
		'subnav_superfish'		  => 1,
		'subnav_home'				    => 1
	),
    'genesis-settings'          => get_option( 'genesis-settings' ),
	'theme_mods_' . $this->website['stylesheet'] 	=> array( 
		'nav_menu_locations' 		=> array(
		    'primary'					  => $this->menu_ids['main']
		),
        'custom_css_post_id'    => -1
	)
);


$sidebars_widgets = array(
    'sidebars_widgets' => array(
        'wp_inactive_widgets' => array(),
        'sidebar' => array(
            0 => 'ifound_quick_search-3',
            1 => 'recent-posts-2',
            2 => 'categories-2',
        ),
        'before-header-1' => array(
            0 => 'custom_html-5',
        ),
        'before-header-2' => array(
            0 => 'ifound_social_media-2',
        ),
        'before-header-3' => array(
            0 => 'custom_html-6',
        ),
        'header-mid' => array(
            0 => 'custom_html-2',
        ),
        'header_right' => array(
            0 => 'ifound_broker_logo-2',
        ),
        'front-page-1' => array(
            0 => 'ifound_slider_widget-2',
        ),
        'front-page-2' => array(
            0 => 'ifound_quick_search-2',
        ),
        'front-page-3' => array(
            0 => 'text-5',
            1 => 'featured-page-3',
            2 => 'featured-page-2',
            3 => 'featured-page-4',
            4 => 'featured-page-5',
            5 => 'featured-page-6',
            6 => 'featured-page-7',
        ),
        'front-page-4' => array(
            0 => 'ifound_featured_listings-2',
        ),
        'front-page-5' => array(
            0 => 'text-6',
            1 => 'custom_html-3',
            2 => 'text-7',
        ),
        'front-page-6' => array(
            0 => 'featured-post-2',
        ),
        'front-page-7' => array(
            0 => 'strong-testimonials-view-widget-3',
        ),
        'lead-capture' => array(),
        'footer-1' => array(
            0 => 'nav_menu-2',
        ),
        'footer-2' => array(
            0 => 'recent-posts-3',
        ),
        'footer-3' => array(
            0 => 'custom_html-4',
            1 => 'ifound_social_media-3',
        ),
        'after-entry' => array(),
        'details-before-slider' => array(
            0 => 'ifound_whats_my_payment-2',
        ),
        'details-after-slider' => array(
            0 => 'ifound_save_this_property-2',
        ),
        'after-details' => array(
            0 => 'ifound_featured_listings-3',
            1 => 'ifound_text_me_now-2',
            2 => 'ifound_call_me_now-2',
        ),
        'search-results-before-criteria' => array(),
        'search-results-after-criteria' => array(),
        'search-results-after-map' => array(
            0 => 'ifound_save_this_search-2',
        ),
        'search-results-after-results' => array(),
        'mobile-phone-me-now' => array(
            0 => 'ifound_call_me_now-3',
            1 => 'ifound_text_me_now-3',
        ),
        'array_version' => 3,
    )
);


$widgets = array(
    'widget_archives' => array(
        '_multiwidget' => 1,
    ),
    'widget_autotext' => array(
        '_multiwidget' => 1,
    ),
    'widget_calendar' => array(
        '_multiwidget' => 1,
    ),
    'widget_categories' => array(
        2 => array(
          	'title' => '',
          	'count' => 0,
          	'hierarchical' => 0,
          	'dropdown' => 0,
        ),
        '_multiwidget' => 1,
    ),
    'widget_custom_html' => array(
        2 => array(
          	'title' => '',
          	'content' => '<h5 style="margin-top: 40px;">"' . stripcslashes( $this->dev['tagline'] ) . '"</h5>',
      	),
      	3 => array(
         	 'title' => 'Do You Know What Your Home is Worth?',
          	'content' => '<p>Free Instant Home Valuation</p><p><a href="' . $this->home_worth_slug() . '" class="button taglinebutton">Find Out</a></p>',
      	),
      	4 => array(
          	'title' => 'Contact',
          	'content' => $this->contact_info(),
      	),
      	5 => array(
          	'title' => '',
          	'content' => $this->header_phone(),
      	),
      	6 => array(
          	'title' => '',
          	'content' => $this->my_account_link(),
      	),
      	'_multiwidget' => 1,
    ),
    'widget_featured-page' => array(
        2 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[1]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        3 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[2]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        4 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[3]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        5 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[4]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        6 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[5]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        7 => array(
            'title' => '',
            'page_id' => $this->post_ids[$this->page_slug[6]],
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'content_limit' => '',
            'more_text' => '',
        ),
        '_multiwidget' => 1,
    ),
    'widget_featured-post' => array(
        2 => array(
            'title' => 'Real Estate News',
            'posts_cat' => '0',
            'posts_num' => 3,
            'posts_offset' => '0',
            'orderby' => 'date',
            'order' => 'DESC',
            'gravatar_size' => '45',
            'gravatar_alignment' => 'alignnone',
            'show_image' => '1',
            'image_size' => 'communities',
            'image_alignment' => 'alignnone',
            'show_title' => '1',
            'post_info' => '[post_date] By [post_author_posts_link] [post_comments]',
            'show_content' => 'content-limit',
            'content_limit' => '170',
            'more_text' => 'Read More',
            'extra_title' => '',
            'extra_num' => '',
            'more_from_category_text' => 'More Posts from this Category',
        ),
        '_multiwidget' => 1,
    ),
    'widget_gform_widget' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_broker_logo' => array(
          2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_call_me_now' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_cmc_form' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_featured_listings' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_quick_search' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_save_this_property' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_save_this_search' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_search_nearby' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_social_media' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_text_me_now' => array(
        2 => array(''),
        3 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_ifound_whats_my_payment' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_media_audio' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_gallery' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_image' => array(
        '_multiwidget' => 1,
    ),
    'widget_media_video' => array(
        '_multiwidget' => 1,
    ),
    'widget_meta' => array(
        '_multiwidget' => 1,
    ),
    'widget_ifound_slider_widget' => array(
        2 => array(''),
        '_multiwidget' => 1,
    ),
    'widget_nav_menu' => array(
        2 => array(
            'title' => 'Communities',
            'nav_menu' => $this->menu_ids['communities'],
        ),
        '_multiwidget' => 1,
    ),
    'widget_pages' => array(
        '_multiwidget' => 1,
    ),
    'widget_recent-comments' => array(
        '_multiwidget' => 1,
    ),
    'widget_recent-posts' => array(
        2 => array(
            'title' => '',
            'number' => 5,
        ) ,
        3 => array(
            'title' => 'Real Estate News',
            'number' => 5,
            'show_date' => false,
        ),
        '_multiwidget' => 1,
    ),
    'widget_rss' => array(
        '_multiwidget' => 1,
    ),
    'widget_search' => array(
        '_multiwidget' => 1,
    ),
    'widget_strong-testimonials-view-widget' => array(
        3 => array(
            'title' => 'Testimonials',
            'text' => '',
            'filter' => false,
            'view' => 1,
        ),
        '_multiwidget' => 1,
    ),
    'widget_tag_cloud' => array(
        '_multiwidget' => 1,
    ),
    'widget_text' => array(
        1 => array(),
        5 => array(
            'title' => 'Featured Communities',
            'text' => '',
            'filter' => true,
            'visual' => true,
        ),
        6 => array(
            'title' => '',
            'text' => '',
            'filter' => true,
            'visual' => true,
        ),
        7 => array(
            'title' => 'Get in Touch',
            'text' => $this->call_or_text(),
            'filter' => true,
            'visual' => true,
        ),
        '_multiwidget' => 1,
    ),
    'widget_user-profile' => array(
        '_multiwidget' => 1,
    ),
);


$theme_settings = array_merge( $genesis_settings, $sidebars_widgets, $widgets );