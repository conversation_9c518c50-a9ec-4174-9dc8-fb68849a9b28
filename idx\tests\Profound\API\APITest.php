<?php

namespace Profound\API;

require 'vendor/autoload.php';

use \PHPUnit_Framework_TestCase;

class APITest extends PHPUnit_Framework_TestCase {
	public function setUp() {
		parent::setUp();
	}

	public function testSanity () {
		$this->assertTrue(true);
	}

	public function testParseAPIString() {
		$api = API::from_string("search-v1.01+json");
		$this->assertEquals($api->getName(), "search");
		$this->assertEquals($api->getVersion(), "v1.01");
		$this->assertEquals($api->getFormat(), "json");
	}

	public function testParseAPIStringBlank() {
		$api = API::from_string("");
		$this->assertEquals($api->getName(), "");
		$this->assertEquals($api->getVersion(), "");
		$this->assertEquals($api->getFormat(), "");
	}

	public function testExplicit() {
		$api = API::explicit("search", "v1.00", "json");
		$this->assertEquals($api->getName(), "search");
		$this->assertEquals($api->getVersion(), "v1.00");
		$this->assertEquals($api->getFormat(), "json");	
	}

	public function testGetAPIString() {
		$api = API::explicit("search", "v1.00", "json");
		$this->assertEquals($api->get_api_string(), "search-v1.00+json");
	}
}