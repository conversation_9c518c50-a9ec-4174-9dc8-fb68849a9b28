# History Tracker

A Node.js application that tracks changes to real estate listing data by monitoring watched fields and maintaining historical records.

## Overview

History Tracker processes JSON files containing complete real estate listing records and tracks changes to `StandardStatus` and `Price` fields. When the `StandardStatus` is "Closed", the price value is taken from the `ClosePrice` field; otherwise it uses `ListPrice`. It maintains two tables:

- `property_changes`: Current state and change timestamps for each listing
- `property_history`: Historical snapshots whenever watched fields change

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Database Setup

Execute the SQL schema to create required tables:

```bash
mysql -u username -p database_name < schema.sql
```

### 3. Configuration

Copy the example config and update with your settings:

```bash
cp config.example.js config.js
```

Edit `config.js` with your database credentials, Sendgrid API key, and Twilio settings. For integration tests, also configure the `testDatabase` section with separate test database credentials.

### 4. Build

```bash
npm run build
```

## Usage

### Command Line

The History Tracker requires both an MLS system name and MLS class name:

```bash
npm start <mls-system-name> <mls-class-name>
```

Or run directly:

```bash
node dist/index.js <mls-system-name> <mls-class-name>
```

Examples:
```bash
node dist/index.js mls1 residential
node dist/index.js mls1 commercial
node dist/index.js mls2 residential
node dist/index.js mls2 land
node dist/index.js test residential  # for integration testing
```

### Integration with Sync Process

Your existing sync process should create JSON files in MLS class-specific subdirectories within the `data/` directory containing complete listing records:

```
data/
├── mls1/
│   ├── residential/
│   │   ├── 2024-01-01-sync.json
│   │   └── 2024-01-02-sync.json
│   └── commercial/
│       ├── 2024-01-01-sync.json
│       └── 2024-01-02-sync.json
├── mls2/
│   ├── residential/
│   │   ├── 2024-01-01-sync.json
│   │   └── 2024-01-02-sync.json
│   └── land/
│       ├── 2024-01-01-sync.json
│       └── 2024-01-02-sync.json
└── test/
    └── residential/
        └── test-file.json
```

Each JSON file contains an object with a `value` property containing an array of complete listing records:
```json
{
  "value": [
    {
      "ListingId": "LISTING123",
      "StandardStatus": "Active",
      "ListPrice": 350000,
      "ModificationTimestamp": "2024-01-01T12:00:00Z"
    },
    {
      "ListingId": "LISTING456", 
      "StandardStatus": "Closed",
      "ListPrice": 425000,
      "ClosePrice": 420000,
      "ModificationTimestamp": "2024-01-01T13:30:00Z"
    }
  ]
}
```

**Required fields for each listing record:**
- `ListingId` (string): Unique identifier for the listing
- `StandardStatus` (string): Current status of the listing
- `ListPrice` (number): Current listing price (used when StandardStatus is not "Closed")
- `ClosePrice` (number): Closing price (required when StandardStatus is "Closed")
- `ModificationTimestamp` (string|Date): When the record was last modified

Files are processed alphabetically (chronologically if named with timestamps).

## Development

### Running Tests

```bash
# Unit tests
npm test

# Integration tests (requires test database)
TEST_DATABASE=true npm test

# Coverage report
npm run test:coverage
```

### File Structure

```
src/
├── database.ts           # Database operations
├── fileProcessor.ts      # JSON file handling
├── historyTracker.ts     # Change detection logic  
├── notifications.ts      # Email/SMS notifications
├── index.ts             # Main entry point
└── tests/               # Test files
```

## How It Works

1. **MLS Class Selection**: Specify which MLS system and class to process via command line parameters
2. **Directory Resolution**: Uses MLS class-specific subdirectory within `data/` (e.g., `data/mls1/residential/`)
3. **File Discovery**: Scans MLS class directory for JSON files, processes alphabetically
4. **Record Processing**: Extracts complete listing records from JSON file's `value` array
5. **Change Detection**: For each record, compares with latest history record from database
6. **History Tracking**: 
   - First time: Insert into MLS class-specific `property_history` table only
   - Changes detected: Insert into `property_history` + upsert `property_changes` tables
7. **File Cleanup**: Successfully processed files are deleted
8. **Error Handling**: Failed files remain for retry, notifications sent via email/SMS

## Configuration

The `config.js` file requires:

- **Database**: MariaDB connection settings for production
- **Test Database**: Separate MariaDB connection settings for integration tests (optional)
- **Admin Emails**: Array of email addresses for error notifications  
- **Sendgrid**: API key and sender information
- **Twilio**: Account credentials and phone numbers
- **MLS Systems**: Configuration for multiple MLS systems, each with MLS classes that have their own table names and data directories

## Error Handling

- Processing errors trigger email and SMS notifications
- Failed files are not deleted (can be retried)
- Database connection errors are handled gracefully
- Comprehensive logging for troubleshooting