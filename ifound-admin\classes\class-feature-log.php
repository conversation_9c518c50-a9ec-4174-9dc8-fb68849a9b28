<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * FeatureLog Class
 *
 * @since 1.1.0
 */

class FeatureLog extends iFoundAdmin {

	private $post_type		= 'feature_log';
	private $query_var		= 'feature_log';
	private $label_name 	= 'Feature Log';
	private	$label_names	= 'Feature Logs';
	protected	$blog_id	= 9;

	/**
	 * init FeatureLog class.
	 *
	 * @since 1.1.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 1.1.0
	 */

	public function __construct() {
		add_action( 'init', array( $this, 'feature_log_post_type' ) );
		add_action( 'rest_api_init', array( $this, 'feature_log_route' ) );
	}

	public function feature_log_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'query_var'				=> $this->query_var,
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'register_meta_box_cb'	=> array( $this, 'feature_log_metabox' )
			)
	  	);

	}

	public function feature_log_metabox() {

		add_meta_box(
			'feature_log',
			__( '<i class="far fa-list-alt"></i> Feature Usage Log', 'ifound' ),
			array( $this, 'feature_log'),
			$this->post_type,
			'advanced',
            'high'
		);

	}

	public function feature_log() {

		$log_id = get_the_ID();

		if( $logs = get_post_meta( $log_id, 'usage_log' ) ) {

			rsort( $logs );

			$count = count( $logs ); ?>

			<style>
				.th{
					padding: 5px 10px 5px 0 !important;
				}
			</style>

			<table class="form-table log_meta">

				<tbody>

					<tr>

						<th colspan="3"><label><? _e( 'Current Usage Count:' . $count, 'ifound' ) ; ?></label></th>

					</tr>

					<tr>
						<th><label><? _e( 'Date', 'ifound' ) ; ?></label></th>
						<th><label><? _e( 'Action', 'ifound' ) ; ?></label></td>
						<th colspan="2"><label><? _e( 'URL', 'ifound' ) ; ?></label></td>
					</tr><?

					foreach( $logs as $log ) {

						list( $date, $action, $msg ) = explode( ';', $log ); ?>

						<tr>
							<th class="th" scope="row"><label for="activity-log_data"><? _e( apply_filters( 'pretty_date', $date ), 'ifound' ) ; ?></label></th>
							<td id="activity-log_data"><? _e( $action, 'ifound' ) ; ?></td>
							<td colspan="2" id="activity-log_data"><a href="<? echo $msg; ?>" target="_blank"><? _e( $msg, 'ifound' ) ; ?></a></td>
						</tr><?

					} ?>

				</tbody>

			</table><?

		}

	}

	public function feature_log_route() {

		register_rest_route(
			'ifound-admin/' . $this->api_version,
			'/log-use/(?P<feature_id>\d+)/(?P<value>\S+)/',
			array(
				'methods'  => WP_REST_Server::EDITABLE,
				'callback' => array( $this, 'log_use' ),
				'args' => array(
					'feature_id' => array(
        				'validate_callback' => function( $param, $request, $key ) {
          					return ( $this->blog_id == get_current_blog_id() ) ? is_numeric( $param ) : false;
						}
					),
					'value' => array(
						'sanitize_callback' => 'sanitize_text_field',
        				'validate_callback' => function( $param, $request, $key ) {
          					return is_string( $param );
						}
					)
      			)
			)
		);

	}

	public function log_use( $data ) {

		$feature_id = intval( $data['feature_id'] );
		$value  	= $data['value'];

		if ( is_int( $feature_id ) && $feature_id > 0 && ! empty( $value ) ) {

			$date = current_time( 'mysql' );

			$meta = array(
				'date' 		=> $date,
				'action'	=> $value,
				'url' 		=> $this->referer()
			);

			add_post_meta( $feature_id, 'usage_log', join( ';', $meta ) );

		}

	}

}
