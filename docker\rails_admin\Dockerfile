FROM ifoundagent:base

RUN rm -f /etc/apt/sources.list
RUN echo "deb https://archive.debian.org/debian stretch main" >> /etc/apt/sources.list
    RUN echo "deb-src https://archive.debian.org/debian stretch main" >> /etc/apt/sources.list
    RUN echo "deb https://archive.debian.org/debian stretch-backports main" >> /etc/apt/sources.list
    RUN echo  "deb https://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list
    RUN echo  "deb-src https://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list
RUN apt update

# FIXME: Create a base nodejs image, and re-use it for this
# node.js - this runs apt-get update
RUN curl -sL https://deb.nodesource.com/setup_10.x | bash -

RUN apt-get update

# Install operating system packages
RUN apt-get install -y --no-install-recommends --fix-missing \
        # bundler needs the MySQL development headers to build the mysql2 package
        # Use default- because of: https://askubuntu.com/a/1049909/25773
        default-libmysqlclient-dev \
        # SSH needed for deployments via Capistrano
        ssh \
        # Install Ruby
        ruby-full \
        # Needed for rake tasks
        nodejs \
        # Needed for nokogirl Ruby gem, and other Ruby stuff
		libxslt-dev

# TODO: Not sure if this is the best way to set all this up.
#  Still hoping to get rid of the Rails stuff completely though, so not sure if it's worth more effort.

# Let the ubuntu user instlal stuff to the ruby paths

# Set the directory for running Rails, and for bash when logging in to debug
WORKDIR /www

# Make sure the container can find 'rails' when run manually
ENV PATH="${PATH}:/www/bin"

# Default the user for running rails to the ubuntu user
USER ubuntu

# Start Rails using the local script
CMD ["/www/bin/rails", "s", "-p", "3000"]
