<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );


/**
 * CMC Admin Class
 *
 * @since 1.0.0
 */

class CMC_admin extends CMC{

	/**
	 * init CMC class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct() {

		/**
		 * CMC Admin Menu.
		 *
		 * @since 1.0.0
		 */
		add_action( 'admin_menu', array( $this, 'cmc_submenu' ) );

		 /**
		 * CMC Admin Scripts.
		 *
		 * @since 1.1.0
		 */

		add_action( 'admin_enqueue_scripts', array( $this, 'cmc_admin_scripts' ) );


	}

	/**
	 * CMC Scripts
	 *
	 * @since 1.1.0
	 */

	public function cmc_admin_scripts(){


		/**
		 * Enqueue jQuery.
		 *
		 * @since 1.1.0
		 */

		wp_enqueue_script( 'jquery' );

		/**
		 * Admin map script.
		 *
		 * @since 1.1.0
		 * @since 1.1.1 Change from enqueue to register script.
		 */

		wp_register_script(
			'cmc_admin_js',
			plugins_url( 'js/admin-map.js', __FILE__ ),
			array( 'jquery' ),
			CMC_PLUGIN_VERSION
		);

		/**
		 * Localize CMC Script Data.
		 *
		 * @since 1.1.0
		 */

		wp_localize_script( 'cmc_admin_js', 'cmc_admin_data', array(
			'key'			=> $this->google_key,
			'url'			=> iFoundMap::$GOOGLE_MAPS_URL . '&key=',
			'image_url'		=> plugins_url( '/images/', __FILE__ )
		));

	}

	/**
	 * Init Sub Menuu
	 *
	 * @since 1.0.0
	 */

	public function cmc_submenu(){

		add_submenu_page(
			'profoundmls',
			'CMC Settings',
			'CMC Settings',
			'manage_options',
			'cmc-settings',
			array( $this, 'cmc_submenu_page' )
		);
	}

	public function cmc_submenu_page() {

		/** @since 1.1.1 Only enqueue this script on this page. */
		wp_enqueue_script( 'cmc_admin_js' );

		//must check that the user has the required capability
		if ( ! current_user_can( 'manage_options' ) ) {

		  wp_die( __( 'You do not have sufficient permissions to access this page.') );

		}

		?>

		<h1>Current Market Comparison Settings</h1>

		<?

		if ( isset( $_POST['cmc_form'] ) && check_admin_referer( 'cmc_action', 'cmc_nonce_field' ) ) {

			$map_settings = (object) array(
				'north' 		=> floatval( $_POST['cmc_north'] ),
				'south' 		=> floatval( $_POST['cmc_south'] ),
				'east' 			=> floatval( $_POST['cmc_east'] ),
				'west' 			=> floatval( $_POST['cmc_west'] ),
				'center_lat' 	=> floatval( $_POST['cmc_center_lat'] ),
				'center_lng'	=> floatval( $_POST['cmc_center_lng'] ),
				'zoom' 			=> intval( $_POST['cmc_zoom'] )
			);

			update_option( 'cmc_admin_map_settings', $map_settings );

			update_option( 'cmc_widget_title', sanitize_text_field( $_POST['cmc_widget_title'] ) );

			update_option( 'cmc_pop_up_text', sanitize_text_field( $_POST['cmc_pop_up_text'] ) );

			/** Only update these settings if super admin. These form fields do not display for other users.*/
			if( is_super_admin() ) {

				update_option( 'cmc_activate', sanitize_text_field( $_POST['cmc_activate'] ) );

				update_option( 'cmc_page_id', intval( $_POST['cmc_page_id'] ) );

				update_option( 'cmc_form_id', intval( $_POST['cmc_form_id'] ) );

				update_option( 'cmc_address_capture_form_id', intval( $_POST['cmc_address_capture_form_id'] ) );

				if( isset( $_POST['cmc_reset_form_ptypes'] ) && isset( $_POST['cmc_form_id'] ) )
					$this->cmc_update_form_ptypes();

			}

			?>
			<div class="updated"><p><strong><? _e( 'Settings saved.', 'wprequal' ); ?></strong></p></div>
			<?

		}

		?>

		<style>
			th{
				text-align:left;
			}
		</style>

		<form method="post">

			<? wp_nonce_field( 'cmc_action', 'cmc_nonce_field' ); ?>

			<input type="hidden" name="cmc_form" value="true"/>

	    	<table class="form-table">

		  		<tr valign="top">

		  			<th>Widget Title:</th>

		  			<td>
						<input type="text" name="cmc_widget_title" value="<? echo stripslashes( get_option( 'cmc_widget_title' ) ); ?>"/>
					</td>

          		</tr>

				<tr valign="top">

		  			<th>Pop Up Form Text:</th>

		  			<td>
						<textarea name="cmc_pop_up_text" cols="50" rows="4" maxlength="200"><? echo stripslashes( get_option( 'cmc_pop_up_text' ) ); ?></textarea>
						<div>Max Length 200 Characters.</div>
					</td>

          		</tr>

			</table>

			<h2>Address Autocomplete Area</h2>

			<p>Cover the area with the rectangle that you would like the autocomplete addresses to begin.</p>
			<p>This does not limit the addresses displayed. Only sets the default starting point of addresses displayed.</p>

			<script>
				var map_settings = <? echo json_encode( get_option( 'cmc_admin_map_settings' ) ); ?>
			</script>

			<div id="admin-map" class="admin-map" style="width:700px;height:500px;"></div>

			<? $map = get_option( 'cmc_admin_map_settings' ); ?>

			<input type="hidden" name="cmc_north" id="cmc_north" value="<? echo $map->north; ?>"/>
			<input type="hidden" name="cmc_south" id="cmc_south" value="<? echo $map->south;; ?>"/>
			<input type="hidden" name="cmc_east" id="cmc_east" value="<? echo $map->east; ?>"/>
			<input type="hidden" name="cmc_west" id="cmc_west" value="<? echo $map->west; ?>"/>
			<input type="hidden" name="cmc_center_lat" id="cmc_center_lat" value="<? echo $map->center_lat; ?>"/>
			<input type="hidden" name="cmc_center_lng" id="cmc_center_lng" value="<? echo $map->center_lng; ?>"/>
			<input type="hidden" name="cmc_zoom" id="cmc_zoom" value="<? echo $map->zoom; ?>"/>

			<?

			if( is_super_admin() ) {

				?>

				<h2>Admin Settings</h2>

				<table>

					<h4>
						WARNING: ADVANCED USERS ONLY. The setting below are for admin purposes only.
					</h4>

					<tr valign="top">

						<th>Activate:</th>

						<td>
							<input type="checkbox" name="cmc_activate" value="checked" <? echo get_option( 'cmc_activate' ); ?>/>
						</td>

					</tr>

					<tr valign="top">

						<th>Page ID:</th>

						<td>
							<input type="text" name="cmc_page_id" value="<? echo get_option( 'cmc_page_id' ); ?>"/>
						</td>

					</tr>

					<tr valign="top">

						<th>Form ID:</th>

						<td>
							<input type="text" name="cmc_form_id" value="<? echo get_option( 'cmc_form_id' ); ?>"/>
						</td>

					</tr>

					<tr valign="top">

						<th>Address Capture Form ID:</th>

						<td>
							<input type="text" name="cmc_address_capture_form_id" value="<? echo get_option( 'cmc_address_capture_form_id' ); ?>"/>
						</td>

					</tr>

					<tr valign="top">

						<th>Reset Form Property Types:</th>

						<td>
							<input type="checkbox" name="cmc_reset_form_ptypes" value="checked"/>
						</td>

					</tr>

				</table>

				<?

			}


			submit_button(); ?>

		</form>

		<?
	}

}

/**
 * @see CMC_admin::init()
 *
 */

add_action( 'init', array( 'CMC_admin', 'init' ) );
?>
