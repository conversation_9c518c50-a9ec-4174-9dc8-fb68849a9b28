{"0": {"title": "Address Capture", "description": "", "labelPlacement": "top_label", "descriptionPlacement": "below", "button": {"type": "text", "text": "Submit", "imageUrl": ""}, "fields": [{"type": "hidden", "id": 1, "label": "Current Market Comparison Address", "adminLabel": "", "isRequired": false, "size": "medium", "errorMessage": "", "inputs": null, "formId": 27, "description": "", "allowsPrepopulate": false, "inputMask": false, "inputMaskValue": "", "inputType": "", "labelPlacement": "", "descriptionPlacement": "", "subLabelPlacement": "", "placeholder": "", "cssClass": "", "inputName": "", "visibility": "visible", "noDuplicates": false, "defaultValue": "", "choices": "", "conditionalLogic": "", "productField": "", "multipleFiles": false, "maxFiles": "", "calculationFormula": "", "calculationRounding": "", "enableCalculation": "", "disableQuantity": false, "displayAllCategories": false, "useRichTextEditor": false, "displayOnly": ""}], "version": "*******", "id": 27, "useCurrentUserAsAuthor": true, "postContentTemplateEnabled": false, "postTitleTemplateEnabled": false, "postTitleTemplate": "", "postContentTemplate": "", "lastPageButton": null, "pagination": null, "firstPageCssClass": null, "subLabelPlacement": "below", "cssClass": "cmc-hidden-address", "enableHoneypot": false, "enableAnimation": false, "save": {"enabled": false, "button": {"type": "link", "text": "Save and Continue Later"}}, "limitEntries": false, "limitEntriesCount": "", "limitEntriesPeriod": "", "limitEntriesMessage": "", "scheduleForm": false, "scheduleStart": "", "scheduleStartHour": "", "scheduleStartMinute": "", "scheduleStartAmpm": "", "scheduleEnd": "", "scheduleEndHour": "", "scheduleEndMinute": "", "scheduleEndAmpm": "", "schedulePendingMessage": "", "scheduleMessage": "", "requireLogin": false, "requireLoginMessage": "", "notifications": [{"isActive": true, "id": "58ef06cf27245", "name": "Admin Notification", "service": "wordpress", "event": "form_submission", "to": "{admin_email}", "toType": "email", "bcc": "", "subject": "New Address Capture From {embed_url}", "message": "<h2>New Address Capture</h2>\r\nSomeone has initiated a home value request on your website and has entered an address! If they provide more information you'll receive a separate email with lead information.\r\n\r\n{all_fields}", "from": "{admin_email}", "fromName": "", "replyTo": "", "routing": null, "conditionalLogic": null, "disableAutoformat": false}], "confirmations": [{"id": "58ef06cf2955d", "name": "Default Confirmation", "isDefault": true, "type": "message", "message": "", "url": "", "pageId": 0, "queryString": "", "disableAutoformat": false, "conditionalLogic": []}]}, "version": "*******"}