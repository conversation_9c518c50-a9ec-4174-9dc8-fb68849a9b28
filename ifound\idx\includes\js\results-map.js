'use strict';

jQuery(window).on('ifound:bind-close-button-listeners', function() {
	// We use to track if the listeners had already been added here, and not add them again. This is a bug because if
	// we zoom or pan the map or paginate, there will be different markers.
	// I'm not tracking the listeners which means we're leaking memory here but it should small and thus forgivable.
	var zoomMarkerClose = document.getElementsByClassName('zoom-marker-close');
	Array.prototype.forEach.call(zoomMarkerClose, function(zMarker) {
		zMarker.addEventListener('click', function () {
			zMarker.parentElement.classList.remove('zoom-marker');
		});
	});
});

/* A polyfill for the jQuery serialize method
 *
 * @params {object} form. DOM element for the search form if present
 * @return {string} serialized string
 */
function serialize(form) {
	var result = [];

	var inputs = form.getElementsByTagName('input');
	Array.prototype.forEach.call(inputs, function(input) {
		if(!input.name
			|| input.disabled
			|| input.type === 'file'
			|| input.type === 'reset'
			|| input.type === 'submit'
			|| input.type === 'button'
		) return;
		if(input.type === 'select-multiple') {
			var opts = input.getElementsByTagName('option');
			Array.prototype.forEach.call(opts, function(opt) {
				results.push(encodeURIComponent(input.name) + '=' + encodeURIComponent(opt.value));
			});
		} else if((input.type !== 'checkbox' && input.type !== 'radio') || field.checked) {
			result.push(encodeURIComponent(input.name) + '=' + encodeURIComponent(input.value));
		}
	});

	var selects = form.getElementsByTagName('select');
	Array.prototype.forEach.call(selects, function(select) {
		var val = select.options[select.selectedIndex].value;
		result.push(encodeURIComponent(select.name) + '=' + encodeURIComponent(val));
	});

	return result.join('&');
}

function emptySearch() {
	var url = window.location.href.split('?');
	return (url[0].endsWith('/listing-search/'));
}

function displaySaveThis() {
	var button = document.getElementsByClassName('button save-this')[0];
    if(typeof button === 'undefined') return;
	var displayStyle = window.getComputedStyle(button).display;

	if((displayStyle) === 'none') button.style.display = 'inline-block';
}

/* A closure for the map object created using google maps api
 * contains all the functionality needed for the map we have
 *
 * @params {object} mapData. Data accociated with the map about to be created
 * @params {object} mapDom. DOM element for the map object
 * @params {object=} searchForm. DOM search form element
 * @params {bool=} doSetMarkers. Whether markers should be set on the map
 * @params {object=} shortcode. Passing a shortcode associated with the map if not a search page
 * @return {function} init. The map object initialization method
 * @return {function} mapSearch. Performing map search with AJAX requests
 * @return {function} setMarkers. Setting markers on the map
 * @return {function} bindEventListeners. Creating event listeners for the new set of DOM elements
 * @return {function} clearMarkers. Clearing the markers off the map
 */
function searchMap(mapData, mapDom, searchForm, doSetMarkers, shortcode) {
	var initialized = false;
	// default params assignment
	searchForm = typeof searchForm === 'undefined' ? false : searchForm;
	doSetMarkers = typeof doSetMarkers === 'undefined' ? false : doSetMarkers;
	shortcode = typeof shortcode === 'undefined' ? false : shortcode;

	var map;
	var loaded = false;
	var markers = [];
	var polygons = [];
	var other_google_maps_items = [];
	var coordinates = [];
	var drawingManager, selectedShape, colorPalette, selectedColor;
	var colors = ['#1E90FF', '#FF1493', '#eef848', '#32CD32', '#FF8C00', '#4B0082','#030237'];
	var colorButtons = [];

	var formId = searchForm.id;
	var mapDomObj = mapDom;

	var mapObjKey = Object.keys(mapData.maps)[0];
	var pins, options, shapes, mapType, other_items, other_google_maps_items;
	if(typeof mapData.maps[mapObjKey].pins !== 'undefined')
		pins = mapData.maps[mapObjKey].pins;
	else pins = false;
	if(typeof mapData.maps[mapObjKey].other_items !== 'undefined')
		other_items = mapData.maps[mapObjKey].other_items;
	else other_items = false;

	options = typeof mapData.maps[mapObjKey].options !== 'undefined' ? mapData.maps[mapObjKey].options || false : false;
	shapes = typeof mapData.maps[mapObjKey].shapes !== 'undefined' ? mapData.maps[mapObjKey].shapes || false : false;

	var styles = [{
		featureType: 'all',
		stylers: [{saturation: 0}],
		},{
		featureType: 'road.arterial',
		elementType: 'geometry',
		stylers: [{hue: '#242f3e'}, {saturation: 20}]
		}
	];

	var polyOptions = {
		strokeWeight: 0,
		fillOpacity: .2,
		editable: true,
		draggable: true
	};

	var setInitialized = function() {
		initialized = true;
	};

	var getInitialized = function() {
		return initialized;
	}

	/* Clear the markers of the map
	 *
	 * @params {void}
	 * @return {void}
	 */
	var clearMarkers = function() {
		markers.forEach(function(marker) { marker.setMap(null); });
		markers.length = 0;

		clearOtherItems();
	};

	function clearOtherItems() {
		other_google_maps_items.forEach(function(item) { item.setMap(null); });
		other_google_maps_items.length = 0;
	}

    /* Attach link to the anchor element for the marker
     *
     * @params {object} marker. Google marker object
     * @reutrn {void}
     */
	var attachLink = function(marker, anchorName) {
		marker.addListener('click', function() {
			var zoomMarkerDiv = shortcode ? shortcode.getElementsByClassName('zoom-marker')[0] :
							document.getElementsByClassName('zoom-marker')[0];
			if(typeof zoomMarkerDiv !== 'undefined') zoomMarkerDiv.classList.remove('zoom-marker');
			var anchorDiv = document.getElementsByName(anchorName)[0];
			anchorDiv.classList.add('zoom-marker');

		});
	};

	/* Adding the polygons withing the map bounds
	 *
	 * @params {void}
	 * @return {void}
	 */
	var fitPolygons = function() {
		var bounds = new google.maps.LatLngBounds();
		polygons.forEach(function(polygon) {
			var paths = polygon.getPaths();
			paths.forEach(function(path) {
				var pathArr = path.getArray();
				pathArr.forEach(function(elem) { bounds.extend(elem); });
			});
		});
	};

	// Reminder: Dale doesn't like it when there's (e.g.) only one search result because it would zoom in too much, not
	// giving context to the listing. It's not really about how many listings there are, just about their proximity. So
	// we solve by putting a max zoom when fitting the bounds.
	var maxZoomOnFitBounds = 17;
	/* Create and add new markers to the map
	 *
	 * @params {bool} fit. If we should changed our maps bounds
	 * @reutrn {void}
	 */
	var setMarkers = function(fit, pins) {
		var bounds = new google.maps.LatLngBounds();
		var image = {
			active: ifound_map.active_icon,
			closed: ifound_map.closed_icon
		};

		var z = 100;
		pins.forEach(function(pin) {
			var icon = image[pin.status];
			if (pin.has_open_house) {
				icon = ifound_map.open_house_icon;
			}
			var marker = new google.maps.Marker({
				position: {lat: pin.center_lat, lng: pin.center_lng},
				map: map,
				icon: icon,
				title: pin.title,
				zIndex: z
			});
			z++;
			attachLink(marker, pin.mls_id);
			markers.push(marker);
			bounds.extend(marker.getPosition());
		});
		if (fit) {
			google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
				this.setZoom(Math.min(this.getZoom(), maxZoomOnFitBounds));
			});
			map.fitBounds(bounds);
		}
	};

	/* Draw polygons on maps
	 *
	 * @params {void}
	 * @retrn {void}
	 */
	var setPolygons = function() {
		shapes.forEach(function(shape) {
			var polygon = new google.maps.Polygon({
				id: shape.id,
				paths: shape.paths,
				strokeColor: shape.color,
				strokeOpacity: .4,
				strokeWeight: 0,
				fillColor: shape.color,
				fillOpacity: .2,
				draggable: true,
			});
			polygon.setMap(map);
			polygons.push(polygon);
			google.maps.event.addListener(polygon.getPath(), 'set_at', function() {
				coordinates = polygon.getPath().getArray();
				document.getElementById('paths-' + polygon.id).value = coordinates;
			});
			polygon.addListener('click', function() {
				setSelection(polygon);
			});
		});
		fitPolygons();
	};

	var getTopPosition = function() {
		return mapDomObj.getBoundingClientRect().top;
	};

	function setOtherItems(other_items) {
		other_items.forEach(function(item) {
			if (item.metadata.type === 'nearby') {
				var other_google_maps_item = new google.maps.Marker({
					position: item.data.position,
					icon: {
						url: ifound_map.subject_icon,
						scaledSize: new google.maps.Size(40, 60),
					},
					title: 'Radius Center',
					map: map,
					// 101 because it's bigger than the 100 used for markers.
					zIndex: 101,
					animation: google.maps.Animation.DROP,
				});
				other_google_maps_items.push(other_google_maps_item);
				other_google_maps_item = new google.maps.Circle({
					center: item.data.position,
					map: map,
					// Convert miles to meters.
					radius: item.metadata.radius * 1609.34,
					strokeColor: colors[0],
					strokeOpacity: 0.3,
					strokeWeight: 2,
					fillColor: colors[0],
					fillOpacity: 0.05,
				});
				other_google_maps_items.push(other_google_maps_item);
			} else if (item.metadata.type === 'dropped_pin') {
				var other_google_maps_item = new google.maps.Marker({
					position: item.data.position,
					icon: {
						url: ifound_map.subject_icon,
						scaledSize: new google.maps.Size(40, 60),
					},
					title: 'Dropped pin',
					map: map,
					// 101 because it's bigger than the 100 used for markers.
					zIndex: 101,
					animation: google.maps.Animation.DROP,
				});
				other_google_maps_items.push(other_google_maps_item);
			}
		});
	}

	/* Searches once a map is dragged around
	 *
	 * @params {bool} fit - whether to search of markers withing the bounds
	 *         {int} delay - delay for the search to run, in milliseconds
	 * @return {void}
	 */
	var mapSearch = function(fit, delay, polygonSearch, input, callbacks) {
		// default params assignment
		polygonSearch = typeof polygonSearch === 'undefined' ? false : polygonSearch;
		input = typeof input === 'undefined' ? false : input;
		callbacks = typeof callbacks === 'undefined' ? false : callbacks;

		if(!ifound_map.do_bounds && !polygonSearch) return;

		clearMarkers();

		jQuery('.while-we-wait').addClass('active');

		setTimeout(function() {
			var queryString = (input || serialize(searchForm));
			var url = ifound_map.site_url + ifound_map.endpoint + '?' + queryString;
			jQuery.getJSON(url, function(data) {
				pins = data.map_data.pins;
				if(pins) setMarkers(false, pins);
				other_items = data.map_data.other_items;
				if(other_items) setOtherItems(other_items);

				var resultsDiv = shortcode ? shortcode.getElementsByClassName('replace-results-here')[0] :
							document.getElementsByClassName('replace-results-here')[0];
				resultsDiv.innerHTML = data.body;

                jQuery('.while-we-wait').removeClass('active');
				bindEventListeners();
			});
		}, delay);
	};

	var bindEventListeners = function() {
		jQuery(window).trigger('ifound:bind-close-button-listeners');
	};

	/* Coloring the drawn polygon
	 *
	 * @params {string} color. Color to fill the drawn polygon
	 * @return {void}
	 */
	var selectColor = function(color) {
		colors.forEach(function(currColor) {
			colorButtons[currColor].style.border = currColor === color ? '2px solid #789' : '2px solid #fff';
		});
		var polygonOptions = drawingManager.get('polygonOptions');
		polygonOptions.fillColor = color;
		drawingManager.set('polygonOptions', polygonOptions);
	};

	/* Setting the selected shape color
	 *
	 * @params {string} color. Color to fill the selection with
	 * @return {void}
	 */
	var setSelectedShapeColor = function(color) {
		if(selectedShape) {
			if(selectedShape.type === google.maps.drawing.Overlay.POLYLINE) {
				selectedShape.set('strokeColor', color);
			} else {
				selectedShape.set('fillColor', color);
			}
			document.getElementById('color-' + selectedShape.id).value = color;
			document.getElementsByClassName('polygon-id-' + selectedShape.id)[0].style.backgroundColor = color;
		}
	};

	/* Create a controller button for a polygon
	 *
	 * @params {string} color. Color to fill the button with
	 * @return {object} button. Span element
	 */
	var makeButtonColor = function(color) {
		var button = document.createElement('span');
		button.className = 'color-button';
		button.style.backgroundColor = color;
		button.addEventListener('click', function() {
			selectColor(color);
			setSelectedShapeColor(color);
		});
		return button;
	};

	/* Clearing the drawn shapes
	 *
	 * @params {void}
	 * @return {void}
	 */
	var clearSelection = function() {
		if(selectedShape) {
			if(selectedShape.type !== 'marker') selectedShape.setEditable(false);
			selectedShape = null;
		}
	};

	/* Drawing the selected shape
	 *
	 * @params {object} shape. Google events overlay member
	 * @return {boid}
	 */
	var setSelection = function(shape) {
		if(shape.type !== 'marker') {
			clearSelection();
			shape.setEditable(true);
			selectColor(shape.get('fillColor') || shape.get('strokeColor'));
		}
		selectedShape = shape;
	};

	/* Deleting the drawn shape
	 *
	 * @params {void}
	 * @return {void}
	 */
	var deleteSelectedShape = function() {
		if(selectedShape) selectedShape.setMap(null);
	};


	/* Adding the color palette to the map
	 *
	 * @params {void}
	 * @return {void}
	 */
	var buildColorPalette = function() {
		colorPalette = document.getElementById('color-palette');
		colors.forEach(function(color) {
			var colorButton = makeButtonColor(color);
			colorPalette.appendChild(colorButton);
			colorButtons[color] = colorButton;
		});
		selectColor(colors[0]);
	};

	/* Append the polygon search to the search bar
	 *
	 * @params {object} polygon. Google polygon object
	 * @return {void}
	 */
	var polygonInput = function(polygon) {
		var coordinates = polygon.getPath().getArray();
		var color = typeof polygon.fillColor === 'undefined' ? '#030237' : polygon.fillColor;

		var input = '<div class="dynamic-input">'
		input +=        '<div class="dynamic-input-label">';
		input +=        '<span class="polygon-color polygon-id-' + polygon.id + '" style="background:' + color + '"></span> - Polygon';
		input +=        '<i class="fa fa-times-circle dynamic-input-remove" aria-hidden="true" polyid="' + polygon.id + '"></i>';
		input +=        '</div>';
		input +=        '<input type="hidden" name="polygons[' + polygon.id + '][paths]" value="' + coordinates + '" class="dynamic-value" id="paths-' + polygon.id + '"/>';
		input +=        '<input type="hidden" name="polygons[' + polygon.id + '][color]" value="' + color + '" class="dynamic-value" id="color-' + polygon.id + '"/>';
		input +=        '</div>';

		document.getElementById('ifound-dynamic-form').innerHTML += input;
	};

	jQuery(document).on('click', '.dynamic-input-remove', function () {
		var ID = jQuery(this).attr('polyid');
		jQuery.each(polygons, function () {
			if (this.id == ID) {
				this.setMap(null);
			}
		});
		jQuery(this).parents('.dynamic-input').remove();
		jQuery(window).trigger('ifound:search');
	});

	// If the Google Maps JS library has not loaded, and we paginate, we should not try to reference the google global
	// object in any way, such as when we try to set the markers. However we still need to save the data off for when
	// the map is shown for the first time (and we set markers then).
	function setDataForInitialMapLoad(_pins, _other_items) {
		pins = _pins;
		other_items = _other_items;
	}

	return {
		init: function() {
			loadGoogleMapsForResultsMap().then(function() {
				if(typeof ifound_paging !== 'undefined')
					ifound_paging.has_map = true;

				if(loaded) return -1;
				map = new google.maps.Map(mapDom, {
					zoom: options ? options.zoom : ifound_map.geo.zoom,
					center: options ? {lat: options.center.lat, lng: options.center.lng} :
						{lat: ifound_map.geo.center_lat, lng: ifound_map.geo.center_lng},
					styles: styles,
					...window.iFoundGlobal.sharedGoogleMapBaseOptions,
				});

				['dragend', 'zoom_changed'].forEach(function(eventName) {
					map.addListener(eventName, function() {
						if(emptySearch()) return;

						mapSearch(false, 0);
					});
				});

				map.addListener('bounds_changed', function() {
					if(shortcode) return;
					var boundsInputDiv = document.getElementById('ifound_bounds');
					boundsInputDiv.value = map.getBounds();
				});

				if(pins) setMarkers(true, pins);
				if(shapes) setPolygons();
				if(other_items) setOtherItems(other_items);

				bindEventListeners();
				loaded = true;
				setInitialized();

				if(shortcode) return;
				/*=========================/
				/*  Drawing manager area   /
				/*========================*/
				drawingManager = new google.maps.drawing.DrawingManager({
					drawingControlOptions: {
						position: google.maps.ControlPosition.TOP_CENTER,
						drawingModes: ['polygon']
					},
					markerOptions: {
						draggable: true,
					},
					polylineOptions: {
						editable: true,
						draggable: true
					},
					rectangleOptions: polyOptions,
					circleOptions: polyOptions,
					polygonOptions: polyOptions,
					map: map
				});

				google.maps.event.addListener(drawingManager, 'polygoncomplete', function(polygon) {
					polygon.id = Math.round(new Date().getTime() + (Math.random() * 100));
					polygonInput(polygon);
					polygons.push(polygon);
					google.maps.event.addListener(polygon.getPath(), 'set_at', function() {
						coordinates = (polygon.getPath().getArray());
						document.getElementById('paths-' + polygon.id).value = coordinates;
					});
				});

				google.maps.event.addListener(drawingManager, 'overlaycomplete', function(e) {
					var newShape = e.overlay;
					newShape.type = e.type;

					if(e.type !== google.maps.drawing.OverlayType.MARKER) {
						drawingManager.setDrawingMode(null);

						google.maps.event.addListener(newShape, 'click', function(e) {
							if(e.vertex !== undefined) {
								if(newShape.type === google.maps.drawing.OverlayType.POLYGON) {
									var path = newShape.getPaths().getAt(e.path);
									path.removeAt(e.vertex);
									if(path.length > 3) newShape.setMap(null);
								} else if(newShape.type === google.maps.drawing.OverlayType.POLYLINE) {
									var path = newShape.getPath();
									path.removeAt(e.vertex);
									if(path.length > 2) newShape.setMap(null);
								}
							}
							setSelection(newShape);
						});
						setSelection(newShape);
					} else {
						google.maps.event.addListener(newShape, 'click', function(e) { setSelection(newShape); });
						setSelection(newShape);
					}
				});

				google.maps.event.addListener(drawingManager, 'drawingmode_changed', clearSelection);
				google.maps.event.addListener(map, 'click', clearSelection);
				buildColorPalette();
			});
		},
		mapSearch: mapSearch,
		setMarkers: setMarkers,
		setOtherItems: setOtherItems,
		bindEventListeners: bindEventListeners,
		clearMarkers: clearMarkers,
		getInitialized: getInitialized,
		setDataForInitialMapLoad: setDataForInitialMapLoad,
	};
}

/* Return map button for a give shortcode
 * or returns the map button for the whole page
 * if it's a search
 *
 * @params {object=} shortcode. DOM object of .shortcode results class.
 * @return {object}. DOM object for the map button
 */
function getMapButton(shortcode) {
	// default params assignment
	shortcode = typeof shortcode === 'undefined' ? false : shortcode;
	if(!shortcode) return document.getElementsByClassName('map-heading')[0];
	return shortcode.getElementsByClassName('map-button')[0];
}

/* Toggle map on the click event for the map button
 *
 * @params {object} map. Search Map object
 * @params {object} mapButton. Map button DOM object
 * @params {object} shortode. Shortcode Results DOM object
 * @return {void}
 */
function toggleMap(map, mapButton, shortcode) {
	// default params assignment
	shortcode = typeof shortcode === 'undefined' ? false : shortcode;

	var mParent = shortcode || document;
	var mapWrapper = mParent.getElementsByClassName('results-map')[0];
	var displayStyle = window.getComputedStyle(mapWrapper).display;
	if(displayStyle !== 'none') {
		mapWrapper.style.display = 'none';
		mapButton.innerHTML = mapButton.innerHTML.replace('Hide', 'Show');
	} else {
		map.init();
		mapWrapper.style.display = 'block';
		mapButton.innerHTML = mapButton.innerHTML.replace('Show', 'Hide');
	}
}

/* Display all the toggle map buttons
 *
 * @params {string} buttonClass.
 * @return {void}
 */
function showMapButtons(buttonClass) {
	var buttons = document.getElementsByClassName(buttonClass);
	Array.prototype.forEach.call(buttons, function(button) { button.style.display = 'inline-block'; });
}

/* Split the input value to the city and the state
 *
 * @params {string} inputVal. Value to split
 * @return {object}. An object containing two elements: city and state
 */
function getCriteriaState(inputVal) {
	var parts = inputVal.split(',');
	if(parts.length !== 2) return null;
	return {
		city: parts[0],
		state: parts[1].replace(/ /g, '')
	};
}

// Build a search URL using path segments, instead of a query string. Use a hyphen to separate field name from value.
// I think the original intention here was a "pretty URL", the thought being (I'm guessing) that path segments was
// prettier than using a query string, and perhaps hoping that it was better for SEO.
function getReplaceStateUrl() {
	var newPath = '/';
	var criteria = document.querySelectorAll('input.dynamic-value,select.dynamic-value');

	for(var i = 0; i < criteria.length; i++) {
		if(criteria[i].name.includes('polygons[')) {
			continue;
		}
		var name = criteria[i].name.replace( '[]', '' ).replace( '[min]', '_min' ).replace( '[max]', '_max' );
		newPath += name + '-' + encodeURIComponent(criteria[i].value) + '/';
	}

	const url = new URL(window.location.href);
	// We keep the query string intact, except we reset pagination by ensuring the param is removed.
	url.searchParams.delete('results_page_num');
	url.pathname = '/listing-search' + newPath;
	return url.toString();
}

/* Create a new search based on the form field input
 *
 * @params {object} map. searchMap object corresponding to the search
 * @params {object} mapDataObj. Map data object corresponding to the search
 * @return {void}
 */
function createNewSearch(map, mapDataObj) {
	if(emptySearch()) displaySaveThis();
	var input = serialize(document.getElementById('ifound-dynamic-form'));
	// To explain: our pagination links, and their associated JavaScript, was originally built with the assumption that
	// when pagination is shown, the search had already been run on the server, and this input_obj thing (which holds
	// info like if the search is residential/rentals/land, sort order, inputs, etc) exists. When we switched to instant
	// search, such that even starting a new search would use ajax and thus the page had not been initially loaded on
	// the server, the input_obj doesn't exist. So the simplest fix I can think of is to make it exist here.
	if (typeof input_obj === 'undefined') {
		const urlSearchParams = new URLSearchParams(input);
		// The input_obj var is supposed to be an array of searches. We'll just assume there is one search, which I
		// think is an assumption we make in many places already, so if it's ever not true, we'll need to fix it here,
		// but many other places also.
		window.input_obj = [{
			...Object.fromEntries(urlSearchParams.entries()),
			pp: 1,
		}];
	}
	var url = ifound_map.site_url + ifound_map.endpoint + (input ? '?' : '') + input;
	jQuery('.while-we-wait').addClass('active');
	jQuery.getJSON(url, function(data) {
		var resultsDiv = document.getElementsByClassName('replace-results-here')[0];
		resultsDiv.innerHTML = data.body;
		history.replaceState(null, '', getReplaceStateUrl());
		jQuery('.while-we-wait').removeClass('active');

		window.iFoundGlobal.getLoadGoogleMapsPromise().then(function(isGoogleMapsLoadingOrLoaded) {
			if (isGoogleMapsLoadingOrLoaded) {
				map.clearMarkers();
				var pins = data.map_data.pins;
				if (pins) map.setMarkers(false, pins);
				var other_items = data.map_data.other_items;
				if (other_items) {
					map.setOtherItems(other_items);
				}
				map.bindEventListeners();
			}
			page(map, mapDataObj, false, 0, input);
		});
	});
}

/* Bind the update search button to the search event
 *
 * @params {object} map. searchMap object corresponding to the search
 * @params {object=} mapDataObj. Data object corresponding to the search
 * @return {void}
 */
function createSearchFormController(map, mapDataObj) {
	// default params assignment
	mapDataObj = typeof mapDataObj === 'undefined' ? false : mapDataObj;

	var searchButton = document.getElementById('quick-search-button');
	searchButton.style.display = 'block';
	function doSearch() {
		window.iFoundAddPotentiallyUnfinishedSearchCriteria();
		createNewSearch(map, mapDataObj);
	}
	searchButton.addEventListener('click', doSearch);

	var debouncedDoSearch = _.debounce(doSearch, 1000);
	jQuery(window).on('ifound:search', debouncedDoSearch);
}

/* A polyfill for jQuery's param helper method
 * This function doesn't support child object seriazlization
 * Might add the support for it in the future if needed
 *
 * @params {object} inputObj. Object to serialize
 * @return {string}. Serialized string
 */
function param(inputObj) {
	var chunks = [];

	for(var key in inputObj) {
		if(inputObj.hasOwnProperty(key)) {
             if(key.toLowerCase() == 'polygons') {
                // Different polygons are numbered keys
                var pIndex = 0;
                for(var pKey in inputObj[key]) {
                    var s = 'polygons[' + pIndex + '][paths]=' + inputObj[key][pKey]['paths']
                                + '&polygons[' + (pIndex++) + '][color]=' + inputObj[key][pKey]['color'];
                    chunks.push(s);
                }
            }
			else if(typeof inputObj[key] === 'object' && inputObj[key] !== null) {
				for(var objKey in inputObj[key]) {
                    chunks.push(key + '[' + objKey + ']' + '=' + inputObj[key][objKey]);
                }
			} else {
				chunks.push(key + '=' + inputObj[key]);
			}
		}
	}

	return encodeURI(chunks.join('&'));
}

/* Scroll the window to the element
 *
 * @params {object} elem. DOM element
 * @return {void}
 */
function scrollToElem(elem) {
	elem.scrollIntoView();
}

/*======================
== Begin Aerial Sphere =
======================*/

/* Getting the marker data from the newly acquired data
 *
 * @param {object} data. data.body object returned by the JSON method
 * @return {array} markerData. Array of objects containing marker data
 *                             suitable for passing to the AS method
 */
function asGetMarkerData(data) {
	var resSec = data.split('results-section')[1];
	var listings = resSec.split('results-image');
	var markerData = [];
	var linkRE = /href=\"(http.+?)"/;
	var imgRE = /src=\"(http.+?)"/;
	var statRE = /(<div class=\"status-wrap.+?<\/div>)/;
	var propRE = new RegExp('(<div class=\"prop-item.+)<img src', 's');
	for(var i = 1; i < listings.length; i++) {
		markerData.push({
			href: linkRE.exec(listings[i])[1],
			img: imgRE.exec(listings[i])[1],
			stat: statRE.exec(listings[i])[1],
			propData: propRE.exec(listings[i])[1]
		});
	}
	return markerData;
}

/* Get a median coordinate for a collection of pins
 *
 * @param {array} pins. Array of pins objects
 * @param {string} key. A key (coordinate type - lng, lat) to work on
 * @return {float}. Value of the mean coordinate
 * */
function asGetMedianCoord(pins, key) {
	var sorted = pins.sort(function(a,b) { a[key] - b[key];});
	return sorted[Math.floor(sorted.length/2)][key];
}

/* Display the new set of restuls on the Aerial Sphere
 *
 * @param {obejct} data. Data object returned by the AJAX call
 * @param {string} sphereKey. Key specifiying which spehre to use
 *                            from the aerialSpheres array
 * @return {void}
 */
function asDrawSphere(data, sphereKey) {
	if(typeof AerialSphere !== 'function') return;

	var markerData = asGetMarkerData(data.body);
	var aerialSphere = window.aerialSpheres[sphereKey];
	aerialSphere.removeAllMarkers();

	var accLat = 0, accLng = 0;
	var pins = data.map_data.pins;
	for(var i = 0; i < pins.length; i++) {
		accLat += pins[i].center_lat;
		accLng += pins[i].center_lng;

		pins[i].title =  pins[i].title + '<a style="color:#000" href="'
		                               + markerData[i].href + '" target="_blank"><div style="height:140px;"><img src="'
		                               + markerData[i].img + '" /></div></a>'
		                               + markerData[i].stat + markerData[i].propData;

		var icon = null;
		switch(pins[i].status) {
			case 'active':
				icon = 'https://i.ibb.co/qMQ7qrp/active-icon.png';
				break;
			case 'pending':
				icon = 'https://i.ibb.co/tmQ0FPX/ucb-icon.png';
				break;
			case 'closed':
				icon = 'https://i.ibb.co/c3YNdx1/sold-icon.png';
				break;
			default:
				icon = 'https://i.ibb.co/c3YNdx1/sold-icon.png';
		}
		 aerialSphere.addMarker(pins[i].center_lat, pins[i].center_lng, '', pins[i].title, icon, 'Homes For Sale', pins[i].mls_id, '', function(){void 0;});
	}
	aerialSphere.getNearestPano(asGetMedianCoord(pins, 'center_lat'), asGetMedianCoord(pins, 'center_lng'), function(panoObj) {
		aerialSphere.setAutorotate(true);
		var lat = panoObj.panoMarker.marker.position.lat;
		var lng = panoObj.panoMarker.marker.position.lng;
		aerialSphere.openPanorama(lat, lng, lat, lng);
	});
}
/*======================
== End Aerial Sphere   =
======================*/

/* A helper function to paginate throught the same-page polygon search
 *
 * @params {string} data. URL for the ajax request
 * @params {integer} pageNum. Number of the page to navigate to
 * @return {string}. New request url
 */
function getNextPolygonPage(data, pageNum) {
	if(data.includes('&pp=')) return data.replace(/\&pp=[0-9]/, '&pp=pageNum');
	return data += ('&pp=' + pageNum);
}

function bindSelectSort() {
	var selectElem = document.getElementsByClassName('sort-select')[0];
	selectElem.onchange = function() {

	};
}

// http://stackoverflow.com/questions/1090948/change-url-parameters-with-jquery/10997390#10997390
function updateURLParameter(url, param, paramVal) {
    var TheAnchor = null;
    var newAdditionalURL = "";
    var tempArray = url.split("?");
    var baseURL = tempArray[0];
    var additionalURL = tempArray[1];
    var temp = "";

    if (additionalURL) {
        var tmpAnchor = additionalURL.split("#");
        var TheParams = tmpAnchor[0];
        TheAnchor = tmpAnchor[1];
        if (TheAnchor)
            additionalURL = TheParams;

        tempArray = additionalURL.split("&");

        for (var i = 0; i < tempArray.length; i++) {
            if (tempArray[i].split('=')[0] != param) {
                newAdditionalURL += temp + tempArray[i];
                temp = "&";
            }
        }
    } else {
        var tmpAnchor = baseURL.split("#");
        var TheParams = tmpAnchor[0];
        TheAnchor = tmpAnchor[1];

        if (TheParams)
            baseURL = TheParams;
    }

    if (TheAnchor)
        paramVal += "#" + TheAnchor;

    var rows_txt = temp + "" + param + "=" + paramVal;
    return baseURL + "?" + newAdditionalURL + rows_txt;
}

// Hold the list of functions we'll call from popstate, when the user navigates back and forth.
// It's keyed by search index (there could be multiple searches on the page), and then further keyed by page number.
var fetchPageFns = {};
var alreadySetInitialPushState = false;

/* Pagination function. Not optimal. The eventual recursion can blow up th heap
 * But that's if you have dozens of millions of searches
 *
 * @params {object} map. A search map object corresponding to the search
 * @params {object} mapaDataObj. A mapa data object corresponding to the search
 * @params {object=} shortcode. The DOM results object corresponding to the search
 * @params {integer=} index. The index of the search on the page
 * @params {object=} data. Data for the search for the polygon pagination
 * @return {void}
 */
function page (map, mapDataObj, shortcode, index, data) {
	// default params assignment
	shortcode = typeof shortcode === 'undefined' ? false : shortcode;
	index = typeof index === 'undefined' ? 0 : index;
	data = typeof data === 'undefined' ? false : data;

	var pElem = shortcode || document;
	var button = shortcode ?  pElem.getElementsByClassName('map-button')[0] : pElem.getElementsByClassName('map-heading')[0];

	var pageButtons = pElem.getElementsByClassName('next-page');
	if (!alreadySetInitialPushState) {
		history.replaceState({
			pfmls: true,
			eventName: 'pagination',
			index: index,
			pageNum: (typeof input_obj !== 'undefined' && input_obj[index].pp) || 1,
		}, '', window.location.href);
		alreadySetInitialPushState = true;
	}
    Array.prototype.forEach.call(pageButtons, function(pButton) {
        if (!fetchPageFns[index]) {
            fetchPageFns[index] = {};
        }
        var pageNum = parseInt(pButton.getAttribute('new_page'));
        fetchPageFns[index][pageNum] = updateResults;

        function updateResults(doPushState) {
            if(typeof input_obj !== 'undefined') {
                input_obj[index].pp = pageNum;
                var url = updateURLParameter(window.location.href, 'results_page_num', pageNum);
                if (doPushState) {
                    history.pushState({
                        pfmls: true,
                        eventName: 'pagination',
                        index: index,
                        pageNum: (typeof input_obj !== 'undefined' && input_obj[index].pp) || 1,
                    }, '', url);
                }
                var input =  data ? getNextPolygonPage(data, pageNum) : param(input_obj[index]);
            } else var input = getNextPolygonPage(data, pageNum);

            var queryString = input.replace(/#/g, '%23') || serialize(searchForm);
            var url = ifound_map.site_url + ifound_map.endpoint + '?' + queryString;
            jQuery('.while-we-wait').addClass('active');
            jQuery.getJSON(url, function(data) {
                map.clearMarkers();
                var pins = data.map_data.pins;
                var other_items = data.map_data.other_items;
                map.setDataForInitialMapLoad(pins, other_items);
                if (hasLoadedGoogleMapsLibrary) {
                    if(pins) map.setMarkers(false, pins);
                    if (other_items) map.setOtherItems(other_items);
                }

                var sphereKey = 'as' + (index + 1);
                var resultsDiv = pElem.getElementsByClassName('replace-results-here')[0];
                resultsDiv.innerHTML = data.body;

                jQuery('.while-we-wait').removeClass('active');

                map.bindEventListeners();
                scrollToElem(button);
                page(map, mapDataObj, shortcode, index);
                asDrawSphere(data, sphereKey);
            });
        }

        pButton.addEventListener('click', function() {
            updateResults(true);
        });
    });
}

document.addEventListener('DOMContentLoaded', function() {
	var shortcodes = document.getElementsByClassName('shortcode-results');

	// See if the page is a search page or the results page and act accordingly
	if(shortcodes.length === 0) {
		// Hide the save-this button on the init load
		if(emptySearch()) {
            var ssButton = document.getElementsByClassName('button save-this')[0];
            if(typeof ssButton !== 'undefined') ssButton.style.display = 'none';
        }
		var mapDataObj = map_object[0];
		var firstMap = Object.values(mapDataObj.maps)[0];
		var showMapByDefault = firstMap.show_map_by_default;
		// Create the map and bind the events
		var map = searchMap(mapDataObj, document.getElementsByClassName('ifound-results-map')[0],
					document.getElementsByClassName('ifound-dynamic-form-wrapper')[0], true);

		var mapButton = getMapButton();
		mapButton.addEventListener('click', function() {
			toggleMap(map, mapButton);
		});
		if (showMapByDefault) {
			loadGoogleMapsForResultsMap().then(function() {
				toggleMap(map, mapButton);
			});
		}

		// Bind events to the search button
		createSearchFormController(map, mapDataObj);
		page(map, mapDataObj);

		var inputRemoves = document.querySelectorAll('.ifound-dynamic-form .dynamic-input-remove, .search-bar .dynamic-input-remove');
		Array.prototype.forEach.call(inputRemoves, function(input) {
			input.addEventListener('click', function() {
				var inputVal = input.parentElement.parentElement.getElementsByTagName('input')[0].value;
				if(inputVal.includes('polygons[')) map.clearMarkers();
				input.closest('.dynamic-input').remove();
			});
		});
	} else {
		showMapButtons('map-button');
		Array.prototype.forEach.call(shortcodes, function(shortcode,index) {
			var mapButton = getMapButton(shortcode);
			var mapDataObj = map_object[index];
			var map = searchMap(mapDataObj, shortcode.getElementsByClassName('ifound-results-map')[0],
						false, true, shortcode);
			mapButton.addEventListener('click', function() {
				toggleMap(map, mapButton, shortcode);
			});
			var firstMap = Object.values(mapDataObj.maps)[0];
			var showMapByDefault = firstMap.show_map_by_default;
			loadGoogleMapsForResultsMap().then(function() {
				if (showMapByDefault) {
					toggleMap(map, mapButton, shortcode);
				}
			});
			page(map, mapDataObj, shortcode, index);
		});
	}
});

var hasLoadedGoogleMapsLibrary = false;
function loadGoogleMapsForResultsMap() {
	return window.iFoundGlobal.loadGoogleMaps(['drawing'])
		.then(function() {
			hasLoadedGoogleMapsLibrary = true;
		});
}

window.addEventListener('popstate', function(event) {
	if (event.state && event.state.pfmls) {
		if (event.state.eventName === 'pagination') {
			var doPushState = false;
			fetchPageFns[event.state.index][event.state.pageNum](doPushState);
		}
	}
});
