<?php

class TimeLog {

	public static $starttime = 0;
	public static $inittime = 0;

	public static function log($msg) {
		$prefix = "--";
		if (self::$inittime) {
			$start = self::$starttime;
			if (!$msg) {
				$msg = "Total time elapsed";
				$start = self::$inittime;
			}
			$diff = self::timediff($start, microtime(true));
			echo "$prefix $msg: $diff\n";
		} else {
			self::$inittime = microtime(true);
		}
		self::$starttime = microtime(true);
	}
	
	private static function timediff($start, $end) {
		$diff = round($end - $start, 3);
		if ($diff < 60) {
			return "$diff seconds";
		} else if ($diff < 3600) {
			return round($diff / 60, 1) . " minutes";
		} else {
			return round($diff / 3600, 1) . " hours";
		}
	}
}

function timelog($msg = null) {
	TimeLog::log($msg);
}

