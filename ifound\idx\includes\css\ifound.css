@charset "UTF-8";
/* CSS Document */

/* IDX URLs */
.idx-urls{
	position: fixed;
	background-color: white;
	opacity: 0.85;
	padding: 12px;
	top: 45px;
	right: 5px;
	width: 750px;
	height: 100px;
	overflow: auto;
	z-index: 10000;
	border-radius: 4px;
	font-size: 12px;
	color: blue;
}

/* Client Profile and Saved campaign Meta Boxes. This keeps the page from jumping on status change. */
td.status.next-time{
	min-width: 190px;
}
.update-campaign.fa-toggle-on{
	color: green;
}

.update-campaign.fa-toggle-off{
	color: #999;
}

/** Map Controls **/
.ifound-map-control-wrapper{
	width: 50px;
	height: 108px;
}

.ifound-map-control.zoom-out:before{
	content: "\f068";
	display: inline-block;
	font-family: 'Font Awesome 5 Pro';
}

.ifound-map-control.zoom-in:before{
	content: "\f067";
	display: inline-block;
	font-family: 'Font Awesome 5 Pro';
}

.ifound-map-control{
	padding:12px;
	color:#fff;
	margin: 1px auto;
}

.red {
	color: red;
}

.ifound-wrap {
  	margin:0 auto;
    clear: both;
}

.ifound-wrap::after,
.account-already-form:after {
    content: "";
    clear: both;
    display: table;
}

.ifound-grid .ifound-prop-h2,
.featured-listing .ifound-prop-h2{
	width: 100%;
	float: left;
}

.area-map .gm-style-iw.gm-style-iw-c {
	padding: 0;
}

.area-map .gm-style-iw-d,
#ifa_polygon_map .gm-style-iw-d {
	overflow: initial !important;
}

.gm-style-iw-ch {
	padding-top: unset;
}

#ifa_polygon_map .gm-style-iw-d {
  padding: 4px !important;
}

.area-map .gm-ui-hover-effect,
#ifa_polygon_map .gm-ui-hover-effect {
	display: none !important;
}

/* Genesis padding fixes */
.site-inner .site-main {
	background-color: #fff;
    padding: 5%;
    margin-bottom: 40px;
}

button, input[type="button"],
input[type="reset"],
input[type="submit"],
.button {
    background-color: #333;
    border: 0;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    font-size: 1.6rem;
    font-weight: 700;
    padding: 16px 24px;
    text-decoration: none;
    white-space: normal;
    width: auto;
}

.entry-content .button:focus,
.entry-content .button:hover {
    color: #fff;
}

button:focus,
button:hover,
input[type="button"]:focus,
input[type="button"]:hover,
input[type="reset"]:focus,
input[type="reset"]:hover,
input[type="submit"]:focus,
input[type="submit"]:hover,
.button:focus,
.button:hover {
    background-color: #c3251d;
    color: #fff;
}

img {
    height: auto;
    max-width: 100%;
}

/* While we wait popup */
.while-we-wait {
	display: none;
}

.while-we-wait.active.pop-drop {
	display: block;
	position: fixed;
	top:0;
	bottom: 0;
	right: 0;
	left: 0;
	z-index: 9999;
	background-color: rgba(0, 0, 0, .8);
}

.while-we-wait.active.pop-msg {
	display: block;
	position: absolute;
	margin: 0 auto;
	width: 100%;
	top: 50%;
	text-align: center;
	font-size: 60px;
	color: #fff;
	z-index: 9999;
}

/* Registration Pop up*/
.ifound-popup,
.ifound-login-popup {
	width: 100%;
	max-width: 960px;
	padding:30px;
	margin: 100px auto 0;
	background-color: #fff;
	opacity: 1;
	position: absolute;
	left: 50%;
  	transform: translate(-50%);
	display:none;
	z-index: 9999;
}

.ifound-login-popup {
	max-width: 320px;
}

.ifound-popup-backdrop,
.ifound-login-backdrop {
	background-color: rgba(0, 0, 0, 0.8);
    bottom: 0;
    cursor: default;
    left: 0;
    opacity: 1;
    position: fixed;
    right: 0;
    top: 0;
    display:none;
    z-index: 9999;
    -webkit-transition: opacity .5s;
    -moz-transition: opacity .5s;
    -ms-transition: opacity .5s;
    -o-transition: opacity .5s;
    transition: opacity .5s;
}

.ifound-popup.active,
.ifound-popup-backdrop.active,
.ifound-login-popup.active,
.ifound-login-backdrop.active {
    opacity: 1;
    display:block;
}

.registration-section-1 {
	width: 100%;
}

.registration-section-2,
.ifound-popup-form {
	width:50%;
	float: left;
	padding: 10px 20px;
}

.registration-section-2 {
	border: 1px solid #ddd;
    background: #fefefe;
    margin-top: 30px;
    border-radius: 4px;
    padding-top: 20px;
}

.registration-account-already-form {
	margin: 2px auto 30px;
	text-align: center;
	clear: both;
}

.registration-account-already-form .account-already {
	background-color: #fcfcfc;
	cursor: default;
	color: #000;
	display: inline-block;
	line-height: 1.5;
	padding: 0 10px;
}

.ifound-close-button,
.ifound-login-close {
	float: right;
	color: #ccc;
	cursor: pointer;
}

.account-already,
.account-already-login {
	cursor: pointer;
}

.account-already {
	display: block;
	padding-top: 20px;
}

.account-already-button {
	width: 20%;
	float: left
}

.account-already-button a.button {
	display: block;
	height: 60px;
	width: 100%;
}

.account-already-input {
	width: 80%;
	float: left;
}

.account-already-input input {
	height: 60px;
	width: 100%;
}

.account-already-form {
	border: 1px solid #ddd;
    background: #fefefe;
    margin-top: 20px;
    padding: 10px;
}

fal.fa-account-already {
	line-height: 30px;
}

.social-logins {
	margin-top: 10px;
}

.social-login-actions {
	display: flex;
	justify-content: flex-end;
}

/* Without this, the negative margins that Google's JS puts on the iframe will cause the button to get cut off on
   the right
 */
.g_id_signin iframe {
	max-width: initial;
}

.login-error {
	color: red;
}

.empty-input {
	border: red thin solid !important;
	background: #F6D9D9 !important;
}

.create-post {
	display: none;
}

/* LOGIN - FOOTER */
.ifound_required_footer {
	background: #000;
	color: #fff;
	font-size: .7em;
	padding: 2px 2px 60px;
	text-align: center;
	width: 100%;
}

.ifound_required_footer a {
	color: #ccc;
}

.ifound_required_footer a:hover {
	color: #fff;
	text-decoration: underline;
}

.bounds-search .ifound_required_footer {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 40%;
	max-height: 50px;
	overflow: scroll;
}

#ifound-footer-logout {
	display: inline-block;
	margin: 0 2px;
}

.footer-login {
	cursor: pointer;
}

.ifound-popup.login {
	max-width: 400px;
}

.ifound-close-login {
	float: right;
}

/* MLS Footer */
.mls-disclosure {
	padding: 5px 40px;
}

.mls-disclosure img {
	margin: 10px 10px 0;
 }

 .mls-pdp-logo{
 	float: right;
 }

/*
	Search Bar
------------------------------------------------------------------------------*/
.dynamic-heading:after {
   content: "\f107";
   display: inline-block;
   font-family: 'Font Awesome 5 Pro';
   vertical-align: middle;
}

.dynamic-button,
.dynamic-input {
	border: 1px solid #e6e6e6;
    background-color: #fcfcfc;
	border-radius: 4px;
	display: inline-block;
	font-size: 18px;
	line-height: 1.4;
    margin: 2px 0;
    padding: 2px 5px;
    text-align: center;
}

.dynamic-input {
	background-color: #f5f5f5;
    border: #ccc 1px solid;
	margin: 2px;
}

.search-criteria-heading,
.default-criteria-heading,
.more-filters-heading,
.criteria-heading,
.budget-heading,
.map-heading {
	position: relative;
	background: #f5f5f5;
	margin-bottom: 5px;
	padding: 10px 20px;
	width: 100%;
}

.more-filters-heading {
	display: flex;
	justify-content: space-between;
}

.default-criteria-heading {
	display: inline-block;
}

.search-criteria-wrapper,
.default-criteria-wrapper {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 10px;
	margin-bottom: 5px;
}

.results-content-main .search-bar {
	position: relative;
}

.criteria-heading,
.display-options-criteria-heading,
.budget-heading,
.more-filters-heading,
.map-heading {
	cursor: pointer;
}
.budget-body{
	padding: 10px;
}

.budget-add-button-wrapper {
	margin-top: 10px;
}

.criteria-body,
.budget-body {
	display: none;
}

.criteria-body {
	padding-bottom: 5px;
}

.criteria-body.active,
.budget-body.active {
	display: block;
}
.ifound-dynamic-form {
	border: #ddd 1px solid;
	border-radius: 4px;
	display: inline-block;
	min-height: 50px;
	margin-top: 10px;
	overflow: scroll;
	padding: 10px;
	text-align: left;
	width: 100%;
}

.dynamic-button:hover {
	background: #f5f6f7;
}

.dynamic-input-button:hover {
	background: #e6e6e6;
}

.dynamic-input-remove {
	color: #f00;
	padding: 2px;
	cursor: pointer;
}

.dynamic-input-label .polygon-color{
	position: relative;
	margin: 0 10px;
	padding: 0 10px;
}

.search-criteria-body .dynamic-input-button,
.more-filters-wrapper .dynamic-input-button,
.search-criteria-body .dynamic-button,
.more-filters-wrapper .dynamic-button {
	cursor: pointer;
}

.search-criteria-body input,
.more-filters-wrapper input,
.additional-criteria input {
	display: inline-block;
	width: 48%;
}

.additional-criteria-body {
	display: none;
}

.more-filters-criteria {
	display: none;
	z-index: 999;
	width: 100%;
	background: #fff;
	border: thin solid #ccc;
	border-radius: 4px;
	padding: 10px;
	margin-top: 10px;
}

.dynamic-input-plus,
.dynamic-input-single-plus,
.dynamic-input-range,
.ifound-close,
.ifound-close-login,
.advanced-close,
.shortcode-close,
.more-filters-criteria i {
	cursor: pointer;
}

.lookups-body {
	display: none;
	position: absolute;
	text-align: left;
	padding: 10px 20px;
	z-index: 999;
	background: #fff;
	width: auto;
	max-width: 320px;
	min-width: 95px;
	box-shadow: 0 2px 4px rgba(0,0,0,.25);
	border-radius: 4px;
	margin: 2px 0 0 -10px;
}

.lookups-body .ifound-wrap {
	max-height: calc(40vh - 120px);
	overflow: auto;
}

.lookups-body .ifound-close {
	position: absolute;
	top: 10px;
	right: 10px;
	cursor: pointer;
	z-index: 9999;
}

.lookups-body .address-container {
    max-width: 500px;
}

.lookups-body .address{
    width: 100%;
}

.dynamic-input-tabs.ui-widget {
	font-family: inherit;
}

.dynamic-input-tabs.ui-widget.ui-widget-content {
	border: none;
}

.dynamic-input-tabs.ui-tabs .ui-tabs-panel {
	padding: 10px;
}

.modify-search-button-wrapper {
    margin-top: 10px;
    margin-bottom: 10px;
}

select.mls_class_select,
select.sort_select {
    border: #ccc 1px solid;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    height: auto;
    line-height: 1.4;
    padding: 2px 5px;
    width: auto;
     -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-padding-end: 20px;
    -moz-padding-end: 20px;
    -webkit-padding-start: 2px;
    -moz-padding-start: 2px;
}

.mls-class-select-wrap,
.sort-select-wrap {
	display: inline-block;
}

.mls-class-select-wrap .ifound-wrap,
.sort-select-wrap .ifound-wrap {
	position: relative;
}

.mls-class-select-wrap .ifound-wrap:before,
.sort-select-wrap .ifound-wrap:before {
	content: "\f107";
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    vertical-align: middle;
    position: absolute;
    right: 8px;
    top: 2px;
	/* I'm commenting this out because it is over the top of the multibox dropdown results. However I don't know why it
	was in here in the first place so I'll it as a comment for posterity.
	 */
    /*z-index: 1;*/
    text-align: center;
    height: 100%;
    pointer-events: none;
    box-sizing: border-box;
}

.search-criteria-placeholder {
	display: inline-block;
	border: 2px silver dashed;
	padding-left: 4px;
	padding-right: 4px;
	font-size: 18px;
	line-height: 1.4;
	border-radius: 4px;
}

.dynamic-input + .search-criteria-placeholder {
	display: none;
}

.color-button{
	width: 18px !important;
	height: 18px !important;
}

.color-button {
  	width: 14px;
   	height: 14px;
  	font-size: 0;
	margin: 2px;
   	float: right;
 	cursor: pointer;
}

.sort-select-wrap{
	float: right;
}

.save-this, .paging {
	display: inline-block;
}

.required-red {
	border-color: red;
	background-color: #FADBDC;
}

.dynamic-input-array.active:before {
	font-family: 'Font Awesome 5 Pro';
    content: "\f00c";
    color: #000;
}

.dynamic-input-validate.active {
	background-color: #0070c9;
    background: -webkit-linear-gradient(#42a1ec, #0070c9);
    background: linear-gradient(#42a1ec, #0070c9);
    border-color: #07c;
    border-radius: 3px;
    color: #fff;
    display: inline-block;
    padding: 3px 6px;
    margin-top:5px;
}

.ifound_no_results {
	color: #f00;
	font-size: 22px;
	font-style: italic;
}

.dynamic-button.multibox-advanced {
	border: 0;
	padding: 0;
}

.ifound-react-select-multibox-advanced-container {
	min-width: 310px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control {
	border-color: #e6e6e6;
	min-height: initial;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control:hover {
	border-color: #e6e6e6;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control--is-focused {
	box-shadow: none;
	border-color: #e6e6e6;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__control--is-focused:hover {
	border-color: #e6e6e6;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__value-container {
	padding: 0 8px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__input-container {
	padding-top: 1px;
	padding-bottom: 1px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__placeholder {
	text-align: left;
	white-space: nowrap;
	font-size: 16px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__menu {
	/* This prevents the width from being narrowed to the width of the input control */
	width: initial;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading {
	display: flex;
	text-align: left;
	border-bottom: 1px silver solid;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .map-icon {
	width: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .label {
	margin-left: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__group-heading .legend-item {
	margin-left: 10px;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__option {
	text-align: left;
	font-size: 14px;
	padding: 4px 12px 4px 36px;
	white-space: nowrap;
	overflow-x: hidden;
	display: flex;
    align-items: center;
}

.ifound-react-select-multibox-advanced-container
.ifound-react-select-multibox-advanced__option .map-icon {
	width: 10px;
	margin-right: 5px;
}

/** Save this title popup **/

.save-this-title-wrapper{
	display: none;
	position: absolute;
	margin-top: -120px;
	width: 100%;
	max-width: 500px;
	padding: 15px;
	background: #fff;
	border: thin solid #ccc;
	border-radius: 7px;
	box-shadow: 0 .188em .625em #494949;
	z-index: 2;
}

.save-this-title-wrapper .button{
	margin: 10px auto 0;
	font-size: 16px;
}

.save-this-title-heading{
	font-size: 24px;
}

.save-this-title-close{
	float: right;
	cursor: pointer;
	color: #999;
}

/* Slick Slider */

.ifound-details-slider img {
	width: 100%;
	max-height: 70vh;
	object-fit: contain;
}

.ifound-details-slider-nav .slick-list {
	max-height: 95px;
}

.ifound-details-slider-nav .slick-list img {
	object-fit: fill;
}

.ifound-details-slider-nav {
	margin-top: 2px;
}

.ifound-details-slider-nav .slick-slide {
	border-right: 1px solid #fff;
	border-left: 1px solid #fff;
}

.slick-prev,
.slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    padding: 0;
    cursor: pointer;
    color: rgba(0, 0, 0, 0);
    border: none;
    outline: none;
    background: rgba(0, 0, 0, 0);
}

.slick-prev:hover,
.slick-next:hover,
 slick-prev:focus,
.slick-next:focus {
	background-color: transparent;
	opacity: 1;
}

.slick-next {
    right: -25px;
}

.slick-prev {
    left: -25px;
}

.slick-prev:before,
.slick-next:before {
    font-family: 'Font Awesome 5 Pro';
    font-size: 20px;
    line-height: 1;
    color: #aaa;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.slick-prev::before {
    content: "\f0a8";
}

.slick-next::before {
    content: "\f0a9";
}

.slick-dots {
    position: absolute;
	bottom: -65px;
	display: block;
	width: 100%;
	padding: 0;
	list-style: none;
	text-align: center;
}

.slick-dots li {
  	position: relative;
	display: inline-block;
	width: 10px;
	height: 10px;
	margin: 0 5px;
	padding: 0;
	cursor: pointer;
}

.slick-dots li button {
	font-size: 0;
	line-height: 0;
	display: block;
	width: 10px;
	height: 10px;
	padding: 5px;
	cursor: pointer;
	color: transparent;
	border: 0;
	outline: none;
	background: transparent;
}

.slick-dots li button:before {
	font-family: 'slick';
	font-size: 30px;
	line-height: 20px;
	position: absolute;
	top: 0;
	left: 0;
	width: 10px;
	height: 10px;
	content: '•';
	text-align: center;
	opacity: .4;
	color: black;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.slick-dots li button:hover,
.slick-dots li button:focus{
	outline: none;
}

.slick-dots li button:hover:before,
.slick-dots li button:focus:before {
	opacity: 1;
}


/* Column Classes
--------------------------------------------- */

.five-sixths,
.four-sixths,
.one-fourth,
.one-half,
.one-sixth,
.one-third,
.three-fourths,
.three-sixths,
.two-fourths,
.two-sixths,
.two-thirds {
	float: left;
	margin-left: 2.564102564102564%;
}

.one-half,
.three-sixths,
.two-fourths {
	width: 48.717948717948715%;
}

.one-third,
.two-sixths {
	width: 31.623931623931625%;
}

.four-sixths,
.two-thirds {
	width: 65.81196581196582%;
}

.one-fourth {
	width: 23.076923076923077%;
}

.three-fourths {
	width: 74.35897435897436%;
}

.one-sixth {
	width: 14.52991452991453%;
}

.five-sixths {
	width: 82.90598290598291%;
}

.first {
	clear: both;
	margin-left: 0;
}

/* Profile Page */

.client-profile h2 {
	margin-top: 40px;
}

.form-table {
	border-collapse: separate;
	border-spacing: 2px;
	margin-top: 0.5em;
	margin-bottom: 10px;
	width: 100%;
	clear: both;
}

.form-table td {
	font-size: 16px;
	margin-bottom: 9px;
	padding: 15px 10px;
	line-height: 1.3;
	vertical-align: middle;
	border-top: none;
}

.form-table th,
.form-wrap label {
	font-size: 16px;
	font-weight: 400;
	text-shadow: none;
	vertical-align: baseline;
}

.form-table th {
	vertical-align: top;
	text-align: left;
	padding: 20px 10px 20px 0;
	line-height: 1.3;
	font-weight: 600;
}

.form-table td p {
	margin-top: 4px;
	margin-bottom: 0;
}

.form-table.save-this-section {
	border: 1px solid #ddd;
}

.form-table.save-this-section tr:nth-child(1) {
	background: #efefef;
}

.form-table.save-this-section tr:nth-child(1) th,
.form-table.save-this-section tr:nth-child(1) td {
	padding: 10px;
}

.form-table.save-this-section tr:nth-child(2) th,
.form-table.save-this-section tr:nth-child(2) td,
.form-table.save-this-section tr:nth-child(3) th,
.form-table.save-this-section tr:nth-child(3) td,
.form-table.save-this-section tr:nth-child(4) th,
.form-table.save-this-section tr:nth-child(4) td,
.form-table.save-this-section tr:nth-child(5) td {
	padding: 2px 10px;
}

.campaign-active i.remove-alert {
	cursor: pointer;
}

.campaign-active i.fa-toggle-on.remove-alert {
	color: #008000;
}

i.fa-toggle-off.remove-alert {
	color: #f00;
}

.form-table.save-this-section .remove-this {
	font-size: 14px;
	letter-spacing: normal;
	margin: 5px 0;
	padding: 5px 10px;
}

/* Area Map Fix for black box
--------------------------------------------- */

#area-map > div > div > div:nth-child(1) > div:nth-child(3) > div:nth-child(2) > div:nth-child(4) > div > div:nth-child(1) > div:nth-child(3) {
    display: none;
}

#area-map > div > div > div:nth-child(1) > div:nth-child(3) > div:nth-child(2) > div:nth-child(4) > div > div:nth-child(1) > div:nth-child(1) {
    border-top: 24px solid rgba(0,0,0,.8) !important;
}

#area-map > div > div > div:nth-child(1) > div:nth-child(3) > div:nth-child(2) > div:nth-child(4) > div > div:nth-child(1) > div:nth-child(1) {
    top:25px !important;
}

/* Responsive Video
---------------------------------------------------------- */
.video-responsive{
    overflow:hidden;
    padding-bottom:56.25%;
    position:relative;
    height:0;
}

.video-responsive iframe{
    left:0;
    top:0;
    height:100%;
    width:100%;
    position:absolute;
}

.ifound-property-video{
	margin: 10px auto;
	clear: both;
}

/* Listing Status
--------------------------------------------- */


/* Hide listing status hack that was implemented on some sites, before fix */
.prop-item.list-status {
	display: none;
}

/* Style Legit listing Status*/

.results-status {
	background-color: #0006;
	-webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
    box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
    color: #fff;
	font-size: 14px;
    font-weight: 700;
    padding: 2px 25px;
    position: absolute;
    left: 0;
    top: 0;
    text-align: center;
    width: auto;
}

.ifound-grid .ifound-results,
.featured-listing,
.ifound-results .results-wrapper {
    position: relative;
}

/* Map labels for polygon maps */
.map-label-iw {
    padding: 10px;
}
.gm-style-iw-c {
    padding: 0 !important;
}

.gm-style-iw-d {
    overflow: unset !important;
}

.gm-ui-hover-effect {
    display: none !important;
}

@media all and (max-width: 800px) {

	.search-criteria-body input,
	.more-filters-wrapper input,
	.additional-criteria input {
	    display: inline-block;
	    min-width: 100px;
	    width: 100%;
	}

	.registration-section-2,
	.ifound-popup-form,
	.account-already-input,
	.account-already-button {
		width: 100%;
		float: none;
	}

	.account-already-input {
		margin-bottom: 10px;
	}

	.site-inner .site-main {
		padding: 0;
	}

	.while-we-wait.active.pop-msg {
		font-size: 50px;
	}
}

@media screen and ( max-width: 782px ) {
	.form-table {
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}

	.form-table th,
	.form-table td,
	.label-responsive {
		display: block;
		width: auto;
		vertical-align: middle;
	}

	.label-responsive {
		margin: 0.5em 0;
	}

	.form-table td input[type="text"],
	.form-table td input[type="email"],
	.form-table td input[type="password"],
	.form-table td select,
	.form-table td textarea,
	.form-table span.description {
		width: 100%;
		line-height: 1.5;
		padding: 7px 10px;
		display: block;
		max-width: none;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}

	.form-table span.description {
		display: inline;
		padding: 4px 0 0;
		line-height: 1.4em;
		font-size: 14px;
	}

	.form-table th {
		padding-top: 10px;
		padding-bottom: 0;
		border-bottom: 0;
	}

	.form-table td {
		margin-bottom: 0;
		padding-bottom: 6px;
		padding-top: 4px;
		padding-left: 0;
	}

	.form-table input.regular-text {
		width: 100%;
	}

	.form-table fieldset label {
		display: block;
	}
}

@media only screen and (max-width: 768px) {
	.form-field input[type="text"],
	.form-field input[type="email"],
	.form-field input[type="password"],
	.form-field select,
	.form-field textarea {
		width: 99%;
	}

	.form-wrap .form-field {
		padding:0;
	}
}

@media all and (max-width: 480px) {

	.search-criteria-wrapper .dynamic-button,
	.additional-criteria .dynamic-button,
	.more-filters-criteria .dynamic-button,
	.default-search-criteria .dynamic-button,
	.dynamic-input {
		text-align: left;
		width: 100%;
	}

	.search-bar .lookups-body {
		left: 0;
		right: 0;
		margin-left: auto;
		margin-right: auto;
	}

	.more-filters-heading {
		position: relative;
		padding: 5px 10px 10px;
	}

	.dynamic-heading:after,
	.dynamic-input-label i.dynamic-input-remove {
		float: right;
	}

	.results-section .ifound-results.zoom-marker {
        max-width:none;
        width: auto;
        top: 2%;
        bottom: 2%;
        left: 2%;
        right: 2%;
        overflow: scroll;
    }

    .while-we-wait.active.pop-msg {
		font-size: 30px;
	}

	.sort-select-wrap{
		float: none;
	}

}

/*  Quick Search
------------------------------------------------------------------------------*/

aside .quick-search .select-wrap,
.sidebar .quick-search .select-wrap,
.sidebar .quick-search .text-wrap {
    margin-bottom: 7px;
}

aside .quick-search .qs-button-wrap,
.sidebar .quick-search .qs-button-wrap {
    margin-top: 10px;
}

.sidebar .quick-search .number-wrap {
	clear: both;
	padding-top: 7px;
}

.sidebar .quick-search .checkbox-wrap {
	clear: both;
	padding-top: 7px;
}

.sidebar .widget_ifound_quick_search .quick-search-button-wrap {
	clear: both;
	padding-top: 10px;
}

.sidebar .quick-search .number-wrap.number-wrap-city {
	padding-top: 0; bottom: 7px;
}

.quick-search select {
	display: block;
	width: 100%;
}

.quick-search .number-input {
	border-radius: 4px;
	width:48%;
	float: left;
	margin: 0 2% 0 0;
	padding-left: 5px;
}

.quick-search .checkbox-wrap {
	font-size: 14px;
}

.quick-search .number-input::placeholder {
	font-size: 14px;
	opacity: 1;
}

.quick-search input[name=bedrooms_max],
.quick-search input[name=bathrooms_max],
.quick-search input[name=list_price_max] {
	margin-right: 0 !important;
}

.quick-search .number-input {
	width: 49%;
}

.ifound-text-input {
	border-radius: 4px;
	padding-left: 5px;
	line-height: 1;
}

.ifound-text-input::placeholder {
	opacity: 1;
}

.quick-search select {
	border-radius: 4px;
}

.sidebar #quick-search-button {
	text-align: center;
	width: 100%;
}

.results-page .qsearch-button-wrapper,
.results-page .show-map-button-wrapper {
	display: inline-block;
	margin-top: 10px;
}

.results-page .search-results-after-map.widget_ifound_save_this_search {
 	display: inline-flex;
	margin-left: 5px;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__placeholder {
	text-align: left;
	white-space: nowrap;
	font-size: 16px;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control {
	border-color: #e6e6e6;
	min-height: initial;
	background-color: #f5f5f5;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control:hover {
	border-color: #e6e6e6;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control--is-focused {
	box-shadow: none;
	border-color: #e6e6e6;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control--is-focused:hover {
	box-shadow: none;
	border-color: #e6e6e6;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__value-container {
	padding: 0 8px;
}
.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__multi-value {
	flex: 1;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading {
	display: flex;
	text-align: left;
	border-bottom: 1px silver solid;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .map-icon {
	width: 10px;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .label {
	margin-left: 10px;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .legend-item {
	margin-left: 10px;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__option {
	text-align: left;
	font-size: 14px;
	padding: 4px 12px 4px 36px;
	white-space: nowrap;
	overflow-x: hidden;
	display: flex;
	align-items: center;
}

.sidebar .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__option .map-icon {
	width: 10px;
	margin-right: 5px;
}

/*  Horizontal Home Page Search Widget on Genesis
------------------------------------------------------------------------------*/

.home .front-page-2 .flexible-widgets .widget_ifound_quick_search {
	background-color: rgba(0,0,0,.6);
    border-radius: 0 3px 3px 3px;
    padding: 20px 20px 10px;
    width: 100%;
}

.home .widget_ifound_quick_search h3.ifound-widget-title {
	display: none;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch-container {
	width: 325px;
	float: left;
	margin-right: 15px;
	height: 40px;
	line-height: 30px;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control {
	/* TODO: The cursor should be text, except when a selection has already been made, in which case it should
	     be 'default'. That might need to be handled via JavaScript, in component overrides for react-select.*/
	/*cursor: text;*/
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__control--is-focused {
	border-color: inherit;
	box-shadow: inherit;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__value-container {
	/* If you select a value, and it's too long, the "dummy input" that react-select creates ends up wrapping to
	a second row for further input. Since we only allow a single input, we don't need that second row.
	*/
	flex-wrap: nowrap;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__multi-value {
	flex: 1;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__input-container {
	margin: 0;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__placeholder {
	text-align: left;
	white-space: nowrap;
	font-size: 16px;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__menu {
	/* This prevents the width from being narrowed to the width of the input control */
	width: initial;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__group-heading {
	display: flex;
	text-align: left;
	border-bottom: 1px silver solid;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .map-icon {
	width: 10px;
	margin-right: 5px;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .label {
	margin-left: 10px;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__group-heading .legend-item {
	margin-left: 10px;
	display: flex;
	align-items: center;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__option {
	text-align: left;
	font-size: 14px;
	padding: 4px 12px 4px 36px;
	white-space: nowrap;
	overflow-x: hidden;
	display: flex;
	align-items: center;
}

.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
.ifound-react-select-multibox-quicksearch__option .map-icon {
	width: 10px;
	margin-right: 5px;
}

.home .widget_ifound_quick_search .select-wrap,
.home .widget_ifound_quick_search .number-wrap,
.home .widget_ifound_quick_search .checkbox-wrap,
.home .widget_ifound_quick_search .text-wrap {
	float: left;
	margin-right: 15px;
	text-align: left;
	width: calc(15% - 20px);
}

.home .widget_ifound_quick_search .checkbox-wrap {
	font-size: 16px;
}

.home .quick-search .number-input {
	border-radius: 4px;
	width:48%;
	float: left;
	margin: 0 2% 0 0;
	height: 40px;
	padding-left: 5px;
	line-height: 30px;
}

.home .widget_ifound_quick_search .select-wrap select {
	height: 40px;
	white-space: nowrap;
}

.home .widget_ifound_quick_search .qs-button-wrap {
	float: right;
}

.home .widget_ifound_quick_search .qs-button-wrap .button {
	font-size: 18px;
    letter-spacing: normal;
}

.ifound-text-input {
	border-radius: 4px;
	height: 40px;
	padding-left: 5px;
	line-height: 30px;
}

.home .widget_ifound_quick_search .ifound-more-options {
	clear: right;
	float: right;
    font-size: 14px;
}

.home .widget_ifound_quick_search .ifound-more-options a {
	color: #fff;
	display: inline-block;
	margin-top: 5px;
}

.home .search-nearby-button {
	font-size: 20px;
	margin-top: 10px;
	width: 100%;
}

.home .search-nearby-button:before,
.home .text-me-now-button:before {
	font-family: 'Font Awesome 5 Pro';
    content: "\f002";
    margin-right: 8px;
    color: #fff;
}

.home .text-me-now-button:before {
    content: "\f10b";
}

.home .text-me-now-button a {
	color: #fff;
}

/* Phone me now
--------------------------------------------------- */
.mobile-phone-me-now-wrapper {
	display: none;
	background: #000;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 99;
}

.phone-halves .mobile-phone-me-now {
	width:50%;
	float: left;
}

.ifound-text-me-now .button,
.ifound-call-me-now .button {
	border-radius: 0;
	border: none;
	color: #fff;
	font-size: 20px;
    letter-spacing: normal;
	margin-top: 10px;
	padding: 10px 0;
	text-align: center;
	width: 100%;
}

.mobile-phone-me-now-wrapper .ifound-text-me-now .button,
.mobile-phone-me-now-wrapper .ifound-call-me-now .button {
	margin-top: 0;
}

.ifound-text-me-now a.button,
.ifound-call-me-now a.button {
	color: #fff;
	font-size: 20px;
}

.phone-halves .mobile-phone-me-now:first-of-type {
	border-right: 1px solid #fff;
}

.ifound-text-me-now .button .far,
.ifound-call-me-now .button .far {
	margin-right: 8px;
}

@media only screen and (max-width: 500px) {
	.mobile-phone-me-now-wrapper {
		border: 1px solid #fff;
		display: block;
	}
}
/** End Phone me now **/

/** Polygon and Area Maps */
.area-map-wrapper {
	width:100%;
	height:80vh;
}

.polygons-map,
.area-map {
	width:100%;
	height:80vh;
}

.polygon-title-wrapper {
	background-color: rgba(0,0,0,.6);
	border: 1px solid #ccc;
    box-shadow: 0 1px 1px rgba(0,0,0,.2);
    color: #fff;
	position: absolute;
	top: 10px;
    right:10px;
    min-width: 300px;
    padding: 0px 20px;
	text-align: center;
	z-index: 1;
}

.polygons-map-wrapper{
	position: relative;
}

.polygon-title{
	font-size: 32px;
}


.ifound-info-window .button{
	font-size: 14px;
   	padding: 4px 10px;
}

.area-map-wrapper .gm-style-iw + div {
 	display: none;
}

/*listing status */
.prop-item.list-status:before {
	font-size:14px;
	padding:2px 15px;
	margin:2px;
}

.pdp-data.list-status:before {
	padding:5px;
}

.list-status.Active:before {
	content:'Available';
	background-color:green;
	color:white;
}

.list-status.Active.UCB:before,
.list-status.Active.CCBS:before,
.list-status.Active.Under.Contract:before {
	content:"Accepting Backup Offers";
	background-color:yellow;
	color:black;
}

.list-status.Pending:before {
	content:"Pending Sale";
	background-color:yellow;
	color:black;
}

.list-status.Closed:before {
	content:"Sold";
	background-color:red;
	color:white;
}

@media only screen and (max-width: 1160px) {
	.home .widget_ifound_quick_search .select-wrap,
	.home .widget_ifound_quick_search .number-wrap,
	.home .widget_ifound_quick_search .text-wrap {
		width: calc(17% - 20px);
	}

	.home .widget_ifound_quick_search .qs-button-wrap,
	.home .widget_ifound_quick_search .quick-search-button-wrap {
		margin-top: 10px;
	}

	.home .widget_ifound_quick_search .qs-button-wrap,
	.home .widget_ifound_quick_search .ifound-more-options,
	.home .widget_ifound_quick_search .quick-search-button-wrap {
		display: inline-block;
		float: none;
	}

	.home .quick-search input[name=bedrooms_max],
	.home .quick-search input[name=bathrooms_max],
	.home .quick-search input[name=list_price_max] {
		margin-right: 0 !important;
	}

	.home .quick-search .number-input {
		width: 49%;
	}
}

@media only screen and (max-width: 800px) {

	.home .widget_ifound_quick_search .select-wrap,
	.home .widget_ifound_quick_search .number-wrap,
	.home .widget_ifound_quick_search .text-wrap {
		margin-top: 10px;
		width: 30%;
	}

	.home .front-page-2 .flexible-widgets .widget_ifound_quick_search {
		background-color: #252A2B;
	}

	.polygons-map-wrapper{
		left: auto;
		right: auto;
		bottom: auto;
		top: auto;
		position: relative;
		z-index: 0;
	}

	.polygon-title-wrapper{
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
	}

	.polygon-title a{
		color:#fff;
		font-size: 20px;
		padding: 4px auto;
	}

}

@media only screen and (max-width: 700px) {

	.results-page .search-results-after-map.widget_ifound_save_this_search {
		margin-left: 0;
 	}

}

@media only screen and (max-width: 500px) {
	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
		.ifound-react-select-multibox-quicksearch-container {
		width: 100%;
		white-space: nowrap;
		height: 40px;
		line-height: 30px;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__control--is-focused {
		border-color: inherit;
		box-shadow: inherit;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__value-container {
		/* If you select a value, and it's too long, the "dummy input" that react-select creates ends up wrapping to
		a second row for further input. Since we only allow a single input, we don't need that second row.
		*/
		flex-wrap: nowrap;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__menu {
		/* Use the same width as the text input */
		width: 100%;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
		.ifound-react-select-multibox-quicksearch__placeholder {
		text-align: left;
		font-size: 16px;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
		.ifound-react-select-multibox-quicksearch__group-heading {
		display: flex;
		text-align: left;
		border-bottom: 1px silver solid;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
	.ifound-react-select-multibox-quicksearch__group-heading .label {
		margin-left: 10px;
	}

	.home .widget_ifound_quick_search .ifound-multibox-quicksearch-spa
		.ifound-react-select-multibox-quicksearch__option {
		text-align: left;
		font-size: 14px;
		padding: 4px 12px 4px 36px;
		white-space: nowrap;
		overflow-x: hidden;
	}


	.ifound-react-select-multibox-advanced-container
	.ifound-react-select-multibox-advanced__menu {
		/* Use the same width as the text input */
		width: 100%;
	}


	.home .widget_ifound_quick_search .select-wrap,
	.home .widget_ifound_quick_search .number-wrap,
	.home .widget_ifound_quick_search .text-wrap {
		width: 100%;
	}

	.home .widget_ifound_quick_search .quick-search-button-wrap,
	.home #quick-search-button {
		width: 100%;
	}

	.home .find-home a {
		width: 100%;
	}

	.criteria-collapse-control,
	.button.criteria-collapse-control {
		display: inline-block;
	}

	#show-criteria {
		margin-bottom: 10px;
	}

	#show-criteria-arrow {
		float: right;
		margin-right: 10px;
	}

}

@media only screen and (max-height: 550px) {

	.lookups-body .ifound-wrap {
	    max-height: 100%;
	}

}

/* End Home Page Search Widget on Genesis */

/*
 Keep this synced with other places in code using this string: khjfdgaft677d6r
*/
.fab.fa-twitter, .fab.fa-twitter-square {
	font-family: sans-serif;
	font-weight: bold;
}
.fab.fa-twitter::before, .fab.fa-twitter-square::before {
	content: "𝕏";
}
.fab.fa-twitter::before {
	font-size: 1.2em;
}
