import { ToastProvider, DefaultToastContainer } from 'react-toast-notifications';

// Use 100000 to show on top of Wordpress admin bar, which has z-index of 99999.
export const MyCustomToastContainer = ({ children, ...props }) => (
	<DefaultToastContainer {...props} style={{ zIndex: 100000 }}>
		{children}
	</DefaultToastContainer>
);

export default function provideToast(WrappedComponent) {
	return props => (
		<ToastProvider
			autoDismiss={true}
			components={{ ToastContainer: MyCustomToastContainer }}
		>
			<WrappedComponent {...props} />
		</ToastProvider>
	);
}
