<?php
/**
 *
 * This script answers all search queries from WP plugins
 *
 * TODO: Update the returned JSON to only include the fields needed.
 * So, rather than getting only the needed fields from the database
 * Get all property data, but only build and send what is needed.
 * Discuss or review the plugin use of the search results to
 * determine what is used there.
 *
 */

namespace Profound\API;

use \PropQuery;

class SearchAPIResponder extends APIResponder {
	private $idx;

	protected function getDefaultAPI() {
		return API::explicit("search", "v1.00", "json");
	}

	protected function computeResponseData() {
		$this->idx = new PropQuery('../config.ini', array('search' => $this->api));
		return $this->idx->searchProps();
	}
}