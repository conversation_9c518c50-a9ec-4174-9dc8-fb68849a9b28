<?
defined('ABSPATH') or die('You do not have access!');
if (!class_exists('iFoundCrm')) die('You do not have access!');

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

class iFoundDripTemplate extends iFoundCrm {
	use NewHooklessTrait;

	public static $the_post_type  = 'drip_template';
	private static $query_var = 'drip-template';
	private static $label_name = 'Drip Template';
	private static $label_names = 'Drip Templates';
	public static $template_data_key = 'template_data';
	// We track a version so we can properly upgrade stored data over time.
	private static $version = '1.0.0';

	protected $post_type;

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct($options = []) {
		$this->post_type = static::$the_post_type;

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', [$this, 'post_type']);
			add_action('save_post_drip_template', [$this, 'save_post_drip_template'], 10, 3);
			add_action('pre_get_posts', [$this, 'pre_get_posts']);
			// Reminder: map_meta_cap, at the least, prevents other team members from editing others' posts.
			add_filter('map_meta_cap', [$this, 'map_meta_cap'], 10, 4);

			// TODO: Finish this. See comment on the callback.
			// add_filter('views_edit-' . static::$the_post_type, [$this, 'views_filter_for_own_posts']);

			// before_delete_post has 2 parameters as of WP 5.5, but our code will still work with 1.
			add_action('before_delete_post', [$this, 'before_delete_post']);
		}
	}

	public function post_type() {
		$args = [
			'labels'               => [
				'name'          => __(static::$label_names),
				'singular_name' => __(static::$label_name),
				'add_new'       => __('Add New ' . static::$label_name),
				'add_new_item'  => __('Add New ' . static::$label_name),
				'edit_item'     => __('Edit ' . static::$label_name),
				'new_item'      => __('New ' . static::$label_name),
				'view_item'     => __('View ' . static::$label_name),
				'view_items'    => __('View ' . static::$label_names),
				'search_items'  => __('Search ' . static::$label_names),
				'all_items'     => __(static::$label_names),
				'attributes'    => __(static::$label_name . ' Attributes'),
				'not_found'     => __('No ' . static::$label_names . ' found'),
			],
			'query_var'            => static::$query_var,
			'show_ui'              => true,
			'show_in_menu'         => $this->crm_menu(),
			'show_in_nav_menus'    => false,
			'show_in_admin_bar'    => false,
			'public'               => false,
			'has_archive'          => false,
			'hierarchical'         => true,
			'supports'             => ['title'],
			'register_meta_box_cb' => [$this, 'add_drip_template_metabox'],
		];
		if (!$this->is_site_admin()) {
			$args['capability_type'] = $this->post_type;
			// Wordpress has this rule that if there is no menu item, it won't allow access to the page. Because we are
			// setting this page up as a submenu under the Contact Manager top-level menu, there won't be any (sub)menu
			// for "New Drip Template", and thus a non-admin would not be able to create a new drip template. To get
			// around this, we unset the 'show_in_menu' key. The consequence is that the user will not see where they
			// are in the menu (no menu or submenu will be highlighted) which is confusing. Worse, the 'Drip Templates'
			// submenu itself will disappear. But I can't think of a better way to deal with this. I'm thinking since
			// it's rare enough, users won't complain.
			if (strpos($this->current_url(), 'post-new.php?post_type=' . $this->post_type) !== false) {
				unset($args['show_in_menu']);
			}
		}
		register_post_type($this->post_type, $args);
	}

	// TODO: Finish this. I don't want to focus on it right now, but it works and we should use it for all custom post
	// types.
	// I got it from: https://wordpress.stackexchange.com/a/178250/27896
	// public function views_filter_for_own_posts($views) {
	// 	$author = get_current_user_id();
    //
	// 	unset($views['mine']);
    //
	// 	$new_views = array(
	// 		'publish'   => __('Published'),
	// 		'trash'     => __('Trash')
	// 	);
    //
	// 	foreach( $new_views as $view => $name ) {
    //
	// 		$query = array(
	// 			'author'      => $author,
	// 			'post_type'   => $this->post_type,
	// 		);
    //
	// 		if($view == 'all') {
    //
	// 			// $query['all_posts'] = 1;
	// 			$query['numberposts'] = -1;
	// 			$class = ( get_query_var('all_posts') == 1 || get_query_var('post_status') == '' ) ? ' class="current"' : '';
	// 			$url_query_var = 'all_posts=1';
    //
	// 		} else {
    //
	// 			$query['post_status'] = $view;
	// 			$class = ( get_query_var('post_status') == $view ) ? ' class="current"' : '';
	// 			$url_query_var = 'post_status='.$view;
    //
	// 		}
    //
	// 		$result = new WP_Query($query);
    //
	// 		if($result->found_posts > 0) {
    //
	// 			$views[$view] = sprintf(
	// 				'<a href="%s"'. $class .'>'.__($name).' <span class="count">(%d)</span></a>',
	// 				admin_url('edit.php?'.$url_query_var.'&post_type='.$this->post_type),
	// 				$result->found_posts
	// 			);
    //
	// 		} else {
    //
	// 			unset($views[$view]);
    //
	// 		}
    //
	// 	}
    //
	// 	return $views;
	// }

	public function save_post_drip_template($post_id, $post, $update) {
		if ($update) {
			$previous_template_data = get_post_meta($post_id, static::$template_data_key, true);
			$json_string = $_POST[static::$template_data_key];
			$json_string = urldecode($json_string);
			$new_template_data = json_decode($json_string, true);
			$new_template_data['version'] = static::$version;
			update_post_meta($post_id, static::$template_data_key, $new_template_data);
			if ($previous_template_data) {
				iFoundDripCampaign::new_hookless()->update_campaigns_from_updated_template($post_id,
					$previous_template_data, $new_template_data);
			}
		}
	}

	public function add_drip_template_metabox() {
		add_meta_box(
			'drip_template_meta',
			__('<i class="far fa-tint"></i> Drip Template', 'ifound'),
			[$this, 'drip_template_metabox'],
			$this->post_type,
			'advanced',
			'high'
		);
	}

	public function drip_template_metabox($post) {
		wp_enqueue_script('ifound_spa_js');
		$template_data = get_post_meta($post->ID, static::$template_data_key, true);
		$email_templates = $this->get_drip_template_email_templates('drip-email');
		$reminder_templates = $this->get_drip_template_email_templates('reminder-text');
		?>
		<div class="ifound-drip-templates-wrapper">
			Loading...
		</div>
		<script>
			jQuery(document).ready(function() {
				var domContainer = document.querySelector('.ifound-drip-templates-wrapper');
				var originalShouldAllowSubmit = function() {
					return true;
				}
				// This is how we allow our React component to be in charge of validation.
				var shouldAllowSubmit = originalShouldAllowSubmit;
				var props = {
					originalShouldAllowSubmit: originalShouldAllowSubmit,
					setShouldAllowSubmit: function(fn) {
						shouldAllowSubmit = fn;
					},
					template_data: <?= json_encode($template_data) ?>,
					emailTemplates: <?= json_encode($email_templates) ?>,
					reminderTemplates: <?= json_encode($reminder_templates) ?>,
					intervals: <?= json_encode($this->get_intervals()) ?>,
				};
				window.ifound_spa.render_app('drip_templates', domContainer, props);
				jQuery('#publish').on('click', function(event) {
					if (!shouldAllowSubmit()) {
						event.preventDefault();
					}
				});
			});
		</script>
		<?php if ($template_data) { ?>
			<div>
				<h3>What happens when I update a drip template?</h3>
				<ul>
					<li>Drip campaigns using this drip template will be affected.</li>
					<li>
						Each campaign keeps track of its upcoming step number. That number starts at one, and when each
                        step is run and emails are sent, the upcoming step number is increased by one and waits for the
                        next run.
					</li>
					<li>
						If the number of steps is decreased, campaigns that have already run that many steps will be
						deactivated and marked done.
					</li>
					<li>
						For any intervals that are changed, campaigns waiting to run steps from those changed intervals
                        will be updated to run at the new interval. If that interval has already passed, they will run
						immediately.
					</li>
					<li>
                        If steps are added to the end, but no other changes are made, no campaigns will be affected.
                    </li>
                    <li>Campaigns that are inactive will not be made active.</li>
				</ul>
			</div>
		<?php
		}
	}

	private function get_drip_template_email_templates($term) {
		$args = [
			'posts_per_page' => -1,
			'post_type' 	 => 'ifound_email',
			'author__in'     => iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
			'tax_query' 	 => [
				[
					'taxonomy' => 'ifound_email_type',
					'field' 	=> 'slug',
					'terms' 	=> $term,
				],
			],
		];
		$templates = get_posts($args);
		$template_ids_by_title = array_reduce($templates, function($carry, $x) {
			// We use the title as the key, and the ID as the value, as opposed to vice-versa, because when we use the
			// entries on the JavaScript side, iterating them happens in order of keys. As in, they'd be sorted by ID,
			// regardless of how we sort them here. Flipping the key and value gets around this.
			$carry[$x->post_title] = $x->ID;
			return $carry;
		}, []);
		uksort($template_ids_by_title, function($a, $b) {
			return strnatcmp($a, $b);
		});
		return $template_ids_by_title;
	}

	public function get_intervals() {
		return [
			'1 Day',
			'2 Days',
			'3 Days',
			'4 Days',
			'5 Days',
			'6 Days',
			'1 Week',
			'2 Weeks',
			'3 Weeks',
			'1 Month',
			'2 Months',
			'3 Months',
			'4 Months',
			'5 Months',
			'6 Months',
			'7 Months',
			'8 Months',
			'9 Months',
			'10 Months',
			'11 Months',
			'1 Year',
			'2 Years',
			'3 Years',
			'4 Years',
		];
	}

	// Don't show a user templates that belong to other users.
	public function pre_get_posts( $query ) {
		global $pagenow;
		if ($pagenow !== 'edit.php') {
			return $query;
		}

		global $typenow;
		if( current_user_can( 'edit_others_posts' ) && $typenow !== $this->post_type ) {
			return $query;
		}

		if ($typenow === $this->post_type) {
			if ($this->is_site_admin()) {
				$query->set('author__in', iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids());
			} else {
				$query->set('author', get_current_user_id());
			}
		}

		return $query;
	}

	// Check if the email template is in use by at least one drip template that has at least one drip campaign that is
	// not done.
	public function is_email_template_in_use($email_template_id) {
		$args = [
			'numberposts' => -1,
			'post_type'   => static::$the_post_type,
			'author__in'  => iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
			'fields'      => 'ids',
		];
		$drip_template_ids = get_posts($args);
		foreach ($drip_template_ids as $drip_template_id) {
			$template_data = get_post_meta($drip_template_id, static::$template_data_key, true);
			$steps = $template_data['steps'];
			$types = ['customer_template_id', 'reminder_template_id'];
			$look_for_campaigns = false;
			foreach ($steps as $step) {
				foreach ($types as $type) {
					$email_template_id_to_check = $step[$type];
					if ($email_template_id_to_check === $email_template_id) {
						$look_for_campaigns = true;
						break;
					}
				}
				if ($look_for_campaigns) {
					break;
				}
			}
			if ($look_for_campaigns) {
				if ($this->is_drip_template_in_use($drip_template_id)) {
					return true;
				}
			}
		}
		return false;
	}

	// Check if the drip template is in use by at least one drip campaign that is not done.
	private function is_drip_template_in_use($drip_template_id) {
		$campaign_args = [
			'numberposts' => 1,
			'post_type'   => iFoundDripCampaign::$the_post_type,
			'author__in'  => iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
			'fields'      => 'ids',
			'meta_query'  => [
				[
					'key'   => 'drip_template_id',
					'value' => $drip_template_id,
				],
				[
					'key'   => 'is_done',
					'value' => 'false',
				],
			],
		];
		$campaign_ids = get_posts($campaign_args);
		return count($campaign_ids) > 0;
	}

	// Wordpress doesn't have a good way to prevent trashing/deleting. You can use the pre_trash_post hook, but you can
	// only return true/false, without an explanation to the user. So we circumvent their intended use to do our own
	// thing.
	public function before_delete_post($post_id) {
		if (get_post_type($post_id) === $this->post_type) {
			if ($this->is_drip_template_in_use($post_id)) {
				$message = 'This drip template is in use by at least one drip campaign that has not ended';
				$title = 'Unable to remove this post';
				wp_die($message, $title, ['code' => 400]);
			}
		}
	}

	// Initially this function is called from our plugin update code. I don't want that code knowing about the innards
	// of drip templates. I don't want to break encapsulation. It's tough because I'm still asking for the $steps to be
	// created outside of this function. I suppose 'version' is the data value I want to ensure exists.
	public function create_template($title, $user_id, $steps, $postmetas) {
		$post_fields = [
			'post_title'   => $title,
			'post_content' => '',
			'post_status'  => 'publish',
			'post_type'    => static::$the_post_type,
			'post_author'  => $user_id,
		];
		$drip_template_id = wp_insert_post($post_fields);
		$template_data = [
			'version' => static::$version,
			'steps'   => $steps,
		];
		add_post_meta($drip_template_id, static::$template_data_key, $template_data);
		foreach ($postmetas as $key => $value) {
			add_post_meta($drip_template_id, $key, $value);
		}
	}
}
