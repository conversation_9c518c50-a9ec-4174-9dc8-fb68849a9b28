<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

 
class FeaturedImage extends iFoundAdmin {

	private $post_type		= 'featured_images';
	private $label_name 	= 'Featured Image';
	private	$label_names	= 'Featured Images';
	protected $blog_id		= 4;
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	 
	public function __construct() {
		
		add_action( 'init', array( $this, 'featured_images_post_type' ) );
		add_action( 'admin_menu', array( $this, 'add_menu_items' ) );

		add_filter( 'manage_featured_images_posts_columns' , array( $this, 'add_featured_images_columns' ) );
		add_action( 'manage_featured_images_posts_custom_column' , array( $this, 'featured_images_column'), 10, 2 );
			
	}

	public function featured_images_post_type() {

		register_post_type( $this->post_type,
			array(
				'labels' => array(
					'name' 			=> __( $this->label_names ),
					'singular_name' => __( $this->label_name ),
					'add_new_item'	=> __( 'Add New ' . $this->label_name ),
					'edit_item'		=> __( 'Edit ' . $this->label_name ),
					'new_item'		=> __( 'New ' . $this->label_name ),
					'view_item'		=> __( 'View ' . $this->label_name ),
					'view_items'	=> __( 'View ' . $this->label_names ),
					'search_items'	=> __( 'Search ' . $this->label_names ),
					'all_items'		=> __( $this->label_names ),
					'attributes'	=> __( $this->label_name . ' Attributes' ),
					'menu_name'		=> __( $this->label_names ),
				),
				'show_in_menu'			=> $this->show(),
				'menu_position'			=> 2,
				'public' 				=> true,
				'has_archive' 			=> false,
				'exclude_from_search'	=> false,
				'publicly_queryable'	=> false,
				'hierarchical' 			=> true,
				'show_in_nav_menus'		=> false,
				'show_in_admin_bar'		=> false,
				'supports'				=> array( 'title' ),
				'register_meta_box_cb'	=> array( $this, 'add_metabox' )
			)
	  	);
		
	}

	public function add_featured_images_columns( $columns ) {
		
		return array_merge( 
			array( 'cb' 						=> __( 'checkall', 'ifound' ) ),
			array( 'featured_image' 			=> __( 'Featured Image', 'ifound' ) ),
			array( 'title' 						=> __( 'Image URL', 'ifound' ) ),
			array( 'taxonomy-mls_name'			=> __( 'MLS Name', 'ifound' ) ),
			array( 'taxonomy-stylesheet' 		=> __( 'Style Sheet', 'ifound' ) )
		);
	
	}

	public function featured_images_column( $column, $id ) { 

		if ( $column == 'featured_image' ) {
	
			ob_start(); ?>

			<div><? 

				$title 	= get_the_title( $id ); ?>

				<div><image src="<? echo $title; ?>" height="200"/></div>

			</div><?

			echo ob_get_clean();

		}
		
	}

	public function add_metabox() {

		add_meta_box( 
			'featured_image_metabox', 
			__( '<i class="far fa-image"></i> Featured Image', 'ifound' ), 
			array( $this, 'featured_image_metabox'), 
			$this->post_type,
			'advanced',
            'high'
		);
		
	}

	public function featured_image_metabox() { ?>

		<table class="form-table">
					
			<tbody>

				<tr>
								
					<td><? 

						$title 	= get_the_title( get_the_ID() ); ?>

						<div><image src="<? echo $title; ?>"/></div>
					
					</td>
										
				</tr
							
			</tbody>
					
		</table><?

	}

	public function add_menu_items() {
		
		if( get_current_blog_id() == $this->blog_id ) {

			add_menu_page(
	        	__( 'Import Images', 'ifound' ),
				__( 'Import Images', 'ifound' ),
	        	'manage_options',
				'import-images',
	        	array( $this, 'import_images_page' ) 
			);

		}

	}

	public function import_images_page () { 

		if( isset( $_POST['ifound-image-import'] ) ) {

			$this->import_images();

		} 

		$stylesheets 	= get_terms ( array(
			'taxonomy'   	=> 'stylesheet',
			'fields'	    => 'names',
			'hide_empty' 	=> false
		) );

		$mls_names 		= get_terms ( array(
			'taxonomy'  	=> 'mls_name',
			'fields'	    => 'names',
			'hide_empty'   	=> false
		) ); ?>

		<h1>Import Images</h1>

		<ol>
			<li>Images must be .jpg</li>
			<li>FTP the images to wp-content/uploads/staging/ </li>
			<li>Check all the categories that apply for this set of images </li>
			<li>Click Import Now to upload images into the system. </li>
			<li>The images at wp-content/uploads/staging/ will be removed automactically.</li>
		</ol>

		<form method="post">

			<h2>Choose MLS</h2>

			<table class="form-table">

				<tbody><?

					foreach( $mls_names as $mls_name )
						$this->checkbox( 'mls_name', $mls_name ); ?>

				</tbody>

			</table>

			<h2>Choose Stylesheets</h2>

			<table class="form-table">

				<tbody><?

					foreach( $stylesheets as $stylesheet )
						$this->checkbox( 'stylesheet', $stylesheet ); ?>

				</tbody>

			</table>
			
			<div class="clear"></div>

			<input type="submit" value="Import Now" class="button button-primary">
			<input type="hidden" name="ifound-image-import" value="import-now">

		</form><?

	}

	public function checkbox( $name, $value ) { ?>

		<tr>
								
			<th scope="row"><label for="<? echo $name; ?>"><? _e( $value, 'ifound' ); ?></label></th>

			<td><input type="checkbox" name="<? echo $name; ?>[]" id="<? echo $name; ?>" value="<? echo $value; ?>"></td>

		</tr><?
		
	}

	private function import_images() {

		$the_path 	= ABSPATH . 'wp-content/uploads/staging/';
		$new_path 	= ABSPATH . 'wp-content/uploads/featured-images/';
		$url 		= $this->get_config()['featured_images_origin'] . '/wp-content/uploads/featured-images/';
		
		$handler = opendir( $the_path );
			
		while ( $photo = readdir( $handler ) ) {
				
			if( exif_imagetype( $the_path . '/' . $photo ) == IMAGETYPE_JPEG ) {
						
				$new_photo = 'featured-image-' . time() . rand( 1, 99999999 ) . '.jpg';

				copy( $the_path . '/' . $photo, $new_path .'/' . $new_photo );
				
				unlink( $the_path . '/' . $photo );

				$post_data = array(
    				'post_title' 	=> $url . $new_photo,
    				'post_type' 	=> $this->post_type,
    				'post_status'   => 'publish'
				);
				
				$object_id = wp_insert_post( $post_data );
				
				wp_set_object_terms( $object_id, $_POST['mls_name'], 'mls_name', true );
				wp_set_object_terms( $object_id, $_POST['stylesheet'], 'stylesheet', true );

			}

		}
		
		closedir( $handler );

	}

}
