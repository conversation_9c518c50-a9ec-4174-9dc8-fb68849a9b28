import React, { useState, useEffect, useRef } from 'react';
// Reminder: when I was looking for docs on 2023-10-26, I could not find them. A thread posted at
//   https://github.com/JustFly1984/react-google-maps-api/issues/3282#issuecomment-1695464573
// mentioned that they could be found at
//   https://web.archive.org/web/20230701010714mp_/https://react-google-maps-api-docs.netlify.app
import { GoogleMap, LoadScript, DrawingManager, Marker, Circle, InfoWindow } from '@react-google-maps/api';
import axios from 'axios';
import urljoin from 'url-join';
import useFetcher from '../../hooks/useFetcher';
import useLocalStorageState from '../../hooks/useLocalStorageState';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _uniq from 'lodash/uniq';
import _uniqBy from 'lodash/uniqBy';
import { milesToMeters } from '../../lib/address';
import PropTypes from 'prop-types';
import useIfoundToasts from '../../hooks/useIfoundToasts';
import { disabledHelper } from '../../lib/component';
import { useDebounce } from 'use-debounce';
import produce from 'immer';

const libraries = ['drawing'];

const containerStyle = {
	height: '900px',
};

const center = {
	lat: 33.4484,
	lng: -112.074,
};

const options = {}
let google
let map
// I'm putting this as a global because the script loading (controlled by <LoadScript> below) is expected to be global
// too, as opposed to per component.
let mapLoadPromiseResolveFn
let mapLoadPromise = new Promise(resolve => {
	mapLoadPromiseResolveFn = m => {
		map = m;
		resolve(m);
	}
})

BulkCampaignPublisher.propTypes = {
	googleMapsApiKey: PropTypes.string,
	campaigns: PropTypes.arrayOf(PropTypes.object.isRequired),
	subjectMapIcon: PropTypes.string.isRequired,
	endpoint: PropTypes.string.isRequired,
	markSelectedCampaignsPublished: PropTypes.func.isRequired,
	toggleSelectCampaign: PropTypes.func.isRequired,
};

function BulkCampaignPublisher(props) {
	const {
		googleMapsApiKey,
		campaigns: nonDebouncedCampaigns,
		subjectMapIcon,
		endpoint,
		markSelectedCampaignsPublished,
		toggleSelectCampaign,
	} = props;

	const [campaigns] = useDebounce(nonDebouncedCampaigns, 500)
	const [hasLoadedGoogleMaps, setHasLoadedGoogleMaps] = useState(false);
	const [geoForCampaignsByCampaignId, setGeoForCampaignsByCampaignId] = useState(null);
	const [infoWindowsToShow, setInfoWindowsToShow] = useState([]);
	// By "MVC Object", I mean the actual object created by Google Maps API. That's their term.
	const [markerMvcObjectsByCampaignId, setMarkerMvcObjectsByCampaignId] = useState({});
	const [polygons, setPolygons] = useState([]);
	const polygonsRef = useRef();
	polygonsRef.current = polygons;
	const [listings, setListings] = useState(null);
	// Reminder: we keep this number separate, rather than use listings.length, because a search could return more
	// results than we want to show on the map.
	const [numFound, setNumFound] = useState(0);
	const [zoom, setZoom] = useState(10);
	const key = 'bulkCampaignAutomator';
	const [startDate, setStartDate] = useLocalStorageState([key, 'startDate'], '')
	const [timeOfDay, setTimeOfDay] = useLocalStorageState([key, 'timeOfDay'], '');
	const [isPublishScheduled, setIsPublishScheduled] = useLocalStorageState([key, 'isPublishScheduled'], false);
	const { addErrorToast, showToastForFetchError } = useIfoundToasts();

	function clear() {
		setListings(null);
		setNumFound(0);
		polygons.forEach(x => x.setMap(null));
		setPolygons([]);
		setGeoForCampaignsByCampaignId(null);
		setInfoWindowsToShow([]);
	}

	useEffect(() => {
		clear();
	}, [campaigns]);

	const infoEndpoint = urljoin(endpoint, 'info')
	const urlObj = new URL(infoEndpoint, location.origin)
	urlObj.searchParams.set('ids', campaigns.map(x => x.ID).join(','))
	const url = campaigns.length ? urlObj.toString() : null
	const {
		isLoading: campaignsAreLoading,
		errorMessage: campaignsErrorMessage,
		run: runCampaignsFetch,
	} = useFetcher({
		fetchFn: ({ abortSignal }) => axios({
			url,
			signal: abortSignal,
		})
			.then(response => {
				if (response.config.signal.aborted) {
					return;
				}
				return mapLoadPromise.then(() => {
					const _geoForCampaignsByCampaignId = _map(response.data, x => {
						return {
							campaignId: x.campaignId,
							latLng: new google.maps.LatLng(x.lat, x.lng),
							streetName: x.street_name,
							address: x.address,
						};
					});
					setGeoForCampaignsByCampaignId(_geoForCampaignsByCampaignId)
					const bounds = new google.maps.LatLngBounds();
					for (const geo of _geoForCampaignsByCampaignId) {
						bounds.extend(geo.latLng)
					}
					// Don't zoom in too much. For a single listing, it would be level 22.
					google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
						this.setZoom(Math.min(this.getZoom(), 16));
					});
					map.fitBounds(bounds)
				})
			}),
		url,
	});

	// window.google won't exist until the Google Maps script has been loaded with LoadScript. Once it has loaded, we
	// can use the values in (window.)google.maps.
	function onLoadScript() {
		google = window.google;
		options.drawingControlOptions = {
			position: google.maps.ControlPosition.BOTTOM_CENTER,
			drawingModes: [
				google.maps.drawing.OverlayType.POLYGON,
			],
		};
		setHasLoadedGoogleMaps(true);
	}

	function onLoadMap(m) {
		mapLoadPromiseResolveFn(m);
	}

	// Add an InfoWindow that allows a polygon to be removed.
	const onPolygonComplete = polygon => {
		addPolygonToMap(polygon);

		setPolygons(polygons.concat(polygon));
	}

	function addPolygonToMap(polygon) {
		polygon.setMap(map);
		polygon.setEditable(true);
		polygon.setDraggable(true);

		// Add "remove" button
		const element = document.createElement('button');
		element.type = 'button';
		element.className = 'button';
		element.appendChild(document.createTextNode('Remove polygon'));
		const infowindow = new google.maps.InfoWindow();
		infowindow.setContent(element);
		element.onclick = function removePolygon() {
			// We use polygonsRef.current to use the current value of polygons, not a stale reference.
			setPolygons(polygonsRef.current.filter(x => x !== polygon))
			infowindow.close();
			infowindow.setMap(null);
			polygon.setMap(null);
		};

		polygon.addListener('click', (event) => {
			infowindow.setPosition(event.latLng);
			infowindow.open({
				map: polygon.map,
				shouldFocus: false,
			});
		});
	}

	function serializePolygons(pgons) {
		return pgons.map(pgon => pgon.getPath().getArray(x => ({ lat: x.lat(), lng: x.lng() })));
	}

	const {
		isLoading: searchIsLoading,
		errorMessage: searchErrorMessage,
		run: runFetchSearch,
		clear: clearSearch,
	} = useFetcher({
		fetchFn: () => {
			setNumFound(0);
			setListings(null);
			const url = urljoin(endpoint, campaigns[0].ID.toString(), 'search')
			return axios({
				url,
				method: 'POST',
				data: {
					polygons: serializePolygons(polygons),
				},
			})
				.then(response => {
					setListings(response.data.listings);
					setNumFound(response.data.query.numFound);
				});
		},
	});

	function runSearch() {
		runFetchSearch();
	}

	function clearListings() {
		setListings(null);
		setNumFound(0);
	}

	function validate() {
		if (!polygons.length) {
			addErrorToast("You didn't set any polygons!");
			return false;
		}
		if (isPublishScheduled) {
			if (!startDate) {
				addErrorToast("You didn't set a start date!");
				return false;
			}
			if (!timeOfDay) {
				addErrorToast("You didn't set a time of day!");
				return false;
			}
		}
		return true;
	}

	const { isLoading: publishIsLoading, run: runFetchPublish } = useFetcher({
		fetchFn: () => {
			return axios({
				url: urljoin(endpoint, 'publish'),
				method: 'PATCH',
				data: {
					ids: campaigns.map(x => x.ID),
					polygons: serializePolygons(polygons),
					is_publish_scheduled: isPublishScheduled,
					start_date: startDate,
					time_of_day: timeOfDay,
				},
			})
				.then(() => {
					markSelectedCampaignsPublished();
				})
				.catch(showToastForFetchError);
		},
	});

	function publish() {
		const isValid = validate();
		if (!isValid) {
			return;
		}
		runFetchPublish();
	}

	function addInfoWindowForCampaignId(campaignId) {
		if (infoWindowsToShow.find(x => x.campaignId === campaignId)) {
			// It's already in the list
			return;
		}
		const geoDataForCampaign = geoForCampaignsByCampaignId.find(x => x.campaignId === campaignId);
		const infoWindowData = {
			campaignId,
			latLng: geoDataForCampaign.latLng,
			address: geoDataForCampaign.address,
		};
		const newInfoWindowsToShow = infoWindowsToShow.concat(infoWindowData);
		setInfoWindowsToShow(newInfoWindowsToShow);
	}

	function removeInfoWindowForCampaignId(campaignId) {
		const newInfoWindowsToShow = infoWindowsToShow.filter(x => x.campaignId !== campaignId);
		setInfoWindowsToShow(newInfoWindowsToShow);
	}

	function unselectCampaign(campaignId) {
		removeInfoWindowForCampaignId(campaignId);
		toggleSelectCampaign(campaignId);
	}

	function onMarkerLoad(campaignId, marker) {
		const newMarkerMvcObjectsByCampaignId = produce(markerMvcObjectsByCampaignId, o => {
			o[campaignId] = marker;
			return o;
		});
		setMarkerMvcObjectsByCampaignId(newMarkerMvcObjectsByCampaignId);
	}

	function onMarkerUnmount(campaignId) {
		const newMarkerMvcObjectsByCampaignId = produce(markerMvcObjectsByCampaignId, o => {
			delete o[campaignId];
			return o;
		});
		setMarkerMvcObjectsByCampaignId(newMarkerMvcObjectsByCampaignId);
	}

	const isBlocked = campaigns.length === 0 || campaignsAreLoading || publishIsLoading || campaignsErrorMessage;
	const blockMessage = (campaignsAreLoading || publishIsLoading)
		? 'Loading...'
		: campaignsErrorMessage
			? 'Error'
			: campaigns.length === 0
				? 'Select campaign(s)'
				: ''
	;
	let uniqueMapMarkers = [];
	if (geoForCampaignsByCampaignId) {
		// We're unique-ifying the markers that we show. The idea is that only one would ever be shown because I'm
		// assuming one would overlap the other(s). So you'd never be able to pick ones behind the front one. However,
		// there could be multiple campaigns with the same lat/lng. I'm going with a we'll-cross-that-bridge-when-we-get
		// there attitude.
		uniqueMapMarkers = _uniqBy(geoForCampaignsByCampaignId,
			x => x.latLng.lat().toString() + x.latLng.lng().toString());
	}

	return <div className="owl" style={{ marginTop: '10px' }}>
		{campaignsErrorMessage && <div className="h-owl error" style={{
			justifyContent: 'space-between',
			padding: '10px'
		}}>
			<div>Error loading campaign(s): {campaignsErrorMessage}</div>
			<button className="button" onClick={runCampaignsFetch}>Retry</button>
		</div>}
		<div className="h-owl" style={{
			marginTop: '10px',
			marginBottom: '10px',
			opacity: isBlocked ? 0.3 : 1,
			pointerEvents: isBlocked ? 'none' : 'initial'
		}}>
			<button
				className="button"
				{...disabledHelper(searchIsLoading)}
				onClick={runSearch}
			>
				{searchIsLoading
					? <i className="fa fa-spinner fa-spin" />
					: <i className="fa fa-search"/>
				}
				{' '}
				Show listings
			</button>
			<div className="h-owl">
				<div>Publish:</div>
				<label>
					<input
						type="radio"
						checked={!isPublishScheduled}
						onChange={() => setIsPublishScheduled(false)}
					/> Now
				</label>
				<label>
					<input
						type="radio"
						checked={isPublishScheduled}
						onChange={() => setIsPublishScheduled(true)}
					/> Scheduled
				</label>
				<div>Start Date: <input
					type="date"
					value={startDate}
					onChange={e => setDate(e.target.value)}
					{...disabledHelper(!isPublishScheduled)}
				/></div>
				<div>
					Time of Day: <input
					type="time"
					value={timeOfDay}
					onChange={e => setTimeOfDay(e.target.value)}
					{...disabledHelper(!isPublishScheduled)}
				/>
				</div>
			</div>
			<button className="button" onClick={publish}>
				Publish Campaign(s)
			</button>
		</div>
		{searchErrorMessage && <div className="h-owl error" style={{
			justifyContent: 'space-between',
			padding: '10px'
		}}>
			<div>Error fetching listings: {searchErrorMessage}</div>
			<button className="button" onClick={clearSearch}>Ok</button>
		</div>}
		{listings && <div className="h-owl">
			<div>
				Num listings: {numFound}
				{numFound > listings.length ? ', showing first ' + listings.length : ''}
			</div>
			<button className="button" onClick={clearListings}>Clear Listings</button>
		</div>}
		<div style={{ position: 'relative' }}>
			<LoadScript
				libraries={libraries}
				googleMapsApiKey={googleMapsApiKey}
				onLoad={onLoadScript}
				loadingElement={<span>Loading map...</span>}
			>
				{isBlocked && <div className="full-cover">
					<div style={{ fontSize: '64px', color: 'black' }}>{blockMessage}</div>
				</div>}
				{hasLoadedGoogleMaps && <GoogleMap
					onLoad={onLoadMap}
					mapContainerStyle={containerStyle}
					center={center}
					zoom={zoom}
				>
					{uniqueMapMarkers.map(x => <Marker
						key={x.latLng.lat().toString() + x.latLng.lng().toString()}
						position={x.latLng}
						icon={{
							url: subjectMapIcon,
							scaledSize: new google.maps.Size(40, 60),
						}}
						onClick={() => addInfoWindowForCampaignId(x.campaignId)}
						onLoad={marker => onMarkerLoad(x.campaignId, marker)}
						onUnmount={marker => onMarkerUnmount(x.campaignId)}
					/>)}
					{infoWindowsToShow.map(x => <InfoWindow
						key={x.latLng.lat().toString() + x.latLng.lng().toString()}
						position={x.latLng}
						onCloseClick={() => removeInfoWindowForCampaignId(x.campaignId)}
						anchor={markerMvcObjectsByCampaignId[x.campaignId]}
					>
						<div style={{ marginTop: '10px' }}>
							<div>{x.address}</div>
							<div style={{ marginTop: '10px' }}>
								<button onClick={() => unselectCampaign(x.campaignId)}>Remove</button>
							</div>
						</div>
					</InfoWindow>)}
					{
						(listings || []).map(x => <Marker
							key={`${x.location_0_coordinate}${x.location_1_coordinate}`}
							position={{lat: x.location_0_coordinate, lng: x.location_1_coordinate}}
						/>)
					}
					<DrawingManager
						drawingMode="polygon"
						onPolygonComplete={onPolygonComplete}
						options={options}
					/>
				</GoogleMap>}
			</LoadScript>
		</div>
	</div>;
}

export default BulkCampaignPublisher;
