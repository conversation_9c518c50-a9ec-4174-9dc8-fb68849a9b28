# IDX Search - TaskBump cases

This page contains links to cases related to the [IDX / Property Search](PropertySearch.md) functionality.

[TOC]

## Switching to Solr

### Main Case

* [1026: Switch property search to use full-text search engine](https://taskbump.com/case-details/54ee0862f80df960c000002c/1026-switch-property-search-to-use-full-text-search-engine)

### History

* [1180: Severely degraded IDX performance](https://taskbump.com/case-details/55428805f80df9377e0001f3/1180-severely-degraded-idx-performance) - case which predicated switch to Solr

### Support cases

Development

* [1303: Deploy Solr & configure Admin access over TLS in production](https://taskbump.com/case-details/55a44550f80df95daf000058/1303-deploy-solr-configure-admin-access-over-tls-in-production)
* [1304: Cron script to check for properties missing Latitude + Longitude](https://taskbump.com/case-details/55a446e9f80df929fc000007/1304-cron-script-to-check-for-properties-missing-latitude-longitude)
* [1301: New Relic monitoring of new node.js IDX server](https://taskbump.com/case-details/55a43d78f80df95daf000032/1301-new-relic-monitoring-of-new-node-js-idx-server)
* [1296: IDX Search testing script](https://taskbump.com/case-details/55a3ef0df80df9046c000036/1296-idx-search-testing-script)

Debug

* [1330: Easily view & click IDX URLs requested by WordPress](https://taskbump.com/case-details/55b2c038f80df96875000038/1330-easily-view-click-idx-urls-requested-by-wordpress)
* [777: Use GET requests for IDX queries](https://taskbump.com/case-details/5474bc207cdbd65a2b000030/777-use-get-requests-for-idx-queries)

Solr & RETS

* [1318: Differential updates for Solr](https://taskbump.com/case-details/55ac324b51a31e4520000015/1318-differential-updates-for-solr)
* [1340: Sync all properties from RETS during sync](https://taskbump.com/case-details/55b85209f80df91d4a000011/1340-sync-all-properties-from-rets-during-sync)

Misc

* [1326: Aggregate Statistics for IDX searches](https://taskbump.com/case-details/55b15b4dd0b9383821000004/1326-aggregate-statistics-for-idx-searches)

### Issues / Bugs

Misc / Early Issues

* [1295: Listings on festivalrealty.com not appearing](https://taskbump.com/case-details/55a05bc97c6d8b73f1000000/1295-listings-on-festivalrealty-com-not-appearing)
* [1321: "No Pool" in IDX search not functioning properly](https://taskbump.com/case-details/55afc2947c6d8b3299000007/1321--no-pool-in-idx-search-not-functioning-properly)
* [1322: Featured Listings not working on both Jen Cline sites.](https://taskbump.com/case-details/55afdca77c6d8b2d2c000007/1322-featured-listings-not-working-on-both-jen-cline-sites)
* [1325: Case insensitive matching for Solr](https://taskbump.com/case-details/55b15912f80df96e6f000008/1325-case-insensitive-matching-for-solr)
* [1332: http://betsiemelter.com/ community links not working](https://taskbump.com/case-details/55b684337c6d8b7ddb000007/1332-http-betsiemelter-com-community-links-not-working)
* [1333: Map displayed zoomed out to world view](https://taskbump.com/case-details/55b687b87c6d8b7ddb00000c/1333-map-displayed-zoomed-out-to-world-view)
* [1356: Match cities exactly for IDX searches](https://taskbump.com/case-details/55c58368f80df932eb00001f/1356-match-cities-exactly-for-idx-searches)
* [1331: Images not displaying on detail pages on all sites.](https://taskbump.com/case-details/55b408c70711ee6ba0000001/1331-images-not-displaying-on-detail-pages-on-all-sites)

User Error

* [1328: http://sharonmillersells.com/ trouble with IDX for Horse properties](https://taskbump.com/case-details/55b1d6127c6d8b5796000006/1328-http-sharonmillersells-com-trouble-with-idx-for-horse-properties)

Performance

* [1344: Old IDX Server Response time rising](https://taskbump.com/case-details/55b9ba63f80df9770b00003c/1344-old-idx-server-response-time-rising)

RETS / Solr sync related:

* [1354: Property Details URLs with bad MLS ID](https://taskbump.com/case-details/55c57115f80df932eb000017/1354-property-details-urls-with-bad-mls-id)
* [1363: Festival Realty property supposedly missing images](https://taskbump.com/case-details/55d518ddf80df92a63000001/1363-festival-realty-property-supposedly-missing-images)
* [1359: Remove duplicates from RETS sync](https://taskbump.com/case-details/55d206905047f0174d000001/1359-remove-duplicates-from-rets-sync)

Refactor & Coding errors:

* [1351: Search results blank for Prescott IDX pages](https://taskbump.com/case-details/55c270714632d0123c000008/1351-search-results-blank-for-prescott-idx-pages)
* [1368: Cell Phone Search Near Me not Working](https://taskbump.com/case-details/55db8ea0bfc2e252fa000001/1368-cell-phone-search-near-me-not-working)

## Closed Listings

* [1122: Display Closed Listings in ARMLS](https://taskbump.com/case-details/5522d2314f98dd798000000e/1122-display-closed-listings-in-armls)
* [1347: Closed listings UI changes](https://taskbump.com/case-details/55baae905047f01406000011/1347-closed-listings-ui-changes)
* [1324: IDX Detail pages not redirecting correctly](https://taskbump.com/case-details/55b118270711ee5fde000001/1324-idx-detail-pages-not-redirecting-correctly)
* [1341: Specify relative time ranges with searches](https://taskbump.com/case-details/55b8fbe9f80df91d4a000051/1341-specify-relative-time-ranges-with-searches)

## Old Cases

* [565: Second Quick Search Widget](https://taskbump.com/case-details/533510264f98dd4fb5000002/565-second-quick-search-widget)

## TODO

* [1302: Solr extension for New Relic](https://taskbump.com/case-details/55a441ddf80df95daf00004b/1302-solr-extension-for-new-relic)
* [1298: Error reporting service for new IDX server](https://taskbump.com/case-details/55a439bdf80df95daf000015/1298-error-reporting-service-for-new-idx-server)
* [1024: Search Box with auto-complete for different criteria](https://taskbump.com/case-details/54ec0cea4f98dd53f7000005/1024-search-box-with-auto-complete-for-different-criteria)
* [1339: Switch to node.js script for RETS sync](https://taskbump.com/case-details/55b81e04f80df91d4a000000/1339-switch-to-node-js-script-for-rets-sync)
