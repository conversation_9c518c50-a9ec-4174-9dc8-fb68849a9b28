<?
/**
 * iFOUND_results class
 *
 * Display MLS search results pages.
 *
 * @package iFOUND
 * @since 1.0.0
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

class iFoundResults extends iFoundIdx {
    use UtilTrait;

	/**
	 * Results.
	 *
	 * @since 1.0.0
	 * @access private
	 * @var object $results The MLS data.
	 */

	private $results;

	private $results_settings;
	private $stats_settings;


	/**
	 * init iFOUND_results class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct() {


		/**
		 * Display Map
		 *
		 * @since 1.0.0
		 */

		add_action( 'ifound_display_map', array( $this, 'display_map' ), 10, 2 );

		add_action( 'ifound_display_map_wrapper', array( $this, 'display_map_wrapper' ), 10, 2 );
		/**
		 * Display Results
		 *
		 * @since 1.0.0
		 */

		add_action( 'ifound_display_results', array( $this, 'display_results' ), 10, 2 );

		add_action( 'ifound_single_listing', array( $this, 'single_listing' ), 10, 3 );

		/**
		 * Display Pagination
		 *
		 * @since 1.0.0
		 */

		add_action( 'ifound_display_pagination', array( $this, 'display_pagination' ), 10, 1 );

		/**
		 * No Results
		 *
		 * @since 1.0.0
		 */

		add_action( 'ifound_no_results', array( $this, 'no_results' ), 10, 1 );

		/**
		 * Price Stats
		 *
		 * @since 1.0.0
		 */

		add_action( 'ifound_price_stats', array( $this, 'price_stats' ), 10, 2 );
		add_action( 'ifound_stats_only', array( $this, 'stats_only' ), 10, 2 );
		add_action('ifound_subject_info', [$this, 'subject_info']);

	}


	/**
	 * Display Map
	 *
	 * Display a map with a pin for each property.
	 *
	 * @since 1.0.0
	 * @since 3.6.5 Add conditions to enqueue scripts.
	 *
	 * @param  object $results The results object.
	 */

	public function display_map( $results, $extra = [] ) {

		if( ! isset( $results->display['hide_map'] ) ) {

			wp_enqueue_script( 'results_map_js' );

			$map_id = apply_filters( 'ifound_map_id', false );

			do_action( 'ifound_map_object', $results, $map_id, $extra ); ?>

			<div class="results-map">

				<div class="ifound-wrap">

					<div id="ifound-map-<? echo $map_id; ?>" class="ifound-results-map"></div>
					<span id="color-palette" class="ifound-color-palette"></span>
				</div>

			</div><?

		}

	}

	public function display_map_wrapper( $results, $extra = [] ) {
		?> <div class="map-search">
			<div class="ifound-wrap">
				<div class="map-heading"><i class="fal fa-map"></i><? _e(' Map', 'ifound'); ?></div>
				<div class="map-body"><? $this->display_map($results, $extra); ?></div>
			</div>
		</div> <?
	}

	private function get_stat_name_for_humans($stat_name) {
		$stat_name_humans = ucwords(str_replace('_', ' ', $stat_name));
		if ($stat_name === 'close_price') {
			$stat_name_humans = 'Sold Price';
		} else if ($stat_name === 'list_price') {
			$stat_name_humans = 'Sale Price';
		} else if ($stat_name === 'price_sqft') {
			$stat_name_humans = 'Price / Sqft';
		} else if ($stat_name === 'computed_days_on_market') {
			$stat_name_humans = 'Days on Market';
		}
		return $stat_name_humans;
	}

	private function get_facet_name_humans($facet_name) {
		$facet_display_names_map = [
			'min' => 'Low',
			'median' => 'Median',
			'max' => 'High',
			'mean' => 'Average',
		];
		return $facet_display_names_map[$facet_name];
	}

	private function get_facet_value_info($value, $stat_name) {
		$value_info = ['prefix' => ''];
		if ($stat_name !== 'computed_days_on_market') {
			$value_info['prefix'] = '$';
		}
		$value_info['value'] = number_format((float)$value, 0, '.', ',');
		return $value_info;
	}

	public function stats_only($results, $extra = []) {
		// $stats_input is the search params, and tells us which stats to show.
		$stats_input = $extra['stats'];
		// $stats holds the results from the search.
		$stats = $results->stats->fields;
		?>

		<script>
			window.ifound_stats_only = <?= json_encode($stats) ?>;
		</script>
		<div class="ifound-stats-only">
			<?
			foreach ($stats_input['fields'] as $stat_name => $facets) {
				if (count(array_filter($facets)) === 0) {
					continue;
				}
				$stat_name_humans = $this->get_stat_name_for_humans($stat_name);
				foreach ($facets as $facet_name => $facet_on) {
					if (!$facet_on) {
						continue;
					}
					$facet_name_humans = $this->get_facet_name_humans($facet_name);
					$value_info = $this->get_facet_value_info($stats->$stat_name->$facet_name, $stat_name);
					?>
					<div class="facet <?= $stat_name ?>-stat-only facet-<?= $facet_name ?>">
						<div class="ifound-title-stats-only"><?= $facet_name_humans ?> <?= $stat_name_humans ?></div>
						<div class="ifound-value-stats-only">
							<?= $value_info['prefix'] ?><span class="ifound-counter"><?= $value_info['value'] ?></span>
						</div>
					</div>
					<?
				}
			}
			?>
		</div>

		<?php
	}

	public function price_stats( $results, $extra = [] ) {

		if ( ! isset( $results->display['hide_stats'] ) ) {

			if ( ! $results->listings ) return;

			?>
			<div class="ifound-price-stats">
				<div class="ifound-wrap">
					<div class="price-stats">
						<div class="ifound-wrap">
							<div class="total"><h2>Search Results: <span><?= number_format($results->query->numFound) ?> Properties</span></h2></div>
							<?= $this->price_stats_inner($results, $extra) ?>
						</div>
					</div>
				</div>
			</div>
			<?php
		}
	}

	private function price_stats_inner($results, $extra) {
		if ($results->meta->mls_class !== 'res') {
			return;
		}
		if ( ! $results->stats ) return;

		// $stats_input is the search params, and tells us which stats to show.
		$stats_input = $extra['stats'];
		if (!$stats_input || $stats_input['show'] === 'default') {
			$stats_input['fields'] = $this->get_default_stat_fields();
		}
		// $stats holds the results from the search.
		$stats = $results->stats->fields;
		// The default search is for active listings, but we show a stat for Sold Price, which we compute over the
		// previous 3 months. To make it clear, we display a hint. But if the search intentionally included closed
		// listings, we don't need the hint, as the page (title/text) should the context clear.
		$show_hints = true;
		if (isset($results->input_obj['list_status']) && !empty(array_intersect(['Closed', 'Sold'], $results->input_obj['list_status']))) {
			$show_hints = false;
		}

		// Ensure list price comes after close price. Dale wants it this way for aesthetic reasons, I think mainly
		// to help consumers understand the timeline nature, and help explain why the values COULD be quite
		// different.
		$this->util()->move_item($stats_input['fields'], 'list_price', 'down', 'close_price');

		$trends_inputs = [
			'query' => $results->input_obj,
			'stats' => $extra['stats'],
		];
		$trends_inputs_json = json_encode($trends_inputs);
		$trends = [
			'endpoint' => rest_url('ifound/' . iFOUND_PLUGIN_VERSION . '/trends'),
			'inputs' => $trends_inputs_json,
			// Include a signature so the input can't be tampered with.
			// I don't know if SECURE_AUTH_KEY is a good choice. I can't find much info about it,
			// so I can't find any reason why it wouldn't be a good choice.
			'signature' => hash_hmac('sha256', $trends_inputs_json, SECURE_AUTH_KEY),
		];

		ob_start();
		?>

		<script>
			window.ifound_trends = <?= json_encode($trends) ?>;
		</script>
		<div>
			<button class="hide_show_stats hide-show">Show stats</button>
		</div>
		<div class="items">
			<?
			foreach ($stats_input['fields'] as $stat_name => $facets) {
				if (count(array_filter($facets)) === 0) {
					continue;
				}
				$stat_name_humans = $this->get_stat_name_for_humans($stat_name);
				?>
				<div class="stats-data <?= $stat_name ?>-stat">
					<div class="heading">
						<?= $stat_name_humans ?>
						<? if ($show_hints): ?>
							<span class="hint">
								<?
								if ($stat_name === 'close_price' || $stat_name === 'computed_days_on_market') {
									echo 'Last 3 mo.';
								} else {
									echo 'Today';
								}
								?>
							</span>
						<? endif ?>
					</div>
					<div class="facets">
						<?
						foreach ($facets as $facet_name => $facet_on) {
							if (!$facet_on) {
								continue;
							}
							$facet_name_humans = $this->get_facet_name_humans($facet_name);
							$value_info = $this->get_facet_value_info($stats->$stat_name->$facet_name, $stat_name);
							?>
							<div class="heading"><?= $facet_name_humans ?></div>
							<div class="facet facet-<?= $facet_name ?>">
								<?= $value_info['prefix'] ?><span class="ifound-counter"><?= $value_info['value'] ?></span></div>
							<?
						}
						?>
					</div>
				</div>
				<?
			}
			?>
		</div>
        <?php
            // We decided to not show trends, at least temporarily.
        ?>
		<div style="display:none;">
			<div>
				<button class="hide_show_trends hide-show">Show trends <i class="fa fa-spinner fa-spin" style="display: none;"></i></button>
			</div>
			<div class="error"></div>
		</div>
		<div class="ifound_trends" style="display: none;">
		</div>
		<?
		return ob_get_clean();
	}

	public function subject_info($save_this_id) {
		$params = get_post_meta($save_this_id, 'params', true);
		$mls_id = isset($params['nearby']['mls_id']) ? $params['nearby']['mls_id'] : null;
		if (!$mls_id) {
			return;
		}
		$contact_id = get_post_meta($save_this_id, 'contact_id', true);
		$owned_listings = get_post_meta($contact_id, 'owned_listings', true);
		$subject = isset($owned_listings['listings'][$mls_id]) ? $owned_listings['listings'][$mls_id]['data'] : null;
		if (!$subject) {
			return;
		}
		$address = apply_filters('ifound_format_listing_address', $subject);
		$sale_price = $subject['ListPrice'];
		$close_price = $subject['ClosePrice'];
		$close_date = $subject['CloseDate'];
		$year_built = $subject['YearBuilt'];
		$square_feet = $subject['SquareFeet'];
		$beds = $subject['Beds'];
		$bathrooms = $subject['Bathrooms'];

		?>
		<div class="subject-info">
			<h2>Subject Property</h2>
			<div class="radius-hint">Showing properties within <?= $params['nearby']['radius'] ?> mile(s) of subject address</div>
			<h3><?= $address ?></h3>
			<div class="ifound-wrap">
				<div class="one-half first">
					<? if ($close_date): ?>
						<div><span class="label">Close Price:</span> <?= $close_price ?></div>
						<div><span class="label">Close Date:</span> <?= apply_filters( 'pretty_date_only', $close_date ) ?></div>
					<? else: ?>
						<div><span class="label">Sale Price:</span> <?= $sale_price ?></div>
					<? endif ?>
					<div><span class="label">Year Built:</span> <?= $year_built ?></div>
				</div>
				<div class="one-half">
					<div><span class="label">Square Feet:</span> <?= $square_feet ?></div>
					<div><span class="label">Beds:</span> <?= $beds ?></div>
					<div><span class="label">Bathrooms:</span> <?= $bathrooms ?></div>
				</div>
			</div>
		</div>
		<?php
	}

	private function get_results_body() {
		$maybeNoFollow = $this->util()->is_user_agent_bot(['include_google' => false]) ? 'rel="nofollow"' : '';
		$is_user_agent_facebook = $this->util()->is_user_agent_facebook();
		$link_with_href = $is_user_agent_facebook ? '' : 'href="{DetailsURL}"';
		$results_body = <<<EOT
<div class="results-wrapper {Classes}">
	<div class="ifound-wrap results">
		<div class="results-image">
			<div class="ifound-wrap">
				<a {$maybeNoFollow} {$link_with_href}>
					<img class="results-img" src="{Image}">
				</a>
				{VirtualTourSection}
				{OpenHouse}
			</div>
		</div>
		<div class="results-data">
			<div class="ifound-wrap">
				<h2><a href="{DetailsURL}">{results_h2}</a></h2>
				<div class="results-status">
					<div class="status-wrap">{Status}</div>
				</div>
				{results_content}
			</div>
		</div>
		<div class="results-buttons">
			<div class="ifound-wrap">
				<a {$maybeNoFollow} {$link_with_href} class="button results-button">View Details</a>
			</div>
			{SavePropButton}
		</div>
	</div>
</div>
EOT;
		return $results_body;
	}

	/**
	 * Display Results
	 *
	 * Display the results on a page.
	 *
	 * @since 1.0.0
	 * @since 2.3.0 Move before and after hooks here.
	 * @since 2.5.9 Add doing campaign button.
	 *
	 * @param  object $results The results object.
	 * @param  object $extra Optional. Schema: ['mls_class' => string].
	 * @return bool  False if no listings.
	 */

	public function display_results( $results, $extra = [] ){

		wp_enqueue_style( 'search_results_css' );

		do_action( 'ifound_before_results', $results ); ?>

		<div class="results-section">
			<div class="ifound-wrap"><?

				if( empty( $results->listings ) || $results->backup ) {

					do_action( 'ifound_no_results', $results );

				}

				if ( ! empty( $results->listings ) ) {

					$results_body = $this->get_results_body();

					foreach( $results->listings as $listing ) { ?>

						<div class="ifound-results" name="<? echo $listing->ListingID; ?>">

							<div class="zoom-marker-close">

								<i class="fal fa-window-close"></i>

							</div><?

							do_action( 'ifound_single_listing', $results_body, $listing, $extra );

							$this->doing_campaign_button( $listing ); ?>

						</div><?

					}

				} ?>

			</div>

		</div><?

		do_action( 'ifound_after_results', $results );

	}

    private function get_open_house_result_section($listing) {
        if (isset($listing->open_houses) && count($listing->open_houses)) {
            $open_house_datetime = $listing->open_houses[0]->StartDateString;
	        $html = <<<EOT
<div class="ifound-open-house-datetime">
    <i class="fal fa-clock"></i> {$open_house_datetime}
</div>
EOT;
	        return $html;
		}
		return '';
	}

	private function get_virtual_tour_section($listing) {
		ob_start();
		?>
		<div class="ifound-virtual-tour">
			<? if (isset($listing->VirtualTour)): ?>
			<a class="button vt-link" href="<?= $listing->VirtualTour ?>" target="_blank">
				<i class="fal fa-video-plus"></i> Virtual Tour
			</a>
			<? endif ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Single Listing
	 *
	 * Display a single listing.
	 *
	 * @since 3.2.0
	 *
	 * @param string $results_body The HTML for the listing body.
	 * @param object $listing      A single listing object.
	 * @param object $extra        Optional. Schema: ['mls_class' => string].
	 */

	public function single_listing( $results_body, $listing, $extra = [] ) {
		$listing->Image = apply_filters('ifound_check_image', $listing->img_url);
		$listing->Status = apply_filters('ifound_check_status', $listing, $extra);
		$listing->Classes = apply_filters('ifound_classes', $listing);
		$listing->VirtualTourSection = $this->get_virtual_tour_section($listing);
		$listing->OpenHouse = $this->get_open_house_result_section($listing);

		$content = apply_filters( 'ifound_filter_merge_tags', $results_body, $listing );

		$content = apply_filters( 'ifound_detail_url', $content, $listing );
		$content = apply_filters( 'ifound_results_h2', $content, $listing );

		// Don't show the Realtracs logo if the listing is owned by the broker displaying the website. For now, for
		// simplicity and because we only have a single Realtracs client, I'm hard-coding their office IDs. Eventually we'll
		// want a way for iFound staff to set these MLS Office IDs per site. The reason we want iFound staff to do it is
		// because Dale said that the agent didn't know the numbers for his broker. So I had to go look them up, based on the
		// office name. Here's how I did that, for this example of Benchmark Realty, LLC:
		// select ListOfficeMlsId, ListOfficeName from realtracs_property_res where ListOfficeName like '%benchmark%' group by ListOfficeMlsId, ListOfficeName;
		$mls_office_ids = [
			'1760',
			'3015',
			'3222',
			'3773',
			'3865',
			'4009',
			'4417',
		];
		if (iFoundIdx::mls_name() === 'realtracs' && in_array($listing->ListOfficeID, $mls_office_ids, true)) {
			$s1 = '<img src="https://ifoundagent.com/mls-assets/realtracs/realtracs-40x19.png" alt="MLS Real Estate Logo" />';
			$s2 = '<img src="https://ifoundagent.com/mls-assets/realtracs/realtracs-40x19.png" style="visibility: hidden;" alt="MLS Real Estate Logo" />';
			$content = str_replace($s1, $s2, $content);
		}

		echo $content;

	}

	/**
	 * Doing Campaign Button
	 *
	 * The button to select MLS IDs un the Campaign Builder.
	 *
	 * @since 3.2.0
	 *
	 * @param object $listing A single listing object.
	 */

	public function doing_campaign_button( $listing ) {

		if( defined( 'DOING_CAMPAIGN' ) ) { ?>

			<div class="doing-campaign-wrapper">

				<div class="ifound-wrap">

					<div
						class="button button-primary doing-campaign-button dynamic-input-array mls-id-input"
						label="MLS ID"
						name="mls_id"
						display="<? echo $listing->ListingID; ?>"
						value="<? echo $listing->ListingID; ?>"
					>
						<? _e( 'Include', 'ifound' ); ?>
					</div>

				</div>

			</div><?

		}

	}

	/**
	 * Display Pagination
	 *
	 * Let's display pagination for our results.
	 * TODO: Add ajax for pagination.
	 *
	 * @since 1.0.0
	 * @since 2.3.0 Move before and after hooks here.
	 * @since 3.6.5 Enqueue paging_js
	 *
	 * @param  object $results The results object..
	 */

	public function display_pagination( $results ) {

		if( ! isset( $results->display['hide_paging'] ) ) {

			$paging = $results->paging->parseable;

			if( $paging->total_items <= $paging->items_per_page ) return;

			$pages = ceil( $results->paging->total / $paging->items_per_page );

			/** Do not allow too many pages if high current page number */
			$page = $paging->current_page - 2;

			/** Start with page 2 so we alwats have a page 1 */
			if( $page < 2 ) $page = 2;
			$i = 1;

			$first_current = $paging->current_page == 1 ? 'current' : '';

			$input_obj = ! empty( $results->input_obj ) ? $results->input_obj : (object) array( 'pp' => $paging->current_page );

			do_action( 'ifound_before_pagination', $results ); ?>

			<div class="ifound-pagination center">

				<div class="ifound-wrap">

					<script>
					if(typeof input_obj === 'undefined') var input_obj = [];
					input_obj.push(<? echo json_encode( $input_obj ); ?>);</script>

					<div new_page="1" class="button paging next-page <? echo $first_current; ?>">1</div>

					<?
					while( $page <= $pages )	{

						if( $page > $pages ) break;

						/** Limit to 6 buttons total, we always have the first and last page. */
						if( $i == 6 ) $page = round( $pages );

						$current = $page == $paging->current_page ? 'current' : ''; ?>

						<div new_page="<? echo $page; ?>" class="button paging next-page <? echo $current; ?>"><? echo $page; ?></div><?

						$page++;

						$i++;

					} ?>

				</div>

			</div><?

			do_action( 'ifound_after_pagination', $results );

		}

	}

	/**
	 * No Results
	 *
	 * This is what we do if we have an empty query.
	 *
	 * @since 1.0.0
	 *
	 * @param object $results The results object..
	 */

	public function no_results( $results ) {
		if($results === NULL) return;
		$default = 'There are no properties that meet your filters.';

		$msg = empty( $results->display['zero_results_message'] ) ? $default : $results->display['zero_results_message']; ?>

		<div class="ifound_no_results">

			<div class="ifound-wrap">

				<div class="no_results_msg"><? _e( $msg, 'ifound' ); ?></div>
				<!-- Hide Display Map Button -->
				<script>
				if(typeof url === 'undefined') {
						var url = window.location.href;
				}
				if(url.includes('/advanced-search/')  || url.includes('/listing-search/')) {
					document.getElementsByClassName('map-button')[0].style.display = 'none';
				} else {
					let shortcodes = jQuery('.shortcode-results');
					for(let shortcode of shortcodes) {
						if(shortcode.getElementsByClassName('ifound_no_results').length > 0) {
							shortcode.getElementsByClassName('map-button')[0].style.display = 'none';
						}
					}
				}
				</script>
				<? if (defined('DOING_SAVED_SEARCH')) { ?>
					<? _e('Use the Modify and Rerun Search button to choose more relaxed criteria') ?><? _e( '.', 'ifound' ); ?>
				<? } else { ?>
				<div class="no_results_start_new">
					<? _e( 'Start a new ', 'ifound' ); ?>
					<a href="<? echo site_url( '/listing-search/' ); ?>">
						<? _e( 'search', 'ifound' ); ?>
					</a><? _e( '.', 'ifound' ); ?>
				</div>
				<? } ?>

			</div>

		</div><?

	}
}
