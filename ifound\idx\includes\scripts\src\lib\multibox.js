import axios from 'axios';
import debounce from 'lodash.debounce';
import { components } from 'react-select';
import groupBy from 'lodash/groupBy';

function loadOptionsCallback(inputValue, endpoint, labelMap, mlsClass, cb) {
    // For dev, mainly when I was working on styling.
    // return cb([{
    //     key: 'address',
    //     label: 'Address',
    //     options: [{
    //         label: 'first label',
    //         value: {
    //             mlsId: 1,
    //             fullAddress: '123 Main St',
    //         },
    //     }],
    // }])
    axios({
        url: endpoint,
        params: {
            q: inputValue,
            mls_class: mlsClass,
        },
    })
        .then(response => {
            const data = response.data;
            const opts = [];
            for (const [key, values] of Object.entries(data)) {
                if (values.length) {
                    const group = {
                        key,
                        label: labelMap[key],
                        options: values.map(x => {
                            let label = x;
                            if (key === 'mls_id') {
                                label = x.mlsId + ' - ' + x.fullAddress;
                            } else if (key === 'address') {
                                label = x.fullAddress;
                            } else if (key === 'school') {
                                label = x.value;
                                // If there are duplicates, we call out the school type using parens
                                const schoolsByName = groupBy(values, 'value');
                                if (schoolsByName[x.value].length > 1) {
                                    const typeName = labelSubTypeMap[x.type];
                                    label = `${x.value} (as ${typeName})`;
                                }
                            } else if (key === 'school_district') {
                                label = x.value;
                                // We don't yet split out school districts by type (elementary, middle, high), the way
                                // that we do with schools.
                            }
                            const ret = {
                                value: x,
                                label,
                                type: key,
                            }
                            return ret;
                        }),
                    };
                    opts.push(group);
                }
            }
            cb(opts);
        })
        .catch(error => {
            console.log('error', error)
            // It's sad that there isn't a better way to handle this. I got this idea from:
            // https://github.com/JedWatson/react-select/issues/1528#issuecomment-277351348
            cb([{ label: 'Something went wrong', value: 'error', isDisabled: true }]);
        });
}

export const debouncedLoadOptionsCallback = debounce(loadOptionsCallback, 667);

export function buildLabelMap(searchType) {
    const labelMap = {
        address: 'Address',
        city: 'City',
        township: 'Township',
        county: 'County',
        zip: 'Zip Code',
        school: 'School',
        school_district: 'School District',
        mls_id: 'MLS ID',
        // Dale likes the term Neighborhood. That's fine for quick search, but it doesn't fit well with all our existing
        // user-facing labels for advanced search.
        subdivision: searchType === 'quick' ? 'Neighborhood / Subdivision' : 'Subdivision',
        mls_area: 'MLS Area',
    }
    return labelMap;
};

export function getGroupIcon(groupKey) {
    const groupLabelIconMap = {
        address: 'fa fa-map-marker',
        city: 'fa fa-building',
        township: 'fa fa-building',
        county: 'fa fa-map',
        zip: 'fa fa-map-marker',
        school: 'fa fa-graduation-cap',
        school_district: 'fa fa-graduation-cap',
        mls_id: 'fa fa-home',
        subdivision: 'fa fa-users',
        mls_area: 'fa fa-users',
    }
    return groupLabelIconMap[groupKey];
}

export const noOptionsMessage = () => "Oops, we didn't find anything. Try other terms.";
export const HiddenComponent = () => <></>;
export const GroupHeadingMaker = icons => props => {
    return <components.GroupHeading {...props}>
        <div>
            <i className={getGroupIcon(props.data.key)} />
        </div>
        <div className="label">{props.data.label}</div>
        {props.data.key === 'address'
            && <>
                <div className="legend-item"><img className="map-icon" src={icons.active} /> <span className="">Active</span></div>
                <div className="legend-item"><img className="map-icon" src={icons.closed} /> <span className="">Sold</span></div>
            </>
        }
    </components.GroupHeading>;
}
export const OptionMaker = (bucketsThatGoToPdp, mlsClass, icons) => props => {
    if (bucketsThatGoToPdp.includes(props.data.type)) {
        let listStatusDiv = null;
        if (props.data.type === 'address' && mlsClass !== 'rentals') {
            const listStatusEnum = props.data.value.listStatusEnum;
            if (listStatusEnum) {
                let src;
                if (listStatusEnum === '_active') {
                    src = icons.active;
                } else if (listStatusEnum === '_closed') {
                    src = icons.closed;
                }
                listStatusDiv = <img className="map-icon" src={src} />
            }
        }
        return <components.Option {...props}>
            {listStatusDiv} {props.data.label}
        </components.Option>;
    }
    return components.Option(props);
}

export const labelSubTypeMap = {
    elementary_school: 'Elementary School',
    // I'm using 'Jr. High' because that's the current Display Name in our field mappings.
    middle_school: 'Jr. High School',
    high_school: 'High School',
};
