[Home](Home)

[TOC]

# Automated WordPress Deployment Research

## WordPress Stacks

### [WP Stack](http://www.knewton.com/tech/blog/2012/07/announcing-wp-stack/)
>WP Stack is a set of deployment scripts using Capistrano, and a series of drop-in WordPress plugins that work in tandem with this system to perform commonly desired tasks on professional sites, such as rewriting media files to a CDN or serving WordPress multisite uploads directly through Nginx. It supports both production and staging environments, as well as file/database snapshotting from production to staging, so that you can test your code changes on fresh data.

>The commands for tasks like deployment, rollback, and database/file syncs, are short one-liners such as cap production deploy or cap staging db:sync. And while code changes (like templates or plugins) require commits and deployments, the regular WordPress workflow is unchanged… so content creators can post, upload media, and edit content to their heart’s content without needing to know what’s going on under the hood.

### [Bedrock WordPress Stack](http://roots.io/wordpress-stack/)
On [github](https://github.com/roots/bedrock)

This looks like an excellent deployment solution, but it doesn't appear to support multisite deployment, only deployment of a fresh WP install.  This might not be immediately useful for us out-of-the-box, but the basic principles may serve as valuable examples or as a starting point for something more attuned to our needs.


## Capistrano

### [Screencast: WordPress deployment with Capistrano](http://roots.io/screencasts/deploying-wordpress-with-capistrano/) ($12)
It appears that Capistrano can be used for simple, reversible, repeatable WP installs.  This is a very promising options for initial installation.  Most of the automated deployment solutions I'm finding are powered by Capistrano.


## Composer

### [Screencast: Using Composer with WordPress](http://roots.io/screencasts/using-composer-with-wordpress/) ($12)
Composer can be used to manage WordPress installs and plugins.  This screencast provides a basic introduction to Composer and then covers retrofitting existing projects, use with Git, etc.

### [Blog Entry: Using Composer with WordPress](http://roots.io/using-composer-with-wordpress/)
Extensive information on Capistrano basics and how it works with WordPress.  Very well-written and informative.  Raises an interesting point: WordPress itself is a third-party dependency.  This covers how to define Wordpress itself as well as its plugins and themes as Composer packages.  Also covers the basics of using [Wordpress Packagist](http://wpackagist.org/).  Again, this does **not** cover multisite deployment and management.

### [Wordpress Packagist](http://wpackagist.org/)
Mirrors WordPress plugins directory as Composer package files.

### [Moving WordPress code into its own subdirectory](http://codex.wordpress.org/Giving_WordPress_Its_Own_Directory)
Detailed instructions for moving WP code from an existing installation into its own subdirectory to help isolate third-party code.  This can be helpful for using Composer with WP.  *Note:* WordPress will only look for `wp-config.php` in its own directory or one level up.  Note also that while I haven't tried these steps, a comment lower on the page stated that the `wp-content/` directory will still be in `wp/` after completing these instructions.

>The instructions you link to on giving WordPress its own directory don't actually get you to the point that you say they do. After that, you also have to move 'wp-content' from the 'wp' folder to the base folder, and then set some constants in the wp-config telling WordPress where the wp-content folder, themes folder, and plugins folder are (WP_CONTENT_URL, WP_CONTENT_DIR, and $theme_root). If you don't do those additional steps, WordPress still looks for the wp-content folder inside 'wp'.

## Interesting Plugins

### [Batch Create](https://premium.wpmudev.org/project/batch-create/)
Allows automated creation of any number of users/sites by uploading a .xls or .csv file.  Plenty of potential for automated multisite deployment here.

### [WP Live Server Deploy](http://premium.wpmudev.org/blog/automate-wordpress-migration-with-wp-live-server-deploy/)
Automates the process of deploying WordPress sites from development to production.  Handles database deployment as well.

### [Multi-DB](https://premium.wpmudev.org/project/multi-db/)
Reconfigures WP to use many databases instead of loading hundreds or thousands of tables into the single database for a multisite installation.

### [New Blog Defaults](http://wordpress.org/plugins/wpmu-new-blog-defaults/)
Allows many settings to be defined for new blogs.

## Customizing installation

### [Custom install using `install.php`](http://wpbits.wordpress.com/2007/08/10/automating-wordpress-customizations-the-installphp-way/)
Provides instructions for customizing installation using the `install.php` file within WordPress itself.

### [Using plugins](http://wpbits.wordpress.com/2007/08/09/automating-wordpress-customizations-the-plugin-way/)
Information on setting WordPress options using a custom plugin.


## Programmatic approaches to multisite creation

### [Wordpress.org thread re: hijacking `wp-signup.php`](http://wordpress.org/support/topic/automate-creating-sites-in-multi-site-programmatically)
Suggests that the `wpmu_create_blog()` function can provide a programmatic means for this.  Suggests looking into the `validate_another_blog_signup()` function inside `wp-signup.php` for examples of how this might be done.  

See also [`wpmu_new_blog()`](http://adambrown.info/p/wp_hooks/hook/wpmu_new_blog?version=3.3&file=wp-includes/ms-functions.php), which can be hooked to provide functionality any time a new blog is created.

### [Programmatically cloning multisite blogs](http://buddydev.com/wordpress-multisite/cloning-blogs-on-wordpress-multisite-programmatically/)
Provides the php code for a cloning script!  Claims to preserve all active plugins, themes, widgets, etc.

### [WP-CLI Command Line tool](http://wp-cli.org/)
Command-line tool for WordPress that allows the creation of custom commands.  Could probably be used with the above to register a new blog and clone contents of an old blog.  Can also execute arbitrary PHP code either from a file (using `wp eval-file <filename>`) or from the command line (`wp eval <mycode>`).  This method is probably much easier than creating a custom command but might not provide the same ability to provide command-line switches, like to provide a blog ID, etc.

- [Command Cookbook](https://github.com/wp-cli/wp-cli/wiki/Commands-Cookbook) - how to make custom commands
- [TutsPlus tutorial](http://code.tutsplus.com/articles/using-wp-cli-for-fun-and-profit--wp-24496)
- [List of commands added by users](https://github.com/wp-cli/wp-cli/wiki/List-of-community-commands) - Note that BackupBuddy has associated commands for WP-CLI, though they aren't specified in that doc.


