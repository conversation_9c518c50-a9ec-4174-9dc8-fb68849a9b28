<?php

class world {

function get_states_assoc(){

	$states = array( 
				array( 'abr'=>'AL', 'name'=>'Alabama' ),
				array( 'abr'=>'AK', 'name'=>'Alaska' ),
				array( 'abr'=>'AZ', 'name'=>'Arizona' ),
				array( 'abr'=>'AR', 'name'=>'Arkansas' ),
				array( 'abr'=>'CA', 'name'=>'California' ),
				array( 'abr'=>'CO', 'name'=>'Colorado' ),
				array( 'abr'=>'CT', 'name'=>'Connecticut' ),
				array( 'abr'=>'DC', 'name'=>'District of Columbia' ),
				array( 'abr'=>'DE', 'name'=>'Delaware' ),
				array( 'abr'=>'FL', 'name'=>'Florida' ),
				array( 'abr'=>'GA', 'name'=>'Georgia' ),
				array( 'abr'=>'HI', 'name'=>'Hawaii' ),
				array( 'abr'=>'ID', 'name'=>'Idaho' ),
				array( 'abr'=>'IL', 'name'=>'Illinois' ),
				array( 'abr'=>'IN', 'name'=>'Indiana' ),
				array( 'abr'=>'IA', 'name'=>'Iowa' ),
				array( 'abr'=>'KS', 'name'=>'Kansas' ),
				array( 'abr'=>'KY', 'name'=>'Kentucky' ),
				array( 'abr'=>'LA', 'name'=>'Louisiana' ),
				array( 'abr'=>'ME', 'name'=>'Maine' ),
				array( 'abr'=>'MD', 'name'=>'Maryland' ),
				array( 'abr'=>'MA', 'name'=>'Massachusetts' ),
				array( 'abr'=>'MI', 'name'=>'Michigan' ),
				array( 'abr'=>'MN', 'name'=>'Minnesota' ),
				array( 'abr'=>'MS', 'name'=>'Mississippi' ),
				array( 'abr'=>'MO', 'name'=>'Missouri' ),
				array( 'abr'=>'MT', 'name'=>'Montana' ),
				array( 'abr'=>'NE', 'name'=>'Nebraska' ),
				array( 'abr'=>'NV', 'name'=>'Nevada' ),
				array( 'abr'=>'NH', 'name'=>'New Hampshire' ),
				array( 'abr'=>'NJ', 'name'=>'New Jersey' ),
				array( 'abr'=>'NM', 'name'=>'New Mexico' ),
				array( 'abr'=>'NY', 'name'=>'New York' ),
				array( 'abr'=>'NC', 'name'=>'North Carolina' ),
				array( 'abr'=>'ND', 'name'=>'North Dakota' ),
				array( 'abr'=>'OH', 'name'=>'Ohio' ),
				array( 'abr'=>'OK', 'name'=>'Oklahoma' ),
				array( 'abr'=>'OR', 'name'=>'Oregon' ),
				array( 'abr'=>'PA', 'name'=>'Pennsylvania' ),
				array( 'abr'=>'PR', 'name'=>'Puerto Rico' ),
				array( 'abr'=>'RI', 'name'=>'Rhode Island' ),
				array( 'abr'=>'SC', 'name'=>'South Carolina' ),
				array( 'abr'=>'SD', 'name'=>'South Dakota' ),
				array( 'abr'=>'TN', 'name'=>'Tennessee' ),
				array( 'abr'=>'TX', 'name'=>'Texas' ),
				array( 'abr'=>'UT', 'name'=>'Utah' ),
				array( 'abr'=>'VT', 'name'=>'Vermont' ),
				array( 'abr'=>'VA', 'name'=>'Virginia' ),
				array( 'abr'=>'WA', 'name'=>'Washington' ),
				array( 'abr'=>'WV', 'name'=>'West Virginia' ),
				array( 'abr'=>'WI', 'name'=>'Wisconsin' ),
				array( 'abr'=>'WY', 'name'=>'Wyoming' )
			);

	return $states;
	
}


function get_states(){

	$states = array( 
				array(	'AL', 'Alabama' ),
				array(	'AK', 'Alaska' ),
				array(	'AZ', 'Arizona' ),
				array(	'AR', 'Arkansas' ),
				array(	'CA', 'California' ),
				array(	'CO', 'Colorado' ),
				array(	'CT', 'Connecticut' ),
				array(	'DC', 'District of Columbia' ),
				array(	'DE', 'Delaware' ),
				array(	'FL', 'Florida' ),
				array(	'GA', 'Georgia' ),
				array(	'HI', 'Hawaii' ),
				array(	'ID', 'Idaho' ),
				array(	'IL', 'Illinois' ),
				array(	'IN', 'Indiana' ),
				array(	'IA', 'Iowa' ),
				array(	'KS', 'Kansas' ),
				array(	'KY', 'Kentucky' ),
				array(	'LA', 'Louisiana' ),
				array(	'ME', 'Maine' ),
				array(	'MD', 'Maryland' ),
				array(	'MA', 'Massachusetts' ),
				array(	'MI', 'Michigan' ),
				array(	'MN', 'Minnesota' ),
				array(	'MS', 'Mississippi' ),
				array(	'MO', 'Missouri' ),
				array(	'MT', 'Montana' ),
				array(	'NE', 'Nebraska' ),
				array(	'NV', 'Nevada' ),
				array(	'NH', 'New Hampshire' ),
				array(	'NJ', 'New Jersey' ),
				array(	'NM', 'New Mexico' ),
				array(	'NY', 'New York' ),
				array(	'NC', 'North Carolina' ),
				array(	'ND', 'North Dakota' ),
				array(	'OH', 'Ohio' ),
				array(	'OK', 'Oklahoma' ),
				array(	'OR', 'Oregon' ),
				array(	'PA', 'Pennsylvania' ),
				array(	'PR', 'Puerto Rico' ),
				array(	'RI', 'Rhode Island' ),
				array(	'SC', 'South Carolina' ),
				array(	'SD', 'South Dakota' ),
				array(	'TN', 'Tennessee' ),
				array(	'TX', 'Texas' ),
				array(	'UT', 'Utah' ),
				array(	'VT', 'Vermont' ),
				array(	'VA', 'Virginia' ),
				array(	'WA', 'Washington' ),
				array(	'WV', 'West Virginia' ),
				array(	'WI', 'Wisconsin' ),
				array(	'WY', 'Wyoming' )
			);

	return $states;
	
} // End States Associated ------------------------------------------


function get_countries_assoc(){

	$countries = array(
						array( "abr"=>"US", "name"=>"United States"),
						array( "abr"=>"AF", "name"=>"Afghanistan"),
						array( "abr"=>"AX", "name"=>"Aland Islands"),
						array( "abr"=>"AL", "name"=>"Albania"),
						array( "abr"=>"DZ", "name"=>"Algeria"),
						array( "abr"=>"AS", "name"=>"American Samoa"),
						array( "abr"=>"AD", "name"=>"Andorra"),
						array( "abr"=>"AO", "name"=>"Angola"),
						array( "abr"=>"AI", "name"=>"Anguilla"),
						array( "abr"=>"AQ", "name"=>"Antarctica"),
						array( "abr"=>"AG", "name"=>"Antigua And Barbuda"),
						array( "abr"=>"AR", "name"=>"Argentina"),
						array( "abr"=>"AM", "name"=>"Armenia"),
						array( "abr"=>"AW", "name"=>"Aruba"),
						array( "abr"=>"AU", "name"=>"Australia"),
						array( "abr"=>"AT", "name"=>"Austria"),
						array( "abr"=>"AZ", "name"=>"Azerbaijan"),
						array( "abr"=>"BS", "name"=>"Bahamas"),
						array( "abr"=>"BH", "name"=>"Bahrain"),
						array( "abr"=>"BD", "name"=>"Bangladesh"),
						array( "abr"=>"BB", "name"=>"Barbados"),
						array( "abr"=>"BY", "name"=>"Belarus"),
						array( "abr"=>"BE", "name"=>"Belgium"),
						array( "abr"=>"BZ", "name"=>"Belize"),
						array( "abr"=>"BJ", "name"=>"Benin"),
						array( "abr"=>"BM", "name"=>"Bermuda"),
						array( "abr"=>"BT", "name"=>"Bhutan"),
						array( "abr"=>"BO", "name"=>"Bolivia"),
						array( "abr"=>"BA", "name"=>"Bosnia And Herzegovina"),
						array( "abr"=>"BW", "name"=>"Botswana"),
						array( "abr"=>"BV", "name"=>"Bouvet Island"),
						array( "abr"=>"BR", "name"=>"Brazil"),
						array( "abr"=>"IO", "name"=>"British Indian Ocean Territory"),
						array( "abr"=>"BN", "name"=>"Brunei Darussalam"),
						array( "abr"=>"BG", "name"=>"Bulgaria"),
						array( "abr"=>"BF", "name"=>"Burkina Faso"),
						array( "abr"=>"BI", "name"=>"Burundi"),
						array( "abr"=>"KH", "name"=>"Cambodia"),
						array( "abr"=>"CM", "name"=>"Cameroon"),
						array( "abr"=>"CA", "name"=>"Canada"),
						array( "abr"=>"CV", "name"=>"Cape Verde"),
						array( "abr"=>"KY", "name"=>"Cayman Islands"),
						array( "abr"=>"CF", "name"=>"Central African Republic"),
						array( "abr"=>"TD", "name"=>"Chad"),
						array( "abr"=>"CL", "name"=>"Chile"),
						array( "abr"=>"CN", "name"=>"China"),
						array( "abr"=>"CX", "name"=>"Christmas Island"),
						array( "abr"=>"CC", "name"=>"Cocos (Keeling) Islands"),
						array( "abr"=>"CO", "name"=>"Colombia"),
						array( "abr"=>"KM", "name"=>"Comoros"),
						array( "abr"=>"CG", "name"=>"Congo"),
						array( "abr"=>"CD", "name"=>"The Democratic Republic Of The Congo"),
						array( "abr"=>"CK", "name"=>"Cook Islands"),
						array( "abr"=>"CR", "name"=>"Costa Rica"),
						array( "abr"=>"CI", "name"=>"Cote D'ivoire"),
						array( "abr"=>"HR", "name"=>"Croatia"),
						array( "abr"=>"CY", "name"=>"Cyprus"),
						array( "abr"=>"CZ", "name"=>"Czech Republic"),
						array( "abr"=>"CS", "name"=>"Czechoslovakia"),
						array( "abr"=>"DK", "name"=>"Denmark"),
						array( "abr"=>"DJ", "name"=>"Djibouti"),
						array( "abr"=>"DM", "name"=>"Dominica"),
						array( "abr"=>"DO", "name"=>"Dominican Republic"),
						array( "abr"=>"TL", "name"=>"Timor-Leste"),
						array( "abr"=>"EC", "name"=>"Ecuador"),
						array( "abr"=>"EG", "name"=>"Egypt"),
						array( "abr"=>"SV", "name"=>"El Salvador"),
						array( "abr"=>"GQ", "name"=>"Equatorial Guinea"),
						array( "abr"=>"ER", "name"=>"Eritrea"),
						array( "abr"=>"EE", "name"=>"Estonia"),
						array( "abr"=>"ET", "name"=>"Ethiopia"),
						array( "abr"=>"FK", "name"=>"Falkland Islands (Malvinas)"),
						array( "abr"=>"FO", "name"=>"Faroe Islands"),
						array( "abr"=>"FJ", "name"=>"Fiji"),
						array( "abr"=>"FI", "name"=>"Finland"),
						array( "abr"=>"FR", "name"=>"France"),
						array( "abr"=>"GF", "name"=>"French Guiana"),
						array( "abr"=>"PF", "name"=>"French Polynesia"),
						array( "abr"=>"TF", "name"=>"French Southern Territories"),
						array( "abr"=>"GA", "name"=>"Gabon"),
						array( "abr"=>"GM", "name"=>"Gambia"),
						array( "abr"=>"GE", "name"=>"Georgia"),
						array( "abr"=>"DE", "name"=>"Germany"),
						array( "abr"=>"GH", "name"=>"Ghana"),
						array( "abr"=>"GI", "name"=>"Gibraltar"),
						array( "abr"=>"GB", "name"=>"United Kingdom"),
						array( "abr"=>"GR", "name"=>"Greece"),
						array( "abr"=>"GL", "name"=>"Greenland"),
						array( "abr"=>"GD", "name"=>"Grenada"),
						array( "abr"=>"GP", "name"=>"Guadeloupe"),
						array( "abr"=>"GU", "name"=>"Guam"),
						array( "abr"=>"GT", "name"=>"Guatemala"),
						array( "abr"=>"GG", "name"=>"Guernsey"),
						array( "abr"=>"GN", "name"=>"Guinea"),
						array( "abr"=>"GW", "name"=>"Guinea-Bissau"),
						array( "abr"=>"GY", "name"=>"Guyana"),
						array( "abr"=>"HT", "name"=>"Haiti"),
						array( "abr"=>"HM", "name"=>"Heard Island And Mcdonald Islands"),
						array( "abr"=>"HN", "name"=>"Honduras"),
						array( "abr"=>"HK", "name"=>"Hong Kong"),
						array( "abr"=>"HU", "name"=>"Hungary"),
						array( "abr"=>"IS", "name"=>"Iceland"),
						array( "abr"=>"IN", "name"=>"India"),
						array( "abr"=>"IQ", "name"=>"Iraq"),
						array( "abr"=>"IE", "name"=>"Ireland"),
						array( "abr"=>"IM", "name"=>"Isle Of Man"),
						array( "abr"=>"IL", "name"=>"Israel"),
						array( "abr"=>"IT", "name"=>"Italy"),
						array( "abr"=>"JM", "name"=>"Jamaica"),
						array( "abr"=>"JP", "name"=>"Japan"),
						array( "abr"=>"JE", "name"=>"Jersey"),
						array( "abr"=>"JO", "name"=>"Jordan"),
						array( "abr"=>"KZ", "name"=>"Kazakhstan"),
						array( "abr"=>"KE", "name"=>"Kenya"),
						array( "abr"=>"KI", "name"=>"Kiribati"),
						array( "abr"=>"KR", "name"=>"Korea, Republic Of"),
						array( "abr"=>"KW", "name"=>"Kuwait"),
						array( "abr"=>"KG", "name"=>"Kyrgyzstan"),
						array( "abr"=>"LA", "name"=>"Lao People's Democratic Republic"),
						array( "abr"=>"LV", "name"=>"Latvia"),
						array( "abr"=>"LB", "name"=>"Lebanon"),
						array( "abr"=>"LS", "name"=>"Lesotho"),
						array( "abr"=>"LR", "name"=>"Liberia"),
						array( "abr"=>"LY", "name"=>"Libyan Arab Jamahiriya"),
						array( "abr"=>"LI", "name"=>"Liechtenstein"),
						array( "abr"=>"LT", "name"=>"Lithuania"),
						array( "abr"=>"LU", "name"=>"Luxembourg"),
						array( "abr"=>"MO", "name"=>"Macao"),
						array( "abr"=>"MK", "name"=>"Macedonia, The Former Yugoslav Republic Of"),
						array( "abr"=>"MG", "name"=>"Madagascar"),
						array( "abr"=>"MW", "name"=>"Malawi"),
						array( "abr"=>"MY", "name"=>"Malaysia"),
						array( "abr"=>"MV", "name"=>"Maldives"),
						array( "abr"=>"ML", "name"=>"Mali"),
						array( "abr"=>"MT", "name"=>"Malta"),
						array( "abr"=>"MH", "name"=>"Marshall Islands"),
						array( "abr"=>"MQ", "name"=>"Martinique"),
						array( "abr"=>"MR", "name"=>"Mauritania"),
						array( "abr"=>"MU", "name"=>"Mauritius"),
						array( "abr"=>"YT", "name"=>"Mayotte"),
						array( "abr"=>"MX", "name"=>"Mexico"),
						array( "abr"=>"FM", "name"=>"Micronesia, Federated States Of"),
						array( "abr"=>"MD", "name"=>"Moldova, Republic Of"),
						array( "abr"=>"MC", "name"=>"Monaco"),
						array( "abr"=>"MN", "name"=>"Mongolia"),
						array( "abr"=>"ME", "name"=>"Montenegro"),
						array( "abr"=>"MS", "name"=>"Montserrat"),
						array( "abr"=>"MA", "name"=>"Morocco"),
						array( "abr"=>"MZ", "name"=>"Mozambique"),
						array( "abr"=>"MM", "name"=>"Myanmar"),
						array( "abr"=>"NA", "name"=>"Namibia"),
						array( "abr"=>"NR", "name"=>"Nauru"),
						array( "abr"=>"NP", "name"=>"Nepal"),
						array( "abr"=>"NL", "name"=>"The Netherlands"),
						array( "abr"=>"AN", "name"=>"Netherlands Antilles"),
						array( "abr"=>"NT", "name"=>"Neutral Zone"),
						array( "abr"=>"NC", "name"=>"New Caledonia"),
						array( "abr"=>"NZ", "name"=>"New Zealand"),
						array( "abr"=>"NI", "name"=>"Nicaragua"),
						array( "abr"=>"NE", "name"=>"Niger"),
						array( "abr"=>"NG", "name"=>"Nigeria"),
						array( "abr"=>"NU", "name"=>"Niue"),
						array( "abr"=>"NF", "name"=>"Norfolk Island"),
						array( "abr"=>"MP", "name"=>"Northern Mariana Islands"),
						array( "abr"=>"NO", "name"=>"Norway"),
						array( "abr"=>"OM", "name"=>"Oman"),
						array( "abr"=>"PK", "name"=>"Pakistan"),
						array( "abr"=>"PW", "name"=>"Palau"),
						array( "abr"=>"PS", "name"=>"Palestinian Territory, Occupied"),
						array( "abr"=>"PA", "name"=>"Panama"),
						array( "abr"=>"PG", "name"=>"Papua New Guinea"),
						array( "abr"=>"PY", "name"=>"Paraguay"),
						array( "abr"=>"PE", "name"=>"Peru"),
						array( "abr"=>"PH", "name"=>"Philippines"),
						array( "abr"=>"PN", "name"=>"Pitcairn"),
						array( "abr"=>"PL", "name"=>"Poland"),
						array( "abr"=>"PT", "name"=>"Portugal"),
						array( "abr"=>"PR", "name"=>"Puerto Rico"),
						array( "abr"=>"QA", "name"=>"Qatar"),
						array( "abr"=>"RE", "name"=>"Reunion"),
						array( "abr"=>"RO", "name"=>"Romania"),
						array( "abr"=>"RU", "name"=>"Russian Federation"),
						array( "abr"=>"RW", "name"=>"Rwanda"),
						array( "abr"=>"KN", "name"=>"Saint Kitts And Nevis"),
						array( "abr"=>"LC", "name"=>"Saint Lucia"),
						array( "abr"=>"VC", "name"=>"Saint Vincent And The Grenadines"),
						array( "abr"=>"WS", "name"=>"Samoa"),
						array( "abr"=>"SM", "name"=>"San Marino"),
						array( "abr"=>"ST", "name"=>"Sao Tome And Principe"),
						array( "abr"=>"SA", "name"=>"Saudi Arabia"),
						array( "abr"=>"SN", "name"=>"Senegal"),
						array( "abr"=>"RS", "name"=>"Serbia"),
						array( "abr"=>"SC", "name"=>"Seychelles"),
						array( "abr"=>"SL", "name"=>"Sierra Leone"),
						array( "abr"=>"SG", "name"=>"Singapore"),
						array( "abr"=>"SK", "name"=>"Slovakia"),
						array( "abr"=>"SI", "name"=>"Slovenia"),
						array( "abr"=>"SB", "name"=>"Solomon Islands"),
						array( "abr"=>"SO", "name"=>"Somalia"),
						array( "abr"=>"ZA", "name"=>"South Africa"),
						array( "abr"=>"GS", "name"=>"South Georgia And The South Sandwich Islands"),
						array( "abr"=>"ES", "name"=>"Spain"),
						array( "abr"=>"LK", "name"=>"Sri Lanka"),
						array( "abr"=>"SH", "name"=>"Saint Helena"),
						array( "abr"=>"PM", "name"=>"Saint Pierre And Miquelon"),
						array( "abr"=>"SR", "name"=>"Suriname"),
						array( "abr"=>"SJ", "name"=>"Svalbard And Jan Mayen"),
						array( "abr"=>"SZ", "name"=>"Swaziland"),
						array( "abr"=>"SE", "name"=>"Sweden"),
						array( "abr"=>"CH", "name"=>"Switzerland"),
						array( "abr"=>"SY", "name"=>"Syrian Arab Republic"),
						array( "abr"=>"TW", "name"=>"Taiwan, Province Of China"),
						array( "abr"=>"TJ", "name"=>"Tajikistan"),
						array( "abr"=>"TZ", "name"=>"Tanzania, United Republic Of"),
						array( "abr"=>"TH", "name"=>"Thailand"),
						array( "abr"=>"TG", "name"=>"Togo"),
						array( "abr"=>"TK", "name"=>"Tokelau"),
						array( "abr"=>"TO", "name"=>"Tonga"),
						array( "abr"=>"TT", "name"=>"Trinidad And Tobago"),
						array( "abr"=>"TN", "name"=>"Tunisia"),
						array( "abr"=>"TR", "name"=>"Turkey"),
						array( "abr"=>"TM", "name"=>"Turkmenistan"),
						array( "abr"=>"TC", "name"=>"Turks And Caicos Islands"),
						array( "abr"=>"TV", "name"=>"Tuvalu"),
						array( "abr"=>"UG", "name"=>"Uganda"),
						array( "abr"=>"UA", "name"=>"Ukraine"),
						array( "abr"=>"AE", "name"=>"United Arab Emirates"),
						array( "abr"=>"UK", "name"=>"United Kingdom"),
						array( "abr"=>"UM", "name"=>"United States Minor Outlying Islands"),
						array( "abr"=>"UY", "name"=>"Uruguay"),
						array( "abr"=>"SU", "name"=>"Ussr"),
						array( "abr"=>"UZ", "name"=>"Uzbekistan"),
						array( "abr"=>"VU", "name"=>"Vanuatu"),
						array( "abr"=>"VA", "name"=>"Holy See (Vatican City State)"),
						array( "abr"=>"VE", "name"=>"Venezuela"),
						array( "abr"=>"VN", "name"=>"Viet Nam"),
						array( "abr"=>"VG", "name"=>"Virgin Islands, British"),
						array( "abr"=>"VI", "name"=>"Virgin Islands, U.S."),
						array( "abr"=>"WF", "name"=>"Wallis And Futuna"),
						array( "abr"=>"EH", "name"=>"Western Sahara"),
						array( "abr"=>"YE", "name"=>"Yemen"),
						array( "abr"=>"YU", "name"=>"Yugoslavia"),
						array( "abr"=>"ZR", "name"=>"Zaire"),
						array( "abr"=>"ZM", "name"=>"Zambia"),
						array( "abr"=>"ZW", "name"=>"Zimbabwe")
		);

	return $countries;
	
} // End Countries Associated ------------------------------------------


function get_countries(){

	$countries = array(
						array( "US", "United States"),
						array( "AF", "Afghanistan"),
						array( "AX", "Aland Islands"),
						array( "AL", "Albania"),
						array( "DZ", "Algeria"),
						array( "AS", "American Samoa"),
						array( "AD", "Andorra"),
						array( "AO", "Angola"),
						array( "AI", "Anguilla"),
						array( "AQ", "Antarctica"),
						array( "AG", "Antigua And Barbuda"),
						array( "AR", "Argentina"),
						array( "AM", "Armenia"),
						array( "AW", "Aruba"),
						array( "AU", "Australia"),
						array( "AT", "Austria"),
						array( "AZ", "Azerbaijan"),
						array( "BS", "Bahamas"),
						array( "BH", "Bahrain"),
						array( "BD", "Bangladesh"),
						array( "BB", "Barbados"),
						array( "BY", "Belarus"),
						array( "BE", "Belgium"),
						array( "BZ", "Belize"),
						array( "BJ", "Benin"),
						array( "BM", "Bermuda"),
						array( "BT", "Bhutan"),
						array( "BO", "Bolivia"),
						array( "BA", "Bosnia And Herzegovina"),
						array( "BW", "Botswana"),
						array( "BV", "Bouvet Island"),
						array( "BR", "Brazil"),
						array( "IO", "British Indian Ocean Territory"),
						array( "BN", "Brunei Darussalam"),
						array( "BG", "Bulgaria"),
						array( "BF", "Burkina Faso"),
						array( "BI", "Burundi"),
						array( "KH", "Cambodia"),
						array( "CM", "Cameroon"),
						array( "CA", "Canada"),
						array( "CV", "Cape Verde"),
						array( "KY", "Cayman Islands"),
						array( "CF", "Central African Republic"),
						array( "TD", "Chad"),
						array( "CL", "Chile"),
						array( "CN", "China"),
						array( "CX", "Christmas Island"),
						array( "CC", "Cocos (Keeling) Islands"),
						array( "CO", "Colombia"),
						array( "KM", "Comoros"),
						array( "CG", "Congo"),
						array( "CD", "The Democratic Republic Of The Congo"),
						array( "CK", "Cook Islands"),
						array( "CR", "Costa Rica"),
						array( "CI", "Cote D'ivoire"),
						array( "HR", "Croatia"),
						array( "CY", "Cyprus"),
						array( "CZ", "Czech Republic"),
						array( "CS", "Czechoslovakia"),
						array( "DK", "Denmark"),
						array( "DJ", "Djibouti"),
						array( "DM", "Dominica"),
						array( "DO", "Dominican Republic"),
						array( "TL", "Timor-Leste"),
						array( "EC", "Ecuador"),
						array( "EG", "Egypt"),
						array( "SV", "El Salvador"),
						array( "GQ", "Equatorial Guinea"),
						array( "ER", "Eritrea"),
						array( "EE", "Estonia"),
						array( "ET", "Ethiopia"),
						array( "FK", "Falkland Islands (Malvinas)"),
						array( "FO", "Faroe Islands"),
						array( "FJ", "Fiji"),
						array( "FI", "Finland"),
						array( "FR", "France"),
						array( "GF", "French Guiana"),
						array( "PF", "French Polynesia"),
						array( "TF", "French Southern Territories"),
						array( "GA", "Gabon"),
						array( "GM", "Gambia"),
						array( "GE", "Georgia"),
						array( "DE", "Germany"),
						array( "GH", "Ghana"),
						array( "GI", "Gibraltar"),
						array( "GB", "United Kingdom"),
						array( "GR", "Greece"),
						array( "GL", "Greenland"),
						array( "GD", "Grenada"),
						array( "GP", "Guadeloupe"),
						array( "GU", "Guam"),
						array( "GT", "Guatemala"),
						array( "GG", "Guernsey"),
						array( "GN", "Guinea"),
						array( "GW", "Guinea-Bissau"),
						array( "GY", "Guyana"),
						array( "HT", "Haiti"),
						array( "HM", "Heard Island And Mcdonald Islands"),
						array( "HN", "Honduras"),
						array( "HK", "Hong Kong"),
						array( "HU", "Hungary"),
						array( "IS", "Iceland"),
						array( "IN", "India"),
						array( "IQ", "Iraq"),
						array( "IE", "Ireland"),
						array( "IM", "Isle Of Man"),
						array( "IL", "Israel"),
						array( "IT", "Italy"),
						array( "JM", "Jamaica"),
						array( "JP", "Japan"),
						array( "JE", "Jersey"),
						array( "JO", "Jordan"),
						array( "KZ", "Kazakhstan"),
						array( "KE", "Kenya"),
						array( "KI", "Kiribati"),
						array( "KR", "Korea, Republic Of"),
						array( "KW", "Kuwait"),
						array( "KG", "Kyrgyzstan"),
						array( "LA", "Lao People's Democratic Republic"),
						array( "LV", "Latvia"),
						array( "LB", "Lebanon"),
						array( "LS", "Lesotho"),
						array( "LR", "Liberia"),
						array( "LY", "Libyan Arab Jamahiriya"),
						array( "LI", "Liechtenstein"),
						array( "LT", "Lithuania"),
						array( "LU", "Luxembourg"),
						array( "MO", "Macao"),
						array( "MK", "Macedonia, The Former Yugoslav Republic Of"),
						array( "MG", "Madagascar"),
						array( "MW", "Malawi"),
						array( "MY", "Malaysia"),
						array( "MV", "Maldives"),
						array( "ML", "Mali"),
						array( "MT", "Malta"),
						array( "MH", "Marshall Islands"),
						array( "MQ", "Martinique"),
						array( "MR", "Mauritania"),
						array( "MU", "Mauritius"),
						array( "YT", "Mayotte"),
						array( "MX", "Mexico"),
						array( "FM", "Micronesia, Federated States Of"),
						array( "MD", "Moldova, Republic Of"),
						array( "MC", "Monaco"),
						array( "MN", "Mongolia"),
						array( "ME", "Montenegro"),
						array( "MS", "Montserrat"),
						array( "MA", "Morocco"),
						array( "MZ", "Mozambique"),
						array( "MM", "Myanmar"),
						array( "NA", "Namibia"),
						array( "NR", "Nauru"),
						array( "NP", "Nepal"),
						array( "NL", "The Netherlands"),
						array( "AN", "Netherlands Antilles"),
						array( "NT", "Neutral Zone"),
						array( "NC", "New Caledonia"),
						array( "NZ", "New Zealand"),
						array( "NI", "Nicaragua"),
						array( "NE", "Niger"),
						array( "NG", "Nigeria"),
						array( "NU", "Niue"),
						array( "NF", "Norfolk Island"),
						array( "MP", "Northern Mariana Islands"),
						array( "NO", "Norway"),
						array( "OM", "Oman"),
						array( "PK", "Pakistan"),
						array( "PW", "Palau"),
						array( "PS", "Palestinian Territory, Occupied"),
						array( "PA", "Panama"),
						array( "PG", "Papua New Guinea"),
						array( "PY", "Paraguay"),
						array( "PE", "Peru"),
						array( "PH", "Philippines"),
						array( "PN", "Pitcairn"),
						array( "PL", "Poland"),
						array( "PT", "Portugal"),
						array( "PR", "Puerto Rico"),
						array( "QA", "Qatar"),
						array( "RE", "Reunion"),
						array( "RO", "Romania"),
						array( "RU", "Russian Federation"),
						array( "RW", "Rwanda"),
						array( "KN", "Saint Kitts And Nevis"),
						array( "LC", "Saint Lucia"),
						array( "VC", "Saint Vincent And The Grenadines"),
						array( "WS", "Samoa"),
						array( "SM", "San Marino"),
						array( "ST", "Sao Tome And Principe"),
						array( "SA", "Saudi Arabia"),
						array( "SN", "Senegal"),
						array( "RS", "Serbia"),
						array( "SC", "Seychelles"),
						array( "SL", "Sierra Leone"),
						array( "SG", "Singapore"),
						array( "SK", "Slovakia"),
						array( "SI", "Slovenia"),
						array( "SB", "Solomon Islands"),
						array( "SO", "Somalia"),
						array( "ZA", "South Africa"),
						array( "GS", "South Georgia And The South Sandwich Islands"),
						array( "ES", "Spain"),
						array( "LK", "Sri Lanka"),
						array( "SH", "Saint Helena"),
						array( "PM", "Saint Pierre And Miquelon"),
						array( "SR", "Suriname"),
						array( "SJ", "Svalbard And Jan Mayen"),
						array( "SZ", "Swaziland"),
						array( "SE", "Sweden"),
						array( "CH", "Switzerland"),
						array( "SY", "Syrian Arab Republic"),
						array( "TW", "Taiwan, Province Of China"),
						array( "TJ", "Tajikistan"),
						array( "TZ", "Tanzania, United Republic Of"),
						array( "TH", "Thailand"),
						array( "TG", "Togo"),
						array( "TK", "Tokelau"),
						array( "TO", "Tonga"),
						array( "TT", "Trinidad And Tobago"),
						array( "TN", "Tunisia"),
						array( "TR", "Turkey"),
						array( "TM", "Turkmenistan"),
						array( "TC", "Turks And Caicos Islands"),
						array( "TV", "Tuvalu"),
						array( "UG", "Uganda"),
						array( "UA", "Ukraine"),
						array( "AE", "United Arab Emirates"),
						array( "UK", "United Kingdom"),
						array( "UM", "United States Minor Outlying Islands"),
						array( "UY", "Uruguay"),
						array( "SU", "Ussr"),
						array( "UZ", "Uzbekistan"),
						array( "VU", "Vanuatu"),
						array( "VA", "Holy See (Vatican City State)"),
						array( "VE", "Venezuela"),
						array( "VN", "Viet Nam"),
						array( "VG", "Virgin Islands, British"),
						array( "VI", "Virgin Islands, U.S."),
						array( "WF", "Wallis And Futuna"),
						array( "EH", "Western Sahara"),
						array( "YE", "Yemen"),
						array( "YU", "Yugoslavia"),
						array( "ZR", "Zaire"),
						array( "ZM", "Zambia"),
						array( "ZW", "Zimbabwe")
		);

	return $countries;
	
} // End Countries ------------------------------------------


} //end class
?>