<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../traits/NewHooklessTrait.php');

/**
 * iFoundIdx Class
 *
 * @since 1.0.0
 */

class iFoundIdx extends iFound {
	use UtilTrait;
	use NewHooklessTrait;

	/**
	 * Google Maps API Key.
	 *
	 * @since 1.0.0
	 * @access protected
	 * @var string $google_key
	 */

	/**
	 * URL Query Vars
	 *
	 * @since 1.0.0
	 * @since 1.1.6 Added $polygon_search
	 * @since 2.5.39 Add subdivisions.
	 *
	 * @var string $search 			This is used in the search url as the query var.
	 * @var string $detail 			This is used in the PDP url as the query var.
	 * @var string $advanced 		This is used in the advanced search url as the query var.
	 * @var string $save_this 		This is used in the save this url as the query var.
	 * @var string $client_profile 	This is used in the client profile url as the query var.
	 * @var string $unsubscribe 	This is used in the unsubscribe url as the query var.
	 * @var string $polygon_search 	This is used in the polygon search url as the query var.
	 * @var string $subdivisions 	This is used in the polygon search for subdivisions url as the query var.
	 * @var string $cmc_report 	    This is used in the cmc page url as the query var.
	 * @var string $search_nearby 	This is used in the search nearby url as the query var.
	 */

	protected $search 			= 'listing-search';
	public $detail				= 'listing-details';
	protected $advanced 		= 'advanced-search';
	protected $save_this 		= 'save-this';
	protected $client_profile	= 'client-profile';
	protected $unsubscribe 		= 'unsubscribe';
	protected $polygon_search	= 'map-search';
	protected $subdivisions		= 'subdivisions-search';
	protected $cmc_report		= 'current-market-comparison';
	protected $search_nearby	= 'search-nearby';

	protected $params;

	static $stats_version = '0.1.0';

	/**
	 * Mappings.
	 *
	 * @since 5.0.0
	 * @var object $mappings The mapping for the easy_names.
	 */

	public $mappings;

	/**
	 * init iFoundIdx class.
	 *
	 * @since 1.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		$this->mappings = $this->mappings();

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('init', array($this, 'mls_associations'), 1);
			add_action('shutdown', array($this, 'idx_urls'));
			add_action('ifound_mls_associations', array($this, 'mls_associations'));

			add_action('ifound_no_data', array($this, 'no_data'), 10, 1);
			add_action('wp_enqueue_scripts', array($this, 'scripts'), 4);
			add_action('admin_enqueue_scripts', array($this, 'scripts'), 4);

			add_action('ifound_required_footer', array($this, 'broker_disclosure'), 1);
			add_action('ifound_required_footer', array($this, 'mls_disclosure'), 2);

			add_filter('ifound_filter_merge_tags', array($this, 'filter_merge_tags'), 10, 2);
			add_filter('ifound_check_image', array($this, 'check_image'), 10, 2);
			add_filter('ifound_check_status', array($this, 'check_status'), 10, 2);
			add_filter('body_class', array($this, 'body_class'));
			add_filter('ifound_classes', array($this, 'classes'));
			add_filter('ifound_lookups', array($this, 'lookups'));
			add_filter('ifound_lookups_to_criteria', array($this, 'lookups_to_criteria'));
			add_filter('ifound_video_code', array($this, 'video_code'), 5);

			add_action('init', array($this, 'refresh_transients'));
			add_action('admin_init', array($this, 'refresh_transients'));
			add_action('ifound_refresh_transients', array($this, 'refresh_transients'), 10, 1);

			add_filter('ifound_mls_name', array($this, 'mls_name'), 10, 0);

			add_filter('ifound_current_user_can_create_posts', [$this, 'current_user_can_create_posts']);

			// Initially this is called by the Blogdog client plugin.
			add_filter('ifound_filter_prop_types_for_agent', [$this, 'filter_prop_types_for_agent']);

			add_action('wp_ajax_pdp_compensation', array($this, 'ajax_pdp_compensation'));
			add_action('wp_ajax_nopriv_pdp_compensation', array($this, 'ajax_pdp_compensation'));
		}
	}

	/**
	 * Scripts
	 *
	 * For Font Awesome fonts and installation.
	 * @link https://fontawesome.com/icons?d=gallery
	 *
	 * @since 1.0.0
	 * @since 1.0.29 Added  Fontawesome 5.0.0 beta
	 * @since 1.0.36 Remove ifound-old.css
	 * @since 1.7.2  Update Fontawesome 5.0.0 stable
	 * @since 2.4.1  Update Fontawesome 5.0.1
	 * @since 2.5.62 Add clickEvent for click on desktop or touchstart on mobile to dynamic_js
	 * @since 3.6.5  Add paging.js
	 */

	public function scripts(){

		/**
		 * iFound Styles.
		 *
		 * @since 1.0.0
		 */


		if( ! is_admin() ) {

			wp_enqueue_style( 'ifound_styles', plugins_url( 'includes/css/ifound.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		}

		wp_enqueue_style( 'custom_jquery_ui', plugins_url( 'includes/css/custom-jquery-ui.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		wp_enqueue_style( 'font_awesome_5_0_1', plugins_url( 'fontawesome/css/fontawesome-all.min.css', __FILE__ ), array(), '5.0.1' );

		wp_register_style( 'search_results_css', plugins_url( 'includes/css/search-results.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'search_bar_css', plugins_url( 'includes/css/search-bar.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'advanced_search_css', plugins_url( 'includes/css/advanced-search.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'featured_listings_css', plugins_url( 'includes/css/featured-listings.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'property_details_css', plugins_url( 'includes/css/property-details.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		wp_register_style( 'social_media_css', plugins_url( 'includes/css/social-media.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );

		/**
		 * jQuery.
		 *
		 * @since 1.0.0
		 */

		wp_enqueue_script( 'jquery' );
		wp_enqueue_script( 'jquery-ui-core' );

		wp_register_script( 'cookie_js', plugins_url( 'includes/js/cookie.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );

		wp_register_script( 'paging_js', plugins_url( 'includes/js/paging.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'paging_js', 'ifound_paging', array(
			'endpoint' 		=> '/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/search/'  . wp_create_nonce( 'search_secure_me' ) . '/',
			'site_url'		=> site_url(),
			'has_map'		=> false
		));

		wp_register_script( 'dynamic_js', plugins_url( 'includes/js/dynamic.js', __FILE__ ), array( 'jquery', 'jquery-ui-core', 'jquery-ui-datepicker' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'dynamic_js', 'dynamic', array(
			'endpoint'           => site_url('/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/criteria/' . wp_create_nonce('dynamic_secure_me') . '/'),
			// We used to change the clickEvent to 'touchstart' if the device was mobile. But that means the event will
			// still fire when the item is touched but the user is intending to scroll. Just 'click' works great.
			'clickEvent'         => 'click',
			'mls_name'           => apply_filters('ifound_mls_name', null),
			'isMultipleStateMls' => $this->is_multiple_state_mls(),
		));

		// We are using webpack to build JS/CSS bundles.
		// We have two apps, one for the admin context, and one for non-admin. We are using the SplitChunks plugin so
		// that the node_modules / vendor files are split out. This means that when we make a change to our JS but when
		// the vendor JS doesn't change (most of the time), the users don't need to re-download that code.
		// Also, much of our code is shared between the admin and non-admin apps, so our users that are admins won't
		// need to download the code twice.
		// To accomlish this, we have a wp_register_script for each chunk, and a final meta one where the src is false
		// but had dependencies on all the chunks.
		// I'm using this meta one so that we only have to use wp_enqueue_script once (instead of several times all over
		// our plugin).
		if (is_admin()) {
			// By default, wp-scripts builds by excluding React and ReactDOM. This is great when the block editor is in
			// use because the Wordpress JS payload already includes those. But if the block editor is not in use, then we
			// need to supply them. So we build two different versions.
			$dir = 'build-with-react';
			if (method_exists(get_current_screen(), 'is_block_editor') && get_current_screen()->is_block_editor()) {
				$dir = 'build-without-react';
			}
			wp_register_script('ifound_spa_js_1', plugins_url('includes/scripts/' . $dir . '/admin.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_register_script('ifound_spa_js_2', plugins_url('includes/scripts/' . $dir . '/vendors~admin.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_register_script('ifound_spa_js_3', plugins_url('includes/scripts/' . $dir . '/vendors~admin~non-admin.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_register_script('ifound_spa_js', false, [
				'ifound_shared_js',
				'ifound_spa_js_1',
				'ifound_spa_js_2',
				'ifound_spa_js_3',
			], iFOUND_PLUGIN_VERSION);

			wp_enqueue_style('ifound_spa_styles_1', plugins_url('includes/scripts/' . $dir . '/admin.css', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_enqueue_style('ifound_spa_styles_2', plugins_url('includes/scripts/' . $dir . '/vendors~admin~non-admin.css', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_enqueue_style('ifound_spa_styles', false, [
				'ifound_spa_styles_1'
			], iFOUND_PLUGIN_VERSION);
		} else {
			$dir = 'build-with-react';
			// Doing this in a non-admin context throws an error: Call to undefined function get_current_screen()
			// if (method_exists(get_current_screen(), 'is_block_editor') && get_current_screen()->is_block_editor()) {
			// 	$dir = 'build-without-react';
			// }
			wp_register_script('ifound_spa_js_1', plugins_url('includes/scripts/' . $dir . '/non-admin.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_register_script('ifound_spa_js_2', plugins_url('includes/scripts/' . $dir . '/vendors~admin~non-admin.js', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_register_script('ifound_spa_js', false, [
				'ifound_shared_js',
				'ifound_spa_js_1',
				'ifound_spa_js_2'
			], iFOUND_PLUGIN_VERSION);

			wp_enqueue_style('ifound_spa_styles_1', plugins_url('includes/scripts/' . $dir . '/non-admin.css', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_enqueue_style('ifound_spa_styles_2', plugins_url('includes/scripts/' . $dir . '/vendors~admin~non-admin.css', __FILE__), [], iFOUND_PLUGIN_VERSION);
			wp_enqueue_style('ifound_spa_styles', false, [
				'ifound_spa_styles_1'
			], iFOUND_PLUGIN_VERSION);
		}


		wp_register_script( 'budget_search_js', plugins_url( 'includes/js/budget.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_register_script( 'draw_map_js', plugins_url( 'includes/js/drawMap.js', __FILE__), array ( 'jquery' ), iFOUND_PLUGIN_VERSION );

		wp_register_script( 'whats_my_payment_js', plugins_url( 'includes/js/whats-my-payment.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'whats_my_payment_js', 'whats_my_payment', array(
			'search_url' 	=> site_url( '/' . $this->search . '/' )
		));

		wp_enqueue_script( 'login_js', plugins_url( 'includes/js/login.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'login_js', 'ifound_login', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'login_secure_me' )
		));

		wp_register_script( 'search_nearby_js', plugins_url( 'includes/js/search-nearby.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'search_nearby_js', 'ifound_search_nearby', array(
			'endpoint' 	=> site_url( '/' . $this->search_nearby . '/' )
		));

		wp_register_script( 'quick_search_js', plugins_url( 'includes/js/quick-search.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'quick_search_js', 'quick_search', array(
			'url' => site_url( '/' . $this->search . '/' ),
			'isMultipleStateMls' => $this->is_multiple_state_mls(),
		));
		wp_register_script( 'quick_search_page_js', plugins_url( 'includes/js/quick-search-page.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );

		/**
		 * Slick.
		 *
		 * @since 1.0.0
		 */

		wp_register_style('slick_css', plugins_url('includes/slick/slick.css', __FILE__));
		wp_register_script('slick_helper_js', plugins_url('includes/js/slick-helper.js', __FILE__), ['jquery', 'jquery-migrate']);
		wp_register_script('slick_js', plugins_url('includes/slick/slick.min.js', __FILE__), ['slick_helper_js']);

		wp_enqueue_script('ifound_spa_js');

		wp_register_script( 'pdp_js', plugins_url( 'includes/js/pdp.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'pdp_js', 'ifound_pdp', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
		));
	}

	/**
	 * Process input
	 *
	 * Let's process the input and get out MLS data now.
	 *
	 * @since 1.0.0
	 * @since 3.6.7  Remove esxtened_data and display params.
	 * @since 3.6.8  Remove iFoundIdx::get_words()
	 *
	 * @param  object $input The search criteria.
	 * @return object $results The MLS data for our search.
	 */

	public function process_input( &$input, $options = [] ) {
		if (empty($input)) {
			if (gettype($input) === 'array') {
				$input = [];
			} else {
				$input = new stdClass;
			}
		}

		// We are hacking the input and forcing it to be global by doing this. But it's already been assumed that it's
		// global (as in, there's only one result set on the page) in many places. The good news is we can centralize
		// this code here instead of at all the call sites.
		if(isset($_GET['results_page_num'])) {
			if (gettype($input) === 'object') {
				$input->pp = intval($_GET['results_page_num']);
			} else if (gettype($input) === 'array') {
				$input['pp'] = intval($_GET['results_page_num']);
			}
		}

		$this->params = $this->get_params( $input, $options );

		$results = $this->request();

		return $results;

	}

	// prop_type_mapped is a virtual field. Map it back to prop_type values.
	private function map_prop_type_mapped_to_prop_type($prop_type_mapped_values) {
		$choices = $this->get_prop_type_mapped();
		$filtered_choices = $this->get_agent_filtered_prop_type_mapped_keys($choices);
		$prop_types = [];
		$criteria = $this->lookups_to_criteria()->prop_type->values;
		foreach ($prop_type_mapped_values as $choice_key) {
			// $choice = $this->util()->array_find($choices, function($v) use ($choice_label) {
			// 	return $v['label'] === $choice_label;
			// });
			$choice = $choices[$choice_key];
			if ($choice) {
				foreach ($choice['prop_sub_types'] as $prop_sub_type) {
					// Only include values the agent allows.
					if (in_array($prop_sub_type, $criteria, true)) {
						$prop_types[] = $prop_sub_type;
					}
				}
			}
		}
		$unique = array_unique($prop_types);
		return $unique;
	}

	/**
	 * Get Params
	 *
	 * Get all the params
	 *
	 * @since 1.0.0
	 * @since 3.6.4 Use iFoundIdx::mls_associations_value() to retrieve searchable value.
	 * @since 3.6.7 Move hide_contingent param.
	 * @since 3.6.8 Add conditions for text, nomber, date.
	 *
	 * @param  object $input  The search criteria.
	 * @return string $params Our input params.
	 */

	protected function get_params( $input, $options = []) {
		if( empty( $input ) ) return false;

		$params = array();

		$param_type = $this->param_type();

		/** Lets loop through $input and see if we find other params.*/
		foreach( $input as $key => $value ) {

			if( ! empty( $value ) ) {
				if ($key === 'prop_type_mapped') {
					$params['prop_type'] = $this->map_prop_type_mapped_to_prop_type($value);
				} else if( in_array($key, ['budget', 'nearby'], true) ) {

					$params[$key] = $value;

				} else if( is_object( $value ) || is_array( $value ) ) {

					$value = json_decode( json_encode( $value ), true );

					if ( isset( $value['min'] ) || isset( $value['max'] ) ) {

						if ( isset( $value['min'] ) ) {
							$params[$key]['min'] = $value['min'];
						}

						if ( isset( $value['max'] ) ) {
							$params[$key]['max'] = $value['max'];
						}

					} else {

						foreach( $value as $val ) {

							$params[$key][] = $this->mls_associations_value( $key, $val );

						}

					}

				} else {

					if( in_array( $key, $param_type['text'] ) ) {

						$params[$key][] = $this->mls_associations_value( $key, $value );

					} else {

						$remove = array( '_min', '_max' );
						$easy_name = str_replace( $remove, '', $key );

						if( in_array( $easy_name, $param_type['number'] ) || in_array( $easy_name, $param_type['date'] ) ) {

							$mm = ( strpos( $key, '_max' ) !== false ) ? 'max' : 'min';

							$params[$easy_name][$mm] = $value;

						} else {

							// This is for our other params
							$params[$key] = $this->mls_associations_value( $key, $value );

						}

					}

				}

			}

		}

		$params['stats'] = $this->get_stats_params( $params, $options['stats'] );
		$params['trends'] = $options['trends'];
		if (isset($options['email_content_options'])) {
			$params['email_content_options'] = $options['email_content_options'];
		}
		if (isset($options['trace'])) {
			$params['trace'] = $options['trace'];
		}

		return empty( $params ) ? false : $params;

	}

	private function get_stats_params( $params, $options = [] ) {
		$options = wp_parse_args($options, [
			'show' => 'default',
			// Don't bother setting the default fields to [] because http_build_query will strip it.
		]);
		if ($options['show'] === 'default') {
			// We set these here (as opposed to on the IDX server) because we use these values as part of search results
			// to know which stats to show as defaults.
			$options['fields'] = $this->get_default_stat_fields();
		}

		$stats_params = wp_parse_args($options, [
			'version' => static::$stats_version,
		]);

		return $stats_params;

	}

	protected function get_default_stat_fields() {
		$default = [
			'list_price' => [
				'median' => true,
			],
			'close_price' => [
				'median' => true,
			],
		];
		// RESO Web API MLSs do not have this field. We might compute it in the future, but for now, hide it.
		// See: https://ifoundagent.teamwork.com/#/tasks/21524250
		// Update: ARMLS (Spark) has it, because they are still including it as a custom legacy field.
		if (!$this->is_reso_web_api() || in_array(static::mls_name(), ['armls_spark', 'naar_spark'])) {
		    $default['price_sqft'] = [
				'median' => true,
			];
		}
		// We add this field here because the order is important. As in, if we use price_sqft, we want it grouped with
		// the price stats and before the days on market.
		$default['computed_days_on_market'] = [
			'mean' => true,
		];
		return $default;
	}

	/**
	 * Hide Contingent
	 *
	 * Hide contingent listings in search or featured listing queries.
	 *
	 * @since 2.6.0
	 *
	 * @return bool $hide True to hide contingent. False to show contingent.
	 */

	public function hide_contingent() {

		$key = defined( 'DOING_FEATURED_LISTINGS' ) ? 'ifound_featured_settings' : 'ifound_search_settings';

		$settings = get_option( $key );

		$hide_contingent = is_array( $settings['hide_contingent'] ) ? $settings['hide_contingent'] : array();

		return in_array( 'checked', $hide_contingent ) ? true : false;

	}

	/**
	 * Param Type
	 *
	 * This is temporary until we update min,max and mls assoviations.
	 *
	 * @since 3.6.8
	 *
	 * @return array $params An array of params.
	 */

	public function param_type() {

		$mappings = $this->field_mappings();

		$params = array();

		$types = array(
			'text',
			'number',
			'date',
			'boolean',
		);

		foreach ( $types as $type ) {

			foreach ( $mappings as $mapping ) {

				if( $mapping->type == $type ) {

					$params[$type][] = $mapping->easy_name;

				}

			}

		}

		return empty( $params ) ? array() : $params;

	}

	public function multibox_request($q, $mls_class_code) {
		$endpoint = '';
		$endpoint .= $this->base_url( true ) . '/multibox/search';
		$endpoint = add_query_arg('apikey', $this->api_secret_urlencoded(), $endpoint);
		if( $this->hide_contingent() ) {
			$endpoint = add_query_arg('hide_contingent', true, $endpoint);
		}
		$endpoint = add_query_arg('q', $q, $endpoint);
		$endpoint = add_query_arg('mls_class', $mls_class_code, $endpoint);
		$headers = $this->headers();
		$response = wp_remote_get($endpoint, $headers);
		$results = json_decode($response['body'], true);
		return $results;
	}

	/**
	 * Request
	 *
	 * Make API request and return response.
	 *
	 * @since 1.0.0
	 * @since 1.8.0 return new stdClass() if no listings.
	 * @since 3.6.7 Remove extened_data param.
	 * @since 3.6.7 Move hide_contingent param.
	 *
	 * @return object $results The result of the API request.
	 */

	protected function request() {

		if( $this->api_secret_urlencoded() ) {

			global $idxurls;

			$endpoint = '';

			/** Address to Results Endpoint */
			$endpoint .= $this->base_url( true ) . '/i/search/?';

			if( $this->hide_contingent() ) {

				$endpoint .= '&hide_contingent=true';

			}

			/** Set Params */
			if( ! empty( $this->params ) ) {
				$params_to_use = iFoundSaveThisUpgrader::new_hookless()->maybe_temp_upgrade_params($this->params);
				$endpoint .= '&' . http_build_query($params_to_use);
				// http_build_query makes "true" arrays (numerically indexed, started from zero, no gaps, etc) turn into
				// a string like mykey[0]=hello&mykey[1]=world. If you want it like mykey[]=hello&mykey[]=world, you can
				// do the following, which I got from:
				// https://www.php.net/manual/en/function.http-build-query.php#111819
				// I'm NOT going to do it now, because I don't feel confident that I know what the repercussions would
				// be.
				// $endpoint = preg_replace('/%5B[0-9]+%5D/simU', '%5B%5D', $endpoint);

			}

			/** Set the API Secret */
			$endpoint .= '&apikey='. $this->api_secret_urlencoded();

			$idxurls[] = $endpoint;

			/** @link https://codex.wordpress.org/Function_Reference/wp_remote_get */
			$headers = $this->headers();
			$response = wp_remote_get( $endpoint, $headers);

			if( is_array( $response ) ) {

				$results = json_decode( $response['body'] );

				if (isset($results->listings)) {

					return $results;

				}

			}

			return new stdClass();

			 /** If no API Key, let's send an error message. */
		} else die( 'An API Key is required. Please contact the site admin.' );

	}

	public function change_mls_for_account_on_idx_server($new_mls) {
		$endpoint = $this->base_url(true) . '/account/mls';
		$headers = $this->headers();
		$args = &$headers;
		$body = [
			'mls' => $new_mls,
			'apikey' => $this->api_secret(),
		];
		$args['body'] = $body;
		$response = wp_remote_post($endpoint, $args);
		$this->util()->throw_if_wp_error($response);
	}

	/**
	 * Request
	 *
	 * Make API request and return response.
	 *
	 * @since 1.0.0
	 * @since 1.8.0 return new stdClass() if empty response.
	 * @since 3.6.2 Use string as param for iFoundIdx::pdp_request()
	 *
	 * @param  string $mls_id  The MLS ID.
	 * @return object $results The result of the API request.
	 */

	public function pdp_request( $mls_id ) {

		$endpoint = '';

		if( $this->api_secret_urlencoded() ) {

			global $idxurls;

			/** Address to PDP Endpoint */
			$endpoint .= $this->base_url() . '/q/prop/?';

			$endpoint .= 'q=' . $mls_id;

			/** Set the API Secret */
			$endpoint .= '&apikey='. $this->api_secret_urlencoded();

			$idxurls[] = $endpoint;

			/** @link https://codex.wordpress.org/Function_Reference/wp_remote_get */
			$response = wp_remote_get( $endpoint, $this->headers() );

			if( is_array( $response ) ) {

				$results = json_decode( $response['body'] );

				if( ! empty( $results ) ) {
					// PHP 8.1 cleanup. This was being set to an undefined variable, so I'll clean up the warning, but
					// who knows, maybe what it did was needed.
					$results->input_obj = null;

					return $results;

				}

			}

			return new stdClass();

		 /** If no API Key, let's send an error message. */
		} else die( 'An API Key is required. Please contact the site admin.' );

	}

	public function search_nearby_enabled() {
		return $this->google_key = get_option( 'ifound_api_settings' )['google_custom_api'];
	}

	protected function base_url( $search = false ) {
		if( $search ) {
			return $this->get_config()['api_base_url'];
		}
        return $this->get_config()['idx_url'];
	}

	/**
	 * Headers
	 *
	 * An array of HTTP headers for our IDX servers.
	 *
	 * @since 1.0.0
	 * @since 2.4.20 Move the headers into associative array.
	 *
	 * @return array $headers An array of HTTP headers
	 */

	public function headers() {

		return array(
			'user-agent'  => 'GetFoundIDX (iFound/1.0 +https://ifoundagent.com)',
			'headers'     =>  array(
            	'Accept' 					=>  'application/json',
            	'X-IDX-Client-IP' 			=> $_SERVER['REMOTE_ADDR'],
            	'X-IDX-Client-Referer' 		=> ( isset( $_SERVER['HTTP_REFERER'] ) ? $_SERVER['HTTP_REFERER']  : '' ),
            	'X-IDX-Client-User-Agent' 	=> $_SERVER['HTTP_USER_AGENT'],
				// Don't let the referer be too long, as it will overwhelm the headers size and servers will possibly reject it.
				'Referer' 					=> substr($this->current_url(), 0, 1024),
        	)
		);

	}

	public function api_secret_urlencoded() {
		$api_secret = $this->api_secret();
		// Return false instead of null
		return $api_secret ? urlencode($api_secret) : false;
	}

	protected function api_secret() {
		$option = get_option('ifound_api_settings');
		if ($option) {
			return $option['api_secret'];
		}
		return null;
	}

	/**
	 * Check Image
	 *
	 * Checks a single image, sends a no-image.png if the listing has no image.
	 *
	 * @since 1.0.0
	 *
	 * @param  string $image The unprocessed full image URL.
	 * @param  bool   $large If the image is a large version.
	 * @return string $image The processed full image URL.
	 */

	public function check_image( $image, $large = false ) {

		if( empty( $image ) ) {

			/** Get the correct no-image image to return. */
			if( $large ) {

				return plugin_dir_url( __FILE__ ) .'images/no-image-lg.png';

			}

			return plugin_dir_url( __FILE__ ) .'images/no-image.png';

		}

		return $image;

	}

	/**
	 * Check Status
	 *
	 * Check the status and return a human readable status name.
	 *
	 * @since 1.0.0
	 * @since 2.5.11 Add default text for for sale and for rent.
	 * @since 2.5.12 Move Active conditions after contingent. Contingent can still be active. Add Under Contract condition.
	 * @since 2.5.31 Add condition for rentals on PDP.
	 * @since 2.5.65 Ahd New Listing label.
	 * @since 4.1.4  Check list status in lowercase.
	 *
	 * @param object  $listing The object for a single listing returned from API.
     * @param object  $extra Optional. Schema: ['mls_class' => string]
	 * @return string $status  A human readable version of the listing status.
	 */

	public function check_status( $listing, $extra = [] ) {

		global $mls_associations;

		$list_status_lower = strtolower( $listing->ListStatus );

		if( $list_status_lower == 'closed' || $list_status_lower == 'sold' ) {
			return  'SOLD';
		} elseif( $list_status_lower == 'pending' ) {
			return 'Pending';
		} elseif (strpos($list_status_lower, 'coming soon') !== false) {
			return 'Call Me - Coming Soon';
		}

		if( ! empty( $listing->Contingent ) && $listing->Contingent != 'No' ) {

			$array 	= (array) $mls_associations->contingent;
			$key 	= array_search( $listing->Contingent, $array );
			return empty( $key ) ?  'Contingent' : $key;

		}

		$mls_class = $this->mls_class(isset($extra['mls_class']) ? $extra['mls_class'] : false);
		if( $list_status_lower == 'active' && $mls_class == 'res' && $this->is_new_listing( $listing ) )
			return  'New Listing';

		if( $list_status_lower == 'active' && ( $mls_class == 'rentals' || $listing->mls_class == 'rentals' ) ) {
			return  'For Rent';
		} elseif( $list_status_lower == 'active' ) {
			return  'For Sale';
		} elseif(strpos($list_status_lower, 'under contract') !== false) {
			return 'Under Contract';
		}

		return 'Call Me';

	}

	/**
	 * Is New Listing
	 *
	 * Check yo see if this is a new listing.
	 *
	 * @since 2.5.65
	 *
	 * @param  object  $listing     The object for a single listing returned from API.
	 * @return bool    $new_losting True if new listing. False if not.
	 */

	public function is_new_listing( $listing ) {

		return ( isset( $listing->ListDate ) && strtotime( $listing->ListDate ) > ( time() - 86400 * 2 ) ) ? true : false;

	}

	private function may_show_broker_compensations() {
		if (!apply_filters('ifound_has_feature', 'broker-compensation')) {
			return false;
		}
		$history = $this->get_broker_compensations_for_agent()['legal_disclaimer_history'];
		if ($history) {
			return $history[count($history) - 1]['status'] === 'checked';
		}
		return false;
	}

	// Note that this is the "settings" aka options data, not just the value from the compensations key.
	public function get_broker_compensations_for_agent() {
		return iFoundCrm::new_hookless()
			->get_option_for_agent(iFoundAdmin::$ifound_broker_compensations_option_name, [
				'legal_disclaimer_history' => [],
				'compensations'            => [],
			]);
	}

	/**
	 * Filter Merge Tags
	 *
	 * Replace merge tags from content templates.
	 *
	 * @since 1.0.0
	 *
	 * @param  string $body Unfiltered content tehplate.
	 * @param  object $data Data for each marge tag.
	 * @return string $body Filtered content with merge tags replaced.
	 */

	public function filter_merge_tags( $body, $data = array() ) {

		/** Let's replace all the merge tags for the data. */
		if( $body && ( is_array( $data ) || is_object( $data ) ) ) {

			foreach( $data as $key => $value ) {

				if( is_object( $value ) || is_array( $value ) ) continue;

				$body = str_ireplace( '{' . $key . '}', $value ?? '', $body );

			}

			if( defined( 'DOING_DETAILS' ) && isset( $data->pdp_template ) ) {
				$broker_compensations = [];
				if ($this->may_show_broker_compensations()) {
					$broker_compensations = $this->get_broker_compensations_for_agent()['compensations'];
				}
				$compensation = $this->util()->array_find($broker_compensations, function($x) use ($data) {
					return $x['mls_id'] === $data->ListingID;
				});

				foreach( $data->pdp_template as $key => $v ) {
					// Has the agent set compensation for the given MLS ID? If so, we replace the value from the MLS
					// data.
					if ($compensation && $key === 'pdp_general_value') {
						$v = str_replace('Buyer Agency Compensation', 'Compensation', $v);
						$pattern = '#<div class="pdp-data buyer-agency-compensation">(.*?)</div>#';
						$did_match = preg_match($pattern, $v, $matches);
						// Was the HTML div there? If so, we replace it. If not, we add it. This is because right now
						// the divs are in our templates, but I plan to remove them a little while after all the MLSs
						// have removed their compensation fields.
						$anchor = <<<EOT
							<div class="compensation_more_info_container">
								<div>
									<a href="javascript:void(0);" data-mls_id="{$data->ListingID}">Additional info</a>
									<i class="fal fa-spinner fa-spin" style="display: none;"></i>
								</div>
								<div class="compensation_more_info_error"></div>
							</div>
						EOT;
						if ($did_match) {
							$v = str_replace($matches[1], $anchor, $v);
						} else {
							$pattern = '#(<div class="pdp-data listing-broker">(.*)</div>\\s*</div>)#m';
							$did_match = preg_match($pattern, $v, $matches);
							if ($did_match) {
								$replacement = $matches[1] . <<<EOT
									<div class="pdp-data-wrapper buyer-agency-compensation">
										<div class="pdp-label buyer-agency-compensation">Compensation:</div>
										<div class="pdp-data buyer-agency-compensation">{$anchor}</div>
									</div>
								EOT;

								$v = str_replace($matches[1], $replacement, $v);
							}
						}

						// And we then remove the following label + value from the MLS data, which is the buyer agency
						// disclaimer. We will remove the disclaimer from the templates eventually, but until then, we
						// remove it here.
						$v = preg_replace('#<div class="pdp-label buyer-agency-compensation-disclaimer">(.*?)</div>#',
							'', $v);
						$v = preg_replace('#<div class="pdp-data buyer-agency-compensation-disclaimer">(.*?)</div>#',
							'', $v);
					}

					$body = str_ireplace( '{' . $key . '}', $v ?? '', $body );
				}
			}

		}

		/**
		 * This is here because the do_action fires before the content is filtered.
		 * Thus putting the button before the content.
		 *
		 * @since 1.0.9
		 */

		if( strpos( $body, '{SavePropButton}' ) !== false ) {

			$address = apply_filters( 'ifound_address', $data );

			ob_start();
			is_admin() ? '' : do_action( 'ifound_save_this_button', 'property-alert', $address, $data->ListingID );
			$value = ob_get_clean();

			$body = str_replace( '{SavePropButton}', $value, $body );

		}

		return $body;

	}

	/**
	 * MLS Disclosure
	 *
	 * MLS disclosure displayed in the footer of every page.
	 *
	 * @since 1.0.0
	 * @since 1.0.27 Use the MLS disclosure included in Global $mls_associations.
	 * @since 1.2.31 Add current_time which considers gmt offset. @see iFOUND::current_time()
	 * @since 3.1.1  Use merge tag for fair housing logo.
	 */

	public function mls_disclosure() {

		global $mls_associations;

		if( isset( $mls_associations->disclosure ) ) {

			$last_time    = isset( $this->last_updated ) ? $this->last_updated : time() - 8000;
			$current_time = $this->current_time( $last_time );
			$last_updated = date( 'F j, Y, g:i a', $current_time );
			$disclosure = str_replace( '{LastUpdated}', $last_updated, $mls_associations->disclosure );

			$agent = get_option( 'ifound_agent' );
			// Fun fact: we store blank entries as (float) 0.0 or potentially (int) 0 (is this our fault or PHP's or
			// Wordpress'?). To not confuse anyone, don't output the value 0, just output blank if the broker name is
			// blank.
			$broker_name = is_string($agent['broker_name']) ? $agent['broker_name'] : '';
			$disclosure = str_replace( '{BrokerName}', $broker_name, $disclosure );

			$realtorLogo = esc_url( plugins_url( 'images/realtor-logo.png', __FILE__ ) );
			$disclosure = str_replace( '{RealtorLogo}', $realtorLogo, $disclosure );
			$fairHousingLogo = esc_url( plugins_url( 'images/fair-housing-mls-logo.png', __FILE__ ) );
			$disclosure = str_replace( '{FairHousingLogo}', $fairHousingLogo, $disclosure );

			echo str_replace( '{Year}', date( 'Y' ), $disclosure );

		}

	}

	/**
	 * Broker Disclosure
	 *
	 * Broker disclosure displayed in the footer of every page.
	 *
	 * @since 1.1.2
	 */

	public function broker_disclosure() {

		$agent = get_option( 'ifound_agent' );

		if( ! empty( $agent['broker_disclosure'] ) ) {

			$disclosure = str_replace( '{Year}', date( 'Y' ), $agent['broker_disclosure'] ); ?>

			<div class="broker-disclosure">

				<div class="ifound-wrap">

					<? _e( $disclosure, 'ifound' ); ?>

				</div>

			</div><?

		}

	}

	/**
	 * No Data
	 *
	 * This is what we do when we have no data to display.
	 *
	 * @since 1.0.0
	 *
	 * @param string $type The type of query that came up empty.
	 */

	public function no_data( $type ) {

		die( 'The data was missing from your request.' );

	}

	/**
	 * Template
	 *
	 * Let's find the correct template.
	 *
	 * @since 1.0.2
	 * @since 1.0.30 Remove home body class from all our templates.
	 * @since 1.0.30 Remove current-menu-item from all our templates.
	 * @since 1.2.14 Add cmc-report.
	 * @param object  $extra Optional. Used in the template PHP. Schema: ['mls_class' => string].
	 */

	public function template( $results = false, $extra = [] ) {

		if		( defined( 'DOING_DETAILS' 	 	) ) $type = 'property-details';
		elseif	( defined( 'DOING_SHORTCODE' 	) ) $type = 'shortcode';
		elseif	( defined( 'DOING_ADVANCED'  	) ) $type = 'advanced-search';
		elseif	( defined( 'DOING_SAVED_SEARCH' ) ) $type = 'advanced-search';
		elseif	( defined( 'DOING_SUBDIVISIONS' ) ) $type = 'polygons';
		elseif	( defined( 'DOING_UNSUBSCRIBE'  ) ) $type = 'unsubscribe';
		elseif	( defined( 'DOING_CLIENT'  		) ) $type = 'client-profile';
		else 										$type = 'search-results';

		/** Remove .home body class. */
		add_filter( 'body_class', array( $this, 'remove_home_body_class' ), 99 );
		add_filter( 'nav_menu_css_class' , array( $this, 'remove_nav_class' ) );

		$custom_template = $this->custom_template( $type );

		/** Check the theme for a custom template.*/
		if(
			! wp_is_mobile()
			&&
			! empty( $custom_template )
			&&
			file_exists( $custom_template )
		) {

			/** We store this with the theme stylesheet so we can customize it to the theme. */
			return include( $custom_template );

		} else {

			/** This is just in case the theme does not a custom template. */
			return include( plugin_dir_path( __FILE__ ) . 'includes/templates/' . $type . '.php' );

		}

	}

	/**
	 * Custom Template
	 *
	 * Le't find a custom template in the theme style sheet dir. You know, where the themes function.php is.
	 *
	 * @since 1.7.0
	 *
	 * @param string $type            The type of template we are searching for.
	 * @return mixed $custom_template Path to custom templae. Or bool false on failure.
	 */

	public function custom_template( $type ) {

		$options = array(
			'search-results' 	=> 'ifound_results_settings',
			'property-details' 	=> 'ifound_detalis_settings'
		);

		if( array_key_exists( $type, $options ) ) {

			if( $option	= get_option( $options[$type] ) ) {

				if( ! empty( $option['custom_template'] ) ) {

					return get_stylesheet_directory() . '/' . $option['custom_template'];

				}

			}

		}

		return false;

	}

	public function is_reso_web_api() {
	    $reso_web_api_mlss_config = $this->get_config()['reso_web_api_mlss'];
	    $reso_web_api_mlss_array = preg_split('/,\s*/', $reso_web_api_mlss_config);
	    $mls_name = static::mls_name();
	    $is_in_array = in_array($mls_name, $reso_web_api_mlss_array, true);
	    return $is_in_array;
	}

	// This isn't the best name for this because it's not like Bright is the only MLS that has multiple states. But
	// while Realtracs has multiple states, we handle it in a different way. At this point, the main thing that 'is
	// multiple state MLS' means is that when we list the cities, we include the state too. The term 'isMultipleState'
	// is kind of legacy at this point, and I'll keep it for now.
	public function is_multiple_state_mls() {
		return in_array(static::mls_name(), ['brightmls'], true);
	}

	/**
	 * MLS Name
	 *
	 * Name of MLS stored in database.
	 *
	 * @since 1.0.0
	 *
	 * @return string $mls_name A lowercase version of the MLS name.
	 */

	public static function mls_name() {
		$option = get_option( 'ifound_api_settings' );
		return strtolower( $option['mls_name'] );
	}

	/**
	 * MLS Class
	 *
	 * Gets the mls class code from the full mls class name.
	 *
	 * @since 1.0.0
	 * @since 2.4.8 Update to consider if mls_class is allowed via setting. This will override the cookie if settings change.
	 * @since 2.4.10 Add both params and move all current mls class decisions into this method.
	 *
	 * @global object $mls_associations
	 *
	 * @param  string $mls_class The name of the MLS class or bool false in none is provided.
	 * @param  bool   $code      What type of MLS class name to return. Full name on false. The MLS class code if true.
	 * @return string $mls_class The current MLS class code. Defaults to res.
	 */

	public function mls_class( $mls_class = false, $code = true ) {
		global $mls_associations;

		if( ! $mls_class ) {

		    // Default to residential
			$mls_class = 'Residential';

			// Check to see if the mls_class is in the URL query string
			if (isset($_GET['mls_class'])) {
				if (in_array($_GET['mls_class'], $this->allowed_mls_classes(), true)) {
					$mls_class = $_GET['mls_class'];
				}
			} else {
				// Check to see if the mls_class is in the URL path
				$matches = [];
				$uri = urldecode($_SERVER['REQUEST_URI']);
				preg_match('#/mls_class-([\ a-zA-Z]+)/#', $uri, $matches);
				if ($matches) {
					$mls_class = $matches[1];
				}
			}

		}

		if( $code ) {

			return isset( $mls_associations->mls_class->$mls_class ) ? $mls_associations->mls_class->$mls_class : $mls_class;

		}

		return $mls_class;

	}

	/**
	 * Allowed MLS Classes
	 *
	 * These are the allowed MLS classes that are checked in WP Admin > iFound Settings > Search Settings.
	 * If we are in the Admin. We always use all the MLS classes provided in $mls_associations->mls_class.
	 *
	 * @since 2.4.10
	 *
	 * @global object $mls_associations
	 *
	 * @return array $allowed_mls_classes An array of MLS Classes.
	 */

	public function allowed_mls_classes() {

		if( $this->is_admin() ) {

			global $mls_associations;

			$mls_classes = isset( $mls_associations->mls_class ) ? $mls_associations->mls_class : array();

			foreach( $mls_classes as $key => $value )
				$allowed_mls_classes[] = $key;

			return empty( $allowed_mls_classes ) ? array() : $allowed_mls_classes;

		} else {

			$settings = get_option( 'ifound_search_settings' );

			return empty( $settings['mls_class'] ) ? array() : $settings['mls_class'];

		}

	}

	/**
	 * MLS Associations
	 *
	 * An API response containing the human readable names as key and the searchable value.
	 *
	 * @since 1.0.0
	 * @since 2.4.11 Change name from relationships.
	 * @since 2.4.12 Store mls_associations in a 1 hour transient.
	 * @since 2.4.18 Do not allow empty array to be saved in transient.
	 *
	 * @global object $mls_associations All the mls_associations included in the API response.
	 */

	public function mls_associations() {

		global $mls_associations, $idxurls;;

		$transient = 'mls_associations';

		$mls_associations = get_transient( $transient );

		$mls_name = $this->mls_name();

		if( empty( $mls_associations ) && $mls_name ) {

			$mls_associations_origin = $this->get_config()['mls_associations_origin'];
			$url = $mls_associations_origin . $mls_name . '/';

			$idxurls[] = $url;

			$response = wp_remote_get( $url, $this->headers() );
			$mls_associations = json_decode( wp_remote_retrieve_body( $response ) );

			if( ! empty( $mls_associations ) )
				set_transient( $transient, $mls_associations, 24 * HOUR_IN_SECONDS );

		}

		$mls_associations = isset( $mls_associations ) ? $mls_associations : array();

	}

	/**
	 * MLS Association Value
	 *
	 * Get the mls_association search value.
	 *
	 * @since 3.6.4
	 *
	 * @param  string $key   The easy_name to search.
	 * @param  string $value The readable value.
	 * @return string $value The search value of the mls association. Or value in none exist.
	 */

	public function mls_associations_value( $key, $value ) {

		global $mls_associations;

		return isset( $mls_associations->$key->$value ) ? $mls_associations->$key->$value : $value;

	}

	/**
	 * Field Mappings
	 *
	 * An object containing all the mappings stored in the Rails Admin. These are different for each MLS Name and MLS Class.
	 * This is stored in a 24 hour expiring transient. To refresh the data stored use ?ifound_refresh as a url parameter.
	 *
	 * @see iFoundIdx::refresh_transients()
	 *
	 * @link https://admin.ifoundagent.com/#/field_mappings
	 *
	 * @since 1.0.0
	 * @since 1.0.20 Add $class param for iFOUND_admin::sortable_criteria() to always show 'res' mappings.
	 * @since 2.4.18 Do not allow empty array to be saved in transient.
	 *
	 * @param  string $class    A predefined MLS Class or bool false if not defined.
	 * @return object $mappings The mappings object if set. An empty array if not set.
	 */

	public function field_mappings( $class = false ) {

		global $idxurls;

		$mls_class = $class ? $class : $this->mls_class();

		$transient = 'mappings_' . $mls_class;

		if( $this->api_secret_urlencoded() && ! $mappings = get_transient( $transient ) ) {

			$url = $this->base_url( true ) . '/q/field-mapping/?apikey=' . $this->api_secret_urlencoded() . '&mls_class=' . $mls_class;

			$idxurls[] = $url;

			$response = wp_remote_get( $url, $this->headers() );

			$json = json_decode( wp_remote_retrieve_body( $response ) );

			$mappings = $json->field_mappings;

			if( ! empty( $mappings ) )
				set_transient( $transient, $mappings, 24 * HOUR_IN_SECONDS );

		}
		if (isset($mappings)) {
			$mappings->nearby = (object)[
				'display_name' => 'Radius/Pin Drop',
				'easy_name'    => 'nearby',
				'type'         => 'composite',
			];
			$mappings->multibox = (object)[
				'display_name' => 'Text Search',
				'easy_name'    => 'multibox',
				'type'         => 'composite',
			];
			$mappings->open_house = (object)[
				'display_name' => 'Has Open House',
				'easy_name'    => 'open_house',
				'type'         => 'boolean',
			];
			if ($this->get_prop_type_mapped()) {
				$mappings->prop_type_mapped = (object)[
					'display_name'       => 'Property Type',
					'admin_display_name' => 'Property Type (Simple)',
					'easy_name'          => 'prop_type_mapped',
					'type'               => 'composite',
				];
			}
		}

		return isset( $mappings ) ? $mappings : array();

	}

	/**
	 * Mappings
	 *
	 * The res field mappings.
	 *
	 * @since 5.0.0
	 *
	 * @return object $mappings The mappings object.
	 */

	public function mappings() {
		return $this->field_mappings( 'res' );
	}

	/**
	 * Lookups
	 *
	 * An object containing all the values for the mappings keys if has values. These are different for each MLS Name and MLS Class.
	 * This is stored in a 24 hour expiring transient. To refresh the data stored use ?ifound_refresh as a url parameter.
	 *
	 * @see iFoundIdx::refresh_transients()
	 *
	 * @since 1.0.0
	 * @since 2.4.18 Do not allow empty array to be saved in transient.
	 *
	 * @return object $lookups The lookups object if set. An empty array if not set.
	 */

	public function lookups() {

		global $idxurls;

		$transient = 'lookups_' . $this->mls_class();

		if( $this->api_secret_urlencoded() && ! $lookups = get_transient( $transient ) ) {

			$url = $this->base_url( true ) . '/q/lookups/?apikey=' . $this->api_secret_urlencoded() . '&mls_class=' . $this->mls_class();

			$idxurls[] = $url;

			$response = wp_remote_get( $url, $this->headers() );
			$body = wp_remote_retrieve_body($response);
			$json = json_decode($body);
			// $last = json_last_error_msg();

			$lookups = $json->lookups;

			if( ! empty( $lookups ) )
				set_transient( $transient, $lookups, 24 * HOUR_IN_SECONDS );

		}

		return isset( $lookups ) ? $lookups : array();

	}

	/**
	 * Lookups to Criteria
	 *
	 * An object containing all the lookup values with the "Chosen" mls_associations included. These are called and loaded via REST API endpoint.
	 * These are different for each MLS Name and MLS Class.
	 * This is stored in a 24 hour expiring transient. To refresh the data stored use ?ifound_refresh as a url parameter.
	 *
	 * @see iFoundIdx::refresh_transients()
	 *
	 * For a list of "Chosen" mls_associations which vary per site.
	 * @link /wp-admin/admin.php?page=ifound_search_settings
	 *
	 * @since 1.0.16
	 * @since 2.4.18 Do not allow empty array to be saved in transient.
	 *
	 * @return object $criteria All the lookup values, with the "Chosen" mls_associations included.
	 */

	public function lookups_to_criteria() {
		$transient = 'lookups_to_criteria_' . $this->mls_class();

		if ( false === ( $criteria = get_transient( $transient ) ) ) {

			/** Get the "Chosen" mls_associations from the options table. */
			$search_settings = (object) get_option( 'ifound_search_settings' );

			$criteria = $this->lookups();

            // Override certain lookups with MLS associations.
            $overrides = ['city', 'prop_type', 'list_status', 'mls_class', 'contingent'];
            foreach ($overrides as $override) {
                $criteria->$override = (object)$criteria->$override;
                $criteria->$override->values = $search_settings->$override;
            }

			if( ! empty( $criteria ) )
				set_transient( $transient, $criteria, 24 * HOUR_IN_SECONDS );
		}

		return isset( $criteria ) ? $criteria : array();

	}

	/**
	 * Body Class
	 *
	 * our custom body class
	 *
	 * @since 1.0.4
	 * @since 1.0.8 All body class methods were combined and moved to iFOUND::body_class()
	 *
	 * @param array $classes The array of body classes.
	 * @return array $classes The array with our custom class added.
	 */

	public function body_class( $classes ) {

		/** We want to add the MLS name as a class for every page. */
		$classes[] = $this->mls_name();

		if( defined( 'DOING_RESULTS' ) ) {

			$results_settings	= (object) get_option( 'ifound_results_settings' );

			/** This adds the custom results class if we have one set. */
			if( $class = $results_settings->results_class ) {

 				$classes[] = $class;

			}

		}

		if( defined( 'DOING_DETAILS' ) ) {

			$detalis_settings = (object) get_option( 'ifound_detalis_settings' );

			if( $class = $detalis_settings->details_class ) {

 				$classes[] = $class;

			}

		}

		if( defined( 'DOING_ADVANCED' ) || defined( 'DOING_SAVED_SEARCH' ) || defined( 'DOING_POLYGONS' ) ) {

 			$classes[] = 'ifound-advanced-body';

		}

		if( wp_is_mobile()  ) {

 			$classes[] = 'ifound-mobile';

		}

		$results_settings = get_option( 'ifound_results_settings' );

		if( is_array( $results_settings['grid_layout'] ) && in_array( 'checked', $results_settings['grid_layout'] ) ) {

			$classes[] = 'ifound-grid';

		}

		// Allow our dynamic pages to be targeted by (external, custom-per-client) CSS.
		if (defined('DOING_SAVED_SEARCH')
			|| defined('DOING_DETAILS')
			|| defined('DOING_ADVANCED')
			|| defined('DOING_RESULTS')
			|| defined('DOING_SUBDIVISIONS')
			|| defined('DOING_POLYGONS')
			|| defined('DOING_CLIENT')
		) {
			$classes[] = 'ifa-dynamic';
		}

    	return $classes;

	}

	/**
	 * Remove Home Body Class
	 *
	 * This is used to remove the .home body class.
	 *
	 * @since 1.0.30
	 */

	public function remove_home_body_class( $classes ) {
		return array_filter( $classes, function( $i ) { return $i != 'home'; } );
	}

	/**
	 * Remove Nav Class
	 *
	 * This is used to remove the current-menu-item class.
	 *
	 * @since 1.0.30
	 */

	public function remove_nav_class( $classes ) {
		return array_filter( $classes, function( $i ) { return $i != 'current-menu-item'; } );
	}

	/**
	 * Classes
	 *
	 * Classes using IDX data.
	 *
	 * @since 2.5.17
	 * @since 2.5.18 Evaluate contingent as if it is empty. API returns an empty value for contingent very often.
	 *
	 * @param object $listing The data for a single listing.
	 * @return string $classes A strung of the data for classes.
	 */

	public function classes( $listing ) {

		global $mls_associations;

		$c = ( isset( $listing->Contingent ) && $listing->Contingent != 'No' ) ? $listing->Contingent : false;
		$contingent = ( $c && isset( $mls_associations->contingent->$c ) ) ? sanitize_key( $mls_associations->contingent->$c ) : false;

		$classes = array_filter( array(
			$contingent,
			isset( $listing->ListStatus ) 	? ucwords( sanitize_key( $listing->ListStatus ) ) 	: false,
			isset( $listing->City ) 		? sanitize_key( $listing->City ) 					: false,
			sanitize_key( $this->mls_class( false, false ) )
		));

		return implode( ' ', $classes );
	}

	/**
	 * Refresh Transients
	 *
	 * This is a listener that deletes all the transients assciated with the iFound plugin.
	 * Use ?ifound_refresh as url param.
	 *
	 * @since 1.0.16
	 * @since 1.0.23 Refresh on search settings page submit.
	 * @since 2.4.12 Delete mls_associations transient. Then call @see iFOUND::mls_associations() to rebuild.
	 * @since 2.5.37 Add fetaured_listings, search_bar, adv_search_bar
	 * @since 2.5.38 Add ability refresh on plugin update.
	 * @since 2.5.63 Add action to refresh transients
	 *
	 * @param bool $uptate Bool true if we are doing a plugin update.
	 */

	public function refresh_transients( $uptate = false ) {

		/** Refresh on seacrh settings page submit or by query param. */
		if(
			$uptate
			||
			isset( $_REQUEST['ifound_search_settings'] )
			||
			isset( $_GET['ifound_refresh'] )
			||
			(
				isset( $_POST['option_page'] )
				&&
				$_POST['option_page'] == 'ifound_featured_settings'
			)
		) {

			delete_transient( 'mls_associations' );
			delete_transient( 'ifound_featured_listings' );

			$this->mls_associations();

			global $mls_associations;

			if( ! isset( $mls_associations->mls_class ) ) return;

			foreach( $mls_associations->mls_class as $mls_class ) {

				delete_transient( 'lookups_' . $mls_class );
				delete_transient( 'mappings_' . $mls_class );
				delete_transient( 'lookups_to_criteria_' . $mls_class );
				delete_transient( 'ifound_search_bar_' . $mls_class );
				delete_transient( 'ifound_adv_search_bar_' . $mls_class );

			}

		}

	}

	/**
	 * IDX URLs
	 *
	 * Display the requested IDX URLs as links in the top right of the page if `idxurls` is specified in the query string
	 * This code was copied from the profound plugin.
	 *
	 * @since 1.0.17
	 */

	public function idx_urls() {

		global $idxurls;
		$urls = isset($idxurls) && is_array($idxurls) ? $idxurls : [];

		if ( isset( $_GET['idxurls'] ) ) { ?>

			<div class="idx-urls">

				<ul><?

					foreach ( $urls as $url ) {
						$url = str_replace('newidx:8155', 'idx-server.test', $url);
						// This isn't needed on the MLS associations site, but doesn't hurt.
						$url = add_query_arg(['debug' => 2], $url);
						?>

						<li>
							<a target="_blank" href="<?= $url ?>"><?= htmlentities($url) ?></a>
						</li><?

					} ?>

				</ul>

			</div><?

		}

	}

	public function video_code( $video_code ) {

		// FIXME: Get a default code if 1 exists
		$video_code = 'oEOB4cV29Gg';

		return $video_code ? $video_code : false;

	}

    /**
     * @param int $http_status_code
     */
	public function show_wp_template_page_and_die($http_status_code) {
		status_header( $http_status_code );
		nocache_headers();
		include( get_query_template( strval($http_status_code) ) );
		die();
	}

	public function current_user_can_create_posts() {
	    // Here's the story. I'm struggling with Wordpress permissions right now. I want to do a simple check to see if
		// the current user can create a post using current_user_can, but I can't figure out how to do it. Based on
		// googling, it doesn't seem straight-forward. I need to do it in multiple places, so I'm putting it here to be
		// central so that when I figure out the right way, I only have to update one place.
		// A way that other developers have dealt with it in the past was just with $this->is_site_admin() (a function
		// on the iFoundCRM class), or using current_user_can('manage_options').

		// What I'd really like to be able to do is current_user_can('create_posts'), but that doesn't seem
		// to work.
		// I got the following way/idea of checking from: https://wordpress.stackexchange.com/a/342747/27896
		$can_create = current_user_can( get_post_type_object( 'post' )->cap->create_posts);
		return $can_create;
	}

	// Realtracs has several property sub types that seem wrong to ask consumers to understand, and several of them
	// overlap from my perspective. So I'll do a mapping here, such that one label can map to multiple property
	// sub types.
	//
	// The following was written when we first did this concept for the CMC code, and it makes more sense in that
	// context.
	//
	// As far as what goes in the database though, it should be one value. That way we can make changes to
	// the mapping after the fact, if we want. If we don't return choices here, then it means the choices from the
	// gravity form field should be used.
	// The reason I'm using hard-coded array keys is because we can't have them change, which is what would happen if
	// we didn't force the keys. If we e.g. removed the first one, they'd all have changed. Note that if we change any,
	// we'll be changing the CMC report; the user might see vastly different values. Although perhaps that's no big
	// deal considering that we don't truly save off the report anyway, we always re-run it with the latest listings.
	public function get_prop_type_mapped() {
		$mls = iFoundIdx::mls_name();
		if ($mls === 'realtracs') {
			$choices = [
				1 => [
					'label' => 'Single Family',
					'prop_sub_types' => [
						'Single Family Residence',
						'Zero Lot Line',
					],
				],
				2 => [
					'label' => 'Condo',
					'prop_sub_types' => [
						'Garden',
						'High Rise',
						'Flat Condo',
						'Other Condo',
						'Horizontal Property Regime - Attached',
						'Horizontal Property Regime - Detached',
						'Loft',
					],
				],
				3 => [
					'label' => 'Townhouse',
					'prop_sub_types' => [
						'Townhouse',
					],
				],
				4 => [
					'label' => 'Mobile Home',
					'prop_sub_types' => [
						'Modular Home',
						'Mobile Home',
						'Manufactured On Land',
					],
				],
			];
			return $choices;
		}
		return null;
	}

	// The agent is allowed to disallow certain property types. So cross reference all choices with what the agent
	// allows. We might potentially remove an entire mapping. So for example, if the agent didn't allow Townhouse, and
	// the mapping had only the Townhouse property type in its array, we'd exclude the Townhouse mapping altogether.
	private function get_agent_filtered_prop_type_mapped($choices) {
		$criteria = $this->lookups_to_criteria()->prop_type->values;
		$filtered_choices = array_filter($choices, function($value) use ($criteria) {
			// Is at least one property sub type allowed by the agent?
			return $this->util()->in_both_arrays($value['prop_sub_types'], $criteria);
		});
		return $filtered_choices;
	}

	// Remember here that the KEYS aren't necessarily continuous. As in, they might be [1, 2, 4].
	public function get_agent_filtered_prop_type_mapped_labels($choices) {
		$filtered_choices = $this->get_agent_filtered_prop_type_mapped($choices);
		$mapped = array_map(function($value) { return $value['label']; }, $filtered_choices);
		return $mapped;
	}

	// Remember here that the keys aren't necessarily continuous. As in, they might be [1, 2, 4].
	public function get_agent_filtered_prop_type_mapped_keys($choices) {
		$filtered_choices = $this->get_agent_filtered_prop_type_mapped($choices);
		$keys = array_keys($filtered_choices);
		return $keys;
	}

	private function is_field_value_the_same_as_label($easy_name) {
		$value_to_label_fields = ['prop_type_mapped'];
		return !in_array($easy_name, $value_to_label_fields, true);
	}

	// Originally, whenever we'd create a dropdown, the label was also the value. With our new prop_type_mapped field,
	// the values are different than the labels. But we don't want the developer to care which case it is when iterating
	// through the list, we just want them to know there is a value and a label. So for the fields other than
	//  prop_type_mapped, transform their key to be the same as their value.
	// Reminder: I previously thought an easy way to deal with this was to see if the array had the key zero, which I
	// thought would tell us if it was NOT a manufactured list, like the one at get_prop_type_mapped() (which we start
	// at a key of 1). However, that's not good enough because for fields like prop_type, the agent is able to filter
	// out some in their Search Results Settings, in which case the key of 0 might not exist.
	protected function make_value_to_label_mapping($values, $easy_name) {
		if ($this->is_field_value_the_same_as_label($easy_name)) {
			$new_mapping = [];
			foreach ($values as $value) {
				$new_mapping[$value] = $value;
			}
			return $new_mapping;
		}
		return $values;
	}

    // The agent can choose which prop types to allow. We store which ones are allowed by their label, which is
    // concocted by iFound, as opposed to storing the actual value used in the MLS. This seems bad because we allow
    // the labels to change, but oh well, I don't want to deal with that now. So we need to cross-reference which MLS
    // values to return by their labels. As in, what's returned is the actual MLS values.
    protected function get_agent_filtered_prop_type() {
	    global $mls_associations;
	    $mls_associations_prop_type_array = (array) $mls_associations->prop_type;
	    $agent_allow_prop_type_labels = $this->lookups_to_criteria()->prop_type->values;
        $agent_filtered_prop_type = [];
        foreach ($agent_allow_prop_type_labels as $label) {
            $agent_filtered_prop_type[$label] = $mls_associations_prop_type_array[$label];
        }
        $agent_filtered_prop_type = array_unique($agent_filtered_prop_type);
        return $agent_filtered_prop_type;
    }

	public function filter_prop_types_for_agent(array $input_prop_types) {
		$agent_filtered_prop_type = $this->get_agent_filtered_prop_type();
        $intersection = array_intersect($input_prop_types, $agent_filtered_prop_type);
		return $intersection;
	}

	public function ajax_pdp_compensation() {
		$mls_id = $_REQUEST['mls_id'];
		$broker_compensations = [];
		if ($this->may_show_broker_compensations()) {
			$broker_compensations = $this->get_broker_compensations_for_agent()['compensations'];
		}
		$compensation = $this->util()->array_find($broker_compensations, function($x) use ($mls_id) {
			return $x['mls_id'] === $mls_id;
		});
		if (!$compensation) {
			wp_die('That MLS ID does not have compensation additional info');
		}
		echo json_encode([
			'text' => strip_tags($compensation['text']),
		]);
		die();
	}
}

$idx_directories = array(
	'includes',
	'admin',
	'depricated'
);

foreach( $idx_directories as $directory ) {

	foreach ( glob( plugin_dir_path( __FILE__ ) . $directory . '/*.php' ) as $file ) {
	    require_once $file;
	}

}
