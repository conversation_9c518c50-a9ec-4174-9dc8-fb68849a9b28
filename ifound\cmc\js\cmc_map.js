jQuery( document ).ready( function( $ ) {

	$('.open').on('click', function(e) {
		$(this).text($(this).text() == 'Close' ? 'More' : 'Close');
		$(this).parents('.listing-wrap').find('.open-box').slideToggle('slow');
	});

	var max_radius = parseFloat(cmc_map.max_radius);
	/** 1 mile = 1609.34 meters */
	var areas = {
		area_1: {
		 	radius: max_radius * 1609.34 * .25,
		 	color: 'green'
		},
		area_2: {
			radius: max_radius * 1609.34 * .5,
		 	color: 'yellow'
		},
		area_3: {
		 	radius: max_radius * 1609.34 * .75,
		 	color: 'orange'
		},
		area_4: {
			radius: max_radius * 1609.34,
			color: 'red'
		}
	};

 	function initMap() {

		var styles = [
			{ featureType: "all",
				stylers: [
					{ saturation: 0 }
				]
			},{ featureType: "road.arterial",
				elementType: "geometry",
				stylers: [
					{ hue: "#ff9009" },
					{ saturation: 20 }
				]
			}
		];

 		// Create the map.
 		var map = new google.maps.Map(document.getElementById('cmc-map'), {
			zoom: 14,
			center: { lat: cmc_map.map_stuff[0][1], lng: cmc_map.map_stuff[0][2] },
			...window.iFoundGlobal.sharedGoogleMapBaseOptions,
 		});


		// Construct the circle for each value in citymap.
		// Note: We scale the area of the circle based on the population.
		for (var area in areas) {
			// Add the circle for this city to the map.
			var areaCircle = new google.maps.Circle({
				strokeColor: areas[area].color,
				strokeOpacity: 0.8,
				strokeWeight: 4,
				fillOpacity: 0,
				map: map,
				center: { lat: cmc_map.map_stuff[0][1], lng: cmc_map.map_stuff[0][2] },
				radius: areas[area].radius
			});
		}

		google.maps.event.addListener(map, 'zoom_changed', function() {
			if(document.contains(document.getElementById('zoom'))){
				var zoom = map.getZoom();
				document.getElementById('zoom').value = zoom;
			}
		});

		setMarkers(map);

		map.fitBounds(areaCircle.getBounds());
 	}


	function setMarkers(map) {

		var image = {
			subject : cmc_map.image_url + 'subject.png',
			active : cmc_map.image_url + 'active.png',
			closed : cmc_map.image_url + 'closed.png'
		};

		z = 100;

        for (var i = 0; i < cmc_map.map_stuff.length; i++) {
          	var property = cmc_map.map_stuff[i];
         	var marker = new google.maps.Marker({
            	position: {lat: property[1], lng: property[2]},
            	map: map,
            	icon: image[property[3]],
            	title: property[0],
            	zIndex: z
          	});
			z++;

			attachLink(marker, property[4]);
        }

		function attachLink( marker, anchorID ){
			marker.addListener( 'click', function() {
				$( '.highlight' ).removeClass( 'highlight' );
    			$('html, body').animate({
        			scrollTop: $( '#' + anchorID ).offset().top - ( $( window ).height() - $( '#' + anchorID ).outerHeight( true ) ) / 2
    			}, 2000);
				$( '#' + anchorID ).addClass( 'highlight' );
  			});
		}
	}

	var mapHasBeenInitialized = false;
	var $cmcMap = $('#cmc-map');
	var $toggleShowMapButton = $('.toggle-show-map');
	function toggleShowAndMaybeInitMap() {
		$cmcMap.toggle(0, 'swing', function() {
			var isShowing = $(this).is(':visible');
			$toggleShowMapButton.text(isShowing ? 'Hide map' : 'Show map');
			if (!mapHasBeenInitialized) {
				window.iFoundGlobal.loadGoogleMaps().then(function() {
					initMap();
				});
				mapHasBeenInitialized = true;
			}
		});
	}

	var isPdp = !!$('.ifound-details').length;
	if (isPdp) {
		$toggleShowMapButton.show();
		$toggleShowMapButton.on('click', function() {
			toggleShowAndMaybeInitMap();
		});
		if (cmc_map.show_map_by_default) {
			toggleShowAndMaybeInitMap();
		}
	} else {
		// If we're in this block, it means we're on the CMC page.

		window.iFoundGlobal.loadGoogleMaps().then(function() {
			$cmcMap.show();
			initMap();
		});
	}

});
