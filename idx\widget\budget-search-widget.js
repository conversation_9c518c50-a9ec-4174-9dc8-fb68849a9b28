var clean = function(input) {
	return input.replace(/[$,]/g, "");
}

// Run a reverse calculation based on the user-entered 'preferred monthly payment' to
// attempt to return a sensible idea of affordable properties.
// We do some guesswork. We guess interest rate, which probably isn't bad. We guess
// insurance. We guess down payment, which is a decent approximation, but only for
// a conventional down payment and not others.
var reverse_calculate = function (preferredMonthlyPayment) {
	var years = 30;
	// This is just a guess.
	var interestRateVal = 3.5;
	// Annual tax is basically a function of list price. Since we don't know the list price yet,
	// (it's what we're trying to calculate here), I've noticed a good approximation for the annual
	// taxes is the monthly payment.
	var taxesVal = preferredMonthlyPayment;
	// This is just a guess for now.
	// It's $80/mo for a year.
	var insuranceVal = 80 * 12;
	var monthlyTax = taxesVal / 12;
	var monthlyInsurance = insuranceVal / 12;
	var principalPlusInterest = preferredMonthlyPayment - monthlyTax - monthlyInsurance;
	var monthlyInterest = interestRateVal / 1200;
	var base = 1;
	var mbase = 1 + monthlyInterest;
	for (var i=0;i<years * 12 - 1;i++) {
		base = base * mbase;
	}
	var loanAmountVal = Math.floor(principalPlusInterest / (monthlyInterest / (1 - (1 / base))));
	// More guesswork. This down payment is approximate for a conventional 5%.
	var downPaymentVal = preferredMonthlyPayment * 10;
	var listPriceVal = loanAmountVal + downPaymentVal;
	return listPriceVal;
}

$(function() {
	var $container = $(".budget-search-widget");
	var city = $container.data('city');
	$container.find(".search").on("click", function() {
		var $pfp = $container.find(".pfp");
		var pfp = clean($pfp.val());
		
		var listPrice = reverse_calculate(pfp);
		var low = Math.round(listPrice * .9);
		var high = Math.round(listPrice * 1.1);

		var urlBase = "http://" + $container.data('host');
		var forwardSubdir = $container.data('forward-subdir');

		var searchUrl = urlBase + (forwardSubdir.length > 0 ? "/" + forwardSubdir : "") + "/search-results/?city=" + city + "&pricerange=" + low + "%2C" + high;
		//console.log(searchUrl);
		window.top.location.href = searchUrl;
	});
});