<?php

defined('ABSPATH') or die('You do not have access!');

// This is another class related to bulk campaigns. Its name is HomeownerCampaigns... for the sake of consistency with
// other, older files, because our original name for the concept was homeowner campaigns. We have since started calling
// the concept bulk campaigns.
class iFoundHomeownerCampaignsController {
	use UtilTrait;

	public static $endpoint_namespace = 'ifound';
	public static $endpoint_base = '/homeowner-campaigns';

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	public function __construct() {
		add_action('rest_api_init', [$this, 'rest_api_init_hook']);
	}

	public function rest_api_init_hook() {
		$this->register_routes();
	}

	private function register_routes() {
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/(?P<id>[\d]+)/search', array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'search'),
				'permission_callback' => array($this, 'permissions_check'),
			),
		));
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/info', array(
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array($this, 'get_items'),
				'permission_callback' => array($this, 'permissions_check'),
			),
		));
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/publish', array(
			array(
				'methods'             => WP_REST_Server::EDITABLE,
				'callback'            => array($this, 'publish_items'),
				'permission_callback' => array($this, 'permissions_check'),
			),
		));
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/(?P<id>[\d]+)', array(
			array(
				'methods'             => WP_REST_Server::DELETABLE,
				'callback'            => array($this, 'delete_item'),
				'permission_callback' => array($this, 'permissions_check'),
			),
		));
		register_rest_route(static::$endpoint_namespace,static::$endpoint_base . '/delete_many', array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'delete_items'),
				'permission_callback' => array($this, 'permissions_check'),
			),
		));
	}

	public function permissions_check() {
		return apply_filters('ifound_has_feature', 'bulk-campaigns');
	}

	public function get_items(WP_REST_Request $request) {
		$ids = $request->get_param('ids');
		$ids = $this->util()->split_on_comma($ids);
		$post_ids = array_map(function($x) { return intval($x); }, $ids);
		$data = [];
		// Here's the story about first params. If someone used the bulk creator multiple times, each time with
		// different initial params (listing status, days back), then it wouldn't make sense to do a search (for
		// listings) unless it's for the same params.
		$first_params = null;
		foreach ($post_ids as $post_id) {
			$contact_id = get_post_meta($post_id, 'contact_id', true);
			$address_fields = iFoundJointContact::new_hookless()->get_address_fields($contact_id);
			$address = iFoundAddress::new_hookless()->build_address($address_fields);
			$ifound_geo = iFoundJointContact::new_hookless()->maybe_geocode_contact($contact_id);
			$this->util()->maybe_geocode_campaign($post_id);
			$params = get_post_meta($post_id, 'params', true );
			if (!$first_params) {
				$first_params = $params;
			} else {
				if ($params !== $first_params) {
					return new WP_Error('campaigns_params_differ', 'Params differ on at least two campaigns');
				}
			}
			$subject_lat_lng = iFoundGeo::new_hookless()->get_lat_lng($ifound_geo);
			$data[$post_id] = [
				'campaignId' => $post_id,
				'lat' => $subject_lat_lng['lat'],
				'lng' => $subject_lat_lng['lng'],
				'address' => $address,
				'street_name' => iFoundGeo::new_hookless()->get_street_name($ifound_geo),
			];
		}
		return new WP_REST_Response($data, 200);
	}

	public function search(WP_REST_Request $request) {
		$post_id = intval($request->get_param('id'));
		$polygons_input = $request->get_param('polygons');
		$params = get_post_meta($post_id, 'params', true );
		$polygons = $this->make_ifound_polygons($polygons_input);
		$params['polygons'] = $polygons;
		$results = iFoundIdx::new_hookless()->process_input($params, [
			'stats' => [
				'show' => 'no_stats',
			],
			'trace' => [
				'save_this_id' => $post_id,
			],
		]);
		$data = $results;
		return new WP_REST_Response($data, 200);
	}

	// This is essentially the opposite of make_ifound_polygons().
	private function convert_paths_to_latlngs($polygons) {
		return array_map(function($polygon) {
			$paths = $polygon['paths'];
			$paths = substr($paths, 1);
			$paths = substr($paths, 0, -1);
			$paths = preg_split('#(\), ?\()#', $paths);
			return array_map(function($latlng_string) {
				$vals = explode(', ', $latlng_string);
				$lat = floatval($vals[0]);
				$lng = floatval($vals[1]);
				return [
					'lat' => $lat,
					'lng' => $lng,
				];
			}, $paths);
		}, $polygons);
	}

	// This is essentially the opposite of convert_paths_to_latlngs().
	private function make_ifound_polygons($polygons_input) {
		$polygons_paths = array_map(function($polygon) {
			$coords = array_map(function($coord) {
				return '(' . $coord['lat'] . ', ' . $coord['lng'] . ')';
			}, $polygon);
			return join(',', $coords);
		}, $polygons_input);
		$polygons = array_map(function($path) {
			return [
				'color' => '#1E90FF',
				'paths' => $path,
			];
		}, $polygons_paths);
		return $polygons;
	}

	public function publish_items(WP_REST_Request $request) {
		$is_publish_scheduled = $request->get_param('is_publish_scheduled');
		// The date and time will be in the browser's local time zone. And this is how it is using the search campaign
		// builder too.
		$start_date = $request->get_param('start_date');
		$time_of_day = $request->get_param('time_of_day');
		// The time of day from the browser will be hours and minutes, and we must add seconds to be compatible with our
		// campaign expectations.
		$time_of_day .= ':00';
		$polygons_input = $request->get_param('polygons');
		$polygons = $this->make_ifound_polygons($polygons_input);
		$post_ids = $request->get_param('ids');
		foreach ($post_ids as $post_id) {
			iFoundHomeownerCampaign::new_hookless()->publish($post_id, [
				'is_publish_scheduled' => $is_publish_scheduled,
				'start_date' => $start_date,
				'time_of_day' => $time_of_day,
				'polygons' => $polygons,
			]);
		}
		return new WP_REST_Response(['success' => true], 200);
	}

	public function delete_item(WP_REST_Request $request) {
		$post_id = intval($request->get_param('id'));
		wp_delete_post($post_id);
		return new WP_REST_Response(['success' => true], 200);
	}

	public function delete_items(WP_REST_Request $request) {
		$json = $request->get_json_params();
		$ids = $json['ids'];
		foreach ($ids as $post_id) {
			$result = wp_delete_post($post_id);
			if (!$result) {
				return new WP_Error('could_not_delete', "Could not delete campaign #{$post_id}");
			}
		}
		return new WP_REST_Response(['success' => true], 200);
	}
}
