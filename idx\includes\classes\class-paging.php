<?php
/*******************************************************************
**********       Pinion Media :: Sanitizer Class      **************
**********      Updated 100914 | Version 1.7.2.1      **************
**********               for ProFound IDX             **************
**********    (c) Copyright 2011 Pinion Media, llc    **************
**********           http://pinionmedia.com           **************
********************************************************************/

require_once(dirname(__FILE__) .'/../config.php');
require_once('dbroot.php');

/*
// Make sure the following is used in the db class fuction to set the sql_strg in that object
	
	// Paging stuff
	$this->sql_strg = $sql;
	$sql .= ' LIMIT '.(($page - 1) * $result_max).', '.$result_max.' ';

*/


class paging extends dbroot {

// The following VARs should be coming from in
// $this->page
// $this->sql_strg

	private $url_varval_strg;


function get_paging( $sql_strg, $page, $result_max ){
	
	$this->result_max = $result_max;
	$this->page = $page;
	$this->sql_strg = $sql_strg;

	$this->total_result_count = $this->process('get_count', $this->sql_strg);	
	$this->set_paging_vars();
	
}



function set_paging_vars(){

	$this->page_count = ceil($this->total_result_count / $this->result_max);
	$this->prev_page = (($this->page - 1) <= 0)? 1 : ($this->page - 1);
	$this->next_page = (($this->page + 1) >= $this->page_count)? $this->page_count : $this->page + 1;
	
	$this->result_start = (($this->page - 1) * $this->result_max);
	$this->result_stop = ($this->page_count != $this->page)? ($this->result_start + $this->result_max) : $this->total_result_count;
	$this->status_strg = ($this->result_start + 1).' - '.$this->result_stop.' of '.number_format($this->total_result_count);
	
}



function set_url_vars($url_strg, $url_vars_array=''){

	$this->url_strg = $url_strg;
		
	if(is_array($url_vars_array)){
		$this->url_varval_strg = '';
		foreach($url_vars_array AS $id=>$result){
			$this->url_varval_strg .= "&$id=$result";
		}
	}
	
}


# FIXME: this function has way too much code duplication.  refactor it.  or better yet, find a 
#   library off the internet that does this already.
function get_paging_strg(){
	
	$this->paging_strg = '';

	if($this->page_count > 1){
		
		$pagerange_low = (($this->page - 6) <= 0)? 0 : ($this->page - 6);
		$pagerange_high = (($this->page + 6) >= $this->page_count)? $this->page_count : ($this->page + 6);
	
		#$this->paging_strg .= ($this->page > 1 )? '<a href="'.$this->url_strg.'?pp='.$this->prev_page.$this->url_varval_strg.'">&laquo; Previous</a>' : '&laquo; Previous '; 
		$this->paging_strg .= ($this->page > 1 )? '<a href="' . 
			sprintf($this->url_strg, $this->prev_page) . $this->url_varval_strg.'">&laquo; Previous</a>' : '&laquo; Previous '; 
		$this->paging_strg .= ' [ ';
		$this->paging_strg .= (($this->page_count > 20) && ($pagerange_low > 0))? ' ... ' : '';
		for($i=1; $i<=$this->page_count; $i++){ 
			if($this->page_count > 20 && ($i > $pagerange_low && $i < $pagerange_high)){
				$this->paging_strg .= ($i == $this->page)? '<strong>'.$i.'</strong> ' : '<a href="' . 
					sprintf($this->url_strg, $i) . $this->url_varval_strg.'"><strong>'.$i.'</strong></a> '; 
			}elseif($this->page_count < 20){
				$this->paging_strg .= ($i == $this->page)? '<strong>'.$i.'</strong> ' : '<a href="' .
					sprintf($this->url_strg, $i) . $this->url_varval_strg.'"><strong>'.$i.'</strong></a> '; 
			}
		} 
		$this->paging_strg .= (($this->page_count > 20) && ($pagerange_high < $this->page_count))? ' ... ' : '';
		$this->paging_strg .= ' ] ';
		$this->paging_strg .= ($this->page < $this->page_count)? '<a href="' . 
			sprintf($this->url_strg, $this->next_page) . $this->url_varval_strg.'">Next &raquo;</a>' : ' Next &raquo;';
		
	}
	
	return $this->paging_strg;
	
}



function get_status_strg(){

	if($this->page_count > 1){
		print 'Displaying ';
		print $this->status_strg;
    	print ' results';
	}
 
}

// We output some raw data, which can be used on the client side as it's seen fit
public function get_parseable() {
	return array(
		'current_page' => $this->page,
		'total_items' => $this->total_result_count,
		'items_per_page' => $this->result_max,
	);
}

} // End paging class

?>