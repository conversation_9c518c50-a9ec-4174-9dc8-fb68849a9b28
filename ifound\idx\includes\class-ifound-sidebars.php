<?
/**
 * iFoundSidebars class
 *
 * Display widget areas and widgets in varies locations.
 *
 * @since 1.0.8
 */

defined( 'ABSPATH' ) or die( 'You do not have access!' );

/**
 * iFoundSidebars Class
 *
 * @since 1.0.0
 */
 
class iFoundSidebars {
	
	/**
	 * init iFound_sidebars class.
	 *
	 * @since 1.0.0
	 */
	 
	public static function init() {
		$class = __CLASS__;
		new $class;
	}
	
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	 
	public function __construct() {
		
		$this->widgets();
		$this->sidebars();

		add_action( 'wp_footer', array( $this, 'mobile_phone_me_now' ) );
		
	}
	
	/**
	 *  Widgets
	 *
	 * Let's get our widgets registered.
	 *
	 * @since 1.0.0
	 * @since 2.4.5 Add call me now widgets.
	 *
	 * @link https://codex.wordpress.org/Function_Reference/register_widget
	 */
	
	public function widgets() {
		
		$widgets = array(
			'iFound_quick_search',
			'iFound_search_nearby',
			'iFound_featured_listings',
			'iFound_text_me_now',
			'iFound_call_me_now',
			'iFound_whats_my_payment',
			'iFound_save_this_property',
			'iFound_save_this_search',
			'iFound_social_media',
			'iFound_Broker_Logo',
			'iFound_Property_Video'
		);
		
		foreach( $widgets as $widget ) {
			if( class_exists( $widget ) ) {
				register_widget( $widget );
			} 
		}
	
	}
	
	/**
	 *  Sidebars
	 *
	 * Register widget sidebars.
	 *
	 * @since 1.0.0
	 * @since 2.4.5 Add phone me now sidebar.
	 *
	 * @link https://codex.wordpress.org/Function_Reference/register_sidebar
	 */
	
	public function sidebars() {
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Details - Before Slider', 'ifound' ),
        	'id' 			=> 'details-before-slider',
        	'description' 	=> __( 'Display content on IDX property detail pages before the slider.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="pdp-before-slider %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Details - After Slider', 'ifound' ),
        	'id' 			=> 'details-after-slider',
        	'description' 	=> __( 'Display content on IDX property detail pages after the slider.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="pdp-after-slider %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Details - After Details', 'ifound' ),
        	'id' 			=> 'after-details',
        	'description' 	=> __( 'Display content on IDX property detail pages after the details.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="pdp-after-details %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Results - Before Filters', 'ifound' ),
        	'id' 			=> 'search-results-before-criteria',
        	'description' 	=> __( 'Display content on IDX search results pages before the filters.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="search-results-before-criteria %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Results - After Filters', 'ifound' ),
        	'id' 			=> 'search-results-after-criteria',
        	'description' 	=> __( 'Display content on IDX search results pages after the filters.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="search-results-after-criteria %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Results - After Map', 'ifound' ),
        	'id' 			=> 'search-results-after-map',
        	'description' 	=> __( 'Display content on IDX search results pages after the map.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="search-results-after-map %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );
		
		register_sidebar( array(
        	'name' 			=> __( 'iFound Results - After Results', 'ifound' ),
        	'id' 			=> 'search-results-after-results',
        	'description' 	=> __( 'Display content on IDX search results pages after the search results.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="search-results-after-results %2$s"><div class="ifound-wrap">',
			'after_widget'  => '</div></div>'
    	) );

    	register_sidebar( array(
        	'name' 			=> __( 'iFound Mobile - Phone Me Now', 'ifound' ),
        	'id' 			=> 'mobile-phone-me-now',
        	'description' 	=> __( 'Display mobile phone buttons in the footer on mobile devices.', 'ifound' ),
        	'before_widget' => '<div id="%1$s" class="mobile-phone-me-now %2$s">',
			'after_widget'  => '</div>'
    	) );
		
	}

	/**
	 * Mobile Phone Me Now
	 *
	 * Mobile widget area for call me now and text me now widgets.
	 *
	 * @since 2.4.5
	 */

	public function mobile_phone_me_now() {

		if ( wp_is_mobile() && is_active_sidebar( 'mobile-phone-me-now' ) ) : 

			global $_wp_sidebars_widgets; 

			$widgets = $_wp_sidebars_widgets; 

			if ( isset( $widgets['mobile-phone-me-now'] ) ) :

				$widget_count = count( $widgets['mobile-phone-me-now'] ); 

				$class = $widget_count > 1 ? 'phone-halves' : ''; ?>

				<div class="mobile-phone-me-now-wrapper <? echo $class; ?>">

					<div class="ifound-wrap"><?

						dynamic_sidebar( 'mobile-phone-me-now' ); ?>

					</div>

				</div><?

			endif;

	 	endif;

	}
	
}

foreach ( glob( plugin_dir_path( __FILE__ ) . 'widgets/*.php' ) as $file ) {
    require_once $file;
}
