<?
defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

/**
 * iFound Social Media Admin Class
 *
 * @since 1.0.29
 */
 
class iFoundSocialMediaAdmin {
	
	/**
	 * init iFound Social Media Admin Class.
	 *
	 * @since 1.0.29
	 */
	 
	public static function init() {
        $class = __CLASS__;
        new $class;
    }
	
	/**
	 * Constructor
	 *
	 * @since 1.0.29
	 */
	 
	public function __construct() {
		 
		add_action( 'admin_menu', array( $this, 'iFound_add_pages' ) );
		 
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
		
		add_action( 'wp_ajax_sociaL_media_save', array( $this, 'sociaL_media_save' ) );
		
	}
	
	
	/**
	 * Enqueue admin scripts and styles.
	 *
	 * @since 1.0.29
	 */
	
	public function enqueue_admin_scripts() {
		
		$this->options = (object) get_option( 'ifound_sociaL_media_networks' );

		wp_register_style( 'social_media_admin_css', plugins_url( 'css/social-media.css', __FILE__ ), array(), iFOUND_PLUGIN_VERSION );
		
		wp_register_script( 'social_media_admin_js', plugins_url( 'js/social-media.js', __FILE__ ) , array ( 'jquery', 'jquery-ui-core', 'jquery-ui-sortable' ), iFOUND_PLUGIN_VERSION, true );
		wp_localize_script( 'social_media_admin_js', 'social_media_admin', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'social_media_admin_secure_me' ),
			'networks'		=> $this->options->networks ?: array(),
			'button_type'	=> $this->options->button_type ?: false
		) );
	}
	
	/**
	 *  iFound admin page 
	 *
	 * 	Add admin page tab to main menu
	 *
	 * 	@since 1.0.29
	 */
	 
	public function iFound_add_pages() {
		
		add_submenu_page(
        	'ifound_agent',
        	__( 'Social Media', 'ifound' ),
			__( 'Social Media', 'ifound' ),
        	'manage_options',
			'ifound-social-media',
        	array( $this, 'social_media_page' ) 
		);
		
	}
	
	/**
	 *  Social Media Page
	 *
	 * 	@since 1.0.29
	 */
	 
	public function social_media_page() {
		
		/** must check that the user has the required capability */
		if ( ! current_user_can( 'manage_options' ) ) {
		  	wp_die( __( 'You do not have sufficient permissions to access this page.') );
		} 

		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'jquery-ui-sortable' );
		wp_enqueue_style( 'social_media_css' );
		wp_enqueue_style( 'social_media_admin_css' );
		wp_enqueue_script( 'social_media_admin_js' );  

		$class = isset( $this->options->button_type ) ? $this->options->button_type : 'radius'; ?>
		
		<div id="social_media_admin" class="ifound-social-<? echo $class; ?>">
		
			<h1 class="ifound-admin-h1"><? _e( 'Social Media Settings', 'ifound' ) ?></h1><?

			do_action( 'ifound_help_button', 'social_media' ); ?>

			<div class="ifound-wrap">
			
				<div id="poststuff">
				
					<div class="metabox-holder quick-edit-row-page">
				
						<div class="postbox-container inline-edit-col-right">
						
							<form method="post" id="social_networks_form">
			
								<div class="meta-box-sortables ui-sortable">
					
									<div class="postbox">
						
										<h2 class="hndle ui-sortable-handle"><? _e( 'Profiles', 'ifound' ); ?></h2>

										<div class="inside">

											<? $this->button_type_select(); ?>

										</div>
						
										<div class="inside">
				
											<? $this->social_media_icons(); ?>
											
											
										</div>
										
										<div class="inside">
										
											<div class="social-link-wrapper">
				
												<div id="active-social-profiles" class="ifound-wrap">
												
													<!-- Social Links are inserted here. -->
					
												</div>
					
											</div>
											
										</div>
											
									</div>
									
								</div>
								
							</form>
					
						</div>
						
					</div>
					
				</div>
				
				<div class="button-wrapper">
				
					<div class="ifound-wrap">
					
						<div id="sodial-media-save" class="save-changes button button-primary">
							<i class="fal fa-plus-square" id="save-spinner" aria-hidden="true"></i>
							<? _e( 'Save Changes', 'ifound' ) ?>
						</div>
						
					</div>
					
				</div>	

			</div>
			
		</div><?

	}
	
	/**
	 *  Social Media Icons
	 *
	 * 	@since 1.0.29 
	 */
	
	public function social_media_icons() { ?>
						
		<div class="all-choices-wrapper">
					
			<div class="ifound-wrap">
						
				<div class="click-msg"><? _e( 'Click icons to add social media networks.', 'ifound' ); ?></div><?

				foreach( $this->social_media_networks() as $key => $value ) { ?>

					<div class="social-network-icon-wrapper">	
					
						<div class="ifound-wrap">
									
							<i class="fab fa-<? echo $value; ?> social-network-icon" title="<? echo $key; ?>" id="<? echo $key; ?>" aria-hidden="true"></i>
					
						</div>
							
					</div><? 

				} ?>
				
			</div>
					
		</div><?
		
	}

	public function button_type_select() { ?>

		<div class="button-type-select-wrapper">

			<div class="ifound-wrap">

				<label for="button-type-select"><? _e( 'Button Style', 'ifound' ); ?></label>
		
				<select name="button_type" id="button-type-select">
					<option value="radius"><? _e( 'Radius', 'ifound' ); ?></option>
					<option value="floating"><? _e( 'Floating', 'ifound' ); ?></option>
					<option value="square"><? _e( 'Square', 'ifound' ); ?></option>
					<option value="round"><? _e( 'Round', 'ifound' ); ?></option>
					<option value="oval"><? _e( 'Oval', 'ifound' ); ?></option>
				</select>

			</div>

		</div><?
	}

	/**
	 *  Social Media Networks
	 *
	 * 	@since 1.1.1
	 */
	
	public function social_media_networks() {

		// Reminder: the value here is a CSS class name
		return (object) array(
			'facebook' 		=> 'facebook-f',
			'twitter' 		=> 'twitter',
			'googleplus'	=> 'google-plus-g',
			'linkedin' 		=> 'linkedin-in',
			'pinterest' 	=> 'pinterest-p',
			'youtube' 		=> 'youtube',
			'vimeo' 		=> 'vimeo',
			'instagram' 	=> 'instagram',
			'tumblr'		=> 'tumblr',
			'tiktok'        => 'tiktok',
		);

	}
	
	/**
	 *  Social Media Save
	 *
	 * 	@since 1.0.29 
	 */
	
	public function sociaL_media_save() {
		
		check_ajax_referer( 'social_media_admin_secure_me', 'social_media_admin_nonce' );
		
		$protocols = array( 'http', 'https' );
		
		$choices = explode( '&', $_REQUEST['input'] );
		
		$i = 1;
		foreach( $choices as $choice ) {
			
			list( $key, $value ) = explode( '=', $choice );

			if( $key == 'button_type' )
				
				$networks['button_type'] = sanitize_title( $value );
			
			else {

				$clean_url = esc_url( urldecode( $value ), $protocols );
			
				if( ! empty( $clean_url ) ) {
				
					$networks['networks'][$i]['url'] 	= $clean_url;
					$networks['networks'][$i]['name'] 	= sanitize_key( $key );
					$networks['networks'][$i]['class']	= $this->social_media_networks()->$key; 
					
				}

				$i++;

			}
			
		}
		
		$networks = ! empty( $networks ) ? $networks : array();

		update_option( 'ifound_sociaL_media_networks', $networks );
			
		$response = 'fa-plus-square';
		
		echo json_encode( $response );
			
		die();

	}
	
}
