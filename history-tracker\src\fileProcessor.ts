import { promises as fs } from 'fs'
import path from 'path'
import { Property } from './database.js'

export interface ProcessingResult {
	filename: string
	records: Property[]
	processed: boolean
	error?: string
}

export async function getJsonFiles(dataDir: string): Promise<string[]> {
	try {
		const files = await fs.readdir(dataDir)
		const jsonFiles = files.filter(file => file.endsWith('.json')).sort() // Alphabetical sort for chronological processing

		return jsonFiles.map(file => path.join(dataDir, file))
	} catch (error) {
		if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
			// Data directory doesn't exist, create it
			await fs.mkdir(dataDir, { recursive: true })
			return []
		}
		throw error
	}
}

export async function readJsonFile(filePath: string): Promise<Property[]> {
	try {
		const content = await fs.readFile(filePath, 'utf-8')
		const data = JSON.parse(content)

		if (!data || typeof data !== 'object' || !('value' in data)) {
			throw new Error('JSON file must contain an object with a "value" property')
		}

		if (!Array.isArray(data.value)) {
			throw new Error('The "value" property must be an array of listing records')
		}

		// Validate and convert records to Property objects
		const properties: Property[] = []
		for (let i = 0; i < data.value.length; i++) {
			const record = data.value[i]
			
			if (!record || typeof record !== 'object') {
				throw new Error(`Record at index ${i} must be an object`)
			}

			// Validate required fields
			if (!record.ListingId || typeof record.ListingId !== 'string') {
				throw new Error(`Record at index ${i} must have a valid ListingId string`)
			}

			if (!record.StandardStatus || typeof record.StandardStatus !== 'string') {
				throw new Error(`Record at index ${i} must have a valid StandardStatus string`)
			}

			// Determine price based on StandardStatus and skip record if price is invalid
			let price: number
			if (record.StandardStatus.toLowerCase() === 'closed') {
				// For closed listings, use ClosePrice
				if (typeof record.ClosePrice !== 'number' || record.ClosePrice === null || record.ClosePrice <= 0) {
					// Skip this record if ClosePrice is missing, null, zero, or negative
					continue
				}
				price = record.ClosePrice
			} else {
				// For all other statuses, use ListPrice
				if (typeof record.ListPrice !== 'number' || record.ListPrice === null || record.ListPrice <= 0) {
					// Skip this record if ListPrice is missing, null, zero, or negative
					continue
				}
				price = record.ListPrice
			}

			// Convert ModificationTimestamp to Date if it's a string
			let modificationTimestamp: Date
			if (record.ModificationTimestamp instanceof Date) {
				modificationTimestamp = record.ModificationTimestamp
			} else if (typeof record.ModificationTimestamp === 'string') {
				modificationTimestamp = new Date(record.ModificationTimestamp)
				if (isNaN(modificationTimestamp.getTime())) {
					throw new Error(`Record at index ${i} has invalid ModificationTimestamp format`)
				}
			} else {
				throw new Error(`Record at index ${i} must have a valid ModificationTimestamp`)
			}

			properties.push({
				ListingId: record.ListingId,
				StandardStatus: record.StandardStatus,
				Price: price,
				ModificationTimestamp: modificationTimestamp
			})
		}

		return properties
	} catch (error) {
		if (error instanceof SyntaxError) {
			throw new Error(`Invalid JSON format: ${error.message}`)
		}
		throw error
	}
}

export async function deleteJsonFile(filePath: string): Promise<void> {
	try {
		await fs.unlink(filePath)
	} catch (error) {
		if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
			throw error
		}
		// File doesn't exist, that's fine
	}
}

export async function processJsonFiles(
	dataDir: string,
	processRecords: (records: Property[]) => Promise<void>
): Promise<ProcessingResult[]> {
	const results: ProcessingResult[] = []
	const jsonFiles = await getJsonFiles(dataDir)

	for (const filePath of jsonFiles) {
		const filename = path.basename(filePath)
		const result: ProcessingResult = {
			filename,
			records: [],
			processed: false,
		}

		try {
			const records = await readJsonFile(filePath)
			result.records = records

			if (records.length > 0) {
				await processRecords(records)
			}

			await deleteJsonFile(filePath)
			result.processed = true
		} catch (error) {
			// Throw immediately upon first error instead of continuing
			const errorMessage = error instanceof Error ? error.message : 'Unknown error'
			throw new Error(`Failed to process file ${filename}: ${errorMessage}`)
		}

		results.push(result)
	}

	return results
}
