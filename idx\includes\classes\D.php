<?php

use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Profound\Config;

class D {
	private static $logger;

	// Dump to error log
	public static function elog($var, $visual = "*") {
		$visual_multiple = "";
		if ($visual) {
			$visual_multiple = str_repeat($visual, 30);
		}
		$modified_var = $var;
		if (is_bool($var)) {
			$modified_var = $var ? "true" : "false";
		}
		$output_string = "$visual_multiple " . print_r($modified_var, true);
		error_log($output_string . "\n");
	}

	protected static function getLogger() {
		if (!self::$logger) {
			$log = new Logger('idx');
			$logPath = Config::getDebugLogPath();
			$streamHandler = new StreamHandler($logPath, Logger::DEBUG);
			// Don't show "[] []" at the end of each line.
			// See: http://stackoverflow.com/a/13971250/135101
			// Allow in-line line breaks.
			// See: https://github.com/Seldaek/monolog/issues/366
			$formatter = new LineFormatter(null, null, true, true);
			$streamHandler->setFormatter($formatter);
			$log->pushHandler($streamHandler);
			self::$logger = $log;
		}
		return self::$logger;
	}

	// By default, there is some retardedness here. The value false is blank as a string.
	// Arrays, even if not empty, will print as "Array()". So we do some basic formatting.
	protected static function basicFormat($var) {
		$modifiedVar = $var;
		if (is_bool($var)) {
			$modifiedVar = $var ? "true" : "false";
		}
		$outputString = print_r($modifiedVar, true);
		return $outputString;
	}

	protected static function shouldLog() {
		return !!Config::getDebugLogPath();
	}

	public static function dlog($var) {
		if (!self::shouldLog()) {
			return;
		}

		$outputString = self::basicFormat($var);
		$log = self::getLogger();
		$log->addDebug($outputString);
	}

	// This is a convenience method, to help us not log for every request but
	// to limit the logging to a single domain or url match. To get the URL,
	// we assume there is a $_REQUEST['url'] value, which there will be for
	// IDX queries, or you can optionally supply a URL as the 3rd argument.
	public static function dlogForUrl($var, $urlRegex, $url = null) {
		$url = $url ?: (@$_REQUEST['p_url'] ?: '');
		if (preg_match($urlRegex, $url)) {
			self::dlog($var);
		}
	}
}
