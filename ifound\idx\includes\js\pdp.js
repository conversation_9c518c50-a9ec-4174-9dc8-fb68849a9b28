jQuery(document).ready(function($){
	var $error = $('.compensation_more_info_error');
	var $spinner = $('.compensation_more_info_container i.fa-spinner');
	$('.compensation_more_info_container a').on('click', function() {
		var $this = $(this);
		$error.hide();
		$spinner.show();
		jQuery.ajax ( {
			url : ifound_pdp.endpoint,
			type : 'POST',
			data : {
				action : 'pdp_compensation',
				mls_id: $(this).data('mls_id'),
			},
			beforeSend: function() {
				$( '#save-this-spinner' ).removeClass( 'fa-plus-square fa-exclamation-triangle' ).addClass( 'fa-spinner fa-spin' );
			},
			success: function( response ) {
				$this.replaceWith(response.text);
			},
			error: function(response) {
				$error.html(response.responseText);
				$error.show();
			},
			complete: function() {
				$spinner.hide();
			},
			dataType:'json'
		});
	});
});

