import React from 'react';
import PropTypes from 'prop-types';
import axios from 'axios';
import classnames from 'classnames';

function CampaignStatusToggle(props) {
	const [isActive, setIsActive] = React.useState(props.initialEnabledStatus);

	console.log('props.initialEnabledStatus', props.initialEnabledStatus)
	const statusText = isActive
		? props.initialEnabledStatus
			? 'Enabled'
			: 'Enabled (will immediately send campaign upon clicking Update Campaign)'
		: props.isPropertyAlert
			? 'Disabled'
			: props.initialEnabledStatus
				? 'Disabled'
				: 'Disabled (enabling will immediately send campaign upon clicking Update Campaign)'
	;
	const iconClassNames = isActive ? 'fa-toggle-on' : 'fa-toggle-off';

	return <div>
		<div style={{ display: 'flex', alignItems: 'center' }}>
			<div>
				<label onClick={() => setIsActive(!isActive)}>
					<input type="hidden" name="campaign_status" value={isActive ? 'active' : 'inactive'} />
					<i className={classnames('fa fa-2x update-campaign', iconClassNames)}/>
				</label>
			</div>
			<div style={{ marginLeft: '0.5rem' }}>
				{statusText}
			</div>
		</div>
	</div>
}

CampaignStatusToggle.propTypes = {
	isPropertyAlert: PropTypes.bool.isRequired,
	initialEnabledStatus: PropTypes.bool.isRequired,
};

export default CampaignStatusToggle;
