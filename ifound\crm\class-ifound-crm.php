<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

require_once(__DIR__ . '/../traits/NewHooklessTrait.php');

/**
 * CRM Class
 *
 * @since 3.0.0
 */

class iFoundCrm extends iFoundIdx {
	use UtilTrait;
	use NewHooklessTrait;

	private static $sms_consent_given_option_name = 'sms_opt_in_consent_given_datetime';
	private static $sms_consent_revoked_option_name = 'sms_opt_in_consent_revoked_datetime';
	public static $crm_settings_menu_slug = 'crm-settings';
	public static $crm_settings_option_name = 'ifound_crm_settings';
	public static $exclude_tag_name = 'ifa-exclude';
	public static $from_email_domain = 'mail.ifoundagent.com';

	public static $pretty_date_format = null;
	public static $pretty_datetime_format = null;
	public static $pretty_datetime_format_with_seconds = null;

	public static $AUTO_START_SEARCH_CAMPAIGN_KEY = 'auto_start_search_campaign';
	public static $AUTO_START_DRIP_CAMPAIGN_KEY = 'auto_start_drip_campaign';
	public static $intro_title_template = "You're now receiving text messages from %s at %s. Type STOP to unsubscribe";
	public static $SMS_INTRO_TITLE_KEY = 'sms_intro_title';
	public static $SMS_FEATURE_KEY = 'sms';

	public $cookie = 'ifound-registered';
	protected $agent;

	public static function static_init() {
		static::$pretty_date_format = 'M j, Y';
		static::$pretty_datetime_format = static::$pretty_date_format . ', g:i a';
		static::$pretty_datetime_format_with_seconds = static::$pretty_date_format . ', g:i:s a';
	}

	/**
	 * init iFOUND_crm class.
	 *
	 * @since 3.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 3.0.0
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('admin_init', array($this, 'register_crm_settings'));
			add_action('admin_init', array($this, 'register_crm_integrations'));
			add_action('ifound_footer_links', array($this, 'crm_footer_links'));
			add_action('admin_menu', array($this, 'settings_menu'), 5);
			add_filter('hidden_meta_boxes', array($this, 'remove_plugin_metaboxes'), 10, 3);

			add_filter('pretty_date', array($this, 'pretty_date'), 10, 2);
			add_filter('pretty_date_only', array($this, 'pretty_date_only'), 10, 2);
			// Don't add this filter if the teams feature is enabled, because if so, it will swallow the original value
			// passed to it, not allowing following filters to see the original value passed.
			if (!apply_filters('ifound_has_feature', 'teams')) {
				add_filter('ifound_crm_id', array($this, 'crm_id_filter'), 5, 1);
			}
			add_filter('ifound_crm_menu', array($this, 'crm_menu'));
			add_action('updated_option', [$this, 'updated_option_hook'], 10, 3);
		}

	}

	/**
	 * CRM ID
	 *
	 * The author ID of the current user. Blank if a site admin.
	 *
	 * @since 3.0.0
	 *
	 * @return int $crm_id The author ID of the current CRM user.
	 */

	public function crm_id($options = []) {
		if ($this->is_site_admin()) {
			return '';
		}
		$crm_id = apply_filters( 'ifound_crm_id', $options );
		return $crm_id;
	}



	// I'm leaving these 3 functions here for posterity. All of the places we were using these for were wrong; we should
	// have instead been using something like iFoundAdmin::get_this_user_ids_or_primary_admin_ids(). So if it's been a
	// while and we still have no need for these, just delete them.

	// This method differs from crm_id_and_shared_id in that it returns a single value, which is the shared owner ID if
	// the user is an admin. Otherwise it will get the current user's ID or look up a user ID based on contact ID, etc.
	// public function crm_id_or_shared_id($options = []) {
	// 	return $this->crm_id($options) ?: iFoundAdmin::$shared_owner_ID;
	// }

	// This method differs from crm_id_or_shared_id in that it returns an array. If the user is an admin, it returns an
	// array of just the shared owner ID. Otherwise the array also includes the current user's ID.
	// We decided to make admins share a common set of values. The way we do that is when they are created, they are
	// set to be owned by the "shared owner ID", or 1. The problem is that legacy data would have its owner set as the
	// admin's ID. So, for admins, when reading back existing data, we should look up what's owned by the admin's ID or
	// the shared owner ID. This doesn't affect non-admins.
	// public function crm_id_and_shared_id($options = []) {
	// 	$crm_id = $this->crm_id_or_shared_id($options);
	// 	if ($crm_id === iFoundAdmin::$shared_owner_ID) {
	// 		$current_user_id = get_current_user_id();
	// 		// I'm not sure if this would ever be called when there is no current user. Maybe during cron? Let's be on
	// 		// the safe side and not incldue the current user ID if there is none.
	// 		if ($current_user_id && $current_user_id !== iFoundAdmin::$shared_owner_ID) {
	// 			return [$crm_id, $current_user_id];
	// 		}
	// 	}
	// 	return [$crm_id];
	// }
	//
	// public function crm_id_and_shared_id_string($options = []) {
	// 	return join(', ', $this->crm_id_and_shared_id($options));
	// }



	// Ensure we always return a string, to prevent === from returning false on int to string comparisons.
	public function crm_id_from_user_id($user_id) {
		if ($this->util()->user_has_admin_or_super_role($user_id)) {
			return '';
		}
		return (string) $user_id;
	}

	/**
	 * CRM
	 *
	 * The CRM settings for the current user.
	 *
	 * @since 3.0.0
	 *
	 * @param  int    $crm_id The author ID of the current user.
	 * @return object $crm    An object of crm settings.
	 */

	public function crm( $crm_id = false ) {
		// If an argument was actually passed, use it. Otherwise, go look up the CRM ID.
		$crm_id = count(func_get_args()) ? $crm_id : $this->crm_id();
		return (object) get_option( 'ifound_crm_settings' . $crm_id );
	}

	public function crm_from_user_id($user_id) {
		$crm_id = $this->crm_id_from_user_id($user_id);
		return $this->crm($crm_id);
	}

	private function make_image($url, $max_width_px) {
		if (!$url) {
			return '';
		}
		return "<img src=\"$url\" style=\"max-width:{$max_width_px}px;\">";
	}

	/**
	 * Agent
	 *
	 * The iFound agent settings for the current user.
	 *
	 * @since 3.0.0
	 *
	 * @param  int    $crm_id The author ID of the current user.
	 * @return object $agent  An object of crm settings.
	 */

	public function agent( $crm_id = false ) {
		$crm_id = (isset($crm_id) && !is_array($crm_id)) ? $crm_id : $this->crm_id($crm_id);
		if ($crm_id) {
			$user = get_userdata($crm_id);
			$usermeta = $this->util()->get_single_metas($crm_id, [
				'ifa-agent-broker-name',
				'ifa-agent-street-address',
				'ifa-agent-city',
				'ifa-agent-state',
				'ifa-agent-zip',
				'ifa-agent-office-phone',
				'ifa-agent-mobile-phone',
				'ifa-agent-fax',
				'ifa-user-headshot',
				'ifa-signature-graphic',
			], 'user');

			$broker_logo = iFound_Broker_Logo::get_broker_logo([
				'ensure_href_is_url' => true,
				'agent_id' => $crm_id,
			]);

			$agent = (object) [
				'agent_name'        => $user->display_name,
				'broker_name'       => $usermeta['ifa-agent-broker-name'],
				'street_address'    => $usermeta['ifa-agent-street-address'],
				'city'              => $usermeta['ifa-agent-city'],
				'state'             => $usermeta['ifa-agent-state'],
				'zip'               => $usermeta['ifa-agent-zip'],
				'office_phone'      => $usermeta['ifa-agent-office-phone'],
				'mobile_phone'      => $usermeta['ifa-agent-mobile-phone'],
				'fax'               => $usermeta['ifa-agent-fax'],
				'email'             => $user->user_email,
				'broker_logo'       => $this->make_image($broker_logo, 300),
				'head_shot'         => $this->make_image($usermeta['ifa-user-headshot'], 300),
				'signature_graphic' => $this->make_image($usermeta['ifa-signature-graphic'], 600),
			];
			return $agent;
		}
		$agent = (object) get_option( 'ifound_agent' );
		// We do not allow broker_logo as a merge tag for single users, although it happens to already be a value set in
		// the agent options, so we remove it here.
		unset($agent->broker_logo);
		return $agent;
	}

	/**
	 * CRM Menu
	 *
	 * The admin page link to use for the CRM menu.
	 *
	 * @since 3.0.0
	 *
	 * @return string $crm_menu A string to add to the page url.
	 */

	public function crm_menu($user_id = null) {
		return 'edit.php?post_type=' . iFoundJointContact::new_hookless()->get_new_contact_type($user_id);
	}

	public function get_crm_menu_admin_url($user_id = null) {
		return admin_url($this->crm_menu($user_id) . '&page=' . static::$crm_settings_menu_slug);
	}

	public function is_site_admin() {
		return $this->util()->is_site_admin();
	}

	/**
	 * CRM Settings Capability
	 *
	 * The capability required to edit CRM settings.
	 *
	 * @since 3.0.0
	 *
	 * @return string $crm_settings_capability The name of the capability.
	 */

	public function crm_settings_capability() {
		return 'edit_crm_settings';
	}

	/**
	 * CRM Post Author
	 *
	 * The author ID of the current contact.
	 *
	 * @since 3.0.0
	 *
	 * @param  int $contact_id  The post ID of the contact.
	 * @return int $post_author The author ID of the contact.
	 */

	public function crm_post_author( $contact_id ) {
		return get_post_field ( 'post_author', $contact_id, 'db' );
	}

	/**
	 * CRM ID Filter
	 *
	 * We return a blank if Teams is not in use.
	 *
	 * @since 3.0.0
	 *
	 * @param  int $crm_id An empty var.
	 * @return int $crm_id A blank space.
	 */

	public function crm_id_filter( $crm_id ) {
		return '';
	}

	/**
	 * Get Contact ID
	 *
	 * @since 1.0.28
	 * @since 1.2.34 Return intval of contact_id
	 * @since 2.5.53 Override defined value if GET param isset.
	 * @since 3.1.3  Return contact ID of 0 if no contact exosts.
	 *
	 * @return int $contact_id The defined contact ID. or false in no contact ID.
	 */

	public static function get_contact_id() {
		$contact_id = defined( 'iFOUND_CONTACT_ID' ) ? intval( iFOUND_CONTACT_ID ) : 0;
		return isset( $_GET['contact_id'] ) ? intval( $_GET['contact_id'] ) : $contact_id;
	}

	/**
	 * Settings Menu
	 *
	 * Menu tabs for the CRM.
	 *
	 * @since 3.0.0
	 */

	public function settings_menu() {

		add_submenu_page(
			$this->crm_menu(),
			__( 'Settings', 'ifound' ),
			__( 'Settings', 'ifound' ),
			'edit_crm_settings',
			static::$crm_settings_menu_slug,
			array( $this, 'settings_page' )
		);

		add_submenu_page(
			$this->crm_menu(),
			__( 'CRM Integration', 'ifound' ),
			__( 'CRM Integration', 'ifound' ),
			'edit_crm_settings',
			'crm_integration',
			array( $this, 'crm_integration_page' )
		);

	}

	/**
	 * CRM Settings
	 *
	 * The settings for the CRM.
	 *
	 * @since 3.0.0
	 */

	public function crm_settings() {
		$from_name_hint = <<<EOT
			<ul style="list-style-position: inside; list-style-type: disc;">
				<li>When we send emails on your behalf, this is the "from" name we'll use</li>
				<li>We identify you by name in text messages to contacts</li>
				<li>In templates, this replaces the merge tag <code>{AgentName}</code></li>
			</ul>
		EOT;
		$sms_number_hint = <<<EOT
		Drip Campaign Reminder Texts and Instant Activity Notifications will be sent to this to this number. If you do
		not check the SMS Opt-in checkbox below, no SMS messages will be sent.
		EOT;
		$sms_opt_in_hint = <<<EOT
		By checking this box, you consent to receiving recurring SMS messages to the phone number in the SMS Number
		field. Messages will be sent from ************. Message and data rates apply.
		EOT;
		$enable_website_visit_follow_up_hint = <<<EOT
		If enabled, when a contact visits your site, we'll send them an email 3 hours afterward. We'll only ever send it
		once, and only if the visit is between 8 am and 8 pm. If the visit is outside of these hours, we'll send it at the upcoming
		8 am.
		EOT;
		// We expect this example intro to be replaced on the page by JavaScript. The only reason to bother making it in
		// PHP is to make it less jarring when JS replaces it.
		$example_intro = sprintf(static::$intro_title_template, 'Agent Joe Smith', home_url());
		$intro_title_hint = <<<EOT
		Upon the FIRST time a contact phone number is sent a text, we'll inform them that they'll be receiving texts
		from you, using this title, and we'll mention your website. E.g. if you put your title as "Agent Joe Smith", the
		message might look like "$example_intro"
		EOT;
		if (iFoundEmail::new_hookless()->has_separate_email_service_provider()) {
			$from_email_heading = 'From Email';
			$from_email_hint = <<<EOT
			You have your own Email Service Provider (ESP) configured with the WP Mail SMTP plugin. Emails will be sent
			from this email address.
			EOT;
		} else {
			$from_email_heading = 'Reply-To Email';
			$from_name = $this->crm()->from_name;
			$from_address = iFoundEmail::new_hookless()->make_from_email_address($from_name);
			$from_email_hint = <<<EOT
			You are using iFoundAgent's Email Service Provider (ESP). Emails will be sent from $from_address, and use
			this value as the Reply-To.
			EOT;
		}
		$crm_settings = array(
			'from_name' 		=> array(
				'section'	=> 'What name do you go by?',
				'heading' 	=> '"From" Name',
				'type'		=> 'text',
				'show'		=> true,
				'hint'		=> $from_name_hint,
			),
			'sms_number' => array(
				'section'	=> 'Text messages to agent (you)',
				'heading'	=> 'SMS Number',
				'type'		=> 'text',
				'show'		=> true,
				'hint'		=> $sms_number_hint,
			),
			'sms_opt_in' => array(
				'heading'   => 'SMS Opt-in',
				'type'      => 'checkbox',
				'value'     => 'enabled',
				'show'      => true,
				'hint'		=> $sms_opt_in_hint,
			),
			'from_email'		=> array(
				'section'	=> 'Email Defaults',
				'heading' 	=> $from_email_heading,
				'type'		=> 'text',
				'show'		=> true,
				'hint'		=> $from_email_hint,
			),
			'header'	=> array(
				'section'	=> 'Default Email Templates',
				'heading' 	=> 'Header',
				'type'		=> 'email',
				'category'	=> 'header',
				'include'	=> array( 'header' ),
				'show'		=> true
			),
			'footer' 	=> array(
				'heading' 	=> 'Footer',
				'type'		=> 'email',
				'category'	=> 'footer',
				'include'	=> array( 'footer' ),
				'show'		=> true
			),
			'signature'	=> array(
				'heading' 	=> 'Signature',
				'type'		=> 'email',
				'category'	=> 'signature',
				'include'	=> array( 'signature' ),
				'show'		=> true
			),
			'search_notice' 	=> array(
				'heading' 	=> 'Saved Search Notifcation',
				'type'		=> 'email',
				'category'	=> 'search_notice',
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			'prop_notice' 	=> array(
				'heading' 	=> 'Saved Property Notifcation',
				'type'		=> 'email',
				'category'	=> 'prop_notice',
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			iFoundEmail::$INSTANT_UPDATE 	=> array(
				'heading' 	=> 'Instant Update New Listing Notifcation',
				'type'		=> 'email',
				'category'	=> iFoundEmail::$INSTANT_UPDATE,
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED 	=> array(
				'heading' 	=> 'Instant Update Recently Closed Notifcation',
				'type'		=> 'email',
				'category'	=> iFoundEmail::$INSTANT_UPDATE_RECENTLY_CLOSED,
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			'prop_alert' 	=> array(
				'heading' 	=> 'Property Alert Notifcation',
				'type'		=> 'email',
				'category'	=> 'prop_alert',
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			'homeowner_campaign'=> array(
				'heading' 	=> 'Homeowner Campaign',
				'type'		=> 'email',
				'category'	=> 'homeowner_campaign',
				'include'	=> array( 'content' ),
				'show'		=> true
			),
			iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE => array(
				'heading' 	=> iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_LABEL,
				'type'		=> 'email',
				'category'	=> iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_EMAIL_TYPE,
				'include'	=> array( iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_SLUG ),
				'show'		=> true
			),
			'campaign_post_category_id'	=> array(
				'section'	=> 'Campaign Blog Posts',
				'heading' 	=> 'Campaign Post Category',
				'type'		=> 'post_categories',
				'show'		=> $this->is_site_admin()
			),
			'campaign_post_author'	=> array(
				'heading' 	=> 'Campaign Post Author',
				'type'		=> 'author',
				'show'		=> $this->is_site_admin()
			),
			'time_of_day'		=> array(
				'section'	=> 'Activity Report Email',
				'heading' 	=> 'Time of Day',
				'type'		=> 'time_of_day',
				'show'		=> true,
				'none_message' => 'No Report Scheduled',
			),
			'qty_logs_to_report'	=> array(
				'heading' 	=> 'Qty of Activity Logs to Report',
				'type'		=> 'text',
				'show'		=> true
			),
			iFoundDripCampaign::$cron_upcoming_reminders_email_setting_name	=> array(
				'section'	=> 'Upcoming Task Reminders Email',
				'heading' 	=> 'Time of Day',
				'type'		=> 'time_of_day',
				'show'		=> true,
				'none_message' => "Don't send email",
			),
			'contact_visit_email' 	=> array(
				'section'	=> 'Contact Visit Notifications',
				'heading' 	=> 'Enable Instant Activity Notifications',
				'type'		=> 'checkbox',
				'value'		=> 'enabled',
				'show'		=> true
			),
			'instant_updates_notification' => array(
				'section'       => 'Instant Update Notifications',
				'heading'       => 'Enable instant update admin notifications',
				'type'          => 'checkbox',
				'value'         => 'enabled',
				'show'          => true
			),
			'notification_cc_email_addresses' => array(
				'section'       => 'Add CC email address(es) for the following admin notifications',
				'heading'       => 'CC Email Address(es)',
				'hint'          => 'If multiple, separate by comma',
				'type'          => 'text',
				'show'          => true
			),
			'notification_cc_activity_report' => array(
				'heading'       => 'Activity Report',
				'type'          => 'checkbox',
				'value'         => 'enabled',
				'show'          => true
			),
			'notification_cc_instant_activity' => array(
				'heading'       => 'Instant Activity Notification',
				'type'          => 'checkbox',
				'value'         => 'enabled',
				'show'          => true
			),
			iFoundEmail::$WEBSITE_VISIT_FOLLOW_UP_ENABLED_SETTING_KEY => array(
				'section'       => 'Website Visit Follow-Up emails',
				'heading'       => 'Enable Website Visit Follow-Up emails',
				'type'          => 'checkbox',
				'value'         => 'enabled',
				'show'          => true,
				'hint'          => $enable_website_visit_follow_up_hint,
			),
		);
		if ($this->util()->is_monetization_project() && iFoundTeams::new_hookless()->user_has_team_member_role()) {
			$crm_settings[static::$AUTO_START_SEARCH_CAMPAIGN_KEY] = [
				'section' => 'Leads',
				'heading' => 'Automatically start search campaign for leads',
				'type'    => 'checkbox',
				'value'   => 'enabled',
				'show'    => true,
			];
			$crm_settings[static::$AUTO_START_DRIP_CAMPAIGN_KEY] = [
				'heading' => 'Automatically start drip campaign for leads',
				'type'    => 'checkbox',
				'value'   => 'enabled',
				'show'    => true,
			];
		}
		if (apply_filters('ifound_has_feature', iFoundCrm::$SMS_FEATURE_KEY)) {
			$crm_settings[static::$SMS_INTRO_TITLE_KEY] = [
				'section'	=> 'Text messages to contacts',
				'heading'	=> 'Intro title',
				'type'		=> 'text',
				'show'		=> true,
				'hint'		=> $intro_title_hint,
			];
			$this->util()->move_item($crm_settings, static::$SMS_INTRO_TITLE_KEY, 'down', 'sms_opt_in');
		}


		return $this->obj( $crm_settings );

	}

	/**
	 * Register CRM Settings
	 *
	 * Register the settings for the CRM.
	 *
	 * @since 3.0.0
	 */

	public function register_crm_settings() {

		$args = array(
			'sanitize_callback' => function($value) {
				$val = $this->sanitize($value);
				$config_names_to_check = [
					'from_email',
					'sms_number',
					'notification_cc_email_addresses',
				];
				// If these values are not set, our sanitize function would change it to (float)'', which results in in
				// a decimal 0 going into the final option value. Here we force it back to a (empty) string.
				foreach ($config_names_to_check as $config_name) {
					if ($val[$config_name] === 0.0) {
						$val[$config_name] = '';
					}
				}
				return $val;
			},
		);

		// This filter hook is called by Wordpress to know if settings can be written for the options page. We need to
		// override which capability is checked.
		add_filter( 'option_page_capability_ifound_crm_settings' . $this->crm_id(), array( $this, 'crm_settings_capability' ) );

		register_setting( 'ifound_crm_settings' . $this->crm_id(), 'ifound_crm_settings' . $this->crm_id(), $args );

	}

	// Reminder: this hook doesn't fire if the new value is the same as the old value.
	public function updated_option_hook($option_name, $old_value, $new_value) {
		if ($option_name !== 'ifound_crm_settings' . $this->crm_id()) {
			return;
		}
		// As required by Twilio, we document the consent.
		// https://www.twilio.com/legal/messaging-policy
		// "Prior to sending the first message, you must obtain agreement from the message recipient to communicate with
		// them - this is referred to as "consent", you must make clear to the individual they are agreeing to receive
		// messages of the type you're going to send. You need to keep a record of the consent, such as a copy of the
		// document or form that the message recipient signed, or a timestamp of when the customer completed a sign-up
		// flow."
		if (!isset($old_value['sms_opt_in']) && isset($new_value['sms_opt_in'])) {
			update_option(static::$sms_consent_given_option_name . $this->crm_id(), current_time('mysql', true));
		} else if (isset($old_value['sms_opt_in']) && !isset($new_value['sms_opt_in'])) {
			update_option(static::$sms_consent_revoked_option_name . $this->crm_id(), current_time('mysql', true));
		}
		// To ensure we meet the following Twilio requirement, we immediately send a message.
		// https://www.twilio.com/legal/messaging-policy
		// "If you do not send an initial message to that individual within a reasonable period after receiving consent
		// (or as set forth by local regulations or best practices), then you will need to reconfirm consent in the
		// first message you send to that recipient."
		if (isset($new_value['sms_opt_in'])
			&& $new_value['sms_number']
			&& ($new_value['sms_number'] !== $old_value['sms_number']
				|| !isset($old_value['sms_opt_in'])
			)
		) {
			$body = "Hello from iFoundAgent. This is the number from which we'll send you messages related to your"
				. ' website.';
			iFoundSms::new_hookless()->send($new_value['sms_number'], $body, iFoundSms::$PURPOSE_AGENT_INTRO, [
				'user_id' => get_current_user_id(),
			]);
		}
	}

	private function get_agent_base_url() {
		$site_url = get_option('siteurl');
		if (iFoundTeams::new_hookless()->user_has_team_member_role()) {
			$site_url = iFoundTeams::new_hookless()->get_team_member_url($site_url);
		}
		return $site_url;
	}

/**
 * Settings Page
	 *
	 * The page that displays the CRM settings.
	 *
	 * @since 3.0.0
	 */

	public function settings_page() {
		$maybe_show_hint = function($value) {
			if ($value->hint) {
				?>
				<div style="color:#aaa;font-size:small;max-width:500px;"><?= $value->hint ?></div>
				<?php
			}
		};

		if ( ! current_user_can( 'edit_crm_settings' ) ) return;

		$settings = get_option( 'ifound_crm_settings' . $this->crm_id() );
		$website = $this->get_agent_base_url();
		?>
		<div class="ifound-wrap">

			<h1 class="ifound-admin-h1"><? _e( 'iFound CRM Settings', 'ifound' );?></h1><?

			do_action( 'ifound_help_button', 'crm_settings' ); ?>

			<form method="post" action="options.php">

				<? settings_fields( 'ifound_crm_settings' . $this->crm_id() ); ?>
				<? do_settings_sections( 'ifound_crm_settings' . $this->crm_id() ); ?>

				<table class="form-table contact-table">

					<tbody><?

						foreach( $this->crm_settings() as $setting => $value ) {

							$name = 'ifound_crm_settings' . $this->crm_id() . '[' . $setting . ']';
							$text_width_in_px = 186;
							if (stripos($setting, 'email') !== false || stripos($setting, 'intro_title') !== false) {
								$text_width_in_px = 400;
							}

							if( isset( $value->section ) && $value->show ) { ?>

								<tr><td colspan="2" class="default-criteria-heading"><? _e( $value->section, 'ifound' ); ?></td></tr><?

							} ?>

							<tr><?

								if( $value->show ) { ?>

									<th scope="row"><label><? _e( $value->heading, 'ifound' ); ?></label></th><?

								}

								if( $value->type == 'email' && $value->show ) { ?>

									<td>

										<? do_action( 'ifound_email_dropdown', $value->category, $value->include, $settings[$setting], array(), $name ); ?>

									</td><?

								} elseif( $value->type == 'text' && $value->show ) { ?>

									<td>

										<input type="text" name="<? echo $name; ?>" id="<? echo $setting; ?>" style="width: <?= $text_width_in_px?>px" value="<? echo $settings[$setting]; ?>">
										<? $maybe_show_hint($value); ?>

										<? if (stripos($setting, 'intro_title') !== false) { ?>
										<script>
											jQuery(document).ready(function($) {
												var domContainer = document.querySelector('input#sms_intro_title').parentElement;
												var props = {
													template: "<?= static::$intro_title_template ?>",
													inputTitle: "<?= $settings[$setting] ?? '' ?>",
													website: "<?= $website ?>",
													hiddenInputFieldName: "<?= $name ?>",
												};
												window.ifound_spa.render_app('intro_title', domContainer, props);
											});
										</script>
										<? } ?>

									</td><?

								} elseif( $value->type == 'time_of_day' && $value->show ) { ?>

									<td><?

										do_action( 'ifound_time_of_day_select', $settings[$setting], array( 'none' => $value->none_message ), $name );
										if ($setting === 'time_of_day') {
											do_action( 'ifound_next_activity_report' );
										}
										?>

									</td><?

								} elseif( $value->type == 'post_categories' && $value->show ) { ?>

									<td><?

										$args = array(
											'selected'           => $settings[$setting],
											'hierarchical'       => 1,
											'name'               => $name,
											'hide_empty'         => 0,
											'id'                 => $name,
											'taxonomy'           => 'category',
											'value_field'	     => 'term_id',
										);

										wp_dropdown_categories( $args ); ?>

									</td><?

								} elseif( $value->type == 'author' && $value->show ) { ?>

									<td><?

										$args = array(
											'selected'           => $settings[$setting],
											'name'               => $name,
											'id'                 => $setting,
										);

										wp_dropdown_users( $args ); ?>

									</td><?

								} elseif( $value->type == 'checkbox' && $value->show ) {

									$checked = $settings[$setting] == 'enabled' ? 'checked' : ''; ?>

									<td>

										<input type="checkbox" name="<? echo $name; ?>" value="<? echo $value->value; ?>" <? echo $checked; ?>>
										<? $maybe_show_hint($value); ?>

									</td><?

								} ?>

							</tr><?

						} ?>

					</tbody>

				</table>

				<? submit_button(); ?>

			</form>

		</div><?

	}

	/**
	 * CRM Integrations
	 *
	 * The integrations for the CRM.
	 *
	 * @since 3.0.0
	 *
	 * @return object $crm_integrations An object of settings names and values.
	 */

	public function crm_integrations() {

		$wise_agent_setting_name = iFoundWiseAgent::new_hookless()
			->get_option_name(iFoundWiseAgent::$api_key_option_name);
		$crm_integrations = array(
			// We no longer use these commented out integrations. However, I'm leaving them here for now until we
			// decide it's worth clearing them out completely from our codebase.
			//
			// 'liondesk_api_key' 	=> array(
			// 	'heading' 	=> 'LionDesk API Key'
			// ),
			// 'imax_api_key' 	=> array(
			// 	'heading' 	=> 'iMax CRM API Key'
			// ),
			// 'imax_api_email' 	=> array(
			// 	'heading' 	=> 'iMax CRM API Email'
			// ),

			$wise_agent_setting_name => array(
				'heading'	=> 'WiseAgent API Key'
			),
		);

		return $this->obj( $crm_integrations );

	}

	/**
	 * Redister CRM Integrations
	 *
	 * Register the settings with WP.
	 *
	 * @since 3.0.0
	 */

	public function register_crm_integrations() {
		// This filter hook is called by Wordpress to know if settings can be written for the options page. We need to
		// override which capability is checked.
		add_filter( 'option_page_capability_ifound-crm-integrations' . $this->crm_id(), array( $this, 'crm_settings_capability' ) );

		foreach( $this->crm_integrations() as $setting => $value ) {
			register_setting( 'ifound-crm-integrations' . $this->crm_id(), $setting, 'sanitize_text_field' );
		}
	}

	/**
	 * CRM Integrations Page
	 *
	 * The page to display the CRM integrations.
	 *
	 * @since 3.0.0
	 */

	public function crm_integration_page() {
		if ( ! current_user_can( 'edit_crm_settings' ) ) return; ?>

		<div class="ifound-wrap">

			<h1 class="ifound-admin-h1"><? _e( 'CRM Integration', 'ifound' ); ?></h1><?

			do_action( 'ifound_help_button', 'crm_integration' ); ?>

			<form method="post" action="options.php">

				<? settings_fields( 'ifound-crm-integrations' . $this->crm_id() ); ?>
				<? do_settings_sections( 'ifound-crm-integrations' . $this->crm_id() ); ?>

				<table class="form-table contact-table">

					<tbody><?

						foreach( $this->crm_integrations() as $setting => $value ) { ?>

							<tr>

								<th scope="row"><label><? _e( $value->heading, 'ifound' ); ?></label></th>

								 <td>

									<input type="text" name="<? echo $setting; ?>" id="<? echo $setting; ?>" value="<? echo get_option( $setting ); ?>" class="regular-text">
								</td>

							</tr><?
						} ?>
					</tbody>

				</table>

				<? submit_button(); ?>

			</form>

		</div><?

	}

	/**
	 * Allowed Metaboxes
	 *
	 * The metaboxes we allow to be shown on post types.
	 *
	 * @global string $typenow
	 *
	 * @since 2.4.4
	 * @since 2.5.32 Add campaign_statusdiv
	 *
	 * @return array $allowed An array of allowed metaboxes for our post types.
	 */

	public function allowed_meta_boxes() {

		global $typenow;

		$allowed = array(
			'contacts' => array(
				'submitdiv',
				'contacts_statusdiv',
				'contact_tagdiv',
				'assign_team_members_metabox',
				'contact_meta',
				'activity_log_meta',
				'results_views_meta',
				'detail_views_meta',
				'views_meta',
				'notes_meta',
				'owned_listings_meta',
				'save_this_campaign_meta',
				'save_this_property_meta',
				'save_this_search_meta',
				'private_tour_metabox_contacts',
				'start_campaign_metabox',
				iFoundContacts::$contact_tag_taxonomy . 'div',
			),
			'private_contact' => array(
				'submitdiv',
				'contacts_statusdiv',
				'contact_tagdiv',
				'team_memberdiv',
				'contact_meta',
				'activity_log_meta',
				'results_views_meta',
				'detail_views_meta',
				'views_meta',
				'notes_meta',
				'owned_listings_meta',
				'save_this_campaign_meta',
				'save_this_property_meta',
				'save_this_search_meta',
				'start_campaign_metabox',
				iFoundContacts::$contact_tag_taxonomy . 'div',
			),
			'save_this' => array(
				'submitdiv',
				'contacts_statusdiv',
				'saved_canpaigns_meta',
				'activity_log_meta',
				'save_typediv',
				// Don't allow changing the campaign status manually. Users might accidentally set the status to both
				// Active and Inactive. To change the status, they must use the toggle on the campaign's contact page.
				// 'campaign_statusdiv',
				'assign_team_members_metabox'
			),
			'ifound_email' => array(
				'submitdiv',
				'contacts_statusdiv',
				'ifound_email_typediv'
			),
			'drip_campaign' => array(
				'submitdiv',
				'drip_campaign_meta',
				// Don't allow changing the campaign status manually because it will not create/remove the cron event.
				// To change the status, they must use the toggle on the campaign's contact page.
				// 'campaign_statusdiv',
				'drip_campaign_activity_log_meta',
			),
			'drip_template' => array(
				'submitdiv',
				'drip_template_meta'
			),
			'team_members' => array(
				'submitdiv',
				'team_member_statusdiv',
				'team_member_meta',
				'team_member_leads'
			)
		);

		$allowed = apply_filters( 'ifound_allowed_meta_boxes', $allowed );

		return $allowed[$typenow];

	}

	/**
	 *	Remove Plugin Metaboxes
	 *
	 *	Removes inwanted metaboxes.
	 *
	 *	@since 2.4.4
	 *
	 *  @global array    $wp_meta_boxes
	 *  @global string   $typenow
	 *
	 *  @param array     $hidden       An array of hidden meta boxes.
	 *  @param WP_Screen $screen       WP_Screen object of the current screen.
	 *  @param bool      $use_defaults Whether to show the default meta boxes. Default true.
	 *
	 *  @return array    $hidden       An array of hidden meta boxes.
	 */

	public function remove_plugin_metaboxes( $hidden, $screen, $use_defaults ) {

		global $wp_meta_boxes, $typenow;

		if( array_key_exists( $typenow, $this->types() ) ) {

			if( $typenow === $screen->id && isset( $wp_meta_boxes[$typenow] ) ) {

				$tmp = array();
				foreach( (array) $wp_meta_boxes[$typenow] as $context_key => $context_item ) {

					foreach( $context_item as $priority_key => $priority_item ) {

						foreach( $priority_item as $metabox_key => $metabox_item ) {

							if( ! in_array( $metabox_item['id'], $this->allowed_meta_boxes() ) )

								$tmp[] = $metabox_key;

						}
					}
				}
				$hidden = $tmp;
			}

		}

		return $hidden;

	}

	/**
	 * Types
	 *
	 * @since 2.8.0
	 *
	 * @return array $types An array of post types.
	 */

	public function types() {

		 $types = array(
			 iFoundSaveThis::$the_post_type => 'Campaign',
			 'contacts'                     => 'Contact',
			 'private_contact'              => 'Private Contact',
			 'drip_campaign'                => 'Campaign',
			 'drip_template'                => 'Template',
			 'ifound_email'                 => 'Template',
		);

		$types = apply_filters( 'ifound_types', $types );

		return $types;

	}

	/**
	 * Get Post Id by Key Value
	 *
	 * Check to see if the meta key value pair exists. You can use other post meta keys.
	 *
	 * @since 1.0.0
	 * @since 1.2.48 Change name from contact_exists. We use this for more then contacts. Add the $single param.
	 * @since 6.0.0  Change name from exists. Clarifies what the function is doing. Previous name too vague.
	 * @link https://gist.github.com/feedmeastraycat/3065969
	 *
	 * @param  string $key    The meta key to search with.
	 * @param  string $value  The meta value to search for.
	 * @param  bool   $single False if we want to return the entire meta array. True for a single post ID.
	 * @return mixed  $meta   If single a single post_id. The entire meta array for $single false. Bool false on failure.
	 */

	public function get_post_id_by_key_value( $key, $value, $single = true ) {

		global $wpdb;

		$sql = "
			SELECT *
			FROM ".$wpdb->postmeta."
			WHERE meta_key='" . esc_sql( $key ) . "'
			AND meta_value='" . esc_sql( $value ) . "'
		";
		$meta_array = $wpdb->get_results($sql);

		if ( is_array( $meta_array ) && ! empty( $meta_array ) && isset( $meta_array[0] ) ) {

			if ( $single ) {

				$meta = $meta_array[0];

				return $meta->post_id;

			} else {

				return $meta_array;

			}

		}

		return false;

	}

	/**
	 * Pretty Date
	 *
	 * The date in a defined pretty format.
	 *
	 * @since 3.0.0
	 *
	 * @param  mixed  $input A text timestamp. Or, an int PHP time.
	 * @param  bool   $gmt   True to add the gmt offset. False to not add gmt offset.
	 * @return string $date  The formatted date.
	 */

	public function pretty_date( $input, $gmt = false, $include_seconds = false ) {

		$offset = $gmt ? get_option( 'gmt_offset' ) * 3600 : 0;

		$time = is_int( $input ) ? $input : strtotime( $input );

		$format_string = static::$pretty_datetime_format;
		if ($include_seconds) {
			$format_string = static::$pretty_datetime_format_with_seconds;
		}

		$date = ! $time ? '-' : date( $format_string, $time + ( $offset ) );

		return $date;

	}

	public function pretty_date_only($input) {
		$time = is_int( $input ) ? $input : strtotime( $input );
		$date = !$time ? '-' : date(static::$pretty_date_format, $time);
		return $date;
	}

	/**
	 *	CRM Footer Links
	 *
	 *	Add CRM links to the footer.
	 *
	 *	@since 3.3.3
	 */

	public function crm_footer_links() { ?>

		<div id="ifound-footer-logout">
			<a class="footer-login" href="<? echo admin_url( '/admin.php?page=campaign-builder' ); ?>"><? _e( 'CAMPAIGN BUILDER', 'ifound' ); ?></a>
		</div>

		<div id="ifound-footer-logout">
			<a class="footer-login" href="<? echo admin_url( '/edit.php?post_type=contacts' ); ?>"><? _e( 'CONTACTS', 'ifound' ); ?></a>
		</div><?

	}

	/**
	 *	Filter Dropdown
	 *
	 *	Add dropdown select for sorting.
	 *
	 *	@since 3.3.3
	 *
	 *	@link  https://codex.wordpress.org/Function_Reference/wp_dropdown_categories
	 */

	public function filter_dropdown() {

		global $typenow;

		if ( $typenow == $this->post_type ) {

			foreach( $this->taxonomys as $taxonomy ) {

				$selected = isset( $_GET[ $taxonomy ] ) ? $_GET[ $taxonomy ] : '';

				$info_taxonomy = get_taxonomy( $taxonomy );

				wp_dropdown_categories(array(
					'show_option_all' => __( 'Show All ' . $info_taxonomy->label ),
					'taxonomy'        => $taxonomy,
					'name'            => $taxonomy,
					'orderby'         => 'name',
					'selected'        => $selected,
					'show_count'      => false,
					'hide_empty'      => true,
				));

			}

		}

	}

	/**
	 *	Filter Query
	 *
	 *	Get the item selected in the dropdown.
	 *
	 *	@since 3.3.3
	 */

	public function filter_query( $query ) {

		global $pagenow, $typenow;

		foreach( $this->taxonomys as $taxonomy ) {

			$q_vars    = &$query->query_vars;

			if (
				$pagenow == 'edit.php'
				&&
				isset( $q_vars['post_type'] )
				&&
				$q_vars['post_type'] == $typenow
				&&
				isset( $q_vars[ $taxonomy ] )
				&&
				is_numeric( $q_vars[ $taxonomy ] )
				&&
				$q_vars[ $taxonomy ] != 0 )
			{
				$term = get_term_by( 'id', $q_vars[ $taxonomy ], $taxonomy );
				$q_vars[ $taxonomy ] = $term->slug;
			}

		}

	}

	/**
	 * Map Meta Cap
	 *
	 * Map user capibilities to custom post types.
	 *
	 * @link http://justintadlock.com/archives/2010/07/10/meta-capabilities-for-custom-post-types
	 *
	 * @since 3.3.3
	 *
	 * @return array $caps Return the capabilities required by the user.
	 */

	public function map_meta_cap( $caps, $cap, $user_id, $args ) {
		/* If editing, deleting, or reading, get the post and post type object. */
		if (
			'edit_' . $this->post_type == $cap
			||
			'delete_' . $this->post_type == $cap
			||
			'read_' . $this->post_type == $cap
			||
			'create_' . $this->post_type == $cap
			||
			'publish_' . $this->post_type == $cap
		) {
			$post = get_post( $args[0] );
			if ($post) {
				$post_type = get_post_type_object( $post->post_type );

				/* Set an empty array for the caps. */
				$caps = array();
				/* If editing, assign the required capability. */
				if ( 'edit_' . $this->post_type == $cap || 'publish_' . $this->post_type == $cap || 'create_' . $this->post_type == $cap ) {
					if ( $user_id == $post->post_author ) {
						$caps[] = $post_type->cap->edit_posts;
					}
					else {
						$caps[] = $post_type->cap->edit_others_posts;
					}
				}

				/* If deleting, assign the required capability. */
				elseif ( 'delete_' . $this->post_type == $cap ) {
					if ( $user_id == $post->post_author ) {
						$caps[] = $post_type->cap->delete_posts;
					}
					else {
						$caps[] = $post_type->cap->delete_others_posts;
					}
				}
			}
		}
		return $caps;
	}

	public function get_option_for_agent($name, $default = false, $crm_id_options = []) {
		$option_name = $this->get_option_name_for_agent($name, $crm_id_options);
		return get_option($option_name, $default);
	}

	public function get_option_name_for_agent($option_name, $crm_id_options = []) {
		return $option_name . $this->crm_id($crm_id_options);
	}

	public function update_option_for_agent($name, $value, $crm_id_options = []) {
		$option_name = $this->get_option_name_for_agent($name, $crm_id_options);
		return update_option($option_name, $value);
	}

	public function get_transient_for_agent($name, $crm_id_options = []) {
		$option_name = $this->get_option_name_for_agent($name, $crm_id_options);
		return get_transient($option_name);
	}

	protected function set_transient_for_agent($name, $value, $expiration = 0, $crm_id_options = []) {
		$option_name = $this->get_option_name_for_agent($name, $crm_id_options);
		return set_transient($option_name, $value, $expiration);
	}

	protected function delete_transient_for_agent($name, $crm_id_options = []) {
		$option_name = $this->get_option_name_for_agent($name, $crm_id_options);
		return delete_transient($option_name);
	}

	protected function update_cron_for_hook($setting_name, $hook_name, $add_random_offset = false) {
		$user_id = iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id();
		$crm_id = $this->crm_id_from_user_id($user_id);
		$key = 'ifound_crm_settings' . $crm_id;

		if( isset( $_POST[$key] ) && $_POST['option_page'] == $key ) {
			$input = $_POST[$key];
			wp_clear_scheduled_hook($hook_name, [$user_id]);

			$time_of_day = sanitize_text_field($input[$setting_name]);
			if ($time_of_day !== 'none') {
				$cron_time = iFoundUtil::get_cron_time($time_of_day);
				if ($add_random_offset) {
					// Add 0-3600 seconds, so that all crons from all sites don't run at the same time. Base the number of
					// seconds on a numerical hash of the domain plus the user id, trying to space multiple users out by
					// about 5 minutes.
					$host = $this->util()->get_host();
					// To generate a numerical hash, I used this idea:
					// https://stackoverflow.com/a/3379493/135101
					$bignum = hexdec(substr(sha1($host), 0, 15));
					$extra_seconds = ($bignum + $user_id * 5 * 60) % (60 * 60);
					$cron_time += $extra_seconds;
				}
				wp_schedule_event($cron_time, 'daily', $hook_name, [$user_id]);
			}
		}
	}
}

iFoundCrm::static_init();

$crm_directories = array(
	'includes',
	'integrations',
	'teams',
	'admin'
);

foreach( $crm_directories as $directory ) {

	foreach ( glob( plugin_dir_path( __FILE__ ) . $directory . '/*.php' ) as $file ) {
		require_once $file;
	}

}

