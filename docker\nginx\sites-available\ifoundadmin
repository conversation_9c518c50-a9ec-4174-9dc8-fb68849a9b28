server {
    listen 80;
    server_name ifoundadmin.test *.ifoundadmin.test api.reblogdog.test;

    root /var/www/ifoundadmin;
    index index.php;

    access_log /var/log/nginx/ifoundadmin.log;
    error_log /var/log/nginx/ifoundadmin-error.log;

    # Ignore requests for favicon.ico during dev. It's an extra request for each Wordpress page load,
    # which clutters debugging sequences.
    location /favicon.ico {
        return 444;
    }

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        set $upstream ifoundadmin:9000;
        fastcgi_pass $upstream;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /www$fastcgi_script_name;
        # Make PHP think we're using HTTPS if an upstream reverse-proxy set X-Forwarded-Proto.
        # See: https://stackoverflow.com/a/46984243/135101
        if ($http_x_forwarded_proto = 'https') {
            set $fe_https 'on';
        }
        fastcgi_param HTTPS $fe_https;
    }

    include /etc/nginx/global/idx.conf;

}
