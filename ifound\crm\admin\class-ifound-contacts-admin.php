<?
defined('ABSPATH') or die('You do not have access!');

require_once(__DIR__ . '/../../traits/NewHooklessTrait.php');

/**
 * iFoundContactsAdmin Class
 *
 * @since 3.0.0
 */
class iFoundContactsAdmin extends iFoundContacts {
	use UtilTrait;
	use NewHooklessTrait;

	private static $recent_date_description = null;
	private static $more_recent_than_date = null;
	private static $download_includes_message = null;

	public static function static_init() {
		$days = 30;
		static::$recent_date_description = "in the last {$days} days";
		static::$more_recent_than_date = iFoundUtil::current_datetime()->sub(new DateInterval("P{$days}D"));
		static::$download_includes_message = 'CSV includes name, email, address of each unique contact who visited'
			. ' the site ' . static::$recent_date_description;
	}

	/**
	 * init iFoundContactsAdmin class.
	 *
	 * @since 3.0.0
	 */

	public static function init() {
		$class = __CLASS__;
		new $class;
	}

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */

	public function __construct($options = []) {
		// Normally our constructors do nothing but set up hooks so there is no need to call parent constructors.
		// But in this case, it does more than that so we must call it.
		parent::__construct(['enable_hooks' => false]);

		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			// Reminder: Previously, this is_admin() check was done in class-ifound-features.php.
			// But the WP REST API doesn't know if the user is an admin at that point. So we do the
			// check here.
			if (is_admin()) {
				add_action('admin_init', [$this, 'download_activity']);
				add_action('admin_init', array($this, 'upload'));
				add_action('admin_init', array($this, 'export'));
				add_action('admin_init', array($this, 'handle_upgrade_campaigns'));
				add_action('admin_menu', array($this, 'contacts_menu'), 6);
				// I think we should use add_meta_boxes instead of admin_menu, but admin_menu seems to work also.
				add_action('admin_menu', array($this, 'replace_submit_meta_box'));
				add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
				$this->handle_dynamic_hooks_for_save_post('add');
				add_action('deleted_post', array($this, 'delete_contacts_save_this'), 10, 1);
				add_action('add_meta_boxes_contacts', array($this, 'contacts_meta_box'));
				add_action('pre_get_posts', array($this, 'sortable_columns_query'), 10, 1);
				add_filter('parse_query', array($this, 'filter_query'));
				add_action('restrict_manage_posts', array($this, 'filter_dropdown'), 20, 1);
				add_action('wp_ajax_new_contact', array($this, 'new_contact'));
				add_action('wp_ajax_lookup_contact', array($this, 'lookup_contact'), 10, 1);
				add_filter('bulk_actions-edit-contacts', array($this, 'custom_bulk_actions_contacts'), 15);
				add_filter('bulk_actions-edit-private_contact', array($this, 'custom_bulk_actions_contacts'), 15);
				add_filter('bulk_actions-edit-save_this', array($this, 'custom_bulk_actions'), 15);
				add_filter('bulk_actions-edit-ifound_email', array($this, 'custom_bulk_actions'), 15);
				add_filter('bulk_actions-edit-drip_template', array($this, 'custom_bulk_actions'), 15);
				add_filter('bulk_actions-edit-drip_campaign', array($this, 'custom_bulk_actions'), 15);
				add_filter('page_row_actions', array($this, 'modify_list_row_actions'), 10, 2);
				add_filter('contacts_autocomplete_json', array($this, 'contacts_autocomplete_json'));

				add_filter('manage_contacts_posts_columns', array($this, 'add_contacts_columns'));
				add_action('manage_contacts_posts_custom_column', array($this, 'contact_info_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'latest_visit_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'recent_actions_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'first_name_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'last_name_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'wiseagent_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'created_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'owner_column'), 10, 2);
				add_action('manage_contacts_posts_custom_column', array($this, 'mls_contact_column'), 10, 2);
				add_filter('manage_edit-contacts_sortable_columns', array($this, 'sortable_contacts_columns'));

				add_filter('manage_private_contact_posts_columns', array($this, 'add_contacts_columns'));
				add_action('manage_private_contact_posts_custom_column', array($this, 'contact_info_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'latest_visit_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'recent_actions_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'first_name_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'last_name_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'wiseagent_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'created_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'owner_column'), 10, 2);
				add_action('manage_private_contact_posts_custom_column', array($this, 'mls_contact_column'), 10, 2);
				add_filter('manage_edit-private_contact_sortable_columns', array($this, 'sortable_contacts_columns'));
			}
			add_action( 'rest_api_init', array( $this, 'register_routes' ) );
		}
	}

	private function handle_dynamic_hooks_for_save_post($type) {
		$prefix = 'save_post_';
		$func_name = 'save_post_contacts';
		$callable = [$this, $func_name];
		if ($type === 'add') {
			add_action($prefix . iFoundContacts::$the_post_type, $callable, 10, 3);
			add_action($prefix . iFoundPrivateContact::$the_post_type, $callable, 10, 3);
		} else if ($type === 'remove') {
			remove_action($prefix . iFoundContacts::$the_post_type, $callable);
			remove_action($prefix . iFoundPrivateContact::$the_post_type, $callable);
		} else {
			throw new \Exception('Unknown type for dynamic hooks for save post:' . $type);
		}
	}

	/**
	 * Admin Scripts
	 *
	 * @since 1.0.0
	 * @since 1.0.39 Use iFOUND_PLUGIN_VERSION for JS version.
	 */

	public function admin_scripts() {

		wp_register_script('email_editor_js', plugins_url('js/email-editor.js', __FILE__), array('jquery', 'ifound_shared_admin_js'), iFOUND_PLUGIN_VERSION);
		wp_localize_script('email_editor_js', 'email_editor', array(
			'endpoint' => site_url('/wp-json/ifound/' . iFOUND_PLUGIN_VERSION . '/email/' . wp_create_nonce('admin_secure_me') . '/'),
			'nonce' => wp_create_nonce('wp_rest'),
			'liondesk' => 'https://www.liondesk.com/admin/communications.html?',
			// Hmm. Is this 'link' used?
			'link' => site_url('/save-this/save_this_id')
		));
		wp_localize_script('email_editor_js', 'lookup_contact', array(
			'endpoint' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('lookup_contact_secure_me'),
		));

		wp_register_script('contacts_js', plugins_url('js/contacts.js', __FILE__), array('jquery', 'jquery-ui-datepicker'), iFOUND_PLUGIN_VERSION);

		wp_register_script('new_contact_js', plugins_url('js/new-contact.js', __FILE__), array('jquery', 'jquery-ui-core', 'jquery-ui-autocomplete'), iFOUND_PLUGIN_VERSION);

		wp_register_script('featured_image_js', plugins_url('js/featured-image.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);

		global $typenow;

		if (array_key_exists($typenow, $this->types())) {
			wp_enqueue_script('contacts_admin_js', plugins_url('js/contacts-admin.js', __FILE__), array('jquery'), iFOUND_PLUGIN_VERSION);
			wp_localize_script('contacts_admin_js', 'ifound_contacts_admin', [
				// We put values in a nested array so WP doesn't change booleans to e.g. "1" for true.
				// See: https://wordpress.stackexchange.com/a/186191/27896
				'vals' => [
					'is_team_member' => (new iFoundTeams(['enabled_hooks' => false]))->user_has_team_member_role(),
				],
				'admin_contact_post_type' => iFoundContacts::$the_post_type,
				'admin_contact_tag_taxonomy' => iFoundContacts::$generic_contact_tag_taxonomy,
				// This value might or might be different than the admin_contact_tag_taxonomy value above this during
				// runtime.
				'team_member_contact_tag_taxonomy' => iFoundContacts::$contact_tag_taxonomy,
			]);
		}

	}

	public function register_routes() {
		$permission_callback = function($request) {
			// Reminder: I'm using current_user_can() with edit_contactss (the plural), as opposed to
			// current_user_can('edit_contacts', $request->get_param('contact_id')) because it's simpler; we shouldn't
			// need a check on the specific post. If we wanted to, we'd want to make sure we used these args elsewhere
			// when we call register_post_type():
			//   'map_meta_cap'		=> true
			//   'capability_type'	=> 'contacts'
			$can = current_user_can('edit_contactss');
			// $contact_id = $request->get_param('contact_id');
			// $can = current_user_can('edit_contacts', $contact_id);
			return $can;
		};
		$contact_id_arg = [
			'sanitize_callback' => 'absint',
			'validate_callback' => function( $param, $request, $key ) {
				$is_numeric = is_numeric( $param );
				return $is_numeric;
			},
		];
		$mls_id_arg = [
			'sanitize_callback' => 'sanitize_text_field',
			'validate_callback' => function( $param, $request, $key ) {
				$is_valid = $request->get_method() === 'POST' || is_string( $param );
				return $is_valid;
			},
		];
		$rrr = function($route, $args) {
			register_rest_route('ifound/' . iFOUND_PLUGIN_VERSION, $route, $args);
		};

		$rrr(
			'/admin/contacts/(?P<contact_id>\d+)/owned_listings/(?P<mls_id>\S+)/refresh',
			[
				'methods'  => WP_REST_Server::CREATABLE,
				'callback' => [$this, 'owned_listings_api_refresh'],
				'permission_callback' => $permission_callback,
				'args' => [
					'contact_id' => $contact_id_arg,
					'mls_id' => $mls_id_arg,
				],
			]
		);

		$rrr(
			'/admin/contacts/(?P<contact_id>\d+)/owned_listings(/(?P<mls_id>\S+))?',
			[
				'methods'  => [WP_REST_Server::CREATABLE, WP_REST_Server::DELETABLE],
				'callback' => [$this, 'owned_listings_api'],
				'permission_callback' => $permission_callback,
				// Reminder: I'm not putting nonce as an arg here. As it currently is as of this commit,
				// the _wpnonce query string arg establishes my identity and that I'm authorized to perform this
				// action, using the permission_callback.
				'args' => [
					'contact_id' => $contact_id_arg,
					'mls_id' => $mls_id_arg,
				],
			]
		);
	}

	public function contacts_menu() {

		add_submenu_page(
			$this->crm_menu(),
			__('CRM Import', 'ifound'),
			__('CRM Import', 'ifound'),
			'crm_import',
			'import_contacts',
			array($this, 'import_contacts')
		);

		add_submenu_page(
			$this->crm_menu(),
			__('CRM Export', 'ifound'),
			__('CRM Export', 'ifound'),
			'crm_export',
			'export_contacts',
			array($this, 'export_contacts')
		);

		add_submenu_page(
			// By using admin.php, the page title will work and the menu slug will still properly get registered for use later with menu_page_url.
			'admin.php',
			__('Create Search Campaign', 'ifound'),
			__('Create Search Campaign', 'ifound'),
			'crm_import',
			'create_homeowner_campaign',
			function () {
				do_action('ifound_create_homeowner_campaign_page');
			}
		);

		add_submenu_page(
			'admin.php',
			__('Email', 'ifound'),
			__('Email', 'ifound'),
			'crm_import',
			'bulk_email',
			function () {
				do_action('ifound_bulk_email_page');
			}
		);

		add_submenu_page(
			'admin.php',
			__('Create Drip Campaigns', 'ifound'),
			__('Create Drip Campaigns', 'ifound'),
			'crm_import',
			iFoundBulkDripCampaigns::$snake_slug,
			function () {
				do_action(iFoundBulkDripCampaigns::$ifound_snake_slug . '_page');
			}
		);

		add_submenu_page(
			'admin.php',
			__('Activity Log', 'ifound'),
			__('Activity Log', 'ifound'),
			'crm_import',
			'activity_log',
			function () {
				do_action('ifound_activity_log_page');
			}
		);

		add_submenu_page(
			$this->crm_menu(),
			__('Latest Website Activity', 'ifound'),
			__('Latest Website Activity', 'ifound'),
			'crm_import',
			'latest_website_activity',
			[$this, 'latest_website_activity_page']
		);

		add_submenu_page(
			$this->crm_menu(),
			__('Latest Email Activity', 'ifound'),
			__('Latest Email Activity', 'ifound'),
			'crm_import',
			'latest_email_activity',
			[$this, 'latest_email_activity_content']
		);

		add_submenu_page(
			$this->crm_menu(),
			__('Upgrade Campaigns', 'ifound'),
			__('Upgrade Campaigns', 'ifound'),
			iFoundAdmin::$manage_ifound_staff_settings_capability_name,
			'upgrade_campaigns',
			[$this, 'upgrade_campaigns_page']
		);
	}

	/**
	 *    Contact Info Column
	 *
	 *    Contact Info Column for the save this post type.
	 *
	 * @param array $column The column for contact link.
	 * @param int $save_this_id The ID if the save this.
	 * @since 1.2.48
	 *
	 */

	public function contact_info_column($column, $save_this_id) {

		if ($column == 'contact_info') {

			ob_start(); ?>

			<div><?

			if ($email = get_post_meta($save_this_id, 'email', true)) { ?>

				<div><a href="mailto:<? echo $email; ?>"><? _e('Email: ' . $email, 'ifound'); ?></a></div><?

			}

			if ($hphone = get_post_meta($save_this_id, 'hphone', true)) { ?>

				<div><a href="tel:<? echo $hphone; ?>"><? _e('Call - Home: ' . $hphone, 'ifound'); ?></a></div><?

			}

			if ($mphone = get_post_meta($save_this_id, 'mphone', true)) { ?>

				<div><a href="tel:<? echo $mphone; ?>"><? _e('Call - Mobile: ' . $mphone, 'ifound'); ?></a></div>
				<div><a href="sms:<? echo $mphone; ?>"><? _e('Text - Mobile: ' . $mphone, 'ifound'); ?></a></div><?

			} ?>

			</div><?

			echo ob_get_clean();

		}

	}

	public function latest_visit_column($column, $post_id) {

		if ($column == 'latest-visit') {

			if ($visits = $this->most_recent_meta($post_id, '%_view', 1)) {

				foreach ($visits as $visit) {

					list($date) = explode(';', $visit->meta_value); ?>

					<? _e(apply_filters('pretty_date', $date), 'ifound');

				}

			} else {

				_e('__', 'ifound');

			}

		}

	}

	public function first_name_column($column, $post_id) {

		if ($column == 'fname') {

			if ($fname = get_post_meta($post_id, 'fname', true)) {
				?>
				<div class="admin-contact-name"><? _e($fname, 'ifound') ?></div>
				<?php

			} else {

				_e('__', 'ifound');

			}

		}

	}

	public function last_name_column($column, $post_id) {

		if ($column == 'lname') {

			if ($lname = get_post_meta($post_id, 'lname', true)) {
				?>

				<div class="admin-contact-name"><?= $lname ?></div>
				<?php

			} else {

				_e('__', 'ifound');

			}

		}

	}

	public function recent_actions_column($column, $post_id) {

		if ($column == 'activity_log') {

			if ($logs = $this->most_recent_meta($post_id, 'activity_log')) {

				if ($logs) {
					$log = $logs[0];

					list($date, $action, $msg) = explode(';', $log->meta_value);
					?>
					<? _e(apply_filters('pretty_date', $date), 'ifound'); ?> -
					<b><? _e($action, 'ifound'); ?></b>
					<?

				}

			} else {

				_e('__', 'ifound');

			}

		}

	}

	public function wiseagent_column($column, $post_id) {
		if ($column === 'wiseagent') {
			echo '<div style="text-align: center;">';
			if (get_post_meta($post_id, iFoundWiseAgent::$wa_id_key, true)) {
				?><img src='/wp-content/plugins/ifound/idx/images/wiseagent.png' width="30"/><?
			} else {
				_e('__', 'ifound');
			}
			echo '</div>';
		}
	}

	public function mls_contact_column($column, $post_id) {
		if ($column === 'mls_contact') {
			echo '<div style="text-align: center;">';
			if ($this->is_contact_from_mls($post_id)) {
				?><img src="<?= $this->get_mls_image() ?>"><?
			} else {
				_e('__', 'ifound');
			}
			echo "</div>";
		}
	}

	public function is_contact_from_mls($contact_id) {
		$mls_id = get_post_meta($contact_id, iFoundMlsContacts::$mls_id_meta_key, true);
		return !!$mls_id;
	}

	public function get_mls_image() {
		$mls = iFoundIdx::mls_name();
		if ($mls === 'armls') {
			return '/wp-content/plugins/ifound/idx/images/ARMLS.png';
		}
		return null;
	}

	public function created_column($column, $post_id) {
		if ($column === 'created') {
			global $post;
			echo apply_filters('pretty_date', $post->post_date);
		}
	}

	public function owner_column($column, $post_id) {
		if ($column === 'owner') {
			global $post;
			echo $post->post_type === 'private_contact' ? 'Private' : 'Team';
		}
	}

	public function add_contacts_columns($columns) {

		$contacts_columns = array_merge(
			array('cb' => __('checkall', 'ifound')),
			array('fname' => __('First Name', 'ifound')),
			array('lname' => __('Last Name', 'ifound')),
			array('contact_info' => __('Contact Info', 'ifound')),
			array('created' => __('Created', 'ifound')),
			array('taxonomy-contacts_status' => __('Contact Status', 'ifound')),
			array('taxonomy-' . iFoundContacts::$contact_tag_taxonomy =>
				__(iFoundContacts::$contact_tag_taxonomy_plural_label, 'ifound')),
			array('latest-visit' => __('Latest Visit', 'ifound')),
			array('activity_log' => __('Most Recent Action', 'ifound')),
			array('wiseagent' => __('Wise Agent Sync Status', 'ifound')),
			array('mls_contact' => __('Imported from MLS?', 'ifound')),
		);

		return apply_filters('ifound_admin_columns', $contacts_columns);

	}

	public function sortable_contacts_columns($columns) {
		$forced_columns = [
			'fname'                                             => 'fname',
			'lname'                                             => 'lname',
			'taxonomy-contacts_status'                          => 'contacts_status',
			'taxonomy-' . iFoundContacts::$contact_tag_taxonomy => 'contact_tags',
			'created'                                           => 'created',
		];
		// When the user first sorts by this column, it should sort desc, not asc.
		$forced_columns['latest-visit'] = ['latest-visit', 'desc'];
		$forced_columns['activity_log'] = ['activity_log', 'desc'];

		global $typenow;
		if ($typenow === 'private_contact') {
			$forced_columns['owner'] = 'owner';
		}

		return $forced_columns;
	}

	public function sortable_columns_query($query) {

		if (!is_admin()) return;

		$orderby = $query->get('orderby');

		$options = array(
			'fname' => 'fname',
			'lname' => 'lname'
		);

		if ($orderby
			// $orderby is not necessarily a string or int, which is what array_key_exists() expects.
			&& (gettype($orderby) === 'string' || gettype($orderby) === 'integer')
			&& array_key_exists($orderby, $options))
		{
			$query->set('meta_key', $options[$orderby]);
			$query->set('orderby', 'meta_value');
		} else if ($orderby === 'created') {
			$query->set('orderby', 'date');
		} else if ($orderby === 'latest-visit') {
			// We use this NOT EXISTS way to include contacts that have never visited. Otherwise they are excluded.
			// See: https://wordpress.stackexchange.com/a/293403/27896
			$query->set('meta_query', [
				'relation' => 'OR',
				['key' => 'latest_visit', 'compare' => 'NOT EXISTS'],
				['key' => 'latest_visit'],
			]);
			$query->set('orderby', 'meta_value');
		} else if ($orderby === 'activity_log') {
			// We use this NOT EXISTS way to include contacts that have no activity. Otherwise they are excluded.
			// See: https://wordpress.stackexchange.com/a/293403/27896
			$query->set('meta_query', [
				'relation' => 'OR',
				['key' => 'latest_activity_log', 'compare' => 'NOT EXISTS'],
				['key' => 'latest_activity_log'],
			]);
			$query->set('orderby', 'meta_value');
			// $order = $query->get('order');
			// $switched_order = $order === 'asc' ? 'desc' : 'asc';
			// $query->set('order', $switched_order);
		} else if ($orderby === 'owner') {
			$query->set('orderby', 'post_type');
			$order = $query->get('order');
			// We reverse the order because the post_type slugs of 'contacts' and 'private_contacts' are in the opposite
			// alphabetical order than the labels we are using for the 'Owner' column, which are 'Team' and 'Private',
			// respectively.
			$switched_order = $order === 'asc' ? 'desc' : 'asc';
			$query->set('order', $switched_order);
		} else if ($orderby === 'contact_tags') {
			// Ok. I'm scratching my head why it seems backward by default. Oh well. Simple enough to reverse it.
			$query->set('orderby', 'contact_tags');
			$order = $query->get('order');
			$switched_order = $order === 'asc' ? 'desc' : 'asc';
			$query->set('order', $switched_order);
		}

	}

	/**
	 *    Custom Bulk Actions
	 *
	 *  Customize the bulk actions dropdown in wp admin screen.
	 *
	 * @param array $actions The array of actions provided by WP and other plugins.
	 * @return array $actions The custom array of actions we provided.
	 * @since 1.2.48
	 *
	 */

	public function custom_bulk_actions($actions) {
		unset($actions);
		$actions['edit'] = __('Edit', 'edit');
		$actions['delete'] = __('Delete Permanently', 'delete');
		return $actions;
	}

	public function custom_bulk_actions_contacts($actions) {
		$our_actions = $this->custom_bulk_actions($actions);
		$our_actions['ifound_create_homeowner_campaign'] = __('Create Search Campaigns', 'ifound');
		$our_actions['ifound_create_bulk_drip_campaigns'] = __('Create Drip Campaigns', 'ifound');
		$our_actions['ifound_bulk_email'] = __('Send Email', 'ifound');
		return $our_actions;
	}

	/**
	 *    Modify List Row Actions
	 *
	 *    Modify the list row for the save this and contacts post type.
	 *
	 * @param array $actions The default actions.
	 * @param object $post The post object.
	 * @return array  $actions The modified actions.
	 * @since 1.2.48
	 *
	 */

	public function modify_list_row_actions($actions, $post) {

		global $typenow;

		// Check for your post type.
		if (array_key_exists($typenow, $this->types())) {

			unset($actions['inline hide-if-no-js']);
			unset($actions['trash']);

		}

		return $actions;

	}

	/**
	 *    Replace Submit Metabox
	 *
	 *    Replace the submit metabox for save this and contacts.
	 *
	 * @since 1.2.48
	 */

	public function replace_submit_meta_box() {

		foreach ($this->types() as $item => $value) {

			remove_meta_box('submitdiv', $item, 'core');

			add_meta_box(
				'submitdiv',
				sprintf(__('Save/Update %s'),
					$value),
				array($this, 'submit_meta_box'),
				$item,
				'side',
				'high'
			);

		}
	}

	/**
	 *    Submit Metabox
	 *
	 *    Content for the submit metabox for save this and contacts.
	 *
	 * @since 1.2.48
	 */

	public function submit_meta_box() {

		global $action, $post;

		if ($post->post_type === iFoundSaveThis::$the_post_type && $post->post_status === 'draft') {
			iFoundSaveThisAdmin::new_hookless()->draft_submit_meta_box($post);
			return;
		}

		$post_type = $post->post_type; // get current post_type
		$post_type_object = get_post_type_object($post_type);
		$can_publish = current_user_can($post_type_object->cap->publish_posts);
		$can_delete = current_user_can($post_type_object->cap->delete_posts);

		$item = $this->types(); ?>

		<div class="submitbox" id="submitpost">

		<div id="major-publishing-actions"><?

			do_action('post_submitbox_start'); ?>

			<div id="publishing-action">

				<span class="spinner"></span><?

				if (!in_array($post->post_status, array('publish')) || 0 == $post->ID) {

					if ($can_publish) : ?>

						<input name="original_publish" type="hidden" id="original_publish"
							   value="<?php esc_attr_e('Add Tab') ?>" /><?
						submit_button(__('Add ' . $item[$post_type]), 'primary button-large', 'publish', false, array('accesskey' => 'p'));

					endif;

				} else { ?>

					<input name="original_publish" type="hidden" id="original_publish"
						   value="<?php esc_attr_e('Update ' . $item[$post_type]); ?>"/>
					<input name="save" type="submit" class="button button-primary button-large" id="publish"
						   accesskey="p" value="<?php esc_attr_e('Update ' . $item[$post_type]); ?>" /><?

				} ?>

			</div><?

			if ($can_delete) { ?>

				<div id="delete-action">

				<a class="submitdelete deletion" href="<? echo get_delete_post_link($post->ID, '', true); ?>">
					<? _e('Delete Permanently', 'ifound'); ?>
				</a>

				</div><?

			} ?>

			<div class="clear"></div>

		</div>

		</div><?

	}

	/**
	 *    Contacts Metabox
	 *
	 *    Add contacts metaboxes.
	 *
	 * @since 1.0.0
	 *
	 * @link https://developer.wordpress.org/reference/functions/add_meta_box/
	 */

	public static function contacts_meta_box() {

		global $typenow;

		add_meta_box(
			'contact_meta',
			__('<i class="fal fa-address-book" aria-hidden="true"></i> Contact Info', 'ifound'),
			array(iFoundContacts::new_hookless(), 'contact_profile'),
			$typenow,
			'advanced',
			'high'
		);

		add_meta_box(
			'owned_listings_meta',
			'Owned Listings',
			[iFoundContactsAdmin::new_hookless(), 'owned_listings_meta_box'],
			$typenow,
			'advanced',
			'high'
		);

		add_meta_box(
			'notes_meta',
			__('<i class="fal fa-sticky-note" aria-hidden="true"></i> Notes', 'ifound'),
			array('iFoundNotes', 'notes_meta_box'),
			$typenow,
			'advanced',
			'default'
		);

	}

	public function owned_listings_meta_box($post) {
		wp_enqueue_script( 'ifound_spa_js' );
		$owned_listings_full = get_post_meta($post->ID, 'owned_listings', true);
		$owned_listings = isset($owned_listings_full['listings']) ? $owned_listings_full['listings'] : (object)[];
		?>
		<div class="ifound-owned-listings-wrapper">
			Loading...
		</div>
		<script>
			jQuery(document).ready(function() {
				var domContainer = document.querySelector('.ifound-owned-listings-wrapper');
				var props = {
					endpoint: '<?= rest_url('ifound/' . iFOUND_PLUGIN_VERSION . '/admin/contacts/' . $post->ID . '/owned_listings') ?>',
					owned_listings: <?= json_encode($owned_listings) ?>,
				};
				Object.assign(props, _.pick(ifound_shared_admin, [
					'nonce',
				]));
				window.ifound_spa.render_app('owned_listings', domContainer, props);
			});
		</script>
		<?
	}

	private function fetch_listing_data($mls_id) {
		$params = [
			// Get all fields, not just what's normally returned with a search
			'all' => true,
			'mls_id' => $mls_id,
			'list_status' => ['Active', 'Pending', 'Closed', 'Sold'],
		];
		$extra = ['stats' => ['show' => 'no_stats']];
		$results = $this->process_input($params, $extra);
		// I don't know why, but if you search Solr ARMLS (via the npm lib) for the listing ID 'a', it will match
		// ALL the documents in the core, and return the first page of those results. It seems that somehow it
		// ignores our invalid params. If you do the same query via the solr admin UI, it says "Invalid Number: a".
		// For now, I'll assume multiple results is not a match.
		if (!$results->listings || count($results->listings) !== 1) {
			return new WP_Error('not_found', "No listing with ID '$mls_id'", ['status' => 404]);
		}
		$listing_data = (array)$results->listings[0];
		return $listing_data;
	}

	// TODO: Use a controller as recommended at:
	// https://developer.wordpress.org/rest-api/extending-the-rest-api/adding-custom-endpoints/#the-controller-pattern
	// This code does not use a controller YET as it was originally following the WP AJAX pattern.
	public function owned_listings_api(WP_REST_Request $request) {
		$post_id = $request->get_param('contact_id');
		if ($request->get_method() === 'POST') {
			$json_string = file_get_contents('php://input');
			$json = json_decode($json_string, true);
			$mls_id = $json['mls_id'];
			$owned_listings = get_post_meta($post_id, 'owned_listings', true);
			if ($owned_listings) {
				if (array_key_exists($mls_id, $owned_listings['listings'])) {
					return new WP_Error( 'duplicate', "Duplicate MLS ID for contact: $mls_id", ['status' => 409]);
				}
			} else {
				$owned_listings = [
					'version' => '1.0.0',
					'listings' => [],
				];
			}
			$listing_data = $this->fetch_listing_data($mls_id);
			if (is_wp_error($listing_data)) {
				return $listing_data;
			}
			$now = new DateTime('NOW');
			$now_string = $now->format('c');
			$owned_listing = [
				'created_at' => $now_string,
				'updated_at' => $now_string,
				'data' => $listing_data,
			];
			$owned_listings['listings'][$mls_id] = $owned_listing;
			update_post_meta($post_id, 'owned_listings', $owned_listings);
			return [
				'owned_listing' => $owned_listing,
			];
		} else if ($request->get_method() === 'DELETE') {
			$mls_id = $request->get_param('mls_id');
			$owned_listings = get_post_meta($post_id, 'owned_listings', true);
			unset($owned_listings['listings'][$mls_id]);
			update_post_meta($post_id, 'owned_listings', $owned_listings);
		} else {
			return new WP_Error('bad_request', "Invalid sub_action: $sub_action", ['status' => 400]);
		}
	}

	public function owned_listings_api_refresh(WP_REST_Request $request) {
		$mls_id = $request->get_param('mls_id');
		$refreshed_listing_data = $this->fetch_listing_data($mls_id);
		if (is_wp_error($refreshed_listing_data)) {
			return $refreshed_listing_data;
		}
		$post_id = $request->get_param('contact_id');
		$owned_listings = get_post_meta($post_id, 'owned_listings', true);
		$existing_listing = &$owned_listings['listings'][$mls_id];
		$now = new DateTime('NOW');
		$now_string = $now->format('c');
		$existing_listing['updated_at'] = $now_string;
		// This is a handy way to test that refresh works on the site.
		// if ($existing_listing['data']['ListStatus'] === 'Closed') {
		// 	$refreshed_listing_data['ListStatus'] = 'Active';
		// } else {
		// 	$refreshed_listing_data['ListStatus'] = 'Closed';
		// }
		$existing_listing['data'] = $refreshed_listing_data;
		update_post_meta($post_id, 'owned_listings', $owned_listings);
		return [
			'owned_listing' => $existing_listing,
		];
	}

	public function enqueue_contact_autosuggest() {
		wp_enqueue_script('jquery-ui-core');
		wp_enqueue_script('jquery-ui-autocomplete');
		wp_enqueue_script('new_contact_js');
		// Reminder: we used to have this wp_localize_script() call in the admin_enqueue_scripts hook, but it proved too
		// slow on sites with many contacts. So we do it here, where we know it's used.
		wp_localize_script('new_contact_js', 'new_contact', array(
			'contacts_json' => apply_filters('contacts_autocomplete_json', false)
		));
	}

	/**
	 * Contacts AutoComplete Json
	 *
	 * This includes all names of contacts that are not unsubscribed.
	 *
	 * @return array $contacts_array An array of contact names.
	 * @since 1.0.0
	 *
	 */

	public function contacts_autocomplete_json() {

		$offset = 0;
		$contacts_array = [];
		$done = false;
		$pagesize = 200;
		while (!$done) {
			$args = array(
				'numberposts' => $pagesize,
				'post_type' => $this->is_site_admin()
					? $this->post_type
					: [$this->post_type, iFoundPrivateContact::$the_post_type],
				'author__in' => iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
				'orderby' => 'title',
				'order' => 'ASC',
				'offset' => $offset,
			);

			$contacts = get_posts($args) ?: array();

			foreach ($contacts as $contact) {

				/** check unsubscribe */
				if (has_term('unsubscribed', $this->taxonomy, $contact)) continue;

				$m = $this->util()->get_single_metas($contact->ID, ['address', 'city', 'state', 'zip']);
				// The address will be used for google autocomplete so we don't use address_2 or commas.
				$address = implode(' ', [$m['address'], $m['city'], $m['state'], $m['zip']]);

				$owned_listings_meta = get_post_meta($contact->ID, 'owned_listings', true);
				$owned_listings = [];
				if ($owned_listings_meta) {
					foreach ($owned_listings_meta['listings'] as $mls_id => $info) {
						$d = $info['data'];
						$owned_listing_address = apply_filters('ifound_format_listing_address', $d);
						$owned_listings[] = [
							'mls_id' => $mls_id,
							'address' => $owned_listing_address,
							'lat' => $d['location_0_coordinate'],
							'lng' => $d['location_1_coordinate'],
						];
					}
				}

				$contacts_array[] = [
					'key' => $contact->ID,
					'value' => html_entity_decode($contact->post_title),
					'address' => $address,
					'owned_listings' => $owned_listings,
				];

			}
			$offset += $pagesize;
			$done = count($contacts) < $pagesize;
		}

		return $contacts_array;

	}

	/**
	 * Save Post Contacts
	 *
	 * This publishes and updates a contact from the WP Admin contact page.
	 *
	 * @param int $contact_id The ID of the post being saved.
	 * @since 2.4.2 Add condition for original_publish.
	 * @since 2.5.34 Add ifound_external_crm_new_submission
	 *
	 * @since 1.0.0
	 */

	public function save_post_contacts($contact_id, $contact, $update) {
		if (!current_user_can('edit_contactss')) return;

		if (isset($_POST['original_publish'])) {

			$entry = $this->save_contact_meta($contact_id, $_POST);

			$need_to_update_contact = false;
			// If the post title is blank, use contact first and last name.
			if ($contact->post_title === '') {
				$contact->post_title = $entry['fname'] . ' ' . $entry['lname'];
				$need_to_update_contact = true;
			}

			$new_contact_author = iFoundJointContact::new_hookless()->get_new_contact_author();
			if (!$update && $new_contact_author !== $contact->post_author) {
				$contact->post_author = $new_contact_author;
				$need_to_update_contact = true;
			}

			if ($need_to_update_contact) {
				// Avoid infinite update loop. Unhook, then re-hook after.
				$this->handle_dynamic_hooks_for_save_post('remove');
				wp_update_post($contact);
				$this->handle_dynamic_hooks_for_save_post('add');
			}

			do_action('ifound_external_crm_new_submission', $this->obj($entry));

		}

	}

	/**
	 *    Delete Contacts Save This
	 *
	 *    Permanently delete the save this for the contact that was just deleted.
	 *
	 * @param int $contact_id The ID if the contact that was just deleted.
	 * @since 1.2.48
	 *
	 */

	public function delete_contacts_save_this($contact_id) {

		$post_type = get_post_type($contact_id);

		if (in_array($post_type, [$this->post_type, iFoundPrivateContact::$the_post_type], true)) {

			if ($meta_array = $this->get_post_id_by_key_value('contact_id', $contact_id, false)) {

				foreach ($meta_array as $meta)
					wp_delete_post($meta->post_id, true);

			}

		}

	}

	public function import_contacts() {

		if (!current_user_can('crm_import')) return; ?>

		<script>
			jQuery(document).ready(function ($) {
				$(document).on('click', '.add-select', function () {
					$('.choices-select-wrap').append($('.choices-select').html());
				});
				$('#delimiter').on('change', function () {
					$('.choices-select-wrap, .add-field-wrapper').removeClass('hide');
					if ($('#delimiter').val() == 'liondesk') {
						$('.choices-select-wrap, .add-field-wrapper').addClass('hide');
					}
				});
			});
		</script>

		<style>
			.hide {
				display: none;
			}
		</style>

		<h1 class="ifound-admin-h1"><? _e('Import Contacts', 'ifound'); ?></h1><?

		do_action('ifound_help_button', 'crm_import'); ?>

		<div class="import-help-msg">

			<? _e('Would you like us to import your contacts for you?', 'ifound'); ?> <a
					href="<? echo esc_url('https://ifoundagent.com/import-contacts/'); ?>"
					target="_blank"><? _e('Click here for help!', 'ifound'); ?></a>
		</div>

		<form method="post">

			<? wp_nonce_field('ifound_upload_contacts', 'ifound_upload_contacts_secure_me'); ?>

			<table class="form-table">

				<tbody>

				<tr>

					<th scope="row"><label for="delimiter"><? _e('Delimiter', 'ifound'); ?></label></th>

					<td>

						<select name="delimiter" id="delimiter">
							<option value=",">,</option>
							<option value=";">;</option>
							<option value="|">|</option>
							<option value="liondesk">LionDesk Format</option>
						</select>

					</td>

				</tr>

				<tr class="add-field-wrapper">

					<th scope="row" colspan="2"><? _e('Add Field', 'ifound'); ?> <i
								class="fal fa-plus-square add-select" aria-hidden="true"></i></th>

				</tr>

				<tr>

					<td class="choices-select-wrap" colspan="2">

							<span class="choices-select">

								<select name="choices[]">

									<option value=""><? _e('Blank', 'ifound'); ?></option><?

									foreach ($this->contact_fields() as $key => $value) { ?>

										<option value="<? echo $key; ?>"><? echo $value; ?></option><?

									} ?>
									<option value="<?= iFoundContacts::$generic_contact_tag_taxonomy ?>">
										<?= iFoundContacts::$contact_tag_taxonomy_plural_label ?>
									</option>
									<option value="<?= iFoundContacts::$the_taxonomy ?>">
										<?= iFoundContacts::$taxonomy_plural_label ?>
									</option>

								</select>

							</span>

					</td>

				</tr>


				<tr>

					<td colspan="10">
						<div style="color:#aaa;font-size:small;margin-bottom:5px;">
							For any field values that contain a comma, you'll need to wrap the value with double quotes.
							The most likely candidate would fields that accept multiple items such as Contact Tags or
							Contact Statuses.
						</div>
						<textarea name="dump_data" rows="20" cols="200"
											   placeholder="Paste your contacts here." class="large-text"></textarea>
					</td>

				</tr>

				<tr>

					<td colspan="2"><input type="submit" name="upload_dump" value="Import"
										   class="button button-primary"></td>

				</tr>

				</tbody>

			</table>

		</form><?

	}

	/**
	 * Upload
	 *
	 * Upload bulk contacts
	 *
	 * LionDesk Export Format
	 * idCustomer,FirstName,LastName,Email,Birthday,Anniversary,SpouseName,SpouseEmail,SpouseNumber,SpouseBirthday,Created,Unsubscribed,
	 * Assigned_Agent_First,Assigned_Agent_Last,Mobile_Phone,Home_Phone,Source,Status,Tags,Hotness,Last_Comment,Address1,Address2,City,State,Zip,
	 * OfficeAddress1,OfficeAddress2,OfficeCity,OfficeState,OfficeZip,CustomField_DateofClosing,CustomField_Subdivision,CustomField_BlogPostLink
	 *
	 * @since 1.0.0
	 * @since 1.2.2 Add Liondesk support
	 * @since 2.5.43 Add Liondesk contact ID to import.
	 */

	public function upload() {

		if (!current_user_can('crm_import')) return;

		if (isset($_POST['upload_dump'])) {

			/** @link https://codex.wordpress.org/Function_Reference/check_admin_referer */
			check_admin_referer('ifound_upload_contacts', 'ifound_upload_contacts_secure_me');

			// PHP auto escapes input. We don't want that. Fix it.
			// https://stackoverflow.com/a/33604648/135101
			$post_values = array_map('stripslashes_deep', $_POST);
			$data = $post_values['dump_data'];
			$contacts = [];
			if ($_POST['delimiter'] == 'liondesk') {
				// TODO: We don't support liondesk anymore. I did not update or test this code path as I made my recent
				//   changes.
				$rows = preg_split('/[\r\n]+/', $data, -1, PREG_SPLIT_NO_EMPTY);
				foreach ($rows as $row) {

					$string = $this->clean_delimiters_between(stripslashes($row));

					list(
						$c['liondesk_contact_id'],    //idCustomer
						$c['fname'],                  //FirstName
						$c['lname'],                  //LastName
						$c['email'],                  //Email
						$c['birthday'],               //Birthday
						$NA,                          //Anniversary
						$c['fname_spouse'],           //SpouseName
						$c['email_spouse'],           //SpouseEmail
						$c['mphone_spouse'],          //SpouseNumber
						$c['birthday_spouse'],        //SpouseBirthday,
						$c['acquired_date'],          //Created
						$NA,                          //Unsubscribed,
						$NA,                          //Assigned_Agent_First
						$NA,                          //Assigned_Agent_Last
						$c['mphone'],                 //Mobile_Phone
						$c['hphone'],                 //Home_Phone,
						$c['acquired_from'],          //Source
						$c['status'],                 //Status
						$NA,                          //Tags
						$NA,                          //Hotness
						$NA,                          //Last_Comment
						$c['address'],                //Address1
						$c['address_2'],              //Address2
						$c['city'],                   //City
						$c['state'],                  //State
						$c['zip']                     //Zip
						// There are several fields here that we do not use.
						) = explode(',', $string);
					$contacts[] = $c;
				}
			} else {
				$orig_value = ini_get('auto_detect_line_endings');
				ini_set('auto_detect_line_endings', true);
				try {
					// We can't simply use explode() with a delimiter. That wouldn't work for values with the delimiter
					// in them. Let's just use fgetcsv. Since that is meant to use a file handle, but we don't have a
					// file handle, we can use this trick from: https://stackoverflow.com/a/34519691/135101
					$stream = fopen('php://memory', 'r+');
					fwrite($stream, $data);
					rewind($stream);
					while (($parts = fgetcsv($stream, 0, $_POST['delimiter'])) !== false) {
						foreach ($_POST['choices'] as $i => $choice) {
							$c[$choice] = $parts[$i];
						}
						$contacts[] = $c;
					}
					fclose($stream);
				} finally {
					ini_set('auto_detect_line_endings', $orig_value);
				}
			}

			$lowercase_emails_seen_in_list = [];
			$num_contacts_imported = 0;
			$num_missing_email = 0;
			$email_addresses_considered_duplicates = [];
			foreach ($contacts as $c) {
				/** Do not allow dupe email addresses */
				$lowercase_email = strtolower($c['email']);
				if (!$lowercase_email) {
					$num_missing_email++;
					continue;
				}
				if (isset($lowercase_emails_seen_in_list[$lowercase_email])) {
					$email_addresses_considered_duplicates[] = $c['email'];
					continue;
				}
				$lowercase_emails_seen_in_list[$lowercase_email] = true;
				if (!empty($c['email']) && $this->get_post_id_by_key_value('email', $c['email'])) {
					$email_addresses_considered_duplicates[] = $c['email'];
					continue;
				}

				$post_title = ucwords($c['fname']) . ' ' . ucwords($c['lname']);

				$post = array(
					'post_title' => trim(str_replace('"', ' ', $post_title)),
					'post_status' => 'publish',
					'post_type' => iFoundJointContact::new_hookless()->get_new_contact_type(),
					'post_author' => iFoundAdmin::new_hookless()->get_this_user_id_or_primary_admin_id(),
				);

				$contact_id = wp_insert_post($post);
				$num_contacts_imported++;

				$this->save_contact_meta($contact_id, $c);

				// Handle contact tags
				if (isset($c[iFoundContacts::$generic_contact_tag_taxonomy])) {
					$contact_tags = $this->util()->split_on_comma($c[iFoundContacts::$generic_contact_tag_taxonomy]);
					wp_set_object_terms($contact_id, $contact_tags, iFoundContacts::$contact_tag_taxonomy, true);
				}
				// Handle contact statuses
				if (isset($c[iFoundContacts::$the_taxonomy])) {
					$contact_statuses = $this->util()->split_on_comma($c[iFoundContacts::$the_taxonomy]);
					wp_set_object_terms($contact_id, $contact_statuses, iFoundContacts::$the_taxonomy, true);
				}
			}

			add_action('admin_notices', function() use ($num_contacts_imported,
				$email_addresses_considered_duplicates, $num_missing_email) {
				?>
				<div class="notice notice-success is-dismissible">
					<div>CRM Import process done.</div>
					<div>Successfully imported <?= $num_contacts_imported ?> contact(s).</div>
					<?php
						echo "<div>Number of records missing an email address: {$num_missing_email}</div>\n";
					?>
					<?php if (count($email_addresses_considered_duplicates)) { ?>
						<div>Duplicates found (<?= count($email_addresses_considered_duplicates) ?>):</div>
						<?php
							foreach ($email_addresses_considered_duplicates as $email) {
								echo "<div>{$email}</div>\n";
							}
						?>
					<?php } else { ?>
						There were no duplicates found.
					<?php } ?>
				</div>
				<?php
			});

		}

	}

	/**
	 *    Export Contacts
	 *
	 *  Export contacts page.
	 *
	 * @since 2.2.0
	 */

	public function export_contacts() {

		if (!current_user_can('crm_export')) return; ?>

		<h1 class="ifound-admin-h1"><? _e('Export Contacts', 'ifound'); ?></h1><?

		do_action('ifound_help_button', 'crm_export'); ?>

		<table class="form-table">

		<tbody>

		<tr>

			<td><? _e('Export and download contacts to .csv file.', 'ifound'); ?></td>

		</tr>

		<tr>

			<td>

				<form method="post">

					<? wp_nonce_field('download_contacts_action', 'download_contacts_field'); ?>

					<input type="hidden" name="export_contacts" value="true">

					<input type="submit" name="download_contacts" class="button button-primary"
						   value="<? _e('Download Now', 'ifound'); ?>">

				</form>

			</td>

		</tr>

		</tbody>

		</table><?

	}

	/**
	 * Export
	 *
	 * Export all contacts and contact profile data.
	 *
	 * @since 2.2.0
	 */

	public function export() {
		if (!current_user_can('crm_export')) return;
		if (
			isset($_POST['export_contacts'])
			&&
			$_POST['export_contacts'] == 'true'
			&&
			check_admin_referer('download_contacts_action', 'download_contacts_field')
		) {
			$args = array(
				'posts_per_page' => -1,
				'post_type' => iFoundJointContact::new_hookless()->get_new_contact_type(),
				'author__in' => iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids(),
			);
			$the_query = new WP_Query($args);
			if ($the_query->have_posts()) {
				$contact_fields = (array) $this->contact_fields();
				$labels = array_values($contact_fields);
				$labels[] = iFoundContacts::$taxonomy_singular_label;
				$labels[] = iFoundContacts::$contact_tag_taxonomy_singular_label;
				$labels_str[] = join(',', $labels);
				$contacts = [];
				while ($the_query->have_posts()) {
					$the_query->the_post();
					$fields = [];
					foreach ($contact_fields as $key => $value) {
						$fields[] = get_post_meta(get_the_ID(), $key, true);
					}
					// Contact statuses
					$contact_statuses = get_the_terms(get_the_ID(), iFoundContacts::$the_taxonomy);
					if ($contact_statuses) {
						$cats = [];
						foreach ($contact_statuses as $term) {
							$cats[] = $term->name;
						}
						$fields[] = join(',', $cats);
					}
					// Contact tags
					$contact_tags = get_the_terms(get_the_ID(), iFoundContacts::$contact_tag_taxonomy);
					if ($contact_tags) {
						$cats = [];
						foreach ($contact_tags as $term) {
							$cats[] = $term->name;
						}
						$fields[] = join(',', $cats);
					}
					// Wrap all values in double quotes, unless the value is empty.
					$fields = array_map(function ($value) {
						return empty($value) ? '' : '"' . $value . '"';
					}, $fields);
					$contacts[] = join(',', $fields);
				}
				$contacts = array_merge($labels_str, $contacts);
				$export_data = join(PHP_EOL, $contacts);
				wp_reset_postdata();
				$basename = 'contacts-' . date('y-m-d') . '.csv';
				header('Pragma: public');
				header('Expires: 0');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Cache-Control: private', false);
				header('Content-Type: application/octet-stream');
				header('Content-Disposition: attachment; filename="' . $basename . '";');
				header('Content-Transfer-Encoding: binary');
				$f = fopen('php://output', 'w');
				fwrite($f, $export_data);
				stream_get_contents($f);
				die();
			}
		}
	}

	/**
	 * Clean Delimiters Between
	 *
	 * This is currently only used for LionDesk format. We use this to remove delimiter chars
	 * between our actual delimiters. LionDesk is not a true csv.
	 *
	 * @param string $string The single row from csv as a string.
	 * @return string $string The string with delimiter chars removed.
	 * @since 1.2.7
	 *
	 */

	public function clean_delimiters_between($string) {

		while (strpos($string, ',"') !== false) {

			$beginning = strpos($string, ',"');
			$end = strpos($string, '",');

			if ($beginning === false || $end === false) {
				return $string;
			}

			$text_between = trim(substr($string, ($beginning + 1), ($end - $beginning)));

			$remove = array(',', ';', '|', '"');

			$clean_text = str_replace($remove, '', $text_between);

			$string = str_replace($text_between, $clean_text, $string);

		}

		return $string;

	}

	public function lookup_contact() {
		$contact_id = $_GET['contact_id'];
		if (!$contact_id) {
			die();
		}
		check_ajax_referer('lookup_contact_secure_me', 'lookup_contact_nonce');
		$contact = get_post($contact_id);
		if ($contact === NULL) {
			echo json_encode(NULL);
			die();
		}

		$email_keys = array(
			'email',
			'email2',
			'email_spouse',
			'email2_spouse',
		);
		$emails = $this->util()->get_single_metas($contact->ID, $email_keys);

		$mphone_keys = [
			'mphone',
			'mphone_spouse',
		];
		$mphones = $this->util()->get_single_metas($contact->ID, $mphone_keys);

		$may_text_vals = iFoundJointContact::new_hookless()->get_may_text_vals($contact_id);

		$vals = [
			'emails' => $emails,
			'mphones' => $mphones,
			'mayTextVals' => $may_text_vals,
		];
		echo json_encode($vals);
		die();
	}

	private function latest_email_activity_page_content($display_data) {
		ob_start();
		?>

		<div class="ifound-wrap latest-email-activity">
			<h1>Latest Email Activity</h1>
			<div>
				<p>Sent emails that have been read, NOT unique per contact</p>
			</div>
			<div>
				<table class="wp-list-table widefat striped">
					<thead>
					<th>Contact</th>
					<th>Campaign</th>
					<th>Action</th>
					<th>Email Address</th>
					<th>Date Sent</th>
					<th>Date First Read</th>
					</thead>
					<tbody>
					<?php
					foreach ($display_data as $data) {
						?>
						<tr>
							<td>
								<a href="<?=admin_url("post.php?post={$data['contact_id']}&action=edit")?>">
									<?= $data['contact_name'] ?>
								</a>
							</td>
							<td>
								<a href="<?=admin_url("post.php?post={$data['campaign']->ID}&action=edit")?>">
									<?= $data['campaign']->post_title ?>
								</a>
							</td>
							<td><?= $data['action'] ?></td>
							<td><?= $data['email'] ?></td>
							<td><?= $this->pretty_date($data['date_sent']) ?></td>
							<td><?= $this->pretty_date($data['first_read']) ?></td>
						</tr>
						<?php
					}
					?>
					</tbody>
				</table>
			</div>
		</div>

		<?php
		return ob_get_clean();
	}

	private function email_activity_slim_content_line($info) {
		ob_start();
		?>

		<div>
			<a href="<?=admin_url("post.php?post={$info['contact_id']}&action=edit")?>"><?= $info['contact_name'] ?></a>
		</div>
		<div><?=apply_filters('pretty_date', $info['first_read'])?></div>
		<div><?= $info['action'] ?></div>
		<div>
			<a href="<?=admin_url("post.php?post={$info['campaign']->ID}&action=edit")?>">Source Campaign: <?= $info['campaign']->post_title ?></a>
		</div>

		<?php
		return ob_get_clean();
	}

	public function latest_email_activity_content() {
		$author_ids = iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids();
		$display_data = $this->latest_email_activity($author_ids, []);
		$content = $this->latest_email_activity_page_content($display_data);
		echo $content;
	}

	private function latest_email_activity($author_ids, $options = []) {
		global $wpdb;

		$options = wp_parse_args($options, [
			'unique_per_contact' => false,
			'limit' => 100,
		]);

		$table_name = $wpdb->prefix . iFoundEmailTrackingPixel::$table_name;
		$author_ids_string = join(', ', $author_ids);
		$contact_limit = $options['limit'];
		$query_limit = $contact_limit;
		if ($options['unique_per_contact']) {
			$query_limit = 10000;
		}
		$contact_ids_seen = [];
		$display_data = [];
		$query_offset = 0;
		do {
			// The second join to the posts table (join {$wpdb->prefix}posts p on p2.id = pm2.meta_value) is not ideal
			// because meta_value is a string whereas p2.id is an int. But hopefully still faster than doing a second
			// query.
			$sql = <<<EOT
				select
					pm.meta_value as action_log_meta_value,
					ife.date_first_read,
					ife.to_email,
					ife.date_sent,
					p.id as campaign_id,
					p.post_title as campaign_title,
					p2.id as contact_id,
					p2.post_title as contact_title
				from $table_name ife
				join {$wpdb->prefix}postmeta pm on ife.meta_id = pm.meta_id
				join {$wpdb->prefix}posts p on pm.post_id = p.id
				join {$wpdb->prefix}postmeta pm2 on pm2.post_id = p.id and pm2.meta_key = 'contact_id'
				join {$wpdb->prefix}posts p2 on p2.id = pm2.meta_value
				where p.post_author in ($author_ids_string)
				and ife.date_first_read is not null
				order by ife.date_first_read desc
				limit $query_limit
				offset $query_offset
				;
EOT;
			$results = $wpdb->get_results($sql, ARRAY_A);
			foreach ($results as $result) {
				$contact_id = intval($result['contact_id']);
				if ($options['unique_per_contact'] && array_key_exists($contact_id, $contact_ids_seen)) {
					continue;
				}
				$activity_log_items = explode(';', $result['action_log_meta_value']);
				$action = $activity_log_items[1];
				$massaged_action = $action;
				if ($action === 'Send Drip Campaign Customer Email') {
					$massaged_action = 'Read Drip Campaign Email';
				} else if ($action === 'Send Client Email') {
					$massaged_action = 'Read Search Campaign Email';
				} else if ($action === 'Send Instant Update Email') {
					$massaged_action = 'Read Active Property Instant Update Email';
				} else if ($action === 'Send Instant Update Recently Closed Email') {
					$massaged_action = 'Read Closed Property Instant Update Email';
				}
				// Temporary: the code that displays these values later expects the campaign to be an object rather than
				// individual values. We used to look up the entire campaign in the database. We don't need to do that
				// now, but so that that code works, make this an object for now.
				$campaign = new stdClass();
				$campaign->ID = $result['campaign_id'];
				$campaign->post_title = $result['campaign_title'];
				$display_record = [
					'contact_id'   => $contact_id,
					'contact_name' => $result['contact_title'],
					'first_read'   => $result['date_first_read'],
					'date_sent'    => $result['date_sent'],
					'action'       => $massaged_action,
					'email'        => $result['to_email'],
					'campaign'     => $campaign,
				];
				$display_data[] = $display_record;
				$contact_ids_seen[$contact_id] = true;
				if (count($contact_ids_seen) >= $contact_limit) {
					break;
				}
			}
			if (count($results) < $query_limit) {
				break;
			}
			$query_offset += $query_limit;
		} while ($options['unique_per_contact'] && count($contact_ids_seen) < $contact_limit);
		return $display_data;
	}

	public function latest_website_activity_page() {
		$limit = $this->crm()->qty_logs_to_report;
		$author_ids = iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids();
		$latest_visits = $this->get_latest_visits($author_ids, [
			'limit' => $limit,
			'include_visit_counts' => true,
			'more_recent_than_date' => static::$more_recent_than_date,
		]);
		$content = $this->latest_visits_page_content($latest_visits, [
			'recent_date_description' => static::$recent_date_description,
		]);
		echo $content;
	}

	// 'Slim' content means it's meant for email (especially mobile) and the widget.
	private function latest_visits_slim_content($latest_visits, $options = []) {
		$options = wp_parse_args($options, [
			'show_header' => true,
			'user_id' => get_current_user_id(),
		]);
		$url = admin_url($this->crm_menu($options['user_id']));
		$url = add_query_arg('page', 'latest_website_activity', $url);
		$download_link = add_query_arg('download', 'true', $url);
		ob_start();
		?>
		<div>
			<?php if ($options['show_header']): ?>
				<h1>Latest Website Activity</h1>
			<?php endif ?>
			<div>Per unique registered visitor</div>
			<div><a href="<?= $url ?>">Latest Website Activity page</a></div>
			<div><a href="<?= $download_link ?>" download>Download recently visited contacts as CSV</a>*</div>
			<ol>
				<?php
				foreach ($latest_visits as $info) {
					?>
					<li><?= $this->latest_visit_slim_content_line($info) ?></li>
					<?php
				}
				?>
			</ol>
			<div><strong>*</strong> <?= static::$download_includes_message ?></div>
		</div>
		<?php
		return ob_get_clean();
	}

	private function latest_visit_slim_content_line($info) {
		ob_start();
		?>
		<div>
			<a href="<?=admin_url("post.php?post={$info['contact_id']}&action=edit")?>"><?=$info['contact_title']?></a>
		</div>
		<div><?=apply_filters('pretty_date', $info['date'])?></div>
		<div><?=$info['category']?></div>
		<div><a href="<?=$info['url']?>"><?=$info['link_title']?></a></div>
		<?php
		return ob_get_clean();
	}

	private function latest_visits_page_content($latest_visits_by_contact_id, $options = []) {
		$options = wp_parse_args($options, [
			'user_id' => get_current_user_id(),
			'more_recent_than_date' => iFoundUtil::current_datetime(),
			'recent_date_description' => static::$recent_date_description,
		]);
		$download_link = admin_url($this->crm_menu($options['user_id']));
		$download_link = add_query_arg('page', 'latest_website_activity', $download_link);
		$download_link = add_query_arg('download', 'true', $download_link);
		$more_recent_than_date_str = $options['more_recent_than_date']
			->format(iFoundCrm::$pretty_datetime_format_with_seconds);
		ob_start();
		?>

		<div class="ifound-wrap latest-activity">
			<h1>Latest Website Activity</h1>
			<div class="download_csv_section">
				<div><a href="<?= $download_link ?>">Download recently visited contacts as CSV</a></div>
				<div><?= static::$download_includes_message ?></div>
			</div>
			<div>Per unique registered visitor</div>
			<div>
				<table class="wp-list-table widefat striped">
					<thead>
					<th>Contact title</th>
					<th>Page views*</th>
					<th>Date</th>
					<th>Category</th>
					<th>Page visited</th>
					</thead>
					<tbody>
					<?php
					foreach ($latest_visits_by_contact_id as $info) {
						?>
						<tr>
							<td>
								<a href="<?=admin_url("post.php?post={$info['contact_id']}&action=edit")?>">
									<?=$info['contact_title']?>
								</a>
							</td>
							<td><?=$info['num_visits']?></td>
							<td><?=apply_filters('pretty_date', $info['date'])?></td>
							<td><?=$info['category']?></td>
							<td><a href="<?=$info['url']?>"><?=$info['link_title']?></a></td>
						</tr>
						<?php
					}
					?>
					</tbody>
				</table>
			</div>
			<div>
				* Page views - this is the number of page views <?= $options['recent_date_description'] ?>
			</div>
		</div>

		<?php
		return ob_get_clean();
	}

	public function download_activity() {
		$current_url = iFoundIdx::current_url();
		$is_download_page = strpos($current_url, 'page=latest_website_activity') !== false
			&& strpos($current_url, 'download=true') !== false;
		if (!$is_download_page) {
			return;
		}

		$author_ids = iFoundAdmin::new_hookless()->get_this_user_ids_or_primary_admin_ids();
		$thirty_days_ago = static::$more_recent_than_date;
		$contact_ids = $this->get_recently_visited_unique_contact_ids($author_ids, $thirty_days_ago);
		$contacts = [];
		$header_to_meta_key_map = [
			'First Name' => 'fname',
			'Last Name' => 'lname',
			'Email' => 'email',
			'Address' => 'address',
			'City' => 'city',
			'State' => 'state',
			'Zip' => 'zip',
		];
		foreach ($contact_ids as $contact_id) {
			$metas = $this->util()->get_single_metas($contact_id, $header_to_meta_key_map);
			$contact = ['Contact ID' => $contact_id];
			foreach ($header_to_meta_key_map as $header => $meta_key) {
				$contact[$header] = $metas[$meta_key];
			}
			$contacts[] = $contact;
		}
		usort($contacts, function($a, $b) {
			return $a['First Name'] <=> $b['First Name'];
		});

		$headers = array_keys($header_to_meta_key_map);
		array_unshift($headers, 'Contact ID');
		header('Content-Type: text/csv; charset=utf-8');
		$datestr = current_time('Y-m-d');
		header("Content-Disposition: attachment; filename=latest_website_activity_{$datestr}.csv");
		$this->util()->array2csv($contacts, $headers);
		die();
	}

	// Get the unique contacts that have visited the site since $date
	private function get_recently_visited_unique_contact_ids($author_ids, $more_recent_than_date) {
		global $wpdb;

		$post_types = iFoundJointContact::$all_contact_post_types_quoted_str_for_db;
		$author_ids_string = join(',', $author_ids);
		$meta_keys_string = apply_filters('ifound_website_activity_types_string', '');
		$more_recent_than_date_str = $more_recent_than_date->format(iFoundUtil::$mysql_date_format);

		$sql = <<<SQL
			SELECT p.ID as ID
			FROM $wpdb->postmeta pm
			JOIN $wpdb->posts p
			ON pm.post_id = p.ID
			WHERE p.post_type IN ($post_types)
			AND p.post_author IN ($author_ids_string)
			AND meta_key IN $meta_keys_string
			AND meta_value > '$more_recent_than_date_str'
			group by p.ID
			ORDER BY p.ID ASC
SQL;

		$results = $wpdb->get_results($sql, ARRAY_A);
		$contact_ids = array_values(array_map(function($x) { return $x['ID']; }, $results));
		return $contact_ids;
	}

	// Get the $limit most recent unique contacts that have visited the site
	private function get_latest_visits($author_ids, $options = []) {
		global $wpdb;
		$options = wp_parse_args($options, [
			'limit' => 100,
			'include_visit_counts' => false,
			'more_recent_than_date' => static::$more_recent_than_date,
		]);

		$post_types = iFoundJointContact::$all_contact_post_types_quoted_str_for_db;
		$author_ids_string = join(',', $author_ids);
		$meta_keys_string = apply_filters('ifound_website_activity_types_string', '');

		$sql = <<<SQL
			SELECT post_id, meta_key, meta_value, p.post_title
			FROM $wpdb->postmeta pm
			JOIN $wpdb->posts p
			ON pm.post_id = p.ID
			WHERE p.post_type IN ($post_types)
			AND p.post_author IN ($author_ids_string)
			AND meta_key IN $meta_keys_string
			ORDER BY meta_id DESC
SQL;

		$results = $wpdb->get_results($sql);

		$category_map = [
			'advanced_view' => 'Advanced',
			'detail_view' => 'Property Details (click link for status)',
			'page_view' => 'Visited',
			'polygons_view' => 'Polygons',
			'results_view' => 'Search Results',
		];

		$latest_visits_by_contact_id = [];
		if (is_array($results) && !empty($results) && isset($results[0])) {
			foreach ($results as $result) {
				if (!array_key_exists($result->post_id, $latest_visits_by_contact_id)) {
					$visit_parts = explode(';', $result->meta_value);
					$date = $visit_parts[0];
					$url = $visit_parts[1];
					$link_title = $url;
					$category = $category_map[$result->meta_key];
					if (strpos($url, '/save-this/') !== false) {
						$category = 'Visited Campaign';
						// Now get the campaign title. To do that, we'll need to get the campaign ID from the URL.
						$path = parse_url($url, PHP_URL_PATH);
						if ($path) {
							$path_parts = explode('/', $path);
							if (count($path_parts)) {
								$path_parts = array_values(array_filter($path_parts));
								$campaign_id = $path_parts[count($path_parts) - 1];
								$post = get_post($campaign_id);
								if (!$post) {
									continue;
								}
								$link_title = $post->post_title;
							}
						}
					}
					if ($result->meta_key === 'detail_view') {
						$address = $url;
						$link_title = $address;
						$listing_id = $visit_parts[2];
						$slug = sanitize_title($address);
						$origin = 'http' . ( isset( $_SERVER['HTTPS'] ) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'];
						$url = "$origin/listing-details/$slug/$listing_id";
					}

					$latest_visits_by_contact_id[$result->post_id] = [
						'contact_id' => $result->post_id,
						'contact_title' => $result->post_title,
						'date' => $date,
						'url' => $url,
						'link_title' => $link_title,
						'category' => $category,
					];
				}
				if (count($latest_visits_by_contact_id) === $options['limit']) {
					break;
				}
			}
		}

		if ($options['include_visit_counts']) {
			$contact_ids_comma_list = join(', ', array_keys($latest_visits_by_contact_id));
			$more_recent_than_date_str = $options['more_recent_than_date']->format(iFoundUtil::$mysql_date_format);
			// Reminder: we only want to count page_view. We don't want to count the others like detail_view, etc. Each
			// page view will record in the postmeta BOTH a page_view as well as the specific type, such as detail_view.
			// So we don't want to double count.
			$page_view_type = 'page_view';
			$sql = <<<SQL
				SELECT pm.post_id as contact_id, count(*) as count
				FROM $wpdb->postmeta pm
				WHERE pm.post_id IN ($contact_ids_comma_list)
				AND meta_key = '$page_view_type'
				AND meta_value > '$more_recent_than_date_str'
				GROUP BY pm.post_id
SQL;
			$results = $wpdb->get_results($sql);
			foreach ($results as $result) {
				$contact_id = $result->contact_id;
				$latest_visits_by_contact_id[$contact_id]['num_visits'] = $result->count;
			}
		}


		return array_values($latest_visits_by_contact_id);
	}

	// 'Slim' content means it's meant for email (especially mobile) and the widget.
	private function latest_email_activity_slim_content($latest_visits, $options = []) {
		$options = wp_parse_args($options, [
			'show_header' => true,
			'user_id' => get_current_user_id(),
		]);
		$url = admin_url($this->crm_menu($options['user_id']));
		$url = add_query_arg('page', 'latest_email_activity', $url);
		ob_start();
		?>
		<div>
			<?php if ($options['show_header']): ?>
				<h1>Latest Email Activity</h1>
			<?php endif ?>
			<div>Sent emails that have been read, per unique registered visitor</div>
			<div><a href="<?= $url ?>">Latest Email Activity page</a></div>
			<ol>
				<?php
				foreach ($latest_visits as $info) {
					?>
					<li><?= $this->email_activity_slim_content_line($info) ?></li>
					<?php
				}
				?>
			</ol>
		</div>
		<?php
		return ob_get_clean();
	}

	public function get_latest_web_and_email_activity_slim_content($limit, $author_ids, $user_id) {
		$latest_visits = $this->get_latest_visits($author_ids, ['limit' => $limit]);
		$email_activity = $this->latest_email_activity($author_ids, [
			'limit' => $limit,
			'unique_per_contact' => true,
		]);
		$settings_page_link = iFoundCrm::new_hookless()->get_crm_menu_admin_url($user_id);

		ob_start();

		?>
		<div style="font-size:smaller;">
			Here are the latest stats from your website powered by iFoundAgent. To control when/if you get this email, visit the <a href="<?= $settings_page_link ?>">iFound CRM Settings page</a> and look in the Activity Report Email section.
		</div>
		<?php

		echo $this->latest_visits_slim_content($latest_visits, ['user_id' => $user_id]);

		echo $this->latest_email_activity_slim_content($email_activity, ['user_id' => $user_id]);

		return ob_get_clean();
	}

	public function handle_upgrade_campaigns() {
		if (!current_user_can(iFoundAdmin::$manage_ifound_staff_settings_capability_name)) {
			return;
		}
		if (($_POST['do_upgrade'] ?? null) === 'yes') {
			iFoundSaveThisUpgrader::new_hookless()->transition_mls();
		} else if (($_POST['do_rollback'] ?? null) === 'yes') {
			iFoundSaveThisUpgrader::new_hookless()->rollback_mls_transition();
		}
	}

	public function upgrade_campaigns_page() {
		if (!current_user_can(iFoundAdmin::$manage_ifound_staff_settings_capability_name)) {
			return;
		}
		?>
		<h1>Upgrade Campaigns and Shortcode</h1>
		<div>Reminder: This page is only shown to <?= iFoundAdmin::new_hookless()->get_staff_name() ?> staff.</div>
		<div>Current MLS: <?= iFoundIdx::mls_name() ?></div>
		<?php
			$mls = iFoundIdx::mls_name();
			if (!in_array($mls, ['armls', 'armls_spark'])) {
				echo "<div>The MLS ($mls) is not allowed for ugprading</div>";
				return;
			}

			wp_enqueue_script('jquery-ui-core');
			wp_enqueue_script('jquery-ui-accordion');
			$upgrade_disabled_string = iFoundIdx::mls_name() === iFoundSaveThisUpgrader::$upgrade_to_mls
				? 'disabled="disabled"' : '';
			$rollback_disabled_string = iFoundIdx::mls_name() === iFoundSaveThisUpgrader::$upgrade_from_mls
			? 'disabled="disabled"' : '';
		?>
		<div>Check progress on the <a href="/wp-admin/tools.php?page=action-scheduler">Scheduled Actions page</a></div>
		<div>
			<form method="POST">
				<button class="button button-primary" type="submit" name="do_upgrade" value="yes"
						<?= $upgrade_disabled_string ?>
				>
					Transition from <?= iFoundSaveThisUpgrader::$upgrade_from_mls ?>
					to <?= iFoundSaveThisUpgrader::$upgrade_to_mls ?>
				</button>
				<button class="button" type="submit" name="do_rollback" value="yes"
					<?= $rollback_disabled_string ?>
				>
					Rollback transition
				</button>
				<div>Reminder: there is no feedback when clicking the above buttons.</div>
			</form>
		</div>
		<?php
		$unchanged_post_ids = iFoundSaveThisUpgrader::new_hookless()->get_unchanged_upgraded_campaign_ids();
		$limit = 1000;
		$post_ids = iFoundSaveThisUpgrader::new_hookless()->get_changed_upgraded_campaign_ids($limit);
		$params_by_post_id = [];
		foreach ($post_ids as $post_id) {
			$params = get_post_meta($post_id, 'params', true);
			$old_params = get_post_meta($post_id, iFoundSaveThisUpgrader::$copy_to_from_meta_key, true);
			$params_by_post_id[$post_id] = [
				'params' => $params,
				'old_params' => $old_params,
			];
		}
		$unchanged_shortcode_meta_ids =
			iFoundSaveThisUpgrader::new_hookless()->get_upgraded_shortcode_meta_ids($limit, 'unchanged');
		$changed_shortcode_meta_ids =
			iFoundSaveThisUpgrader::new_hookless()->get_upgraded_shortcode_meta_ids($limit, 'changed');
		$shortcodes_by_meta_id = [];
		foreach ($changed_shortcode_meta_ids as $meta_id) {
			$meta_value = iFound::new_hookless()->get_meta_by_id($meta_id);
			$shortcodes_by_meta_id[$meta_id] = $meta_value;
		}
		?>
		<div class="upgraded_campaigns">
			<h3>Campaigns</h3>
			<div>
				<div style="margin-bottom: 10px;">
					Number of upgraded but unchanged campaigns: <?= count($unchanged_post_ids) ?>
				</div>
				<div>Here are the first <?= $limit ?> upgraded campaigns that changed.</div>
				<table class="striped widefat">
					<thead>
					<tr>
						<th>Post ID</th>
						<th>Old params</th>
						<th>New Params</th>
					</tr>
					</thead>
					<tbody>
					<? foreach ($params_by_post_id as $post_id => $val): ?>
						<tr>
							<td><a href="/wp-admin/post.php?post=<?= $post_id ?>&action=edit"><?= $post_id ?></a></td>
							<td class="params_original"><? print_r($val['old_params']) ?></td>
							<td class="params_changed params_diff"><? print_r($val['params']) ?></td>
						</tr>
					<? endforeach ?>
					</tbody>
				</table>
			</div>
		</div>
		<div class="upgraded_shortcodes">
			<h3>Shortcode</h3>
			<div>
				<div style="margin-bottom: 10px;">
					Number of upgraded but unchanged shortcodes: <?= count($unchanged_shortcode_meta_ids) ?>
				</div>
				<div>Here are the first <?= $limit ?> meta IDs.</div>
				<table class="striped widefat">
					<thead>
					<tr>
						<th>Post ID</th>
						<th>Meta ID</th>
						<th>Old Query</th>
						<th>New Query</th>
						<th>Old Backup Query</th>
						<th>New Backup Query</th>
					</tr>
					</thead>
					<tbody>
					<? foreach ($shortcodes_by_meta_id as $meta_id => $val): ?>
						<?php
							$post_id = get_post_meta_by_id($meta_id)->post_id;
						?>
						<tr>
							<td><a href="/wp-admin/post.php?post=<?= $post_id ?>&action=edit"><?= $post_id ?></a></td>
							<td><?= $meta_id ?></td>
							<td class="query_original">
								<? print_r($val[iFoundSaveThisUpgrader::$copy_to_shortcode_key]['query'] ?? null) ?>
							</td>
							<td class="query_changed query_diff"><? print_r($val['query']) ?></td>
							<td class="backup_query_original">
								<? print_r($val[iFoundSaveThisUpgrader::$copy_to_shortcode_key]['backup_query'] ?? null) ?>
							</td>
							<td class="backup_query_changed backup_query_diff"><? print_r($val['backup_query']) ?></td>
						</tr>
					<? endforeach ?>
					</tbody>
				</table>
			</div>
		</div>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/diff_match_patch/20121119/diff_match_patch.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/jquery-prettytextdiff@1.0.4/jquery.pretty-text-diff.min.js"></script>
		<style>
			.upgraded_campaigns, .upgraded_shortcodes {
				ins {
					background-color: #c6ffc6;
					text-decoration: none;
				}

				del {
					background-color: #ffc6c6;
				}

				/* I'm not sure why the prettyTextDiff() puts line break tags in the output. Simple enough to fix. */
				br {
					display: none;
				}
			}
		</style>
		<script>
			jQuery(document).ready(function($) {
				$('.upgraded_campaigns tr').prettyTextDiff({
					originalContainer: '.params_original',
					changedContainer: '.params_changed',
					diffContainer: '.params_diff',
				});
				$('.upgraded_shortcodes tr').prettyTextDiff({
					originalContainer: '.query_original',
					changedContainer: '.query_changed',
					diffContainer: '.query_diff',
				});
				$('.upgraded_shortcodes tr').prettyTextDiff({
					originalContainer: '.backup_query_original',
					changedContainer: '.backup_query_changed',
					diffContainer: '.backup_query_diff',
				});
				$('.upgraded_campaigns, .upgraded_shortcodes').accordion({
					active: false,
					collapsible: true,
				});
			});
		</script>
		<?php
	}
}

iFoundContactsAdmin::static_init();
