DB = pfndidx_azdb
MYSQL = mysql -h idx_db -u root -proot $(DB)

load-dump:
	gunzip -c /tmp/downloads/dev-idx-db.sql.gz | $(MYSQL)

load-data:
	gunzip -c /tmp/downloads/$(MLS).sql.gz | sed 's/_dump//' | $(MYSQL)

rets-summary:
	$(MYSQL) -e "select table_name,table_rows from information_schema.tables where table_schema = '$(DB)' and table_name regexp '_(property|images|fields)' and table_name not regexp '_dump'"
     