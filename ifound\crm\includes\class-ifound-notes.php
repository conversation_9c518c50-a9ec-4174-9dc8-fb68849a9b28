<?
defined( 'ABSPATH' ) or die( 'You do not have access!' );

if( ! class_exists( 'iFoundContacts' ) ) die( 'You do not have access!' );

/**
 * iFoundNotes Class
 *
 * @since 3.0.0
 */

class iFoundNotes {
	use NewHooklessTrait;

	/**
	 * init iFoundNotes class.
	 *
	 * @since 3.0.0
	 */

	public static function init() {
        $class = __CLASS__;
        new $class;
    }

	/**
	 * Constructor
	 *
	 * @since 3.0.0
	 */

	public function __construct($options = []) {
		$options = wp_parse_args($options, [
			// Enable hooks by default, but allow them to not be set up, so that we can instantiate this class and use
			// it without the hooks being engaged multiple times.
			'enable_hooks' => true,
		]);

		if ($options['enable_hooks']) {
			add_action('wp_enqueue_scripts', array($this, 'notes_scripts'));
			add_action('admin_enqueue_scripts', array($this, 'notes_scripts'));
			add_action('wp_ajax_new_note_ajax', array($this, 'new_note_ajax'));
			add_action('wp_ajax_nopriv_new_note_ajax', array($this, 'new_note_ajax'));
			add_action('ifound_notes_box', array($this, 'notes_box'), 10, 2);
			add_action('ifound_notes_list', array($this, 'notes_list'), 10, 2);
		}
	}

	/**
	 * Notes Scripts
	 *
	 * @since 3.0.0
	 */

	public function notes_scripts(){

		wp_register_script( 'notes_js', plugins_url( 'js/notes.js', __FILE__ ), array( 'jquery' ), iFOUND_PLUGIN_VERSION );
		wp_localize_script( 'notes_js', 'notes', array(
			'endpoint' 		=> admin_url( 'admin-ajax.php' ),
			'nonce' 		=> wp_create_nonce( 'notes_secure_me' )
		));

	}

	/**
	 *	New Note Ajax
	 *
	 *  Stores the new note in the db. Returns a respose to jQuery.
	 *
	 *	@since 1.2.39
	 *  @since 2.4.26 Strup slashes from text in response.
	 */

	public function new_note_ajax() {

		check_ajax_referer( 'notes_secure_me', 'notes_nonce' );

		$input = $_REQUEST['input'];

		$text = sanitize_text_field( $input['text'] );

		$post_id = intval( $input['post_id'] );

		if( is_int( $post_id ) && $post_id > 0 && ! empty( $text ) ) {

			$response = array(
				'class' 	=> 'fa-check-circle',
				'note'		=> '<span class="new-note">' . stripslashes( $this->note_body( $date, $text ) ) . '</span>',
				'success'	=> true
			);

			$this->add_note($post_id, $text);

		} else {

			$response = array(
				'class' 	=> 'fa-exclamation-triangle',
				'note'		=> false,
				'success'	=> false
			);

		}

		echo json_encode( $response );

		die();

	}

	public function add_note($post_id, $text) {
		$date = current_time( 'mysql' );
		$note = (object) array(
			'date' 	=> $date,
			'text'	=> $text
		);
		add_post_meta( $post_id, 'notes', $note );
	}

	/**
	 *	Notes Metabox
	 *
	 *	@since 1.2.39
	 */

	public static function notes_meta_box( $post ) {

		do_action( 'ifound_notes_box', $post->ID );

	}

	/**
	 *	Notes Box
	 *
	 *	@since 1.2.39
	 */

	public function notes_box( $post_id, $mls_id = '' ) {

		wp_enqueue_script( 'notes_js' ); ?>

		<div class="ifound-notes-wrapper"><?

			do_action( 'ifound_notes_list', $post_id, $mls_id ); ?>

			<table class="form-table notes_meta views_meta new-note-wrapper">

				<tbody>

					<tr>
						<th colspan="4" scope="row"><label for="new-note-text"><? _e( 'New Note:', 'ifound' ) ; ?></label></th>
					</tr>

					<tr>
						<td colspan="4"><textarea id="new-note-text" class="new-note-text large-text"></textarea></td>
					</tr>

					<tr>
						<td colspan="4">
							<div class="button button-primary add-notes" post_id="<? echo $post_id; ?>" mls_id="<? echo $mls_id; ?>">
								<i class="fal fa-plus-square new-note-spinner"></i>
								<? _e( 'Save Note', 'ifound' ) ; ?>
							</div>
						</td>
					</tr>

				</tbody>

			</table>

		</div><?

	}

	/**
	 *	Notes Box
	 *
	 *	@since 1.2.39
	 */

	public function notes_list( $post_id, $mls_id ) { ?>

		<ul class="new-notes-catch"><?

			$meta_key = empty( $mls_id ) ? 'notes' : 'notes_' . $mls_id;

			if( $notes = get_post_meta( $post_id, $meta_key ) ) {

				krsort( $notes );

				foreach( $notes as $note ) {

					echo $this->note_body( $note->date, $note->text );

				}

			} else {

				_e( 'No Notes at this time.', 'ifound' );

			} ?>

		</ul><?

	}

	/**
	 *	Notes Body
	 *
	 *  The body of the note in notes metabox.
	 *
	 *	@since 1.2.39
	 *  @since 2.4.26 Add pretty date.
	 */

	public function note_body( $date, $text ) {

		ob_start(); ?>

		<li>
			<span class="note-text-label"><? _e( apply_filters( 'pretty_date',  $date ), 'ifound' ) ; ?></span>
			<span class="note-text"><? _e( $text, 'ifound' ) ; ?></span>
		</li><?

		return ob_get_clean();

	}

}
